package com.trading.market.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trading.common.dto.KlineData;
import com.trading.market.controller.MarketController;
import com.trading.market.service.MarketDataService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@ExtendWith(MockitoExtension.class)
class MarketDataServiceImplTest {

    private MockMvc mockMvc;

    @Mock
    private MarketDataService marketDataService;

    @InjectMocks
    private MarketController marketController;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(marketController).build();
    }

    private KlineData createValidKlineData() {
        KlineData klineData = new KlineData();
        klineData.setSymbol("BTCUSDT");
        klineData.setInterval("1m");
        klineData.setOpenTime(LocalDateTime.now());
        klineData.setCloseTime(LocalDateTime.now().plusMinutes(1));
        klineData.setOpenPrice(new BigDecimal("60000"));
        klineData.setHighPrice(new BigDecimal("61000"));
        klineData.setLowPrice(new BigDecimal("59000"));
        klineData.setClosePrice(new BigDecimal("60500"));
        klineData.setVolume(new BigDecimal("100"));
        klineData.setQuoteVolume(new BigDecimal("6000000"));
        klineData.setTradeCount(1000L);
        klineData.setTakerBuyVolume(new BigDecimal("50"));
        klineData.setTakerBuyQuoteVolume(new BigDecimal("3000000"));
        klineData.setIsClosed(true);
        return klineData;
    }

    @Test
    void testGetKlineData_Success() throws Exception {
        // Arrange
        KlineData klineData = createValidKlineData();
        when(marketDataService.getKlineData("BTCUSDT", "1m", 1)).thenReturn(List.of(klineData));

        // Act & Assert
        mockMvc.perform(get("/api/market/kline/BTCUSDT/1m").param("limit", "1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data[0].symbol").value("BTCUSDT"));

        verify(marketDataService).getKlineData("BTCUSDT", "1m", 1);
    }

    @Test
    void testInjectKlineDataForE2E_Success() throws Exception {
        // Arrange
        KlineData klineData = createValidKlineData();
        objectMapper.findAndRegisterModules(); // Ensure Java 8 date/time module is registered
        when(marketDataService.saveKlineDataAsync(any(KlineData.class))).thenReturn(CompletableFuture.completedFuture(null));

        // Act & Assert
        mockMvc.perform(post("/api/market/e2e/kline/inject")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(klineData)))
                .andExpect(status().isOk());

        verify(marketDataService).saveKlineDataAsync(any(KlineData.class));
    }
}
