<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trading.market.mapper.TradeDataMapper">

    <insert id="insert" parameterType="com.trading.common.dto.TradeData" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO trade_data (
            tradeId, symbol, price, quantity, quoteQuantity, tradeTime,
            buyerOrderId, sellerOrderId, side, isBuyerMaker, isBestMatch,
            commission, commissionAsset, createdAt, source, dataType,
            rawData, metadata, qualityScore, version, updatedAt
        ) VALUES (
            #{tradeId}, #{symbol}, #{price}, #{quantity}, #{quoteQuantity}, #{tradeTime},
            #{buyerOrderId}, #{sellerOrderId}, #{side}, #{isBuyerMaker}, #{isBestMatch},
            #{commission}, #{commissionAsset}, #{createdAt}, #{source}, #{dataType},
            #{rawData}, #{metadata}, #{qualityScore}, #{version}, #{updatedAt}
        )
    </insert>

</mapper>
