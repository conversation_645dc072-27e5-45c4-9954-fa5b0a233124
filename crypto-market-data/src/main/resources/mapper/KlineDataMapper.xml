<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trading.market.mapper.KlineDataMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.trading.common.dto.KlineData">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="symbol" property="symbol" jdbcType="VARCHAR"/>
        <result column="interval_type" property="interval" jdbcType="VARCHAR"/>
        <result column="open_time" property="openTime" jdbcType="TIMESTAMP"/>
        <result column="close_time" property="closeTime" jdbcType="TIMESTAMP"/>
        <result column="open_price" property="openPrice" jdbcType="DECIMAL"/>
        <result column="high_price" property="highPrice" jdbcType="DECIMAL"/>
        <result column="low_price" property="lowPrice" jdbcType="DECIMAL"/>
        <result column="close_price" property="closePrice" jdbcType="DECIMAL"/>
        <result column="volume" property="volume" jdbcType="DECIMAL"/>
        <result column="quote_volume" property="quoteVolume" jdbcType="DECIMAL"/>
        <result column="trade_count" property="tradeCount" jdbcType="BIGINT"/>
        <result column="taker_buy_volume" property="takerBuyVolume" jdbcType="DECIMAL"/>
        <result column="taker_buy_quote_volume" property="takerBuyQuoteVolume" jdbcType="DECIMAL"/>
        <result column="latency" property="latency" jdbcType="BIGINT"/>
        <result column="source" property="source" jdbcType="VARCHAR"/>
        <result column="raw_data" property="rawData" jdbcType="LONGVARCHAR"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="is_closed" property="isClosed" jdbcType="BOOLEAN"/>
        <result column="data_type" property="dataType" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础SQL片段 -->
    <sql id="Base_Column_List">
        id, symbol, interval_type, open_time, close_time, open_price, high_price, low_price, 
        close_price, volume, quote_volume, trade_count, taker_buy_volume, taker_buy_quote_volume,
        latency, source, raw_data, created_at, updated_at, is_closed, data_type
    </sql>

    <!-- 插入字段 -->
    <sql id="Insert_Column_List">
        symbol, interval_type, open_time, close_time, open_price, high_price, low_price, 
        close_price, volume, quote_volume, trade_count, taker_buy_volume, taker_buy_quote_volume,
        latency, source, raw_data, created_at, updated_at, is_closed, data_type
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM kline_data
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据交易对和时间间隔查询 -->
    <select id="findBySymbolAndInterval" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM kline_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND interval_type = #{interval,jdbcType=VARCHAR}
        ORDER BY open_time DESC
        LIMIT #{limit,jdbcType=INTEGER}
    </select>

    <!-- 根据交易对、时间间隔和时间范围查询 -->
    <select id="findBySymbolAndIntervalAndTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM kline_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND interval_type = #{interval,jdbcType=VARCHAR}
        AND open_time >= #{startTime,jdbcType=TIMESTAMP}
        AND open_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        ORDER BY open_time ASC
    </select>

    <!-- 查询最新的K线数据(高效, 限制数量) -->
    <select id="findRecentKlineData" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM kline_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
          AND interval_type = #{interval,jdbcType=VARCHAR}
        ORDER BY open_time DESC
        LIMIT #{limit,jdbcType=INTEGER}
    </select>

    <!-- 分页查询 -->
    <select id="findBySymbolAndIntervalAndTimeRangePage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM kline_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND interval_type = #{interval,jdbcType=VARCHAR}
        AND open_time >= #{startTime,jdbcType=TIMESTAMP}
        AND open_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        ORDER BY open_time ASC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询最新的K线数据 -->
    <select id="findLatestBySymbolAndInterval" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM kline_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND interval_type = #{interval,jdbcType=VARCHAR}
        ORDER BY open_time DESC
        LIMIT 1
    </select>

    <!-- 查询最早的K线数据 -->
    <select id="findEarliestBySymbolAndInterval" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM kline_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND interval_type = #{interval,jdbcType=VARCHAR}
        ORDER BY open_time ASC
        LIMIT 1
    </select>

    <!-- 根据交易对和开盘时间查询 -->
    <select id="findBySymbolAndIntervalAndOpenTime" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM kline_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND interval_type = #{interval,jdbcType=VARCHAR}
        AND open_time = #{openTime,jdbcType=TIMESTAMP}
        LIMIT 1
    </select>

    <!-- 查询指定时间范围内的所有K线数据 -->
    <select id="findByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM kline_data
        WHERE open_time >= #{startTime,jdbcType=TIMESTAMP}
        AND open_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        ORDER BY symbol, interval_type, open_time ASC
    </select>

    <!-- 统计指定交易对和时间间隔的K线数据数量 -->
    <select id="countBySymbolAndInterval" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM kline_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND interval_type = #{interval,jdbcType=VARCHAR}
    </select>

    <!-- 检查是否存在指定的K线数据 -->
    <select id="existsBySymbolAndIntervalAndOpenTime" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM kline_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND interval_type = #{interval,jdbcType=VARCHAR}
        AND open_time = #{openTime,jdbcType=TIMESTAMP}
    </select>

    <!-- 查询所有交易对 -->
    <select id="findAllSymbols" resultType="java.lang.String">
        SELECT DISTINCT symbol
        FROM kline_data
        ORDER BY symbol
    </select>

    <!-- 查询所有时间间隔 -->
    <select id="findAllIntervals" resultType="java.lang.String">
        SELECT DISTINCT interval_type
        FROM kline_data
        ORDER BY interval_type
    </select>

    <!-- 查询指定交易对的最早时间 -->
    <select id="findEarliestTimeBySymbolAndInterval" resultType="java.time.Instant">
        SELECT MIN(open_time)
        FROM kline_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND interval_type = #{interval,jdbcType=VARCHAR}
    </select>

    <!-- 查询指定交易对的最晚时间 -->
    <select id="findLatestTimeBySymbolAndInterval" resultType="java.time.Instant">
        SELECT MAX(open_time)
        FROM kline_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND interval_type = #{interval,jdbcType=VARCHAR}
    </select>


    <!-- 插入K线数据 -->
    <insert id="insert" parameterType="com.trading.common.dto.KlineData" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO kline_data (<include refid="Insert_Column_List"/>)
        VALUES (#{symbol,jdbcType=VARCHAR}, #{interval,jdbcType=VARCHAR}, #{openTime,jdbcType=TIMESTAMP},
                #{closeTime,jdbcType=TIMESTAMP}, #{openPrice,jdbcType=DECIMAL}, #{highPrice,jdbcType=DECIMAL},
                #{lowPrice,jdbcType=DECIMAL}, #{closePrice,jdbcType=DECIMAL}, #{volume,jdbcType=DECIMAL},
                #{quoteVolume,jdbcType=DECIMAL}, #{tradeCount,jdbcType=INTEGER}, #{takerBuyVolume,jdbcType=DECIMAL},
                #{takerBuyQuoteVolume,jdbcType=DECIMAL}, #{latency,jdbcType=BIGINT},
                #{source,jdbcType=VARCHAR}, #{rawData,jdbcType=LONGVARCHAR}, #{createdAt,jdbcType=TIMESTAMP},
                #{updatedAt,jdbcType=TIMESTAMP}, #{isClosed,jdbcType=BOOLEAN}, #{dataType,jdbcType=VARCHAR})
    </insert>

    <!-- 批量插入K线数据 - 处理重复键 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO kline_data (<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.symbol,jdbcType=VARCHAR}, #{item.interval,jdbcType=VARCHAR}, #{item.openTime,jdbcType=TIMESTAMP},
             #{item.closeTime,jdbcType=TIMESTAMP}, #{item.openPrice,jdbcType=DECIMAL}, #{item.highPrice,jdbcType=DECIMAL},
             #{item.lowPrice,jdbcType=DECIMAL}, #{item.closePrice,jdbcType=DECIMAL}, #{item.volume,jdbcType=DECIMAL},
             #{item.quoteVolume,jdbcType=DECIMAL}, #{item.tradeCount,jdbcType=BIGINT}, #{item.takerBuyVolume,jdbcType=DECIMAL},
             #{item.takerBuyQuoteVolume,jdbcType=DECIMAL}, #{item.latency,jdbcType=BIGINT},
             #{item.source,jdbcType=VARCHAR}, #{item.rawData,jdbcType=LONGVARCHAR}, #{item.createdAt,jdbcType=TIMESTAMP},
             #{item.updatedAt,jdbcType=TIMESTAMP}, #{item.isClosed,jdbcType=BOOLEAN}, #{item.dataType,jdbcType=VARCHAR})
        </foreach>
        ON DUPLICATE KEY UPDATE
            close_price = VALUES(close_price),
            high_price = VALUES(high_price),
            low_price = VALUES(low_price),
            volume = VALUES(volume),
            quote_volume = VALUES(quote_volume),
            trade_count = VALUES(trade_count),
            taker_buy_volume = VALUES(taker_buy_volume),
            taker_buy_quote_volume = VALUES(taker_buy_quote_volume),
            latency = VALUES(latency),
            raw_data = VALUES(raw_data),
            updated_at = VALUES(updated_at),
            is_closed = VALUES(is_closed),
            close_time = VALUES(close_time)
    </insert>

    <!-- 更新K线数据 -->
    <update id="updateById" parameterType="com.trading.common.dto.KlineData">
        UPDATE kline_data
        SET symbol = #{symbol,jdbcType=VARCHAR},
            interval_type = #{interval,jdbcType=VARCHAR},
            open_time = #{openTime,jdbcType=TIMESTAMP},
            close_time = #{closeTime,jdbcType=TIMESTAMP},
            open_price = #{openPrice,jdbcType=DECIMAL},
            high_price = #{highPrice,jdbcType=DECIMAL},
            low_price = #{lowPrice,jdbcType=DECIMAL},
            close_price = #{closePrice,jdbcType=DECIMAL},
            volume = #{volume,jdbcType=DECIMAL},
            quote_volume = #{quoteVolume,jdbcType=DECIMAL},
            trade_count = #{tradeCount,jdbcType=INTEGER},
            taker_buy_volume = #{takerBuyVolume,jdbcType=DECIMAL},
            taker_buy_quote_volume = #{takerBuyQuoteVolume,jdbcType=DECIMAL},
            latency = #{latency,jdbcType=BIGINT},
            source = #{source,jdbcType=VARCHAR},
            raw_data = #{rawData,jdbcType=LONGVARCHAR},
            updated_at = #{updatedAt,jdbcType=TIMESTAMP},
            is_closed = #{isClosed,jdbcType=BOOLEAN},
            data_type = #{dataType,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=BIGINT}
    </update>

    <!-- 删除K线数据 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM kline_data
        WHERE id = #{id,jdbcType=BIGINT}
    </delete>

    <!-- 删除指定时间之前的K线数据 -->
    <delete id="deleteByOpenTimeBefore">
        DELETE FROM kline_data
        WHERE open_time &lt; #{beforeTime,jdbcType=TIMESTAMP}
    </delete>

</mapper>
