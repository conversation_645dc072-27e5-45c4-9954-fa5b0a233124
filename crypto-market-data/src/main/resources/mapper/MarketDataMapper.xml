<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.trading.market.mapper.MarketDataMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.trading.common.dto.TradeData">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="timestamp" property="timestamp" jdbcType="TIMESTAMP"/>
        <result column="trade_time" property="tradeTime" jdbcType="TIMESTAMP"/>
        <result column="symbol" property="symbol" jdbcType="VARCHAR"/>
        <result column="data_type" property="dataType" jdbcType="VARCHAR"/>
        <result column="source" property="source" jdbcType="VARCHAR"/>
        <result column="price" property="price" jdbcType="DECIMAL"/>
        <result column="quantity" property="quantity" jdbcType="DECIMAL"/>
        <result column="amount" property="amount" jdbcType="DECIMAL"/>
        <result column="raw_data" property="rawData" jdbcType="LONGVARCHAR"/>
        <result column="quality_score" property="qualityScore" jdbcType="DOUBLE"/>
        <result column="created_at" property="createdAt" jdbcType="TIMESTAMP"/>
        <result column="version" property="version" jdbcType="VARCHAR"/>

        <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP"/>
        <result column="side" property="side" jdbcType="VARCHAR"/>
        <result column="trade_id" property="tradeId" jdbcType="BIGINT"/>
    </resultMap>

    <!-- 基础SQL片段 -->
    <sql id="Base_Column_List">
        id, timestamp, trade_time, symbol, data_type, source, price, quantity, amount, raw_data,
        quality_score, created_at, version, updated_at, side, trade_id
    </sql>

    <!-- 插入字段 -->
    <sql id="Insert_Column_List">
        timestamp, trade_time, symbol, data_type, source, price, quantity, amount, raw_data,
        quality_score, created_at, version, updated_at, side, trade_id
    </sql>

    <!-- 根据主键查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM market_data
        WHERE id = #{id,jdbcType=BIGINT}
    </select>

    <!-- 根据交易对和数据类型查询 -->
    <select id="findBySymbolAndDataType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM market_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND data_type = #{dataType,jdbcType=VARCHAR}
        ORDER BY timestamp ASC
    </select>

    <!-- 根据交易对、数据类型和时间范围查询 -->
    <select id="findBySymbolAndDataTypeAndTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM market_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND data_type = #{dataType,jdbcType=VARCHAR}
        AND timestamp >= #{startTime,jdbcType=TIMESTAMP}
        AND timestamp &lt;= #{endTime,jdbcType=TIMESTAMP}
        ORDER BY timestamp ASC
    </select>

    <!-- 分页查询 -->
    <select id="findBySymbolAndDataTypeAndTimeRangePage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM market_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND data_type = #{dataType,jdbcType=VARCHAR}
        AND timestamp >= #{startTime,jdbcType=TIMESTAMP}
        AND timestamp &lt;= #{endTime,jdbcType=TIMESTAMP}
        ORDER BY timestamp ASC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 查询最新的市场数据 -->
    <select id="findLatestBySymbolAndDataType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM market_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND data_type = #{dataType,jdbcType=VARCHAR}
        ORDER BY timestamp DESC
        LIMIT 1
    </select>

    <!-- 根据数据类型查询 -->
    <select id="findByDataType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM market_data
        WHERE data_type = #{dataType,jdbcType=VARCHAR}
        ORDER BY timestamp ASC
    </select>

    <!-- 查询指定时间范围内的所有市场数据 -->
    <select id="findByTimeRange" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM market_data
        WHERE timestamp >= #{startTime,jdbcType=TIMESTAMP}
        AND timestamp &lt;= #{endTime,jdbcType=TIMESTAMP}
        ORDER BY symbol, data_type, timestamp ASC
    </select>

    <!-- 查询深度数据 -->
    <select id="findDepthData" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM market_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND data_type = 'depth'
        AND timestamp >= #{startTime,jdbcType=TIMESTAMP}
        AND timestamp &lt;= #{endTime,jdbcType=TIMESTAMP}
        ORDER BY timestamp DESC
    </select>

    <!-- 查询交易数据 -->
    <select id="findTradeData" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM market_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND data_type = 'trade'
        AND timestamp >= #{startTime,jdbcType=TIMESTAMP}
        AND timestamp &lt;= #{endTime,jdbcType=TIMESTAMP}
        ORDER BY timestamp DESC
    </select>

    <!-- 查询统计数据 -->
    <select id="findStatsData" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM market_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND data_type = 'stats'
        AND timestamp >= #{startTime,jdbcType=TIMESTAMP}
        AND timestamp &lt;= #{endTime,jdbcType=TIMESTAMP}
        ORDER BY timestamp DESC
    </select>

    <!-- 统计指定交易对和数据类型的市场数据数量 -->
    <select id="countBySymbolAndDataType" resultType="java.lang.Long">
        SELECT COUNT(*)
        FROM market_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND data_type = #{dataType,jdbcType=VARCHAR}
    </select>

    <!-- 检查是否存在指定的市场数据 -->
    <select id="existsBySymbolAndDataTypeAndTimestamp" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM market_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND data_type = #{dataType,jdbcType=VARCHAR}
        AND timestamp = #{timestamp,jdbcType=TIMESTAMP}
        AND COALESCE(trade_id, -1) = #{tradeId,jdbcType=BIGINT}
    </select>

    <!-- 查询所有交易对 -->
    <select id="findAllSymbols" resultType="java.lang.String">
        SELECT DISTINCT symbol
        FROM market_data
        ORDER BY symbol
    </select>

    <!-- 查询所有数据类型 -->
    <select id="findAllDataTypes" resultType="java.lang.String">
        SELECT DISTINCT data_type
        FROM market_data
        ORDER BY data_type
    </select>

    <!-- 查询指定交易对的最早时间 -->
    <select id="findEarliestTimeBySymbolAndDataType" resultType="java.time.Instant">
        SELECT MIN(timestamp)
        FROM market_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND data_type = #{dataType,jdbcType=VARCHAR}
    </select>

    <!-- 查询指定交易对的最晚时间 -->
    <select id="findLatestTimeBySymbolAndDataType" resultType="java.time.Instant">
        SELECT MAX(timestamp)
        FROM market_data
        WHERE symbol = #{symbol,jdbcType=VARCHAR}
        AND data_type = #{dataType,jdbcType=VARCHAR}
    </select>

    <!-- 查询数据质量分数低于阈值的市场数据 -->
    <select id="findByQualityScoreLessThan" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM market_data
        WHERE quality_score &lt; #{threshold,jdbcType=DOUBLE}
        ORDER BY quality_score ASC
    </select>

    <!-- 根据数据源查询市场数据 -->
    <select id="findBySource" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM market_data
        WHERE source = #{source,jdbcType=VARCHAR}
        ORDER BY timestamp ASC
    </select>

    <!-- 插入市场数据 -->
    <!-- <insert id="insert" parameterType="com.trading.common.dto.TradeData" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO market_data (<include refid="Insert_Column_List"/>)
        VALUES (#{timestamp,jdbcType=TIMESTAMP}, #{symbol,jdbcType=VARCHAR}, #{dataType,jdbcType=VARCHAR},
                #{source,jdbcType=VARCHAR}, #{price,jdbcType=DECIMAL}, #{quantity,jdbcType=DECIMAL},
                #{amount,jdbcType=DECIMAL}, #{rawData,jdbcType=LONGVARCHAR}, #{qualityScore,jdbcType=DOUBLE},
                #{createdAt,jdbcType=TIMESTAMP}, #{version,jdbcType=VARCHAR},
                #{updatedAt,jdbcType=TIMESTAMP}, #{side,jdbcType=VARCHAR},
                #{tradeId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT})
    </insert> -->

    <!-- 批量插入市场数据 -->
    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO market_data (<include refid="Insert_Column_List"/>)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.timestamp,jdbcType=TIMESTAMP}, #{item.tradeTime,jdbcType=TIMESTAMP}, #{item.symbol,jdbcType=VARCHAR}, #{item.dataType,jdbcType=VARCHAR},
             #{item.source,jdbcType=VARCHAR}, #{item.price,jdbcType=DECIMAL}, #{item.quantity,jdbcType=DECIMAL},
             #{item.quoteQuantity,jdbcType=DECIMAL}, #{item.rawData,jdbcType=LONGVARCHAR}, #{item.qualityScore,jdbcType=DOUBLE},
             #{item.createdAt,jdbcType=TIMESTAMP}, #{item.version,jdbcType=VARCHAR},
             #{item.updatedAt,jdbcType=TIMESTAMP}, #{item.side,jdbcType=VARCHAR},
             #{item.tradeId,jdbcType=BIGINT})
        </foreach>
    </insert>

    <!-- 更新市场数据 -->
    <!-- <update id="updateById" parameterType="com.trading.common.dto.TradeData">
        UPDATE market_data
        SET timestamp = #{timestamp,jdbcType=TIMESTAMP},
            symbol = #{symbol,jdbcType=VARCHAR},
            data_type = #{dataType,jdbcType=VARCHAR},
            source = #{source,jdbcType=VARCHAR},
            price = #{price,jdbcType=DECIMAL},
            quantity = #{quantity,jdbcType=DECIMAL},
            amount = #{amount,jdbcType=DECIMAL},
            raw_data = #{rawData,jdbcType=LONGVARCHAR},
            quality_score = #{qualityScore,jdbcType=DOUBLE},
            version = #{version,jdbcType=VARCHAR},

            updated_at = #{updatedAt,jdbcType=TIMESTAMP},
            side = #{side,jdbcType=VARCHAR},
            trade_id = #{tradeId,jdbcType=BIGINT},
            order_id = #{orderId,jdbcType=BIGINT}
        WHERE id = #{id,jdbcType=BIGINT}
    </update> -->

    <!-- 删除市场数据 -->
    <!-- <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM market_data
        WHERE id = #{id,jdbcType=BIGINT}
    </delete> -->

    <!-- 删除指定时间之前的市场数据 -->
    <delete id="deleteByTimestampBefore">
        DELETE FROM market_data
        WHERE timestamp &lt; #{beforeTime,jdbcType=TIMESTAMP}
    </delete>

</mapper>
