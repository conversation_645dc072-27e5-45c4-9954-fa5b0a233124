-- 高性能数据库索引优化脚本
-- 为crypto-market-data模块添加关键查询索引

USE crypto_market_data;

-- ================================
-- 1. Market Data 表索引优化
-- ================================

-- 主要查询索引：按交易对和时间查询
CREATE INDEX IF NOT EXISTS idx_market_data_symbol_time 
ON market_data(symbol, timestamp DESC);

-- 交易时间索引：按trade_time查询
CREATE INDEX IF NOT EXISTS idx_market_data_trade_time 
ON market_data(trade_time DESC);

-- 复合索引：交易对 + 数据类型 + 时间范围查询
CREATE INDEX IF NOT EXISTS idx_market_data_symbol_type_time 
ON market_data(symbol, data_type, timestamp DESC);

-- 交易ID索引：用于去重和查询
CREATE INDEX IF NOT EXISTS idx_market_data_trade_id 
ON market_data(trade_id);

-- 价格范围查询索引
CREATE INDEX IF NOT EXISTS idx_market_data_price_time 
ON market_data(price, timestamp DESC);

-- ================================
-- 2. Kline Data 表索引优化
-- ================================

-- 主要查询索引：按交易对、时间间隔和开盘时间查询
CREATE INDEX IF NOT EXISTS idx_kline_data_symbol_interval_time 
ON kline_data(symbol, interval_type, open_time DESC);

-- 收盘时间索引：按收盘时间查询
CREATE INDEX IF NOT EXISTS idx_kline_data_close_time 
ON kline_data(close_time DESC);

-- 复合索引：交易对 + 时间间隔 + 收盘时间
CREATE INDEX IF NOT EXISTS idx_kline_data_symbol_interval_close 
ON kline_data(symbol, interval_type, close_time DESC);

-- 成交量索引：用于成交量分析
CREATE INDEX IF NOT EXISTS idx_kline_data_volume_time 
ON kline_data(volume DESC, open_time DESC);

-- 价格变动索引：用于价格分析
CREATE INDEX IF NOT EXISTS idx_kline_data_price_change 
ON kline_data(symbol, close_price, open_time DESC);

-- ================================
-- 3. Depth Data 表索引优化
-- ================================

-- 主要查询索引：按交易对和时间查询
CREATE INDEX IF NOT EXISTS idx_depth_data_symbol_time 
ON depth_data(symbol, timestamp DESC);

-- 深度级别索引：按深度级别查询
CREATE INDEX IF NOT EXISTS idx_depth_data_levels_time 
ON depth_data(levels, timestamp DESC);

-- 复合索引：交易对 + 深度级别 + 时间
CREATE INDEX IF NOT EXISTS idx_depth_data_symbol_levels_time 
ON depth_data(symbol, levels, timestamp DESC);

-- 更新ID索引：用于增量更新
CREATE INDEX IF NOT EXISTS idx_depth_data_update_id 
ON depth_data(last_update_id DESC);

-- ================================
-- 4. Data Quality Stats 表索引优化
-- ================================

-- 主要查询索引：按交易对、数据类型和时间查询
CREATE INDEX IF NOT EXISTS idx_quality_stats_symbol_type_time 
ON data_quality_stats(symbol, data_type, date_hour DESC);

-- 质量分数索引：用于质量分析
CREATE INDEX IF NOT EXISTS idx_quality_stats_score_time 
ON data_quality_stats(quality_score DESC, date_hour DESC);

-- 错误统计索引：用于错误分析
CREATE INDEX IF NOT EXISTS idx_quality_stats_errors 
ON data_quality_stats(error_count DESC, date_hour DESC);

-- ================================
-- 5. 系统配置表索引
-- ================================

-- 配置键索引
CREATE INDEX IF NOT EXISTS idx_system_config_key 
ON system_config(config_key);

-- 配置分组索引
CREATE INDEX IF NOT EXISTS idx_system_config_group 
ON system_config(config_group);

-- ================================
-- 6. 分区表优化（可选）
-- ================================

-- 为大表创建按时间分区（MySQL 8.0+）
-- 注意：这需要重建表，生产环境请谨慎操作

-- Market Data 按月分区示例（注释掉，需要手动执行）
/*
ALTER TABLE market_data PARTITION BY RANGE (YEAR(timestamp) * 100 + MONTH(timestamp)) (
    PARTITION p202508 VALUES LESS THAN (202509),
    PARTITION p202509 VALUES LESS THAN (202510),
    PARTITION p202510 VALUES LESS THAN (202511),
    PARTITION p202511 VALUES LESS THAN (202512),
    PARTITION p202512 VALUES LESS THAN (202601),
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
*/

-- ================================
-- 7. 索引使用情况监控
-- ================================

-- 创建索引使用情况监控视图
CREATE OR REPLACE VIEW v_index_usage AS
SELECT 
    TABLE_SCHEMA,
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    CARDINALITY,
    CASE 
        WHEN INDEX_NAME = 'PRIMARY' THEN 'PRIMARY KEY'
        WHEN NON_UNIQUE = 0 THEN 'UNIQUE'
        ELSE 'INDEX'
    END AS INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'crypto_market_data'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- ================================
-- 8. 性能优化建议
-- ================================

-- 查询缓存优化（如果启用）
SET GLOBAL query_cache_size = 268435456;  -- 256MB
SET GLOBAL query_cache_type = ON;

-- InnoDB 缓冲池优化建议
-- SET GLOBAL innodb_buffer_pool_size = 1073741824;  -- 1GB，根据实际内存调整

-- 慢查询日志启用
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 1;  -- 记录超过1秒的查询

-- ================================
-- 9. 索引维护脚本
-- ================================

-- 分析表统计信息（定期执行）
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS sp_analyze_tables()
BEGIN
    ANALYZE TABLE market_data;
    ANALYZE TABLE kline_data;
    ANALYZE TABLE depth_data;
    ANALYZE TABLE data_quality_stats;
    SELECT 'Table analysis completed' AS status;
END //
DELIMITER ;

-- 优化表（定期执行，注意会锁表）
DELIMITER //
CREATE PROCEDURE IF NOT EXISTS sp_optimize_tables()
BEGIN
    OPTIMIZE TABLE market_data;
    OPTIMIZE TABLE kline_data;
    OPTIMIZE TABLE depth_data;
    OPTIMIZE TABLE data_quality_stats;
    SELECT 'Table optimization completed' AS status;
END //
DELIMITER ;

-- ================================
-- 10. 执行确认
-- ================================

-- 显示创建的索引
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    GROUP_CONCAT(COLUMN_NAME ORDER BY SEQ_IN_INDEX) AS COLUMNS,
    INDEX_TYPE,
    CASE NON_UNIQUE WHEN 0 THEN 'UNIQUE' ELSE 'NON-UNIQUE' END AS UNIQUENESS
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'crypto_market_data'
    AND INDEX_NAME NOT IN ('PRIMARY')
GROUP BY TABLE_NAME, INDEX_NAME, INDEX_TYPE, NON_UNIQUE
ORDER BY TABLE_NAME, INDEX_NAME;

SELECT '✅ 高性能索引创建完成！' AS status;
SELECT '📊 建议定期执行 CALL sp_analyze_tables(); 更新统计信息' AS maintenance_tip;
SELECT '🔧 建议定期执行 CALL sp_optimize_tables(); 优化表结构（注意锁表）' AS optimization_tip;
