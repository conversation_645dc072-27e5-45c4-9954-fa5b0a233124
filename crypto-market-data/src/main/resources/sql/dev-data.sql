-- 开发环境初始数据
-- Crypto Trading System Development Data for MySQL

USE crypto_market_data;

-- 插入开发环境K线测试数据
INSERT IGNORE INTO kline_data (symbol, interval_type, open_time, close_time, open_price, high_price, low_price, close_price, volume, quote_volume, trade_count, taker_buy_volume, taker_buy_quote_volume) VALUES
('BTCUSDT', '1m', '2024-01-01 00:00:00.000', '2024-01-01 00:01:00.000', 50000.00000000, 50100.00000000, 49900.00000000, 50050.00000000, 10.50000000, 525000.00000000, 100, 5.50000000, 275000.00000000),
('BTCUSDT', '5m', '2024-01-01 00:00:00.000', '2024-01-01 00:05:00.000', 50000.00000000, 50200.00000000, 49800.00000000, 50150.00000000, 52.30000000, 2615000.00000000, 500, 26.20000000, 1310000.00000000),
('ETHUSDT', '1m', '2024-01-01 00:00:00.000', '2024-01-01 00:01:00.000', 3000.00000000, 3010.00000000, 2990.00000000, 3005.00000000, 50.00000000, 150000.00000000, 80, 25.00000000, 75000.00000000),
('ETHUSDT', '5m', '2024-01-01 00:00:00.000', '2024-01-01 00:05:00.000', 3000.00000000, 3020.00000000, 2980.00000000, 3015.00000000, 250.00000000, 750000.00000000, 400, 125.00000000, 375000.00000000),
('BNBUSDT', '1m', '2024-01-01 00:00:00.000', '2024-01-01 00:01:00.000', 300.00000000, 302.00000000, 298.00000000, 301.00000000, 100.00000000, 30000.00000000, 50, 50.00000000, 15000.00000000);

-- 插入开发环境市场数据
INSERT IGNORE INTO market_data (symbol, data_type, timestamp, price, quantity, amount, side, trade_id) VALUES
('BTCUSDT', 'trade', '2024-01-01 00:00:00.000', 50000.00000000, 0.10000000, 5000.00000000, 'BUY', 1001),
('BTCUSDT', 'trade', '2024-01-01 00:00:01.000', 50010.00000000, 0.05000000, 2500.50000000, 'SELL', 1002),
('BTCUSDT', 'trade', '2024-01-01 00:00:02.000', 50005.00000000, 0.20000000, 10001.00000000, 'BUY', 1003),
('ETHUSDT', 'trade', '2024-01-01 00:00:00.000', 3000.00000000, 1.00000000, 3000.00000000, 'SELL', 2001),
('ETHUSDT', 'trade', '2024-01-01 00:00:01.000', 3005.00000000, 0.50000000, 1502.50000000, 'BUY', 2002),
('BNBUSDT', 'trade', '2024-01-01 00:00:00.000', 300.00000000, 2.00000000, 600.00000000, 'BUY', 3001);

-- 插入开发环境深度数据
INSERT IGNORE INTO depth_data (symbol, timestamp, levels, bids, asks, last_update_id) VALUES
('BTCUSDT', '2024-01-01 00:00:00.000', 10, '[["49999.00000000","1.00000000"],["49998.00000000","2.00000000"],["49997.00000000","1.50000000"],["49996.00000000","3.00000000"],["49995.00000000","2.50000000"],["49994.00000000","1.80000000"],["49993.00000000","2.20000000"],["49992.00000000","1.70000000"],["49991.00000000","2.80000000"],["49990.00000000","3.50000000"]]', '[["50001.00000000","1.50000000"],["50002.00000000","2.50000000"],["50003.00000000","1.80000000"],["50004.00000000","3.20000000"],["50005.00000000","2.80000000"],["50006.00000000","1.90000000"],["50007.00000000","2.30000000"],["50008.00000000","1.60000000"],["50009.00000000","2.90000000"],["50010.00000000","3.60000000"]]', 1001),
('ETHUSDT', '2024-01-01 00:00:00.000', 10, '[["2999.00000000","10.00000000"],["2998.00000000","20.00000000"],["2997.00000000","15.00000000"],["2996.00000000","30.00000000"],["2995.00000000","25.00000000"],["2994.00000000","18.00000000"],["2993.00000000","22.00000000"],["2992.00000000","17.00000000"],["2991.00000000","28.00000000"],["2990.00000000","35.00000000"]]', '[["3001.00000000","15.00000000"],["3002.00000000","25.00000000"],["3003.00000000","18.00000000"],["3004.00000000","32.00000000"],["3005.00000000","28.00000000"],["3006.00000000","19.00000000"],["3007.00000000","23.00000000"],["3008.00000000","16.00000000"],["3009.00000000","29.00000000"],["3010.00000000","36.00000000"]]', 2001);

-- 插入开发环境数据质量统计
INSERT IGNORE INTO data_quality_stats (symbol, data_type, date_hour, total_count, success_count, error_count, avg_latency, max_latency, avg_quality_score) VALUES
('BTCUSDT', 'trade', '2024-01-01 00:00:00.000', 1000, 980, 20, 15.50, 50, 0.9800),
('BTCUSDT', 'kline', '2024-01-01 00:00:00.000', 60, 59, 1, 12.30, 45, 0.9833),
('BTCUSDT', 'depth', '2024-01-01 00:00:00.000', 120, 118, 2, 22.10, 80, 0.9833),
('ETHUSDT', 'trade', '2024-01-01 00:00:00.000', 800, 790, 10, 18.20, 60, 0.9875),
('ETHUSDT', 'depth', '2024-01-01 00:00:00.000', 120, 118, 2, 22.10, 80, 0.9833),
('BNBUSDT', 'trade', '2024-01-01 00:00:00.000', 500, 500, 0, 10.50, 30, 1.0000);
