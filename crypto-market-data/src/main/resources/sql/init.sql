-- =====================================================
-- 加密货币市场数据系统 - 数据库初始化脚本
-- =====================================================

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS crypto_market_data 
    CHARACTER SET utf8mb4 
    COLLATE utf8mb4_unicode_ci;

USE crypto_market_data;

-- =====================================================
-- 1. 市场数据表 (market_data)
-- =====================================================
CREATE TABLE IF NOT EXISTS market_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    symbol VARCHAR(20) NOT NULL COMMENT '交易对符号',
    data_type VARCHAR(20) NOT NULL DEFAULT 'trade' COMMENT '数据类型(depth/trade/stats)',
    timestamp TIMESTAMP(3) NOT NULL COMMENT '时间戳',
    trade_time TIMESTAMP(3) NULL COMMENT '成交时间',
    price DECIMAL(20,8) COMMENT '价格',
    quantity DECIMAL(20,8) COMMENT '数量',
    amount DECIMAL(20,8) COMMENT '成交额',
    side VARCHAR(10) COMMENT '买卖方向(BUY/SELL)',
    trade_id BIGINT COMMENT '交易ID',
    order_id BIGINT COMMENT '订单ID',
    source VARCHAR(20) DEFAULT 'binance' COMMENT '数据源',
    quality_score DOUBLE DEFAULT 1.0 COMMENT '数据质量分数',
    raw_data TEXT COMMENT '原始数据JSON',
    version VARCHAR(10) DEFAULT '1.0' COMMENT '数据版本',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    
    INDEX idx_symbol_timestamp (symbol, timestamp),
    INDEX idx_symbol_data_type (symbol, data_type),
    INDEX idx_trade_time (trade_time),
    INDEX idx_timestamp (timestamp),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='市场数据表';

-- =====================================================
-- 2. K线数据表 (kline_data)
-- =====================================================
CREATE TABLE IF NOT EXISTS kline_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    symbol VARCHAR(20) NOT NULL COMMENT '交易对符号',
    interval_type VARCHAR(10) NOT NULL COMMENT 'K线间隔(1m,3m,5m,15m,30m,1h,4h,1d)',
    open_time TIMESTAMP(3) NOT NULL COMMENT '开盘时间',
    close_time TIMESTAMP(3) NOT NULL COMMENT '收盘时间',
    open_price DECIMAL(20,8) NOT NULL COMMENT '开盘价',
    high_price DECIMAL(20,8) NOT NULL COMMENT '最高价',
    low_price DECIMAL(20,8) NOT NULL COMMENT '最低价',
    close_price DECIMAL(20,8) NOT NULL COMMENT '收盘价',
    volume DECIMAL(20,8) NOT NULL COMMENT '成交量',
    quote_volume DECIMAL(20,8) NOT NULL COMMENT '成交额',
    trade_count INT NOT NULL COMMENT '成交笔数',
    taker_buy_volume DECIMAL(20,8) COMMENT '主动买入成交量',
    taker_buy_quote_volume DECIMAL(20,8) COMMENT '主动买入成交额',
    source VARCHAR(20) DEFAULT 'binance' COMMENT '数据源',
    quality_score DOUBLE DEFAULT 1.0 COMMENT '数据质量分数',
    raw_data TEXT COMMENT '原始数据JSON',
    version VARCHAR(10) DEFAULT '1.0' COMMENT '数据版本',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    
    UNIQUE KEY uk_symbol_interval_open_time (symbol, interval_type, open_time),
    INDEX idx_symbol_interval (symbol, interval_type),
    INDEX idx_open_time (open_time),
    INDEX idx_close_time (close_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='K线数据表';

-- =====================================================
-- 3. 深度数据表 (depth_data)
-- =====================================================
CREATE TABLE IF NOT EXISTS depth_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    symbol VARCHAR(20) NOT NULL COMMENT '交易对符号',
    last_update_id BIGINT NOT NULL COMMENT '最后更新ID',
    bids TEXT COMMENT '买盘数据JSON',
    asks TEXT COMMENT '卖盘数据JSON',
    source VARCHAR(20) DEFAULT 'binance' COMMENT '数据源',
    levels INT DEFAULT 20 COMMENT '深度档位数',
    timestamp TIMESTAMP(3) NOT NULL COMMENT '时间戳',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    
    UNIQUE KEY uk_symbol_update_id (symbol, last_update_id),
    INDEX idx_symbol_timestamp (symbol, timestamp),
    INDEX idx_timestamp (timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='深度数据表';

-- =====================================================
-- 4. 24小时统计数据表 (ticker_data)
-- =====================================================
CREATE TABLE IF NOT EXISTS ticker_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    symbol VARCHAR(20) NOT NULL COMMENT '交易对符号',
    price_change DECIMAL(20,8) COMMENT '24小时价格变化',
    price_change_percent DECIMAL(10,4) COMMENT '24小时价格变化百分比',
    weighted_avg_price DECIMAL(20,8) COMMENT '加权平均价',
    prev_close_price DECIMAL(20,8) COMMENT '前收盘价',
    last_price DECIMAL(20,8) COMMENT '最新价格',
    last_qty DECIMAL(20,8) COMMENT '最新成交量',
    bid_price DECIMAL(20,8) COMMENT '买一价',
    ask_price DECIMAL(20,8) COMMENT '卖一价',
    open_price DECIMAL(20,8) COMMENT '今日开盘价',
    high_price DECIMAL(20,8) COMMENT '今日最高价',
    low_price DECIMAL(20,8) COMMENT '今日最低价',
    volume DECIMAL(20,8) COMMENT '24小时成交量',
    quote_volume DECIMAL(20,8) COMMENT '24小时成交额',
    open_time TIMESTAMP(3) COMMENT '24小时开盘时间',
    close_time TIMESTAMP(3) COMMENT '24小时收盘时间',
    first_id BIGINT COMMENT '首笔成交id',
    last_id BIGINT COMMENT '末笔成交id',
    count INT COMMENT '成交笔数',
    source VARCHAR(20) DEFAULT 'binance' COMMENT '数据源',
    timestamp TIMESTAMP(3) NOT NULL COMMENT '时间戳',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    
    UNIQUE KEY uk_symbol_timestamp (symbol, timestamp),
    INDEX idx_symbol (symbol),
    INDEX idx_timestamp (timestamp)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='24小时统计数据表';

-- =====================================================
-- 5. 数据质量监控表 (data_quality_log)
-- =====================================================
CREATE TABLE IF NOT EXISTS data_quality_log (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    data_type VARCHAR(20) NOT NULL COMMENT '数据类型',
    symbol VARCHAR(20) NOT NULL COMMENT '交易对符号',
    quality_score DOUBLE NOT NULL COMMENT '质量分数',
    issues TEXT COMMENT '质量问题描述',
    fingerprint VARCHAR(255) COMMENT '数据指纹',
    timestamp TIMESTAMP(3) NOT NULL COMMENT '时间戳',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    
    INDEX idx_data_type_symbol (data_type, symbol),
    INDEX idx_timestamp (timestamp),
    INDEX idx_quality_score (quality_score)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据质量监控表';

-- =====================================================
-- 6. 系统配置表 (system_config)
-- =====================================================
CREATE TABLE IF NOT EXISTS system_config (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    config_key VARCHAR(100) NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    config_type VARCHAR(20) DEFAULT 'STRING' COMMENT '配置类型',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
    
    UNIQUE KEY uk_config_key (config_key),
    INDEX idx_is_active (is_active)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- =====================================================
-- 7. 插入默认配置数据
-- =====================================================
INSERT IGNORE INTO system_config (config_key, config_value, description, config_type) VALUES
('market.data.batch_size', '200', '市场数据批处理大小', 'INTEGER'),
('market.data.batch_timeout', '100', '批处理超时时间(毫秒)', 'INTEGER'),
('websocket.reconnect.max_attempts', '5', 'WebSocket最大重连次数', 'INTEGER'),
('data.quality.min_score', '0.8', '数据质量最低分数', 'DOUBLE'),
('cache.ttl.market_data', '300', '市场数据缓存TTL(秒)', 'INTEGER'),
('influxdb.batch_size', '500', 'InfluxDB批处理大小', 'INTEGER'),
('influxdb.flush_interval', '2000', 'InfluxDB刷新间隔(毫秒)', 'INTEGER');

-- =====================================================
-- 8. 创建视图
-- =====================================================

-- 最新市场数据视图
CREATE OR REPLACE VIEW v_latest_market_data AS
SELECT 
    symbol,
    data_type,
    timestamp,
    trade_time,
    price,
    quantity,
    amount,
    side,
    source,
    quality_score
FROM market_data 
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY timestamp DESC;

-- 数据质量统计视图
CREATE OR REPLACE VIEW v_data_quality_stats AS
SELECT 
    data_type,
    symbol,
    COUNT(*) as total_records,
    AVG(quality_score) as avg_quality_score,
    MIN(quality_score) as min_quality_score,
    MAX(quality_score) as max_quality_score,
    DATE(created_at) as date
FROM data_quality_log 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
GROUP BY data_type, symbol, DATE(created_at)
ORDER BY date DESC, avg_quality_score DESC;

-- =====================================================
-- 9. 创建存储过程
-- =====================================================

DELIMITER //

-- 清理历史数据存储过程
CREATE PROCEDURE IF NOT EXISTS CleanHistoryData(
    IN days_to_keep INT DEFAULT 30
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;
    
    START TRANSACTION;
    
    -- 清理市场数据
    DELETE FROM market_data 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    -- 清理数据质量日志
    DELETE FROM data_quality_log 
    WHERE created_at < DATE_SUB(NOW(), INTERVAL days_to_keep DAY);
    
    COMMIT;
    
    SELECT CONCAT('历史数据清理完成，保留最近 ', days_to_keep, ' 天的数据') as result;
END //

-- 数据质量检查存储过程
CREATE PROCEDURE IF NOT EXISTS CheckDataQuality(
    IN check_symbol VARCHAR(20) DEFAULT NULL,
    IN check_hours INT DEFAULT 1
)
BEGIN
    SELECT 
        symbol,
        data_type,
        COUNT(*) as record_count,
        AVG(quality_score) as avg_quality,
        COUNT(CASE WHEN quality_score < 0.8 THEN 1 END) as low_quality_count,
        MAX(timestamp) as latest_timestamp
    FROM market_data 
    WHERE (check_symbol IS NULL OR symbol = check_symbol)
      AND timestamp >= DATE_SUB(NOW(), INTERVAL check_hours HOUR)
    GROUP BY symbol, data_type
    ORDER BY avg_quality DESC;
END //

DELIMITER ;

-- =====================================================
-- 10. 权限设置（可选）
-- =====================================================

-- 创建只读用户（可选）
-- CREATE USER IF NOT EXISTS 'crypto_readonly'@'%' IDENTIFIED BY 'readonly_password';
-- GRANT SELECT ON crypto_market_data.* TO 'crypto_readonly'@'%';

-- 创建应用用户（可选）
-- CREATE USER IF NOT EXISTS 'crypto_app'@'%' IDENTIFIED BY 'app_password';
-- GRANT SELECT, INSERT, UPDATE, DELETE ON crypto_market_data.* TO 'crypto_app'@'%';

-- FLUSH PRIVILEGES;

-- =====================================================
-- 初始化完成
-- =====================================================
SELECT 'crypto_market_data 数据库初始化完成！' as message;
