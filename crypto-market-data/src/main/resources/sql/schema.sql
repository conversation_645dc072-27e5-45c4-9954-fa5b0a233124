-- 修正后的币安量化交易系统数据库表结构
-- Crypto Trading System Database Schema
-- 支持K线数据、深度数据、交易数据、统计数据存储

-- 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS crypto_market_data
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

-- 创建测试数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS crypto_market_data_test
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE crypto_market_data;

-- K线数据表（保持不变）
CREATE TABLE IF NOT EXISTS kline_data (
                                          id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                          symbol VARCHAR(20) NOT NULL COMMENT '交易对符号',
    interval_type VARCHAR(10) NOT NULL COMMENT '时间间隔',
    open_time TIMESTAMP(3) NOT NULL COMMENT '开盘时间',
    close_time TIMESTAMP(3) NOT NULL COMMENT '收盘时间',
    open_price DECIMAL(20,8) NOT NULL COMMENT '开盘价',
    high_price DECIMAL(20,8) NOT NULL COMMENT '最高价',
    low_price DECIMAL(20,8) NOT NULL COMMENT '最低价',
    close_price DECIMAL(20,8) NOT NULL COMMENT '收盘价',
    volume DECIMAL(20,8) NOT NULL COMMENT '成交量',
    quote_volume DECIMAL(20,8) COMMENT '成交额',
    trade_count INT COMMENT '成交笔数',
    taker_buy_volume DECIMAL(20,8) COMMENT '主动买入成交量',
    taker_buy_quote_volume DECIMAL(20,8) COMMENT '主动买入成交额',
    is_closed BOOLEAN DEFAULT FALSE COMMENT '是否已收盘',
    data_type VARCHAR(20) COMMENT '数据类型',
    source VARCHAR(20) DEFAULT 'binance' COMMENT '数据源',
    latency BIGINT COMMENT '延迟（毫秒）',
    raw_data TEXT COMMENT '原始数据',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',

    -- 索引
    INDEX idx_symbol_interval_time (symbol, interval_type, open_time),
    INDEX idx_symbol_time (symbol, open_time),
    INDEX idx_created_at (created_at),
    INDEX idx_close_time (close_time),

    -- 唯一约束
    UNIQUE KEY uk_symbol_interval_open_time (symbol, interval_type, open_time)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='K线数据表';

-- 市场数据表（深度、交易、统计数据） - 使用虚拟列解决唯一约束问题
CREATE TABLE IF NOT EXISTS market_data (
                                           id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                           symbol VARCHAR(20) NOT NULL COMMENT '交易对符号',
    data_type VARCHAR(20) NOT NULL DEFAULT 'trade' COMMENT '数据类型(depth/trade/stats)',
    timestamp TIMESTAMP(3) NOT NULL COMMENT '时间戳',
    trade_time TIMESTAMP(3) NULL COMMENT '成交时间',
    price DECIMAL(20,8) COMMENT '价格',
    quantity DECIMAL(20,8) COMMENT '数量',
    quote_quantity DECIMAL(20, 8) COMMENT '报价数量',
    amount DECIMAL(20,8) COMMENT '成交额',
    side VARCHAR(10) COMMENT '买卖方向(BUY/SELL)',
    trade_id BIGINT COMMENT '交易ID',
    order_id BIGINT COMMENT '订单ID',
    source VARCHAR(20) DEFAULT 'binance' COMMENT '数据源',
    raw_data TEXT COMMENT '原始数据JSON',
    version VARCHAR(10) DEFAULT '1.0' COMMENT '数据版本',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',

    -- 添加虚拟列处理NULL值
    trade_id_for_unique BIGINT
    GENERATED ALWAYS AS (COALESCE(trade_id, -1))
    VIRTUAL COMMENT '用于唯一约束的trade_id',

    -- 索引
    INDEX idx_symbol_type_time (symbol, data_type, timestamp),
    INDEX idx_symbol_time (symbol, timestamp),
    INDEX idx_data_type_time (data_type, timestamp),
    INDEX idx_created_at (created_at),
    INDEX idx_trade_id (trade_id),

    -- 唯一约束（使用虚拟列）
    UNIQUE KEY uk_symbol_type_timestamp_trade (
                                                  symbol,
                                                  data_type,
                                                  timestamp,
                                                  trade_id_for_unique
                                              )
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='市场数据表';

-- 深度数据表（订单簿）保持不变
CREATE TABLE IF NOT EXISTS depth_data (
                                          id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                          symbol VARCHAR(20) NOT NULL COMMENT '交易对符号',
    timestamp TIMESTAMP(3) NOT NULL COMMENT '时间戳',
    levels INT NOT NULL COMMENT '深度档位数',
    bids TEXT NOT NULL COMMENT '买盘数据JSON',
    asks TEXT NOT NULL COMMENT '卖盘数据JSON',
    last_update_id BIGINT COMMENT '最后更新ID',
    source VARCHAR(20) DEFAULT 'binance' COMMENT '数据源',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',

    -- 索引
    INDEX idx_symbol_time (symbol, timestamp),
    INDEX idx_symbol_levels_time (symbol, levels, timestamp),
    INDEX idx_created_at (created_at),
    UNIQUE KEY uk_symbol (symbol)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='深度数据表';

-- 数据质量监控表保持不变
CREATE TABLE IF NOT EXISTS data_quality_stats (
                                                  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
                                                  symbol VARCHAR(20) NOT NULL COMMENT '交易对符号',
    data_type VARCHAR(20) NOT NULL COMMENT '数据类型',
    date_hour DATETIME NOT NULL COMMENT '统计时间（小时）',
    total_count BIGINT DEFAULT 0 COMMENT '总数据量',
    success_count BIGINT DEFAULT 0 COMMENT '成功数量',
    error_count BIGINT DEFAULT 0 COMMENT '错误数量',
    avg_latency DOUBLE DEFAULT 0 COMMENT '平均延迟',
    max_latency BIGINT DEFAULT 0 COMMENT '最大延迟',
    min_quality_score DOUBLE DEFAULT 1.0 COMMENT '最低质量分数',
    avg_quality_score DOUBLE DEFAULT 1.0 COMMENT '平均质量分数',
    created_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
    updated_at TIMESTAMP(3) DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',

    -- 索引
    INDEX idx_symbol_type_hour (symbol, data_type, date_hour),
    INDEX idx_date_hour (date_hour),

    -- 唯一约束
    UNIQUE KEY uk_symbol_type_hour (symbol, data_type, date_hour)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='数据质量统计表';

-- 视图保持不变
CREATE OR REPLACE VIEW v_latest_kline_data AS
SELECT
    symbol,
    interval_type,
    MAX(open_time) as latest_time,
    COUNT(*) as total_count
FROM kline_data
GROUP BY symbol, interval_type;

CREATE OR REPLACE VIEW v_data_quality_stats AS
SELECT
    symbol,
    data_type,
    DATE(created_at) as date,
    COUNT(*) as total_count,
    AVG(quality_score) as avg_quality_score,
    MIN(quality_score) as min_quality_score,
    AVG(latency) as avg_latency
FROM (
    SELECT symbol, 'kline' as data_type, quality_score, latency, created_at FROM kline_data
    UNION ALL
    SELECT symbol, data_type, quality_score, 0 as latency, created_at FROM market_data
    ) combined_data
GROUP BY symbol, data_type, DATE(created_at);