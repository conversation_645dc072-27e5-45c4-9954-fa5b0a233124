---
# 市场数据模块核心配置
# 此文件仅包含市场数据特有的配置，其他基础配置继承自crypto-common模块
# 通过Maven profile激活，不再需要复杂的spring.profiles.active和import
spring:
  lifecycle:
    timeout-per-shutdown-phase: 30s
  application:
    name: crypto-market-data
  profiles:
    active: dev
  datasource:
    # 数据库连接配置 - 从docker-compose.yml恢复
    # 注意：为了安全，建议为应用程序创建一个专用的数据库用户，而不是使用root。
    url: ***********************************************************************************************************************
    username: root
    password: root
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      connection-timeout: 30000
      minimum-idle: 10
      maximum-pool-size: 20 # Reduced from 100 to expose leaks faster
      idle-timeout: 600000
      max-lifetime: 1800000
      validation-timeout: 5000
      leak-detection-threshold: 20000 # Enable leak detection

server:
  port: 19527
  shutdown: graceful

logging:
  level:
    com.trading.market: DEBUG

# Actuator Management Endpoints
management:
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always


# MyBatis Plus 配置
mybatis-plus:
  # MyBatis日志实现已由logging.level配置控制，不再需要log-impl
  # 指定Mapper XML文件的位置
  mapper-locations: classpath*:mapper/**/*.xml
  # 类型别名包，用于简化XML中实体类的引用
  type-aliases-package: com.trading.market.data.entity
  global-config:
    db-config:
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-not-delete-value: 0
      logic-delete-value: 1
      # 自动填充字段
      id-type: auto

# InfluxDB配置
trading:
  market-data:
    storage:
      influxdb:
        enabled: true
        url: http://localhost:8086
        # TODO: 请将下面的占位符替换为您真实的InfluxDB管理员令牌
        token: "zmJ1sGNooabOZbuEWW3MdwUIeL9btWRXJgX_Y4KgTIxJ3GhCxsWqi25qRQr_4FqrcMMWEibD4LkD397IKG1H0w=="
        org: binance
        bucket: market_data
