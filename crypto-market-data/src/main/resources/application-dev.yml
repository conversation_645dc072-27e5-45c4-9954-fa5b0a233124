---
# 生产环境配置
# Production Environment Configuration

spring:
  server:
    address: 0.0.0.0
    port: 19527
  main:
    allow-bean-definition-overriding: true
  # JVM监控配置
  jmx:
    enabled: true
    default-domain: crypto-market-data


  # 应用监控配置
  management:
    endpoints:
      web:
        exposure:
          include: health,metrics,prometheus,info,env,configprops,threaddump,heapdump
        base-path: /actuator
    endpoint:
      health:
        show-details: always
        show-components: always
      metrics:
        enabled: true
      prometheus:
        enabled: true
    metrics:
      export:
        prometheus:
          enabled: true
          step: 30s
        jmx:
          enabled: true
      distribution:
        percentiles-histogram:
          http.server.requests: true
          jvm.gc.pause: true
          jvm.memory.used: true
        percentiles:
          http.server.requests: 0.5,0.75,0.95,0.99
          jvm.gc.pause: 0.5,0.75,0.95,0.99
      tags:
        application: crypto-market-data
        environment: production

  # 数据源配置（生产环境）
  datasource:
    url: jdbc:mysql://${DB_HOST:localhost}:${DB_PORT:13306}/${DB_NAME:crypto_market_data}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:root}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 25          # 优化：减少最大连接数，避免资源浪费
      minimum-idle: 5                # 优化：减少最小空闲连接
      idle-timeout: 300000           # 5分钟空闲超时
      max-lifetime: 1800000          # 30分钟最大生命周期
      connection-timeout: 10000      # 优化：减少连接超时到10秒
      validation-timeout: 3000       # 优化：减少验证超时到3秒
      leak-detection-threshold: 60000 # 60秒泄漏检测
      connection-test-query: SELECT 1
      auto-commit: false

  sql:
    init:
      mode: always
      data-locations: classpath:sql/dev-data.sql
      schema-locations: classpath:sql/schema.sql

  # Redis配置（生产环境）
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:root}
      timeout: 5000
      connect-timeout: 5000
      lettuce:
        pool:
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: 3000
          time-between-eviction-runs: 30000
          min-evictable-idle-time: 60000
          test-on-borrow: true
          test-on-return: true
          test-while-idle: true
        shutdown-timeout: 100ms

  # Kafka配置（生产环境）
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:localhost:29092}
    producer:
      retries: 3
      batch-size: 16384
      linger-ms: 5
      buffer-memory: 33554432
      compression-type: snappy
      acks: all
      acknowledgments: all
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    consumer:
      group-id: crypto-market-data-production
      auto-offset-reset: latest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.springframework.kafka.support.serializer.JsonDeserializer
      properties:
        spring.json.trusted.packages: "*"


# 性能监控配置
trading:
  sdk:
    binance:
      apiKey: "87c1475c7a003d08400aa7051ee80fea1a105364a591ef2dbe2b80590eaeb379"
      secretKey: "d6aa03a75f489d4ce35bced30a1e80c5db0d70289938b14df4f7409a82e88b47"
      testnet: true
  market-data:
    # 数据收集器配置
    collector:
      enabled: true
      symbols:
        - BTCUSDT
        - ETHUSDT
        - BNBUSDT
        - SOLUSDT
        - DOGEUSDT
        # 历史数据收集配置
      historical:
        force-full-collection: false
        default-days: 90
        incremental-check-hours: 1
      data-types:
        kline:
          enabled: true
          intervals:
 
        depth:
          enabled: true
          levels:
            - 5
            - 10
            - 20
          speed: 100
        trade:
          enabled: true
        ticker:
          enabled: true
      # 性能配置
      performance:
        batch:
          kline-size: 500            # 优化：统一批处理大小
          trade-size: 500            # 优化：与应用层batch-size保持一致
          depth-size: 200            # 优化：深度数据批处理大小
        adaptive-batch:              # 动态批处理配置
          enabled: true
          base-batch-size: 500
          min-batch-size: 100
          max-batch-size: 2000
          system-load-threshold: 0.8
          adjustment-interval-ms: 30000
          kline:
            base-size: 200
            min-size: 50
            max-size: 800
          trade:
            base-size: 500
            min-size: 100
            max-size: 1000
          depth:
            base-size: 200
            min-size: 50
            max-size: 500
        rate-limit:
          request-delay-ms: 500
          max-concurrent-requests: 3
          token-bucket-capacity: 10
          token-refill-rate: 5.0
        retry:
          max-attempts: 3
          delay-ms: 1000
          exponential-backoff-multiplier: 2.0
          max-delay-ms: 30000
    # 性能监控
    monitoring:
      enabled: true
      report-interval-seconds: 30
      metrics-retention-minutes: 120
      detailed-monitoring: true
      jvm:
        enabled: true
        gc-monitoring: true
        memory-monitoring: true
        thread-monitoring: true
        gc-alert-threshold:
          young-gc-per-minute: 5
          full-gc-per-minute: 0
          gc-time-percentage: 10
        memory-alert-threshold:
          heap-usage-percentage: 80
          non-heap-usage-percentage: 85
      application:
        enabled: true
        response-time:
          enabled: true
          slow-request-threshold-ms: 100
          very-slow-request-threshold-ms: 500
        throughput:
          enabled: true
          low-throughput-threshold: 100
        error-rate:
          enabled: true
          high-error-rate-threshold: 0.01
      cache:
        enabled: true
        hit-rate:
          enabled: true
          low-hit-rate-threshold: 0.9
        size:
          enabled: true
          max-size-threshold: 10000
        eviction:
          enabled: true
          high-eviction-rate-threshold: 100
      websocket:
        enabled: true
        reuse-rate:
          enabled: true
          low-reuse-rate-threshold: 0.9
        health:
          enabled: true
          unhealthy-connection-threshold: 0.05
        pool-size:
          enabled: true
          max-pool-size-threshold: 100
      object-pool:
        enabled: true
        utilization:
          enabled: true
          low-utilization-threshold: 0.3
          high-utilization-threshold: 0.9
        hit-rate:
          enabled: true
          low-hit-rate-threshold: 0.8
      async-logging:
        enabled: true
        queue:
          enabled: true
          high-queue-size-threshold: 1000
        latency:
          enabled: true
          high-latency-threshold-ms: 10
    storage:
      influxdb:
        enabled: true
        url: http://localhost:8086
        token: zmJ1sGNooabOZbuEWW3MdwUIeL9btWRXJgX_Y4KgTIxJ3GhCxsWqi25qRQr_4FqrcMMWEibD4LkD397IKG1H0w==
        org: binance
        bucket: market_data
    processor:
      validation:
        enabled: true
        skip-time-validation-for-historical: true # 为历史数据回补跳过时间验证
        price-deviation-threshold: 0.25 # 价格偏离阈值 (25%)
        volume-threshold: 0.0001 # 最小成交量阈值

# 性能调优配置
performance:
  object-pool:
    object-mapper-pool-size: 20
    string-builder-pool-size: 50
    data-model-pool-size: 100
    collection-pool-size: 50
  thread-pool:
    core-pool-size: 8              # 优化：基于CPU核心数调整
    max-pool-size: 32              # 优化：减少最大线程数
    queue-capacity: 2000           # 优化：增加队列容量
    keep-alive-seconds: 60
  virtual-threads:
    enabled: true
    max-threads: 500               # 优化：增加虚拟线程数量
    name-prefix: "market-data-vt-"
  batch-processing:
    batch-size: 500                # 优化：与配置中的batch size保持一致
    flush-interval-ms: 500         # 优化：减少刷新间隔，提高响应速度
    max-queue-size: 10000          # 优化：增加队列大小
  cache:
    caffeine:
      maximum-size: 10000
      expire-after-write: 300s
      expire-after-access: 180s
    redis:
      default-ttl: 3600s
      key-prefix: "crypto:market:"

# 告警配置
alerting:
  enabled: true
  notifications:
    email:
      enabled: false
      smtp-host: smtp.example.com
      smtp-port: 587
      username: <EMAIL>
      password: ${ALERT_EMAIL_PASSWORD:}
      recipients:
        - <EMAIL>
    webhook:
      enabled: true
      url: ${ALERT_WEBHOOK_URL:http://localhost:8080/alerts}
      timeout-ms: 5000
  rules:
    cpu-usage:
      enabled: true
      threshold: 80
      duration-minutes: 5
    memory-usage:
      enabled: true
      threshold: 85
      duration-minutes: 3
    gc-frequency:
      enabled: true
      young-gc-threshold: 5
      full-gc-threshold: 1
      duration-minutes: 1
    response-time:
      enabled: true
      threshold-ms: 100
      duration-minutes: 2
    error-rate:
      enabled: true
      threshold: 0.01
      duration-minutes: 1

# 日志配置
logging:
  level:
    com.trading.market: INFO
    com.trading.market.mapper: WARN
    com.trading.common: INFO
    org.springframework: WARN
    org.apache.kafka: WARN
    org.apache.ibatis: WARN
    java.sql: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS}[%d{SSS}] | %-5level | %-10thread | %-40logger{39} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS}[%d{SSS}] | %-5level | %-10thread | %-40logger{39} - %msg%n"
  file:
    name: logs/crypto-market-data-production.log
    max-size: 100MB
    max-history: 30
    total-size-cap: 3GB