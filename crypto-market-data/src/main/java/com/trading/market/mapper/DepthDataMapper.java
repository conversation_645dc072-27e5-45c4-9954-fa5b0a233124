package com.trading.market.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trading.common.dto.DepthData;
import org.apache.ibatis.annotations.*;

import java.time.Instant;
import java.util.List;

/**
 * 深度数据Mapper接口
 * 使用MyBatis-Plus提供CRUD操作
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Mapper
public interface DepthDataMapper extends BaseMapper<DepthData> {

    @Insert("INSERT INTO depth_data (symbol, last_update_id, bids, asks, source, levels, timestamp, created_at, updated_at) " +
            "VALUES(#{symbol}, #{lastUpdateId}, #{bids, typeHandler=com.trading.common.handler.JsonListTypeHandler}, #{asks, typeHandler=com.trading.common.handler.JsonListTypeHandler}, " +
            "#{source}, #{levels}, #{timestamp}, NOW(3), NOW(3)) " +
            "ON DUPLICATE KEY UPDATE " +
            "last_update_id = VALUES(last_update_id), " +
            "bids = VALUES(bids), " +
            "asks = VALUES(asks), " +
            "levels = VALUES(levels), " +
            "timestamp = VALUES(timestamp), " +
            "updated_at = NOW(3)")
    @Override
    int insert(DepthData entity);

    /**
     * 根据交易对和档位数查询深度数据
     */
    @Select("SELECT * FROM depth_data WHERE symbol = #{symbol} AND levels = #{levels} " +
            "ORDER BY timestamp DESC LIMIT #{limit}")
    List<DepthData> findBySymbolAndLevels(@Param("symbol") String symbol,
                                         @Param("levels") Integer levels,
                                         @Param("limit") int limit);

    /**
     * 根据交易对查询最新深度数据
     */
    @Select("SELECT * FROM depth_data WHERE symbol = #{symbol} AND levels = #{levels} ORDER BY timestamp DESC LIMIT 1")
    DepthData findLatestBySymbolAndLevels(@Param("symbol") String symbol, @Param("levels") Integer levels);

    /**
     * 根据时间范围查询深度数据
     */
    @Select("SELECT * FROM depth_data WHERE symbol = #{symbol} " +
            "AND timestamp BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY timestamp DESC LIMIT #{limit}")
    List<DepthData> findBySymbolAndTimeRange(@Param("symbol") String symbol,
                                           @Param("startTime") Instant startTime,
                                           @Param("endTime") Instant endTime,
                                           @Param("limit") int limit);

    /**
     * 根据交易对和档位数查询指定时间范围的深度数据
     */
    @Select("SELECT * FROM depth_data WHERE symbol = #{symbol} AND levels = #{levels} " +
            "AND timestamp BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY timestamp DESC LIMIT #{limit}")
    List<DepthData> findBySymbolAndLevelsAndTimeRange(@Param("symbol") String symbol,
                                                     @Param("levels") Integer levels,
                                                     @Param("startTime") Instant startTime,
                                                     @Param("endTime") Instant endTime,
                                                     @Param("limit") int limit);

    /**
     * 查询指定交易对的最新时间
     */
    @Select("SELECT MAX(timestamp) FROM depth_data WHERE symbol = #{symbol}")
    Instant findLatestTimeBySymbol(@Param("symbol") String symbol);

    /**
     * 查询指定交易对和档位数的最新时间
     */
    @Select("SELECT MAX(timestamp) FROM depth_data WHERE symbol = #{symbol} AND levels = #{levels}")
    Instant findLatestTimeBySymbolAndLevels(@Param("symbol") String symbol,
                                           @Param("levels") Integer levels);

    /**
     * 检查是否存在指定的深度数据
     */
    @Select("SELECT COUNT(1) FROM depth_data WHERE symbol = #{symbol} AND levels = #{levels} " +
            "AND timestamp = #{timestamp}")
    int existsBySymbolAndLevelsAndTimestamp(@Param("symbol") String symbol,
                                          @Param("levels") Integer levels,
                                          @Param("timestamp") Instant timestamp);

    /**
     * 根据质量分数查询深度数据
     */
    @Select("SELECT * FROM depth_data WHERE quality_score < #{threshold} " +
            "ORDER BY quality_score ASC LIMIT #{limit}")
    List<DepthData> findByQualityScoreLessThan(@Param("threshold") Double threshold,
                                             @Param("limit") int limit);

    /**
     * 统计指定交易对的深度数据数量
     */
    @Select("SELECT COUNT(1) FROM depth_data WHERE symbol = #{symbol}")
    long countBySymbol(@Param("symbol") String symbol);

    /**
     * 统计指定交易对和档位数的深度数据数量
     */
    @Select("SELECT COUNT(1) FROM depth_data WHERE symbol = #{symbol} AND levels = #{levels}")
    long countBySymbolAndLevels(@Param("symbol") String symbol,
                               @Param("levels") Integer levels);

    /**
     * 统计指定时间范围内的深度数据数量
     */
    @Select("SELECT COUNT(1) FROM depth_data WHERE symbol = #{symbol} " +
            "AND timestamp BETWEEN #{startTime} AND #{endTime}")
    long countBySymbolAndTimeRange(@Param("symbol") String symbol,
                                  @Param("startTime") Instant startTime,
                                  @Param("endTime") Instant endTime);

    /**
     * 查询所有支持的交易对
     */
    @Select("SELECT DISTINCT symbol FROM depth_data ORDER BY symbol")
    List<String> findAllSymbols();

    /**
     * 查询所有支持的档位数
     */
    @Select("SELECT DISTINCT levels FROM depth_data ORDER BY levels")
    List<Integer> findAllLevels();

    /**
     * 删除指定时间之前的深度数据
     */
    @Delete("DELETE FROM depth_data WHERE timestamp < #{beforeTime}")
    int deleteByTimestampBefore(@Param("beforeTime") Instant beforeTime);

    /**
     * 删除指定交易对的深度数据
     */
    @Delete("DELETE FROM depth_data WHERE symbol = #{symbol}")
    int deleteBySymbol(@Param("symbol") String symbol);

    /**
     * 批量插入或更新深度数据 (Upsert)
     */
    @Insert("<script>" +
            "INSERT INTO depth_data (symbol, last_update_id, bids, asks, source, levels, timestamp, created_at, updated_at) " +
            "VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.symbol}, #{item.lastUpdateId}, #{item.bids, typeHandler=com.trading.common.handler.JsonListTypeHandler}, #{item.asks, typeHandler=com.trading.common.handler.JsonListTypeHandler}, " +
            "#{item.source}, #{item.levels}, #{item.timestamp}, NOW(3), NOW(3))" +
            "</foreach>" +
            " ON DUPLICATE KEY UPDATE " +
            "last_update_id = VALUES(last_update_id), " +
            "bids = VALUES(bids), " +
            "asks = VALUES(asks), " +
            "levels = VALUES(levels), " +
            "timestamp = VALUES(timestamp), " +
            "updated_at = NOW(3)" +
            "</script>")
    int upsertBatch(@Param("list") List<DepthData> depthDataList);

    /**
     * 批量更新深度数据的质量分数
     */
    @Update("<script>" +
            "<foreach collection='list' item='item' separator=';'>" +
            "UPDATE depth_data SET quality_score = #{item.qualityScore}, updated_at = NOW() " +
            "WHERE id = #{item.id}" +
            "</foreach>" +
            "</script>")
    int batchUpdateQualityScore(@Param("list") List<DepthData> depthDataList);

    /**
     * 查询平均质量分数
     */
    @Select("SELECT AVG(quality_score) FROM depth_data WHERE symbol = #{symbol} " +
            "AND timestamp BETWEEN #{startTime} AND #{endTime}")
    Double getAverageQualityScore(@Param("symbol") String symbol,
                                 @Param("startTime") Instant startTime,
                                 @Param("endTime") Instant endTime);

    /**
     * 查询深度数据统计信息
     */
    @Select("SELECT symbol, levels, COUNT(1) as count, AVG(quality_score) as avg_quality, " +
            "MIN(timestamp) as min_time, MAX(timestamp) as max_time " +
            "FROM depth_data WHERE symbol = #{symbol} GROUP BY symbol, levels")
    List<DepthData> getDepthDataStatistics(@Param("symbol") String symbol);
}
