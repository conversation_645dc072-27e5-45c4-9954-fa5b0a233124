package com.trading.market.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trading.common.dto.DataQualityStats;
import org.apache.ibatis.annotations.*;

import java.time.Instant;
import java.util.List;

/**
 * 数据质量统计Mapper接口
 * 使用MyBatis-Plus提供CRUD操作
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Mapper
public interface DataQualityStatsMapper extends BaseMapper<DataQualityStats> {

    /**
     * 插入或更新数据质量统计（UPSERT）
     */
    @Insert("INSERT INTO data_quality_stats (date_hour, symbol, data_type, total_count, success_count, error_count, avg_latency, max_latency, min_quality_score, avg_quality_score, created_at, updated_at) " +
            "VALUES(#{dateHour}, #{symbol}, #{dataType}, #{totalCount}, #{successCount}, #{errorCount}, #{avgLatency}, #{maxLatency}, #{minQualityScore}, #{avgQualityScore}, #{createdAt}, #{updatedAt}) " +
            "ON DUPLICATE KEY UPDATE " +
            "total_count = total_count + VALUES(total_count), " +
            "success_count = success_count + VALUES(success_count), " +
            "error_count = error_count + VALUES(error_count), " +
            "avg_latency = (avg_latency * total_count + VALUES(avg_latency) * VALUES(total_count)) / (total_count + VALUES(total_count)), " +
            "max_latency = GREATEST(max_latency, VALUES(max_latency)), " +
            "min_quality_score = LEAST(min_quality_score, VALUES(min_quality_score)), " +
            "avg_quality_score = (avg_quality_score * total_count + VALUES(avg_quality_score) * VALUES(total_count)) / (total_count + VALUES(total_count)), " +
            "updated_at = VALUES(updated_at)")
    int upsert(DataQualityStats stats);

    /**
     * 根据交易对和数据类型查询质量统计
     */
    @Select("SELECT * FROM data_quality_stats WHERE symbol = #{symbol} AND data_type = #{dataType} " +
            "ORDER BY date_hour DESC LIMIT #{limit}")
    List<DataQualityStats> findBySymbolAndDataType(@Param("symbol") String symbol,
                                                  @Param("dataType") String dataType,
                                                  @Param("limit") int limit);

    /**
     * 根据交易对查询最新质量统计
     */
    @Select("SELECT * FROM data_quality_stats WHERE symbol = #{symbol} " +
            "ORDER BY date_hour DESC LIMIT 1")
    DataQualityStats findLatestBySymbol(@Param("symbol") String symbol);

    /**
     * 根据交易对和数据类型查询最新质量统计
     */
    @Select("SELECT * FROM data_quality_stats WHERE symbol = #{symbol} AND data_type = #{dataType} " +
            "ORDER BY date_hour DESC LIMIT 1")
    DataQualityStats findLatestBySymbolAndDataType(@Param("symbol") String symbol,
                                                  @Param("dataType") String dataType);

    /**
     * 根据数据类型查询最新质量统计
     */
    @Select("SELECT * FROM data_quality_stats WHERE data_type = #{dataType} " +
            "ORDER BY date_hour DESC LIMIT #{limit}")
    List<DataQualityStats> findLatestByDataType(@Param("dataType") String dataType,
                                              @Param("limit") int limit);

    /**
     * 根据时间范围查询质量统计
     */
    @Select("SELECT * FROM data_quality_stats WHERE symbol = #{symbol} AND data_type = #{dataType} " +
            "AND date_hour BETWEEN #{startTime} AND #{endTime} " +
            "ORDER BY date_hour DESC LIMIT #{limit}")
    List<DataQualityStats> findBySymbolAndDataTypeAndTimeRange(@Param("symbol") String symbol,
                                                              @Param("dataType") String dataType,
                                                              @Param("startTime") Instant startTime,
                                                              @Param("endTime") Instant endTime,
                                                              @Param("limit") int limit);

    /**
     * 查询指定交易对和数据类型的最新时间
     */
    @Select("SELECT MAX(date_hour) FROM data_quality_stats WHERE symbol = #{symbol} AND data_type = #{dataType}")
    Instant findLatestTimeBySymbolAndDataType(@Param("symbol") String symbol,
                                             @Param("dataType") String dataType);

    /**
     * 检查是否存在指定的质量统计数据
     */
    @Select("SELECT COUNT(1) FROM data_quality_stats WHERE symbol = #{symbol} AND data_type = #{dataType} " +
            "AND date_hour = #{dateHour}")
    int existsBySymbolAndDataTypeAndTimestamp(@Param("symbol") String symbol,
                                            @Param("dataType") String dataType,
                                            @Param("dateHour") Instant dateHour);

    /**
     * 根据成功率查询质量统计
     */
    @Select("SELECT * FROM data_quality_stats WHERE (success_count * 1.0 / total_count) < #{threshold} " +
            "ORDER BY (success_count * 1.0 / total_count) ASC LIMIT #{limit}")
    List<DataQualityStats> findBySuccessRateLessThan(@Param("threshold") Double threshold,
                                                   @Param("limit") int limit);

    /**
     * 根据错误率查询质量统计
     */
    @Select("SELECT * FROM data_quality_stats WHERE (error_count * 1.0 / total_count) > #{threshold} " +
            "ORDER BY (error_count * 1.0 / total_count) DESC LIMIT #{limit}")
    List<DataQualityStats> findByErrorRateGreaterThan(@Param("threshold") Double threshold,
                                                     @Param("limit") int limit);

    /**
     * 根据平均延迟查询质量统计
     */
    @Select("SELECT * FROM data_quality_stats WHERE avg_latency > #{threshold} " +
            "ORDER BY avg_latency DESC LIMIT #{limit}")
    List<DataQualityStats> findByAvgLatencyGreaterThan(@Param("threshold") Long threshold,
                                                      @Param("limit") int limit);

    /**
     * 统计指定交易对的质量数据数量
     */
    @Select("SELECT COUNT(1) FROM data_quality_stats WHERE symbol = #{symbol}")
    long countBySymbol(@Param("symbol") String symbol);

    /**
     * 统计指定数据类型的质量数据数量
     */
    @Select("SELECT COUNT(1) FROM data_quality_stats WHERE data_type = #{dataType}")
    long countByDataType(@Param("dataType") String dataType);

    /**
     * 统计指定时间范围内的质量数据数量
     */
    @Select("SELECT COUNT(1) FROM data_quality_stats WHERE symbol = #{symbol} AND data_type = #{dataType} " +
            "AND date_hour BETWEEN #{startTime} AND #{endTime}")
    long countBySymbolAndDataTypeAndTimeRange(@Param("symbol") String symbol,
                                            @Param("dataType") String dataType,
                                            @Param("startTime") Instant startTime,
                                            @Param("endTime") Instant endTime);

    /**
     * 查询所有支持的交易对
     */
    @Select("SELECT DISTINCT symbol FROM data_quality_stats ORDER BY symbol")
    List<String> findAllSymbols();

    /**
     * 查询所有支持的数据类型
     */
    @Select("SELECT DISTINCT data_type FROM data_quality_stats ORDER BY data_type")
    List<String> findAllDataTypes();

    /**
     * 删除指定时间之前的质量统计数据
     */
    @Delete("DELETE FROM data_quality_stats WHERE date_hour < #{beforeTime}")
    int deleteByTimestampBefore(@Param("beforeTime") Instant beforeTime);

    /**
     * 删除指定交易对的质量统计数据
     */
    @Delete("DELETE FROM data_quality_stats WHERE symbol = #{symbol}")
    int deleteBySymbol(@Param("symbol") String symbol);

    /**
     * 批量插入质量统计数据
     */
    @Insert("<script>" +
            "INSERT INTO data_quality_stats (date_hour, symbol, data_type, total_count, success_count, " +
            "error_count, avg_latency, max_latency, min_quality_score, avg_quality_score, " +
            "created_at, updated_at) VALUES " +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.dateHour}, #{item.symbol}, #{item.dataType}, #{item.totalCount}, #{item.successCount}, " +
            "#{item.errorCount}, #{item.avgLatency}, #{item.maxLatency}, " +
            "#{item.minQualityScore}, #{item.avgQualityScore}, #{item.createdAt}, #{item.updatedAt})" +
            "</foreach>" +
            "</script>")
    int batchInsert(@Param("list") List<DataQualityStats> statsList);

    /**
     * 批量更新质量统计数据
     */
    @Update("<script>" +
            "<foreach collection='list' item='item' separator=';'>" +
            "UPDATE data_quality_stats SET " +
            "total_count = #{item.totalCount}, success_count = #{item.successCount}, " +
            "error_count = #{item.errorCount}, avg_latency = #{item.avgLatency}, " +
            "max_latency = #{item.maxLatency}, min_quality_score = #{item.minQualityScore}, " +
            "avg_quality_score = #{item.avgQualityScore}, updated_at = NOW() WHERE id = #{item.id}" +
            "</foreach>" +
            "</script>")
    int batchUpdate(@Param("list") List<DataQualityStats> statsList);

    /**
     * 查询平均成功率
     */
    @Select("SELECT AVG((success_count * 1.0 / total_count)) FROM data_quality_stats WHERE symbol = #{symbol} AND data_type = #{dataType} " +
            "AND date_hour BETWEEN #{startTime} AND #{endTime}")
    Double getAverageSuccessRate(@Param("symbol") String symbol,
                                @Param("dataType") String dataType,
                                @Param("startTime") Instant startTime,
                                @Param("endTime") Instant endTime);

    /**
     * 查询平均延迟
     */
    @Select("SELECT AVG(avg_latency) FROM data_quality_stats WHERE symbol = #{symbol} AND data_type = #{dataType} " +
            "AND date_hour BETWEEN #{startTime} AND #{endTime}")
    Double getAverageLatency(@Param("symbol") String symbol,
                            @Param("dataType") String dataType,
                            @Param("startTime") Instant startTime,
                            @Param("endTime") Instant endTime);

    /**
     * 查询质量统计汇总信息
     */
    @Select("SELECT symbol, data_type, COUNT(1) as count, AVG(avg_quality_score) as avg_quality_score, " +
            "AVG(avg_latency) as avg_latency, " +
            "MIN(date_hour) as min_time, MAX(date_hour) as max_time " +
            "FROM data_quality_stats WHERE symbol = #{symbol} GROUP BY symbol, data_type")
    List<DataQualityStats> getQualityStatsSummary(@Param("symbol") String symbol);

    /**
     * 查询系统整体质量统计
     */
    @Select("SELECT data_type, COUNT(1) as count, AVG(avg_quality_score) as avg_quality_score, " +
            "AVG(avg_latency) as avg_latency " +
            "FROM data_quality_stats GROUP BY data_type ORDER BY data_type")
    List<DataQualityStats> getSystemQualityStats();
}
