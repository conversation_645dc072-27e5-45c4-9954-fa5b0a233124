package com.trading.market.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.trading.common.dto.TradeData;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.Instant;
import java.util.List;

/**
 * 市场数据Mapper接口
 * 使用MyBatis-Plus提供CRUD操作
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Mapper
public interface MarketDataMapper extends BaseMapper<TradeData> {

    /**
     * 根据交易对和数据类型查询市场数据
     * XML映射已在MarketDataMapper.xml中定义，移除注解避免重复映射
     */
    List<TradeData> findBySymbolAndDataType(@Param("symbol") String symbol,
                                           @Param("dataType") String dataType,
                                           @Param("limit") int limit);

    /**
     * 根据交易对、数据类型和时间范围查询市场数据
     * XML映射已在MarketDataMapper.xml中定义，移除注解避免重复映射
     */
    List<TradeData> findBySymbolAndDataTypeAndTimeRange(@Param("symbol") String symbol,
                                                        @Param("dataType") String dataType,
                                                        @Param("startTime") Instant startTime,
                                                        @Param("endTime") Instant endTime,
                                                        @Param("limit") int limit);

    /**
     * 查询最新的市场数据
     * XML映射已在MarketDataMapper.xml中定义，移除注解避免重复映射
     */
    TradeData findLatestBySymbolAndDataType(@Param("symbol") String symbol,
                                          @Param("dataType") String dataType);

    /**
     * 查询指定交易对的最晚时间
     * XML映射已在MarketDataMapper.xml中定义，移除注解避免重复映射
     */
    Instant findLatestTimeBySymbolAndDataType(@Param("symbol") String symbol,
                                             @Param("dataType") String dataType);

    /**
     * 批量插入市场数据
     * XML映射已在MarketDataMapper.xml中定义，移除注解避免重复映射
     */
    int batchInsert(@Param("list") List<TradeData> marketDataList);

    /**
     * 统计市场数据数量
     * XML映射已在MarketDataMapper.xml中定义，移除注解避免重复映射
     */
    long countBySymbolAndDataType(@Param("symbol") String symbol,
                                 @Param("dataType") String dataType);

    /**
     * 查询所有支持的交易对
     * XML映射已在MarketDataMapper.xml中定义，移除注解避免重复映射
     */
    List<String> findAllSymbols();
}