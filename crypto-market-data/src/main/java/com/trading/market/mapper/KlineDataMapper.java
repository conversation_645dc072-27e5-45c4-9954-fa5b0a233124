package com.trading.market.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trading.common.dto.KlineData;
import org.apache.ibatis.annotations.*;

import java.time.Instant;
import java.util.List;

/**
 * K线数据Mapper接口
 * 使用MyBatis-Plus提供CRUD操作
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Mapper
public interface KlineDataMapper extends BaseMapper<KlineData> {

    /**
     * 根据交易对和时间间隔查询K线数据
     * XML映射已在KlineDataMapper.xml中定义，移除注解避免重复映射
     */
    List<KlineData> findBySymbolAndInterval(@Param("symbol") String symbol,
                                           @Param("interval") String interval,
                                           @Param("limit") int limit);

    /**
     * 根据交易对、时间间隔和时间范围查询K线数据
     * XML映射已在KlineDataMapper.xml中定义，移除注解避免重复映射
     */
    List<KlineData> findBySymbolAndIntervalAndTimeRange(@Param("symbol") String symbol,
                                                        @Param("interval") String interval,
                                                        @Param("startTime") Instant startTime,
                                                        @Param("endTime") Instant endTime,
                                                        @Param("limit") int limit);

    /**
     * 根据交易对和时间间隔查询最新的K线数据
     */
    List<KlineData> findRecentKlineData(@Param("symbol") String symbol,
                                        @Param("interval") String interval,
                                        @Param("limit") int limit);

    /**
     * 分页查询K线数据
     */
    @Select("SELECT * FROM kline_data WHERE symbol = #{symbol} AND interval_type = #{interval} " +
            "ORDER BY open_time DESC")
    IPage<KlineData> findBySymbolAndIntervalWithPage(Page<KlineData> page,
                                                     @Param("symbol") String symbol,
                                                     @Param("interval") String interval);

    /**
     * 查询最新的K线数据
     * XML映射已在KlineDataMapper.xml中定义，移除注解避免重复映射
     */
    KlineData findLatestBySymbolAndInterval(@Param("symbol") String symbol,
                                           @Param("interval") String interval);

    /**
     * 查询最早的K线数据
     * XML映射已在KlineDataMapper.xml中定义，移除注解避免重复映射
     */
    KlineData findEarliestBySymbolAndInterval(@Param("symbol") String symbol,
                                             @Param("interval") String interval);

    /**
     * 查询指定交易对的最晚时间
     * XML映射已在KlineDataMapper.xml中定义，移除注解避免重复映射
     */
    Instant findLatestTimeBySymbolAndInterval(@Param("symbol") String symbol,
                                             @Param("interval") String interval);

    /**
     * 检查是否存在指定的K线数据
     * XML映射已在KlineDataMapper.xml中定义，移除注解避免重复映射
     */
    int existsBySymbolAndIntervalAndOpenTime(@Param("symbol") String symbol,
                                           @Param("interval") String interval,
                                           @Param("openTime") Instant openTime);

    /**
     * 查询数据质量分数低于阈值的K线数据
     * XML映射已在KlineDataMapper.xml中定义，移除注解避免重复映射
     */
    List<KlineData> findByQualityScoreLessThan(@Param("threshold") Double threshold,
                                              @Param("limit") int limit);

    /**
     * 批量插入K线数据
     * XML映射已在KlineDataMapper.xml中定义，移除注解避免重复映射
     */
    int batchInsert(@Param("list") List<KlineData> klineDataList);

    /**
     * 根据时间范围删除K线数据
     */
    @Delete("DELETE FROM kline_data WHERE symbol = #{symbol} AND interval_type = #{interval} " +
            "AND open_time >= #{startTime} AND open_time <= #{endTime}")
    int deleteByTimeRange(@Param("symbol") String symbol,
                         @Param("interval") String interval,
                         @Param("startTime") Instant startTime,
                         @Param("endTime") Instant endTime);

    /**
     * 统计K线数据数量
     * XML映射已在KlineDataMapper.xml中定义，移除注解避免重复映射
     */
    long countBySymbolAndInterval(@Param("symbol") String symbol,
                                 @Param("interval") String interval);

    /**
     * 查询指定时间范围内的K线数据数量
     */
    @Select("SELECT COUNT(1) FROM kline_data WHERE symbol = #{symbol} AND interval_type = #{interval} " +
            "AND open_time >= #{startTime} AND open_time <= #{endTime}")
    long countBySymbolAndIntervalAndTimeRange(@Param("symbol") String symbol,
                                             @Param("interval") String interval,
                                             @Param("startTime") Instant startTime,
                                             @Param("endTime") Instant endTime);

    /**
     * 更新K线数据质量分数
     */
    @Update("UPDATE kline_data SET quality_score = #{qualityScore}, updated_at = CURRENT_TIMESTAMP(3) " +
            "WHERE id = #{id}")
    int updateQualityScore(@Param("id") Long id, @Param("qualityScore") Double qualityScore);

    /**
     * 查询所有支持的交易对
     * XML映射已在KlineDataMapper.xml中定义，移除注解避免重复映射
     */
    List<String> findAllSymbols();

    /**
     * 查询所有支持的时间间隔
     * XML映射已在KlineDataMapper.xml中定义，移除注解避免重复映射
     */
    List<String> findAllIntervals();

    /**
     * 查询指定交易对的最早时间
     * XML映射已在KlineDataMapper.xml中定义
     */
    Instant findEarliestTimeBySymbolAndInterval(@Param("symbol") String symbol,
                                              @Param("interval") String interval);


}
