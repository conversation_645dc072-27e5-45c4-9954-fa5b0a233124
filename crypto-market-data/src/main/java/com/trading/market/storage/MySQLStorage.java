package com.trading.market.storage;

import com.trading.market.storage.exception.StorageException;
import com.trading.market.utils.StorageExceptionHandler;

import com.trading.market.mapper.KlineDataMapper;
import com.trading.market.mapper.MarketDataMapper;
import com.trading.market.mapper.DepthDataMapper;
import com.trading.market.mapper.DataQualityStatsMapper;
import com.trading.common.dto.KlineData;
import com.trading.common.dto.TradeData;
import com.trading.common.dto.DepthData;
import com.trading.common.dto.DataQualityStats;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * MySQL存储服务
 * 负责K线数据和市场数据的MySQL持久化操作
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
@org.springframework.boot.autoconfigure.condition.ConditionalOnProperty(name = "trading.market-data.storage.mysql.enabled", havingValue = "true", matchIfMissing = true)
public class MySQLStorage {
    
    private static final Logger log = LoggerFactory.getLogger(MySQLStorage.class);
    
    @Autowired
    private KlineDataMapper klineDataMapper;

    @Autowired
    private MarketDataMapper marketDataMapper;

    @Autowired
    private DepthDataMapper depthDataMapper;

    @Autowired
    private DataQualityStatsMapper dataQualityStatsMapper;
    
    // 统计计数器
    private final AtomicLong klineWriteCount = new AtomicLong(0);
    private final AtomicLong marketDataWriteCount = new AtomicLong(0);
    private final AtomicLong duplicateCount = new AtomicLong(0);
    private final AtomicLong depthDataWriteCount = new AtomicLong(0);
    private final AtomicLong qualityStatsWriteCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    
    /**
     * 保存K线数据到MySQL
     */
    @Transactional
    public void saveKlineData(KlineData klineData) {
        if (klineData == null) {
            log.warn("K线数据为空，跳过MySQL存储");
            return;
        }
        
        // 设置创建时间和更新时间
        if (klineData.getCreatedAt() == null) {
            klineData.setCreatedAt(LocalDateTime.now());
        }
        klineData.setUpdatedAt(LocalDateTime.now());

        // 保存K线数据 - 使用统一异常处理
        StorageExceptionHandler.executeWithExceptionHandling(
            () -> {
                int result = klineDataMapper.insert(klineData);
                if (result > 0) {
                    long currentCount = klineWriteCount.incrementAndGet();
                    log.info("K线数据已保存到MySQL: symbol={}, interval={}, openTime={}, 总保存数={}",
                            klineData.getSymbol(), klineData.getInterval(), klineData.getOpenTime(), currentCount);
                } else {
                    log.warn("K线数据保存失败，影响行数为0: symbol={}, interval={}, openTime={}",
                            klineData.getSymbol(), klineData.getInterval(), klineData.getOpenTime());
                }
            },
            "保存K线数据",
            String.format("symbol=%s, interval=%s, openTime=%s",
                klineData.getSymbol(), klineData.getInterval(), klineData.getOpenTime()),
            log,
            errorCount,
            duplicateCount
        );
    }
    
    /**
     * 保存市场数据到MySQL
     */
    @Transactional
    public void saveMarketData(TradeData tradeData) {
        if (tradeData == null) {
            log.warn("市场数据为空，跳过MySQL存储");
            return;
        }

        // 设置创建时间和更新时间
        if (tradeData.getCreatedAt() == null) {
            tradeData.setCreatedAt(LocalDateTime.now());
        }

        // 确保 dataType 字段有值
        if (tradeData.getDataType() == null || tradeData.getDataType().isEmpty()) {
            tradeData.setDataType("trade");
        }

        // 确保 timestamp 字段有值
        if (tradeData.getTimestamp() == null) {
            tradeData.setTimestamp(tradeData.getTradeTime() != null ? tradeData.getTradeTime() : LocalDateTime.now());
        }

        // 保存市场数据 - 使用统一异常处理
        StorageExceptionHandler.executeWithExceptionHandling(
            () -> {
                int result = marketDataMapper.insert(tradeData);
                if (result > 0) {
                    long currentCount = marketDataWriteCount.incrementAndGet();
                    log.debug("市场数据已保存到MySQL: symbol={}, tradeId={}, timestamp={}, 总保存数={}",
                            tradeData.getSymbol(), tradeData.getTradeId(), tradeData.getTradeTime(), currentCount);
                } else {
                    log.warn("市场数据保存失败，影响行数为0: symbol={}, tradeId={}, timestamp={}",
                            tradeData.getSymbol(), tradeData.getTradeId(), tradeData.getTradeTime());
                }
            },
            "保存市场数据",
            String.format("symbol=%s, tradeId=%s, timestamp=%s",
                tradeData.getSymbol(), tradeData.getTradeId(), tradeData.getTradeTime()),
            log,
            errorCount,
            duplicateCount
        );
    }

    /**
     * 异步保存深度数据到MySQL
     * @param depthData 深度数据
     */
    public void saveDepthDataAsync(DepthData depthData) {
        if (depthData == null) {
            log.warn("深度数据为空，跳过MySQL存储");
            return;
        }
        // In a real-world scenario, you might add this to a queue for batch processing
        // For now, we'll call the batch method with a single item
        saveDepthDataBatch(java.util.Collections.singletonList(depthData));
    }

    /**
     * 保存数据质量统计到MySQL
     */
    @Transactional
    public void saveDataQualityStats(DataQualityStats qualityStats) {
        if (qualityStats == null) {
            log.warn("数据质量统计为空，跳过MySQL存储");
            return;
        }

        try {
            // 检查是否已存在相同的质量统计数据
            int exists = dataQualityStatsMapper.existsBySymbolAndDataTypeAndTimestamp(
                    qualityStats.getSymbol(), qualityStats.getDataType(),
                    qualityStats.getDateHour() != null ?
                        qualityStats.getDateHour().atZone(java.time.ZoneOffset.UTC).toInstant() :
                        Instant.now());

            if (exists > 0) {
                log.debug("数据质量统计已存在，跳过保存: symbol={}, dataType={}, dateHour={}",
                        qualityStats.getSymbol(), qualityStats.getDataType(), qualityStats.getDateHour());
                return;
            }

            // 设置创建时间和更新时间
            if (qualityStats.getCreatedAt() == null) {
                qualityStats.setCreatedAt(Instant.now());
            }
            qualityStats.setUpdatedAt(Instant.now());

            // 保存质量统计数据
            int result = dataQualityStatsMapper.insert(qualityStats);
            if (result > 0) {
                long currentCount = qualityStatsWriteCount.incrementAndGet();
                log.info("数据质量统计已保存到MySQL: id={}, symbol={}, dataType={}, dateHour={}, 总保存数={}",
                        qualityStats.getId(), qualityStats.getSymbol(), qualityStats.getDataType(),
                        qualityStats.getDateHour(), currentCount);
            } else {
                log.warn("数据质量统计保存失败，影响行数为0: symbol={}, dataType={}, dateHour={}",
                        qualityStats.getSymbol(), qualityStats.getDataType(), qualityStats.getDateHour());
            }

        } catch (org.springframework.dao.DuplicateKeyException e) {
            // 重复键异常，数据已存在，记录调试信息但不抛出异常
            duplicateCount.incrementAndGet();
            log.debug("数据质量统计已存在，跳过保存: symbol={}, dataType={}, dateHour={}",
                    qualityStats.getSymbol(), qualityStats.getDataType(), qualityStats.getDateHour());
            // 不增加错误计数，因为这不是真正的错误
        } catch (DataAccessException e) {
            errorCount.incrementAndGet();
            log.error("保存数据质量统计到MySQL失败: symbol={}, dataType={}, dateHour={}",
                    qualityStats.getSymbol(), qualityStats.getDataType(), qualityStats.getDateHour(), e);
            throw new RuntimeException("MySQL数据质量统计保存失败", e);
        } catch (Exception e) {
            errorCount.incrementAndGet();
            log.error("保存数据质量统计到MySQL时发生未知错误: {}", qualityStats, e);
            throw new RuntimeException("MySQL数据质量统计保存失败", e);
        }
    }

    /**
     * 批量保存K线数据
     */
    @Transactional
    public void saveKlineDataBatch(List<KlineData> klineDataList) {
        if (klineDataList == null || klineDataList.isEmpty()) {
            log.warn("K线数据列表为空，跳过批量MySQL存储");
            return;
        }
        
        // Optimized timestamp setting - pre-compute timestamp once
        LocalDateTime now = LocalDateTime.now();
        klineDataList.parallelStream().forEach(klineData -> {
            if (klineData.getCreatedAt() == null) {
                klineData.setCreatedAt(now);
            }
            klineData.setUpdatedAt(now);
        });

        // 批量保存K线数据 - 使用统一异常处理
        StorageExceptionHandler.executeBatchWithExceptionHandling(
            () -> {
                int result = klineDataMapper.batchInsert(klineDataList);
                long currentCount = klineWriteCount.addAndGet(result);
                log.info("批量K线数据已保存到MySQL: 保存数量={}, 总保存数={}", result, currentCount);
                return result;
            },
            "保存K线数据",
            klineDataList.size(),
            log,
            errorCount
        );
    }

    /**
     * 批量保存深度数据
     */
    @Transactional
    public void saveDepthDataBatch(List<DepthData> depthDataList) {
        if (depthDataList == null || depthDataList.isEmpty()) {
            log.warn("深度数据列表为空，跳过批量MySQL存储");
            return;
        }

        try {
            // 设置创建时间和更新时间
            LocalDateTime now = LocalDateTime.now();
            for (DepthData depthData : depthDataList) {
                if (depthData.getCreatedAt() == null) {
                    depthData.setCreatedAt(now);
                }
                depthData.updateTimestamp();
            }

            // 批量插入或更新
            int result = depthDataMapper.upsertBatch(depthDataList);
            long currentCount = depthDataWriteCount.addAndGet(result);

            log.info("批量深度数据已保存到MySQL: 保存数量={}, 总保存数={}", result, currentCount);

        } catch (DataAccessException e) {
            errorCount.incrementAndGet();
            log.error("批量保存深度数据到MySQL失败: 数据量={}", depthDataList.size(), e);
            throw new RuntimeException("MySQL批量深度数据保存失败", e);
        } catch (Exception e) {
            errorCount.incrementAndGet();
            log.error("批量保存深度数据到MySQL时发生未知错误: 数据量={}", depthDataList.size(), e);
            throw new RuntimeException("MySQL批量深度数据保存失败", e);
        }
    }
     
    /**
     * 批量保存市场数据
     */
    @Transactional
    public void saveMarketDataBatch(List<TradeData> marketDataList) {
        if (marketDataList == null || marketDataList.isEmpty()) {
            log.warn("市场数据列表为空，跳过批量MySQL存储");
            return;
        }

        try {
            LocalDateTime now = LocalDateTime.now();
            for (TradeData tradeData : marketDataList) {
                if (tradeData.getCreatedAt() == null) {
                    tradeData.setCreatedAt(now);
                }
                // 确保 dataType 字段有值
                if (tradeData.getDataType() == null || tradeData.getDataType().isEmpty()) {
                    tradeData.setDataType("trade");
                }

                // 确保 timestamp 字段有值
                if (tradeData.getTimestamp() == null) {
                    tradeData.setTimestamp(tradeData.getTradeTime() != null ? tradeData.getTradeTime() : now);
                }
            }

            // 批量插入
            int result = marketDataMapper.batchInsert(marketDataList);
            long currentCount = marketDataWriteCount.addAndGet(result);

            log.info("批量市场数据已保存到MySQL: 保存数量={}, 总保存数={}", result, currentCount);

        } catch (DataAccessException e) {
            errorCount.incrementAndGet();
            log.error("批量保存市场数据到MySQL失败: 数据量={}", marketDataList.size(), e);
            throw new StorageException("MySQL批量市场数据保存失败", e);
        } catch (Exception e) {
            errorCount.incrementAndGet();
            log.error("批量保存市场数据到MySQL时发生未知错误: 数据量={}", marketDataList.size(), e);
            throw new StorageException("MySQL批量市场数据保存失败", e);
        }
    }

    /**
     * 查询K线数据
     */
    public List<KlineData> getKlineData(String symbol, String interval, int limit) {
        try {
            // 直接调用新的高效查询
            return klineDataMapper.findRecentKlineData(symbol, interval, limit);
        } catch (Exception e) {
            log.error("查询K线数据失败: symbol={}, interval={}, limit={}",
                    symbol, interval, limit, e);
            throw new RuntimeException("MySQL K线数据查询失败", e);
        }
    }
    
    /**
     * 查询市场数据
     */
    public List<TradeData> getMarketData(String symbol, String dataType, Instant startTime, Instant endTime) {
        try {
            return marketDataMapper.findBySymbolAndDataTypeAndTimeRange(symbol, dataType, startTime, endTime, 1000);
        } catch (Exception e) {
            log.error("查询市场数据失败: symbol={}, dataType={}, startTime={}, endTime={}",
                    symbol, dataType, startTime, endTime, e);
            throw new RuntimeException("MySQL市场数据查询失败", e);
        }
    }

    /**
     * 查询深度数据
     */
    public List<DepthData> getDepthData(String symbol, Integer levels, Instant startTime, Instant endTime) {
        try {
            return depthDataMapper.findBySymbolAndLevelsAndTimeRange(symbol, levels, startTime, endTime, 1000);
        } catch (Exception e) {
            log.error("查询深度数据失败: symbol={}, levels={}, startTime={}, endTime={}",
                    symbol, levels, startTime, endTime, e);
            throw new RuntimeException("MySQL深度数据查询失败", e);
        }
    }

    /**
     * 查询数据质量统计
     */
    public List<DataQualityStats> getDataQualityStats(String symbol, String dataType, Instant startTime, Instant endTime) {
        try {
            return dataQualityStatsMapper.findBySymbolAndDataTypeAndTimeRange(symbol, dataType, startTime, endTime, 1000);
        } catch (Exception e) {
            log.error("查询数据质量统计失败: symbol={}, dataType={}, startTime={}, endTime={}",
                    symbol, dataType, startTime, endTime, e);
            throw new RuntimeException("MySQL数据质量统计查询失败", e);
        }
    }

    /**
     * 获取重复数据计数
     */
    public long getDuplicateCount() {
        return duplicateCount.get();
    }

    /**
     * 获取统计信息
     */
    public StorageStats getStats() {
        return StorageStats.builder()
                .klineWriteCount(klineWriteCount.get())
                .marketDataWriteCount(marketDataWriteCount.get())
                .depthDataWriteCount(depthDataWriteCount.get())
                .qualityStatsWriteCount(qualityStatsWriteCount.get())
                .errorCount(errorCount.get())
                .duplicateCount(duplicateCount.get())
                .totalWriteCount(klineWriteCount.get() + marketDataWriteCount.get() +
                               depthDataWriteCount.get() + qualityStatsWriteCount.get())
                .build();
    }
    
    /**
     * 检查连接状态
     */
    public boolean isHealthy() {
        try {
            // 简单查询测试连接
            klineDataMapper.selectCount(null);
            marketDataMapper.selectCount(null);
            depthDataMapper.selectCount(null);
            dataQualityStatsMapper.selectCount(null);
            return true;
        } catch (Exception e) {
            log.error("MySQL连接检查失败", e);
            return false;
        }
    }
    
    @lombok.Data
    @lombok.Builder
    public static class StorageStats {
        private long klineWriteCount;
        private long marketDataWriteCount;
        private long depthDataWriteCount;
        private long qualityStatsWriteCount;
        private long errorCount;
        private long duplicateCount;
        private long totalWriteCount;
    }
}
