package com.trading.market.storage;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trading.common.config.MarketDataConfig;
import com.trading.common.dto.DepthData;
import com.trading.common.dto.KlineData;
import com.trading.common.dto.TradeData;
import com.trading.market.service.RedisConnectionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.ZoneOffset;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Redis存储服务
 * 负责将市场数据缓存到Redis，提供快速访问
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
@ConditionalOnProperty(name = "trading.market-data.storage.redis.enabled", havingValue = "true", matchIfMissing = true)
public class RedisStorage {

    private static final Logger log = LoggerFactory.getLogger(RedisStorage.class);
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ObjectMapper objectMapper;

    @Autowired
    private MarketDataConfig marketDataConfig;

    @Autowired
    private RedisConnectionService redisConnectionService;
    
    // 统计信息
    private final AtomicLong cacheCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    
    /**
     * 缓存K线数据
     */
    public void cacheKlineData(KlineData klineData) {
        redisConnectionService.safeExecute(() -> {
            try {
                String key = buildKlineKey(klineData);
                String value = objectMapper.writeValueAsString(klineData);
                long ttl = marketDataConfig.getStorage().getRedis().getCacheTtl();

                redisTemplate.opsForValue().set(key, value, ttl, TimeUnit.SECONDS);
                cacheCount.incrementAndGet();

                log.debug("K线数据已缓存到Redis: key={}", key);

                // 同时缓存最新价格
                cacheLatestPrice(klineData.getSymbol(), klineData.getClosePrice().doubleValue());

            } catch (Exception e) {
                errorCount.incrementAndGet();
                log.error("缓存K线数据到Redis失败: {}", klineData, e);
                throw e; // 重新抛出异常以便safeExecute处理
            }
        });
    }
    
    /**
     * 缓存深度数据
     */
    public void cacheDepthData(DepthData depthData) {
        // 预先检查连接状态，避免无效操作
        if (!redisConnectionService.isConnectionHealthy()) {
            log.debug("Redis连接不健康，跳过缓存: symbol={}",
                depthData.getSymbol());
            return;
        }

        redisConnectionService.safeExecute(() -> {
            String key = buildDepthDataKey(depthData);
            String value = objectMapper.writeValueAsString(depthData);
            long ttl = marketDataConfig.getStorage().getRedis().getCacheTtl();

            redisTemplate.opsForValue().set(key, value, ttl, TimeUnit.SECONDS);
            cacheCount.incrementAndGet();

            log.debug("深度数据已缓存到Redis: key={}", key);
        });
    }

    /**
     * 缓存市场数据 - 增强错误处理，减少连接使用
     */
    public void cacheMarketData(TradeData tradeData) {
        // 预先检查连接状态，避免无效操作
        if (!redisConnectionService.isConnectionHealthy()) {
            log.debug("Redis连接不健康，跳过缓存: symbol={}, tradeId={}",
                tradeData.getSymbol(), tradeData.getTradeId());
            return;
        }

        redisConnectionService.safeExecute(() -> {
            String key = buildMarketDataKey(tradeData);
            String value = objectMapper.writeValueAsString(tradeData);
            long ttl = marketDataConfig.getStorage().getRedis().getCacheTtl();

            redisTemplate.opsForValue().set(key, value, ttl, TimeUnit.SECONDS);
            cacheCount.incrementAndGet();

            log.debug("市场数据已缓存到Redis: key={}", key);

            // 如果有价格信息，缓存最新价格
            if (tradeData.getPrice() != null) {
                cacheLatestPrice(tradeData.getSymbol(), tradeData.getPrice().doubleValue());
            }
        });
    }
    
    /**
     * 缓存最新价格 - 优化连接管理，减少连接泄漏
     */
    public void cacheLatestPrice(String symbol, double price) {
        // 预先检查连接状态，避免无效操作
        if (!redisConnectionService.isConnectionHealthy()) {
            log.debug("Redis连接不健康，跳过价格缓存: symbol={}, price={}", symbol, price);
            return;
        }

        redisConnectionService.safeExecute(() -> {
            try {
                String key = buildLatestPriceKey(symbol);
                redisTemplate.opsForValue().set(key, String.valueOf(price),
                        marketDataConfig.getStorage().getRedis().getCacheTtl(), TimeUnit.SECONDS);

                log.debug("最新价格已缓存: symbol={}, price={}", symbol, price);

            } catch (Exception e) {
                log.warn("缓存最新价格失败: symbol={}, price={}", symbol, price, e);
                // 不重新抛出异常，避免影响主流程
            }
        });
    }
    
    /**
     * 获取最新价格
     */
    public Double getLatestPrice(String symbol) {
        try {
            String key = buildLatestPriceKey(symbol);
            String value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                return Double.parseDouble(value);
            }
            
        } catch (Exception e) {
            log.error("获取最新价格失败: symbol={}", symbol, e);
        }
        
        return null;
    }
    
    /**
     * 获取K线数据
     */
    public KlineData getKlineData(String symbol, String interval, long timestamp) {
        try {
            String key = buildKlineKey(symbol, interval, timestamp);
            String value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                return objectMapper.readValue(value, KlineData.class);
            }
            
        } catch (Exception e) {
            log.error("获取K线数据失败: symbol={}, interval={}, timestamp={}", symbol, interval, timestamp, e);
        }
        
        return null;
    }
    
    /**
     * 获取市场数据
     */
    public TradeData getMarketData(String symbol, String dataType, long timestamp) {
        try {
            String key = buildMarketDataKey(symbol, dataType, timestamp);
            String value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                return objectMapper.readValue(value, TradeData.class);
            }
            
        } catch (Exception e) {
            log.error("获取市场数据失败: symbol={}, dataType={}, timestamp={}", symbol, dataType, timestamp, e);
        }
        
        return null;
    }
    
    /**
     * 缓存交易对列表
     */
    public void cacheSymbolList(java.util.List<String> symbols) {
        try {
            String key = buildSymbolListKey();
            String value = objectMapper.writeValueAsString(symbols);
            
            redisTemplate.opsForValue().set(key, value, 3600, TimeUnit.SECONDS); // 1小时TTL
            log.debug("交易对列表已缓存: count={}", symbols.size());
            
        } catch (Exception e) {
            log.error("缓存交易对列表失败", e);
        }
    }
    
    /**
     * 获取交易对列表
     */
    @SuppressWarnings("unchecked")
    public java.util.List<String> getSymbolList() {
        try {
            String key = buildSymbolListKey();
            String value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                return objectMapper.readValue(value, java.util.List.class);
            }
            
        } catch (Exception e) {
            log.error("获取交易对列表失败", e);
        }
        
        return null;
    }
    
    /**
     * 缓存市场统计信息
     */
    public void cacheMarketStats(String symbol, java.util.Map<String, Object> stats) {
        try {
            String key = buildMarketStatsKey(symbol);
            String value = objectMapper.writeValueAsString(stats);
            
            redisTemplate.opsForValue().set(key, value, 300, TimeUnit.SECONDS); // 5分钟TTL
            log.debug("市场统计信息已缓存: symbol={}", symbol);
            
        } catch (Exception e) {
            log.error("缓存市场统计信息失败: symbol={}", symbol, e);
        }
    }
    
    /**
     * 获取市场统计信息
     */
    @SuppressWarnings("unchecked")
    public java.util.Map<String, Object> getMarketStats(String symbol) {
        try {
            String key = buildMarketStatsKey(symbol);
            String value = redisTemplate.opsForValue().get(key);
            
            if (value != null) {
                return objectMapper.readValue(value, java.util.Map.class);
            }
            
        } catch (Exception e) {
            log.error("获取市场统计信息失败: symbol={}", symbol, e);
        }
        
        return null;
    }
    
    /**
     * 删除缓存
     */
    public void deleteCache(String key) {
        try {
            redisTemplate.delete(key);
            log.debug("缓存已删除: key={}", key);
        } catch (Exception e) {
            log.error("删除缓存失败: key={}", key, e);
        }
    }
    
    /**
     * 检查键是否存在
     */
    public boolean exists(String key) {
        try {
            return Boolean.TRUE.equals(redisTemplate.hasKey(key));
        } catch (Exception e) {
            log.error("检查键存在性失败: key={}", key, e);
            return false;
        }
    }
    
    /**
     * 设置键的过期时间
     */
    public void expire(String key, long timeout, TimeUnit unit) {
        try {
            redisTemplate.expire(key, timeout, unit);
        } catch (Exception e) {
            log.error("设置键过期时间失败: key={}", key, e);
        }
    }
    
    // ==================== 键构建方法 ====================
    
    private String buildKlineKey(KlineData klineData) {
        return buildKlineKey(klineData.getSymbol(), klineData.getInterval(),
                klineData.getOpenTime().toInstant(ZoneOffset.UTC).toEpochMilli());
    }
    
    private String buildKlineKey(String symbol, String interval, long timestamp) {
        String prefix = marketDataConfig.getStorage().getRedis().getKeyPrefix();
        return String.format("%skline:%s:%s:%d", prefix, symbol, interval, timestamp);
    }
    
    private String buildMarketDataKey(TradeData tradeData) {
        return buildMarketDataKey(tradeData.getSymbol(), "trade",
                tradeData.getTradeTime().toInstant(ZoneOffset.UTC).toEpochMilli());
    }
    
    private String buildMarketDataKey(String symbol, String dataType, long timestamp) {
        String prefix = marketDataConfig.getStorage().getRedis().getKeyPrefix();
        return String.format("%smarket:%s:%s:%d", prefix, symbol, dataType, timestamp);
    }

    private String buildDepthDataKey(DepthData depthData) {
        return buildDepthDataKey(depthData.getSymbol(), depthData.getBids().size(),
                depthData.getTimestamp().toInstant(ZoneOffset.UTC).toEpochMilli());
    }

    private String buildDepthDataKey(String symbol, Integer levels, long timestamp) {
        String prefix = marketDataConfig.getStorage().getRedis().getKeyPrefix();
        return String.format("%sdepth:%s:%d:%d", prefix, symbol, levels, timestamp);
    }

    private String buildLatestPriceKey(String symbol) {
        String prefix = marketDataConfig.getStorage().getRedis().getKeyPrefix();
        return String.format("%slatest_price:%s", prefix, symbol);
    }
    
    private String buildSymbolListKey() {
        String prefix = marketDataConfig.getStorage().getRedis().getKeyPrefix();
        return String.format("%ssymbol_list", prefix);
    }
    
    private String buildMarketStatsKey(String symbol) {
        String prefix = marketDataConfig.getStorage().getRedis().getKeyPrefix();
        return String.format("%smarket_stats:%s", prefix, symbol);
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStatistics getStatistics() {
        return CacheStatistics.builder()
                .cacheCount(cacheCount.get())
                .errorCount(errorCount.get())
                .successRate(calculateSuccessRate())
                .build();
    }
    
    /**
     * 计算成功率
     */
    private double calculateSuccessRate() {
        long total = cacheCount.get() + errorCount.get();
        if (total == 0) {
            return 100.0;
        }
        return ((double) cacheCount.get() / total) * 100.0;
    }
    
    /**
     * 重置统计信息
     */
    public void resetStatistics() {
        cacheCount.set(0);
        errorCount.set(0);
        log.info("Redis缓存统计信息已重置");
    }

    /**
     * 检查Redis连接是否有效
     */
    private boolean isRedisConnectionValid() {
        try {
            // 检查连接工厂状态
            if (redisTemplate.getConnectionFactory() == null) {
                return false;
            }

            // 尝试获取连接并执行ping
            redisTemplate.getConnectionFactory().getConnection().ping();
            return true;

        } catch (Exception e) {
            log.debug("Redis连接检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查Redis连接健康状态
     */
    public boolean isHealthy() {
        try {
            // 执行ping命令测试连接
            String result = redisTemplate.getConnectionFactory().getConnection().ping();
            return "PONG".equals(result);
        } catch (Exception e) {
            log.error("Redis健康检查失败", e);
            return false;
        }
    }
    
    /**
     * Inner class for holding cache statistics.
     */
    @lombok.Data
    @lombok.Builder
    public static class CacheStatistics {
        private long cacheCount;
        private long errorCount;
        private double successRate;
    }
}
