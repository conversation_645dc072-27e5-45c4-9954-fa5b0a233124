package com.trading.market.storage;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.WriteApi;
import com.influxdb.client.domain.Bucket;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.trading.common.config.MarketDataConfig;
import com.trading.common.dto.KlineData;
import com.trading.common.dto.TradeData;
import com.trading.common.dto.DepthData;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import lombok.extern.slf4j.Slf4j;

/**
 * InfluxDB 存储服务，负责将市场数据持久化到 InfluxDB 时序数据库。
 * <p>
 * 该服务是数据处理流水线中的一个关键下游组件。它被设计为仅在配置文件中
 * `influxdb.enabled` 属性为 `true` 时激活。主要职责包括：
 * <ul>
 *     <li>在初始化时 ({@code @PostConstruct}) 检查并（如果需要）创建所需的 InfluxDB Bucket。</li>
 *     <li>提供将不同类型市场数据 DTOs ({@link KlineData}, {@link TradeData}, {@link DepthData})
 *         转换为 InfluxDB 的 {@link Point} 对象的逻辑。</li>
 *     <li>支持单点写入和高性能的批量写入。</li>
 *     <li>提供一个简单的健康检查方法 ({@link #isHealthy()}) 用于监控连接状态。</li>
 *     <li>处理模拟 (Mock) 模式，在测试环境中可以安全地禁用实际的数据库写入操作。</li>
 * </ul>
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
@Slf4j
@ConditionalOnProperty(prefix = "trading.market-data.storage.influxdb", name = "enabled", havingValue = "true")
public class InfluxDBStorage {

    
    @Autowired(required = false)
    private InfluxDBClient influxDBClient;
    
    @Autowired(required = false)
    private WriteApi writeApi;
    
    @Autowired
    private MarketDataConfig marketDataConfig;
    
    // Statistics
    private final AtomicLong writeCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);

    // Regex to parse depth JSON entries
    private static final Pattern DEPTH_ENTRY_PATTERN = Pattern.compile("\\{\"price\":\"(.*?)\",\"quantity\":\"(.*?)\"\\}");

    @PostConstruct
    public void initialize() {
        log.info("Initializing InfluxDBStorage...");
        if (isMockMode()) {
            log.warn("InfluxDBStorage is running in mock mode. No data will be written to InfluxDB.");
            return;
        }

        try {
            String bucketName = marketDataConfig.getStorage().getInfluxdb().getBucket();
            String orgName = marketDataConfig.getStorage().getInfluxdb().getOrg();

            // Check if bucket exists
            Bucket bucket = influxDBClient.getBucketsApi().findBucketByName(bucketName);

            if (bucket == null) {
                log.info("Bucket '{}' not found. Creating it for organization '{}'...", bucketName, orgName);
                influxDBClient.getBucketsApi().createBucket(bucketName, orgName);
                log.info("Bucket '{}' created successfully.", bucketName);
            } else {
                log.info("Bucket '{}' already exists.", bucketName);
            }

            // Final readiness check to ensure the client is operational
            influxDBClient.ready();
            log.info("InfluxDB connection successful. Storage is ready.");

        } catch (Exception e) {
            log.error("Failed to initialize InfluxDBStorage. Could not verify or create bucket. Please check connection and permissions. Error: {}", e.getMessage());
        }
        log.info("InfluxDBStorage initialization complete.");
    }
    
    private boolean isMockClient(InfluxDBClient client) {
        if (client == null) return false;
        try {
            return "mock-version".equals(client.version());
        } catch (Exception e) {
            return true;
        }
    }
    /**
     * 异步保存单条 K-line 数据。
     * <p>
     * 此方法将一个 {@link KlineData} 对象转换为 InfluxDB 的 {@link Point}，
     * 然后通过 {@link WriteApi} 将其写入数据库。
     * 写入操作是异步的，由 InfluxDB Java 客户端内部的批处理机制管理。
     * 如果处于模拟模式，此操作将被跳过。
     * </p>
     *
     * @param klineData 要保存的K线数据对象。
     * @throws com.trading.market.storage.exception.StorageException 如果写入过程中发生错误。
     */
    public void saveKlineData(KlineData klineData) {
        if (isMockMode()) {
            log.debug("Skipping K-line data write to InfluxDB (mock mode): symbol={}, interval={}", klineData.getSymbol(), klineData.getInterval());
            return;
        }
        try {
            Point point = createKlinePoint(klineData);
            writeApi.writePoint(point);
            writeCount.incrementAndGet();
        } catch (Exception e) {
            handleWriteError("K-line data", klineData.getSymbol(), e);
        }
    }
    /**
     * 将 {@link WriteApi} 内部的缓冲区数据立即刷入 InfluxDB。
     * <p>
     * {@link WriteApi} 会在内部缓存数据点，直到达到批处理大小或刷新间隔。
     * 在某些场景下（例如应用关闭前），需要调用此方法来确保所有缓存的数据都被持久化。
     * </p>
     */
    public void flush() {
        if (isMockMode() || writeApi == null) return;
        try {
            log.info("Flushing InfluxDB write buffer...");
            writeApi.flush();
            log.info("InfluxDB write buffer flushed successfully.");
        } catch (Exception e) {
            log.error("Failed to flush InfluxDB write buffer", e);
        }
    }
    /**
     * 异步保存单条交易数据 (TradeData)。
     * <p>
     * 将一个 {@link TradeData} 对象转换为 InfluxDB 的 {@link Point}，并进行异步写入。
     * </p>
     *
     * @param tradeData 要保存的交易数据对象。
     * @throws com.trading.market.storage.exception.StorageException 如果写入过程中发生错误。
     */
    public void saveMarketData(TradeData tradeData) {
        if (isMockMode()) {
            log.debug("Skipping market data write to InfluxDB (mock mode): symbol={}, tradeId={}", tradeData.getSymbol(), tradeData.getTradeId());
            return;
        }
        try {
            Point point = createMarketDataPoint(tradeData);
            writeApi.writePoint(point);
            writeCount.incrementAndGet();
        } catch (Exception e) {
            handleWriteError("Market data", tradeData.getSymbol(), e);
        }
    }

    private Point createKlinePoint(KlineData klineData) {
        Point point = Point.measurement("kline_data")
                .time(klineData.getOpenTime().toInstant(ZoneOffset.UTC), WritePrecision.MS)
                .addTag("symbol", klineData.getSymbol())
                .addTag("interval", klineData.getInterval())
                .addTag("source", "binance")
                .addField("open_price", klineData.getOpenPrice().doubleValue())
                .addField("high_price", klineData.getHighPrice().doubleValue())
                .addField("low_price", klineData.getLowPrice().doubleValue())
                .addField("close_price", klineData.getClosePrice().doubleValue())
                .addField("volume", klineData.getVolume().doubleValue())
                .addField("quote_volume", klineData.getQuoteVolume().doubleValue())
                .addField("trade_count", klineData.getTradeCount())
                .addField("taker_buy_volume", klineData.getTakerBuyVolume().doubleValue())
                .addField("taker_buy_quote_volume", klineData.getTakerBuyQuoteVolume().doubleValue());

        if (klineData.getCloseTime() != null) {
            point.addField("close_time", klineData.getCloseTime().toInstant(ZoneOffset.UTC).toEpochMilli());
        }
        return point;
    }

    private Point createMarketDataPoint(TradeData tradeData) {
         Point point = Point.measurement("market_data")
                .time(tradeData.getTradeTime().toInstant(ZoneOffset.UTC), WritePrecision.MS)
                .addTag("symbol", tradeData.getSymbol())
                .addTag("data_type", "trade")
                .addTag("source", "binance")
                .addField("price", tradeData.getPrice().doubleValue());

        if (tradeData.getQuantity() != null) {
            point.addField("quantity", tradeData.getQuantity().doubleValue());
        }
        return point;
    }
    
    public void saveKlineDataBatch(Iterable<KlineData> klineDataList) {
        if (isMockMode()) return;
        try {
            java.util.List<Point> points = new java.util.ArrayList<>();
            klineDataList.forEach(kline -> points.add(createKlinePoint(kline)));
            if (!points.isEmpty()) {
                writeApi.writePoints(points);
                writeCount.addAndGet(points.size());
            }
        } catch (Exception e) {
            handleWriteError("K-line batch", "multiple", e);
            throw new RuntimeException("InfluxDB K-line batch write failed", e);
        }
    }
    
    public void saveDepthData(DepthData depthData) {
        if (isMockMode()) {
            log.debug("Skipping depth data write to InfluxDB (mock mode): symbol={}", depthData.getSymbol());
            return;
        }
        try {
            Point point = createDepthPoint(depthData);
            writeApi.writePoint(point);
            writeCount.incrementAndGet();
        } catch (Exception e) {
            handleWriteError("Depth data", depthData.getSymbol(), e);
        }
    }
    
    private Point createDepthPoint(DepthData depthData) {
        Point point = Point.measurement("depth_data")
                .time(depthData.getTimestamp().toInstant(ZoneOffset.UTC), WritePrecision.MS)
                .addTag("symbol", depthData.getSymbol())
                .addTag("source", "binance");

        point.addField("levels", depthData.getBids().size());
        if (depthData.getLastUpdateId() != null) {
            point.addField("last_update_id", depthData.getLastUpdateId());
        }

        // Add flattened bids and asks
        addFlattenedDepthFields(point, depthData.getBids(), "bid", 5);
        addFlattenedDepthFields(point, depthData.getAsks(), "ask", 5);

        return point;
    }

    private void addFlattenedDepthFields(Point point, List<DepthData.PriceLevel> levels, String prefix, int maxLevels) {
        if (levels == null) {
            return;
        }
        for (int i = 0; i < Math.min(levels.size(), maxLevels); i++) {
            DepthData.PriceLevel level = levels.get(i);
            point.addField(prefix + "_price_" + (i + 1), level.getPrice());
            point.addField(prefix + "_qty_" + (i + 1), level.getQuantity());
        }
    }
    
    public boolean isHealthy() {
        if (isMockMode()) return false;
        try {
            return influxDBClient.ready() != null;
        } catch (Exception e) {
            log.warn("InfluxDB health check failed", e);
            return false;
        }
    }

    private boolean isMockMode() {
        return influxDBClient == null || isMockClient(influxDBClient) || writeApi == null;
    }

    private void handleWriteError(String dataType, String symbol, Exception e) {
        errorCount.incrementAndGet();
        log.error("Failed to save {} to InfluxDB for symbol {}: {}", dataType, symbol, e.getMessage());
        throw new com.trading.market.storage.exception.StorageException("Failed to save " + dataType + " to InfluxDB for symbol " + symbol, e);
    }

    public void saveMarketDataBatch(java.util.List<TradeData> marketDataList) {
        if (isMockMode()) return;
        try {
            java.util.List<Point> points = new java.util.ArrayList<>();
            marketDataList.forEach(data -> points.add(createMarketDataPoint(data)));
            if (!points.isEmpty()) {
                writeApi.writePoints(points);
                writeCount.addAndGet(points.size());
            }
        } catch (Exception e) {
            handleWriteError("Market data batch", "multiple", e);
            throw new RuntimeException("InfluxDB Market data batch write failed", e);
        }
    }

    public void saveDepthDataBatch(java.util.List<DepthData> depthDataList) {
        if (isMockMode()) return;
        try {
            java.util.List<Point> points = new java.util.ArrayList<>();
            depthDataList.forEach(data -> points.add(createDepthPoint(data)));
            if (!points.isEmpty()) {
                writeApi.writePoints(points);
                writeCount.addAndGet(points.size());
            }
        } catch (Exception e) {
            handleWriteError("Depth data batch", "multiple", e);
            throw new RuntimeException("InfluxDB Depth data batch write failed", e);
        }
    }
}
