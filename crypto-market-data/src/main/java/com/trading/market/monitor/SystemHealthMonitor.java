package com.trading.market.monitor;

import com.trading.common.thread.UnifiedThreadPoolManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 系统健康监控器
 * 监控线程池状态、Redis连接状态等关键系统组件
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class SystemHealthMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(SystemHealthMonitor.class);
    
    @Autowired
    private UnifiedThreadPoolManager threadPoolManager;
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    private final ScheduledExecutorService monitorExecutor = Executors.newScheduledThreadPool(2);
    private final AtomicBoolean isMonitoring = new AtomicBoolean(false);
    
    @PostConstruct
    public void startMonitoring() {
        if (isMonitoring.compareAndSet(false, true)) {
            log.info("启动系统健康监控...");
            
            // 每30秒检查一次线程池状态
            monitorExecutor.scheduleAtFixedRate(this::checkThreadPoolHealth, 30, 30, TimeUnit.SECONDS);
            
            // 每60秒检查一次Redis连接状态
            monitorExecutor.scheduleAtFixedRate(this::checkRedisHealth, 60, 60, TimeUnit.SECONDS);
            
            log.info("系统健康监控已启动");
        }
    }
    
    @PreDestroy
    public void stopMonitoring() {
        if (isMonitoring.compareAndSet(true, false)) {
            log.info("开始停止系统健康监控...");
            monitorExecutor.shutdownNow(); // 立即停止所有正在执行和等待的任务
            try {
                // 等待最多5秒钟让任务终止
                if (!monitorExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    log.warn("监控执行器在5秒内未能完全终止。");
                }
            } catch (InterruptedException e) {
                log.error("停止监控时发生中断异常。", e);
                Thread.currentThread().interrupt();
            }
            log.info("系统健康监控已确认停止。");
        }
    }
    
    /**
     * 检查线程池健康状态
     */
    private void checkThreadPoolHealth() {
        if (!isMonitoring.get()) {
            log.warn("监控服务已停止，跳过线程池健康检查。");
            return;
        }
        try {
            // 获取线程池统计信息
            var stats = threadPoolManager.getThreadPoolStats();
            
            for (var entry : stats.entrySet()) {
                String poolName = entry.getKey();
                var poolStats = entry.getValue();
                
                if (poolStats.containsKey("executor")) {
                    Object executor = poolStats.get("executor");
                    if (executor instanceof ThreadPoolExecutor) {
                        ThreadPoolExecutor tpe = (ThreadPoolExecutor) executor;
                        
                        int activeCount = tpe.getActiveCount();
                        int corePoolSize = tpe.getCorePoolSize();
                        int maximumPoolSize = tpe.getMaximumPoolSize();
                        long taskCount = tpe.getTaskCount();
                        long completedTaskCount = tpe.getCompletedTaskCount();
                        int queueSize = tpe.getQueue().size();
                        
                        // 计算线程池使用率
                        double utilizationRate = (double) activeCount / maximumPoolSize;
                        
                        log.debug("线程池状态 [{}]: 活跃线程={}/{}, 队列大小={}, 任务总数={}, 完成任务={}, 使用率={:.2f}%",
                                poolName, activeCount, maximumPoolSize, queueSize, taskCount, completedTaskCount, utilizationRate * 100);
                        
                        // 检查是否需要告警
                        if (utilizationRate > 0.8) {
                            log.warn("线程池 [{}] 使用率过高: {:.2f}%, 活跃线程={}/{}", 
                                    poolName, utilizationRate * 100, activeCount, maximumPoolSize);
                        }
                        
                        if (queueSize > 1000) {
                            log.warn("线程池 [{}] 队列积压严重: {} 个任务等待处理", poolName, queueSize);
                        }
                    }
                }
            }
            
        } catch (Exception e) {
            log.error("检查线程池健康状态失败", e);
        }
    }
    
    /**
     * 检查Redis连接健康状态
     */
    private void checkRedisHealth() {
        if (!isMonitoring.get()) {
            log.warn("监控服务已停止，跳过Redis健康检查。");
            return;
        }
        try {
            long startTime = System.currentTimeMillis();
            
            // 执行ping命令测试连接
            String pong = redisTemplate.getConnectionFactory().getConnection().ping();
            
            long responseTime = System.currentTimeMillis() - startTime;
            
            if ("PONG".equals(pong)) {
                log.debug("Redis连接健康检查通过, 响应时间: {}ms", responseTime);
                
                if (responseTime > 1000) {
                    log.warn("Redis响应时间过长: {}ms", responseTime);
                }
            } else {
                log.error("Redis连接健康检查失败: 响应={}", pong);
            }
            
        } catch (Exception e) {
            log.error("Redis连接健康检查异常", e);
            
            // 尝试重新初始化连接
            try {
                redisTemplate.getConnectionFactory().getConnection().ping();
                log.info("Redis连接重新初始化成功");
            } catch (Exception retryEx) {
                log.error("Redis连接重新初始化失败", retryEx);
            }
        }
    }
    
    /**
     * 获取系统健康状态摘要
     */
    public SystemHealthStatus getHealthStatus() {
        SystemHealthStatus status = new SystemHealthStatus();
        
        try {
            // 检查线程池状态
            var stats = threadPoolManager.getThreadPoolStats();
            status.setThreadPoolHealthy(checkThreadPoolsHealthy(stats));
            
            // 检查Redis状态
            status.setRedisHealthy(checkRedisHealthy());
            
            // 设置整体状态
            status.setOverallHealthy(status.isThreadPoolHealthy() && status.isRedisHealthy());
            
        } catch (Exception e) {
            log.error("获取系统健康状态失败", e);
            status.setOverallHealthy(false);
        }
        
        return status;
    }
    
    private boolean checkThreadPoolsHealthy(java.util.Map<String, java.util.Map<String, Object>> stats) {
        for (var entry : stats.entrySet()) {
            var poolStats = entry.getValue();
            if (poolStats.containsKey("executor")) {
                Object executor = poolStats.get("executor");
                if (executor instanceof ThreadPoolExecutor) {
                    ThreadPoolExecutor tpe = (ThreadPoolExecutor) executor;
                    double utilizationRate = (double) tpe.getActiveCount() / tpe.getMaximumPoolSize();
                    if (utilizationRate > 0.9 || tpe.getQueue().size() > 2000) {
                        return false;
                    }
                }
            }
        }
        return true;
    }
    
    private boolean checkRedisHealthy() {
        try {
            String pong = redisTemplate.getConnectionFactory().getConnection().ping();
            return "PONG".equals(pong);
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * 系统健康状态数据类
     */
    public static class SystemHealthStatus {
        private boolean overallHealthy;
        private boolean threadPoolHealthy;
        private boolean redisHealthy;
        
        // Getters and Setters
        public boolean isOverallHealthy() { return overallHealthy; }
        public void setOverallHealthy(boolean overallHealthy) { this.overallHealthy = overallHealthy; }
        
        public boolean isThreadPoolHealthy() { return threadPoolHealthy; }
        public void setThreadPoolHealthy(boolean threadPoolHealthy) { this.threadPoolHealthy = threadPoolHealthy; }
        
        public boolean isRedisHealthy() { return redisHealthy; }
        public void setRedisHealthy(boolean redisHealthy) { this.redisHealthy = redisHealthy; }
        
        @Override
        public String toString() {
            return String.format("SystemHealthStatus{overall=%s, threadPool=%s, redis=%s}", 
                    overallHealthy, threadPoolHealthy, redisHealthy);
        }
    }
}
