package com.trading.market.controller;

import com.trading.common.cache.MarketDataCacheService;
import com.trading.common.cache.MultiLevelCacheManager;
import com.trading.market.startup.CacheWarmupStartupRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * 缓存管理控制器
 * 提供缓存监控、管理和查询API
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/cache")
public class CacheController {
    
    private static final Logger log = LoggerFactory.getLogger(CacheController.class);
    
    @Autowired
    private MarketDataCacheService cacheService;

    @Autowired
    private CacheWarmupStartupRunner cacheWarmupRunner;
    
    /**
     * 获取缓存统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getCacheStats() {
        try {
            Object statsObj = cacheService.getCacheStats();
            Map<String, Object> result = new HashMap<>();

            if (statsObj != null) {
                // 检查是否是CacheStatsImpl类型，如果是则转换为Map
                if (statsObj instanceof com.trading.common.cache.CacheStatsImpl) {
                    com.trading.common.cache.CacheStatsImpl cacheStats =
                        (com.trading.common.cache.CacheStatsImpl) statsObj;

                    // 手动构建统计信息Map
                    Map<String, Object> statsMap = new HashMap<>();
                    statsMap.put("l1HitCount", cacheStats.getL1HitCount());
                    statsMap.put("l1MissCount", cacheStats.getL1MissCount());
                    statsMap.put("l2HitCount", cacheStats.getL2HitCount());
                    statsMap.put("l2MissCount", cacheStats.getL2MissCount());
                    statsMap.put("hitRate", cacheStats.getHitRate());
                    statsMap.put("l1HitRate", cacheStats.getL1HitRate());
                    statsMap.put("l2HitRate", cacheStats.getL2HitRate());
                    statsMap.put("cacheSize", cacheStats.getCacheSize());

                    result.put("cacheStats", statsMap);
                    result.put("summary", cacheStats.getSummary());
                } else {
                    // 其他类型的统计信息
                    result.put("stats", statsObj);
                }
            } else {
                result.put("stats", "No cache statistics available");
            }

            result.put("timestamp", System.currentTimeMillis());
            result.put("status", "success");

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取缓存统计失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of(
                        "error", "获取缓存统计失败: " + e.getMessage(),
                        "timestamp", System.currentTimeMillis(),
                        "status", "error"
                    ));
        }
    }
    
    /**
     * 获取最新价格
     */
    @GetMapping("/price/{symbol}")
    public ResponseEntity<Map<String, Object>> getLatestPrice(@PathVariable String symbol) {
        try {
            Optional<BigDecimal> price = cacheService.getLatestPrice(symbol.toUpperCase());
            
            Map<String, Object> result = new HashMap<>();
            result.put("symbol", symbol.toUpperCase());
            result.put("price", price.orElse(null));
            result.put("cached", price.isPresent());
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取最新价格失败: symbol={}", symbol, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取最新价格失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取K线数据
     */
    @GetMapping("/kline/{symbol}/{interval}")
    public ResponseEntity<Map<String, Object>> getKlineData(
            @PathVariable String symbol,
            @PathVariable String interval) {
        try {
            Optional<Object> klineData = cacheService.getKlineData(
                    symbol.toUpperCase(), interval, Object.class);
            
            Map<String, Object> result = new HashMap<>();
            result.put("symbol", symbol.toUpperCase());
            result.put("interval", interval);
            result.put("data", klineData.orElse(null));
            result.put("cached", klineData.isPresent());
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取K线数据失败: symbol={}, interval={}", symbol, interval, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取K线数据失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取深度数据
     */
    @GetMapping("/depth/{symbol}")
    public ResponseEntity<Map<String, Object>> getDepthData(
            @PathVariable String symbol,
            @RequestParam(defaultValue = "20") int levels) {
        try {
            Optional<Object> depthData = cacheService.getDepthData(
                    symbol.toUpperCase(), levels, Object.class);
            
            Map<String, Object> result = new HashMap<>();
            result.put("symbol", symbol.toUpperCase());
            result.put("levels", levels);
            result.put("data", depthData.orElse(null));
            result.put("cached", depthData.isPresent());
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取深度数据失败: symbol={}, levels={}", symbol, levels, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取深度数据失败: " + e.getMessage()));
        }
    }
    
    /**
     * 预热缓存
     */
    @PostMapping("/warmup")
    public ResponseEntity<Map<String, Object>> warmUpCache(@RequestBody String[] symbols) {
        try {
            CompletableFuture<Void> future = cacheService.warmUpCache(symbols);
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "缓存预热已启动");
            result.put("symbols", symbols);
            result.put("count", symbols.length);
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("缓存预热失败: symbols={}", (Object) symbols, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "缓存预热失败: " + e.getMessage()));
        }
    }
    
    /**
     * 清空指定交易对缓存
     */
    @DeleteMapping("/symbol/{symbol}")
    public ResponseEntity<Map<String, Object>> evictSymbolCache(@PathVariable String symbol) {
        try {
            cacheService.evictSymbolCache(symbol.toUpperCase());
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "交易对缓存已清空");
            result.put("symbol", symbol.toUpperCase());
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("清空交易对缓存失败: symbol={}", symbol, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "清空交易对缓存失败: " + e.getMessage()));
        }
    }
    
    /**
     * 清空所有缓存
     */
    @DeleteMapping("/all")
    public ResponseEntity<Map<String, Object>> clearAllCache() {
        try {
            cacheService.clearAllCache();
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "所有缓存已清空");
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("清空所有缓存失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "清空所有缓存失败: " + e.getMessage()));
        }
    }
    
    /**
     * 重置缓存统计
     */
    @PostMapping("/stats/reset")
    public ResponseEntity<Map<String, Object>> resetCacheStats() {
        try {
            // 暂时注释掉reset方法调用，因为返回类型是Object
            // cacheService.getCacheStats().reset();
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", "缓存统计已重置");
            result.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("重置缓存统计失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "重置缓存统计失败: " + e.getMessage()));
        }
    }

    /**
     * 手动触发缓存预热
     * 用于配置更新后重新预热缓存
     */
    @PostMapping("/warmup/trigger")
    public ResponseEntity<Map<String, Object>> triggerWarmup() {
        try {
            log.info("收到手动预热请求");

            CompletableFuture<Void> warmupFuture = cacheWarmupRunner.triggerWarmup();

            Map<String, Object> result = new HashMap<>();
            result.put("message", "缓存预热已启动");
            result.put("timestamp", System.currentTimeMillis());
            result.put("status", "started");

            // 异步处理预热完成状态
            warmupFuture.whenComplete((unused, throwable) -> {
                if (throwable != null) {
                    log.error("缓存预热失败", throwable);
                } else {
                    log.info("缓存预热完成");
                }
            });

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("启动缓存预热失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "启动缓存预热失败: " + e.getMessage()));
        }
    }

    /**
     * 预热指定交易对的缓存
     */
    @PostMapping("/warmup/symbols")
    public ResponseEntity<Map<String, Object>> warmupSymbols(@RequestBody List<String> symbols) {
        try {
            if (symbols == null || symbols.isEmpty()) {
                return ResponseEntity.badRequest()
                        .body(Map.of("error", "交易对列表不能为空"));
            }

            log.info("收到指定交易对预热请求，数量: {}", symbols.size());

            CompletableFuture<Void> warmupFuture = cacheWarmupRunner.warmupSymbols(symbols);

            Map<String, Object> result = new HashMap<>();
            result.put("message", "指定交易对缓存预热已启动");
            result.put("symbols", symbols);
            result.put("count", symbols.size());
            result.put("timestamp", System.currentTimeMillis());
            result.put("status", "started");

            // 异步处理预热完成状态
            warmupFuture.whenComplete((unused, throwable) -> {
                if (throwable != null) {
                    log.error("指定交易对缓存预热失败", throwable);
                } else {
                    log.info("指定交易对缓存预热完成，数量: {}", symbols.size());
                }
            });

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("启动指定交易对缓存预热失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "启动指定交易对缓存预热失败: " + e.getMessage()));
        }
    }
}
