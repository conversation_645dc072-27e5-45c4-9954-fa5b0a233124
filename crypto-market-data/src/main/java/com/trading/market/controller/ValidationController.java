package com.trading.market.controller;

import com.trading.common.validator.DataStorageValidationService;
import com.trading.common.validator.model.ValidationReport;
import com.trading.common.validator.model.ValidationResult;
import com.trading.common.validator.model.StorageHealthStatus;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 数据存储验证控制器
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/validation")
@RequiredArgsConstructor
public class ValidationController {
    
    private static final Logger log = LoggerFactory.getLogger(ValidationController.class);
    
    private final DataStorageValidationService validationService;
    
    /**
     * 执行完整的数据存储验证
     */
    @PostMapping("/storage/full")
    public CompletableFuture<ResponseEntity<ValidationReport>> validateAllStorageSystems() {
        log.info("收到完整数据存储验证请求");
        
        return validationService.validateAllStorageSystems()
                .thenApply(report -> {
                    log.info("数据存储验证完成 - 报告ID: {}, 成功率: {:.1f}%", 
                            report.getReportId(), report.getSuccessRate());
                    return ResponseEntity.ok(report);
                })
                .exceptionally(throwable -> {
                    log.error("数据存储验证失败", throwable);
                    return ResponseEntity.internalServerError().build();
                });
    }
    
    /**
     * 验证特定存储系统
     */
    @PostMapping("/storage/{storageName}")
    public CompletableFuture<ResponseEntity<ValidationResult>> validateSpecificStorage(
            @PathVariable String storageName) {
        log.info("收到特定存储系统验证请求: {}", storageName);
        
        return validationService.validateSpecificStorage(storageName)
                .thenApply(result -> {
                    log.info("存储系统 {} 验证完成: {}", storageName, result.isSuccess() ? "成功" : "失败");
                    return ResponseEntity.ok(result);
                })
                .exceptionally(throwable -> {
                    log.error("存储系统 {} 验证失败", storageName, throwable);
                    return ResponseEntity.internalServerError().build();
                });
    }
    
    /**
     * 获取存储系统健康状态
     */
    @GetMapping("/storage/health")
    public CompletableFuture<ResponseEntity<List<StorageHealthStatus>>> getStorageHealthStatus() {
        log.info("收到存储系统健康状态查询请求");
        
        return validationService.getStorageHealthStatus()
                .thenApply(healthStatuses -> {
                    log.info("存储系统健康状态查询完成，共 {} 个系统", healthStatuses.size());
                    return ResponseEntity.ok(healthStatuses);
                })
                .exceptionally(throwable -> {
                    log.error("存储系统健康状态查询失败", throwable);
                    return ResponseEntity.internalServerError().build();
                });
    }
    
    /**
     * 快速连接性检查
     */
    @GetMapping("/storage/connectivity")
    public CompletableFuture<ResponseEntity<Map<String, Boolean>>> quickConnectivityCheck() {
        log.info("收到快速连接性检查请求");
        
        return validationService.quickConnectivityCheck()
                .thenApply(connectivity -> {
                    long connectedCount = connectivity.values().stream().mapToLong(connected -> connected ? 1 : 0).sum();
                    log.info("快速连接性检查完成，{}/{} 个系统连接正常", connectedCount, connectivity.size());
                    return ResponseEntity.ok(connectivity);
                })
                .exceptionally(throwable -> {
                    log.error("快速连接性检查失败", throwable);
                    return ResponseEntity.internalServerError().build();
                });
    }
    
    /**
     * 获取验证报告摘要（文本格式）
     */
    @PostMapping("/storage/full/summary")
    public CompletableFuture<ResponseEntity<String>> getValidationSummary() {
        log.info("收到验证报告摘要请求");
        
        return validationService.validateAllStorageSystems()
                .thenApply(report -> {
                    String summaryText = report.generateSummaryText();
                    log.info("验证报告摘要生成完成");
                    return ResponseEntity.ok(summaryText);
                })
                .exceptionally(throwable -> {
                    log.error("验证报告摘要生成失败", throwable);
                    return ResponseEntity.internalServerError()
                            .body("验证报告摘要生成失败: " + throwable.getMessage());
                });
    }
}
