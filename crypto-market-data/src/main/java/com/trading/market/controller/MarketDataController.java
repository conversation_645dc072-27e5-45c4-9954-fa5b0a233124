package com.trading.market.controller;

import com.trading.market.service.MarketDataService;
import com.trading.common.dto.KlineData;
import com.trading.common.dto.DepthData;
import com.trading.common.dto.TradeData;
import com.trading.market.collector.HistoricalDataCollector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.Instant;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * Market Data API Controller
 * Provides REST API endpoints for crypto-ml-strategy integration
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/market-data")
public class MarketDataController {
    
    private static final Logger log = LoggerFactory.getLogger(MarketDataController.class);
    
    @Autowired
    private MarketDataService marketDataService;

    @Autowired
    private HistoricalDataCollector historicalDataCollector;
    
    /**
     * Get latest market data
     */
    @GetMapping("/latest")
    public ResponseEntity<Map<String, Object>> getLatestMarketData(
            @RequestParam(defaultValue = "BTCUSDT") String symbol) {
        try {
            log.info("Getting latest market data for symbol: {}", symbol);
            
            Optional<TradeData> latestData = marketDataService.getLatestTradeData(symbol);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("symbol", symbol);
            response.put("data", latestData.orElse(null));
            response.put("timestamp", System.currentTimeMillis());
            
            log.info("Latest market data retrieved for symbol: {}, hasData: {}", symbol, latestData.isPresent());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to get latest market data for symbol: {}", symbol, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "error", e.getMessage()));
        }
    }

    /**
     * Get historical K-line data for a specific symbol, interval, and date range.
     * This endpoint is designed for consumption by the Python ML strategy module.
     */
    @GetMapping("/historical")
    public ResponseEntity<Map<String, Object>> getHistoricalData(
            @RequestParam String symbol,
            @RequestParam(defaultValue = "1m") String interval,
            @RequestParam @org.springframework.format.annotation.DateTimeFormat(iso = org.springframework.format.annotation.DateTimeFormat.ISO.DATE_TIME) java.time.ZonedDateTime startTime,
            @RequestParam @org.springframework.format.annotation.DateTimeFormat(iso = org.springframework.format.annotation.DateTimeFormat.ISO.DATE_TIME) java.time.ZonedDateTime endTime) {
        try {
            Instant startInstant = startTime.toInstant();
            Instant endInstant = endTime.toInstant();

            log.info("Getting historical K-line data for symbol: {}, interval: {}, start: {}, end: {}",
                    symbol, interval, startInstant, endInstant);

            List<KlineData> klineData = marketDataService.getHistoricalData(symbol, interval, startInstant, endInstant);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("symbol", symbol);
            response.put("interval", interval);
            response.put("data", klineData);
            response.put("count", klineData.size());
            response.put("timestamp", System.currentTimeMillis());

            log.info("Historical K-line data retrieved for symbol: {}, count: {}", symbol, klineData.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to get historical K-line data for symbol: {}", symbol, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "error", e.getMessage()));
        }
    }
    
    /**
     * Get supported symbols
     */
    @GetMapping("/symbols")
    public ResponseEntity<Map<String, Object>> getSymbols() {
        try {
            log.info("Getting supported symbols");
            
            List<String> symbols = List.of("BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", 
                                         "DOTUSDT", "LINKUSDT", "LTCUSDT", "BCHUSDT",
                                         "XLMUSDT", "EOSUSDT");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("symbols", symbols);
            response.put("count", symbols.size());
            response.put("timestamp", System.currentTimeMillis());
            
            log.info("Symbols retrieved, count: {}", symbols.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to get symbols", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "error", e.getMessage()));
        }
    }
    
    /**
     * Get K-line data for specific symbol and interval
     */
    @GetMapping("/kline/{symbol}/{interval}")
    public ResponseEntity<Map<String, Object>> getKlineData(
            @PathVariable String symbol,
            @PathVariable String interval,
            @RequestParam(defaultValue = "100") int limit) {
        try {
            log.info("Getting K-line data for symbol: {}, interval: {}, limit: {}", symbol, interval, limit);
            
            List<KlineData> klineData = marketDataService.getKlineData(symbol, interval, limit);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("symbol", symbol);
            response.put("interval", interval);
            response.put("data", klineData);
            response.put("count", klineData.size());
            response.put("timestamp", System.currentTimeMillis());
            
            log.info("K-line data retrieved for symbol: {}, interval: {}, count: {}", symbol, interval, klineData.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to get K-line data for symbol: {}, interval: {}", symbol, interval, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "error", e.getMessage()));
        }
    }
    
    /**
     * Get depth data for specific symbol
     */
    @GetMapping("/depth/{symbol}")
    public ResponseEntity<Map<String, Object>> getDepthData(
            @PathVariable String symbol,
            @RequestParam(defaultValue = "20") int levels) {
        try {
            log.info("Getting depth data for symbol: {}, levels: {}", symbol, levels);
            
            Optional<DepthData> depthData = marketDataService.getLatestDepthData(symbol, levels);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("symbol", symbol);
            response.put("levels", levels);
            response.put("data", depthData.orElse(null));
            response.put("timestamp", System.currentTimeMillis());
            
            log.info("Depth data retrieved for symbol: {}, levels: {}, hasData: {}", symbol, levels, depthData.isPresent());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("Failed to get depth data for symbol: {}, levels: {}", symbol, levels, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "error", e.getMessage()));
        }
    }

    /**
     * Get recent trade data for a specific symbol.
     * This endpoint is designed for consumption by the Python ML strategy module.
     */
    @GetMapping("/trade/{symbol}")
    public ResponseEntity<Map<String, Object>> getTradeData(
            @PathVariable String symbol,
            @RequestParam(defaultValue = "30") int days) {
        try {
            log.info("Getting trade data for symbol: {}, days: {}", symbol, days);

            CompletableFuture<List<TradeData>> tradeDataFuture = marketDataService.getHistoricalMarketData(symbol, "trade", days);
            List<TradeData> tradeData = tradeDataFuture.join();

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("symbol", symbol);
            response.put("dataType", "trade");
            response.put("data", tradeData);
            response.put("count", tradeData.size());
            response.put("timestamp", System.currentTimeMillis());

            log.info("Trade data retrieved for symbol: {}, count: {}", symbol, tradeData.size());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to get trade data for symbol: {}, days: {}", symbol, days, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "error", e.getMessage()));
        }
    }

    /**
     * 健康检查端点
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("status", "UP");
            response.put("service", "crypto-market-data");
            response.put("timestamp", Instant.now().toString());
            response.put("version", "1.0.0");

            // 检查数据库连接
            try {
                long totalRecords = marketDataService.getTotalRecordsCount();
                response.put("database", "UP");
                response.put("totalRecords", totalRecords);
            } catch (Exception e) {
                response.put("database", "DOWN");
                response.put("databaseError", e.getMessage());
            }

            // 检查历史数据收集器状态
            try {
                String collectorStatus = historicalDataCollector.getStatus();
                response.put("collector", collectorStatus);
            } catch (Exception e) {
                response.put("collector", "UNKNOWN");
                response.put("collectorError", e.getMessage());
            }

            log.info("Health check completed: {}", response);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Health check failed", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("status", "DOWN", "error", e.getMessage()));
        }
    }

    /**
     * 获取统计信息端点
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getStats() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("timestamp", Instant.now().toString());

            // 获取数据统计
            Map<String, Object> dataStats = marketDataService.getDataStatistics();
            response.put("dataStatistics", dataStats);

            // 获取收集器统计
            Map<String, Object> collectorStats = historicalDataCollector.getStatistics();
            response.put("collectorStatistics", collectorStats);

            log.info("Statistics retrieved: {}", response);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to get statistics", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "error", e.getMessage()));
        }
    }

    /**
     * 强制收集历史数据端点
     */
    @PostMapping("/collector/force-collect")
    public ResponseEntity<Map<String, Object>> forceCollect(@RequestBody Map<String, Object> request) {
        try {
            log.info("Force collect request: {}", request);

            // 解析请求参数
            int days = (Integer) request.getOrDefault("days", 90);
            boolean forceFullCollection = (Boolean) request.getOrDefault("forceFullCollection", false);
            @SuppressWarnings("unchecked")
            List<String> symbols = (List<String>) request.getOrDefault("symbols",
                List.of("BTCUSDT", "ETHUSDT", "BNBUSDT", "SOLUSDT", "DOGEUSDT"));

            // 启动异步收集
            CompletableFuture<Void> collectionFuture = historicalDataCollector.forceCollectHistoricalData(
                symbols, days, forceFullCollection);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Historical data collection started");
            response.put("days", days);
            response.put("symbols", symbols);
            response.put("forceFullCollection", forceFullCollection);
            response.put("timestamp", Instant.now().toString());

            log.info("Force collection started for {} symbols, {} days", symbols.size(), days);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to start force collection", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "error", e.getMessage()));
        }
    }

    /**
     * 获取收集器状态端点
     */
    @GetMapping("/collector/status")
    public ResponseEntity<Map<String, Object>> getCollectorStatus() {
        try {
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("status", historicalDataCollector.getStatus());
            response.put("statistics", historicalDataCollector.getStatistics());
            response.put("timestamp", Instant.now().toString());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("Failed to get collector status", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "error", e.getMessage()));
        }
    }
}