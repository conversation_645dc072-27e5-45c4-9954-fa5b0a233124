
package com.trading.market.controller;

import com.trading.common.dto.KlineData;
import com.trading.market.collector.HistoricalDataCollector;
import com.trading.market.processor.DataProcessor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 用于数据恢复和回补的REST控制器。
 * <p>
 * 提供端点以接收外部脚本（如Python回补脚本）发送的历史数据，
 * 或手动触发历史数据采集任务。
 * </p>
 */
@RestController
@RequestMapping("/api/v1/data-recovery")
@Slf4j
public class DataRecoveryController {

    @Autowired
    private DataProcessor dataProcessor;

    @Autowired
    private HistoricalDataCollector historicalDataCollector;

    /**
     * 接收并处理批量K线数据。
     *
     * @param klineDataList 从外部脚本接收的K线数据列表。
     * @return 响应实体，包含处理结果的摘要。
     */
    @PostMapping("/kline")
    public ResponseEntity<String> submitKlineData(@RequestBody List<KlineData> klineDataList) {
        if (klineDataList == null || klineDataList.isEmpty()) {
            return ResponseEntity.badRequest().body("Request body must contain a list of KlineData.");
        }

        log.info("Received {} K-line records for data recovery.", klineDataList.size());

        // 使用异步方式将数据提交到处理器，避免阻塞请求线程
        CompletableFuture<?>[] futures = klineDataList.stream()
                .map(dataProcessor::processKlineData)
                .toArray(CompletableFuture[]::new);

        // 等待所有数据都进入处理队列
        CompletableFuture.allOf(futures).join();

        String responseMessage = String.format("Successfully submitted %d K-line records for processing.", klineDataList.size());
        return ResponseEntity.ok(responseMessage);
    }

    /**
     * 手动触发指定交易对的历史数据采集。
     *
     * @param request 包含交易对列表、回补天数和是否强制全量采集的请求体。
     * @return 响应实体，告知任务已触发。
     */
    @PostMapping("/trigger-collection")
    public ResponseEntity<String> triggerHistoricalCollection(@RequestBody ManualCollectionRequest request) {
        List<String> symbols = request.getSymbols();
        int days = request.getDays();
        boolean forceFull = request.isForceFull();

        if (symbols == null || symbols.isEmpty() || days <= 0) {
            return ResponseEntity.badRequest().body("Request must contain a valid list of symbols and a positive number of days.");
        }

        log.info("Manual historical data collection triggered for symbols: {}, days: {}, forceFull: {}", symbols, days, forceFull);

        // 异步触发采集任务
        historicalDataCollector.forceCollectHistoricalData(symbols, days, forceFull);

        String responseMessage = String.format("Successfully triggered historical data collection for %d symbols for the last %d days.", symbols.size(), days);
        return ResponseEntity.ok(responseMessage);
    }

    /**
     * 用于手动触发数据采集请求的DTO。
     */
    @Data
    static class ManualCollectionRequest {
        private List<String> symbols;
        private int days;
        private boolean forceFull = false; // 默认为增量更新
    }
}
