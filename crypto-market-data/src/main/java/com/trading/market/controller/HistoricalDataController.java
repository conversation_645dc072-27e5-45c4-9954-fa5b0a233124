package com.trading.market.controller;

import com.trading.market.collector.HistoricalDataCollector;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 历史数据控制器
 * 提供历史数据收集的REST API接口
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/historical")
public class HistoricalDataController {
    
    private static final Logger log = LoggerFactory.getLogger(HistoricalDataController.class);
    
    @Autowired
    private HistoricalDataCollector historicalDataCollector;
    
    /**
     * 手动触发历史数据收集
     */
    @PostMapping("/collect")
    public ResponseEntity<Map<String, Object>> startCollection() {
        log.info("接收到手动触发历史数据收集请求");
        
        try {
            // 使用CompletableFuture异步启动历史数据收集
            CompletableFuture.runAsync(() -> {
                try {
                    log.info("开始异步执行历史数据收集...");
                    historicalDataCollector.startHistoricalDataCollection();
                    log.info("历史数据收集完成");
                } catch (Exception e) {
                    log.error("历史数据收集过程中发生错误", e);
                }
            });
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "历史数据收集已启动，正在后台执行");
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("启动历史数据收集失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "启动历史数据收集失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 获取历史数据收集统计信息
     */
    @GetMapping("/stats")
    public ResponseEntity<Map<String, Object>> getCollectionStats() {
        log.info("获取历史数据收集统计信息");
        
        try {
            HistoricalDataCollector.CollectionStats stats = historicalDataCollector.getStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("data", stats);
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取历史数据收集统计信息失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "获取统计信息失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
    
    /**
     * 检查历史数据收集状态
     */
    @GetMapping("/status")
    public ResponseEntity<Map<String, Object>> getCollectionStatus() {
        log.info("检查历史数据收集状态");
        
        try {
            HistoricalDataCollector.CollectionStats stats = historicalDataCollector.getStats();
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("isRunning", stats.getTotalCollectedCount() > 0);
            response.put("totalCollected", stats.getTotalCollectedCount());
            response.put("errorCount", stats.getErrorCount());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("检查历史数据收集状态失败", e);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "检查状态失败: " + e.getMessage());
            response.put("timestamp", System.currentTimeMillis());
            
            return ResponseEntity.internalServerError().body(response);
        }
    }
}
