package com.trading.market.controller;

import com.trading.market.service.DataVolumeService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;

/**
 * 智能数据量管理控制器
 * 提供智能数据量检测、统计和管理功能
 */
@Slf4j
@RestController
@RequestMapping("/api/market/data-volume")
@RequiredArgsConstructor
public class DataVolumeController {

    private final DataVolumeService dataVolumeService;

    /**
     * 获取数据量统计概览
     */
    @GetMapping("/overview")
    public ResponseEntity<Map<String, Object>> getDataVolumeOverview() {
        try {
            Map<String, Object> overview = dataVolumeService.getDataVolumeOverview();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", overview
            ));
        } catch (Exception e) {
            log.error("获取数据量概览失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 智能数据收集建议
     */
    @GetMapping("/collection-recommendation")
    public ResponseEntity<Map<String, Object>> getCollectionRecommendation() {
        try {
            Map<String, Object> recommendation = dataVolumeService.getCollectionRecommendation();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "recommendation", recommendation
            ));
        } catch (Exception e) {
            log.error("获取收集建议失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 执行智能数据收集
     */
    @PostMapping("/smart-collection")
    public ResponseEntity<Map<String, Object>> executeSmartCollection(
            @RequestParam(required = false) String symbol,
            @RequestParam(required = false) String interval,
            @RequestParam(defaultValue = "false") boolean force) {
        try {
            Map<String, Object> result = dataVolumeService.executeSmartCollection(symbol, interval, force);
            return ResponseEntity.ok(Map.of(
                "success", true,
                "result", result
            ));
        } catch (Exception e) {
            log.error("智能数据收集失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 计算数据量
     * 根据交易对数量计算90天的数据量
     */
    @GetMapping("/calculate")
    public ResponseEntity<Map<String, Object>> calculateDataVolume(
            @RequestParam(defaultValue = "1") int symbolCount) {
        try {
            log.info("开始计算数据量: symbolCount={}", symbolCount);

            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("symbolCount", symbolCount);
            result.put("days", 90);

            // K线数据计算
            Map<String, Object> klineData = calculateKlineData(symbolCount);
            result.put("klineData", klineData);

            // 深度数据计算
            Map<String, Object> depthData = calculateDepthData(symbolCount);
            result.put("depthData", depthData);

            // 交易数据计算
            Map<String, Object> tradeData = calculateTradeData(symbolCount);
            result.put("tradeData", tradeData);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("计算数据量失败: symbolCount={}", symbolCount, e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 获取支持的时间间隔信息
     */
    @GetMapping("/intervals")
    public ResponseEntity<Map<String, Object>> getIntervals() {
        try {
            Map<String, Object> result = new HashMap<>();
            result.put("success", true);
            result.put("totalIntervalsSupported", 6);

            Map<String, Object> intervals = new HashMap<>();

            // 1分钟间隔：90天 * 24小时 * 60分钟 = 129,600条
            intervals.put("1m", Map.of("recordsPer90Days", 129600));

            // 5分钟间隔：129,600 / 5 = 25,920条
            intervals.put("5m", Map.of("recordsPer90Days", 25920));

            // 15分钟间隔：129,600 / 15 = 8,640条
            intervals.put("15m", Map.of("recordsPer90Days", 8640));

            // 1小时间隔：90天 * 24小时 = 2,160条
            intervals.put("1h", Map.of("recordsPer90Days", 2160));

            // 4小时间隔：2,160 / 4 = 540条
            intervals.put("4h", Map.of("recordsPer90Days", 540));

            // 1天间隔：90条
            intervals.put("1d", Map.of("recordsPer90Days", 90));

            result.put("intervals", intervals);

            return ResponseEntity.ok(result);

        } catch (Exception e) {
            log.error("获取时间间隔信息失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 获取支持的数据类型信息
     */
    @GetMapping("/data-types")
    public ResponseEntity<Map<String, Object>> getDataTypes() {
        try {
            Map<String, Object> result = dataVolumeService.getDataTypes();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "data", result
            ));
        } catch (Exception e) {
            log.error("获取数据类型信息失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 获取存储策略信息
     */
    @GetMapping("/storage-strategy")
    public ResponseEntity<Map<String, Object>> getStorageStrategy() {
        try {
            Map<String, Object> strategy = dataVolumeService.getStorageStrategy();
            return ResponseEntity.ok(Map.of(
                "success", true,
                "strategy", strategy
            ));
        } catch (Exception e) {
            log.error("获取存储策略失败", e);
            return ResponseEntity.ok(Map.of(
                "success", false,
                "error", e.getMessage()
            ));
        }
    }

    /**
     * 计算K线数据量
     */
    private Map<String, Object> calculateKlineData(int symbolCount) {
        Map<String, Object> klineData = new HashMap<>();

        // 各时间间隔的记录数
        Map<String, Integer> intervalCounts = new HashMap<>();
        intervalCounts.put("1m", 129600);
        intervalCounts.put("5m", 25920);
        intervalCounts.put("15m", 8640);
        intervalCounts.put("1h", 2160);
        intervalCounts.put("4h", 540);
        intervalCounts.put("1d", 90);

        // 计算总记录数：166,950条/交易对
        long totalRecords = 166950L * symbolCount;

        // 计算存储大小（假设每条记录200字节）
        BigDecimal totalSizeGB = BigDecimal.valueOf(totalRecords * 200)
                .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 3, RoundingMode.HALF_UP);

        klineData.put("totalRecords", totalRecords);
        klineData.put("intervalCounts", intervalCounts);
        klineData.put("totalSizeGB", totalSizeGB);

        return klineData;
    }

    /**
     * 计算深度数据量
     */
    private Map<String, Object> calculateDepthData(int symbolCount) {
        Map<String, Object> depthData = new HashMap<>();

        // 90天 * 24小时 * 60分钟 * 60秒 * 10次/秒 * 3档位
        long totalRecords = 90L * 24 * 60 * 60 * 10 * 3 * symbolCount;

        // 计算存储大小（假设每条记录500字节）
        BigDecimal totalSizeGB = BigDecimal.valueOf(totalRecords * 500)
                .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 3, RoundingMode.HALF_UP);

        depthData.put("totalRecords", totalRecords);
        depthData.put("totalSizeGB", totalSizeGB);
        depthData.put("description", "90天深度数据，每100ms更新，3个档位");

        return depthData;
    }

    /**
     * 计算交易数据量
     */
    private Map<String, Object> calculateTradeData(int symbolCount) {
        Map<String, Object> tradeData = new HashMap<>();

        // 90天 * 24小时 * 60分钟 * 60秒 * 10笔/秒
        long totalRecords = 90L * 24 * 60 * 60 * 10 * symbolCount;

        // 计算存储大小（假设每条记录100字节）
        BigDecimal totalSizeGB = BigDecimal.valueOf(totalRecords * 100)
                .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 3, RoundingMode.HALF_UP);

        tradeData.put("totalRecords", totalRecords);
        tradeData.put("totalSizeGB", totalSizeGB);
        tradeData.put("description", "90天交易数据，平均每秒10笔交易");

        return tradeData;
    }
}
