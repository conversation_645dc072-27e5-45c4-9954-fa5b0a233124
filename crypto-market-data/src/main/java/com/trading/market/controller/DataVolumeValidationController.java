package com.trading.market.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据量验证控制器
 * 用于验证90天数据量计算的准确性
 */
@Slf4j
@RestController
@RequestMapping("/api/market/data-volume-validation")
public class DataVolumeValidationController {

    /**
     * 验证90天时间间隔计算
     */
    @GetMapping("/validate-intervals")
    public Map<String, Object> validateIntervals() {
        log.info("开始验证90天时间间隔计算");
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("validationTime", LocalDateTime.now());
        
        // 90天的总分钟数
        long totalMinutes = 90L * 24 * 60; // 129,600分钟
        long totalHours = 90L * 24; // 2,160小时
        long totalDays = 90L; // 90天
        
        Map<String, Object> timeValidation = new HashMap<>();
        timeValidation.put("totalDays", totalDays);
        timeValidation.put("totalHours", totalHours);
        timeValidation.put("totalMinutes", totalMinutes);
        timeValidation.put("totalSeconds", totalMinutes * 60); // 7,776,000秒
        
        // 验证各个时间间隔的K线数量
        Map<String, Object> intervalValidation = new HashMap<>();
        
        // 1分钟K线：每分钟1条
        long count1m = totalMinutes; // 129,600条
        intervalValidation.put("1m", Map.of(
            "description", "1分钟K线",
            "calculation", "90天 × 24小时 × 60分钟",
            "formula", "90 × 24 × 60",
            "result", count1m,
            "expected", 129600,
            "correct", count1m == 129600
        ));
        
        // 5分钟K线：每5分钟1条
        long count5m = totalMinutes / 5; // 25,920条
        intervalValidation.put("5m", Map.of(
            "description", "5分钟K线",
            "calculation", "90天 × 24小时 × 12次(每小时)",
            "formula", "90 × 24 × 12",
            "result", count5m,
            "expected", 25920,
            "correct", count5m == 25920
        ));
        
        // 15分钟K线：每15分钟1条
        long count15m = totalMinutes / 15; // 8,640条
        intervalValidation.put("15m", Map.of(
            "description", "15分钟K线",
            "calculation", "90天 × 24小时 × 4次(每小时)",
            "formula", "90 × 24 × 4",
            "result", count15m,
            "expected", 8640,
            "correct", count15m == 8640
        ));
        
        // 1小时K线：每小时1条
        long count1h = totalHours; // 2,160条
        intervalValidation.put("1h", Map.of(
            "description", "1小时K线",
            "calculation", "90天 × 24小时",
            "formula", "90 × 24",
            "result", count1h,
            "expected", 2160,
            "correct", count1h == 2160
        ));
        
        // 4小时K线：每4小时1条
        long count4h = totalHours / 4; // 540条
        intervalValidation.put("4h", Map.of(
            "description", "4小时K线",
            "calculation", "90天 × 6次(每天)",
            "formula", "90 × 6",
            "result", count4h,
            "expected", 540,
            "correct", count4h == 540
        ));
        
        // 1天K线：每天1条
        long count1d = totalDays; // 90条
        intervalValidation.put("1d", Map.of(
            "description", "1天K线",
            "calculation", "90天",
            "formula", "90",
            "result", count1d,
            "expected", 90,
            "correct", count1d == 90
        ));
        
        result.put("timeValidation", timeValidation);
        result.put("intervalValidation", intervalValidation);
        
        // 计算总K线数量
        long totalKlineRecords = count1m + count5m + count15m + count1h + count4h + count1d;
        result.put("totalKlineRecords", totalKlineRecords);
        result.put("totalKlineRecordsExpected", 166950);
        result.put("klineCalculationCorrect", totalKlineRecords == 166950);
        
        log.info("90天时间间隔计算验证完成: 总K线记录数={}", totalKlineRecords);
        
        return result;
    }

    /**
     * 验证深度数据计算
     */
    @GetMapping("/validate-depth")
    public Map<String, Object> validateDepthData() {
        log.info("开始验证深度数据计算");
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        
        // 90天的总秒数
        long totalSeconds = 90L * 24 * 60 * 60; // 7,776,000秒
        
        // 深度数据更新频率分析
        Map<String, Object> depthAnalysis = new HashMap<>();
        
        // 每100ms更新一次 = 每秒10次
        int updatesPerSecond = 10;
        long totalUpdates = totalSeconds * updatesPerSecond; // 77,760,000次更新
        
        // 3个档位（5档、10档、20档）
        int levels = 3;
        long totalDepthRecords = totalUpdates * levels; // 233,280,000条记录
        
        depthAnalysis.put("totalSeconds", totalSeconds);
        depthAnalysis.put("updatesPerSecond", updatesPerSecond);
        depthAnalysis.put("totalUpdates", totalUpdates);
        depthAnalysis.put("levels", levels);
        depthAnalysis.put("totalRecords", totalDepthRecords);
        
        // 存储大小计算
        int recordSizeBytes = 500; // 每条记录500字节
        long totalSizeBytes = totalDepthRecords * recordSizeBytes;
        BigDecimal totalSizeGB = BigDecimal.valueOf(totalSizeBytes)
                .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 3, RoundingMode.HALF_UP);
        
        depthAnalysis.put("recordSizeBytes", recordSizeBytes);
        depthAnalysis.put("totalSizeBytes", totalSizeBytes);
        depthAnalysis.put("totalSizeGB", totalSizeGB);
        
        // 合理性检查
        Map<String, Object> reasonabilityCheck = new HashMap<>();
        reasonabilityCheck.put("isReasonable", totalSizeGB.compareTo(BigDecimal.valueOf(200)) < 0);
        reasonabilityCheck.put("reason", "单个交易对90天深度数据超过100GB可能过高");
        reasonabilityCheck.put("suggestion", "考虑降低更新频率或减少档位数量");
        
        result.put("depthAnalysis", depthAnalysis);
        result.put("reasonabilityCheck", reasonabilityCheck);
        
        log.info("深度数据计算验证完成: 总记录数={}, 总大小={}GB", totalDepthRecords, totalSizeGB);
        
        return result;
    }

    /**
     * 验证交易数据计算
     */
    @GetMapping("/validate-trade")
    public Map<String, Object> validateTradeData() {
        log.info("开始验证交易数据计算");
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        
        // 90天的总秒数
        long totalSeconds = 90L * 24 * 60 * 60; // 7,776,000秒
        
        // 交易频率分析
        Map<String, Object> tradeAnalysis = new HashMap<>();
        
        // 假设平均每秒10笔交易
        int tradesPerSecond = 10;
        long totalTrades = totalSeconds * tradesPerSecond; // 77,760,000笔交易
        
        tradeAnalysis.put("totalSeconds", totalSeconds);
        tradeAnalysis.put("tradesPerSecond", tradesPerSecond);
        tradeAnalysis.put("totalTrades", totalTrades);
        
        // 存储大小计算
        int recordSizeBytes = 100; // 每条记录100字节
        long totalSizeBytes = totalTrades * recordSizeBytes;
        BigDecimal totalSizeGB = BigDecimal.valueOf(totalSizeBytes)
                .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 3, RoundingMode.HALF_UP);
        
        tradeAnalysis.put("recordSizeBytes", recordSizeBytes);
        tradeAnalysis.put("totalSizeBytes", totalSizeBytes);
        tradeAnalysis.put("totalSizeGB", totalSizeGB);
        
        // 合理性检查
        Map<String, Object> reasonabilityCheck = new HashMap<>();
        reasonabilityCheck.put("isReasonable", totalSizeGB.compareTo(BigDecimal.valueOf(20)) < 0);
        reasonabilityCheck.put("reason", "单个交易对90天交易数据约7GB是合理的");
        reasonabilityCheck.put("actualMarketData", "实际市场中，活跃交易对每秒可能有几十到几百笔交易");
        
        result.put("tradeAnalysis", tradeAnalysis);
        result.put("reasonabilityCheck", reasonabilityCheck);
        
        log.info("交易数据计算验证完成: 总记录数={}, 总大小={}GB", totalTrades, totalSizeGB);
        
        return result;
    }

    /**
     * 优化建议
     */
    @GetMapping("/optimization-suggestions")
    public Map<String, Object> getOptimizationSuggestions() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        
        Map<String, Object> suggestions = new HashMap<>();
        
        // K线数据优化
        Map<String, Object> klineOptimization = new HashMap<>();
        klineOptimization.put("compression", "使用InfluxDB可以实现10:1的压缩比");
        klineOptimization.put("retention", "建议保留30天原始数据，90天以上使用聚合数据");
        klineOptimization.put("storage", "K线数据相对较小，可以全部保存");
        suggestions.put("kline", klineOptimization);
        
        // 深度数据优化
        Map<String, Object> depthOptimization = new HashMap<>();
        depthOptimization.put("frequency", "考虑降低更新频率到每500ms一次");
        depthOptimization.put("levels", "只保存5档和20档，去掉10档");
        depthOptimization.put("retention", "只保留7天原始深度数据");
        depthOptimization.put("compression", "使用差分压缩，可以大幅减少存储空间");
        suggestions.put("depth", depthOptimization);
        
        // 交易数据优化
        Map<String, Object> tradeOptimization = new HashMap<>();
        tradeOptimization.put("aggregation", "可以按分钟聚合小额交易");
        tradeOptimization.put("retention", "保留30天详细交易数据");
        tradeOptimization.put("indexing", "按时间和交易对建立索引");
        suggestions.put("trade", tradeOptimization);
        
        // 总体建议
        Map<String, Object> overallSuggestions = new HashMap<>();
        overallSuggestions.put("database", "使用InfluxDB存储时序数据，MySQL存储元数据");
        overallSuggestions.put("caching", "Redis缓存最近1小时的热点数据");
        overallSuggestions.put("monitoring", "实时监控存储使用情况");
        overallSuggestions.put("backup", "定期备份重要数据");
        suggestions.put("overall", overallSuggestions);
        
        result.put("suggestions", suggestions);
        
        return result;
    }

    /**
     * 实际存储需求计算
     */
    @GetMapping("/realistic-calculation")
    public Map<String, Object> getRealisticCalculation(@RequestParam(defaultValue = "5") int symbolCount) {
        log.info("开始计算实际存储需求: symbolCount={}", symbolCount);
        
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("symbolCount", symbolCount);
        
        // 优化后的计算
        Map<String, Object> optimizedCalculation = new HashMap<>();
        
        // K线数据（保持不变）
        long klineRecords = 166950L * symbolCount;
        BigDecimal klineSize = BigDecimal.valueOf(klineRecords * 200)
                .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 3, RoundingMode.HALF_UP);
        
        // 深度数据（优化：每500ms更新，只保存2个档位，7天数据）
        long depthRecords = 7L * 24 * 60 * 60 * 2 * 2 * symbolCount; // 7天，每500ms，2档位
        BigDecimal depthSize = BigDecimal.valueOf(depthRecords * 500)
                .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 3, RoundingMode.HALF_UP);
        
        // 交易数据（30天详细数据）
        long tradeRecords = 30L * 24 * 60 * 60 * 10 * symbolCount; // 30天
        BigDecimal tradeSize = BigDecimal.valueOf(tradeRecords * 100)
                .divide(BigDecimal.valueOf(1024 * 1024 * 1024), 3, RoundingMode.HALF_UP);
        
        optimizedCalculation.put("kline", Map.of(
            "records", klineRecords,
            "sizeGB", klineSize,
            "description", "90天完整K线数据"
        ));
        
        optimizedCalculation.put("depth", Map.of(
            "records", depthRecords,
            "sizeGB", depthSize,
            "description", "7天深度数据（优化后）"
        ));
        
        optimizedCalculation.put("trade", Map.of(
            "records", tradeRecords,
            "sizeGB", tradeSize,
            "description", "30天交易数据"
        ));
        
        BigDecimal totalSize = klineSize.add(depthSize).add(tradeSize);
        optimizedCalculation.put("total", Map.of(
            "records", klineRecords + depthRecords + tradeRecords,
            "sizeGB", totalSize,
            "sizeTB", totalSize.divide(BigDecimal.valueOf(1024), 4, RoundingMode.HALF_UP)
        ));
        
        result.put("optimizedCalculation", optimizedCalculation);
        
        log.info("实际存储需求计算完成: 总大小={}GB", totalSize);
        
        return result;
    }
}
