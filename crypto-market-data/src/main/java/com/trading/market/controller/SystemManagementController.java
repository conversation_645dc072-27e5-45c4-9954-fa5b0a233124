package com.trading.market.controller;

import com.trading.common.circuit.CircuitBreakerManager;
import com.trading.common.recovery.ErrorRecoveryManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 系统管理控制器
 * 提供系统级别的运维管理接口
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/system")
public class SystemManagementController {
    
    private static final Logger log = LoggerFactory.getLogger(SystemManagementController.class);
    
    @Autowired(required = false)
    private CircuitBreakerManager circuitBreakerManager;
    
    @Autowired(required = false)
    private ErrorRecoveryManager errorRecoveryManager;
    
    /**
     * 获取所有熔断器状态
     */
    @GetMapping("/circuit-breakers")
    public ResponseEntity<Map<String, Object>> getCircuitBreakerStatus() {
        log.info("收到获取熔断器状态请求");
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            if (circuitBreakerManager != null) {
                result.put("status", "OK");
                result.put("count", circuitBreakerManager.getCircuitBreakerCount());
                result.put("details", circuitBreakerManager.getCircuitBreakerStatus());
                result.put("timestamp", System.currentTimeMillis());
            } else {
                result.put("status", "ERROR");
                result.put("message", "Circuit breaker manager not available");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取熔断器状态失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取熔断器状态失败: " + e.getMessage()));
        }
    }
    
    /**
     * 重置所有熔断器
     */
    @PostMapping("/circuit-breakers/reset")
    public ResponseEntity<Map<String, Object>> resetCircuitBreakers() {
        log.info("收到重置熔断器请求");
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            if (circuitBreakerManager != null) {
                circuitBreakerManager.resetAllCircuitBreakers();
                
                result.put("status", "SUCCESS");
                result.put("message", "所有熔断器已重置");
                result.put("timestamp", System.currentTimeMillis());
            } else {
                result.put("status", "ERROR");
                result.put("message", "Circuit breaker manager not available");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("重置熔断器失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "重置熔断器失败: " + e.getMessage()));
        }
    }
    
    /**
     * 重置特定熔断器
     */
    @PostMapping("/circuit-breakers/reset/{name}")
    public ResponseEntity<Map<String, Object>> resetCircuitBreaker(@PathVariable String name) {
        log.info("收到重置特定熔断器请求: {}", name);
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            if (circuitBreakerManager != null) {
                if (name.equals("redis-cache-write-kline:BTCUSDT:1h") || 
                    name.startsWith("redis-cache")) {
                    
                    // 针对特定熔断器的重置逻辑 - 先尝试移除再重新创建
                    circuitBreakerManager.removeCircuitBreaker(name);
                    
                    result.put("status", "SUCCESS");
                    result.put("message", "指定熔断器已重置: " + name);
                    result.put("timestamp", System.currentTimeMillis());
                } else {
                    result.put("status", "ERROR");
                    result.put("message", "熔断器不存在或无法单独重置: " + name);
                }
            } else {
                result.put("status", "ERROR");
                result.put("message", "Circuit breaker manager not available");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("重置特定熔断器失败: {}", name, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "重置特定熔断器失败: " + e.getMessage()));
        }
    }
    
    /**
     * 触发错误恢复流程
     */
    @PostMapping("/recovery/trigger")
    public ResponseEntity<Map<String, Object>> triggerErrorRecovery() {
        log.info("收到触发错误恢复请求");
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            if (errorRecoveryManager != null) {
                errorRecoveryManager.forceFullRecovery();
                
                result.put("status", "SUCCESS");
                result.put("message", "已触发全面错误恢复流程");
                result.put("timestamp", System.currentTimeMillis());
            } else {
                result.put("status", "ERROR");
                result.put("message", "Error recovery manager not available");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("触发错误恢复失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "触发错误恢复失败: " + e.getMessage()));
        }
    }
    
    /**
     * 获取错误恢复状态
     */
    @GetMapping("/recovery/status")
    public ResponseEntity<Map<String, Object>> getRecoveryStatus() {
        log.info("收到获取错误恢复状态请求");
        
        try {
            Map<String, Object> result = new HashMap<>();
            
            if (errorRecoveryManager != null) {
                result.put("status", "OK");
                result.put("details", errorRecoveryManager.getRecoveryStatus());
                result.put("timestamp", System.currentTimeMillis());
            } else {
                result.put("status", "ERROR");
                result.put("message", "Error recovery manager not available");
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            log.error("获取错误恢复状态失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("error", "获取错误恢复状态失败: " + e.getMessage()));
        }
    }
} 