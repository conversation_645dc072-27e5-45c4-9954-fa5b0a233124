package com.trading.market.controller;

import com.trading.market.service.MarketDataService;
import com.trading.market.storage.MySQLStorage;
import com.trading.common.dto.KlineData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Market API Controller
 * 提供历史数据查询的REST API接口
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@RestController
@RequestMapping("/api/market")
public class MarketController {
    
    private static final Logger log = LoggerFactory.getLogger(MarketController.class);
    
    private final MarketDataService marketDataService;

    @Autowired
    public MarketController(MarketDataService marketDataService) {
        this.marketDataService = marketDataService;
    }
    
    /**
     * 获取K线历史数据
     */
    @GetMapping("/kline/{symbol}/{timeframe}")
    public ResponseEntity<Map<String, Object>> getKlineData(
            @PathVariable String symbol,
            @PathVariable String timeframe,
            @RequestParam(defaultValue = "1000") int limit) {
        try {
            log.info("获取K线历史数据: symbol={}, timeframe={}, limit={}", symbol, timeframe, limit);

            // marketDataServiceを通じてデータを取得
            List<KlineData> klineData = marketDataService.getKlineData(symbol.toUpperCase(), timeframe, limit);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("symbol", symbol.toUpperCase());
            response.put("timeframe", timeframe);
            response.put("data", klineData);
            response.put("count", klineData.size());
            response.put("timestamp", System.currentTimeMillis());
            
            log.info("K线历史数据获取成功: symbol={}, timeframe={}, count={}", symbol, timeframe, klineData.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取K线历史数据失败: symbol={}, timeframe={}", symbol, timeframe, e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "error", e.getMessage()));
        }
    }
    
    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "UP");
        response.put("service", "market-api");
        response.put("timestamp", System.currentTimeMillis());
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取支持的交易对
     */
    @GetMapping("/symbols")
    public ResponseEntity<Map<String, Object>> getSymbols() {
        try {
            log.info("获取支持的交易对");
            
            List<String> symbols = List.of("BTCUSDT", "ETHUSDT", "BNBUSDT", "SOLUSDT", "DOTUSDT");
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("symbols", symbols);
            response.put("count", symbols.size());
            response.put("timestamp", System.currentTimeMillis());
            
            log.info("支持的交易对获取成功, count: {}", symbols.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            log.error("获取支持的交易对失败", e);
            return ResponseEntity.internalServerError()
                    .body(Map.of("success", false, "error", e.getMessage()));
        }
    }

    /**
     * 根据时间间隔字符串获取分钟数
     */
    private int getIntervalMinutes(String interval) {
        switch (interval.toLowerCase()) {
            case "1m": return 1;
            case "3m": return 3;
            case "5m": return 5;
            case "15m": return 15;
            case "30m": return 30;
            case "1h": return 60;
            case "2h": return 120;
            case "4h": return 240;
            case "6h": return 360;
            case "8h": return 480;
            case "12h": return 720;
            case "1d": return 1440;
            case "3d": return 4320;
            case "1w": return 10080;
            case "1M": return 43200; // 30天
            default: return 60; // 默认1小时
        }
    }

    @PostMapping("/e2e/kline/inject")
    public ResponseEntity<String> injectKlineDataForE2E(@RequestBody KlineData klineData) {
        log.info("Received E2E test kline data injection: {}", klineData);
        marketDataService.saveKlineDataAsync(klineData)
            .thenAccept(v -> log.info("E2E test data saved successfully for symbol: {}", klineData.getSymbol()))
            .exceptionally(ex -> {
                log.error("Failed to save E2E test data for symbol: {}", klineData.getSymbol(), ex);
                return null;
            });
        return ResponseEntity.ok("E2E Kline data injection accepted for symbol: " + klineData.getSymbol());
    }
}