package com.trading.market.startup;

import com.trading.common.config.MarketDataConfig;
import com.trading.market.service.MarketDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * 缓存预热启动器
 * 在应用启动完成后自动预热缓存数据
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Slf4j
@Component
public class CacheWarmupStartupRunner {

    @Autowired
    private MarketDataConfig marketDataConfig;

    @Autowired
    private MarketDataService marketDataService;

    /**
     * 应用启动完成后执行缓存预热
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("应用启动完成，开始执行智能缓存预热...");

        // 检查是否启用数据收集
        if (!marketDataConfig.getCollector().isEnabled()) {
            log.info("数据收集未启用，跳过缓存预热");
            return;
        }

        List<String> symbols = marketDataConfig.getCollector().getSymbols();
        if (symbols == null || symbols.isEmpty()) {
            log.warn("交易对列表为空，跳过缓存预热");
            return;
        }

        // 延迟60秒后开始预热，确保历史数据收集器已经开始工作
        CompletableFuture.delayedExecutor(60, TimeUnit.SECONDS).execute(() -> {
            try {
                log.info("开始预热缓存数据，交易对数量: {}", symbols.size());
                
                // 调用MarketDataService的预热方法
//                CompletableFuture<Void> warmupFuture = marketDataService.preloadCacheData(symbols);
                
                // 异步等待预热完成
//                warmupFuture.whenComplete((result, throwable) -> {
//                    if (throwable != null) {
//                        log.error("缓存预热失败", throwable);
//                    } else {
//                        log.info("缓存预热完成，已预热 {} 个交易对的数据", symbols.size());
//                    }
//                });
                
            } catch (Exception e) {
                log.error("启动缓存预热失败", e);
            }
        });
    }

    /**
     * 手动触发缓存预热
     * 可用于配置更新后的重新预热
     */
    public CompletableFuture<Void> triggerWarmup() {
        List<String> symbols = marketDataConfig.getCollector().getSymbols();
        if (symbols == null || symbols.isEmpty()) {
            log.warn("交易对列表为空，无法执行预热");
            return CompletableFuture.completedFuture(null);
        }

        log.info("手动触发缓存预热，交易对数量: {}", symbols.size());
        return marketDataService.preloadCacheData(symbols);
    }

    /**
     * 预热指定的交易对
     */
    public CompletableFuture<Void> warmupSymbols(List<String> symbols) {
        if (symbols == null || symbols.isEmpty()) {
            log.warn("交易对列表为空，无法执行预热");
            return CompletableFuture.completedFuture(null);
        }

        log.info("预热指定交易对，数量: {}", symbols.size());
        return marketDataService.preloadCacheData(symbols);
    }
}
