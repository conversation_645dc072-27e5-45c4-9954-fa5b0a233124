package com.trading.market.utils;

import com.trading.market.storage.exception.StorageException;
import org.slf4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.dao.DuplicateKeyException;

import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Supplier;

/**
 * 统一存储异常处理工具类
 * 减少重复代码，提供一致的异常处理策略
 */
public class StorageExceptionHandler {
    
    /**
     * 执行存储操作并处理异常
     * 
     * @param operation 存储操作
     * @param operationName 操作名称（用于日志）
     * @param entityInfo 实体信息（用于日志）
     * @param logger 日志记录器
     * @param errorCount 错误计数器
     * @param duplicateCount 重复计数器
     * @param <T> 返回类型
     * @return 操作结果
     * @throws StorageException 存储异常
     */
    public static <T> T executeWithExceptionHandling(
            Supplier<T> operation,
            String operationName,
            String entityInfo,
            Logger logger,
            AtomicLong errorCount,
            AtomicLong duplicateCount) throws StorageException {
        
        try {
            return operation.get();
            
        } catch (DuplicateKeyException e) {
            // 重复键异常，数据已存在，记录调试信息但不抛出异常
            if (duplicateCount != null) {
                duplicateCount.incrementAndGet();
            }
            logger.debug("{}数据已存在（通过唯一键约束捕获），跳过保存: {}", operationName, entityInfo);
            return null; // 或者返回适当的默认值
            
        } catch (DataAccessException e) {
            if (errorCount != null) {
                errorCount.incrementAndGet();
            }
            logger.error("{}到MySQL失败: {}", operationName, entityInfo, e);
            throw new StorageException("MySQL" + operationName + "失败", e);
            
        } catch (Exception e) {
            if (errorCount != null) {
                errorCount.incrementAndGet();
            }
            logger.error("{}时发生未知错误: {}", operationName, entityInfo, e);
            throw new StorageException("MySQL" + operationName + "失败", e);
        }
    }
    
    /**
     * 执行存储操作并处理异常（无返回值版本）
     */
    public static void executeWithExceptionHandling(
            Runnable operation,
            String operationName,
            String entityInfo,
            Logger logger,
            AtomicLong errorCount,
            AtomicLong duplicateCount) throws StorageException {
        
        executeWithExceptionHandling(
            () -> {
                operation.run();
                return null;
            },
            operationName,
            entityInfo,
            logger,
            errorCount,
            duplicateCount
        );
    }
    
    /**
     * 执行批量存储操作并处理异常
     */
    public static <T> T executeBatchWithExceptionHandling(
            Supplier<T> operation,
            String operationName,
            int batchSize,
            Logger logger,
            AtomicLong errorCount) throws RuntimeException {
        
        try {
            return operation.get();
            
        } catch (DataAccessException e) {
            if (errorCount != null) {
                errorCount.incrementAndGet();
            }
            logger.error("批量{}到MySQL失败: 数据量={}", operationName, batchSize, e);
            throw new RuntimeException("MySQL批量" + operationName + "失败", e);
            
        } catch (Exception e) {
            if (errorCount != null) {
                errorCount.incrementAndGet();
            }
            logger.error("批量{}到MySQL时发生未知错误: 数据量={}", operationName, batchSize, e);
            throw new RuntimeException("MySQL批量" + operationName + "失败", e);
        }
    }
    
    /**
     * 执行查询操作并处理异常
     */
    public static <T> T executeQueryWithExceptionHandling(
            Supplier<T> operation,
            String operationName,
            String queryInfo,
            Logger logger) throws RuntimeException {
        
        try {
            return operation.get();
            
        } catch (Exception e) {
            logger.error("{}失败: {}", operationName, queryInfo, e);
            throw new RuntimeException("MySQL" + operationName + "失败", e);
        }
    }
}
