package com.trading.market.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStandaloneConfiguration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

/**
 * 市场数据模块专用的Redis配置。
 *
 * <p>这个配置类解决了之前因不明确的自动配置和被删除的配置类导致的Redis连接静默失败问题。
 * 通过显式定义{@link RedisConnectionFactory}和{@link RedisTemplate}，我们确保了Redis连接的稳定性和可预测性，
 * 彻底消除了对可能发生冲突或失败的自动配置的依赖。</p>
 *
 * <p>关键功能:</p>
 * <ul>
 *   <li><b>显式Bean定义:</b> 强制创建Spring上下文所需的Redis相关Bean，避免静默失败。</li>
 *   <li><b>健壮的序列化:</b> 使用 {@link StringRedisSerializer} 作为键，保证了键在Redis中的可读性；
 *       使用支持Java 8时间类型的 {@link GenericJackson2JsonRedisSerializer} 作为值，
 *       允许将复杂的Java对象以JSON格式存储。</li>
 * </ul>
 */
@Configuration
public class MarketDataRedisConfig {

    /**
     * 显式创建并配置一个 {@link LettuceConnectionFactory} 实例。
     * <p>
     * 此Bean定义是解决Redis静默失败问题的核心。它移除了对Spring Boot自动配置的隐式依赖，
     * 转而使用我们自定义的 {@link EnhancedRedisProperties} 来显式地、确定性地配置连接。
     * 任何在配置属性（如主机、端口、密码）上的问题都将在此Bean的创建阶段立即以失败告终，
     * 从而使问题在应用启动时就暴露出来，而不是在运行时以难以追踪的方式出现。
     *
     * @param redisProperties 我们自定义的、经过验证的类型安全配置属性。
     * @return 一个基于Lettuce的、完全配置好的 {@link RedisConnectionFactory} 实例。
     */
    @Bean
    public LettuceConnectionFactory redisConnectionFactory(EnhancedRedisProperties redisProperties) {
        RedisStandaloneConfiguration config = new RedisStandaloneConfiguration();
        config.setHostName(redisProperties.getHost());
        config.setPort(redisProperties.getPort());
        if (redisProperties.getPassword() != null && !redisProperties.getPassword().isEmpty()) {
            config.setPassword(redisProperties.getPassword());
        }
        config.setDatabase(redisProperties.getDatabase());
        return new LettuceConnectionFactory(config);
    }

    /**
     * 创建并配置一个定制的 {@link RedisTemplate} 实例。
     *
     * <p>此Bean依赖于我们上面显式定义的 {@link RedisConnectionFactory}。
     * 它为Redis操作提供了一个高级抽象，并配置了最佳实践的序列化器。</p>
     *
     * @param redisConnectionFactory 我们在上面显式定义的Redis连接工厂。
     * @return 一个完全配置好的 {@link RedisTemplate<String, Object>} 实例。
     */
    @Bean
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory redisConnectionFactory) {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);

        // 为JSON序列化器配置一个支持Java 8时间API (JSR-310) 的ObjectMapper
        ObjectMapper objectMapper = new ObjectMapper();
        objectMapper.registerModule(new JavaTimeModule());
        
        // 创建一个通用的Jackson2 JSON序列化器
        GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer(objectMapper);
        
        // 为键和哈希键设置字符串序列化器
        StringRedisSerializer stringSerializer = new StringRedisSerializer();

        // 应用序列化器
        template.setKeySerializer(stringSerializer);
        template.setHashKeySerializer(stringSerializer);
        template.setValueSerializer(jsonSerializer);
        template.setHashValueSerializer(jsonSerializer);

        // 初始化模板
        template.afterPropertiesSet();
        return template;
    }
}
