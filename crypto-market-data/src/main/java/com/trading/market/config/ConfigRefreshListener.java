package com.trading.market.config;

import com.trading.common.config.MarketDataConfig;
import com.trading.market.startup.CacheWarmupStartupRunner;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * 配置刷新监听器
 * 监听配置变更事件，自动触发相关操作
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class ConfigRefreshListener {

    private static final Logger log = LoggerFactory.getLogger(ConfigRefreshListener.class);

    @Autowired
    private MarketDataConfig marketDataConfig;

    @Autowired
    private CacheWarmupStartupRunner cacheWarmupRunner;

    /**
     * 监听环境变更事件
     * 当配置刷新时自动触发
     */
    @EventListener
    public void handleEnvironmentChange(EnvironmentChangeEvent event) {
        Set<String> changedKeys = event.getKeys();
        log.info("检测到配置变更，变更的配置项: {}", changedKeys);

        // 检查是否有交易对相关的配置变更
        boolean symbolsChanged = changedKeys.stream()
                .anyMatch(key -> key.contains("trading.market-data.collector.symbols"));

        boolean collectorConfigChanged = changedKeys.stream()
                .anyMatch(key -> key.contains("trading.market-data.collector"));

        if (symbolsChanged) {
            log.info("检测到交易对配置变更，将触发缓存预热");
            handleSymbolsConfigChange();
        } else if (collectorConfigChanged) {
            log.info("检测到收集器配置变更，将重新评估缓存策略");
            handleCollectorConfigChange();
        }
    }

    /**
     * 处理交易对配置变更
     */
    private void handleSymbolsConfigChange() {
        try {
            List<String> currentSymbols = marketDataConfig.getCollector().getSymbols();
            if (currentSymbols != null && !currentSymbols.isEmpty()) {
                log.info("交易对配置已更新，当前交易对数量: {}", currentSymbols.size());
                log.debug("当前交易对列表: {}", currentSymbols);

                // 异步触发缓存预热
                CompletableFuture<Void> warmupFuture = cacheWarmupRunner.warmupSymbols(currentSymbols);
                
                warmupFuture.whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("配置变更后的缓存预热失败", throwable);
                    } else {
                        log.info("配置变更后的缓存预热完成，已预热 {} 个交易对", currentSymbols.size());
                    }
                });
            } else {
                log.warn("交易对配置为空，跳过缓存预热");
            }
        } catch (Exception e) {
            log.error("处理交易对配置变更失败", e);
        }
    }

    /**
     * 处理收集器配置变更
     */
    private void handleCollectorConfigChange() {
        try {
            boolean enabled = marketDataConfig.getCollector().isEnabled();
            int defaultDays = marketDataConfig.getCollector().getHistorical().getDefaultDays();
            
            log.info("收集器配置已更新 - 启用状态: {}, 默认天数: {}", enabled, defaultDays);
            
            if (enabled) {
                // 如果收集器被启用，触发一次完整的预热
                CompletableFuture<Void> warmupFuture = cacheWarmupRunner.triggerWarmup();
                
                warmupFuture.whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.error("收集器配置变更后的缓存预热失败", throwable);
                    } else {
                        log.info("收集器配置变更后的缓存预热完成");
                    }
                });
            } else {
                log.info("收集器已禁用，跳过缓存预热");
            }
        } catch (Exception e) {
            log.error("处理收集器配置变更失败", e);
        }
    }
}
