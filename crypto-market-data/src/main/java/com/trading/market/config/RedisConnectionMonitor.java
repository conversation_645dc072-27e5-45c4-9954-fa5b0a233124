package com.trading.market.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Redis连接监控器
 * 监控Redis连接状态，提供自动恢复机制
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class RedisConnectionMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(RedisConnectionMonitor.class);
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private RedisConnectionFactory redisConnectionFactory;
    
    private final AtomicBoolean isHealthy = new AtomicBoolean(true);
    private final AtomicLong lastHealthCheckTime = new AtomicLong(System.currentTimeMillis());
    private final AtomicLong consecutiveFailures = new AtomicLong(0);
    
    private static final long HEALTH_CHECK_INTERVAL = 120000; // 2分钟，减少CPU消耗
    private static final long MAX_CONSECUTIVE_FAILURES = 3;
    
    /**
     * 定期健康检查
     */
    @Scheduled(fixedDelay = HEALTH_CHECK_INTERVAL)
    public void performHealthCheck() {
        try {
            boolean currentHealth = checkRedisConnection();
            
            if (currentHealth) {
                if (!isHealthy.get()) {
                    log.info("Redis连接已恢复正常");
                    isHealthy.set(true);
                    consecutiveFailures.set(0);
                }
            } else {
                long failures = consecutiveFailures.incrementAndGet();
                log.warn("Redis连接检查失败，连续失败次数: {}", failures);
                
                if (failures >= MAX_CONSECUTIVE_FAILURES) {
                    if (isHealthy.get()) {
                        log.error("Redis连接持续失败，标记为不健康状态");
                        isHealthy.set(false);
                    }
                    
                    // 尝试重新初始化连接
                    attemptConnectionRecovery();
                }
            }
            
            lastHealthCheckTime.set(System.currentTimeMillis());
            
        } catch (Exception e) {
            log.error("Redis健康检查过程中发生异常", e);
        }
    }
    
    /**
     * 检查Redis连接状态
     */
    private boolean checkRedisConnection() {
        try {
            // 检查连接工厂状态
            if (redisConnectionFactory instanceof LettuceConnectionFactory) {
                LettuceConnectionFactory lettuceFactory = (LettuceConnectionFactory) redisConnectionFactory;
                
                // 检查连接工厂是否已停止
                try {
                    lettuceFactory.getConnection().ping();
                    return true;
                } catch (IllegalStateException e) {
                    if (e.getMessage() != null && e.getMessage().contains("STOPPING")) {
                        log.error("检测到LettuceConnectionFactory处于STOPPING状态");
                        return false;
                    }
                    throw e;
                }
            }
            
            // 通用连接检查
            redisTemplate.getConnectionFactory().getConnection().ping();
            return true;
            
        } catch (Exception e) {
            log.debug("Redis连接检查失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 尝试连接恢复
     */
    private void attemptConnectionRecovery() {
        try {
            log.info("尝试恢复Redis连接...");
            
            if (redisConnectionFactory instanceof LettuceConnectionFactory) {
                LettuceConnectionFactory lettuceFactory = (LettuceConnectionFactory) redisConnectionFactory;
                
                // 重新初始化连接工厂
                lettuceFactory.resetConnection();
                lettuceFactory.afterPropertiesSet();
                
                log.info("Redis连接工厂已重新初始化");
                
                // 验证恢复结果
                if (checkRedisConnection()) {
                    log.info("Redis连接恢复成功");
                    isHealthy.set(true);
                    consecutiveFailures.set(0);
                } else {
                    log.warn("Redis连接恢复失败");
                }
            }
            
        } catch (Exception e) {
            log.error("Redis连接恢复过程中发生异常", e);
        }
    }
    
    /**
     * 获取连接健康状态
     */
    public boolean isHealthy() {
        return isHealthy.get();
    }
    
    /**
     * 获取连续失败次数
     */
    public long getConsecutiveFailures() {
        return consecutiveFailures.get();
    }
    
    /**
     * 获取最后检查时间
     */
    public long getLastHealthCheckTime() {
        return lastHealthCheckTime.get();
    }
    
    /**
     * 手动触发健康检查
     */
    public boolean manualHealthCheck() {
        boolean result = checkRedisConnection();
        log.info("手动Redis健康检查结果: {}", result ? "健康" : "不健康");
        return result;
    }
    
    /**
     * 强制重置连接状态
     */
    public void resetConnectionState() {
        log.info("重置Redis连接状态");
        isHealthy.set(true);
        consecutiveFailures.set(0);
        lastHealthCheckTime.set(System.currentTimeMillis());
    }
}
