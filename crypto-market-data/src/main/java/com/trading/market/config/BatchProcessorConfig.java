package com.trading.market.config;

import com.trading.common.batch.UltraFastBatchProcessor;
import com.trading.common.dto.KlineData;
import com.trading.market.service.MarketDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.List;
import java.util.function.Consumer;

/**
 * 批处理器配置类
 * 用于配置超高性能批处理器
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Configuration
public class BatchProcessorConfig {
    
    private static final Logger log = LoggerFactory.getLogger(BatchProcessorConfig.class);
    
    @Autowired
    private MarketDataService marketDataService;
    
    /**
     * 配置K线数据批处理器
     */
    @Bean("klineDataBatchProcessor")
    @Primary
    public UltraFastBatchProcessor<KlineData> klineDataBatchProcessor() {
        log.info("创建K线数据批处理器...");
        
        // 定义批处理逻辑
        Consumer<List<KlineData>> batchProcessor = klineDataList -> {
            try {
                if (klineDataList != null && !klineDataList.isEmpty()) {
                    // 批量保存K线数据（异步）
                    marketDataService.batchSaveKlineDataAsync(klineDataList).join();
                    log.debug("批量保存K线数据: {} 条", klineDataList.size());
                }
            } catch (Exception e) {
                log.error("批量保存K线数据失败: size={}",
                    klineDataList != null ? klineDataList.size() : 0, e);
            }
        };
        
        // 创建批处理器，使用用户要求的1500批次大小
        UltraFastBatchProcessor<KlineData> processor = new UltraFastBatchProcessor<>(
            batchProcessor,
            1500,  // 批次大小 - 用户要求的1500
            100,   // 刷新间隔ms
            7500,  // 最大队列大小 - 相应调整
            4      // 工作线程数 - 相应调整
        );
        
        log.info("K线数据批处理器创建完成");
        return processor;
    }
    
    /**
     * 配置通用数据批处理器
     */
    @Bean("genericBatchProcessor")
    public UltraFastBatchProcessor<Object> genericBatchProcessor() {
        log.info("创建通用数据批处理器...");
        
        // 定义通用批处理逻辑
        Consumer<List<Object>> batchProcessor = dataList -> {
            try {
                if (dataList != null && !dataList.isEmpty()) {
                    log.debug("处理通用数据批次: {} 条", dataList.size());
                    // 这里可以根据数据类型进行不同的处理
                    for (Object data : dataList) {
                        if (data instanceof KlineData) {
                            // 处理K线数据（异步）
                            marketDataService.saveKlineDataAsync((KlineData) data).join();
                        }
                        // 可以添加其他数据类型的处理逻辑
                    }
                }
            } catch (Exception e) {
                log.error("批量处理通用数据失败: size={}", 
                    dataList != null ? dataList.size() : 0, e);
            }
        };
        
        // 创建批处理器，使用优化的配置
        UltraFastBatchProcessor<Object> processor = new UltraFastBatchProcessor<>(
            batchProcessor,
            750,   // 批次大小 - 相应调整
            150,   // 刷新间隔ms - 优化的刷新频率
            3750,  // 最大队列大小 - 相应调整
            3      // 工作线程数 - 相应调整
        );
        
        log.info("通用数据批处理器创建完成");
        return processor;
    }
}
