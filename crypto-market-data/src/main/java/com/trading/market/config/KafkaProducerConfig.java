package com.trading.market.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.core.DefaultKafkaProducerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.HashMap;
import java.util.Map;

/**
 * Kafka生产者配置
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Configuration
@ConditionalOnProperty(prefix = "trading.market-data.kafka", name = "enabled", havingValue = "true", matchIfMissing = true)
public class KafkaProducerConfig {

    private static final Logger log = LoggerFactory.getLogger(KafkaProducerConfig.class);

    @Value("${spring.kafka.bootstrap-servers}")
    private String bootstrapServers;

    @Value("${spring.kafka.producer.batch-size}")
    private int batchSize;

    @Value("${spring.kafka.producer.linger-ms}")
    private int lingerMs;

    @Value("${spring.kafka.producer.buffer-memory}")
    private long bufferMemory;

    @Value("${spring.kafka.producer.compression-type}")
    private String compressionType;

    @Value("${spring.kafka.producer.retries}")
    private int retries;

        @Value("${spring.kafka.producer.acknowledgments}")
    private String acks;


    @Bean
    public ProducerFactory<String, String> producerFactory() {
        Map<String, Object> configProps = new HashMap<>();
        
        // 基础配置
        configProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
        configProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        configProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class);
        
        // 性能优化配置
        configProps.put(ProducerConfig.BATCH_SIZE_CONFIG, batchSize);
        configProps.put(ProducerConfig.LINGER_MS_CONFIG, lingerMs);
        configProps.put(ProducerConfig.BUFFER_MEMORY_CONFIG, bufferMemory);
        configProps.put(ProducerConfig.COMPRESSION_TYPE_CONFIG, compressionType);
        configProps.put(ProducerConfig.RETRIES_CONFIG, retries);
        configProps.put(ProducerConfig.ACKS_CONFIG, acks);
        
        // 可靠性配置 - 优化超时和重试设置
        configProps.put(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG, true);
        configProps.put(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION, 3); // 减少并发请求
        configProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 15000); // 减少请求超时
        configProps.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, 60000); // 减少交付超时

        // 连接配置 - 增加容错性
        configProps.put(ProducerConfig.CONNECTIONS_MAX_IDLE_MS_CONFIG, 300000); // 5分钟
        configProps.put(ProducerConfig.RECONNECT_BACKOFF_MS_CONFIG, 5000); // 重连退避时间
        configProps.put(ProducerConfig.RECONNECT_BACKOFF_MAX_MS_CONFIG, 30000); // 最大重连退避时间
        
        log.info("Kafka生产者配置初始化完成: bootstrapServers={}", bootstrapServers);
        
        return new DefaultKafkaProducerFactory<>(configProps);
    }

    @Bean
    public KafkaTemplate<String, String> kafkaTemplate() {
        KafkaTemplate<String, String> template = new KafkaTemplate<>(producerFactory());

        // 设置默认topic
        template.setDefaultTopic("market-data-default");

        log.info("KafkaTemplate初始化完成");
        return template;
    }
}
