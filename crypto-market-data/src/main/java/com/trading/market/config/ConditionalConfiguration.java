package com.trading.market.config;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;
import org.springframework.kafka.annotation.EnableKafka;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 条件性配置类
 * 用于处理基于配置属性的条件性功能启用
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class ConditionalConfiguration {

    private static final Logger log = LoggerFactory.getLogger(ConditionalConfiguration.class);

    /**
     * Kafka配置 - 条件性启用
     */
    @Configuration
    @ConditionalOnProperty(name = "trading.market-data.kafka.enabled", havingValue = "true", matchIfMissing = false)
    @EnableKafka
    public static class KafkaConfiguration {
        
        public KafkaConfiguration() {
            log.info("Kafka配置已启用");
        }
    }

    /**
     * 调度配置 - 条件性启用
     */
    @Configuration
    @ConditionalOnProperty(name = "trading.market-data.scheduling.enabled", havingValue = "true", matchIfMissing = false)
    @EnableScheduling
    public static class SchedulingConfiguration {
        
        public SchedulingConfiguration() {
            log.info("调度配置已启用");
        }
    }
}
