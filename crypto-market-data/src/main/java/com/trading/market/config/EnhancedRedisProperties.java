package com.trading.market.config;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Positive;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

/**
 * 为市场数据模块提供类型安全且经过验证的Redis配置属性。
 * <p>
 * 通过 {@link ConfigurationProperties} 注解，此类从Spring环境中绑定前缀为
 * {@code spring.data.redis} 的属性。它利用了Jakarta Bean Validation注解
 * (如 {@link NotBlank}, {@link Positive}) 来确保关键配置属性
 * (如主机、端口) 在应用启动时是有效且存在的。
 * <p>
 * 这种方法相比于直接在配置类中注入原始属性值，提供了更强的类型安全性和更早的失败检测。
 * 如果任何验证规则未被满足，应用程序上下文将无法加载，从而防止了因配置错误导致的运行时问题。
 */
@ConfigurationProperties(prefix = "spring.data.redis")
@Data
@Validated
public class EnhancedRedisProperties {

    /**
     * Redis服务器主机名。
     * 不能为空白。
     */
    @NotBlank(message = "Redis host must not be blank")
    private String host = "localhost";

    /**
     * Redis服务器端口。
     * 必须为正数。
     */
    @Positive(message = "Redis port must be a positive number")
    private int port = 6379;

    /**
     * 连接到Redis服务器的密码。
     * 默认为null，表示没有密码。
     */
    private String password;

    /**
     * 使用的Redis数据库索引。
     * 默认为0。
     */
    private int database = 0;

}
