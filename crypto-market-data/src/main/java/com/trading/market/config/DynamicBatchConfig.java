package com.trading.market.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 动态批处理配置管理器
 * 根据系统负载和数据类型动态调整批处理大小
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "performance.adaptive-batch")
public class DynamicBatchConfig {
    
    // 基础配置
    private boolean enabled = true;
    private int baseBatchSize = 500;
    private int minBatchSize = 100;
    private int maxBatchSize = 2000;
    
    // 数据类型特定配置
    private KlineConfig kline = new KlineConfig();
    private TradeConfig trade = new TradeConfig();
    private DepthConfig depth = new DepthConfig();
    
    // 性能监控
    private final AtomicLong totalProcessed = new AtomicLong(0);
    private final AtomicLong totalErrors = new AtomicLong(0);
    private final AtomicInteger currentLoad = new AtomicInteger(0);
    
    // 动态调整参数
    private volatile long lastAdjustmentTime = System.currentTimeMillis();
    private volatile double systemLoadThreshold = 0.8;
    private volatile long adjustmentIntervalMs = 30000; // 30秒调整一次
    
    @PostConstruct
    public void initialize() {
        log.info("动态批处理配置管理器初始化完成");
        log.info("基础批处理大小: {}, 范围: [{}, {}]", baseBatchSize, minBatchSize, maxBatchSize);
        log.info("K线批处理: {}, 交易批处理: {}, 深度批处理: {}", 
                kline.getOptimalSize(), trade.getOptimalSize(), depth.getOptimalSize());
    }
    
    /**
     * 获取K线数据的动态批处理大小
     */
    public int getKlineBatchSize(String interval) {
        if (!enabled) {
            return kline.getBaseSize();
        }
        
        // 根据时间间隔调整
        int intervalMultiplier = getIntervalMultiplier(interval);
        int adjustedSize = kline.getBaseSize() * intervalMultiplier;
        
        // 根据系统负载调整
        adjustedSize = adjustForSystemLoad(adjustedSize);
        
        return Math.max(minBatchSize, Math.min(maxBatchSize, adjustedSize));
    }
    
    /**
     * 获取交易数据的动态批处理大小
     */
    public int getTradeBatchSize(String symbol) {
        if (!enabled) {
            return trade.getBaseSize();
        }
        
        // 根据交易对活跃度调整
        int activityMultiplier = getSymbolActivityMultiplier(symbol);
        int adjustedSize = trade.getBaseSize() * activityMultiplier;
        
        // 根据系统负载调整
        adjustedSize = adjustForSystemLoad(adjustedSize);
        
        return Math.max(minBatchSize, Math.min(maxBatchSize, adjustedSize));
    }
    
    /**
     * 获取深度数据的动态批处理大小
     */
    public int getDepthBatchSize(int levels) {
        if (!enabled) {
            return depth.getBaseSize();
        }
        
        // 根据深度级别调整
        int levelMultiplier = getLevelMultiplier(levels);
        int adjustedSize = depth.getBaseSize() * levelMultiplier;
        
        // 根据系统负载调整
        adjustedSize = adjustForSystemLoad(adjustedSize);
        
        return Math.max(minBatchSize, Math.min(maxBatchSize, adjustedSize));
    }
    
    /**
     * 根据系统负载调整批处理大小
     */
    private int adjustForSystemLoad(int baseSize) {
        double currentLoadRatio = getCurrentSystemLoad();
        
        if (currentLoadRatio > systemLoadThreshold) {
            // 高负载时减少批处理大小
            return (int) (baseSize * (1.0 - (currentLoadRatio - systemLoadThreshold) * 0.5));
        } else if (currentLoadRatio < 0.5) {
            // 低负载时增加批处理大小
            return (int) (baseSize * 1.2);
        }
        
        return baseSize;
    }
    
    /**
     * 获取当前系统负载
     */
    private double getCurrentSystemLoad() {
        // 简化的负载计算，实际可以结合JVM指标、数据库连接池等
        long errorRate = totalErrors.get() * 100 / Math.max(1, totalProcessed.get());
        return Math.min(1.0, errorRate / 10.0 + currentLoad.get() / 100.0);
    }
    
    /**
     * 根据时间间隔获取倍数
     */
    private int getIntervalMultiplier(String interval) {
        switch (interval.toLowerCase()) {
            case "1m": return 1;
            case "3m": return 1;
            case "5m": return 2;
            case "15m": return 3;
            case "30m": return 4;
            case "1h": return 5;
            case "4h": return 6;
            case "1d": return 8;
            default: return 1;
        }
    }
    
    /**
     * 根据交易对活跃度获取倍数
     */
    private int getSymbolActivityMultiplier(String symbol) {
        // 主流交易对使用更大的批处理
        switch (symbol.toUpperCase()) {
            case "BTCUSDT":
            case "ETHUSDT":
                return 2;
            case "BNBUSDT":
            case "SOLUSDT":
                return 1;
            default:
                return 1;
        }
    }
    
    /**
     * 根据深度级别获取倍数
     */
    private int getLevelMultiplier(int levels) {
        if (levels <= 5) return 1;
        if (levels <= 10) return 1;
        if (levels <= 20) return 2;
        return 3;
    }
    
    /**
     * 更新性能统计
     */
    public void updateStats(long processed, long errors) {
        totalProcessed.addAndGet(processed);
        totalErrors.addAndGet(errors);
        
        // 定期调整配置
        long now = System.currentTimeMillis();
        if (now - lastAdjustmentTime > adjustmentIntervalMs) {
            adjustConfiguration();
            lastAdjustmentTime = now;
        }
    }
    
    /**
     * 动态调整配置
     */
    private void adjustConfiguration() {
        double errorRate = totalErrors.get() * 100.0 / Math.max(1, totalProcessed.get());
        
        if (errorRate > 5.0) {
            // 错误率过高，减少批处理大小
            kline.adjustSize(-50);
            trade.adjustSize(-50);
            depth.adjustSize(-25);
            log.warn("检测到高错误率 {:.2f}%，减少批处理大小", errorRate);
        } else if (errorRate < 1.0) {
            // 错误率很低，可以增加批处理大小
            kline.adjustSize(25);
            trade.adjustSize(25);
            depth.adjustSize(10);
            log.info("系统运行良好，错误率 {:.2f}%，增加批处理大小", errorRate);
        }
    }
    
    /**
     * 获取当前配置状态
     */
    public String getConfigStatus() {
        return String.format(
            "DynamicBatch[enabled=%s, kline=%d, trade=%d, depth=%d, load=%.2f, errors=%d/%d]",
            enabled, kline.getOptimalSize(), trade.getOptimalSize(), depth.getOptimalSize(),
            getCurrentSystemLoad(), totalErrors.get(), totalProcessed.get()
        );
    }
    
    @Data
    public static class KlineConfig {
        private int baseSize = 200;
        private int minSize = 50;
        private int maxSize = 800;
        private volatile int currentOptimal = 200;
        
        public int getOptimalSize() {
            return currentOptimal;
        }
        
        public void adjustSize(int delta) {
            currentOptimal = Math.max(minSize, Math.min(maxSize, currentOptimal + delta));
        }
    }
    
    @Data
    public static class TradeConfig {
        private int baseSize = 500;
        private int minSize = 100;
        private int maxSize = 1000;
        private volatile int currentOptimal = 500;
        
        public int getOptimalSize() {
            return currentOptimal;
        }
        
        public void adjustSize(int delta) {
            currentOptimal = Math.max(minSize, Math.min(maxSize, currentOptimal + delta));
        }
    }
    
    @Data
    public static class DepthConfig {
        private int baseSize = 200;
        private int minSize = 50;
        private int maxSize = 500;
        private volatile int currentOptimal = 200;
        
        public int getOptimalSize() {
            return currentOptimal;
        }
        
        public void adjustSize(int delta) {
            currentOptimal = Math.max(minSize, Math.min(maxSize, currentOptimal + delta));
        }
    }
}
