package com.trading.market;

import org.springframework.context.annotation.FilterType;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.event.ApplicationFailedEvent;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.boot.web.servlet.ServletComponentScan;
import com.trading.common.config.InfluxDBConfig;

import com.trading.market.config.ConditionalConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 市场数据模块主应用类
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
import org.springframework.boot.actuate.autoconfigure.influx.InfluxDbHealthContributorAutoConfiguration;

@SpringBootApplication(exclude = { InfluxDbHealthContributorAutoConfiguration.class })
@EnableConfigurationProperties({
    com.trading.common.config.MarketDataConfig.class,
    com.trading.market.config.EnhancedRedisProperties.class,
    })
@EnableScheduling
@EnableAsync
@ComponentScan(basePackages = {
    "com.trading.market",
    "com.trading.sdk",
    "com.trading.common"
},
excludeFilters = @ComponentScan.Filter(type = FilterType.ASSIGNABLE_TYPE, classes = {}))
@ServletComponentScan
@Import({InfluxDBConfig.class, ConditionalConfiguration.class})
@MapperScan("com.trading.market.mapper")
public class MarketDataApplication {

    private static final Logger log = LoggerFactory.getLogger(MarketDataApplication.class);

    /**
     * 应用健康检查Bean
     */
    @Bean
    public ApplicationHealthChecker applicationHealthChecker() {
        return new ApplicationHealthChecker();
    }

    public static void main(String[] args) {
        try {
            SpringApplication app = new SpringApplication(MarketDataApplication.class);
            app.addListeners((ApplicationFailedEvent event) -> {
                log.error("!!! 应用启动失败 !!!", event.getException());
            });
            app.run(args);
            log.info("市场数据应用成功启动。");
        } catch (Exception e) {
            log.error("### 应用启动时捕获到未处理的致命异常 ###", e);
        }
    }

    /**
     * 应用健康检查器
     */
    public static class ApplicationHealthChecker {
        private final Logger log = LoggerFactory.getLogger(ApplicationHealthChecker.class);

        public ApplicationHealthChecker() {
            log.info("ApplicationHealthChecker 初始化完成");
        }

        public boolean isHealthy() {
            return true;
        }
    }
}
