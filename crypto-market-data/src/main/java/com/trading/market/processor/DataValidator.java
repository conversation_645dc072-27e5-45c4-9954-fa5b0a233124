package com.trading.market.processor;

import com.trading.common.config.MarketDataConfig;
import com.trading.common.dto.KlineData;
import com.trading.common.dto.TradeData;
import com.trading.common.dto.DepthData;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 数据验证器
 * 负责验证市场数据的完整性和合理性
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
@Slf4j
public class DataValidator {
    
    @Autowired
    private MarketDataConfig marketDataConfig;

    /**
     * 设置市场数据配置（用于测试）
     */
    public void setMarketDataConfig(MarketDataConfig marketDataConfig) {
        this.marketDataConfig = marketDataConfig;
    }
    
    // 价格缓存，用于偏差检测
    private final ConcurrentHashMap<String, BigDecimal> lastPriceCache = new ConcurrentHashMap<>();
    
    // 验证统计
    private final AtomicLong totalValidations = new AtomicLong(0);
    private final AtomicLong validationFailures = new AtomicLong(0);
    
    /**
     * 验证K线数据
     */
    public boolean validateKlineData(KlineData klineData) {
        totalValidations.incrementAndGet();
        
        try {
            // 基础验证
            if (!validateBasicKlineData(klineData)) {
                validationFailures.incrementAndGet();
                return false;
            }
            
            // 价格逻辑验证
            if (!validateKlinePriceLogic(klineData)) {
                validationFailures.incrementAndGet();
                return false;
            }
            
            // 时间验证
            if (!validateKlineTime(klineData)) {
                validationFailures.incrementAndGet();
                return false;
            }
            
            // 价格偏差验证
            if (!validatePriceDeviation(klineData.getSymbol(), klineData.getClosePrice())) {
                validationFailures.incrementAndGet();
                return false;
            }
            
            // 成交量验证
            if (!validateVolume(klineData.getVolume())) {
                validationFailures.incrementAndGet();
                return false;
            }
            
            // 更新价格缓存
            updatePriceCache(klineData.getSymbol(), klineData.getClosePrice());
            
            return true;
            
        } catch (Exception e) {
            log.error("K线数据验证异常: {}", klineData, e);
            validationFailures.incrementAndGet();
            return false;
        }
    }
    
    /**
     * 验证市场数据
     */
    public boolean validateMarketData(TradeData tradeData) {
        totalValidations.incrementAndGet();

        try {
            log.debug("开始验证市场数据: {}", tradeData);

            // 基础验证
            if (!validateBasicMarketData(tradeData)) {
                validationFailures.incrementAndGet();
                log.warn("基础验证失败: {}", tradeData);
                return false;
            }

            // 时间验证
            if (!validateMarketDataTime(tradeData)) {
                validationFailures.incrementAndGet();
                log.warn("时间验证失败: {}", tradeData);
                return false;
            }

            // 价格验证
            if (tradeData.getPrice() != null) {
                if (!validatePrice(tradeData.getPrice())) {
                    validationFailures.incrementAndGet();
                    log.warn("价格验证失败: price={}, data={}", tradeData.getPrice(), tradeData);
                    return false;
                }

                if (!validatePriceDeviation(tradeData.getSymbol(), tradeData.getPrice())) {
                    validationFailures.incrementAndGet();
                    log.warn("价格偏差验证失败: symbol={}, price={}, data={}",
                            tradeData.getSymbol(), tradeData.getPrice(), tradeData);
                    return false;
                }

                // 更新价格缓存
                updatePriceCache(tradeData.getSymbol(), tradeData.getPrice());
            }

            // 数量验证
            if (tradeData.getQuantity() != null && !validateVolume(tradeData.getQuantity())) {
                validationFailures.incrementAndGet();
                log.warn("数量验证失败: quantity={}, data={}", tradeData.getQuantity(), tradeData);
                return false;
            }

            log.debug("市场数据验证通过: {}", tradeData);
            return true;

        } catch (Exception e) {
            log.error("市场数据验证异常: {}", tradeData, e);
            validationFailures.incrementAndGet();
            return false;
        }
    }
    
    /**
     * 基础K线数据验证
     */
    private boolean validateBasicKlineData(KlineData klineData) {
        if (klineData == null) {
            log.warn("K线数据为空");
            return false;
        }
        
        if (klineData.getSymbol() == null || klineData.getSymbol().trim().isEmpty()) {
            log.warn("K线数据交易对为空: {}", klineData);
            return false;
        }
        
        if (klineData.getInterval() == null || klineData.getInterval().trim().isEmpty()) {
            log.warn("K线数据时间间隔为空: {}", klineData);
            return false;
        }
        
        if (klineData.getOpenTime() == null) {
            log.warn("K线数据开盘时间为空: {}", klineData);
            return false;
        }
        
        return true;
    }
    
    /**
     * 基础市场数据验证
     */
    private boolean validateBasicMarketData(TradeData tradeData) {
        if (tradeData == null) {
            log.warn("市场数据为空");
            return false;
        }

        if (tradeData.getSymbol() == null || tradeData.getSymbol().trim().isEmpty()) {
            log.warn("市场数据交易对为空: {}", tradeData);
            return false;
        }

        if (tradeData.getTradeTime() == null) {
            log.warn("市场数据时间戳为空: {}", tradeData);
            return false;
        }

        return true;
    }
    
    /**
     * K线价格逻辑验证
     */
    private boolean validateKlinePriceLogic(KlineData klineData) {
        BigDecimal open = klineData.getOpenPrice();
        BigDecimal high = klineData.getHighPrice();
        BigDecimal low = klineData.getLowPrice();
        BigDecimal close = klineData.getClosePrice();
        
        if (open == null || high == null || low == null || close == null) {
            log.warn("K线价格数据不完整: {}", klineData);
            return false;
        }
        
        // 验证价格都为正数
        if (open.compareTo(BigDecimal.ZERO) <= 0 || high.compareTo(BigDecimal.ZERO) <= 0 ||
            low.compareTo(BigDecimal.ZERO) <= 0 || close.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("K线价格包含非正数: {}", klineData);
            return false;
        }
        
        // 验证最高价 >= max(开盘价, 收盘价)
        BigDecimal maxOpenClose = open.max(close);
        if (high.compareTo(maxOpenClose) < 0) {
            log.warn("K线最高价小于开盘价或收盘价: high={}, open={}, close={}", high, open, close);
            return false;
        }
        
        // 验证最低价 <= min(开盘价, 收盘价)
        BigDecimal minOpenClose = open.min(close);
        if (low.compareTo(minOpenClose) > 0) {
            log.warn("K线最低价大于开盘价或收盘价: low={}, open={}, close={}", low, open, close);
            return false;
        }
        
        // 验证最高价 >= 最低价
        if (high.compareTo(low) < 0) {
            log.warn("K线最高价小于最低价: high={}, low={}", high, low);
            return false;
        }
        
        return true;
    }
    
    /**
     * K线时间验证
     */
    private boolean validateKlineTime(KlineData klineData) {
        // 如果配置了为历史数据跳过时间验证，则直接返回true
        if (marketDataConfig.getProcessor().getValidation().isSkipTimeValidationForHistorical()) {
            return true;
        }
        LocalDateTime openTime = klineData.getOpenTime();
        LocalDateTime closeTime = klineData.getCloseTime();
        LocalDateTime now = LocalDateTime.now();
        
        // 验证开盘时间不能是未来时间
        if (openTime.isAfter(now.plusSeconds(60))) { // 允许1分钟的时钟偏差
            log.warn("K线开盘时间是未来时间: openTime={}, now={}", openTime, now);
            return false;
        }
        
        // 验证收盘时间大于开盘时间
        if (closeTime != null && closeTime.isBefore(openTime)) {
            log.warn("K线收盘时间早于开盘时间: openTime={}, closeTime={}", openTime, closeTime);
            return false;
        }
        
        return true;
    }
    
    /**
     * 市场数据时间验证
     */
    private boolean validateMarketDataTime(TradeData tradeData) {
        // 如果配置了为历史数据跳过时间验证，则直接返回true
        if (marketDataConfig.getProcessor().getValidation().isSkipTimeValidationForHistorical()) {
            return true;
        }
        Instant timestamp = tradeData.getTradeTime().toInstant(ZoneOffset.UTC);
        Instant now = Instant.now();
    
        // 验证时间戳不能是未来时间
        if (timestamp.isAfter(now.plusSeconds(60))) { // 允许1分钟的时钟偏差
            log.warn("市场数据时间戳是未来时间: timestamp={}, now={}", timestamp, now);
            return false;
        }
    
        // 验证时间戳不能太旧（超过3小时，适应实时数据处理延迟）
        if (timestamp.isBefore(now.minusSeconds(10800))) { // 3小时 = 3 * 3600秒
            log.warn("市场数据时间戳过旧: timestamp={}, now={}, 超过3小时限制", timestamp, now);
            return false;
        }
    
        return true;
    }
    
    /**
     * 价格验证
     */
    private boolean validatePrice(BigDecimal price) {
        if (price == null) {
            return true; // 允许价格为空
        }
        
        if (price.compareTo(BigDecimal.ZERO) <= 0) {
            log.warn("价格必须为正数: price={}", price);
            return false;
        }
        
        // 验证价格精度（最多8位小数）
        if (price.scale() > 8) {
            log.warn("价格精度过高: price={}, scale={}", price, price.scale());
            return false;
        }
        
        return true;
    }
    
    /**
     * 价格偏差验证
     */
    private boolean validatePriceDeviation(String symbol, BigDecimal currentPrice) {
        if (!marketDataConfig.getProcessor().getValidation().isEnabled()) {
            return true;
        }

        BigDecimal lastPrice = lastPriceCache.get(symbol);
        if (lastPrice == null) {
            log.debug("首次收到价格数据: symbol={}, price={}", symbol, currentPrice);
            return true; // 第一次收到价格数据
        }

        double threshold = marketDataConfig.getProcessor().getValidation().getPriceDeviationThreshold();
        BigDecimal deviation = currentPrice.subtract(lastPrice).abs()
                .divide(lastPrice, 4, RoundingMode.HALF_UP);

        if (deviation.doubleValue() > threshold) {
            log.warn("价格偏差过大: symbol={}, lastPrice={}, currentPrice={}, deviation={}%, threshold={}%",
                    symbol, lastPrice, currentPrice, deviation.multiply(BigDecimal.valueOf(100)), threshold * 100);
            return false;
        }

        log.debug("价格偏差验证通过: symbol={}, lastPrice={}, currentPrice={}, deviation={}%",
                symbol, lastPrice, currentPrice, deviation.multiply(BigDecimal.valueOf(100)));
        return true;
    }
    
    /**
     * 成交量验证
     */
    private boolean validateVolume(BigDecimal volume) {
        if (volume == null) {
            return true; // 允许成交量为空
        }
        
        if (volume.compareTo(BigDecimal.ZERO) < 0) {
            log.warn("成交量不能为负数: volume={}", volume);
            return false;
        }
        
        double threshold = marketDataConfig.getProcessor().getValidation().getVolumeThreshold();
        if (volume.doubleValue() < threshold) {
            log.debug("成交量低于阈值: volume={}, threshold={}", volume, threshold);
            // 这里只记录日志，不返回false，因为小额交易是正常的
        }
        
        return true;
    }
    
    /**
     * 更新价格缓存
     */
    private void updatePriceCache(String symbol, BigDecimal price) {
        lastPriceCache.put(symbol, price);
    }
    
    /**
     * 获取验证统计信息
     */
    public ValidationStatistics getStatistics() {
        long total = totalValidations.get();
        long failures = validationFailures.get();
        
        return ValidationStatistics.builder()
                .totalValidations(total)
                .validationFailures(failures)
                .successRate(total > 0 ? ((double) (total - failures) / total) * 100.0 : 100.0)
                .cachedPriceCount(lastPriceCache.size())
                .build();
    }
    
    /**
     * 清理价格缓存
     */
    public void clearPriceCache() {
        lastPriceCache.clear();
        log.info("价格缓存已清理");
    }
    
    /**
     * 验证深度数据
     */
    public boolean validateDepthData(DepthData depthData) {
        totalValidations.incrementAndGet();

        try {
            // 基础验证
            if (!validateBasicDepthData(depthData)) {
                validationFailures.incrementAndGet();
                return false;
            }

            // 时间验证
            if (!validateDepthDataTime(depthData)) {
                validationFailures.incrementAndGet();
                return false;
            }

            // 深度数据格式验证
            if (!validateDepthDataFormat(depthData)) {
                validationFailures.incrementAndGet();
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("验证深度数据时发生异常: {}", depthData, e);
            validationFailures.incrementAndGet();
            return false;
        }
    }

    /**
     * 基础深度数据验证
     */
    private boolean validateBasicDepthData(DepthData depthData) {
        if (depthData == null) {
            log.warn("深度数据为空");
            return false;
        }

        if (depthData.getSymbol() == null || depthData.getSymbol().trim().isEmpty()) {
            log.warn("深度数据交易对为空: {}", depthData);
            return false;
        }

        if (depthData.getTimestamp() == null) {
            log.warn("深度数据时间戳为空: {}", depthData);
            return false;
        }

        if (depthData.getBids() == null || depthData.getAsks() == null) {
            log.warn("深度数据买卖盘无效: {}", depthData);
            return false;
        }

        return true;
    }

    /**
     * 深度数据时间验证
     */
    private boolean validateDepthDataTime(DepthData depthData) {
        Instant now = Instant.now();
        Instant timestamp = depthData.getTimestamp().toInstant(ZoneOffset.UTC);
    
        // 检查时间戳是否在合理范围内（不能太旧或太新）
        if (timestamp.isBefore(now.minusSeconds(10800))) { // 3小时前
            log.warn("深度数据时间戳过旧: timestamp={}, symbol={}, 超过3小时限制", timestamp, depthData.getSymbol());
            return false;
        }

        if (timestamp.isAfter(now.plusSeconds(60))) { // 1分钟后
            log.warn("深度数据时间戳过新: timestamp={}, symbol={}", timestamp, depthData.getSymbol());
            return false;
        }

        return true;
    }

    /**
     * 深度数据格式验证
     */
    private boolean validateDepthDataFormat(DepthData depthData) {
//        // 验证买盘数据
//        if (depthData.getBids() == null || depthData.getBids().isEmpty()) {
//            log.warn("深度数据买盘为空: {}", depthData);
//            return false;
//        }
//
//        // 验证卖盘数据
//        if (depthData.getAsks() == null || depthData.getAsks().isEmpty()) {
//            log.warn("深度数据卖盘为空: {}", depthData);
//            return false;
//        }

        return true;
    }

    /**
     * 验证统计信息
     */
    @lombok.Data
    @Builder
    public static class ValidationStatistics {
        private long totalValidations;
        private long validationFailures;
        private double successRate;
        private int cachedPriceCount;
    }
}
