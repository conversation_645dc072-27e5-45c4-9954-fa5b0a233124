package com.trading.market.processor;

import com.trading.common.config.MarketDataConfig;
import com.trading.common.dto.DataQualityStats;
import com.trading.common.dto.KlineData;
import com.trading.common.dto.TradeData;
import com.trading.common.dto.DepthData;
import com.trading.market.service.MarketDataService;
import lombok.Builder;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.Map;

/**
 * 数据质量监控器
 * 监控数据质量指标，检测异常数据和数据缺失
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class DataQualityMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(DataQualityMonitor.class);
    
    @Autowired
    private MarketDataConfig marketDataConfig;

    @Autowired
    private MarketDataService marketDataService;

    private ScheduledExecutorService monitorExecutor;
    
    // 数据质量统计
    private final ConcurrentHashMap<String, SymbolQualityMetrics> symbolMetrics = new ConcurrentHashMap<>();
    private final LongAdder totalDataPoints = new LongAdder();
    private final LongAdder qualityIssues = new LongAdder();
    private final LongAdder duplicateData = new LongAdder();
    private final LongAdder outOfOrderData = new LongAdder();
    private final LongAdder priceAnomalies = new LongAdder();
    private final LongAdder volumeAnomalies = new LongAdder();
    
    // 最近数据缓存（用于检测重复和乱序）
    private final ConcurrentHashMap<String, Instant> lastTimestamps = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, BigDecimal> lastPrices = new ConcurrentHashMap<>();
    
    // 时间窗口内的数据指纹缓存（用于检测重复数据）
    private final ConcurrentHashMap<String, Set<String>> recentDataFingerprints = new ConcurrentHashMap<>();
    private static final int DUPLICATE_CHECK_WINDOW_MINUTES = 5; // 5分钟时间窗口

    // 集成超高性能去重器
    @Autowired(required = false)
    private com.trading.common.algorithm.UltraFastDeduplicator ultraFastDeduplicator;
    
    @PostConstruct
    public void initialize() {
        log.info("初始化数据质量监控器...");
        
        if (marketDataConfig.getProcessor().getMonitoring().isEnabled()) {
            // 减少监控线程数，降低CPU消耗
            monitorExecutor = Executors.newScheduledThreadPool(1);

            // 定期报告数据质量 - 调整为5分钟间隔，减少CPU消耗
            monitorExecutor.scheduleAtFixedRate(
                    this::reportQualityMetrics,
                    300, 300, TimeUnit.SECONDS);

            // 定期保存数据质量统计到数据库 - 调整为15分钟间隔
            monitorExecutor.scheduleAtFixedRate(
                    this::saveQualityStatsToDatabase,
                    900, 900, TimeUnit.SECONDS);

            // 定期清理过期数据 - 调整为30分钟间隔
            monitorExecutor.scheduleAtFixedRate(
                    this::cleanupExpiredData,
                    1800, 1800, TimeUnit.SECONDS);

            log.info("数据质量监控已启用");
        }
        
        log.info("数据质量监控器初始化完成");
    }
    
    /**
     * 检查K线数据质量
     */
    public QualityCheckResult checkKlineDataQuality(KlineData klineData) {
        totalDataPoints.increment();
        
        String symbol = klineData.getSymbol();
        String key = symbol + "_" + klineData.getInterval();
        
        SymbolQualityMetrics metrics = symbolMetrics.computeIfAbsent(key, k -> new SymbolQualityMetrics());
        metrics.incrementTotal();
        
        QualityCheckResult result = QualityCheckResult.builder()
                .symbol(symbol)
                .dataType("kline")
                .timestamp(klineData.getOpenTime().toInstant(ZoneOffset.UTC))
                .passed(true)
                .build();
        
        // 检查时间戳
        if (!checkTimestamp(key, klineData.getOpenTime().toInstant(ZoneOffset.UTC), result)) {
            metrics.incrementIssues();
            qualityIssues.increment();
        }

        // 检查价格数据
        if (!checkPriceData(key, klineData, result)) {
            metrics.incrementIssues();
            qualityIssues.increment();
        }

        // 检查交易量
        if (!checkVolumeData(klineData, result)) {
            metrics.incrementIssues();
            qualityIssues.increment();
        }

        // 检查数据完整性
        if (!checkDataCompleteness(klineData, result)) {
            metrics.incrementIssues();
            qualityIssues.increment();
        }
        
        // 只有在所有检查都通过时才更新最后的数据
        if (result.isPassed()) {
            lastTimestamps.put(key, klineData.getOpenTime().toInstant(ZoneOffset.UTC));
            lastPrices.put(key, klineData.getClosePrice());
        }
        
        return result;
    }
    
    /**
     * 检查市场数据质量
     */
    public QualityCheckResult checkMarketDataQuality(TradeData tradeData) {
        totalDataPoints.increment();
    
        String symbol = tradeData.getSymbol();
        String key = symbol + "_trade";
    
        log.debug("开始质量检查: key={}, data={}", key, tradeData);
    
        SymbolQualityMetrics metrics = symbolMetrics.computeIfAbsent(key, k -> new SymbolQualityMetrics());
        metrics.incrementTotal();
    
        QualityCheckResult result = QualityCheckResult.builder()
                .symbol(symbol)
                .dataType("trade")
                .timestamp(tradeData.getTradeTime().toInstant(ZoneOffset.UTC))
                .passed(true)
                .build();
    
        // 检查时间戳
        if (!checkTimestamp(key, tradeData.getTradeTime().toInstant(ZoneOffset.UTC), result)) {
            metrics.incrementIssues();
            qualityIssues.increment();
            log.warn("时间戳质量检查失败: key={}, timestamp={}, issues={}",
                    key, tradeData.getTradeTime(), result.getIssues());
        }
    
        // 检查数据指纹（更精确的重复检测）
        String dataFingerprint = generateDataFingerprint(tradeData);
        if (!checkDataFingerprint(key, dataFingerprint, result)) {
            metrics.incrementIssues();
            qualityIssues.increment();
            log.warn("数据指纹检查失败: key={}, fingerprint={}, issues={}",
                    key, dataFingerprint, result.getIssues());
        }
    
        // 检查价格数据
        if (tradeData.getPrice() != null && !checkPriceValue(key, tradeData.getPrice(), result)) {
            metrics.incrementIssues();
            qualityIssues.increment();
            log.warn("价格质量检查失败: key={}, price={}, issues={}",
                    key, tradeData.getPrice(), result.getIssues());
        }
    
        // 检查数据完整性
        if (!checkMarketDataCompleteness(tradeData, result)) {
            metrics.incrementIssues();
            qualityIssues.increment();
            log.warn("数据完整性检查失败: key={}, issues={}", key, result.getIssues());
        }
    
        // 只有在所有检查都通过时才更新最后的数据
        if (result.isPassed()) {
            lastTimestamps.put(key, tradeData.getTradeTime().toInstant(ZoneOffset.UTC));
            if (tradeData.getPrice() != null) {
                lastPrices.put(key, tradeData.getPrice());
            }
        }
    
        log.debug("质量检查完成: key={}, passed={}, issues={}",
                key, result.isPassed(), result.getIssues());
    
        return result;
    }
    
    /**
     * 检查时间戳 - 优化并发处理的乱序检查
     */
    private boolean checkTimestamp(String key, Instant timestamp, QualityCheckResult result) {
        if (timestamp == null) {
            result.addIssue("时间戳为空");
            return false;
        }

        MarketDataConfig.ProcessorConfig.QualityConfig.TimestampCheckConfig timestampConfig =
                marketDataConfig.getProcessor().getQuality().getTimestampCheck();

        if (!timestampConfig.isEnabled()) {
            return true; // 如果时间戳检查被禁用，直接返回true
        }

        // 检查时间戳是否在合理范围内
        Instant now = Instant.now();

        // 检查未来时间
        if (timestamp.isAfter(now.plus(timestampConfig.getMaxFutureMinutes(), ChronoUnit.MINUTES))) {
            result.addIssue("时间戳超前: " + timestamp + " (超过" + timestampConfig.getMaxFutureMinutes() + "分钟限制)");
            return false;
        }

        // 检查过旧时间 - 根据是否启用历史数据模式使用不同的限制
        long maxAgeHours;
        if (timestampConfig.isEnableHistoricalDataMode() && !timestampConfig.isStrictModeForRealtime()) {
            maxAgeHours = timestampConfig.getMaxAgeHours(); // 历史数据模式：24小时
        } else {
            maxAgeHours = timestampConfig.getRealtimeMaxAgeHours(); // 实时数据模式：3小时
        }

        if (timestamp.isBefore(now.minus(maxAgeHours, ChronoUnit.HOURS))) {
            result.addIssue("时间戳过旧: " + timestamp + " (超过" + maxAgeHours + "小时限制)");
            return false;
        }

        // 优化乱序检查：考虑并发处理的情况
        Instant lastTimestamp = lastTimestamps.get(key);
        if (lastTimestamp != null) {
            // 允许一定的时间戳容差（500ms），处理并发和网络延迟
            long timeDiffMs = timestamp.toEpochMilli() - lastTimestamp.toEpochMilli();

            // 只有当时间戳明显早于上次时间戳（超过500ms）时才认为是乱序
            if (timeDiffMs < -500) {
                // 进一步检查：如果时间差在合理范围内（5秒），可能是并发处理导致的轻微乱序
                if (timeDiffMs > -5000) {
                    log.debug("检测到轻微时间戳乱序，可能由并发处理导致: key={}, current={}, last={}, diff={}ms",
                            key, timestamp, lastTimestamp, timeDiffMs);
                    // 对于轻微乱序，只记录但不拒绝数据
                    return true;
                } else {
                    result.addIssue("数据乱序: " + timestamp + " < " + lastTimestamp + " (差异: " + timeDiffMs + "ms)");
                    outOfOrderData.increment();
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * 检查数据指纹是否重复（用于更精确的重复检测）
     * 优先使用超高性能去重器，回退到本地指纹检查
     */
    private boolean checkDataFingerprint(String key, String dataFingerprint, QualityCheckResult result) {
        // 优先使用超高性能去重器
        if (ultraFastDeduplicator != null) {
            String deduplicationKey = "fingerprint:" + key + ":" + dataFingerprint;
            if (ultraFastDeduplicator.isDuplicate(deduplicationKey)) {
                result.addIssue("重复数据指纹(超高性能检测): " + dataFingerprint.substring(0, Math.min(50, dataFingerprint.length())) + "...");
                duplicateData.increment();
                log.debug("超高性能去重器检测到重复数据: key={}, fingerprint={}", key, dataFingerprint);
                return false;
            }

            // 标记为已处理
            ultraFastDeduplicator.markAsProcessed(deduplicationKey);
            return true;
        }

        // 回退到本地指纹检查
        Set<String> fingerprints = recentDataFingerprints.computeIfAbsent(key, k -> ConcurrentHashMap.newKeySet());

        if (fingerprints.contains(dataFingerprint)) {
            // 真正的重复数据
            result.addIssue("重复数据指纹: " + dataFingerprint.substring(0, Math.min(50, dataFingerprint.length())) + "...");
            duplicateData.increment();
            log.debug("检测到重复数据: key={}, fingerprint={}", key, dataFingerprint);
            return false;
        }

        // 添加新的指纹
        fingerprints.add(dataFingerprint);

        // 优化内存管理：使用更智能的清理策略
        if (fingerprints.size() > 2000) { // 增加缓存大小
            // 保留最近的一半指纹，而不是全部清空
            Set<String> newFingerprints = ConcurrentHashMap.newKeySet();
            fingerprints.stream()
                .skip(fingerprints.size() / 2) // 跳过前一半
                .forEach(newFingerprints::add);
            newFingerprints.add(dataFingerprint); // 确保当前指纹被保留

            recentDataFingerprints.put(key, newFingerprints);
            log.debug("清理数据指纹缓存: key={}, 保留数量={}", key, newFingerprints.size());
        }

        return true;
    }

    /**
     * 生成数据指纹 - 优化版本，更精确地识别重复数据
     */
    private String generateDataFingerprint(TradeData tradeData) {
        // 使用更精确的指纹算法，包含更多字段以减少误判
        StringBuilder fingerprint = new StringBuilder();
        fingerprint.append(tradeData.getSymbol()).append("_");
        fingerprint.append("trade").append("_");
    
        // 时间戳精确到毫秒
        if (tradeData.getTradeTime() != null) {
            fingerprint.append(tradeData.getTradeTime().toInstant(ZoneOffset.UTC).toEpochMilli()).append("_");
        }
    
        // 价格信息
        if (tradeData.getPrice() != null) {
            fingerprint.append(tradeData.getPrice().toPlainString()).append("_");
        }
    
        // 数量信息
        if (tradeData.getQuantity() != null) {
            fingerprint.append(tradeData.getQuantity().toPlainString()).append("_");
        }
    
        // 添加交易ID和订单ID以确保唯一性
        if (tradeData.getTradeId() != null) {
            fingerprint.append("trade_").append(tradeData.getTradeId()).append("_");
        }
    
        // 使用稳定的字符串哈希而不是对象hashCode
        return fingerprint.toString();
    }

    /**
     * 生成深度数据指纹 - 专门用于深度数据的指纹生成
     */
    private String generateDepthDataFingerprint(DepthData depthData) {
        StringBuilder fingerprint = new StringBuilder();
        fingerprint.append(depthData.getSymbol()).append("_");
        fingerprint.append("depth_");

        // 时间戳精确到毫秒
        if (depthData.getTimestamp() != null) {
            fingerprint.append(depthData.getTimestamp().toInstant(ZoneOffset.UTC).toEpochMilli()).append("_");
        }

        // 使用买卖盘数据的哈希值
        if (depthData.getBids() != null) {
            fingerprint.append("bids_").append(depthData.getBids().hashCode()).append("_");
        }
        if (depthData.getAsks() != null) {
            fingerprint.append("asks_").append(depthData.getAsks().hashCode()).append("_");
        }

        // 添加最后更新ID
        if (depthData.getLastUpdateId() != null) {
            fingerprint.append("update_").append(depthData.getLastUpdateId());
        }

        return fingerprint.toString();
    }
    
    /**
     * 检查K线价格数据
     */
    private boolean checkPriceData(String key, KlineData klineData, QualityCheckResult result) {
        boolean valid = true;
        
        // 检查价格是否为正数
        if (klineData.getOpenPrice().compareTo(BigDecimal.ZERO) <= 0 ||
            klineData.getHighPrice().compareTo(BigDecimal.ZERO) <= 0 ||
            klineData.getLowPrice().compareTo(BigDecimal.ZERO) <= 0 ||
            klineData.getClosePrice().compareTo(BigDecimal.ZERO) <= 0) {
            result.addIssue("价格不能为零或负数");
            valid = false;
        }
        
        // 检查价格逻辑关系
        if (klineData.getHighPrice().compareTo(klineData.getLowPrice()) < 0) {
            result.addIssue("最高价小于最低价");
            valid = false;
        }
        
        if (klineData.getHighPrice().compareTo(klineData.getOpenPrice()) < 0 ||
            klineData.getHighPrice().compareTo(klineData.getClosePrice()) < 0) {
            result.addIssue("最高价小于开盘价或收盘价");
            valid = false;
        }
        
        if (klineData.getLowPrice().compareTo(klineData.getOpenPrice()) > 0 ||
            klineData.getLowPrice().compareTo(klineData.getClosePrice()) > 0) {
            result.addIssue("最低价大于开盘价或收盘价");
            valid = false;
        }
        
        // 检查价格异常波动
        BigDecimal lastPrice = lastPrices.get(key);
        if (lastPrice != null) {
            double threshold = marketDataConfig.getProcessor().getValidation().getPriceDeviationThreshold();
            BigDecimal deviation = klineData.getClosePrice().subtract(lastPrice)
                    .abs().divide(lastPrice, 4, RoundingMode.HALF_UP);
            
            if (deviation.doubleValue() > threshold) {
                result.addIssue("价格异常波动: " + deviation.multiply(new BigDecimal("100")) + "%");
                priceAnomalies.increment();
                valid = false;
            }
        }
        
        return valid;
    }
    
    /**
     * 检查价格值
     */
    private boolean checkPriceValue(String key, BigDecimal price, QualityCheckResult result) {
        if (price.compareTo(BigDecimal.ZERO) <= 0) {
            result.addIssue("价格不能为零或负数: " + price);
            return false;
        }
        
        // 检查价格异常波动
        BigDecimal lastPrice = lastPrices.get(key);
        if (lastPrice != null) {
            double threshold = marketDataConfig.getProcessor().getValidation().getPriceDeviationThreshold();
            BigDecimal deviation = price.subtract(lastPrice)
                    .abs().divide(lastPrice, 4, RoundingMode.HALF_UP);
            
            if (deviation.doubleValue() > threshold) {
                result.addIssue("价格异常波动: " + deviation.multiply(new BigDecimal("100")) + "%");
                priceAnomalies.increment();
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查交易量数据 - 优化版本，智能处理零交易量情况
     */
    private boolean checkVolumeData(KlineData klineData, QualityCheckResult result) {
        double threshold = marketDataConfig.getProcessor().getValidation().getVolumeThreshold();
        BigDecimal volume = klineData.getVolume();
        BigDecimal quoteVolume = klineData.getQuoteVolume();

        // 检查交易量是否为负数（这是绝对不允许的）
        if (volume.compareTo(BigDecimal.ZERO) < 0) {
            result.addIssue("交易量不能为负数: " + volume);
            volumeAnomalies.increment();
            return false;
        }

        // 对于零交易量的情况，进行更智能的判断
        if (volume.compareTo(BigDecimal.ZERO) == 0) {
            // 检查是否是合理的零交易量情况
            if (isValidZeroVolumeScenario(klineData, result)) {
                // 零交易量但是合理的情况，记录为警告但不拒绝数据
                result.addWarning("零交易量（可能的市场休市或交易暂停）: " + volume);
                log.debug("检测到零交易量但属于合理情况: symbol={}, interval={}, time={}",
                        klineData.getSymbol(), klineData.getInterval(), klineData.getOpenTime());
                return true; // 允许通过，但记录警告
            } else {
                // 不合理的零交易量
                result.addIssue("异常零交易量: " + volume);
                volumeAnomalies.increment();
                return false;
            }
        }

        // 对于非零但小于阈值的交易量，进行宽松处理
        if (volume.doubleValue() < threshold) {
            // 记录为警告，但不拒绝数据（特别是对于小市值币种）
            result.addWarning("交易量较小: " + volume + " (阈值: " + threshold + ")");
            log.debug("检测到小交易量: symbol={}, volume={}, threshold={}",
                    klineData.getSymbol(), volume, threshold);
            // 对于小交易量，我们记录但不拒绝，因为这在某些市场条件下是正常的
            return true;
        }

        // 检查报价交易量
        if (quoteVolume.compareTo(BigDecimal.ZERO) < 0) {
            result.addIssue("报价交易量不能为负数: " + quoteVolume);
            return false;
        }

        // 检查交易量和报价交易量的一致性
        if (volume.compareTo(BigDecimal.ZERO) > 0 && quoteVolume.compareTo(BigDecimal.ZERO) == 0) {
            result.addIssue("交易量大于零但报价交易量为零，数据不一致");
            return false;
        }

        return true;
    }

    /**
     * 判断是否是合理的零交易量场景
     */
    private boolean isValidZeroVolumeScenario(KlineData klineData, QualityCheckResult result) {
        // 检查价格数据是否一致（开盘价=收盘价=最高价=最低价）
        // 这通常表示市场休市或交易暂停
        BigDecimal openPrice = klineData.getOpenPrice();
        BigDecimal closePrice = klineData.getClosePrice();
        BigDecimal highPrice = klineData.getHighPrice();
        BigDecimal lowPrice = klineData.getLowPrice();

        if (openPrice != null && closePrice != null && highPrice != null && lowPrice != null) {
            boolean pricesEqual = openPrice.compareTo(closePrice) == 0 &&
                                 openPrice.compareTo(highPrice) == 0 &&
                                 openPrice.compareTo(lowPrice) == 0;

            if (pricesEqual) {
                // 所有价格相等且交易量为零，这是合理的市场休市情况
                return true;
            }
        }

        // 检查是否是周末或节假日（这里可以根据需要扩展）
        // 目前简单地允许零交易量，因为这在某些市场条件下是正常的
        return true;
    }
    
    /**
     * 检查K线数据完整性
     */
    private boolean checkDataCompleteness(KlineData klineData, QualityCheckResult result) {
        if (klineData.getSymbol() == null || klineData.getSymbol().trim().isEmpty()) {
            result.addIssue("交易对符号为空");
            return false;
        }
        
        if (klineData.getInterval() == null || klineData.getInterval().trim().isEmpty()) {
            result.addIssue("时间间隔为空");
            return false;
        }
        
        if (klineData.getOpenTime() == null || klineData.getCloseTime() == null) {
            result.addIssue("时间戳为空");
            return false;
        }
        
        return true;
    }
    
    /**
     * 检查市场数据完整性
     */
    private boolean checkMarketDataCompleteness(TradeData tradeData, QualityCheckResult result) {
        if (tradeData.getSymbol() == null || tradeData.getSymbol().trim().isEmpty()) {
            result.addIssue("交易对符号为空");
            return false;
        }
        
        if (tradeData.getTradeTime() == null) {
            result.addIssue("时间戳为空");
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取数据质量统计
     */
    public DataQualityStatistics getQualityStatistics() {
        long total = totalDataPoints.sum();
        long issues = qualityIssues.sum();
        
        return DataQualityStatistics.builder()
                .totalDataPoints(total)
                .qualityIssues(issues)
                .duplicateData(duplicateData.sum())
                .outOfOrderData(outOfOrderData.sum())
                .priceAnomalies(priceAnomalies.sum())
                .volumeAnomalies(volumeAnomalies.sum())
                .qualityScore(total > 0 ? ((double) (total - issues) / total) * 100.0 : 100.0)
                .symbolMetrics(new ConcurrentHashMap<>(symbolMetrics))
                .build();
    }
    
    /**
     * 定期报告数据质量指标
     */
    private void reportQualityMetrics() {
        try {
            DataQualityStatistics stats = getQualityStatistics();
            
            log.info("=== 数据质量监控报告 ===");
            log.info("总数据点: {}", stats.getTotalDataPoints());
            log.info("质量问题: {}", stats.getQualityIssues());
            log.info("重复数据: {}", stats.getDuplicateData());
            log.info("乱序数据: {}", stats.getOutOfOrderData());
            log.info("价格异常: {}", stats.getPriceAnomalies());
            log.info("交易量异常: {}", stats.getVolumeAnomalies());
            log.info("质量评分: {:.2f}%", stats.getQualityScore());
            
            // 按交易对报告
            stats.getSymbolMetrics().forEach((symbol, metrics) -> {
                double symbolQuality = metrics.getTotal() > 0 ? 
                        ((double) (metrics.getTotal() - metrics.getIssues()) / metrics.getTotal()) * 100.0 : 100.0;
                log.info("交易对[{}] - 总数: {}, 问题: {}, 质量: {:.2f}%", 
                        symbol, metrics.getTotal(), metrics.getIssues(), symbolQuality);
            });
            
        } catch (Exception e) {
            log.error("数据质量监控报告失败", e);
        }
    }
    
    /**
     * 保存数据质量统计到数据库
     */
    private void saveQualityStatsToDatabase() {
        try {
            LocalDateTime currentHour = LocalDateTime.now(ZoneOffset.UTC).withMinute(0).withSecond(0).withNano(0);
            List<DataQualityStats> statsList = new ArrayList<>();

            // 为每个交易对和数据类型创建质量统计记录
            symbolMetrics.forEach((key, metrics) -> {
                String[] parts = key.split("_");
                if (parts.length >= 2) {
                    String symbol = parts[0];
                    String dataType = parts[1];

                    long total = metrics.getTotal();
                    long issues = metrics.getIssues();
                    long success = total - issues;

                    if (total > 0) {
                        DataQualityStats stats = DataQualityStats.builder()
                                .symbol(symbol)
                                .dataType(dataType)
                                .dateHour(currentHour)
                                .totalCount(total)
                                .successCount(success)
                                .errorCount(issues)
                                .avgLatency(10.0) // 默认延迟，实际应该从性能监控获取
                                .maxLatency(50L)  // 默认最大延迟
                                .minQualityScore(total > 0 ? (double) success / total : 1.0)
                                .avgQualityScore(total > 0 ? (double) success / total : 1.0)
                                .createdAt(Instant.now())
                                .build();

                        statsList.add(stats);
                    }
                }
            });

            // 批量保存到数据库
            if (!statsList.isEmpty()) {
                for (DataQualityStats stats : statsList) {
                    marketDataService.saveDataQualityStatsAsync(stats);
                }
                log.info("已保存{}条数据质量统计记录到数据库", statsList.size());
            }

        } catch (Exception e) {
            log.error("保存数据质量统计到数据库失败", e);
        }
    }

    /**
     * 清理过期数据
     */
    private void cleanupExpiredData() {
        try {
            Instant cutoff = Instant.now().minus(1, ChronoUnit.HOURS);
            Instant fingerprintCutoff = Instant.now().minus(DUPLICATE_CHECK_WINDOW_MINUTES, ChronoUnit.MINUTES);

            // 清理过期的时间戳和价格数据
            int removedTimestamps = 0;
            int removedPrices = 0;
            
            removedTimestamps = lastTimestamps.entrySet().removeIf(entry -> entry.getValue().isBefore(cutoff)) ? 
                lastTimestamps.size() : removedTimestamps;
            removedPrices = lastPrices.entrySet().removeIf(entry ->
                    lastTimestamps.get(entry.getKey()) == null) ? 
                lastPrices.size() : removedPrices;

            // 清理过期的数据指纹
            int removedFingerprints = 0;
            for (Map.Entry<String, Set<String>> entry : recentDataFingerprints.entrySet()) {
                Set<String> fingerprints = entry.getValue();
                int originalSize = fingerprints.size();
                
                // 简单的清理策略：如果指纹集合过大，清空它
                if (fingerprints.size() > 500) {
                    fingerprints.clear();
                    removedFingerprints += originalSize;
                }
            }

            // 移除空的指纹集合
            recentDataFingerprints.entrySet().removeIf(entry -> entry.getValue().isEmpty());

            log.debug("清理过期数据完成: 时间戳={}, 价格={}, 指纹={}", 
                removedTimestamps, removedPrices, removedFingerprints);
        } catch (Exception e) {
            log.error("清理过期数据失败", e);
        }
    }
    
    @PreDestroy
    public void shutdown() {
        log.info("关闭数据质量监控器...");
        if (monitorExecutor != null) {
            monitorExecutor.shutdown();
            try {
                if (!monitorExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    monitorExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                monitorExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("数据质量监控器已关闭");
    }
    
    // 内部类定义
    
    @Data
    @Builder
    public static class QualityCheckResult {
        private String symbol;
        private String dataType;
        private Instant timestamp;
        private boolean passed;
        private Double qualityScore;
        @Builder.Default
        private java.util.List<String> issues = new java.util.ArrayList<>();
        @Builder.Default
        private java.util.List<String> warnings = new java.util.ArrayList<>();

        public void addIssue(String issue) {
            this.issues.add(issue);
            this.passed = false;
        }

        public void addWarning(String warning) {
            this.warnings.add(warning);
            // 警告不会导致检查失败
        }
    }
    
    /**
     * 检查深度数据质量
     */
    public QualityCheckResult checkDepthDataQuality(DepthData depthData) {
        totalDataPoints.increment();

        QualityCheckResult result = QualityCheckResult.builder()
                .symbol(depthData.getSymbol())
                .dataType("depth")
                .timestamp(depthData.getTimestamp().toInstant(ZoneOffset.UTC))
                .passed(true)
                .qualityScore(1.0)
                .build();

        try {
            // 获取或创建交易对质量指标
            SymbolQualityMetrics metrics = symbolMetrics.computeIfAbsent(
                depthData.getSymbol(), k -> new SymbolQualityMetrics());
            metrics.incrementTotal();

            // 检查基础数据完整性
            if (!checkDepthDataCompleteness(depthData, result)) {
                metrics.incrementIssues();
                qualityIssues.increment();
                result.setPassed(false);
            }

            // 检查深度数据格式
            if (!checkDepthDataFormat(depthData, result)) {
                metrics.incrementIssues();
                qualityIssues.increment();
                result.setPassed(false);
            }

            // 检查时间戳合理性
            if (!checkDepthDataTimestamp(depthData, result)) {
                metrics.incrementIssues();
                qualityIssues.increment();
                result.setPassed(false);
            }

            // 检查深度数据指纹（防止重复数据）
            String key = depthData.getSymbol() + "_depth";
            String dataFingerprint = generateDepthDataFingerprint(depthData);
            if (!checkDataFingerprint(key, dataFingerprint, result)) {
                metrics.incrementIssues();
                qualityIssues.increment();
                log.warn("深度数据指纹检查失败: key={}, fingerprint={}, issues={}",
                        key, dataFingerprint, result.getIssues());
            }

            // 计算质量分数
            if (result.isPassed()) {
                result.setQualityScore(1.0);
            } else {
                result.setQualityScore(0.5); // 有问题但不是完全无效
            }

            log.debug("深度数据质量检查完成: symbol={}, passed={}, score={}",
                depthData.getSymbol(), result.isPassed(), result.getQualityScore());

        } catch (Exception e) {
            log.error("检查深度数据质量时发生异常: {}", depthData, e);
            result.setPassed(false);
            result.setQualityScore(0.0);
            result.addIssue("质量检查异常: " + e.getMessage());
        }

        return result;
    }

    /**
     * 检查深度数据完整性
     */
    private boolean checkDepthDataCompleteness(DepthData depthData, QualityCheckResult result) {
        boolean complete = true;

        if (depthData.getBids() == null || depthData.getBids().isEmpty()) {
            result.addIssue("买盘数据为空");
            complete = false;
        }

        if (depthData.getAsks() == null || depthData.getAsks().isEmpty()) {
            result.addIssue("卖盘数据为空");
            complete = false;
        }

        return complete;
    }

    /**
     * 检查深度数据格式
     */
    private boolean checkDepthDataFormat(DepthData depthData, QualityCheckResult result) {
        boolean valid = true;

        // 检查买盘数据格式（应该是JSON格式）
        if (depthData.getBids() != null && !isValidJsonArray(depthData.getBids())) {
            result.addIssue("买盘数据格式无效");
            valid = false;
        }

        // 检查卖盘数据格式（应该是JSON格式）
        if (depthData.getAsks() != null && !isValidJsonArray(depthData.getAsks())) {
            result.addIssue("卖盘数据格式无效");
            valid = false;
        }

        return valid;
    }

    /**
     * 检查深度数据时间戳 - 优化并发处理
     */
    private boolean checkDepthDataTimestamp(DepthData depthData, QualityCheckResult result) {
        Instant now = Instant.now();
        Instant timestamp = depthData.getTimestamp().toInstant(ZoneOffset.UTC);

        MarketDataConfig.ProcessorConfig.QualityConfig.TimestampCheckConfig timestampConfig =
                marketDataConfig.getProcessor().getQuality().getTimestampCheck();

        if (!timestampConfig.isEnabled()) {
            return true; // 如果时间戳检查被禁用，直接返回true
        }

        // 检查过旧时间 - 根据是否启用历史数据模式使用不同的限制
        long maxAgeHours;
        if (timestampConfig.isEnableHistoricalDataMode() && !timestampConfig.isStrictModeForRealtime()) {
            maxAgeHours = timestampConfig.getMaxAgeHours(); // 历史数据模式：24小时
        } else {
            maxAgeHours = timestampConfig.getRealtimeMaxAgeHours(); // 实时数据模式：3小时
        }

        if (timestamp.isBefore(now.minus(maxAgeHours, ChronoUnit.HOURS))) {
            result.addIssue("数据时间戳过旧 (超过" + maxAgeHours + "小时限制)");
            return false;
        }

        // 检查时间戳是否过新 - 放宽限制，允许更大的时钟偏差
        if (timestamp.isAfter(now.plus(timestampConfig.getMaxFutureMinutes() + 2, ChronoUnit.MINUTES))) {
            result.addIssue("数据时间戳过新 (超过" + (timestampConfig.getMaxFutureMinutes() + 2) + "分钟限制)");
            return false;
        }

        // 对于深度数据，检查与上次时间戳的关系，但更宽松
        String key = depthData.getSymbol() + "_depth";
        Instant lastTimestamp = lastTimestamps.get(key);
        if (lastTimestamp != null) {
            long timeDiffMs = timestamp.toEpochMilli() - lastTimestamp.toEpochMilli();

            // 深度数据更新频繁，允许更大的时间戳容差（1秒）
            if (timeDiffMs < -1000) {
                log.debug("深度数据时间戳轻微乱序: symbol={}, current={}, last={}, diff={}ms",
                        depthData.getSymbol(), timestamp, lastTimestamp, timeDiffMs);
                // 对于深度数据的轻微乱序，只记录但不拒绝
                return true;
            }
        }

        return true;
    }

    /**
     * 简单的JSON数组格式验证
     */
    private boolean isValidJsonArray(List<DepthData.PriceLevel> list) {
        // Since it's already a list, it's considered valid
        return list != null;
    }

    @Data
    @Builder
    public static class DataQualityStatistics {
        private long totalDataPoints;
        private long qualityIssues;
        private long duplicateData;
        private long outOfOrderData;
        private long priceAnomalies;
        private long volumeAnomalies;
        private double qualityScore;
        private ConcurrentHashMap<String, SymbolQualityMetrics> symbolMetrics;
    }

    public static class SymbolQualityMetrics {
        private final AtomicLong total = new AtomicLong(0);
        private final AtomicLong issues = new AtomicLong(0);

        public void incrementTotal() { total.incrementAndGet(); }
        public void incrementIssues() { issues.incrementAndGet(); }

        public long getTotal() { return total.get(); }
        public long getIssues() { return issues.get(); }
    }
}