package com.trading.market.processor;

import com.trading.common.config.MarketDataConfig;
import lombok.Builder;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 性能监控器
 * 收集和统计数据处理的性能指标
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class PerformanceMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(PerformanceMonitor.class);
    
    @Autowired
    private MarketDataConfig marketDataConfig;
    
    private ScheduledExecutorService monitorExecutor;
    
    // 全局统计
    private final LongAdder totalProcessed = new LongAdder();
    private final LongAdder totalValidationFailed = new LongAdder();
    private final LongAdder totalStorageFailed = new LongAdder();
    private final LongAdder totalRetryAttempts = new LongAdder();
    
    // 处理时间统计
    private final LongAdder totalProcessingTime = new LongAdder();
    private final AtomicLong maxProcessingTime = new AtomicLong(0);
    private final AtomicLong minProcessingTime = new AtomicLong(Long.MAX_VALUE);
    
    // 存储操作统计
    private final ConcurrentHashMap<String, StorageMetrics> storageMetrics = new ConcurrentHashMap<>();
    
    // 数据类型统计
    private final ConcurrentHashMap<String, DataTypeMetrics> dataTypeMetrics = new ConcurrentHashMap<>();
    
    // 时间窗口统计（每分钟）
    private final ConcurrentHashMap<String, WindowMetrics> windowMetrics = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initialize() {
        log.info("初始化性能监控器...");
        
        // 初始化存储指标
        storageMetrics.put("influxdb", new StorageMetrics());
        storageMetrics.put("redis", new StorageMetrics());
        storageMetrics.put("kafka", new StorageMetrics());
        
        // 启动定期报告
        if (marketDataConfig.getProcessor().getMonitoring().isEnabled()) {
            long reportIntervalSeconds = marketDataConfig.getProcessor().getMonitoring().getReportIntervalSeconds();
            monitorExecutor = Executors.newScheduledThreadPool(1);
            monitorExecutor.scheduleAtFixedRate(
                    this::reportMetrics,
                    reportIntervalSeconds,
                    reportIntervalSeconds,
                    TimeUnit.SECONDS);
            log.info("性能监控报告已启用，间隔: {}秒", reportIntervalSeconds);
        }
        
        log.info("性能监控器初始化完成");
    }
    
    /**
     * 记录数据处理开始
     */
    public ProcessingContext startProcessing(String dataType, String symbol) {
        totalProcessed.increment();
        
        // 更新数据类型统计
        dataTypeMetrics.computeIfAbsent(dataType, k -> new DataTypeMetrics()).increment();
        
        // 更新时间窗口统计
        String windowKey = getCurrentWindowKey();
        windowMetrics.computeIfAbsent(windowKey, k -> new WindowMetrics()).increment();
        
        return ProcessingContext.builder()
                .dataType(dataType)
                .symbol(symbol)
                .startTime(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 记录数据处理完成
     */
    public void recordProcessingComplete(ProcessingContext context) {
        long processingTime = System.currentTimeMillis() - context.getStartTime();
        
        // 更新处理时间统计
        totalProcessingTime.add(processingTime);
        updateProcessingTimeRange(processingTime);
        
        log.debug("数据处理完成: dataType={}, symbol={}, 耗时={}ms", 
                context.getDataType(), context.getSymbol(), processingTime);
    }
    
    /**
     * 记录验证失败 - 增强版本，提供更详细的统计和分类
     */
    public void recordValidationFailure(String dataType, String symbol, String reason) {
        totalValidationFailed.increment();
        dataTypeMetrics.computeIfAbsent(dataType, k -> new DataTypeMetrics()).recordValidationFailure();

        // 根据失败原因进行分类统计
        String failureCategory = categorizeValidationFailure(reason);

        // 对于零交易量的情况，使用DEBUG级别而不是WARN级别
        if (reason.contains("零交易量") || reason.contains("交易量较小")) {
            log.debug("数据验证提醒: dataType={}, symbol={}, reason={}, category={}",
                    dataType, symbol, reason, failureCategory);
        } else {
            log.warn("数据验证失败: dataType={}, symbol={}, reason={}, category={}",
                    dataType, symbol, reason, failureCategory);
        }

        // 记录失败原因的统计信息
        recordValidationFailureByCategory(failureCategory, symbol);
    }

    /**
     * 对验证失败原因进行分类
     */
    private String categorizeValidationFailure(String reason) {
        if (reason.contains("零交易量") || reason.contains("交易量过小") || reason.contains("交易量较小")) {
            return "VOLUME_ISSUE";
        } else if (reason.contains("价格") || reason.contains("偏差")) {
            return "PRICE_ISSUE";
        } else if (reason.contains("时间") || reason.contains("延迟")) {
            return "TIMING_ISSUE";
        } else if (reason.contains("完整性") || reason.contains("缺失")) {
            return "COMPLETENESS_ISSUE";
        } else {
            return "OTHER";
        }
    }

    /**
     * 按分类记录验证失败统计
     */
    private void recordValidationFailureByCategory(String category, String symbol) {
        // 这里可以添加更详细的分类统计逻辑
        // 例如：按交易对、按失败类型等进行统计
        log.debug("验证失败分类统计: category={}, symbol={}", category, symbol);
    }
    
    /**
     * 记录存储操作开始
     */
    public StorageContext startStorageOperation(String storageType, String dataType) {
        return StorageContext.builder()
                .storageType(storageType)
                .dataType(dataType)
                .startTime(System.currentTimeMillis())
                .build();
    }
    
    /**
     * 记录存储操作成功
     */
    public void recordStorageSuccess(StorageContext context) {
        long storageTime = System.currentTimeMillis() - context.getStartTime();
        
        StorageMetrics metrics = storageMetrics.get(context.getStorageType());
        if (metrics != null) {
            metrics.recordSuccess(storageTime);
        }
        
        log.debug("存储操作成功: storageType={}, dataType={}, 耗时={}ms", 
                context.getStorageType(), context.getDataType(), storageTime);
    }
    
    /**
     * 记录存储操作失败
     */
    public void recordStorageFailure(StorageContext context, Throwable error) {
        long storageTime = System.currentTimeMillis() - context.getStartTime();
        totalStorageFailed.increment();
        
        StorageMetrics metrics = storageMetrics.get(context.getStorageType());
        if (metrics != null) {
            metrics.recordFailure(storageTime);
        }
        
        log.error("存储操作失败: storageType={}, dataType={}, 耗时={}ms", 
                context.getStorageType(), context.getDataType(), storageTime, error);
    }
    
    /**
     * 记录重试操作
     */
    public void recordRetryAttempt(String operation, int attempt, String reason) {
        totalRetryAttempts.increment();
        log.info("重试操作: operation={}, attempt={}, reason={}", operation, attempt, reason);
    }
    
    /**
     * 获取性能统计信息
     */
    public PerformanceStatistics getStatistics() {
        long processed = totalProcessed.sum();
        long processingTime = totalProcessingTime.sum();
        
        return PerformanceStatistics.builder()
                .totalProcessed(processed)
                .totalValidationFailed(totalValidationFailed.sum())
                .totalStorageFailed(totalStorageFailed.sum())
                .totalRetryAttempts(totalRetryAttempts.sum())
                .averageProcessingTime(processed > 0 ? (double) processingTime / processed : 0.0)
                .maxProcessingTime(maxProcessingTime.get())
                .minProcessingTime(minProcessingTime.get() == Long.MAX_VALUE ? 0 : minProcessingTime.get())
                .successRate(calculateSuccessRate())
                .throughputPerSecond(calculateThroughput())
                .storageMetrics(new ConcurrentHashMap<>(storageMetrics))
                .dataTypeMetrics(new ConcurrentHashMap<>(dataTypeMetrics))
                .build();
    }
    
    /**
     * 定期报告性能指标
     */
    private void reportMetrics() {
        try {
            PerformanceStatistics stats = getStatistics();
            
            log.info("=== 性能监控报告 ===");
            log.info("总处理数量: {}", stats.getTotalProcessed());
            log.info("验证失败数量: {}", stats.getTotalValidationFailed());
            log.info("存储失败数量: {}", stats.getTotalStorageFailed());
            log.info("重试次数: {}", stats.getTotalRetryAttempts());
            log.info("成功率: {:.2f}%", stats.getSuccessRate());
            log.info("吞吐量: {:.2f} 条/秒", stats.getThroughputPerSecond());
            log.info("平均处理时间: {:.2f}ms", stats.getAverageProcessingTime());
            log.info("最大处理时间: {}ms", stats.getMaxProcessingTime());
            log.info("最小处理时间: {}ms", stats.getMinProcessingTime());
            
            // 存储指标
            stats.getStorageMetrics().forEach((storage, metrics) -> {
                log.info("存储[{}] - 成功: {}, 失败: {}, 平均耗时: {:.2f}ms", 
                        storage, metrics.getSuccessCount(), metrics.getFailureCount(), 
                        metrics.getAverageTime());
            });
            
            // 数据类型指标
            stats.getDataTypeMetrics().forEach((dataType, metrics) -> {
                log.info("数据类型[{}] - 处理: {}, 验证失败: {}", 
                        dataType, metrics.getProcessedCount(), metrics.getValidationFailedCount());
            });
            
        } catch (Exception e) {
            log.error("性能监控报告失败", e);
        }
    }
    
    /**
     * 更新处理时间范围
     */
    private void updateProcessingTimeRange(long processingTime) {
        // 更新最大值
        maxProcessingTime.updateAndGet(current -> Math.max(current, processingTime));
        
        // 更新最小值
        minProcessingTime.updateAndGet(current -> Math.min(current, processingTime));
    }
    
    /**
     * 计算成功率
     */
    private double calculateSuccessRate() {
        long total = totalProcessed.sum();
        if (total == 0) {
            return 100.0;
        }
        
        long failed = totalValidationFailed.sum() + totalStorageFailed.sum();
        return ((double) (total - failed) / total) * 100.0;
    }
    
    /**
     * 计算吞吐量（每秒处理数量）
     */
    private double calculateThroughput() {
        String currentWindow = getCurrentWindowKey();
        WindowMetrics metrics = windowMetrics.get(currentWindow);
        return metrics != null ? metrics.getCount() / 60.0 : 0.0; // 每分钟转换为每秒
    }
    
    /**
     * 获取当前时间窗口键（分钟级别）
     */
    private String getCurrentWindowKey() {
        LocalDateTime now = LocalDateTime.now();
        return String.format("%04d%02d%02d_%02d%02d", 
                now.getYear(), now.getMonthValue(), now.getDayOfMonth(),
                now.getHour(), now.getMinute());
    }
    
    @PreDestroy
    public void shutdown() {
        log.info("关闭性能监控器...");
        if (monitorExecutor != null) {
            monitorExecutor.shutdown();
            try {
                if (!monitorExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                    monitorExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                monitorExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        log.info("性能监控器已关闭");
    }
    
    // 内部类定义
    
    @Data
    @Builder
    public static class ProcessingContext {
        private String dataType;
        private String symbol;
        private long startTime;
    }
    
    @Data
    @Builder
    public static class StorageContext {
        private String storageType;
        private String dataType;
        private long startTime;
    }
    
    @Data
    @Builder
    public static class PerformanceStatistics {
        private long totalProcessed;
        private long totalValidationFailed;
        private long totalStorageFailed;
        private long totalRetryAttempts;
        private double averageProcessingTime;
        private long maxProcessingTime;
        private long minProcessingTime;
        private double successRate;
        private double throughputPerSecond;
        private ConcurrentHashMap<String, StorageMetrics> storageMetrics;
        private ConcurrentHashMap<String, DataTypeMetrics> dataTypeMetrics;
    }
    
    public static class StorageMetrics {
        private final LongAdder successCount = new LongAdder();
        private final LongAdder failureCount = new LongAdder();
        private final LongAdder totalTime = new LongAdder();
        
        public void recordSuccess(long time) {
            successCount.increment();
            totalTime.add(time);
        }
        
        public void recordFailure(long time) {
            failureCount.increment();
            totalTime.add(time);
        }
        
        public long getSuccessCount() { return successCount.sum(); }
        public long getFailureCount() { return failureCount.sum(); }
        public double getAverageTime() {
            long total = successCount.sum() + failureCount.sum();
            return total > 0 ? (double) totalTime.sum() / total : 0.0;
        }
    }
    
    public static class DataTypeMetrics {
        private final LongAdder processedCount = new LongAdder();
        private final LongAdder validationFailedCount = new LongAdder();
        
        public void increment() { processedCount.increment(); }
        public void recordValidationFailure() { validationFailedCount.increment(); }
        
        public long getProcessedCount() { return processedCount.sum(); }
        public long getValidationFailedCount() { return validationFailedCount.sum(); }
    }
    
    public static class WindowMetrics {
        private final LongAdder count = new LongAdder();
        
        public void increment() { count.increment(); }
        public long getCount() { return count.sum(); }
    }
}
