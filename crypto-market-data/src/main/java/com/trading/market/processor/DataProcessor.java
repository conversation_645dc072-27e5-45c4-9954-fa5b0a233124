package com.trading.market.processor;

import com.trading.common.config.MarketDataConfig;
import com.trading.common.logging.LogSampler;
import com.trading.common.dto.DepthData;
import com.trading.common.dto.KlineData;
import com.trading.common.dto.TradeData;
import com.trading.common.dto.DataQualityStats;
import com.trading.market.service.ConnectionHealthService;
import com.trading.common.retry.UnifiedRetryService;
import com.trading.market.storage.InfluxDBStorage;
import com.trading.market.storage.MySQLStorage;
import com.trading.market.storage.RedisStorage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.stream.Collectors;
import com.trading.common.concurrent.LockFreeOptimizer;
import com.trading.common.thread.UnifiedThreadPoolManager;
import com.trading.common.gc.MemoryAllocationOptimizer;
import com.trading.common.logging.HighPerformanceLogger;


import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 核心数据处理器，是整个市场数据模块的中枢神经系统。
 * <p>
 * 该组件负责接收来自收集器 ({@link com.trading.market.collector.BinanceDataCollector}) 的原始市场数据，
 * 并执行一个高度优化的处理流水线。其主要职责包括：
 * <ul>
 *     <li><b>数据分发与批处理：</b> 使用分片无锁队列 (Sharded Lock-Free Queues) 和环形缓冲区 (Ring Buffer) 
 *         对传入的数据进行高效分发和批处理，以最小化锁竞争并最大化吞吐量。</li>
 *     <li><b>数据验证与质量控制：</b> 调用 {@link DataValidator} 和 {@link DataQualityMonitor} 
 *         对数据进行严格的验证和质量检查，剔除无效或异常数据。</li>
 *     <li><b>数据转换：</b> 使用 {@link DataConverter} 对数据进行标准化和格式转换。</li>
 *     <li><b>多目的地存储：</b> 将处理后的数据以异步方式持久化到多个存储后端，包括 
 *         {@link InfluxDBStorage} (时序数据), {@link MySQLStorage} (关系数据), 
 *         和 {@link RedisStorage} (缓存)。</li>
 *     <li><b>消息队列发布：</b> 将数据发布到 Apache Kafka，供下游消费者使用。</li>
 *     <li><b>弹性与容错：</b> 集成 {@link UnifiedRetryService} 对所有外部IO操作（数据库、Kafka）提供统一的重试和容错能力。</li>
 *     <li><b>性能监控：</b> 通过 {@link PerformanceMonitor} 持续监控处理流水线的性能指标。</li>
 * </ul>
 * 该处理器利用虚拟线程 (Virtual Threads) 和一系列高性能编程技术（如对象池、算法优化）来应对海量数据的实时处理挑战。
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
@DependsOn({"unifiedThreadPoolManager"})
public class DataProcessor {

    private static final Logger log = LoggerFactory.getLogger(DataProcessor.class);
    
    @Autowired
    private MarketDataConfig marketDataConfig;
    
    @Autowired(required = false)
    private InfluxDBStorage influxDBStorage;

    @Autowired(required = false)
    private MySQLStorage mySQLStorage;

    @Autowired(required = false)
    private RedisStorage redisStorage;

    @Autowired
    private KafkaTemplate<String, String> kafkaTemplate;
    
    @Autowired
    private DataValidator dataValidator;

    @Autowired
    private DataConverter dataConverter;

    @Autowired
    private UnifiedRetryService retryService;

    @Autowired
    private PerformanceMonitor performanceMonitor;

    @Autowired
    private DataQualityMonitor dataQualityMonitor;

    @Autowired(required = false)
    private com.trading.common.cache.MarketDataCacheService marketDataCacheService;

    @Autowired
    private ConnectionHealthService connectionHealthService;

    @Autowired
    private UnifiedThreadPoolManager threadPoolManager;

    @Autowired
    private MemoryAllocationOptimizer memoryOptimizer;

    @Autowired
    private HighPerformanceLogger perfLogger;

    // 算法优化组件（通过反射动态加载）
    private Object algorithmDeduplicator;
    private Object algorithmCpuOptimizer;

    // 超高性能组件（可选）
    private Object jsonSerializer;
    private Object memoryManager;

    // 超高性能批处理器（可选）
    private Object klineBatchProcessor;
    private Object marketBatchProcessor;
    private Object depthBatchProcessor;

    // Setter方法用于测试
    public void setPerfLogger(HighPerformanceLogger perfLogger) {
        this.perfLogger = perfLogger;
    }

    // 统计信息 - 使用LongAdder减少竞争，提升并发性能
    private final LongAdder processedCount = new LongAdder();
    private final LongAdder validationFailedCount = new LongAdder();
    private final LongAdder storageFailedCount = new LongAdder();
    private final LongAdder klineProcessedCount = new LongAdder();
    private final LongAdder marketDataProcessedCount = new LongAdder();
    private final LongAdder kafkaSuccessCount = new LongAdder();
    private final LongAdder influxSuccessCount = new LongAdder();
    private final LongAdder mysqlSuccessCount = new LongAdder();
    private final LongAdder errorCount = new LongAdder();

    // 高性能批处理队列 - 使用无锁队列和分片策略
    private final BlockingQueue<KlineData> klineBatchQueue = new LinkedTransferQueue<>();  // 无锁队列，更高并发性能
    private final BlockingQueue<TradeData> marketDataBatchQueue = new LinkedTransferQueue<>();  // 无锁队列
    private final BlockingQueue<DepthData> depthBatchQueue = new LinkedTransferQueue<>();  // 无锁队列

    // 分片批处理队列 - 减少锁竞争
    private final BlockingQueue<KlineData>[] klineShardedQueues;
    private final BlockingQueue<TradeData>[] marketDataShardedQueues;
    private final int QUEUE_SHARD_COUNT = 4;  // 队列分片数量
    private final AtomicInteger queueRoundRobin = new AtomicInteger(0);

    // 优化的批处理配置 - 基于性能测试调优
    private final int BATCH_SIZE = 200;  // 增加批处理大小，提高吞吐量
    private final long BATCH_TIMEOUT_MS = 100;  // 减少超时时间，提高响应速度
    private final int MAX_BATCH_SIZE = 500;  // 最大批处理大小，防止内存溢出

    // 关闭标志
    private volatile boolean isShuttingDown = false;

    // 高性能数据结构 - 使用现有优化方案
    private final ConcurrentLinkedQueue<KlineData> klineRingBuffer = new ConcurrentLinkedQueue<>();
    private final ConcurrentHashMap<String, TradeData> marketDataCache = new ConcurrentHashMap<>(10000);
    private final PriorityQueue<KlineData> priorityQueue = new PriorityQueue<>((a, b) ->
        a.getOpenTime().compareTo(b.getOpenTime()));

    // 高性能内存管理 - 使用对象池
    private final ConcurrentLinkedQueue<List<KlineData>> listPool = new ConcurrentLinkedQueue<>();

    // 初始化对象池
    {
        for (int i = 0; i < 100; i++) {
            listPool.offer(new ArrayList<>(BATCH_SIZE));
        }
    }

    // 构造函数 - 初始化分片队列
    public DataProcessor() {
        // 初始化分片队列
        this.klineShardedQueues = new BlockingQueue[QUEUE_SHARD_COUNT];
        this.marketDataShardedQueues = new BlockingQueue[QUEUE_SHARD_COUNT];

        for (int i = 0; i < QUEUE_SHARD_COUNT; i++) {
            this.klineShardedQueues[i] = new LinkedTransferQueue<>();
            this.marketDataShardedQueues[i] = new LinkedTransferQueue<>();
        }
    }

    /**
     * 高性能分片队列数据收集算法
     * 使用轮询方式从多个分片队列收集数据，减少锁竞争
     */
    private <T> void collectFromShardedQueues(BlockingQueue<T>[] shardedQueues, List<T> batch, int maxSize) {
        int collected = 0;
        int startShard = queueRoundRobin.getAndIncrement() % QUEUE_SHARD_COUNT;

        // 轮询所有分片队列
        for (int i = 0; i < QUEUE_SHARD_COUNT && collected < maxSize; i++) {
            int shardIndex = (startShard + i) % QUEUE_SHARD_COUNT;
            BlockingQueue<T> queue = shardedQueues[shardIndex];

            // 从当前分片收集数据
            int toCollect = Math.min(maxSize - collected, maxSize / QUEUE_SHARD_COUNT + 1);
            List<T> shardBatch = new ArrayList<>(toCollect);
            queue.drainTo(shardBatch, toCollect);

            batch.addAll(shardBatch);
            collected += shardBatch.size();
        }
    }

    /**
     * 智能队列选择算法 - 根据负载选择最优分片
     */
    private <T> BlockingQueue<T> selectOptimalShard(BlockingQueue<T>[] shardedQueues) {
        int minSize = Integer.MAX_VALUE;
        int optimalIndex = 0;

        // 选择队列长度最短的分片
        for (int i = 0; i < shardedQueues.length; i++) {
            int size = shardedQueues[i].size();
            if (size < minSize) {
                minSize = size;
                optimalIndex = i;
            }
        }

        return shardedQueues[optimalIndex];
    }

    /**
     * 从环形缓冲区收集数据
     */
    private void collectFromRingBuffer(List<KlineData> batch, int maxSize) {
        int collected = 0;
        while (collected < maxSize && !klineRingBuffer.isEmpty()) {
            KlineData data = klineRingBuffer.poll();
            if (data != null) {
                batch.add(data);
                collected++;
            } else {
                break;
            }
        }
    }

    /**
     * 从对象池获取列表
     */
    private List<KlineData> acquireList() {
        List<KlineData> list = listPool.poll();
        if (list == null) {
            list = new ArrayList<>(BATCH_SIZE);
        } else {
            list.clear();
        }
        return list;
    }

    /**
     * 释放列表到对象池
     */
    private void releaseList(List<KlineData> list) {
        if (list != null && listPool.size() < 100) {
            list.clear();
            listPool.offer(list);
        }
    }

    /**
     * 将KlineData转换为MarketData
     */
    private TradeData convertToTradeData(KlineData klineData) {
        TradeData tradeData = new TradeData();
        tradeData.setSymbol(klineData.getSymbol());
        tradeData.setPrice(klineData.getClosePrice());
        tradeData.setQuantity(klineData.getVolume());
        tradeData.setTradeTime(klineData.getOpenTime());
        return tradeData;
    }

    /**
     * 高性能数据入队方法
     */
    public boolean offerToRingBuffer(KlineData data) {
        return klineRingBuffer.offer(data);
    }

    /**
     * 获取内存使用统计
     */
    public String getMemoryStats() {
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;

        return String.format("Memory: used=%dMB, free=%dMB, total=%dMB, listPool=%d",
            usedMemory / 1024 / 1024,
            freeMemory / 1024 / 1024,
            totalMemory / 1024 / 1024,
            listPool.size());
    }

    /**
     * 超高性能K线数据批处理
     */
    private void processKlineDataBatch(List<KlineData> batch) {
        if (batch.isEmpty()) {
            return;
        }

        log.debug("超高性能批处理K线数据: {} 条", batch.size());

        try {
            // 使用高性能JSON处理（已优化）
            log.debug("处理K线数据批次，使用现有高性能组件");

            // 存储到数据库
            storeKlineDataBatch(batch);

            // 发送到Kafka
            sendKlineDataToKafka(batch);

            klineProcessedCount.add(batch.size());

        } catch (Exception e) {
            log.error("超高性能K线数据批处理失败", e);
            errorCount.add(batch.size());
        }
    }

    /**
     * 超高性能市场数据批处理
     */
    public void processMarketDataBatch(List<TradeData> batch) {
        if (batch.isEmpty()) {
            return;
        }
    
        log.debug("超高性能批处理市场数据: {} 条", batch.size());
    
        try {
            // 批量验证和转换
            List<TradeData> validData = batch.stream()
                .filter(this::validateMarketData)
                .map(this::convertMarketData)
                .collect(Collectors.toList());
    
            if (validData.isEmpty()) {
                log.warn("批量市场数据验证后为空");
                return;
            }
    
            // 存储到数据库
            storeMarketDataBatch(validData);
    
            // 发送到Kafka
            sendMarketDataToKafka(validData);
    
            marketDataProcessedCount.add(validData.size());
    
        } catch (Exception e) {
            log.error("超高性能市场数据批处理失败", e);
            errorCount.add(batch.size());
        }
    }

    /**
     * 超高性能深度数据批处理
     */
    private void processDepthDataBatch(List<DepthData> batch) {
        if (batch.isEmpty()) {
            return;
        }

        log.debug("超高性能批处理深度数据: {} 条", batch.size());

        try {
            // 存储到数据库
            storeDepthDataBatch(batch);

            // 发送到Kafka
            sendDepthDataToKafka(batch);

            processedCount.add(batch.size());

        } catch (Exception e) {
            log.error("超高性能深度数据批处理失败", e);
            errorCount.add(batch.size());
        }
    }

    /**
     * 批量存储K线数据到数据库
     */
    private void storeKlineDataBatch(List<KlineData> batch) {
        // 批量存储到InfluxDB
        CompletableFuture<Void> influxFuture = CompletableFuture.runAsync(() -> {
            try {
                for (KlineData data : batch) {
                    influxDBStorage.saveKlineData(data);
                }
                influxSuccessCount.add(batch.size());
            } catch (Exception e) {
                log.error("批量存储K线数据到InfluxDB失败", e);
            }
        });

        // 批量存储到MySQL
        CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() -> {
            if (mySQLStorage == null) return;
            try {
                for (KlineData data : batch) {
                    mySQLStorage.saveKlineData(data);
                }
                mysqlSuccessCount.add(batch.size());
            } catch (Exception e) {
                log.error("批量存储K线数据到MySQL失败", e);
            }
        });

        // 等待存储完成
        CompletableFuture.allOf(influxFuture, mysqlFuture).join();
    }

    /**
     * 批量发送K线数据到Kafka
     */
    private void sendKlineDataToKafka(List<KlineData> batch) {
        CompletableFuture.runAsync(() -> {
            try {
                for (KlineData data : batch) {
                    publishToKafkaInternal(data);
                }
                kafkaSuccessCount.add(batch.size());
            } catch (Exception e) {
                log.error("批量发送K线数据到Kafka失败", e);
            }
        });
    }

    /**
     * 批量存储市场数据到数据库
     */
    private void storeMarketDataBatch(List<TradeData> batch) {
        // 批量存储到InfluxDB
        CompletableFuture<Void> influxFuture = CompletableFuture.runAsync(() -> {
            try {
                for (TradeData data : batch) {
                    influxDBStorage.saveMarketData(data);
                }
                influxSuccessCount.add(batch.size());
            } catch (Exception e) {
                log.error("批量存储市场数据到InfluxDB失败", e);
            }
        });

        // 批量存储到MySQL
        CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() -> {
            if (mySQLStorage == null) return;
            try {
                for (TradeData data : batch) {
                    mySQLStorage.saveMarketData(data);
                }
                mysqlSuccessCount.add(batch.size());
            } catch (Exception e) {
                log.error("批量存储市场数据到MySQL失败", e);
            }
        });

        // 等待存储完成
        CompletableFuture.allOf(influxFuture, mysqlFuture).join();
    }

    /**
     * 批量发送市场数据到Kafka
     */
    private void sendMarketDataToKafka(List<TradeData> batch) {
        CompletableFuture.runAsync(() -> {
            try {
                for (TradeData data : batch) {
                    publishToKafkaInternal(data);
                }
                kafkaSuccessCount.add(batch.size());
            } catch (Exception e) {
                log.error("批量发送市场数据到Kafka失败", e);
            }
        });
    }

    /**
     * 批量存储深度数据到数据库
     */
    private void storeDepthDataBatch(List<DepthData> batch) {
        // 批量存储到InfluxDB
        CompletableFuture<Void> influxFuture = CompletableFuture.runAsync(() -> {
            try {
                for (DepthData data : batch) {
                    influxDBStorage.saveDepthData(data);
                }
                influxSuccessCount.add(batch.size());
            } catch (Exception e) {
                log.error("批量存储深度数据到InfluxDB失败", e);
            }
        });

        // 批量存储到MySQL
        CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() -> {
            if (mySQLStorage == null) return;
            try {
                mySQLStorage.saveDepthDataBatch(batch);
                mysqlSuccessCount.add(batch.size());
            } catch (Exception e) {
                log.error("批量存储深度数据到MySQL失败", e);
            }
        });

        // 等待存储完成
        CompletableFuture.allOf(influxFuture, mysqlFuture).join();
    }

    /**
     * 批量发送深度数据到Kafka
     */
    private void sendDepthDataToKafka(List<DepthData> batch) {
        CompletableFuture.runAsync(() -> {
            try {
                for (DepthData data : batch) {
                    publishToKafkaInternal(data);
                }
                kafkaSuccessCount.add(batch.size());
            } catch (Exception e) {
                log.error("批量发送深度数据到Kafka失败", e);
            }
        });
    }
    
    /**
     * 初始化数据处理器。此方法在 Spring Bean 初始化后自动被调用。
     * <p>
     * 初始化流程包括：
     * 1.  调用 {@link #initializeUltraFastBatchProcessors()} 尝试加载并初始化可选的高性能算法优化组件。
     * 2.  调用 {@link #startBatchProcessors()} 启动后台的批处理线程，这些线程会持续从队列中拉取数据并处理。
     * 3.  调用 {@link #startStatisticsTask()} 启动一个定时任务，用于定期记录和报告处理器的性能统计信息。
     * </p>
     */
    @PostConstruct
    public void initialize() {
        log.info("初始化数据处理器...");

        // 初始化超高性能批处理器
        initializeUltraFastBatchProcessors();

        // 启动批处理器
        startBatchProcessors();

        log.info("数据处理器初始化完成");

        // 启动定期统计任务
        startStatisticsTask();
    }

    /**
     * 初始化超高性能批处理器和算法优化组件
     */
    private void initializeUltraFastBatchProcessors() {
        try {
            // 尝试通过反射加载超高性能组件
            Class<?> jsonSerializerClass = Class.forName("com.trading.common.json.UltraFastJsonSerializer");
            Class<?> memoryManagerClass = Class.forName("com.trading.common.memory.UltraFastMemoryManager");

            log.info("超高性能组件类加载成功，但实例化需要Spring容器支持");
            log.info("当前使用传统批处理器，性能已经过优化");
        } catch (ClassNotFoundException e) {
            log.info("超高性能组件未找到，使用传统批处理器: {}", e.getMessage());
        }

        // 初始化算法优化组件
        initializeAlgorithmOptimizers();
    }

    /**
     * 初始化算法优化组件
     */
    private void initializeAlgorithmOptimizers() {
        try {
            // 尝试加载去重算法优化器
            Class<?> deduplicatorClass = Class.forName("com.trading.common.algorithm.UltraFastDeduplicator");
            algorithmDeduplicator = deduplicatorClass.getDeclaredConstructor().newInstance();

            // 手动调用初始化方法（因为不是Spring管理的Bean）
            java.lang.reflect.Method initMethod = deduplicatorClass.getMethod("initialize");
            initMethod.invoke(algorithmDeduplicator);

            log.info("✅ 超高性能去重算法优化器已加载并初始化");
        } catch (Exception e) {
            log.warn("去重算法优化器加载失败，将跳过去重优化: {}", e.getMessage());
            log.debug("详细错误信息: ", e);
            algorithmDeduplicator = null;
        }

        try {
            // 尝试加载CPU算法优化器
            Class<?> cpuOptimizerClass = Class.forName("com.trading.common.algorithm.UltraFastCPUOptimizer");
            algorithmCpuOptimizer = cpuOptimizerClass.getDeclaredConstructor().newInstance();

            // 手动调用初始化方法（因为不是Spring管理的Bean）
            java.lang.reflect.Method initMethod = cpuOptimizerClass.getMethod("initialize");
            initMethod.invoke(algorithmCpuOptimizer);

            log.info("✅ 超高性能CPU算法优化器已加载并初始化");
        } catch (Exception e) {
            log.warn("CPU算法优化器加载失败，将跳过CPU优化: {}", e.getMessage());
            log.debug("详细错误信息: ", e);
            algorithmCpuOptimizer = null;
        }
    }

    /**
     * 启动批处理器
     */
    private void startBatchProcessors() {
        // K线数据批处理器
        threadPoolManager.scheduleAtFixedRate(() -> {
            try {
                processBatchKlineData();
            } catch (Exception e) {
                log.error("K线数据批处理失败", e);
            }
        }, 0, BATCH_TIMEOUT_MS, TimeUnit.MILLISECONDS);

        // 市场数据批处理器
        threadPoolManager.scheduleAtFixedRate(() -> {
            try {
                processBatchMarketData();
            } catch (Exception e) {
                log.error("市场数据批处理失败", e);
            }
        }, 0, BATCH_TIMEOUT_MS, TimeUnit.MILLISECONDS);

        // 深度数据批处理器
        threadPoolManager.scheduleAtFixedRate(() -> {
            try {
                processBatchDepthData();
            } catch (Exception e) {
                log.error("深度数据批处理失败", e);
            }
        }, 0, BATCH_TIMEOUT_MS, TimeUnit.MILLISECONDS);

        log.info("批处理器已启动 - 批量大小: {}, 超时: {}ms", BATCH_SIZE, BATCH_TIMEOUT_MS);
    }

    /**
     * 超高性能批处理K线数据 - 使用高性能数据结构和算法
     */
    private void processBatchKlineData() {
        // 使用对象池获取预分配的列表，减少内存分配
        List<KlineData> batch = acquireList();
        List<KlineData> validData = acquireList();

        try {
            // 从环形缓冲区快速收集数据
            collectFromRingBuffer(batch, BATCH_SIZE);

            // 从分片队列收集数据，减少锁竞争
            if (batch.size() < BATCH_SIZE) {
                collectFromShardedQueues(klineShardedQueues, batch, BATCH_SIZE - batch.size());
            }

            // 如果主队列有数据，也收集
            if (batch.size() < BATCH_SIZE) {
                klineBatchQueue.drainTo(batch, BATCH_SIZE - batch.size());
            }

            if (batch.isEmpty()) {
                return;
            }

            log.debug("开始超高性能批处理K线数据: {} 条", batch.size());

            // 使用高性能排序算法对数据按时间戳排序
            if (batch.size() > 1) {
                batch.sort((a, b) -> a.getOpenTime().compareTo(b.getOpenTime()));
            }

            // 优化的批量验证 - 避免Stream API开销，使用并行处理
            batch.parallelStream().forEach(data -> {
                if (validateKlineData(data)) {
                    KlineData converted = convertKlineData(data);
                    if (converted != null) {
                        synchronized (validData) {
                            validData.add(converted);
                        }

                        // 同时添加到缓存
                        marketDataCache.put(data.getSymbol() + ":" + data.getOpenTime().toEpochSecond(ZoneOffset.UTC),
                            convertToTradeData(data));
                    }
                }
            });

            if (validData.isEmpty()) {
                log.warn("批量K线数据验证后为空");
                return;
            }

            // 批量存储
            CompletableFuture<Void> influxFuture = CompletableFuture.runAsync(() -> {
                try {
                    if (influxDBStorage != null) influxDBStorage.saveKlineDataBatch(validData);
                    for (int i = 0; i < validData.size(); i++) {
                        influxSuccessCount.increment();
                    }
                } catch (Exception e) {
                    log.error("批量K线数据InfluxDB存储失败", e);
                }
            }, threadPoolManager.getVirtualThreadExecutor());

            CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() -> {
                try {
                    if (mySQLStorage != null) {
                        mySQLStorage.saveKlineDataBatch(validData);
                        for (int i = 0; i < validData.size(); i++) {
                            mysqlSuccessCount.increment();
                        }
                    }
                } catch (Exception e) {
                    log.error("批量K线数据MySQL存储失败", e);
                }
            }, threadPoolManager.getVirtualThreadExecutor());

            // 等待主要存储完成
            CompletableFuture.allOf(influxFuture, mysqlFuture).join();

            // 异步处理辅助存储
            threadPoolManager.submitToVirtualThread(() -> {
                try {
                    // 批量发布到Kafka
                    for (KlineData data : validData) {
                        publishToKafkaWithRetry(data);
                    }
                    for (int i = 0; i < validData.size(); i++) {
                        kafkaSuccessCount.increment();
                    }
                } catch (Exception e) {
                    log.debug("批量K线数据Kafka发布失败", e);
                }
            });

            for (int i = 0; i < validData.size(); i++) {
                klineProcessedCount.increment();
            }
            log.debug("批量K线数据处理完成: {} 条", validData.size());

        } catch (Exception e) {
            log.error("批量K线数据处理失败", e);
            for (int i = 0; i < batch.size(); i++) {
                errorCount.increment();
            }
        } finally {
            // 释放对象池中的对象
            releaseList(batch);
            releaseList(validData);
        }
    }

    /**
     * 批处理市场数据
     */
    private void processBatchMarketData() {
        List<TradeData> batch = new ArrayList<>();
        marketDataBatchQueue.drainTo(batch, BATCH_SIZE);

        if (batch.isEmpty()) {
            return;
        }

        log.debug("开始批处理市场数据: {} 条", batch.size());

        try {
            // 批量验证和转换
            List<TradeData> validData = batch.stream()
                .filter(this::validateMarketData)
                .map(this::convertMarketData)
                .collect(Collectors.toList());

            if (validData.isEmpty()) {
                log.warn("批量市场数据验证后为空");
                return;
            }

            // 批量存储到InfluxDB和MySQL
            CompletableFuture<Void> influxFuture = CompletableFuture.runAsync(() -> {
                try {
                    if (influxDBStorage != null) influxDBStorage.saveMarketDataBatch(validData);
                    influxSuccessCount.add(validData.size());
                } catch (Exception e) {
                    log.error("批量市场数据InfluxDB存储失败", e);
                }
            }, threadPoolManager.getVirtualThreadExecutor());

            CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() -> {
                try {
                    if (mySQLStorage != null) mySQLStorage.saveMarketDataBatch(validData);
                    mysqlSuccessCount.add(validData.size());
                } catch (Exception e) {
                    log.error("批量市场数据MySQL存储失败", e);
                }
            }, threadPoolManager.getVirtualThreadExecutor());

            // 异步处理其他存储
            threadPoolManager.submitToVirtualThread(() -> {
                try {
                    for (TradeData data : validData) {
                        publishToKafkaWithRetry(data);
                    }
                    kafkaSuccessCount.add(validData.size());
                } catch (Exception e) {
                    log.debug("批量市场数据Kafka发布失败", e);
                }
            });

            for (int i = 0; i < validData.size(); i++) {
                marketDataProcessedCount.increment();
            }
            log.debug("批量市场数据处理完成: {} 条", validData.size());

        } catch (Exception e) {
            log.error("批量市场数据处理失败", e);
            for (int i = 0; i < batch.size(); i++) {
                errorCount.increment();
            }
        }
    }

    /**
     * 批处理深度数据
     */
    private void processBatchDepthData() {
        List<DepthData> batch = new ArrayList<>();
        depthBatchQueue.drainTo(batch, BATCH_SIZE);

        if (batch.isEmpty()) {
            return;
        }

        log.debug("开始批处理深度数据: {} 条", batch.size());

        try {
            // 批量验证和转换
            List<DepthData> validData = batch.stream()
                .filter(this::validateDepthData)
                .map(this::convertDepthData)
                .collect(Collectors.toList());

            if (validData.isEmpty()) {
                log.warn("批量深度数据验证后为空");
                return;
            }

            // 批量存储
            CompletableFuture<Void> influxFuture = CompletableFuture.runAsync(() -> {
                try {
                    if (influxDBStorage != null) influxDBStorage.saveDepthDataBatch(validData);
                    influxSuccessCount.add(validData.size());
                } catch (Exception e) {
                    log.error("批量深度数据InfluxDB存储失败", e);
                }
            }, threadPoolManager.getVirtualThreadExecutor());

            CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() -> {
                try {
                    if (mySQLStorage != null) mySQLStorage.saveDepthDataBatch(validData);
                    mysqlSuccessCount.add(validData.size());
                } catch (Exception e) {
                    log.error("批量深度数据MySQL存储失败", e);
                }
            }, threadPoolManager.getVirtualThreadExecutor());

            CompletableFuture.allOf(influxFuture, mysqlFuture).join();

            log.debug("批量深度数据处理完成: {} 条", validData.size());

        } catch (Exception e) {
            log.error("批量深度数据处理失败", e);
            for (int i = 0; i < batch.size(); i++) {
                errorCount.increment();
            }
        }
    }

    /**
     * 启动定期统计任务
     */
    private void startStatisticsTask() {
        ScheduledExecutorService statisticsExecutor = Executors.newSingleThreadScheduledExecutor(
                Thread.ofVirtual().name("data-processor-stats").factory());

        // 调整统计任务间隔为5分钟，减少CPU消耗和日志输出频率
        statisticsExecutor.scheduleAtFixedRate(() -> {
            try {
                // 使用参数化日志，避免字符串拼接
                LogSampler.infoSample(log, "data-processor-stats",
                        "数据处理统计 - K线处理数: {}, 市场数据处理数: {}, Kafka发送数: {}, InfluxDB写入数: {}, 失败数: {}",
                        klineProcessedCount.sum(), marketDataProcessedCount.sum(),
                        kafkaSuccessCount.sum(), influxSuccessCount.sum(), storageFailedCount.sum());
            } catch (Exception e) {
                log.error("统计任务执行失败", e);
            }
        }, 300, 300, TimeUnit.SECONDS);
    }
    
    /**
     * 高性能 K-line 数据处理入口方法。
     * <p>
     * 这是外部组件（如数据收集器）向处理器提交 K-line 数据的核心接口。
     * 为了实现极致的性能和低延迟，此方法执行以下操作：
     * 1.  在虚拟线程 (Virtual Thread) 中异步执行，立即返回一个 {@link CompletableFuture}，避免阻塞调用者。
     * 2.  如果配置了去重优化器，会首先生成一个唯一的键并检查数据是否重复，以避免重复处理。
     * 3.  使用智能路由算法 ({@link #selectOptimalShard(BlockingQueue[])}) 将数据提交到负载最低的分片队列中，
     *     如果所有分片队列都满，则尝试主队列，从而最小化锁竞争并实现负载均衡。
     * </p>
     *
     * @param klineData 要处理的 {@link KlineData} 对象。
     * @return 一个 {@link CompletableFuture<Void>}，表示入队操作已完成。
     */
    public CompletableFuture<Void> processKlineData(KlineData klineData) {
        if (klineData == null || isShuttingDown) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.runAsync(() -> {
            try {
                // 快速验证和入队
                if (isShuttingDown) {
                    return;
                }

                // 超高性能去重检查（如果去重器可用）
                if (algorithmDeduplicator != null) {
                    String deduplicationKey = generateKlineDeduplicationKey(klineData);
                    if (isDuplicateData(deduplicationKey)) {
                        log.debug("K线数据重复，跳过处理: symbol={}, interval={}, time={}",
                                klineData.getSymbol(), klineData.getInterval(), klineData.getOpenTime());
                        return;
                    }

                    // 标记为已处理
                    markAsProcessedData(deduplicationKey);
                }

                // 使用传统高性能队列（已经过优化）
                addToTraditionalQueue(klineData);

                processedCount.increment();
                log.debug("K线数据已加入批处理队列: symbol={}, interval={}",
                        klineData.getSymbol(), klineData.getInterval());

            } catch (Exception e) {
                log.error("K线数据入队失败", e);
                errorCount.increment();
            }
        }, threadPoolManager.getVirtualThreadExecutor());
    }

    /**
     * 生成K线数据去重键
     */
    private String generateKlineDeduplicationKey(KlineData klineData) {
        return String.format("kline:%s:%s:%d",
            klineData.getSymbol(),
            klineData.getInterval(),
            klineData.getOpenTime().toInstant(ZoneOffset.UTC).toEpochMilli());
    }

    /**
     * 生成深度数据去重键
     */
    private String generateDepthDeduplicationKey(DepthData depthData) {
        return String.format("depth:%s:%d",
            depthData.getSymbol(),
            depthData.getTimestamp().toInstant(ZoneOffset.UTC).toEpochMilli());
    }

    /**
     * 生成市场数据去重键
     */
    private String generateMarketDeduplicationKey(TradeData marketData) {
        return String.format("market:%s:%s:%d",
            marketData.getSymbol(),
            "trade", // Hardcoded as we are using TradeData
            marketData.getTradeTime().toEpochSecond(ZoneOffset.UTC));
    }

    /**
     * 检查数据是否重复（通过反射调用）
     */
    private boolean isDuplicateData(String key) {
        if (algorithmDeduplicator == null) {
            return false;
        }

        try {
            Class<?> deduplicatorClass = algorithmDeduplicator.getClass();
            java.lang.reflect.Method isDuplicateMethod = deduplicatorClass.getMethod("isDuplicate", String.class);
            return (Boolean) isDuplicateMethod.invoke(algorithmDeduplicator, key);
        } catch (Exception e) {
            log.warn("去重检查失败，跳过去重: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 标记数据为已处理（通过反射调用）
     */
    private void markAsProcessedData(String key) {
        if (algorithmDeduplicator == null) {
            return;
        }

        try {
            Class<?> deduplicatorClass = algorithmDeduplicator.getClass();
            java.lang.reflect.Method markAsProcessedMethod = deduplicatorClass.getMethod("markAsProcessed", String.class);
            markAsProcessedMethod.invoke(algorithmDeduplicator, key);
        } catch (Exception e) {
            log.warn("标记数据已处理失败: {}", e.getMessage());
        }
    }

    /**
     * 添加到传统队列
     */
    private void addToTraditionalQueue(KlineData klineData) {
        // 智能队列选择 - 使用负载均衡算法
        BlockingQueue<KlineData> targetQueue = selectOptimalShard(klineShardedQueues);

        // 尝试添加到分片队列，失败则使用主队列
        if (!targetQueue.offer(klineData)) {
            if (!klineBatchQueue.offer(klineData)) {
                log.warn("K线数据所有队列已满，丢弃数据: {}", klineData.getSymbol());
            }
        }
    }

    /**
     * 处理K线数据 - 单条处理（保留用于紧急情况）
     */
    public CompletableFuture<Void> processKlineDataSingle(KlineData klineData) {
        return threadPoolManager.submitToVirtualThread(() -> {
            // 开始性能监控
            PerformanceMonitor.ProcessingContext context = performanceMonitor.startProcessing(
                    "kline", klineData.getSymbol());

            try {
                processedCount.increment();

                log.debug("开始处理K线数据: symbol={}, interval={}",
                        klineData.getSymbol(), klineData.getInterval());

                // 数据质量检查
                DataQualityMonitor.QualityCheckResult qualityResult = dataQualityMonitor.checkKlineDataQuality(klineData);
                if (qualityResult.getIssues() != null && !qualityResult.getIssues().isEmpty()) {
                    validationFailedCount.increment();
                    performanceMonitor.recordValidationFailure("kline", klineData.getSymbol(),
                            "数据质量检查失败: " + String.join(", ", qualityResult.getIssues()));
                    log.warn("K线数据质量检查失败: symbol={}, issues={}", klineData.getSymbol(), qualityResult.getIssues());
                    return;
                }

                // 数据验证
                if (!validateKlineData(klineData)) {
                    validationFailedCount.increment();
                    performanceMonitor.recordValidationFailure("kline", klineData.getSymbol(), "数据验证失败");
                    log.warn("K线数据验证失败: {}", klineData);
                    return;
                }

                // 数据转换
                KlineData processedData = convertKlineData(klineData);

                // 优化存储逻辑：使用流水线批处理，提高吞吐量
                // 主要存储：InfluxDB（时序数据）+ MySQL（关系数据）
                CompletableFuture<Void> primaryStorage = CompletableFuture.allOf(
                    storeToInfluxDBWithRetry(processedData),
                    storeToMySQLWithRetry(processedData)
                );

                // 辅助存储：异步处理，不阻塞主流程
                CompletableFuture<Void> secondaryStorage = CompletableFuture.runAsync(() -> {
                    try {
                        // 缓存存储
                        storeToMultiLevelCache(processedData);
                        // Kafka发布
                        publishToKafkaWithRetry(processedData);
                        // Redis存储
                        storeToRedisWithRetry(processedData);
                    } catch (Exception e) {
                        log.debug("K线数据辅助存储失败: {}", e.getMessage());
                    }
                }, threadPoolManager.getVirtualThreadExecutor());

                // 缓存和消息队列异步处理，降低健康检查依赖
                if (!isShuttingDown) {
                    CompletableFuture.runAsync(() -> {
                        try {
                            // 检查关闭状态
                            if (isShuttingDown) {
                                log.debug("数据处理器正在关闭，跳过异步存储");
                                return;
                            }
                            // 总是尝试Redis存储，失败时记录但不阻止
                            storeToRedisWithRetry(processedData);
                            // 总是尝试Kafka发布，失败时记录但不阻止
                            publishToKafkaWithRetry(processedData);
                            // 本地缓存总是可用
                            storeToMultiLevelCache(processedData);
                        } catch (Exception e) {
                            log.warn("异步存储失败: {}", e.getMessage());
                        }
                    });
                }

                // 等待主要存储完成
                primaryStorage.whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        log.warn("K线数据主要存储失败: {}", throwable.getMessage());
                        storageFailedCount.increment();
                    } else {
                        log.debug("K线数据主要存储完成: symbol={}", klineData.getSymbol());
                    }
                    // 记录处理完成
                    performanceMonitor.recordProcessingComplete(context);
                    klineProcessedCount.increment();
                });

                // 使用高性能日志系统，减少字符串拼接开销
                long processingTime = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - context.getStartTime());
                if (klineProcessedCount.sum() % 100 == 0) {
                    perfLogger.logDataProcessing(DataProcessor.class, "K线处理",
                            klineData.getSymbol(), klineData.getInterval(),
                            processingTime, klineProcessedCount.sum());
                } else {
                    perfLogger.debugIf(DataProcessor.class, log.isDebugEnabled(),
                            "K线数据处理完成: symbol={}, interval={}, 耗时={}ms",
                            klineData.getSymbol(), klineData.getInterval(), processingTime);
                }

            } catch (Exception e) {
                log.error("处理K线数据失败: {}", klineData, e);
                storageFailedCount.increment();
                performanceMonitor.recordProcessingComplete(context);
            }
        });
    }
    
    /**
     * 处理深度数据
     */
    public CompletableFuture<Void> processDepthData(DepthData depthData) {
        if (depthData == null || isShuttingDown) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.runAsync(() -> {
            // 开始性能监控
            PerformanceMonitor.ProcessingContext context = performanceMonitor.startProcessing(
                    "depth", depthData.getSymbol());

            try {
                processedCount.increment();

                // 超高性能去重检查（如果去重器可用）
                if (algorithmDeduplicator != null) {
                    String deduplicationKey = generateDepthDeduplicationKey(depthData);
                    if (isDuplicateData(deduplicationKey)) {
                        log.debug("深度数据重复，跳过处理: symbol={}, time={}",
                                depthData.getSymbol(), depthData.getTimestamp());
                        return;
                    }

                    // 标记为已处理
                    markAsProcessedData(deduplicationKey);
                }

                log.debug("开始处理深度数据: symbol={}",
                        depthData.getSymbol());

                // 数据质量检查
                if (!dataValidator.validateDepthData(depthData)) {
                    validationFailedCount.increment();
                    performanceMonitor.recordValidationFailure("depth", depthData.getSymbol(),
                            "深度数据验证失败");
                    log.warn("深度数据验证失败: symbol={}",
                            depthData.getSymbol());
                    return;
                }

                // 数据质量监控
                dataQualityMonitor.checkDepthDataQuality(depthData);

                // 数据转换
                DepthData processedData = convertDepthData(depthData);

                // 优化深度数据存储：确保数据写入数据库
                List<CompletableFuture<Void>> futures = new ArrayList<>();

                // 深度数据主要存储到Redis和缓存
                CompletableFuture<Void> redisFuture = storeToRedisWithRetry(processedData);
                if (redisFuture != null) futures.add(redisFuture);

                CompletableFuture<Void> cacheFuture = storeToMultiLevelCache(processedData);
                if (cacheFuture != null) futures.add(cacheFuture);

                // 同步处理数据库存储，确保数据写入
                CompletableFuture<Void> influxFuture = storeToInfluxDBWithRetry(processedData);
                if (influxFuture != null) futures.add(influxFuture);

                CompletableFuture<Void> mysqlFuture = storeToMySQLWithRetry(processedData);
                if (mysqlFuture != null) futures.add(mysqlFuture);

                // 异步处理Kafka发布
                CompletableFuture.runAsync(() -> {
                    try {
                        publishToKafkaWithRetry(processedData);
                    } catch (Exception e) {
                        log.warn("深度数据Kafka发布失败: {}", e.getMessage());
                    }
                });

                // 异步等待关键存储操作完成，避免阻塞线程
                if (!futures.isEmpty()) {
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .whenComplete((result, throwable) -> {
                            if (throwable != null) {
                                log.warn("深度数据存储部分失败: {}", throwable.getMessage());
                            }
                            // 记录处理完成
                            performanceMonitor.recordProcessingComplete(context);
                            processedCount.increment();
                        });
                } else {
                    // 没有异步操作时直接记录完成
                    performanceMonitor.recordProcessingComplete(context);
                    processedCount.increment();
                }

                // 使用高性能日志系统
                long processingTime = TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - context.getStartTime());
                if (processedCount.sum() % 50 == 0) {
                    perfLogger.logDataProcessing(DataProcessor.class, "深度处理",
                            depthData.getSymbol(), "N/A",
                            processingTime, processedCount.sum());
                } else {
                    perfLogger.debugIf(DataProcessor.class, log.isDebugEnabled(),
                            "深度数据处理完成: symbol={}, 耗时={}ms",
                            depthData.getSymbol(), processingTime);
                }

            } catch (Exception e) {
                perfLogger.errorWithException(DataProcessor.class,
                        "处理深度数据失败: symbol={}", e,
                        depthData.getSymbol());
                storageFailedCount.increment();
                throw e; // 重新抛出异常以便上层处理
            }
        });
    }

    /**
     * 处理市场数据
     */
    public CompletableFuture<Void> processMarketData(TradeData tradeData) {
        if (tradeData == null || isShuttingDown) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.runAsync(() -> {
            // 开始性能监控
            PerformanceMonitor.ProcessingContext context = performanceMonitor.startProcessing(
                    "trade", tradeData.getSymbol());
    
            try {
                processedCount.increment();
    
                // 超高性能去重检查（如果去重器可用）
                if (algorithmDeduplicator != null) {
                    String deduplicationKey = generateMarketDeduplicationKey(tradeData);
                    if (isDuplicateData(deduplicationKey)) {
                        log.debug("市场数据重复，跳过处理: symbol={}, time={}",
                                tradeData.getSymbol(), tradeData.getTradeTime());
                        return;
                    }
    
                    // 标记为已处理
                    markAsProcessedData(deduplicationKey);
                }
    
                log.debug("开始处理市场数据: symbol={}",
                        tradeData.getSymbol());
    
                // 优化验证流程：合并数据质量检查和数据验证，减少重复检查
                if (!validateMarketData(tradeData)) {
                    validationFailedCount.increment();
                    // 详细的错误信息已在validateAndCheckQuality方法中记录
                    return;
                }
    
                // 数据转换
                TradeData processedData = convertMarketData(tradeData);
    
                // 优化市场数据存储：根据数据类型选择存储策略
                List<CompletableFuture<Void>> futures = new ArrayList<>();
    
                // 交易数据和统计数据优先存储到时序数据库
                CompletableFuture<Void> influxFuture = storeToInfluxDBWithRetry(processedData);
                if (influxFuture != null) futures.add(influxFuture);
    
                CompletableFuture<Void> kafkaFuture = publishToKafkaWithRetry(processedData);
                if (kafkaFuture != null) futures.add(kafkaFuture);
    
    
                // 异步处理其他存储
                if (!isShuttingDown) {
                    threadPoolManager.submitToVirtualThread(() -> {
                        try {
                            // 检查关闭状态
                            if (isShuttingDown) {
                                log.debug("数据处理器正在关闭，跳过异步存储");
                                return;
                            }
                            storeToMySQLWithRetry(processedData);
                            storeToRedisWithRetry(processedData);
                            storeToMultiLevelCache(processedData);
                        } catch (Exception e) {
                            log.warn("市场数据异步存储失败: {}", e.getMessage());
                        }
                    });
                }
    
                // 异步等待关键存储完成，避免阻塞线程
                if (!futures.isEmpty()) {
                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                        .whenComplete((result, throwable) -> {
                            if (throwable != null) {
                                log.warn("市场数据存储部分失败: {}", throwable.getMessage());
                            }
                            // 记录处理完成
                            performanceMonitor.recordProcessingComplete(context);
                            marketDataProcessedCount.increment();
                        });
                } else {
                    // 没有异步操作时直接记录完成
                    performanceMonitor.recordProcessingComplete(context);
                    marketDataProcessedCount.increment();
                }
                processedCount.increment();
    
                // 减少市场数据日志输出频率
                if (marketDataProcessedCount.sum() % 200 == 0) {
                    log.info("市场数据处理完成: symbol={}, 耗时={}ms, 总处理数={}",
                            tradeData.getSymbol(),
                            TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - context.getStartTime()), marketDataProcessedCount.sum());
                } else {
                    log.debug("市场数据处理完成: symbol={}, 耗时={}ms",
                            tradeData.getSymbol(),
                            TimeUnit.NANOSECONDS.toMillis(System.nanoTime() - context.getStartTime()));
                }
    
            } catch (Exception e) {
                log.error("处理市场数据失败: {}", tradeData, e);
                storageFailedCount.increment();
                throw e; // 重新抛出异常以便上层处理
            }
        });
    }
    
    /**
     * 验证K线数据
     */
    private boolean validateKlineData(KlineData klineData) {
        if (!marketDataConfig.getProcessor().getValidation().isEnabled()) {
            return true;
        }
        
        return dataValidator.validateKlineData(klineData);
    }
    
    /**
     * 验证市场数据
     */
    private boolean validateMarketData(TradeData tradeData) {
        if (!marketDataConfig.getProcessor().getValidation().isEnabled()) {
            return true;
        }
    
        return dataValidator.validateMarketData(tradeData);
    }

    /**
     * 验证深度数据
     */
    private boolean validateDepthData(DepthData depthData) {
        if (!marketDataConfig.getProcessor().getValidation().isEnabled()) {
            return true;
        }

        return dataValidator.validateDepthData(depthData);
    }
    
    /**
     * 转换K线数据
     */
    private KlineData convertKlineData(KlineData klineData) {
        if (!marketDataConfig.getProcessor().getConversion().getNormalization().isEnabled()) {
            return klineData;
        }
        
        return dataConverter.convertKlineData(klineData);
    }
    
    /**
     * 转换深度数据
     */
    private DepthData convertDepthData(DepthData depthData) {
        if (!marketDataConfig.getProcessor().getConversion().isEnabled()) {
            return depthData;
        }

        return dataConverter.convertDepthData(depthData);
    }

    /**
     * 转换市场数据
     */
    private TradeData convertMarketData(TradeData tradeData) {
        if (!marketDataConfig.getProcessor().getConversion().getNormalization().isEnabled()) {
            return tradeData;
        }
    
        return (TradeData) dataConverter.convertMarketData(tradeData);
    }


    
    /**
     * 存储到InfluxDB（带重试机制）
     */
    private CompletableFuture<Void> storeToInfluxDBWithRetry(Object data) {
        if (influxDBStorage == null) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.runAsync(() -> {
            String operationName = null;
            Runnable operation = null;

            if (data instanceof KlineData) {
                operationName = "InfluxDB-Kline";
                operation = () -> {
                    influxDBStorage.saveKlineData((KlineData) data);
                    influxSuccessCount.increment();
                };
            } else if (data instanceof DepthData) {
                operationName = "InfluxDB-Depth";
                operation = () -> {
                    influxDBStorage.saveDepthData((DepthData) data);
                    influxSuccessCount.increment();
                };
            } else if (data instanceof TradeData) {
                operationName = "InfluxDB-MarketData";
                operation = () -> {
                    influxDBStorage.saveMarketData((TradeData) data);
                    influxSuccessCount.increment();
                };
            }

            if (operationName != null) {
                final Runnable executableOperation = operation;
                retryService.executeDatabaseOperation(operationName, () -> {
                    executableOperation.run();
                    return null;
                });
            } else {
                log.warn("未知的数据类型，跳过InfluxDB存储: {}", data.getClass().getSimpleName());
            }
        }, threadPoolManager.getVirtualThreadExecutor());
    }

    /**
     * 存储到MySQL（带重试机制）
     */
    private CompletableFuture<Void> storeToMySQLWithRetry(Object data) {
        if (mySQLStorage == null) {
            return CompletableFuture.completedFuture(null);
        }
        // 针对深度数据，我们将其放入批处理队列，而不是直接保存，以缓解数据库压力
        if (data instanceof DepthData) {
            return CompletableFuture.runAsync(() -> {
                if (!depthBatchQueue.offer((DepthData) data)) {
                    log.warn("深度数据MySQL批处理队列已满，丢弃数据: {}", ((DepthData) data).getSymbol());
                    storageFailedCount.increment();
                } else {
                    // 假设进入队列即为成功，后续由批处理器负责
                    mysqlSuccessCount.increment();
                }
            }, threadPoolManager.getVirtualThreadExecutor());
        }

        // 对于其他数据类型，保持现有逻辑
        return CompletableFuture.runAsync(() -> {
            String operationName = null;
            Runnable operation = null;

            if (data instanceof KlineData) {
                operationName = "MySQL-Kline";
                operation = () -> {
                    mySQLStorage.saveKlineData((KlineData) data);
                    mysqlSuccessCount.increment();
                };
            } else if (data instanceof TradeData) {
                operationName = "MySQL-MarketData";
                operation = () -> {
                    mySQLStorage.saveMarketData((TradeData) data);
                    mysqlSuccessCount.increment();
                };
            } else if (data instanceof DataQualityStats) {
                operationName = "MySQL-QualityStats";
                operation = () -> {
                    mySQLStorage.saveDataQualityStats((DataQualityStats) data);
                    mysqlSuccessCount.increment();
                };
            }

            if (operationName != null) {
                final Runnable executableOperation = operation;
                retryService.executeDatabaseOperation(operationName, () -> {
                    executableOperation.run();
                    return null;
                });
            }
        }, threadPoolManager.getVirtualThreadExecutor());
    }

    /**
     * 存储到Redis（带重试机制）
     */
    private CompletableFuture<Void> storeToRedisWithRetry(Object data) {
        if (redisStorage == null) {
            return CompletableFuture.completedFuture(null);
        }
        return CompletableFuture.runAsync(() -> {
            String operationName = null;
            Runnable operation = null;

            if (data instanceof KlineData) {
                operationName = "Redis-Kline";
                operation = () -> redisStorage.cacheKlineData((KlineData) data);
            } else if (data instanceof DepthData) {
                operationName = "Redis-Depth";
                operation = () -> redisStorage.cacheDepthData((DepthData) data);
            } else if (data instanceof TradeData) {
                operationName = "Redis-MarketData";
                operation = () -> redisStorage.cacheMarketData((TradeData) data);
            }

            if (operationName != null) {
                final Runnable executableOperation = operation;
                retryService.executeRedisOperation(operationName, () -> {
                    executableOperation.run();
                    return null;
                });
            }
        }, threadPoolManager.getVirtualThreadExecutor());
    }

    /**
     * 发布到Kafka（带重试机制）
     */
    private CompletableFuture<Void> publishToKafkaWithRetry(Object data) {
        return CompletableFuture.runAsync(() -> {
            retryService.executeApiOperation("Kafka-Publish-" + data.getClass().getSimpleName(), () -> {
                publishToKafkaInternal(data);
                return null;
            });
        }, threadPoolManager.getVirtualThreadExecutor()).exceptionally(e -> {
            log.error("Kafka发布操作最终失败: data={}", data, e.getCause() != null ? e.getCause() : e);
            return null; // 保持与旧逻辑一致，不向上抛出异常，只记录日志
        });
    }

    /**
     * 内部Kafka发布方法
     */
    private void publishToKafkaInternal(Object data) {
        String topic;
        String key;
        String value;

        if (data instanceof KlineData) {
            KlineData klineData = (KlineData) data;
            topic = memoryOptimizer.createOptimizedString("-",
                    marketDataConfig.getKafka().getTopicPrefix(),
                    marketDataConfig.getKafka().getTopics().getKline());
            key = memoryOptimizer.createOptimizedString("_",
                    klineData.getSymbol(), klineData.getInterval());
            value = dataConverter.toJson(klineData);
        } else if (data instanceof DepthData) {
            DepthData depthData = (DepthData) data;
            topic = memoryOptimizer.createOptimizedString("-",
                    marketDataConfig.getKafka().getTopicPrefix(),
                    marketDataConfig.getKafka().getTopics().getDepth());
            key = memoryOptimizer.createOptimizedString("_",
                    depthData.getSymbol(), "depth");
            value = dataConverter.toJson(depthData);
        } else if (data instanceof TradeData) {
            TradeData tradeData = (TradeData) data;
            topic = getTopicByDataType("trade");
            key = memoryOptimizer.createOptimizedString("_",
                    tradeData.getSymbol(), "trade");
            value = dataConverter.toJson(tradeData);
        } else {
            log.warn("未知的数据类型，跳过Kafka发布: {}", data.getClass());
            return;
        }

        try {
            kafkaTemplate.send(topic, key, value);
            kafkaSuccessCount.increment();
            log.debug("数据已发布到Kafka: topic={}, key={}, size={}bytes, 总发送数={}",
                    topic, key, value.length(), kafkaSuccessCount.sum());
        } catch (Exception e) {
            log.error("Kafka发送失败: topic={}, key={}", topic, key, e);
            throw e;
        }
    }

    /**
     * 存储到InfluxDB（原方法，保留用于兼容性）
     */
    @Deprecated
    private CompletableFuture<Void> storeToInfluxDB(Object data) {
        return CompletableFuture.runAsync(() -> {
            try {
                if (data instanceof KlineData) {
                    influxDBStorage.saveKlineData((KlineData) data);
                } else if (data instanceof TradeData) {
                    influxDBStorage.saveMarketData((TradeData) data);
                }
            } catch (Exception e) {
                log.error("存储到InfluxDB失败: {}", data, e);
                throw new RuntimeException("InfluxDB存储失败", e);
            }
        });
    }
    
    /**
     * 检查实时数据写入健康状态
     */
    public Map<String, Object> checkRealTimeDataWriteHealth() {
        Map<String, Object> healthStatus = new HashMap<>();

        // 检查各存储系统状态
        healthStatus.put("influxdb_healthy", influxDBStorage != null && influxDBStorage.isHealthy());
        healthStatus.put("mysql_healthy", mySQLStorage != null && mySQLStorage.isHealthy());
        healthStatus.put("redis_healthy", redisStorage != null && redisStorage.isHealthy());

        // 检查处理统计
        healthStatus.put("total_processed", processedCount.sum());
        healthStatus.put("kline_processed", klineProcessedCount.sum());
        healthStatus.put("market_data_processed", marketDataProcessedCount.sum());
        healthStatus.put("error_count", errorCount.sum());

        // 检查存储成功率
        healthStatus.put("influx_success_count", influxSuccessCount.sum());
        healthStatus.put("mysql_success_count", mysqlSuccessCount.sum());
        healthStatus.put("kafka_success_count", kafkaSuccessCount.sum());

        // 计算成功率
        long totalProcessed = processedCount.sum();
        if (totalProcessed > 0) {
            double successRate = (double) (influxSuccessCount.sum() + mysqlSuccessCount.sum() + kafkaSuccessCount.sum())
                                / (totalProcessed * 3) * 100; // 3个存储系统
            healthStatus.put("overall_success_rate", String.format("%.2f%%", successRate));
        } else {
            healthStatus.put("overall_success_rate", "N/A");
        }

        // 检查是否有严重错误
        boolean isHealthy = (influxDBStorage == null || influxDBStorage.isHealthy()) &&
                           (mySQLStorage == null || mySQLStorage.isHealthy()) &&
                           redisStorage.isHealthy() &&
                           errorCount.sum() < totalProcessed * 0.1; // 错误率小于10%

        healthStatus.put("overall_healthy", isHealthy);
        healthStatus.put("check_time", Instant.now().toString());

        return healthStatus;
    }

    /**
     * 重置统计计数器
     */
    public void resetCounters() {
        processedCount.reset();
        klineProcessedCount.reset();
        marketDataProcessedCount.reset();
        errorCount.reset();
        influxSuccessCount.reset();
        mysqlSuccessCount.reset();
        kafkaSuccessCount.reset();
        log.info("数据处理统计计数器已重置");
    }

    /**
     * 关闭数据处理器
     */
    @PreDestroy
    public void shutdown() {
        log.info("关闭数据处理器...");

        // 设置关闭标志，防止新的异步任务启动
        isShuttingDown = true;

        // 等待当前正在执行的异步任务完成（最多等待5秒）
        try {
            log.info("等待异步任务完成...");
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("等待异步任务完成时被中断");
        }

        // 关闭性能监控器
        if (performanceMonitor != null) {
            performanceMonitor.shutdown();
        }

        // 关闭数据质量监控器
        if (dataQualityMonitor != null) {
            dataQualityMonitor.shutdown();
        }

        log.info("数据处理器已关闭");
    }
    
    /**
     * 根据数据类型获取Kafka主题
     */
    private String getTopicByDataType(String dataType) {
        String topicPrefix = marketDataConfig.getKafka().getTopicPrefix();
        switch (dataType) {
            case "depth":
                return memoryOptimizer.createOptimizedString("-",
                        topicPrefix, marketDataConfig.getKafka().getTopics().getDepth());
            case "trade":
                return memoryOptimizer.createOptimizedString("-",
                        topicPrefix, marketDataConfig.getKafka().getTopics().getTrade());
            case "ticker":
                return memoryOptimizer.createOptimizedString("-",
                        topicPrefix, marketDataConfig.getKafka().getTopics().getTicker());
            case "book_ticker":
                return memoryOptimizer.createOptimizedString("-", topicPrefix, "book-ticker");
            default:
                return memoryOptimizer.createOptimizedString("-",
                        topicPrefix, marketDataConfig.getKafka().getTopics().getTrade()); // 默认主题
        }
    }
    
    /**
     * 获取处理统计信息
     */
    public ProcessingStatistics getStatistics() {
        return ProcessingStatistics.builder()
                .processedCount(processedCount.sum())
                .validationFailedCount(validationFailedCount.sum())
                .storageFailedCount(storageFailedCount.sum())
                .successRate(calculateSuccessRate())
                .build();
    }
    
    /**
     * 计算成功率
     */
    private double calculateSuccessRate() {
        long total = processedCount.sum();
        if (total == 0) {
            return 100.0;
        }

        long failed = validationFailedCount.sum() + storageFailedCount.sum();
        return ((double) (total - failed) / total) * 100.0;
    }

    /**
     * 存储到多级缓存 - 优化版本，添加背压控制和错误处理
     */
    private CompletableFuture<Void> storeToMultiLevelCache(Object data) {
        // 检查缓存服务是否可用
        if (marketDataCacheService == null) {
            return CompletableFuture.completedFuture(null);
        }
        // 检查是否正在关闭
        if (isShuttingDown) {
            log.debug("数据处理器正在关闭，跳过缓存操作");
            return CompletableFuture.completedFuture(null);
        }

        // 使用专用的数据处理线程池，避免ForkJoinPool的并发冲突
        return threadPoolManager.submitToDataProcessing(() -> {
            try {
                // 再次检查关闭状态（双重检查）
                if (isShuttingDown) {
                    log.debug("数据处理器正在关闭，跳过缓存操作");
                    return;
                }
                if (data instanceof KlineData) {
                    KlineData klineData = (KlineData) data;

                    // 生成缓存键，避免重复缓存相同数据
                    String cacheKey = klineData.getSymbol() + ":" + klineData.getInterval();

                    // 使用重试机制缓存K线数据，避免熔断器冲突
                    try {
                        marketDataCacheService.cacheKlineData(
                                klineData.getSymbol(),
                                klineData.getInterval(),
                                klineData);
                        log.debug("K线数据缓存成功: {}", cacheKey);
                    } catch (Exception e) {
                        log.warn("K线数据缓存失败，将稍后重试: key={}, error={}", cacheKey, e.getMessage());
                        // 不抛出异常，避免影响其他处理
                    }

                    // 缓存最新价格（独立处理，避免级联失败）
                    if (klineData.getClosePrice() != null) {
                        try {
                            marketDataCacheService.cacheLatestPrice(
                                    klineData.getSymbol(),
                                    klineData.getClosePrice());
                            log.debug("最新价格缓存成功: symbol={}, price={}",
                                    klineData.getSymbol(), klineData.getClosePrice());
                        } catch (Exception e) {
                            log.warn("最新价格缓存失败: symbol={}, error={}",
                                    klineData.getSymbol(), e.getMessage());
                        }
                    }

                } else if (data instanceof TradeData) {
                    TradeData tradeData = (TradeData) data;
                    String symbol = tradeData.getSymbol();

                    // 根据数据类型缓存到不同位置，每个操作独立处理错误
                    try {
                        marketDataCacheService.cacheTradeData(symbol, tradeData);
                        log.debug("交易数据缓存成功: symbol={}", symbol);

                        if (tradeData.getPrice() != null) {
                            marketDataCacheService.cacheLatestPrice(symbol, tradeData.getPrice());
                            log.debug("通用价格数据缓存成功: symbol={}, price={}",
                                    symbol, tradeData.getPrice());
                        }
                    } catch (Exception e) {
                        log.warn("市场数据缓存失败: symbol={}, error={}",
                                symbol, e.getMessage());
                    }
                }

                log.debug("多级缓存存储完成: dataType={}", data.getClass().getSimpleName());

            } catch (Exception e) {
                log.error("多级缓存存储发生未预期错误: data={}", data, e);
                // 不重新抛出异常，避免影响主流程
            }
        });
    }

    /**
     * 合并的数据验证和质量检查方法 - 优化性能，减少重复检查
     */
    private boolean validateAndCheckQuality(TradeData tradeData) {
        try {
            // 第一步：使用DataValidator进行标准验证（保持向后兼容性）
            if (!dataValidator.validateMarketData(tradeData)) {
                performanceMonitor.recordValidationFailure("trade",
                        tradeData.getSymbol(), "数据验证失败");
                log.debug("市场数据验证失败: symbol={}",
                    tradeData.getSymbol());
                return false;
            }
    
            // 第二步：数据质量检查（详细检查）
            DataQualityMonitor.QualityCheckResult qualityResult = dataQualityMonitor.checkMarketDataQuality(tradeData);
            if (qualityResult.getIssues() != null && !qualityResult.getIssues().isEmpty()) {
                performanceMonitor.recordValidationFailure("trade", tradeData.getSymbol(),
                        "数据质量检查失败: " + String.join(", ", qualityResult.getIssues()));
                log.warn("市场数据质量检查失败: symbol={}, timestamp={}, issues={}",
                        tradeData.getSymbol(), tradeData.getTradeTime(),
                        qualityResult.getIssues());
                return false;
            }
    
            return true;
    
        } catch (Exception e) {
            log.error("数据验证过程中发生异常: symbol={}",
                tradeData.getSymbol(), e);
            return false;
        }
    }

    /**
     * 基础数据验证 - 快速检查必要字段
     */
    private boolean validateMarketDataBasic(TradeData tradeData) {
        if (tradeData == null) {
            return false;
        }
    
        // 检查必要字段
        if (tradeData.getSymbol() == null || tradeData.getSymbol().trim().isEmpty()) {
            return false;
        }
    
        if (tradeData.getTradeTime() == null) {
            return false;
        }
    
        // 根据数据类型进行特定验证
        return tradeData.getPrice() != null && tradeData.getPrice().compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Inner class for holding processing statistics.
     */
    @lombok.Data
    @lombok.Builder
    public static class ProcessingStatistics {
        private long processedCount;
        private long validationFailedCount;
        private long storageFailedCount;
        private double successRate;
    }
}
