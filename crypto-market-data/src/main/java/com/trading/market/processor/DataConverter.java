package com.trading.market.processor;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trading.common.json.HighPerformanceJsonProcessor;
import com.trading.common.config.MarketDataConfig;
import com.trading.common.dto.DepthData;
import com.trading.common.dto.KlineData;
import com.trading.common.dto.TradeData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据转换器
 * 负责数据格式转换、标准化和qlib格式转换
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Slf4j
@Component
public class DataConverter {
    
    @Autowired
    private MarketDataConfig marketDataConfig;

    @Autowired
    private ObjectMapper objectMapper;

    /**
     * 设置市场数据配置（用于测试）
     */
    public void setMarketDataConfig(MarketDataConfig marketDataConfig) {
        this.marketDataConfig = marketDataConfig;
    }

    /**
     * 设置ObjectMapper（用于测试）
     */
    public void setObjectMapper(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }
    
    /**
     * 转换K线数据
     */
    public KlineData convertKlineData(KlineData klineData) {
        if (klineData == null) {
            return null;
        }
        
        try {
            KlineData converted = new KlineData();
            converted.setSymbol(klineData.getSymbol());
            converted.setInterval(klineData.getInterval());
            converted.setOpenTime(klineData.getOpenTime());
            converted.setCloseTime(klineData.getCloseTime());
            converted.setOpenPrice(klineData.getOpenPrice());
            converted.setHighPrice(klineData.getHighPrice());
            converted.setLowPrice(klineData.getLowPrice());
            converted.setClosePrice(klineData.getClosePrice());
            converted.setVolume(klineData.getVolume());
            converted.setQuoteVolume(klineData.getQuoteVolume());
            converted.setTradeCount(klineData.getTradeCount());
            converted.setTakerBuyVolume(klineData.getTakerBuyVolume());
            converted.setTakerBuyQuoteVolume(klineData.getTakerBuyQuoteVolume());
            
            // 标准化处理
            if (marketDataConfig.getProcessor().getConversion().getNormalization().isEnabled()) {
                converted = normalizeKlineData(converted);
            }
            
            return converted;
            
        } catch (Exception e) {
            log.error("转换K线数据失败: {}", klineData, e);
            return klineData; // 返回原始数据
        }
    }
    
    /**
     * 转换深度数据
     */
    public DepthData convertDepthData(DepthData depthData) {
        if (depthData == null) {
            return null;
        }

        try {
            DepthData converted = new DepthData();
            converted.setSymbol(depthData.getSymbol());
            converted.setBids(depthData.getBids());
            converted.setAsks(depthData.getAsks());
            converted.setLastUpdateId(depthData.getLastUpdateId());
            converted.setTimestamp(depthData.getTimestamp());

            // 设置数据源
            converted.setSource(depthData.getSource() != null ? depthData.getSource() : "binance");

            // 设置深度级别 - 修复null值问题
            if (depthData.getLevels() != null) {
                converted.setLevels(depthData.getLevels());
            } else {
                // 根据实际数据计算深度级别
                int bidLevels = depthData.getBids() != null ? depthData.getBids().size() : 0;
                int askLevels = depthData.getAsks() != null ? depthData.getAsks().size() : 0;
                converted.setLevels(Math.max(bidLevels, askLevels));
            }

            // 标准化处理
            if (marketDataConfig.getProcessor().getConversion().getNormalization().isEnabled()) {
                converted = normalizeDepthData(converted);
            }

            return converted;

        } catch (Exception e) {
            log.error("转换深度数据失败: {}", depthData, e);
            return depthData; // 返回原始数据
        }
    }

    /**
     * 转换市场数据
     */
    public TradeData convertMarketData(TradeData tradeData) {
        if (tradeData == null) {
            return null;
        }
    
        try {
            TradeData converted = new TradeData();
            converted.setSymbol(tradeData.getSymbol());
            converted.setTradeId(tradeData.getTradeId());
            converted.setPrice(tradeData.getPrice());
            converted.setQuantity(tradeData.getQuantity());
            converted.setQuoteQuantity(tradeData.getQuoteQuantity());
            converted.setTradeTime(tradeData.getTradeTime());
            converted.setIsBuyerMaker(tradeData.getIsBuyerMaker());
            converted.setSide(tradeData.getSide());
    
    
            // 标准化处理
            if (marketDataConfig.getProcessor().getConversion().getNormalization().isEnabled()) {
                converted = normalizeMarketData(converted);
            }
    
            return converted;
    
        } catch (Exception e) {
            log.error("转换市场数据失败: {}", tradeData, e);
            return tradeData; // 返回原始数据
        }
    }
    
    /**
     * 标准化K线数据
     */
    private KlineData normalizeKlineData(KlineData klineData) {
        MarketDataConfig.ProcessorConfig.ConversionConfig.NormalizationConfig config = 
                marketDataConfig.getProcessor().getConversion().getNormalization();
        
        double scaleFactor = config.getScaleFactor();
        int precision = marketDataConfig.getProcessor().getConversion().getQlibFormat().getPrecision();
        
        // 价格标准化（保持原始精度，不进行缩放）
        if (klineData.getOpenPrice() != null) {
            klineData.setOpenPrice(klineData.getOpenPrice().setScale(precision, RoundingMode.HALF_UP));
        }
        if (klineData.getHighPrice() != null) {
            klineData.setHighPrice(klineData.getHighPrice().setScale(precision, RoundingMode.HALF_UP));
        }
        if (klineData.getLowPrice() != null) {
            klineData.setLowPrice(klineData.getLowPrice().setScale(precision, RoundingMode.HALF_UP));
        }
        if (klineData.getClosePrice() != null) {
            klineData.setClosePrice(klineData.getClosePrice().setScale(precision, RoundingMode.HALF_UP));
        }
        
        // 成交量标准化
        if (klineData.getVolume() != null) {
            klineData.setVolume(klineData.getVolume().setScale(precision, RoundingMode.HALF_UP));
        }
        if (klineData.getQuoteVolume() != null) {
            klineData.setQuoteVolume(klineData.getQuoteVolume().setScale(precision, RoundingMode.HALF_UP));
        }
        
        return klineData;
    }
    
    /**
     * 标准化深度数据
     */
    private DepthData normalizeDepthData(DepthData depthData) {
        try {
            // 时间戳标准化
            if (depthData.getTimestamp() == null) {
                depthData.setTimestamp(LocalDateTime.now(ZoneOffset.UTC));
            }

            // 确保数据源不为空
            if (depthData.getSource() == null || depthData.getSource().trim().isEmpty()) {
                depthData.setSource("binance");
            }

            // 确保深度级别不为空
            if (depthData.getLevels() == null) {
                int bidLevels = depthData.getBids() != null ? depthData.getBids().size() : 0;
                int askLevels = depthData.getAsks() != null ? depthData.getAsks().size() : 0;
                depthData.setLevels(Math.max(bidLevels, askLevels));
            }

            return depthData;

        } catch (Exception e) {
            log.error("标准化深度数据失败: {}", depthData, e);
            return depthData;
        }
    }

    /**
     * 标准化市场数据
     */
    private TradeData normalizeMarketData(TradeData tradeData) {
        int precision = marketDataConfig.getProcessor().getConversion().getQlibFormat().getPrecision();
    
        // 价格标准化
        if (tradeData.getPrice() != null) {
            tradeData.setPrice(tradeData.getPrice().setScale(precision, RoundingMode.HALF_UP));
        }
    
        // 数量标准化
        if (tradeData.getQuantity() != null) {
            tradeData.setQuantity(tradeData.getQuantity().setScale(precision, RoundingMode.HALF_UP));
        }
    
        // 成交额标准化
        if (tradeData.getQuoteQuantity() != null) {
            tradeData.setQuoteQuantity(tradeData.getQuoteQuantity().setScale(precision, RoundingMode.HALF_UP));
        }
    
        return tradeData;
    }
    
    /**
     * 转换为qlib格式
     */
    public Map<String, Object> toQlibFormat(KlineData klineData) {
        if (!marketDataConfig.getProcessor().getConversion().getQlibFormat().isEnabled()) {
            return null;
        }
        
        try {
            Map<String, Object> qlibData = new HashMap<>();
            
            // 时间格式转换
            String dateFormat = marketDataConfig.getProcessor().getConversion().getQlibFormat().getDateFormat();
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern(dateFormat);
            LocalDate date = klineData.getOpenTime().atZone(ZoneOffset.UTC).toLocalDate();
            
            qlibData.put("date", date.format(formatter));
            qlibData.put("symbol", klineData.getSymbol());
            qlibData.put("open", klineData.getOpenPrice());
            qlibData.put("high", klineData.getHighPrice());
            qlibData.put("low", klineData.getLowPrice());
            qlibData.put("close", klineData.getClosePrice());
            qlibData.put("volume", klineData.getVolume());
            qlibData.put("amount", klineData.getQuoteVolume());
            
            // 计算技术指标
            qlibData.put("vwap", calculateVWAP(klineData));
            qlibData.put("change", calculatePriceChange(klineData));
            qlibData.put("pct_change", calculatePriceChangePercent(klineData));
            
            return qlibData;
            
        } catch (Exception e) {
            log.error("转换为qlib格式失败: {}", klineData, e);
            return null;
        }
    }
    
    /**
     * 计算VWAP（成交量加权平均价格）
     */
    private BigDecimal calculateVWAP(KlineData klineData) {
        if (klineData.getQuoteVolume() == null || klineData.getVolume() == null || 
            klineData.getVolume().compareTo(BigDecimal.ZERO) == 0) {
            return klineData.getClosePrice();
        }
        
        return klineData.getQuoteVolume().divide(klineData.getVolume(), 8, RoundingMode.HALF_UP);
    }
    
    /**
     * 计算价格变化
     */
    private BigDecimal calculatePriceChange(KlineData klineData) {
        if (klineData.getOpenPrice() == null || klineData.getClosePrice() == null) {
            return BigDecimal.ZERO;
        }
        
        return klineData.getClosePrice().subtract(klineData.getOpenPrice());
    }
    
    /**
     * 计算价格变化百分比
     */
    private BigDecimal calculatePriceChangePercent(KlineData klineData) {
        if (klineData.getOpenPrice() == null || klineData.getClosePrice() == null || 
            klineData.getOpenPrice().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        return klineData.getClosePrice().subtract(klineData.getOpenPrice())
                .divide(klineData.getOpenPrice(), 4, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));
    }
    
    /**
     * 计算K线数据质量分数
     */
    private double calculateKlineQualityScore(KlineData klineData) {
        // This is a simplified version since the original logic depends on removed fields.
        return 1.0;
    }
    
    /**
     * 计算市场数据质量分数
     */
    private double calculateMarketDataQualityScore(TradeData tradeData) {
        // This is a simplified version since the original logic depends on removed fields.
        return 1.0;
    }

    /**
     * 标准化深度JSON数据
     */
    private String normalizeDepthJson(String depthJson) {
        try {
            // 解析JSON并重新格式化
            JsonNode jsonNode = objectMapper.readTree(depthJson);
            return objectMapper.writeValueAsString(jsonNode);
        } catch (Exception e) {
            log.warn("标准化深度JSON失败，返回原始数据: {}", depthJson, e);
            return depthJson;
        }
    }

    /**
     * 计算深度数据质量分数
     */
    private double calculateDepthDataQualityScore(DepthData depthData) {
        double score = 1.0;

        // 检查数据完整性
        if (depthData.getSymbol() == null || depthData.getSymbol().trim().isEmpty()) {
            score -= 0.2;
        }

        if (depthData.getBids() == null || depthData.getBids().isEmpty()) {
            score -= 0.3;
        }

        if (depthData.getAsks() == null || depthData.getAsks().isEmpty()) {
            score -= 0.3;
        }

        if (depthData.getTimestamp() == null) {
            score -= 0.1;
        }

        // 检查数据时效性
        if (depthData.getTimestamp() != null) {
            long ageMs = Instant.now().toEpochMilli() - depthData.getTimestamp().toInstant(ZoneOffset.UTC).toEpochMilli();
            if (ageMs > 60000) { // 超过1分钟
                score -= 0.1;
            } else if (ageMs > 10000) { // 超过10秒
                score -= 0.05;
            }
        }

        return Math.max(0.0, score);
    }

    /**
     * 检查JSON格式有效性
     */
    private boolean isValidJson(String json) {
        try {
            objectMapper.readTree(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 转换为JSON字符串
     */
    public String toJson(Object data) {
        try {
            return objectMapper.writeValueAsString(data);
        } catch (JsonProcessingException e) {
            log.error("转换为JSON失败: {}", data, e);
            return "{}";
        }
    }
    
    /**
     * 从JSON字符串转换
     */
    public <T> T fromJson(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            log.error("从JSON转换失败: json={}, class={}", json, clazz.getSimpleName(), e);
            return null;
        }
    }
    
    /**
     * 转换为CSV格式（用于数据导出）
     */
    public String toCsv(KlineData klineData) {
        StringBuilder csv = new StringBuilder();
        csv.append(klineData.getSymbol()).append(",");
        csv.append(klineData.getInterval()).append(",");
        csv.append(klineData.getOpenTime()).append(",");
        csv.append(klineData.getCloseTime()).append(",");
        csv.append(klineData.getOpenPrice()).append(",");
        csv.append(klineData.getHighPrice()).append(",");
        csv.append(klineData.getLowPrice()).append(",");
        csv.append(klineData.getClosePrice()).append(",");
        csv.append(klineData.getVolume()).append(",");
        csv.append(klineData.getQuoteVolume()).append(",");
        csv.append(klineData.getTradeCount()).append(",");
        csv.append(klineData.getTakerBuyVolume()).append(",");
        csv.append(klineData.getTakerBuyQuoteVolume());
        
        return csv.toString();
    }
    
    /**
     * 获取CSV头部
     */
    public String getCsvHeader() {
        return "symbol,interval,openTime,closeTime,openPrice,highPrice,lowPrice,closePrice," +
               "volume,quoteVolume,tradeCount,takerBuyVolume,takerBuyQuoteVolume";
    }
}
