package com.trading.market.websocket;

import com.fasterxml.jackson.databind.JsonNode;
import com.trading.common.json.HighPerformanceJsonProcessor;
import com.trading.market.collector.BinanceDataCollector;
import com.trading.common.dto.DepthData;
import com.trading.common.dto.KlineData;
import com.trading.common.dto.TradeData;
import com.trading.market.processor.DataProcessor;
import com.trading.common.dto.Symbol;
import com.trading.sdk.websocket.callback.MessageHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import com.fasterxml.jackson.core.type.TypeReference;
import com.trading.common.enums.OrderSide;

/**
 * 市场数据消息处理器
 * 处理来自WebSocket的市场数据消息，并转发给数据处理器
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
@Primary
public class MarketDataMessageHandler implements MessageHandler {
    
    private static final Logger log = LoggerFactory.getLogger(MarketDataMessageHandler.class);
    
    @Autowired
    private DataProcessor dataProcessor;
    
    @Autowired
    @Lazy
    private BinanceDataCollector dataCollector;

    @Autowired
    private HighPerformanceJsonProcessor jsonProcessor;

    // 关闭状态标记，用于在Spring容器关闭时停止消息处理
    private volatile boolean shutdown = false;

    /**
     * 关闭消息处理器
     * 在Spring容器关闭时调用，停止处理新的消息
     */
    public void shutdown() {
        log.info("MarketDataMessageHandler正在关闭...");
        this.shutdown = true;
    }

    /**
     * 检查是否应该处理消息
     * @return true如果应该处理，false如果应该跳过
     */
    private boolean shouldProcessMessage() {
        if (shutdown) {
            log.debug("消息处理器已关闭，跳过消息处理");
            return false;
        }
        return true;
    }

    public void handleKlineMessage(String subscriptionKey, Symbol symbol, String interval, String message) {
        // 检查是否应该处理消息
        if (!shouldProcessMessage()) {
            return;
        }

        try {
            log.debug("收到K线消息: key={}, symbol={}, interval={}",
                    subscriptionKey, symbol.getSymbol(), interval);

            // 增加消息计数
            dataCollector.incrementMessageCount();
            
            JsonNode jsonNode = jsonProcessor.parseToJsonNode(message);
            if (jsonNode.has("k")) {
                JsonNode klineNode = jsonNode.get("k");
                
                KlineData klineData = parseKlineData(klineNode, symbol.getSymbol());
                if (klineData != null) {
                    // 异步处理K线数据
                    dataProcessor.processKlineData(klineData);
                    log.info("K线数据已提交处理: symbol={}, interval={}, closeTime={}",
                            symbol.getSymbol(), interval, klineData.getCloseTime());
                }
            } else {
                log.warn("K线消息格式异常，缺少'k'字段: {}", message);
            }
            
        } catch (Exception e) {
            log.error("处理K线消息失败: key={}, symbol={}, interval={}, message={}", 
                    subscriptionKey, symbol.getSymbol(), interval, message, e);
        }
    }
    
    public void handleDepthMessage(String subscriptionKey, Symbol symbol, int levels, int speed, String message) {
        // 检查是否应该处理消息
        if (!shouldProcessMessage()) {
            return;
        }

        try {
            log.debug("收到深度消息: key={}, symbol={}, levels={}, speed={}ms",
                    subscriptionKey, symbol.getSymbol(), levels, speed);

            // 增加消息计数
            dataCollector.incrementMessageCount();

            JsonNode jsonNode = jsonProcessor.parseToJsonNode(message);

            // 检查消息格式并提取买卖单数据
            JsonNode bids = null;
            JsonNode asks = null;
            String eventType = jsonNode.has("e") ? jsonNode.get("e").asText() : "";
            Long lastUpdateId = null;

            if ("depthUpdate".equals(eventType)) {
                // 增量深度更新格式 (WebSocket)
                bids = jsonNode.get("b");
                asks = jsonNode.get("a");
                lastUpdateId = jsonNode.has("u") ? jsonNode.get("u").asLong() : null;
            } else {
                // 快照深度格式 (REST API)
                bids = jsonNode.get("bids");
                asks = jsonNode.get("asks");
                lastUpdateId = jsonNode.has("lastUpdateId") ? jsonNode.get("lastUpdateId").asLong() : null;
            }

            if (bids != null && asks != null) {
                // 解析为DepthData实体（主要处理方式）
                DepthData depthData = parseDepthDataToEntity(jsonNode, symbol.getSymbol(), levels, eventType);
                if (depthData != null) {
                    // 异步处理深度数据
                    dataProcessor.processDepthData(depthData);
                    log.debug("深度数据已提交处理: symbol={}, eventType={}, lastUpdateId={}, levels={}, bids={}, asks={}",
                            symbol.getSymbol(), eventType, lastUpdateId, levels, bids.size(), asks.size());
                }
            } else {
                log.warn("深度消息格式异常: eventType={}, subscriptionKey={}, message={}",
                        eventType, subscriptionKey, message);
            }
            
        } catch (Exception e) {
            log.error("处理深度消息失败: key={}, symbol={}, levels={}, speed={}, message={}", 
                    subscriptionKey, symbol.getSymbol(), levels, speed, message, e);
        }
    }
    
    public void handleTradeMessage(String subscriptionKey, Symbol symbol, String message) {
        // 检查是否应该处理消息
        if (!shouldProcessMessage()) {
            return;
        }

        try {
            log.debug("收到交易消息: key={}, symbol={}", subscriptionKey, symbol.getSymbol());

            // 增加消息计数
            dataCollector.incrementMessageCount();
            
            JsonNode jsonNode = jsonProcessor.parseToJsonNode(message);
            if (jsonNode.has("p") && jsonNode.has("q")) {
                TradeData tradeData = parseTradeData(jsonNode, symbol.getSymbol());
                if (tradeData != null) {
                    // 异步处理交易数据
                    dataProcessor.processMarketData(tradeData);
                    log.info("交易数据已提交处理: symbol={}, price={}, quantity={}",
                            symbol.getSymbol(), tradeData.getPrice(), tradeData.getQuantity());
                }
            } else {
                log.warn("交易消息格式异常，缺少'p'或'q'字段: {}", message);
            }
            
        } catch (Exception e) {
            log.error("处理交易消息失败: key={}, symbol={}, message={}", 
                    subscriptionKey, symbol.getSymbol(), message, e);
        }
    }
    
    public void handleTickerMessage(String subscriptionKey, Symbol symbol, String message) {
        // 检查是否应该处理消息
        if (!shouldProcessMessage()) {
            return;
        }

        try {
            log.debug("收到24小时统计消息: key={}, symbol={}", subscriptionKey, symbol.getSymbol());

            // 增加消息计数
            dataCollector.incrementMessageCount();
            
            JsonNode jsonNode = jsonProcessor.parseToJsonNode(message);
            if (jsonNode.has("c") && jsonNode.has("v")) {
                TradeData tradeData = parseTickerData(jsonNode, symbol.getSymbol());
                if (tradeData != null) {
                    // 异步处理24小时统计数据
                    dataProcessor.processMarketData(tradeData);
                    log.info("24小时统计数据已提交处理: symbol={}, price={}, volume={}",
                            symbol.getSymbol(), tradeData.getPrice(), tradeData.getQuantity());
                }
            } else {
                log.warn("24小时统计消息格式异常，缺少'c'或'v'字段: {}", message);
            }
            
        } catch (Exception e) {
            log.error("处理24小时统计消息失败: key={}, symbol={}, message={}", 
                    subscriptionKey, symbol.getSymbol(), message, e);
        }
    }
    
    public void handleBookTickerMessage(String subscriptionKey, Symbol symbol, String message) {
        // 检查是否应该处理消息
        if (!shouldProcessMessage()) {
            return;
        }

        try {
            log.debug("收到最优挂单消息: key={}, symbol={}", subscriptionKey, symbol.getSymbol());

            // 增加消息计数
            dataCollector.incrementMessageCount();
            
            JsonNode jsonNode = jsonProcessor.parseToJsonNode(message);
            if (jsonNode.has("b") && jsonNode.has("a")) {
                TradeData tradeData = parseBookTickerData(jsonNode, symbol.getSymbol());
                if (tradeData != null) {
                    // 异步处理最优挂单数据
                    dataProcessor.processMarketData(tradeData);
                    log.info("最优挂单数据已提交处理: symbol={}, bidPrice={}, askPrice={}",
                            symbol.getSymbol(), jsonNode.get("b").asText(), jsonNode.get("a").asText());
                }
            } else {
                log.warn("最优挂单消息格式异常，缺少'b'或'a'字段: {}", message);
            }
            
        } catch (Exception e) {
            log.error("处理最优挂单消息失败: key={}, symbol={}, message={}", 
                    subscriptionKey, symbol.getSymbol(), message, e);
        }
    }
    
    public void handleMarkPriceMessage(String subscriptionKey, Symbol symbol, String message) {
        // 市场数据模块暂不处理标记价格消息
        log.debug("收到标记价格消息，但市场数据模块暂不处理: key={}, symbol={}", subscriptionKey, symbol.getSymbol());
    }

    public void handleAggTradeMessage(String subscriptionKey, Symbol symbol, String message) {
        // 市场数据模块暂不处理聚合交易消息
        log.debug("收到聚合交易消息，但市场数据模块暂不处理: key={}, symbol={}", subscriptionKey, symbol.getSymbol());
    }

    public void handleUserDataMessage(String subscriptionKey, String listenKey, String message) {
        // 市场数据模块不处理用户数据
        log.debug("收到用户数据消息，但市场数据模块不处理: key={}, listenKey={}", subscriptionKey, listenKey);
    }

    public void handleForceOrderMessage(String subscriptionKey, Symbol symbol, String message) {
        // 市场数据模块不处理强制平仓订单消息
        log.debug("收到强制平仓订单消息，但市场数据模块不处理: key={}, symbol={}", subscriptionKey, symbol.getSymbol());
    }

    @Override
    public void handleConnectionOpen(String subscriptionKey) {
        log.info("WebSocket连接已打开: key={}", subscriptionKey);
    }

    @Override
    public void handleConnectionClose(String subscriptionKey, String reason) {
        log.warn("WebSocket连接已关闭: key={}, reason={}", subscriptionKey, reason);
    }

    @Override
    public void handleConnectionError(String subscriptionKey, Throwable error) {
        log.error("WebSocket连接错误: key={}", subscriptionKey, error);
    }

    @Override
    public void handleReconnect(String subscriptionKey, int attempt) {
        log.info("WebSocket重连: key={}, attempt={}", subscriptionKey, attempt);
    }
    
    /**
     * 解析K线数据
     */
    private KlineData parseKlineData(JsonNode klineNode, String symbol) {
        try {
            return KlineData.builder()
                    .symbol(symbol)
                    .interval(klineNode.get("i").asText())
                    .openTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(klineNode.get("t").asLong()), ZoneOffset.UTC))
                    .closeTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(klineNode.get("T").asLong()), ZoneOffset.UTC))
                    .openPrice(new BigDecimal(klineNode.get("o").asText()))
                    .highPrice(new BigDecimal(klineNode.get("h").asText()))
                    .lowPrice(new BigDecimal(klineNode.get("l").asText()))
                    .closePrice(new BigDecimal(klineNode.get("c").asText()))
                    .volume(new BigDecimal(klineNode.get("v").asText()))
                    .quoteVolume(new BigDecimal(klineNode.get("q").asText()))
                    .tradeCount(klineNode.get("n").asLong())
                    .takerBuyVolume(new BigDecimal(klineNode.get("V").asText()))
                    .takerBuyQuoteVolume(new BigDecimal(klineNode.get("Q").asText()))
                    .isClosed(klineNode.get("x").asBoolean())
                    .source("binance")
                    .build();
        } catch (Exception e) {
            log.error("解析K线数据失败: symbol={}, klineNode={}", symbol, klineNode, e);
            return null;
        }
    }
    
    /**
     * 解析深度数据为DepthData实体
     */
    private DepthData parseDepthDataToEntity(JsonNode jsonNode, String symbol, int levels, String eventType) {
        try {
            // 根据消息类型提取买卖单数据
            JsonNode bids = null;
            JsonNode asks = null;
            Long lastUpdateId = null;

            if ("depthUpdate".equals(eventType)) {
                // 增量深度更新格式
                bids = jsonNode.get("b");
                asks = jsonNode.get("a");
                lastUpdateId = jsonNode.has("u") ? jsonNode.get("u").asLong() : null;
            } else {
                // 快照深度格式
                bids = jsonNode.get("bids");
                asks = jsonNode.get("asks");
                lastUpdateId = jsonNode.has("lastUpdateId") ? jsonNode.get("lastUpdateId").asLong() : null;
            }

            if (bids != null && asks != null) {
                // 将买卖单数据转换为JSON字符串 - 使用高性能JSON处理器
                List<DepthData.PriceLevel> bidsList = jsonProcessor.deserialize(flattenPriceLevels(bids), new TypeReference<List<DepthData.PriceLevel>>() {});
                List<DepthData.PriceLevel> asksList = jsonProcessor.deserialize(flattenPriceLevels(asks), new TypeReference<List<DepthData.PriceLevel>>() {});

                return DepthData.builder()
                        .symbol(symbol)
                        .levels(levels)
                        .bids(bidsList)
                        .asks(asksList)
                        .lastUpdateId(lastUpdateId)
                        .timestamp(LocalDateTime.now())
                        .source("binance")
                        .build();
            }
            return null;
        } catch (Exception e) {
            log.error("解析深度数据为实体失败: symbol={}, levels={}, eventType={}", symbol, levels, eventType, e);
            return null;
        }
    }

    private String flattenPriceLevels(JsonNode priceLevelsNode) {
        StringBuilder sb = jsonProcessor.borrowStringBuilder();
        try {
            sb.append("[");
            boolean first = true;
            for (JsonNode levelNode : priceLevelsNode) {
                if (levelNode.isArray() && levelNode.size() == 2) {
                    if (!first) {
                        sb.append(",");
                    }
                    sb.append("{\"price\":\"").append(levelNode.get(0).asText()).append("\",\"quantity\":\"").append(levelNode.get(1).asText()).append("\"}");
                    first = false;
                }
            }
            sb.append("]");
            return sb.toString();
        } finally {
            jsonProcessor.returnStringBuilder(sb);
        }
    }

    /**
     * 解析交易数据
     */
    private TradeData parseTradeData(JsonNode jsonNode, String symbol) {
        try {
            // 计算成交额
            BigDecimal price = new BigDecimal(jsonNode.get("p").asText());
            BigDecimal quantity = new BigDecimal(jsonNode.get("q").asText());
            BigDecimal amount = price.multiply(quantity);

            // 获取买卖方向
            String side = jsonNode.has("m") && jsonNode.get("m").asBoolean() ? "SELL" : "BUY";

            // 获取交易ID
            Long tradeId = jsonNode.has("t") ? jsonNode.get("t").asLong() : null;

            // 构建原始数据JSON
            String rawData = jsonNode.toString();

            return TradeData.builder()
                    .symbol(symbol)
                    .dataType("trade")
                    .price(price)
                    .quantity(quantity)
                    .amount(amount)
                    .side(OrderSide.valueOf(side))
                    .tradeId(tradeId)
                    .tradeTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(jsonNode.get("T").asLong()), ZoneOffset.UTC))
                    .source("binance")
                    .rawData(rawData)
                    .qualityScore(1.0)
                    .version("1.0")
                    .createdAt(LocalDateTime.now())
                    .build();
        } catch (Exception e) {
            log.error("解析交易数据失败: symbol={}, jsonNode={}", symbol, jsonNode, e);
            return null;
        }
    }
    
    /**
     * 解析24小时统计数据
     */
    private TradeData parseTickerData(JsonNode jsonNode, String symbol) {
        try {
            // 获取当前价格（收盘价）
            BigDecimal currentPrice = new BigDecimal(jsonNode.get("c").asText());

            // 获取24小时成交量
            BigDecimal volume = new BigDecimal(jsonNode.get("v").asText());

            // 获取24小时成交额
            BigDecimal quoteVolume = jsonNode.has("q") ? new BigDecimal(jsonNode.get("q").asText()) : null;

            // 构建原始数据JSON
            String rawData = jsonNode.toString();

            // 构建元数据，包含24小时统计的详细信息
            StringBuilder metadata = new StringBuilder();
            metadata.append("{");
            if (jsonNode.has("o")) metadata.append("\"openPrice\":\"").append(jsonNode.get("o").asText()).append("\",");
            if (jsonNode.has("h")) metadata.append("\"highPrice\":\"").append(jsonNode.get("h").asText()).append("\",");
            if (jsonNode.has("l")) metadata.append("\"lowPrice\":\"").append(jsonNode.get("l").asText()).append("\",");
            if (jsonNode.has("P")) metadata.append("\"priceChangePercent\":\"").append(jsonNode.get("P").asText()).append("\",");
            if (jsonNode.has("p")) metadata.append("\"priceChange\":\"").append(jsonNode.get("p").asText()).append("\",");
            if (jsonNode.has("w")) metadata.append("\"weightedAvgPrice\":\"").append(jsonNode.get("w").asText()).append("\",");
            if (jsonNode.has("x")) metadata.append("\"prevClosePrice\":\"").append(jsonNode.get("x").asText()).append("\",");
            if (jsonNode.has("c")) metadata.append("\"count\":\"").append(jsonNode.get("c").asText()).append("\",");
            metadata.append("\"dataType\":\"24hr_ticker_stats\"}");

            return TradeData.builder()
                    .symbol(symbol)
                    .dataType("stats") // 使用"stats"以匹配数据库查询
                    .price(currentPrice)
                    .quantity(volume)
                    .amount(quoteVolume)
                    .tradeTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(jsonNode.get("E").asLong()), ZoneOffset.UTC))
                    .source("binance")
                    .rawData(rawData)
                    .metadata(metadata.toString())
                    .qualityScore(1.0)
                    .version("1.0")
                    .createdAt(LocalDateTime.now())
                    .build();
        } catch (Exception e) {
            log.error("解析24小时统计数据失败: symbol={}, jsonNode={}", symbol, jsonNode, e);
            return null;
        }
    }
    
    /**
     * 解析最优挂单数据
     */
    private TradeData parseBookTickerData(JsonNode jsonNode, String symbol) {
        try {
            BigDecimal bidPrice = new BigDecimal(jsonNode.get("b").asText());
            BigDecimal askPrice = new BigDecimal(jsonNode.get("a").asText());
            BigDecimal midPrice = bidPrice.add(askPrice).divide(new BigDecimal("2"));
            
            return TradeData.builder()
                    .symbol(symbol)
                    .dataType("book_ticker")
                    .price(midPrice)
                    .tradeTime(LocalDateTime.now())
                    .source("binance")
                    .build();
        } catch (Exception e) {
            log.error("解析最优挂单数据失败: symbol={}, jsonNode={}", symbol, jsonNode, e);
            return null;
        }
    }
}