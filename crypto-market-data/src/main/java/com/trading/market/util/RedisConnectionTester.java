package com.trading.market.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;

/**
 * Redis连接测试工具
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
@ConditionalOnBean(RedisTemplate.class)
public class RedisConnectionTester {

    private static final Logger log = LoggerFactory.getLogger(RedisConnectionTester.class);

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /**
     * 应用启动后测试Redis连接
     */
    @PostConstruct
    public void testConnection() {
        try {
            // 检查RedisTemplate是否可用
            if (redisTemplate == null || redisTemplate.getConnectionFactory() == null) {
                log.warn("Redis未配置或已禁用，跳过连接测试");
                return;
            }

            // 测试基本连接
            String testKey = "test:connection:" + System.currentTimeMillis();
            String testValue = "connection_test_value";

            // 写入测试
            redisTemplate.opsForValue().set(testKey, testValue);
            log.info("Redis写入测试成功: key={}", testKey);

            // 读取测试
            Object retrievedValue = redisTemplate.opsForValue().get(testKey);
            if (testValue.equals(retrievedValue)) {
                log.info("Redis读取测试成功: value={}", retrievedValue);
            } else {
                log.error("Redis读取测试失败: expected={}, actual={}", testValue, retrievedValue);
            }

            // 删除测试数据
            redisTemplate.delete(testKey);
            log.info("Redis删除测试成功: key={}", testKey);

            // 测试连接信息
            redisTemplate.getConnectionFactory().getConnection().ping();
            log.info("Redis服务器PING测试成功，连接正常");

            log.info("✅ Redis连接测试全部通过");

        } catch (Exception e) {
            log.error("❌ Redis连接测试失败", e);
        }
    }

    /**
     * 手动测试Redis连接
     */
    public boolean testRedisConnection() {
        try {
            String testKey = "manual:test:" + System.currentTimeMillis();
            String testValue = "manual_test_value";
            
            redisTemplate.opsForValue().set(testKey, testValue);
            Object result = redisTemplate.opsForValue().get(testKey);
            redisTemplate.delete(testKey);

            boolean success = testValue.equals(result);
            log.info("手动Redis连接测试结果: {}", success ? "成功" : "失败");
            return success;
            
        } catch (Exception e) {
            log.error("手动Redis连接测试失败", e);
            return false;
        }
    }

    /**
     * 获取Redis连接状态信息
     */
    public String getConnectionStatus() {
        try {
            // 测试ping命令
            String pong = redisTemplate.getConnectionFactory().getConnection().ping();
            return "Redis连接正常，PING响应: " + pong;
        } catch (Exception e) {
            return "Redis连接异常: " + e.getMessage();
        }
    }
}
