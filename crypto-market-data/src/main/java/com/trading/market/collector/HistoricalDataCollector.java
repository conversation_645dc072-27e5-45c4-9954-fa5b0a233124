package com.trading.market.collector;

import com.trading.common.config.MarketDataConfig;
import com.trading.common.thread.UnifiedThreadPoolManager;
import com.trading.common.utils.AsyncDelayUtils;
import com.trading.common.retry.UnifiedRetryService;
import com.trading.common.logging.LogSampler;
import com.trading.common.dto.DepthData;
import com.trading.common.dto.KlineData;
import com.trading.common.dto.TradeData;
import com.trading.common.enums.OrderSide;
import com.trading.market.processor.DataProcessor;
import com.trading.market.service.MarketDataService;
import com.trading.market.storage.InfluxDBStorage;
import com.trading.market.storage.MySQLStorage;
import com.trading.sdk.api.MarketDataApi;
import com.trading.common.api.ApiResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * Historical Data Collector
 * Responsible for collecting and pre-storing historical data into InfluxDB and MySQL.
 * This class is inspired by the binance-futures-connector-java SDK and qlib framework.
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
@Slf4j
@DependsOn({"unifiedThreadPoolManager", "multiLevelCacheManagerImpl"})
public class HistoricalDataCollector {

    
    @Autowired
    private MarketDataConfig marketDataConfig;
    
    @Autowired
    private MarketDataApi marketDataApi;
    
    @Autowired
    private DataProcessor dataProcessor;
    
    @Autowired(required = false)
    private InfluxDBStorage influxDBStorage;
    
    @Autowired
    private MySQLStorage mySQLStorage;

    @Autowired
    private MarketDataService marketDataService;

    @Autowired
    private UnifiedThreadPoolManager threadPoolManager;

    @Autowired
    private UnifiedRetryService retryService;


    // Statistical counters
    private final AtomicLong totalCollectedCount = new AtomicLong(0);
    private final AtomicLong klineCollectedCount = new AtomicLong(0);
    private final AtomicLong depthCollectedCount = new AtomicLong(0);
    private final AtomicLong tradeCollectedCount = new AtomicLong(0);
    private final AtomicLong statsCollectedCount = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    
    // Graceful shutdown flag
    private volatile boolean isShuttingDown = false;

    // Virtual thread executor for parallel processing
    private final Executor virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();

    // Memory monitoring and resource management
    private final Runtime runtime = Runtime.getRuntime();
    private volatile long lastMemoryCheck = System.currentTimeMillis();
    private static final long MEMORY_CHECK_INTERVAL = 30000; // 30 seconds
    private static final double MEMORY_THRESHOLD = 0.85; // 85% memory usage threshold
    
    @PostConstruct
    public void initialize() {
        log.info("Initializing HistoricalDataCollector...");

        if (marketDataConfig.getCollector().isEnabled()) {
            int defaultDays = marketDataConfig.getCollector().getHistorical().getDefaultDays();
            log.info("Historical data collection enabled. Will start collecting {} days of history after startup.", defaultDays);

            // 立即开始收集历史数据，不等待30秒
            threadPoolManager.schedule(this::startHistoricalDataCollection, 5, TimeUnit.SECONDS);

            // 定期检查并补充数据
            threadPoolManager.schedule(() -> {
                while (shouldContinue()) {
                    try {
                        checkAndCollectMissingData();
                        Thread.sleep(TimeUnit.MINUTES.toMillis(60));
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    } catch (Exception e) {
                        log.error("定期数据检查失败", e);
                    }
                }
            }, 10, TimeUnit.MINUTES);
        } else {
            log.info("Historical data collection is disabled.");
        }
    }
    
    public void startHistoricalDataCollection() {
        try {
            boolean forceFullCollection = marketDataConfig.getCollector().getHistorical().isForceFullCollection();
            List<String> symbols = marketDataConfig.getCollector().getSymbols();

            if (symbols == null || symbols.isEmpty()) {
                log.warn("Symbol list is empty, skipping historical data collection.");
                return;
            }
            
            log.info("Starting historical data collection for {} symbols.", symbols.size());

            for (String symbol : symbols) {
                if (!shouldContinue()) {
                    log.info("Collection process was interrupted.");
                    return;
                }
                
                if (marketDataConfig.getCollector().getDataTypes().getKline().isEnabled()) {
                    collectHistoricalKlines(symbol, forceFullCollection);
                }
                if (marketDataConfig.getCollector().getDataTypes().getDepth().isEnabled()) {
                    collectHistoricalDepth(symbol);
                }
                if (marketDataConfig.getCollector().getDataTypes().getTrade().isEnabled()) {
                    collectHistoricalTrades(symbol, forceFullCollection);
                }
            }
            generateCollectionReport("Historical Data Collection");
        } catch (Exception e) {
            log.error("Failed to start historical data collection", e);
            errorCount.incrementAndGet();
        }
    }

    /**
     * Public entry point for collecting historical K-line data for a specific symbol.
     * It automatically determines whether to perform a full or incremental collection, using the default number of days.
     */
    public void collectHistoricalKlines(String symbol, boolean forceFull) {
        collectHistoricalKlines(symbol, marketDataConfig.getCollector().getHistorical().getDefaultDays(), forceFull);
    }

    /**
     * Public entry point for collecting historical trade data.
     */
    public void collectHistoricalTrades(String symbol, boolean forceFull) {
        if (!shouldContinue()) return;
        
        int defaultDays = marketDataConfig.getCollector().getHistorical().getDefaultDays();
        Instant endTime = Instant.now();
        Instant startTime;
        
        if (forceFull) {
            startTime = endTime.minus(defaultDays, ChronoUnit.DAYS);
        } else {
            Optional<TradeData> latestTrade = marketDataService.getLatestTradeData(symbol);
            startTime = latestTrade.map(TradeData::getTradeTime).map(t -> t.toInstant(ZoneOffset.UTC)).orElse(endTime.minus(defaultDays, ChronoUnit.DAYS));
        }

        if (startTime.isBefore(endTime)) {
            log.info("Collecting historical trades for {}: {} to {}", symbol, startTime, endTime);
            executeTradeCollection(symbol, startTime, endTime);
        }
    }

    /**
     * Public entry point for collecting current depth data.
     * Binance API does not provide historical depth snapshots, so we always fetch the latest.
     */
    public void collectHistoricalDepth(String symbol) {
        if (!shouldContinue()) return;
        
        log.info("Collecting depth data for {}", symbol);
        List<Integer> levels = marketDataConfig.getCollector().getDataTypes().getDepth().getLevels();

        for (Integer level : levels) {
            if (!shouldContinue()) break;
            try {
                ApiResponse<Map<String, Object>> response = retryService.executeApiOperation(
                    String.format("GetOrderBook[%s-%d]", symbol, level),
                    () -> marketDataApi.getOrderBook(symbol, level)
                );

                if (response.isSuccess() && response.getData() != null) {
                    DepthData depthData = convertToDepthData(response.getData(), symbol);
                    if (depthData != null) {
                        dataProcessor.processDepthData(depthData);
                        depthCollectedCount.incrementAndGet();
                        totalCollectedCount.incrementAndGet();
                    }
                }
                safeSleep(marketDataConfig.getCollector().getPerformance().getRateLimit().getRequestDelayMs());
            } catch (Exception e) {
                log.error("Failed to collect depth data for {} [level {}]", symbol, level, e);
                errorCount.incrementAndGet();
            }
        }
    }

    private void executeKlineCollection(String symbol, String interval, Instant startTime, Instant endTime) {
        log.info("Executing K-line collection for {} [{}]: {} -> {}", symbol, interval, startTime, endTime);
        long intervalMinutes = getIntervalMinutes(interval);
        int batchSize = marketDataConfig.getCollector().getPerformance().getBatch().getKlineSize();

        List<BatchRequest> batches = generateBatchRequests(symbol, interval, startTime, endTime, intervalMinutes, batchSize);
        log.info("Generated {} batches for parallel processing.", batches.size());

        int maxConcurrent = Math.min(marketDataConfig.getCollector().getPerformance().getRateLimit().getMaxConcurrentRequests(), batches.size());
        for (int i = 0; i < batches.size(); i += maxConcurrent) {
            if (!shouldContinue() || !checkMemoryAndPause()) {
                log.warn("K-line collection interrupted for {}.", symbol);
                return;
            }

            int endIndex = Math.min(i + maxConcurrent, batches.size());
            List<BatchRequest> currentBatchGroup = batches.subList(i, endIndex);
            processBatchGroup(currentBatchGroup);

            if (endIndex < batches.size()) {
                safeSleep(marketDataConfig.getCollector().getPerformance().getRateLimit().getRequestDelayMs() * 2);
            }
        }
    }

    private void executeTradeCollection(String symbol, Instant startTime, Instant endTime) {
        Long fromId = null;
        int batchCount = 0;
        
        while (shouldContinue()) {
            try {
                int tradeBatchSize = marketDataConfig.getCollector().getPerformance().getBatch().getTradeSize();
                ApiResponse<List<Map<String, Object>>> response = marketDataApi.getHistoricalTrades(symbol, tradeBatchSize, fromId);

                if (response.isSuccess() && response.getData() != null && !response.getData().isEmpty()) {
                    List<TradeData> tradeDataList = convertToTradeDataList(response.getData(), symbol);
                    List<TradeData> filteredData = tradeDataList.stream()
                            .filter(data -> data.getTradeTime().toInstant(ZoneOffset.UTC).isAfter(startTime) && data.getTradeTime().toInstant(ZoneOffset.UTC).isBefore(endTime))
                            .toList();

                    if (!filteredData.isEmpty()) {
                        dataProcessor.processMarketDataBatch(filteredData);
                        tradeCollectedCount.addAndGet(filteredData.size());
                        totalCollectedCount.addAndGet(filteredData.size());
                    }

                    Map<String, Object> lastTrade = response.getData().get(response.getData().size() - 1);
                    fromId = Long.valueOf(lastTrade.get("id").toString());
                    Instant lastTradeTime = Instant.ofEpochMilli(Long.parseLong(lastTrade.get("time").toString()));
                    if (lastTradeTime.isBefore(startTime)) {
                        break;
                    }
                } else {
                    break; 
                }
                safeSleep(marketDataConfig.getCollector().getPerformance().getRateLimit().getRequestDelayMs());
            } catch (Exception e) {
                log.error("Failed during trade collection for {}", symbol, e);
                errorCount.incrementAndGet();
                break;
            }
        }
    }
    
    private List<BatchRequest> generateBatchRequests(String symbol, String interval, Instant startTime, Instant endTime, long intervalMinutes, int batchSize) {
        List<BatchRequest> batches = new ArrayList<>();
        Instant currentStart = startTime;
        int batchNumber = 0;
        while (currentStart.isBefore(endTime)) {
            batchNumber++;
            Instant currentEnd = currentStart.plus(batchSize * intervalMinutes, ChronoUnit.MINUTES);
            if (currentEnd.isAfter(endTime)) {
                currentEnd = endTime;
            }
            batches.add(new BatchRequest(batchNumber, symbol, interval, currentStart, currentEnd, batchSize));
            currentStart = currentEnd;
        }
        return batches;
    }
    
    private void processBatchGroup(List<BatchRequest> batches) {
        List<CompletableFuture<BatchResult>> futures = batches.stream()
                .map(this::processSingleBatchWithResult)
                .collect(Collectors.toList());
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }
    
    private CompletableFuture<BatchResult> processSingleBatchWithResult(BatchRequest batch) {
        return CompletableFuture.supplyAsync(() -> {
            if (!shouldContinue()) return new BatchResult(batch.batchNumber, false, "Service shutting down", 0);
            
            try {
                ApiResponse<List<List<Object>>> response = retryService.executeApiOperation(
                        String.format("GetKlinesBatch[%s-%s-%d]", batch.symbol, batch.interval, batch.batchNumber),
                        () -> marketDataApi.getKlines(
                            batch.symbol, 
                            batch.interval, 
                            batch.startTime.toEpochMilli(), // startTime
                            batch.endTime.toEpochMilli(),    // endTime
                            batch.batchSize  // limit
                        )
                );

                if (response.isSuccess() && response.getData() != null) {
                    List<KlineData> klineDataList = convertToKlineDataList(response.getData(), batch.symbol, batch.interval);
                    if (!klineDataList.isEmpty()) {
                        storeKlineDataBatch(klineDataList);
                        klineCollectedCount.addAndGet(klineDataList.size());
                        totalCollectedCount.addAndGet(klineDataList.size());
                        safeSleep(marketDataConfig.getCollector().getPerformance().getRateLimit().getRequestDelayMs());
                        return new BatchResult(batch.batchNumber, true, null, klineDataList.size());
                    }
                }
                return new BatchResult(batch.batchNumber, false, response.getErrorMessage(), 0);
            } catch (Exception e) {
                log.error("Failed to process K-line batch {}", batch.batchNumber, e);
                errorCount.incrementAndGet();
                return new BatchResult(batch.batchNumber, false, e.getMessage(), 0);
            }
        }, virtualThreadExecutor);
    }
    
    private void storeKlineDataBatch(List<KlineData> klineDataList) {
        try {
            CompletableFuture<Void> influxFuture = (influxDBStorage != null) ? 
                CompletableFuture.runAsync(() -> influxDBStorage.saveKlineDataBatch(klineDataList), virtualThreadExecutor) : 
                CompletableFuture.completedFuture(null);

            CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() -> mySQLStorage.saveKlineDataBatch(klineDataList), virtualThreadExecutor);
            CompletableFuture.allOf(influxFuture, mysqlFuture).join();
        } catch (Exception e) {
            log.error("Failed to store K-line data batch", e);
            throw new RuntimeException("Failed to store K-line data batch", e);
        }
    }
    
    // Conversion and utility methods (kept as private)
    private List<KlineData> convertToKlineDataList(List<List<Object>> rawData, String symbol, String interval) {
        return rawData.stream().map(klineArray -> {
            try {
                KlineData klineData = new KlineData();
                klineData.setSymbol(symbol);
                klineData.setInterval(interval);
                klineData.setOpenTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(klineArray.get(0).toString())), ZoneOffset.UTC));
                klineData.setCloseTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(klineArray.get(6).toString())), ZoneOffset.UTC));
                klineData.setOpenPrice(new BigDecimal(klineArray.get(1).toString()));
                klineData.setHighPrice(new BigDecimal(klineArray.get(2).toString()));
                klineData.setLowPrice(new BigDecimal(klineArray.get(3).toString()));
                klineData.setClosePrice(new BigDecimal(klineArray.get(4).toString()));
                klineData.setVolume(new BigDecimal(klineArray.get(5).toString()));
                klineData.setQuoteVolume(new BigDecimal(klineArray.get(7).toString()));
                klineData.setTradeCount(Long.parseLong(klineArray.get(8).toString()));
                klineData.setTakerBuyVolume(new BigDecimal(klineArray.get(9).toString()));
                klineData.setTakerBuyQuoteVolume(new BigDecimal(klineArray.get(10).toString()));
                return klineData;
            } catch (Exception e) {
                log.error("Failed to convert K-line data: {}", klineArray, e);
                return null;
            }
        }).filter(java.util.Objects::nonNull).collect(Collectors.toList());
    }

    private DepthData convertToDepthData(Map<String, Object> rawData, String symbol) {
        try {
            DepthData depthData = new DepthData();
            depthData.setSymbol(symbol);

            List<DepthData.PriceLevel> bids = convertPriceLevels((List<List<String>>) rawData.get("bids"));
            List<DepthData.PriceLevel> asks = convertPriceLevels((List<List<String>>) rawData.get("asks"));

            depthData.setBids(bids);
            depthData.setAsks(asks);

            if (rawData.containsKey("lastUpdateId")) {
                depthData.setLastUpdateId(Long.valueOf(rawData.get("lastUpdateId").toString()));
            }
            depthData.setTimestamp(LocalDateTime.now(ZoneOffset.UTC));

            // 设置数据源和深度级别
            depthData.setSource("binance");
            depthData.setLevels(Math.max(bids.size(), asks.size()));

            return depthData;
        } catch (Exception e) {
            log.error("Failed to convert depth data for {}", symbol, e);
            return null;
        }
    }

    private List<DepthData.PriceLevel> convertPriceLevels(List<List<String>> rawLevels) {
        if (rawLevels == null) return new ArrayList<>();
        return rawLevels.stream()
                .map(level -> new DepthData.PriceLevel(level.get(0), level.get(1)))
                .collect(Collectors.toList());
    }

    private List<TradeData> convertToTradeDataList(List<Map<String, Object>> rawDataList, String symbol) {
        return rawDataList.stream().map(tradeMap -> {
            try {
                TradeData tradeData = new TradeData();
                tradeData.setSymbol(symbol);
                tradeData.setTradeId(Long.valueOf(tradeMap.get("id").toString()));
                tradeData.setPrice(new BigDecimal(tradeMap.get("price").toString()));
                tradeData.setQuantity(new BigDecimal(tradeMap.get("qty").toString()));
                tradeData.setQuoteQuantity(new BigDecimal(tradeMap.get("quoteQty").toString()));
                tradeData.setTradeTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(tradeMap.get("time").toString())), ZoneOffset.UTC));
                tradeData.setIsBuyerMaker(Boolean.valueOf(tradeMap.get("isBuyerMaker").toString()));
                tradeData.setSide(tradeData.getIsBuyerMaker() ? OrderSide.SELL : OrderSide.BUY);
                return tradeData;
            } catch (Exception e) {
                log.error("Failed to convert trade data: {}", tradeMap, e);
                return null;
            }
        }).filter(java.util.Objects::nonNull).collect(Collectors.toList());
    }
    
    private long getIntervalMinutes(String interval) {
        // Same as before
        return switch (interval) {
            case "1m" -> 1; case "3m" -> 3; case "5m" -> 5; case "15m" -> 15; case "30m" -> 30;
            case "1h" -> 60; case "2h" -> 120; case "4h" -> 240; case "6h" -> 360; case "8h" -> 480;
            case "12h" -> 720; case "1d" -> 1440; case "3d" -> 4320; case "1w" -> 10080;
            case "1M" -> 43200; // Approx. 30 days
            default -> 1;
        };
    }

    // Shutdown and utility methods
    @PreDestroy
    public void shutdown() {
        log.info("Shutting down HistoricalDataCollector...");
        isShuttingDown = true;
        
        if (influxDBStorage != null) {
            influxDBStorage.flush();
        }
        
        safeSleep(1000);
        log.info("HistoricalDataCollector has been shut down.");
    }
    
    private boolean safeSleep(long millis) {
        if (isShuttingDown || Thread.currentThread().isInterrupted()) return false;
        try {
            AsyncDelayUtils.delay(millis).join();
            return true;
        } catch (Exception e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    private boolean checkMemoryAndPause() {
        if (System.currentTimeMillis() - lastMemoryCheck < MEMORY_CHECK_INTERVAL) return true;
        lastMemoryCheck = System.currentTimeMillis();
        long totalMemory = runtime.totalMemory();
        long usedMemory = totalMemory - runtime.freeMemory();
        double memoryUsage = (double) usedMemory / totalMemory;

        if (memoryUsage > MEMORY_THRESHOLD) {
            log.warn("High memory usage detected ({}%). Triggering GC and pausing.", String.format("%.2f", memoryUsage * 100));
            System.gc();
            safeSleep(2000);
            return (double)(runtime.totalMemory() - runtime.freeMemory()) / runtime.totalMemory() < MEMORY_THRESHOLD;
        }
        return true;
    }

    private boolean shouldContinue() {
        return !isShuttingDown && !Thread.currentThread().isInterrupted();
    }
    
    /**
     * 检查并收集缺失的数据
     */
    private void checkAndCollectMissingData() {
        log.info("开始检查缺失的历史数据...");

        List<String> symbols = marketDataConfig.getCollector().getSymbols();
        if (symbols == null || symbols.isEmpty()) {
            log.warn("没有配置交易对，跳过数据检查");
            return;
        }

        for (String symbol : symbols) {
            if (!shouldContinue()) break;

            try {
                // 检查K线数据
                if (marketDataConfig.getCollector().getDataTypes().getKline().isEnabled()) {
                    checkAndCollectMissingKlineData(symbol);
                }

                // 检查深度数据
                if (marketDataConfig.getCollector().getDataTypes().getDepth().isEnabled()) {
                    collectHistoricalDepth(symbol);
                }

            } catch (Exception e) {
                log.error("检查交易对 {} 的数据时发生错误", symbol, e);
            }
        }

        log.info("历史数据检查完成");
    }

    /**
     * 检查并收集缺失的K线数据
     */
    private void checkAndCollectMissingKlineData(String symbol) {
        List<String> intervals = marketDataConfig.getCollector().getDataTypes().getKline().getIntervals();
        if (intervals == null || intervals.isEmpty()) {
            return;
        }

        for (String interval : intervals) {
            if (!shouldContinue()) break;

            try {
                // 检查数据库中的数据量
                Optional<KlineData> latestKline = marketDataService.getLatestKlineData(symbol, interval);

                Instant endTime = Instant.now();
                Instant startTime;

                if (latestKline.isPresent()) {
                    startTime = latestKline.get().getCloseTime().toInstant(ZoneOffset.UTC);
                    // 如果最新数据距离现在超过2小时，则收集缺失的数据
                    if (Duration.between(startTime, endTime).toHours() > 2) {
                        log.info("发现 {} [{}] 数据缺失，开始补充数据", symbol, interval);
                        executeKlineCollection(symbol, interval, startTime, endTime);
                    }
                } else {
                    // 如果没有数据，收集更多历史数据
                    int defaultDays = marketDataConfig.getCollector().getHistorical().getDefaultDays();
                    startTime = endTime.minus(defaultDays, ChronoUnit.DAYS);
                    log.info("没有找到 {} [{}] 的历史数据，开始收集 {} 天的数据", symbol, interval, defaultDays);
                    executeKlineCollection(symbol, interval, startTime, endTime);
                }

            } catch (Exception e) {
                log.error("检查 {} [{}] K线数据时发生错误", symbol, interval, e);
            }
        }
    }

    private void generateCollectionReport(String collectionType) {
        log.info("=== {} Report ===", collectionType);
        log.info("Total Collected: {}", totalCollectedCount.get());
        log.info("K-lines: {}", klineCollectedCount.get());
        log.info("Depth: {}", depthCollectedCount.get());
        log.info("Trades: {}", tradeCollectedCount.get());
        log.info("Errors: {}", errorCount.get());
        log.info("=========================");
    }
    
    // Inner classes for batching (BatchRequest, BatchResult) and stats
    private static class BatchRequest {
        final int batchNumber;
        final String symbol, interval;
        final Instant startTime, endTime;
        final int batchSize;

        BatchRequest(int bn, String s, String i, Instant st, Instant et, int bs) {
            batchNumber = bn; symbol = s; interval = i; startTime = st; endTime = et; batchSize = bs;
        }
    }

    private static class BatchResult {
        final int batchNumber;
        final boolean success;
        final String errorMessage;
        final int processedCount;

        BatchResult(int bn, boolean s, String em, int pc) {
            batchNumber = bn; success = s; errorMessage = em; processedCount = pc;
        }
    }

    /**
     * 获取统计信息
     */
    public CollectionStats getStats() {
        return CollectionStats.builder()
                .totalCollectedCount(totalCollectedCount.get())
                .klineCollectedCount(klineCollectedCount.get())
                .depthCollectedCount(depthCollectedCount.get())
                .tradeCollectedCount(tradeCollectedCount.get())
                .statsCollectedCount(statsCollectedCount.get())
                .errorCount(errorCount.get())
                .build();
    }

    /**
     * 获取收集器状态
     */
    public String getStatus() {
        if (isShuttingDown) {
            return "SHUTTING_DOWN";
        }
        // Simplified status check
        return "IDLE";
    }

    /**
     * 获取详细统计信息
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("status", getStatus());
        stats.put("totalCollectedCount", totalCollectedCount.get());
        stats.put("klineCollectedCount", klineCollectedCount.get());
        stats.put("depthCollectedCount", depthCollectedCount.get());
        stats.put("tradeCollectedCount", tradeCollectedCount.get());
        stats.put("statsCollectedCount", statsCollectedCount.get());
        stats.put("errorCount", errorCount.get());
        stats.put("isShuttingDown", isShuttingDown);
        stats.put("timestamp", Instant.now().toString());
        return stats;
    }

    /**
     * 强制收集历史数据
     */
    public CompletableFuture<Void> forceCollectHistoricalData(List<String> symbols, int days, boolean forceFullCollection) {
        return CompletableFuture.runAsync(() -> {
            log.info("Starting forced historical data collection: symbols={}, days={}, forceFull={}",
                symbols, days, forceFullCollection);

            for (String symbol : symbols) {
                if (!shouldContinue()) {
                    log.info("Collection interrupted.");
                    return;
                }
                if (marketDataConfig.getCollector().getDataTypes().getKline().isEnabled()) {
                    collectHistoricalKlines(symbol, days, forceFullCollection);
                }
                // Add other data types as needed
            }
            log.info("Forced historical data collection finished.");
        }, virtualThreadExecutor);
    }

    /**
     * 收集历史K线数据（重载以支持自定义天数）
     */
    public void collectHistoricalKlines(String symbol, int days, boolean forceFull) {
        if (!shouldContinue()) return;

        List<String> intervals = marketDataConfig.getCollector().getDataTypes().getKline().getIntervals();

        for (String interval : intervals) {
            if (!shouldContinue()) break;

            Instant endTime = Instant.now();
            Instant startTime;

            if (forceFull) {
                startTime = endTime.minus(days, ChronoUnit.DAYS);
                log.info("Forcing full K-line collection for {} [{}]: {} to {}", symbol, interval, startTime, endTime);
            } else {
                Optional<KlineData> latestKline = marketDataService.getLatestKlineData(symbol, interval);
                if (latestKline.isPresent()) {
                    startTime = latestKline.get().getCloseTime().toInstant(ZoneOffset.UTC);
                } else {
                    startTime = endTime.minus(days, ChronoUnit.DAYS);
                    log.info("No existing K-line data for {} [{}], collecting full history.", symbol, interval);
                }
            }
            
            if (startTime.isBefore(endTime)) {
                executeKlineCollection(symbol, interval, startTime, endTime);
            }
        }
    }
    

/**
 * 收集统计信息（保持向后兼容）
 */
@Data
@Builder
public static class CollectionStats {
    private long totalCollectedCount;
    private long klineCollectedCount;
    private long depthCollectedCount;
    private long tradeCollectedCount;
    private long statsCollectedCount;
    private long errorCount;
}

/**
 * 性能统计信息
 */
@Data
@Builder
public static class PerformanceStats {
    private long totalCollectedCount;
    private long klineCollectedCount;
    private long depthCollectedCount;
    private long tradeCollectedCount;
    private long statsCollectedCount;
    private long errorCount;
    private double memoryUsage;
    private long usedMemoryMB;
    private long totalMemoryMB;
    private boolean isShuttingDown;
}
}