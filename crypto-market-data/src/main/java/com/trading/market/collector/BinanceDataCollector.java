package com.trading.market.collector;

import com.trading.common.config.MarketDataConfig;
import com.trading.common.dto.KlineData;
import com.trading.market.processor.DataProcessor;
import com.trading.market.websocket.MarketDataMessageHandler;
import com.trading.sdk.websocket.WebSocketManager;
import com.trading.common.dto.Symbol;
import com.trading.common.enums.StreamType;
import com.trading.common.exception.SdkException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.List;
import java.util.Map;

/**
 * 币安实时数据收集器核心。
 * <p>
 * 该组件负责通过 WebSocket 从币安交易所实时订阅和接收市场数据。
 * 它利用了 {@code crypto-sdk} 模块提供的 {@link WebSocketManager} 来处理底层的连接和订阅管理。
 * 主要职责包括：
 * <ul>
 *     <li>根据 {@link MarketDataConfig} 中的配置自动订阅指定的交易对 (symbols) 和数据类型 (K-line, Depth, etc.)。</li>
 *     <li>将接收到的原始数据转发给 {@link DataProcessor} 进行后续处理。</li>
 *     <li>管理所有活跃的 WebSocket 订阅生命周期。</li>
 *     <li>内置一个监控服务，定期检查连接健康状况并记录统计信息。</li>
 * </ul>
 * 该收集器被设计为在应用启动时自动初始化 ({@code @PostConstruct})，并在关闭时优雅地清理所有资源 ({@code @PreDestroy})。
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
@Order(500) // 确保在WebSocketManager之前销毁
@ConditionalOnProperty(prefix = "trading.market-data.collector", name = "enabled", havingValue = "true")
public class BinanceDataCollector {

    private static final Logger log = LoggerFactory.getLogger(BinanceDataCollector.class);

    @Autowired
    private MarketDataConfig marketDataConfig;

    @Autowired
    private DataProcessor dataProcessor;

    @Autowired
    private WebSocketManager webSocketManager;

    @Autowired
    private MarketDataMessageHandler messageHandler;

    // 订阅管理
    private final Map<String, String> activeSubscriptions = new ConcurrentHashMap<>();
    private ScheduledExecutorService monitorExecutor;

    // 统计信息
    private final AtomicLong messageCount = new AtomicLong(0);
    private final AtomicLong connectionCount = new AtomicLong(0);
    private volatile long startTime;

    /**
     * 初始化数据收集器。此方法在 Spring Bean 初始化后自动被调用。
     * <p>
     * 初始化流程包括：
     * 1.  验证关键配置是否存在且有效。
     * 2.  如果配置有效，则启动后台监控线程，用于定期检查连接健康和报告统计数据。
     * 3.  调用 {@link #startDataCollection()} 方法，根据配置发起对所有指定交易对的数据流订阅。
     * </p>
     *
     * @throws RuntimeException 如果初始化过程中发生无法恢复的错误，例如关键配置缺失。
     */
    @PostConstruct
    public void initialize() {
        log.info("初始化币安数据收集器...");
        startTime = System.currentTimeMillis();

        try {
            // 验证配置
            validateConfiguration();

            // 初始化监控执行器
            initializeMonitorExecutor();

            // 启动数据收集
            startDataCollection();

            if (marketDataConfig.getCollector().isEnabled() &&
                marketDataConfig.getCollector().getSymbols() != null) {
                log.info("币安数据收集器初始化完成，已订阅 {} 个交易对",
                        marketDataConfig.getCollector().getSymbols().size());
            } else {
                log.info("币安数据收集器初始化完成（开发模式，数据收集已禁用）");
            }
        } catch (Exception e) {
            log.error("币安数据收集器初始化失败", e);
            throw new RuntimeException("数据收集器初始化失败", e);
        }
    }

    /**
     * 验证配置
     */
    private void validateConfiguration() {
        log.info("=== BinanceDataCollector 配置验证开始 ===");

        if (marketDataConfig == null) {
            throw new IllegalStateException("MarketDataConfig未注入！");
        }

        if (marketDataConfig.getCollector() == null) {
            throw new IllegalStateException("Collector配置为null！");
        }

        if (marketDataConfig.getCollector().getDataTypes() == null) {
            throw new IllegalStateException("DataTypes配置为null！");
        }

        if (marketDataConfig.getCollector().getDataTypes().getKline() == null) {
            throw new IllegalStateException("Kline配置为null！");
        }

        var intervals = marketDataConfig.getCollector().getDataTypes().getKline().getIntervals();
        if (intervals == null) {
            throw new IllegalStateException("Intervals配置为null！请检查配置文件中的trading.market-data.collector.data-types.kline.intervals");
        }

        if (intervals.isEmpty()) {
            throw new IllegalStateException("Intervals配置为空！请检查配置文件中的trading.market-data.collector.data-types.kline.intervals");
        }

        log.info("✅ 配置验证通过: intervals={}", intervals);
        log.info("=== BinanceDataCollector 配置验证结束 ===");
    }

    /**
     * 启动数据收集
     */
    private void startDataCollection() {
        // 检查是否启用数据收集
        if (!marketDataConfig.getCollector().isEnabled()) {
            log.info("数据收集已禁用，跳过数据收集启动");
            return;
        }

        List<String> symbols = marketDataConfig.getCollector().getSymbols();
        if (symbols == null || symbols.isEmpty()) {
            log.warn("交易对列表为空，跳过数据收集");
            return;
        }

        for (String symbolStr : symbols) {
            try {
                Symbol symbol = Symbol.of(symbolStr);
                subscribeToSymbol(symbol);
                connectionCount.incrementAndGet();
            } catch (Exception e) {
                log.error("订阅交易对失败: symbol={}", symbolStr, e);
            }
        }

        // 启动连接监控
        startConnectionMonitoring();
    }

    /**
     * 订阅单个交易对的所有已启用的数据流。
     * <p>
     * 此方法会检查 {@link MarketDataConfig} 中每种数据类型（K线、深度、交易等）的 'enabled' 标志。
     * 对于已启用的数据类型，它会调用 {@link WebSocketManager} 中相应的 {@code subscribe} 方法来建立数据流。
     * 成功的订阅会被记录在 {@code activeSubscriptions} 中，以便进行后续的健康检查和管理。
     * </p>
     *
     * @param symbol 要订阅的交易对 ({@link Symbol})。
     * @throws SdkException 如果底层订阅过程中发生错误。
     */
    private void subscribeToSymbol(Symbol symbol) throws SdkException {
        String symbolStr = symbol.getSymbol();

        // 订阅K线数据
        if (marketDataConfig.getCollector().getDataTypes().getKline().isEnabled()) {
            List<String> intervals = marketDataConfig.getCollector().getDataTypes().getKline().getIntervals();
            for (String interval : intervals) {
                String subscriptionKey = webSocketManager.subscribeKlineStream(symbol, interval, StreamType.UM_FUTURES);
                activeSubscriptions.put(String.format("kline_%s_%s", symbolStr, interval), subscriptionKey);
                log.info("已订阅K线数据流: symbol={}, interval={}, key={}", symbolStr, interval, subscriptionKey);
            }
        }

        // 订阅深度数据
        if (marketDataConfig.getCollector().getDataTypes().getDepth().isEnabled()) {
            List<Integer> levels = marketDataConfig.getCollector().getDataTypes().getDepth().getLevels();
            int speed = marketDataConfig.getCollector().getDataTypes().getDepth().getSpeed();
            for (Integer level : levels) {
                String subscriptionKey = webSocketManager.subscribeDepthStream(symbol, level, speed, StreamType.UM_FUTURES);
                activeSubscriptions.put(String.format("depth_%s_%d", symbolStr, level), subscriptionKey);
                log.info("已订阅深度数据流: symbol={}, levels={}, speed={}ms, key={}", symbolStr, level, speed, subscriptionKey);
            }
        }

        // 订阅交易数据
        if (marketDataConfig.getCollector().getDataTypes().getTrade().isEnabled()) {
            String subscriptionKey = webSocketManager.subscribeTradeStream(symbol, StreamType.UM_FUTURES);
            activeSubscriptions.put(String.format("trade_%s", symbolStr), subscriptionKey);
            log.info("已订阅交易数据流: symbol={}, key={}", symbolStr, subscriptionKey);
        }

        // 订阅24小时统计数据
        if (marketDataConfig.getCollector().getDataTypes().getTicker().isEnabled()) {
            String subscriptionKey = webSocketManager.subscribeTickerStream(symbol, StreamType.UM_FUTURES);
            activeSubscriptions.put(String.format("ticker_%s", symbolStr), subscriptionKey);
            log.info("已订阅24小时统计数据流: symbol={}, key={}", symbolStr, subscriptionKey);
        }
    }

    /**
     * 初始化监控执行器
     */
    private void initializeMonitorExecutor() {
        // 减少监控线程数，降低CPU消耗
        monitorExecutor = Executors.newScheduledThreadPool(1, r -> {
            Thread thread = new Thread(r, "data-collector-monitor");
            thread.setDaemon(true);
            return thread;
        });
    }

    /**
     * 启动连接监控
     */
    private void startConnectionMonitoring() {
        // 定期检查连接状态 - 调整为2分钟间隔，减少CPU消耗
        monitorExecutor.scheduleWithFixedDelay(() -> {
            try {
                checkConnectionHealth();
            } catch (Exception e) {
                log.error("连接健康检查失败", e);
            }
        }, 120, 120, TimeUnit.SECONDS);

        // 定期输出统计信息 - 调整为10分钟间隔，减少日志输出频率
        monitorExecutor.scheduleWithFixedDelay(() -> {
            try {
                logStatistics();
            } catch (Exception e) {
                log.error("输出统计信息失败", e);
            }
        }, 600, 600, TimeUnit.SECONDS);
    }

    /**
     * 检查连接健康状态
     */
    private void checkConnectionHealth() {
        int activeCount = 0;
        int inactiveCount = 0;

        for (Map.Entry<String, String> entry : activeSubscriptions.entrySet()) {
            String dataType = entry.getKey();
            String subscriptionKey = entry.getValue();

            if (webSocketManager.isSubscriptionActive(subscriptionKey)) {
                activeCount++;
            } else {
                inactiveCount++;
                log.warn("检测到非活跃订阅: dataType={}, subscriptionKey={}", dataType, subscriptionKey);
            }
        }

        log.debug("连接健康检查完成: 活跃连接={}, 非活跃连接={}", activeCount, inactiveCount);
    }

    /**
     * 输出统计信息
     */
    private void logStatistics() {
        long currentTime = System.currentTimeMillis();
        long runningTime = currentTime - startTime;
        long messageCountValue = messageCount.get();
        long connectionCountValue = connectionCount.get();

        double messagesPerSecond = runningTime > 0 ? (messageCountValue * 1000.0) / runningTime : 0;

        log.info("数据收集器统计: 运行时间={}ms, 总消息数={}, 连接数={}, 消息速率={:.2f}/秒",
                runningTime, messageCountValue, connectionCountValue, messagesPerSecond);
    }

    /**
     * 增加消息计数
     */
    public void incrementMessageCount() {
        messageCount.incrementAndGet();
    }

    /**
     * 获取活跃订阅数量
     */
    public int getActiveSubscriptionCount() {
        return activeSubscriptions.size();
    }

    /**
     * 获取消息计数
     */
    public long getMessageCount() {
        return messageCount.get();
    }

    /**
     * 创建真实的K线数据（用于测试环境时可以生成模拟数据）
     * 注意：生产环境应该使用真实的API数据
     */
    public KlineData createKlineDataForTesting(String symbol, String interval) {
        // 在测试环境中，可以生成模拟数据
        if (isTestEnvironment()) {
            return createMockKlineData(symbol, interval);
        }

        // 生产环境应该调用真实的API
        throw new UnsupportedOperationException("生产环境不支持创建模拟数据，请使用真实API");
    }

    /**
     * 创建模拟K线数据（仅用于测试）
     */
    private KlineData createMockKlineData(String symbol, String interval) {
        // 使用当前时间作为基准，生成更真实的数据
        long currentTime = System.currentTimeMillis();
        long intervalMs = parseIntervalToMillis(interval);

        // 生成基于历史数据的模拟价格
        BigDecimal basePrice = getBasePriceForSymbol(symbol);
        double volatility = 0.02; // 2%的波动率

        BigDecimal openPrice = basePrice.multiply(BigDecimal.valueOf(1 + (Math.random() - 0.5) * volatility));
        BigDecimal highPrice = openPrice.multiply(BigDecimal.valueOf(1 + Math.random() * volatility));
        BigDecimal lowPrice = openPrice.multiply(BigDecimal.valueOf(1 - Math.random() * volatility));
        BigDecimal closePrice = openPrice.multiply(BigDecimal.valueOf(1 + (Math.random() - 0.5) * volatility));

        KlineData klineData = new KlineData();
        klineData.setSymbol(symbol);
        klineData.setInterval(interval);
        klineData.setOpenTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(currentTime), ZoneId.systemDefault()));
        klineData.setCloseTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(currentTime + intervalMs), ZoneId.systemDefault()));
        klineData.setOpenPrice(openPrice);
        klineData.setHighPrice(highPrice);
        klineData.setLowPrice(lowPrice);
        klineData.setClosePrice(closePrice);
        klineData.setVolume(BigDecimal.valueOf(Math.random() * 100));
        klineData.setQuoteVolume(closePrice.multiply(BigDecimal.valueOf(Math.random() * 100)));
        klineData.setTradeCount( (long)(Math.random() * 1000));
        klineData.setTakerBuyVolume(BigDecimal.valueOf(Math.random() * 50));
        klineData.setTakerBuyQuoteVolume(closePrice.multiply(BigDecimal.valueOf(Math.random() * 50)));
        // isClosed and source are no longer part of the DTO
        return klineData;
    }

    /**
     * 检查是否为测试环境
     */
    private boolean isTestEnvironment() {
        String profile = System.getProperty("spring.profiles.active", "");
        return profile.contains("test") || profile.contains("dev");
    }

    /**
     * 解析时间间隔为毫秒
     */
    private long parseIntervalToMillis(String interval) {
        if (interval.endsWith("m")) {
            return Long.parseLong(interval.substring(0, interval.length() - 1)) * 60 * 1000;
        } else if (interval.endsWith("h")) {
            return Long.parseLong(interval.substring(0, interval.length() - 1)) * 60 * 60 * 1000;
        } else if (interval.endsWith("d")) {
            return Long.parseLong(interval.substring(0, interval.length() - 1)) * 24 * 60 * 60 * 1000;
        }
        return 60 * 1000; // 默认1分钟
    }

    /**
     * 获取交易对的基准价格
     */
    private BigDecimal getBasePriceForSymbol(String symbol) {
        // 根据不同的交易对返回不同的基准价格
        switch (symbol.toUpperCase()) {
            case "BTCUSDT":
                return new BigDecimal("50000.00");
            case "ETHUSDT":
                return new BigDecimal("3000.00");
            case "BNBUSDT":
                return new BigDecimal("400.00");
            default:
                return new BigDecimal("100.00");
        }
    }

    /**
     * 在应用程序关闭时，优雅地销毁数据收集器并释放所有资源。
     * <p>
     * 此方法由 Spring 在容器销毁 Bean 之前调用。其清理流程保证了资源的有序释放：
     * 1.  首先停止 {@link MarketDataMessageHandler}，防止在关闭过程中处理新的 WebSocket 消息。
     * 2.  短暂等待，以确保任何正在处理中的消息能够完成。
     * 3.  遍历所有活跃的订阅，并调用 {@link WebSocketManager#unsubscribe(String)} 来优雅地关闭每个连接。
     * 4.  最后，关闭用于监控的 {@code ScheduledExecutorService}。
     * </p>
     */
    @PreDestroy
    public void destroy() {
        log.info("币安数据收集器正在关闭...");

        try {
            // 1. 立即关闭所有WebSocket连接，从源头停止事件
            if (webSocketManager != null) {
                webSocketManager.shutdown();
                log.info("WebSocket管理器已关闭");
            }

            // 2. 停止消息处理器，防止处理残留消息
            if (messageHandler != null) {
                messageHandler.shutdown();
                log.info("消息处理器已停止");
            }

            // 短暂等待，确保关闭事件传播
            try {
                // 使用标准Thread.sleep以避免对特定工具类的依赖
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // 3. 清理订阅记录（此时连接已关，仅为清理内存）
            activeSubscriptions.clear();
            log.info("已清理所有活动订阅记录");


            // 4. 关闭监控执行器
            if (monitorExecutor != null && !monitorExecutor.isShutdown()) {
                monitorExecutor.shutdown();
                try {
                    if (!monitorExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        monitorExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    monitorExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }

            log.info("币安数据收集器关闭完成");
        } catch (Exception e) {
            log.error("关闭数据收集器时发生错误", e);
        }
    }
}
