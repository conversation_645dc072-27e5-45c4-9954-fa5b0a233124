package com.trading.market.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.trading.common.cache.MarketDataCacheService;
import com.trading.common.utils.AsyncDelayUtils;
import com.trading.market.mapper.KlineDataMapper;
import com.trading.market.mapper.MarketDataMapper;
import com.trading.market.mapper.DepthDataMapper;
import com.trading.market.mapper.DataQualityStatsMapper;
import com.trading.common.dto.KlineData;
import com.trading.common.dto.TradeData;
import com.trading.common.dto.DepthData;
import com.trading.common.dto.DataQualityStats;
import com.trading.market.service.MarketDataService;
import com.trading.market.storage.MySQLStorage;
import com.trading.market.storage.InfluxDBStorage;
import com.trading.market.processor.DataValidator;

import org.springframework.transaction.annotation.Isolation;
import com.trading.sdk.api.MarketDataApi;
import com.trading.common.api.ApiResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 市场数据服务实现类
 * 集成多级缓存（本地+Redis）和双存储（MySQL+InfluxDB）
 * 提供完整的数据访问和业务逻辑处理
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
@Transactional
public class MarketDataServiceImpl implements MarketDataService {

    private static final Logger log = LoggerFactory.getLogger(MarketDataServiceImpl.class);

    @Autowired
    private KlineDataMapper klineDataMapper;

    @Autowired
    private MarketDataMapper marketDataMapper;

    @Autowired
    private DepthDataMapper depthDataMapper;

    @Autowired
    private DataQualityStatsMapper dataQualityStatsMapper;

    @Autowired
    private MySQLStorage mySQLStorage;

    @Autowired(required = false)
    private InfluxDBStorage influxDBStorage;

    @Autowired
    private MarketDataCacheService cacheService;

    @Autowired
    private DataValidator dataValidator;

    @Autowired
    private MarketDataApi marketDataApi;

    @Autowired
    private Executor taskExecutor;

    // 统计计数器
    private final AtomicLong klineServiceCount = new AtomicLong(0);
    private final AtomicLong depthServiceCount = new AtomicLong(0);
    private final AtomicLong marketDataServiceCount = new AtomicLong(0);
    private final AtomicLong cacheHitCount = new AtomicLong(0);
    private final AtomicLong cacheMissCount = new AtomicLong(0);

    // ==================== K线数据服务实现 ====================

    @Override
    public CompletableFuture<Void> saveKlineDataAsync(KlineData klineData) {
        if (!dataValidator.validateKlineData(klineData)) {
            log.warn("K线数据验证失败，跳过保存: {}", klineData);
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() ->
            mySQLStorage.saveKlineData(klineData), taskExecutor);

        CompletableFuture<Void> influxFuture = (influxDBStorage == null) ? CompletableFuture.completedFuture(null) :
            CompletableFuture.runAsync(() -> influxDBStorage.saveKlineData(klineData), taskExecutor);

        return CompletableFuture.allOf(mysqlFuture, influxFuture)
            .thenAcceptAsync(v -> {
                cacheService.cacheKlineData(klineData.getSymbol(), klineData.getInterval(), klineData);
                klineServiceCount.incrementAndGet();
                log.debug("K线数据双写存储与缓存更新成功: symbol={}, interval={}, openTime={}",
                    klineData.getSymbol(), klineData.getInterval(), klineData.getOpenTime());
            }, taskExecutor)
            .exceptionally(ex -> {
                log.error("异步保存K线数据失败，可能导致数据不一致: {}", klineData, ex);
                return null;
            });
    }

    @Override
    public CompletableFuture<Void> batchSaveKlineDataAsync(List<KlineData> klineDataList) {
        if (klineDataList == null || klineDataList.isEmpty()) {
            log.warn("K线数据列表为空，跳过批量保存");
            return CompletableFuture.completedFuture(null);
        }

        List<KlineData> validDataList = klineDataList.stream()
            .filter(data -> dataValidator.validateKlineData(data))
            .collect(Collectors.toList());

        if (validDataList.isEmpty()) {
            log.warn("没有有效的K线数据，跳过批量保存");
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() ->
            mySQLStorage.saveKlineDataBatch(validDataList), taskExecutor);

        CompletableFuture<Void> influxFuture = (influxDBStorage == null) ? CompletableFuture.completedFuture(null) :
            CompletableFuture.runAsync(() -> influxDBStorage.saveKlineDataBatch(validDataList), taskExecutor);

        return CompletableFuture.allOf(mysqlFuture, influxFuture)
            .thenAcceptAsync(v -> {
                validDataList.forEach(data ->
                    cacheService.cacheKlineData(data.getSymbol(), data.getInterval(), data));
                klineServiceCount.addAndGet(validDataList.size());
                log.info("K线数据批量双写存储与缓存更新成功: 数量={}", validDataList.size());
            }, taskExecutor)
            .exceptionally(ex -> {
                log.error("异步批量保存K线数据失败，可能导致数据不一致: 数量={}", validDataList.size(), ex);
                return null;
            });
    }

    @Override
    public List<KlineData> getKlineData(String symbol, String interval, int limit) {
        try {
            // 先尝试从缓存获取
            Optional<List<KlineData>> cachedData = cacheService.<KlineData>getKlineDataList(symbol, interval);
            if (cachedData.isPresent()) {
                cacheHitCount.incrementAndGet();
                List<KlineData> result = cachedData.get();
                return result.size() > limit ? result.subList(0, limit) : result;
            }

            // 缓存未命中，从数据库查询
            cacheMissCount.incrementAndGet();
            List<KlineData> dataList = klineDataMapper.findBySymbolAndInterval(symbol, interval, limit);

            // 更新缓存
            if (!dataList.isEmpty()) {
                cacheService.cacheKlineDataList(symbol, interval, dataList);
            }

            log.debug("查询K线数据: symbol={}, interval={}, limit={}, 结果数量={}",
                symbol, interval, limit, dataList.size());

            return dataList;

        } catch (Exception e) {
            log.error("查询K线数据失败: symbol={}, interval={}, limit={}", symbol, interval, limit, e);
            throw new RuntimeException("查询K线数据失败", e);
        }
    }

    @Override
    public List<KlineData> getKlineDataByTimeRange(String symbol, String interval,
                                                  Instant startTime, Instant endTime) {
        try {
            List<KlineData> dataList = klineDataMapper.findBySymbolAndIntervalAndTimeRange(
                symbol, interval, startTime, endTime, 1000);

            log.debug("按时间范围查询K线数据: symbol={}, interval={}, startTime={}, endTime={}, 结果数量={}",
                symbol, interval, startTime, endTime, dataList.size());

            return dataList;

        } catch (Exception e) {
            log.error("按时间范围查询K线数据失败: symbol={}, interval={}, startTime={}, endTime={}",
                symbol, interval, startTime, endTime, e);
            throw new RuntimeException("按时间范围查询K线数据失败", e);
        }
    }

    @Override
    public CompletableFuture<List<KlineData>> getHistoricalKlineData(String symbol, String interval, int days) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("获取历史K线数据: symbol={}, interval={}, days={}", symbol, interval, days);

                // 计算开始时间
                Instant endTime = Instant.now();
                Instant startTime = endTime.minusSeconds(days * 24 * 3600L);

                // 从API获取历史数据
                List<KlineData> historicalData = new ArrayList<>();

                // 分批获取数据，避免单次请求过大
                int batchDays = Math.min(days, 30); // 每次最多获取30天
                Instant currentStart = startTime;

                while (currentStart.isBefore(endTime)) {
                    Instant currentEnd = currentStart.plusSeconds(batchDays * 24 * 3600L);
                    if (currentEnd.isAfter(endTime)) {
                        currentEnd = endTime;
                    }

                    // 从数据库查询现有数据
                    List<KlineData> existingData = getKlineDataByTimeRange(symbol, interval, currentStart, currentEnd);
                    historicalData.addAll(existingData);

                    currentStart = currentEnd;
                }

                log.info("获取历史K线数据完成: symbol={}, interval={}, days={}, 数量={}",
                    symbol, interval, days, historicalData.size());

                return historicalData;

            } catch (Exception e) {
                log.error("获取历史K线数据失败: symbol={}, interval={}, days={}", symbol, interval, days, e);
                throw new RuntimeException("获取历史K线数据失败", e);
            }
        }, taskExecutor);
    }

    @Override
    public Optional<KlineData> getLatestKlineData(String symbol, String interval) {
        try {
            // 先尝试从缓存获取
            Optional<KlineData> cachedData = cacheService.getKlineData(symbol, interval, KlineData.class);
            if (cachedData.isPresent()) {
                cacheHitCount.incrementAndGet();
                return cachedData;
            }

            // 缓存未命中，从数据库查询
            cacheMissCount.incrementAndGet();
            KlineData latestData = klineDataMapper.findLatestBySymbolAndInterval(symbol, interval);

            if (latestData != null) {
                // 更新缓存
                cacheService.cacheKlineData(symbol, interval, latestData);
                return Optional.of(latestData);
            }

            return Optional.empty();

        } catch (Exception e) {
            log.error("查询最新K线数据失败: symbol={}, interval={}", symbol, interval, e);
            throw new RuntimeException("查询最新K线数据失败", e);
        }
    }

    @Override
    public Optional<KlineData> getEarliestKlineData(String symbol, String interval) {
        try {
            // 从数据库查询最早的K线数据
            KlineData earliestData = klineDataMapper.findEarliestBySymbolAndInterval(symbol, interval);

            if (earliestData != null) {
                return Optional.of(earliestData);
            }

            return Optional.empty();

        } catch (Exception e) {
            log.error("查询最早K线数据失败: symbol={}, interval={}", symbol, interval, e);
            throw new RuntimeException("查询最早K线数据失败", e);
        }
    }

    @Override
    public List<KlineData> getHistoricalData(String symbol, String interval, Instant startTime, Instant endTime) {
        try {
            // This is a simple pass-through to the mapper.
            // A more robust implementation might include pagination or streaming.
            final int MAX_RESULTS = 100000; // Safeguard to prevent OOM errors.
            List<KlineData> dataList = klineDataMapper.findBySymbolAndIntervalAndTimeRange(
                symbol, interval, startTime, endTime, MAX_RESULTS);

            log.info("Fetched {} historical data points for {} from {} to {}", dataList.size(), symbol, startTime, endTime);
            return dataList;
        } catch (Exception e) {
            log.error("Failed to retrieve historical data for symbol {}: {}", symbol, e.getMessage(), e);
            throw new RuntimeException("Failed to retrieve historical data", e);
        }
    }

    // ==================== 深度数据服务实现 ====================

    @Override
    public CompletableFuture<Void> saveDepthDataAsync(DepthData depthData) {
        if (!dataValidator.validateDepthData(depthData)) {
            log.warn("深度数据验证失败，跳过保存: {}", depthData);
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() ->
            mySQLStorage.saveDepthDataBatch(List.of(depthData)), taskExecutor);

        CompletableFuture<Void> influxFuture = (influxDBStorage == null) ? CompletableFuture.completedFuture(null) :
            CompletableFuture.runAsync(() -> influxDBStorage.saveDepthData(depthData), taskExecutor);

        return CompletableFuture.allOf(mysqlFuture, influxFuture)
            .thenAcceptAsync(v -> {
                cacheService.cacheDepthData(depthData.getSymbol(), 0, depthData); // Levels removed
                depthServiceCount.incrementAndGet();
                log.debug("深度数据双写存储与缓存更新成功: symbol={}, timestamp={}",
                    depthData.getSymbol(), depthData.getTimestamp());
            }, taskExecutor)
            .exceptionally(ex -> {
                log.error("异步保存深度数据失败，可能导致数据不一致: {}", depthData, ex);
                return null;
            });
    }

    @Override
    public CompletableFuture<Void> batchSaveDepthDataAsync(List<DepthData> depthDataList) {
        if (depthDataList == null || depthDataList.isEmpty()) {
            log.warn("深度数据列表为空，跳过批量保存");
            return CompletableFuture.completedFuture(null);
        }

        List<DepthData> validDataList = depthDataList.stream()
            .filter(data -> dataValidator.validateDepthData(data))
            .collect(Collectors.toList());

        if (validDataList.isEmpty()) {
            log.warn("没有有效的深度数据，跳过批量保存");
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() ->
            mySQLStorage.saveDepthDataBatch(validDataList), taskExecutor);

        CompletableFuture<Void> influxFuture = (influxDBStorage == null) ? CompletableFuture.completedFuture(null) :
            CompletableFuture.runAsync(() -> influxDBStorage.saveDepthDataBatch(validDataList), taskExecutor);

        return CompletableFuture.allOf(mysqlFuture, influxFuture)
            .thenAcceptAsync(v -> {
                validDataList.forEach(data ->
                    cacheService.cacheDepthData(data.getSymbol(), 0, data)); // Levels removed
                depthServiceCount.addAndGet(validDataList.size());
                log.info("深度数据批量双写存储与缓存更新成功: 数量={}", validDataList.size());
            }, taskExecutor)
            .exceptionally(ex -> {
                log.error("异步批量保存深度数据失败，可能导致数据不一致: 数量={}", validDataList.size(), ex);
                return null;
            });
    }

    @Override
    public List<DepthData> getDepthData(String symbol, Integer levels, int limit) {
        try {
            // 先尝试从缓存获取
            Optional<DepthData> cachedData = cacheService.getDepthData(symbol, levels, DepthData.class);
            if (cachedData.isPresent()) {
                cacheHitCount.incrementAndGet();
                // 如果缓存中只有一条数据，从数据库获取更多
                if (limit > 1) {
                    return depthDataMapper.findBySymbolAndLevels(symbol, levels, limit);
                } else {
                    return List.of(cachedData.get());
                }
            }

            // 缓存未命中，从数据库查询
            cacheMissCount.incrementAndGet();
            List<DepthData> dataList = depthDataMapper.findBySymbolAndLevels(symbol, levels, limit);

            // 更新缓存（缓存最新的一条）
            if (!dataList.isEmpty()) {
                cacheService.cacheDepthData(symbol, levels, dataList.get(0));
            }

            log.debug("查询深度数据: symbol={}, levels={}, limit={}, 结果数量={}",
                symbol, levels, limit, dataList.size());

            return dataList;

        } catch (Exception e) {
            log.error("查询深度数据失败: symbol={}, levels={}, limit={}", symbol, levels, limit, e);
            throw new RuntimeException("查询深度数据失败", e);
        }
    }

    @Override
    @Transactional(readOnly = true, isolation = Isolation.READ_UNCOMMITTED)
    public Optional<DepthData> getLatestDepthData(String symbol, Integer levels) {
        try {
            // 先尝试从缓存获取
            Optional<DepthData> cachedData = cacheService.getDepthData(symbol, levels, DepthData.class);
            if (cachedData.isPresent()) {
                cacheHitCount.incrementAndGet();
                return cachedData;
            }

            // 缓存未命中，从数据库查询
            cacheMissCount.incrementAndGet();
            DepthData latestData = depthDataMapper.findLatestBySymbolAndLevels(symbol, levels);

            if (latestData != null) {
                // 更新缓存
                cacheService.cacheDepthData(symbol, levels, latestData);
            }
            return Optional.ofNullable(latestData);

        } catch (Exception e) {
            log.error("查询最新深度数据失败: symbol={}, levels={}", symbol, levels, e);
            throw new RuntimeException("查询最新深度数据失败", e);
        }
    }

    @Override
    public List<DepthData> getDepthDataByTimeRange(String symbol, Integer levels,
                                                  Instant startTime, Instant endTime) {
        try {
            List<DepthData> dataList = depthDataMapper.findBySymbolAndLevelsAndTimeRange(
                symbol, levels, startTime, endTime, 1000);

            log.debug("按时间范围查询深度数据: symbol={}, levels={}, startTime={}, endTime={}, 结果数量={}",
                symbol, levels, startTime, endTime, dataList.size());

            return dataList;

        } catch (Exception e) {
            log.error("按时间范围查询深度数据失败: symbol={}, levels={}, startTime={}, endTime={}",
                symbol, levels, startTime, endTime, e);
            throw new RuntimeException("按时间范围查询深度数据失败", e);
        }
    }

    // ==================== 市场数据服务实现 ====================

    @Override
    public CompletableFuture<Void> saveMarketDataAsync(TradeData tradeData) {
        if (!dataValidator.validateMarketData(tradeData)) {
            log.warn("市场数据验证失败，跳过保存: {}", tradeData);
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() ->
            mySQLStorage.saveMarketData(tradeData), taskExecutor);

        CompletableFuture<Void> influxFuture = (influxDBStorage == null) ? CompletableFuture.completedFuture(null) :
            CompletableFuture.runAsync(() -> influxDBStorage.saveMarketData(tradeData), taskExecutor);

        return CompletableFuture.allOf(mysqlFuture, influxFuture)
            .thenAcceptAsync(v -> {
                cacheService.cacheTradeData(tradeData.getSymbol(), tradeData);
                if (tradeData.getPrice() != null) {
                    cacheService.cacheLatestPrice(tradeData.getSymbol(), tradeData.getPrice());
                }
                marketDataServiceCount.incrementAndGet();
                log.debug("市场数据双写存储与缓存更新成功: symbol={}, timestamp={}",
                    tradeData.getSymbol(), tradeData.getTradeTime());
            }, taskExecutor)
            .exceptionally(ex -> {
                log.error("异步保存市场数据失败，可能导致数据不一致: {}", tradeData, ex);
                return null;
            });
    }

    @Override
    public CompletableFuture<Void> batchSaveMarketDataAsync(List<TradeData> marketDataList) {
        if (marketDataList == null || marketDataList.isEmpty()) {
            log.warn("市场数据列表为空，跳过批量保存");
            return CompletableFuture.completedFuture(null);
        }

        List<TradeData> validDataList = marketDataList.stream()
            .filter(data -> dataValidator.validateMarketData(data))
            .collect(Collectors.toList());

        if (validDataList.isEmpty()) {
            log.warn("没有有效的市场数据，跳过批量保存");
            return CompletableFuture.completedFuture(null);
        }

        CompletableFuture<Void> mysqlFuture = CompletableFuture.runAsync(() ->
            mySQLStorage.saveMarketDataBatch(validDataList), taskExecutor);

        CompletableFuture<Void> influxFuture = (influxDBStorage == null) ? CompletableFuture.completedFuture(null) :
            CompletableFuture.runAsync(() -> influxDBStorage.saveMarketDataBatch(validDataList), taskExecutor);

        return CompletableFuture.allOf(mysqlFuture, influxFuture)
            .thenAcceptAsync(v -> {
                validDataList.forEach(data -> {
                    cacheService.cacheTradeData(data.getSymbol(), data);
                    if (data.getPrice() != null) {
                        cacheService.cacheLatestPrice(data.getSymbol(), data.getPrice());
                    }
                });
                marketDataServiceCount.addAndGet(validDataList.size());
                log.info("市场数据批量双写存储与缓存更新成功: 数量={}", validDataList.size());
            }, taskExecutor)
            .exceptionally(ex -> {
                log.error("异步批量保存市场数据失败，可能导致数据不一致: 数量={}", validDataList.size(), ex);
                return null;
            });
    }

    @Override
    public Optional<TradeData> getLatestTradeData(String symbol) {
        try {
            // 先尝试从缓存获取
            Optional<TradeData> cachedData = cacheService.getTradeData(symbol, TradeData.class);
            if (cachedData.isPresent()) {
                cacheHitCount.incrementAndGet();
                return cachedData;
            }

            // 缓存未命中，从数据库查询
            cacheMissCount.incrementAndGet();
            TradeData latestData = marketDataMapper.findLatestBySymbolAndDataType(symbol, "trade");

            if (latestData != null) {
                // 更新缓存
                cacheService.cacheTradeData(symbol, latestData);
                return Optional.of(latestData);
            }

            return Optional.empty();

        } catch (Exception e) {
            log.error("查询最新交易数据失败: symbol={}", symbol, e);
            throw new RuntimeException("查询最新交易数据失败", e);
        }
    }


    // ==================== 数据质量统计服务实现 ====================

    @Override
    public CompletableFuture<Void> saveDataQualityStatsAsync(DataQualityStats stats) {
        return CompletableFuture.runAsync(() -> {
            try {
                if (stats == null) {
                    log.warn("数据质量统计为空，跳过保存");
                    return;
                }

                // 设置创建时间
                if (stats.getCreatedAt() == null) {
                    stats.setCreatedAt(Instant.now());
                }
                stats.setUpdatedAt(Instant.now());

                // 使用UPSERT操作保存到MySQL，避免重复键错误
                int result = dataQualityStatsMapper.upsert(stats);
                if (result > 0) {
                    log.debug("数据质量统计保存成功: symbol={}, dataType={}, timestamp={}",
                        stats.getSymbol(), stats.getDataType(), stats.getTimestamp());
                } else {
                    log.warn("数据质量统计保存失败，影响行数为0: {}", stats);
                }

            } catch (Exception e) {
                log.error("保存数据质量统计失败: {}", stats, e);
                throw new RuntimeException("保存数据质量统计失败", e);
            }
        }, taskExecutor);
    }

    @Override
    public DataQualityStats getDataQualityStats(String symbol, String dataType, Instant startTime, Instant endTime) {
        try {
            List<DataQualityStats> statsList = dataQualityStatsMapper.findBySymbolAndDataTypeAndTimeRange(
                symbol, dataType, startTime, endTime, 1);

            DataQualityStats stats = statsList.isEmpty() ? null : statsList.get(0);

            log.debug("查询数据质量统计: symbol={}, dataType={}, startTime={}, endTime={}",
                symbol, dataType, startTime, endTime);

            return stats;

        } catch (Exception e) {
            log.error("查询数据质量统计失败: symbol={}, dataType={}, startTime={}, endTime={}",
                symbol, dataType, startTime, endTime, e);
            throw new RuntimeException("查询数据质量统计失败", e);
        }
    }

    @Override
    public CompletableFuture<List<DepthData>> getHistoricalDepthData(String symbol, Integer levels, int days) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("获取历史深度数据: symbol={}, levels={}, days={}", symbol, levels, days);

                // 计算开始时间
                Instant endTime = Instant.now();
                Instant startTime = endTime.minusSeconds(days * 24 * 3600L);

                // 从数据库查询历史数据
                List<DepthData> historicalData = getDepthDataByTimeRange(symbol, levels, startTime, endTime);

                log.info("获取历史深度数据完成: symbol={}, levels={}, days={}, 数量={}",
                    symbol, levels, days, historicalData.size());

                return historicalData;

            } catch (Exception e) {
                log.error("获取历史深度数据失败: symbol={}, levels={}, days={}", symbol, levels, days, e);
                throw new RuntimeException("获取历史深度数据失败", e);
            }
        }, taskExecutor);
    }

    @Override
    public CompletableFuture<List<TradeData>> getHistoricalMarketData(String symbol, String dataType, int days) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("获取历史市场数据: symbol={}, dataType={}, days={}", symbol, dataType, days);

                // 计算开始时间
                Instant endTime = Instant.now();
                Instant startTime = endTime.minusSeconds(days * 24 * 3600L);

                // 从数据库查询历史数据
                List<TradeData> historicalData = marketDataMapper.findBySymbolAndDataTypeAndTimeRange(symbol, dataType, startTime, endTime, 10000);

                log.info("获取历史市场数据完成: symbol={}, dataType={}, days={}, 数量={}",
                    symbol, dataType, days, historicalData.size());

                return historicalData;

            } catch (Exception e) {
                log.error("获取历史市场数据失败: symbol={}, dataType={}, days={}", symbol, dataType, days, e);
                throw new RuntimeException("获取历史市场数据失败", e);
            }
        }, taskExecutor);
    }

    // ==================== 历史数据收集服务实现 ====================

    @Override
    public CompletableFuture<Void> collectHistoricalKlineData(String symbol, String interval, int days) {
        return CompletableFuture.runAsync(() -> {
            try {
                log.info("开始收集历史K线数据: symbol={}, interval={}, days={}", symbol, interval, days);

                // 计算时间范围
                Instant endTime = Instant.now();
                Instant startTime = endTime.minusSeconds(days * 24 * 60 * 60L);

                // 分批收集数据，每次最多1000条
                int batchSize = 1000;
                long totalCollected = 0;

                Instant currentStart = startTime;
                while (currentStart.isBefore(endTime)) {
                    try {
                        // 计算当前批次的结束时间
                        Instant currentEnd = currentStart.plusSeconds(batchSize * getIntervalSeconds(interval));
                        if (currentEnd.isAfter(endTime)) {
                            currentEnd = endTime;
                        }

                        // 调用API获取历史数据
                        ApiResponse<List<List<Object>>> response = marketDataApi.getKlines(
                            symbol, interval, currentStart.toEpochMilli(), currentEnd.toEpochMilli(), batchSize);

                        if (response.isSuccess() && response.getData() != null) {
                            List<List<Object>> klineDataList = response.getData();

                            // 转换并保存数据
                            List<KlineData> convertedDataList = klineDataList.stream()
                                .map(klineArray -> convertToKlineData(symbol, interval, klineArray))
                                .collect(Collectors.toList());

                            // 批量保存
                            batchSaveKlineDataAsync(convertedDataList).join();
                            totalCollected += convertedDataList.size();

                            log.debug("历史K线数据批次收集完成: symbol={}, interval={}, 当前批次={}, 总收集数={}",
                                symbol, interval, convertedDataList.size(), totalCollected);
                        }

                        // 更新下一批次的开始时间
                        currentStart = currentEnd;

                        // 避免API限流，添加延迟 - 使用虚拟线程友好的异步延迟
                        try {
                            AsyncDelayUtils.delay(100).join();
                        } catch (Exception e) {
                            log.info("API限流延迟被中断，停止收集: symbol={}, interval={}", symbol, interval);
                            Thread.currentThread().interrupt();
                            break;
                        }

                    } catch (Exception e) {
                        log.error("收集历史K线数据批次失败: symbol={}, interval={}, currentStart={}",
                            symbol, interval, currentStart, e);
                        // 继续下一批次
                        currentStart = currentStart.plusSeconds(batchSize * getIntervalSeconds(interval));
                    }
                }

                log.info("历史K线数据收集完成: symbol={}, interval={}, days={}, 总收集数={}",
                    symbol, interval, days, totalCollected);

            } catch (Exception e) {
                log.error("收集历史K线数据失败: symbol={}, interval={}, days={}", symbol, interval, days, e);
                throw new RuntimeException("收集历史K线数据失败", e);
            }
        }, taskExecutor);
    }

    @Override
    public CompletableFuture<Void> collectHistoricalDepthData(String symbol, Integer levels, int days) {
        return CompletableFuture.runAsync(() -> {
            try {
                log.info("开始收集历史深度数据: symbol={}, levels={}, days={}", symbol, levels, days);

                // 注意：币安API通常不提供历史深度数据，这里主要是框架实现
                // 实际使用时可能需要从其他数据源获取或使用实时数据
                log.warn("币安API不支持历史深度数据，跳过收集: symbol={}, levels={}", symbol, levels);

            } catch (Exception e) {
                log.error("收集历史深度数据失败: symbol={}, levels={}, days={}", symbol, levels, days, e);
                throw new RuntimeException("收集历史深度数据失败", e);
            }
        }, taskExecutor);
    }

    @Override
    public CompletableFuture<Void> collectHistoricalMarketData(String symbol, String dataType, int days) {
        return CompletableFuture.runAsync(() -> {
            try {
                log.info("开始收集历史市场数据: symbol={}, dataType={}, days={}", symbol, dataType, days);

                // 注意：币安API通常不提供历史市场数据，这里主要是框架实现
                // 实际使用时可能需要从其他数据源获取或使用实时数据
                log.warn("币安API不支持历史市场数据，跳过收集: symbol={}, dataType={}", symbol, dataType);

            } catch (Exception e) {
                log.error("收集历史市场数据失败: symbol={}, dataType={}, days={}", symbol, dataType, days, e);
                throw new RuntimeException("收集历史市场数据失败", e);
            }
        }, taskExecutor);
    }

    @Override
    public CompletableFuture<Void> preloadCacheData(List<String> symbols) {
        return CompletableFuture.runAsync(() -> {
            try {
                log.info("开始预热缓存数据: symbols={}", symbols);

                for (String symbol : symbols) {
                    try {
                        // 预热K线数据缓存（多个时间间隔）
                        List<String> intervals = List.of("1m", "5m", "15m", "1h", "4h", "1d");
                        for (String interval : intervals) {
                            getLatestKlineData(symbol, interval);
                        }

                        // 预热深度数据缓存（多个档位）
                        List<Integer> levels = List.of(5, 10, 20);
                        for (Integer level : levels) {
                            getLatestDepthData(symbol, level);
                        }

                        getLatestTradeData(symbol);


                        log.debug("缓存预热完成: symbol={}", symbol);

                    } catch (Exception e) {
                        log.error("缓存预热失败: symbol={}", symbol, e);
                        // 继续下一个交易对
                    }
                }

                log.info("缓存数据预热完成: symbols={}", symbols);

            } catch (Exception e) {
                log.error("预热缓存数据失败: symbols={}", symbols, e);
                throw new RuntimeException("预热缓存数据失败", e);
            }
        }, taskExecutor);
    }

    // ==================== 缓存管理服务实现 ====================

    @Override
    public void clearCache(String symbol, String dataType) {
        try {
            // 清除指定交易对的缓存（目前按交易对清除）
            cacheService.clearSymbolCache(symbol);
            log.info("清除缓存成功: symbol={}, dataType={}", symbol, dataType);

        } catch (Exception e) {
            log.error("清除缓存失败: symbol={}, dataType={}", symbol, dataType, e);
            throw new RuntimeException("清除缓存失败", e);
        }
    }

    @Override
    public void clearAllCache() {
        try {
            // 清除所有缓存
            cacheService.clearAllCache();
            log.info("清除所有缓存成功");

        } catch (Exception e) {
            log.error("清除所有缓存失败", e);
            throw new RuntimeException("清除所有缓存失败", e);
        }
    }

    @Override
    public Object getCacheStats() {
        try {
            // 获取缓存统计信息
            return cacheService.getCacheStats();

        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            throw new RuntimeException("获取缓存统计信息失败", e);
        }
    }

    // ==================== 数据验证服务实现 ====================

    @Override
    public CompletableFuture<Boolean> validateDataIntegrity(String symbol, Instant startTime, Instant endTime) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                log.info("开始验证数据完整性: symbol={}, startTime={}, endTime={}", symbol, startTime, endTime);

                boolean isValid = true;

                // 验证K线数据完整性
                List<String> intervals = List.of("1m", "5m", "15m", "1h", "4h", "1d");
                for (String interval : intervals) {
                    List<KlineData> klineDataList = getKlineDataByTimeRange(symbol, interval, startTime, endTime);
                    if (klineDataList.isEmpty()) {
                        log.warn("K线数据缺失: symbol={}, interval={}, startTime={}, endTime={}",
                            symbol, interval, startTime, endTime);
                        isValid = false;
                    }
                }

                // 验证深度数据完整性
                List<Integer> levels = List.of(5, 10, 20);
                for (Integer level : levels) {
                    List<DepthData> depthDataList = getDepthDataByTimeRange(symbol, level, startTime, endTime);
                    if (depthDataList.isEmpty()) {
                        log.warn("深度数据缺失: symbol={}, levels={}, startTime={}, endTime={}",
                            symbol, level, startTime, endTime);
                        isValid = false;
                    }
                }

                // 验证市场数据完整性
                List<String> dataTypes = List.of("trade", "stats");
                for (String dataType : dataTypes) {
                    List<TradeData> marketDataList = marketDataMapper.findBySymbolAndDataTypeAndTimeRange(symbol, dataType, startTime, endTime, 1000);
                    if (marketDataList.isEmpty()) {
                        log.warn("市场数据缺失: symbol={}, dataType={}, startTime={}, endTime={}",
                            symbol, dataType, startTime, endTime);
                        isValid = false;
                    }
                }

                log.info("数据完整性验证完成: symbol={}, startTime={}, endTime={}, 结果={}",
                    symbol, startTime, endTime, isValid ? "通过" : "失败");

                return isValid;

            } catch (Exception e) {
                log.error("验证数据完整性失败: symbol={}, startTime={}, endTime={}", symbol, startTime, endTime, e);
                return false;
            }
        }, taskExecutor);
    }

    @Override
    public Object getDataStatistics(String symbol) {
        try {
            // 构建数据统计信息
            return Map.of(
                "symbol", symbol,
                "klineServiceCount", klineServiceCount.get(),
                "depthServiceCount", depthServiceCount.get(),
                "marketDataServiceCount", marketDataServiceCount.get(),
                "cacheHitCount", cacheHitCount.get(),
                "cacheMissCount", cacheMissCount.get(),
                "cacheHitRate", calculateCacheHitRate(),
                "timestamp", Instant.now()
            );

        } catch (Exception e) {
            log.error("获取数据统计信息失败: symbol={}", symbol, e);
            throw new RuntimeException("获取数据统计信息失败", e);
        }
    }

    @Override
    public Map<String, Object> getDataStatistics() {
        try {
            Map<String, Object> stats = new HashMap<>();

            // 基础统计
            stats.put("klineServiceCount", klineServiceCount.get());
            stats.put("depthServiceCount", depthServiceCount.get());
            stats.put("marketDataServiceCount", marketDataServiceCount.get());
            stats.put("cacheHitCount", cacheHitCount.get());
            stats.put("cacheMissCount", cacheMissCount.get());
            stats.put("cacheHitRate", calculateCacheHitRate());
            stats.put("timestamp", Instant.now());

            // 数据库统计
            try {
                long totalKlineCount = klineDataMapper.countBySymbolAndInterval(null, null);
                long totalMarketDataCount = marketDataMapper.countBySymbolAndDataType(null, null);
                long totalDepthDataCount = depthDataMapper.countBySymbolAndLevels(null, null);

                stats.put("totalKlineData", totalKlineCount);
                stats.put("totalMarketData", totalMarketDataCount);
                stats.put("totalDepthData", totalDepthDataCount);
                stats.put("totalRecords", totalKlineCount + totalMarketDataCount + totalDepthDataCount);

                // 支持的交易对
                List<String> symbols = klineDataMapper.findAllSymbols();
                stats.put("supportedSymbols", symbols);
                stats.put("symbolCount", symbols.size());

            } catch (Exception e) {
                log.warn("获取数据库统计信息失败", e);
                stats.put("databaseStatsError", e.getMessage());
            }

            return stats;

        } catch (Exception e) {
            log.error("获取数据统计信息失败", e);
            throw new RuntimeException("获取数据统计信息失败", e);
        }
    }

    @Override
    public long getTotalRecordsCount() {
        try {
            long totalKlineCount = klineDataMapper.countBySymbolAndInterval(null, null);
            long totalMarketDataCount = marketDataMapper.countBySymbolAndDataType(null, null);
            long totalDepthDataCount = depthDataMapper.countBySymbolAndLevels(null, null);

            return totalKlineCount + totalMarketDataCount + totalDepthDataCount;

        } catch (Exception e) {
            log.error("获取总记录数失败", e);
            return 0;
        }
    }

    @Override
    public Object getCacheStatistics() {
        try {
            // 构建缓存统计信息
            return Map.of(
                "cacheHitCount", cacheHitCount.get(),
                "cacheMissCount", cacheMissCount.get(),
                "cacheHitRate", calculateCacheHitRate(),
                "totalRequests", cacheHitCount.get() + cacheMissCount.get(),
                "cacheStatus", cacheService.getCacheStats(),
                "timestamp", Instant.now()
            );

        } catch (Exception e) {
            log.error("获取缓存统计信息失败", e);
            throw new RuntimeException("获取缓存统计信息失败", e);
        }
    }

    @Override
    public Object getHealthStatus() {
        try {
            // 构建系统健康状态信息
            boolean isHealthy = true;
            StringBuilder healthDetails = new StringBuilder();

            // 检查数据库连接
            try {
                klineDataMapper.findBySymbolAndInterval("BTCUSDT", "1m", 1);
                marketDataMapper.selectCount(null);
                depthDataMapper.selectCount(null);
                dataQualityStatsMapper.selectCount(null);
                healthDetails.append("MySQL连接正常; ");
            } catch (Exception e) {
                isHealthy = false;
                healthDetails.append("MySQL连接异常: ").append(e.getMessage()).append("; ");
            }

            // 检查缓存服务
            try {
                cacheService.getCacheStats();
                healthDetails.append("缓存服务正常; ");
            } catch (Exception e) {
                isHealthy = false;
                healthDetails.append("缓存服务异常: ").append(e.getMessage()).append("; ");
            }

            // 检查InfluxDB连接
            try {
                if (influxDBStorage != null && influxDBStorage.isHealthy()) {
                    healthDetails.append("InfluxDB连接正常; ");
                } else {
                    healthDetails.append("InfluxDB未连接 (已禁用或不健康); ");
                }
            } catch (Exception e) {
                isHealthy = false;
                healthDetails.append("InfluxDB连接异常: ").append(e.getMessage()).append("; ");
            }

            return Map.of(
                "status", isHealthy ? "HEALTHY" : "UNHEALTHY",
                "details", healthDetails.toString(),
                "timestamp", Instant.now(),
                "uptime", System.currentTimeMillis(),
                "services", Map.of(
                    "mysql", "ACTIVE",
                    "redis", "ACTIVE",
                    "influxdb", "ACTIVE"
                )
            );

        } catch (Exception e) {
            log.error("获取系统健康状态失败", e);
            return Map.of(
                "status", "ERROR",
                "details", "健康检查失败: " + e.getMessage(),
                "timestamp", Instant.now()
            );
        }
    }

    @Override
    public CompletableFuture<Void> repairMissingData(String symbol, String dataType,
                                                    Instant startTime, Instant endTime) {
        return CompletableFuture.runAsync(() -> {
            try {
                log.info("开始修复缺失数据: symbol={}, dataType={}, startTime={}, endTime={}",
                    symbol, dataType, startTime, endTime);

                switch (dataType.toLowerCase()) {
                    case "kline":
                        repairMissingKlineData(symbol, startTime, endTime);
                        break;
                    case "depth":
                        repairMissingDepthData(symbol, startTime, endTime);
                        break;
                    case "trade":
                    case "stats":
                        repairMissingMarketData(symbol, dataType, startTime, endTime);
                        break;
                    default:
                        log.warn("不支持的数据类型修复: {}", dataType);
                        return;
                }

                log.info("修复缺失数据完成: symbol={}, dataType={}, startTime={}, endTime={}",
                    symbol, dataType, startTime, endTime);

            } catch (Exception e) {
                log.error("修复缺失数据失败: symbol={}, dataType={}, startTime={}, endTime={}",
                    symbol, dataType, startTime, endTime, e);
                throw new RuntimeException("修复缺失数据失败", e);
            }
        }, taskExecutor);
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 根据时间间隔获取秒数
     */
    private long getIntervalSeconds(String interval) {
        switch (interval) {
            case "1m": return 60;
            case "5m": return 300;
            case "15m": return 900;
            case "1h": return 3600;
            case "4h": return 14400;
            case "1d": return 86400;
            default: return 60; // 默认1分钟
        }
    }

    /**
     * 将API返回的K线数组转换为KlineData对象
     */
    private KlineData convertToKlineData(String symbol, String interval, List<Object> klineArray) {
        try {
            KlineData klineData = new KlineData();
            klineData.setSymbol(symbol);
            klineData.setInterval(interval);
            klineData.setOpenTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(klineArray.get(0).toString())), ZoneOffset.UTC));
            klineData.setOpenPrice(new java.math.BigDecimal(klineArray.get(1).toString()));
            klineData.setHighPrice(new java.math.BigDecimal(klineArray.get(2).toString()));
            klineData.setLowPrice(new java.math.BigDecimal(klineArray.get(3).toString()));
            klineData.setClosePrice(new java.math.BigDecimal(klineArray.get(4).toString()));
            klineData.setVolume(new java.math.BigDecimal(klineArray.get(5).toString()));
            klineData.setCloseTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(klineArray.get(6).toString())), ZoneOffset.UTC));
            klineData.setQuoteVolume(new java.math.BigDecimal(klineArray.get(7).toString()));
            klineData.setTradeCount(Long.parseLong(klineArray.get(8).toString()));
            klineData.setTakerBuyVolume(new java.math.BigDecimal(klineArray.get(9).toString()));
            klineData.setTakerBuyQuoteVolume(new java.math.BigDecimal(klineArray.get(10).toString()));
            klineData.setCreatedAt(LocalDateTime.now());
            klineData.setUpdatedAt(LocalDateTime.now());

            return klineData;

        } catch (Exception e) {
            log.error("转换K线数据失败: symbol={}, interval={}, klineArray={}", symbol, interval, klineArray, e);
            throw new RuntimeException("转换K线数据失败", e);
        }
    }

    /**
     * 计算缓存命中率
     */
    private double calculateCacheHitRate() {
        long totalRequests = cacheHitCount.get() + cacheMissCount.get();
        if (totalRequests == 0) {
            return 0.0;
        }
        return (double) cacheHitCount.get() / totalRequests * 100.0;
    }

    /**
     * 修复缺失的K线数据
     */
    private void repairMissingKlineData(String symbol, Instant startTime, Instant endTime) {
        try {
            List<String> intervals = List.of("1m", "5m", "15m", "1h", "4h", "1d");

            for (String interval : intervals) {
                log.info("修复K线数据: symbol={}, interval={}, startTime={}, endTime={}",
                    symbol, interval, startTime, endTime);

                // 从API获取历史数据
                CompletableFuture<List<KlineData>> future = getHistoricalKlineData(symbol, interval, 30);
                List<KlineData> historicalData = future.get();

                // 过滤时间范围内的数据
                List<KlineData> filteredData = historicalData.stream()
                    .filter(data -> !data.getOpenTime().toInstant(ZoneOffset.UTC).isBefore(startTime) &&
                                   !data.getOpenTime().toInstant(ZoneOffset.UTC).isAfter(endTime))
                    .collect(Collectors.toList());

                // 批量保存
                if (!filteredData.isEmpty()) {
                    batchSaveKlineDataAsync(filteredData).get();
                    log.info("修复K线数据完成: symbol={}, interval={}, 数量={}",
                        symbol, interval, filteredData.size());
                }
            }

        } catch (Exception e) {
            log.error("修复K线数据失败: symbol={}, startTime={}, endTime={}", symbol, startTime, endTime, e);
            throw new RuntimeException("修复K线数据失败", e);
        }
    }

    /**
     * 修复缺失的深度数据
     */
    private void repairMissingDepthData(String symbol, Instant startTime, Instant endTime) {
        try {
            List<Integer> levels = List.of(5, 10, 20);

            for (Integer level : levels) {
                log.info("修复深度数据: symbol={}, levels={}, startTime={}, endTime={}",
                    symbol, level, startTime, endTime);

                // 从API获取历史数据
                CompletableFuture<List<DepthData>> future = getHistoricalDepthData(symbol, level, 30);
                List<DepthData> historicalData = future.get();

                // 过滤时间范围内的数据
                List<DepthData> filteredData = historicalData.stream()
                    .filter(data -> !data.getTimestamp().toInstant(ZoneOffset.UTC).isBefore(startTime) &&
                                   !data.getTimestamp().toInstant(ZoneOffset.UTC).isAfter(endTime))
                    .collect(Collectors.toList());

                // 批量保存
                if (!filteredData.isEmpty()) {
                    batchSaveDepthDataAsync(filteredData).get();
                    log.info("修复深度数据完成: symbol={}, levels={}, 数量={}",
                        symbol, level, filteredData.size());
                }
            }

        } catch (Exception e) {
            log.error("修复深度数据失败: symbol={}, startTime={}, endTime={}", symbol, startTime, endTime, e);
            throw new RuntimeException("修复深度数据失败", e);
        }
    }

    /**
     * 修复缺失的市场数据
     */
    private void repairMissingMarketData(String symbol, String dataType, Instant startTime, Instant endTime) {
        try {
            log.info("修复市场数据: symbol={}, dataType={}, startTime={}, endTime={}",
                symbol, dataType, startTime, endTime);

            // 从API获取历史数据
            CompletableFuture<List<TradeData>> future = getHistoricalMarketData(symbol, dataType, 30);
            List<TradeData> historicalData = future.get();

            // 过滤时间范围内的数据
            List<TradeData> filteredData = historicalData.stream()
                .filter(data -> !data.getTradeTime().toInstant(ZoneOffset.UTC).isBefore(startTime) &&
                               !data.getTradeTime().toInstant(ZoneOffset.UTC).isAfter(endTime))
                .collect(Collectors.toList());

            // 批量保存
            if (!filteredData.isEmpty()) {
                batchSaveMarketDataAsync(filteredData).get();
                log.info("修复市场数据完成: symbol={}, dataType={}, 数量={}",
                    symbol, dataType, filteredData.size());
            }

        } catch (Exception e) {
            log.error("修复市场数据失败: symbol={}, dataType={}, startTime={}, endTime={}",
                symbol, dataType, startTime, endTime, e);
            throw new RuntimeException("修复市场数据失败", e);
        }
    }

    @Override
    public CompletableFuture<Void> saveTradeDataAsync(TradeData tradeData) {
        return saveMarketDataAsync(tradeData);
    }
}