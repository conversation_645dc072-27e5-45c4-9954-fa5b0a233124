package com.trading.market.service;

import com.trading.common.utils.AsyncDelayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Redis连接服务
 * 提供Redis连接状态管理和恢复功能
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
public class RedisConnectionService {
    
    private static final Logger log = LoggerFactory.getLogger(RedisConnectionService.class);
    
    @Autowired
    private RedisTemplate<String, String> redisTemplate;
    
    @Autowired
    private RedisConnectionFactory redisConnectionFactory;
    
    private final AtomicBoolean isConnectionHealthy = new AtomicBoolean(true);
    private final AtomicLong lastSuccessfulOperation = new AtomicLong(System.currentTimeMillis());
    private final AtomicLong lastConnectionCheck = new AtomicLong(0);
    private final AtomicLong consecutiveFailures = new AtomicLong(0);

    // 连接检查缓存时间（毫秒）- 进一步减少检查频率，避免连接泄漏
    private static final long CONNECTION_CHECK_CACHE_MS = 30000L; // 30秒缓存，大幅减少检查频率
    private static final long MAX_CONSECUTIVE_FAILURES = 3; // 减少最大连续失败次数，快速发现问题
    private static final long FAST_FAIL_DURATION_MS = 60000L; // 增加快速失败持续时间（60秒）
    
    /**
     * 检查Redis连接是否可用
     */
    public boolean isConnectionAvailable() {
        long currentTime = System.currentTimeMillis();

        // 优化快速失败检查：更智能的恢复策略
        if (consecutiveFailures.get() >= MAX_CONSECUTIVE_FAILURES) {
            long lastFailTime = lastConnectionCheck.get();
            if (currentTime - lastFailTime < FAST_FAIL_DURATION_MS) {
                // 每10秒尝试一次恢复检查，减少频繁检查
                if ((currentTime - lastFailTime) % 10000 < 1000) {
                    log.debug("Redis连接快速失败期内，尝试恢复检查，失败次数: {}", consecutiveFailures.get());
                } else {
                    log.debug("Redis连接处于快速失败状态，跳过连接检查，剩余时间: {}ms",
                        FAST_FAIL_DURATION_MS - (currentTime - lastFailTime));
                    return false;
                }
            } else {
                // 快速失败期结束，重置失败计数器
                log.info("Redis连接快速失败期结束，重置失败计数器");
                consecutiveFailures.set(0);
            }
        }

        // 缓存检查：如果最近检查过且状态健康，直接返回缓存结果
        if (isConnectionHealthy.get() &&
            currentTime - lastConnectionCheck.get() < CONNECTION_CHECK_CACHE_MS) {
            return true;
        }

        // 执行实际连接检查
        lastConnectionCheck.set(currentTime);

        try {
            // 使用更简单可靠的连接检查方式
            if (redisTemplate != null && redisTemplate.getConnectionFactory() != null) {
                // 检查连接工厂状态，避免在STOPPING状态下执行操作
                if (!isConnectionFactoryAvailable()) {
                    log.debug("Redis连接工厂不可用，跳过连接检查");
                    updateConnectionStatus(false);
                    return false;
                }

                // 直接使用RedisTemplate进行简单的ping测试，增加超时控制
                String testResult = redisTemplate.execute((RedisCallback<String>) connection -> {
                    try {
                        // 设置较短的超时时间，避免长时间阻塞
                        return connection.ping();
                    } catch (Exception e) {
                        // 区分不同类型的异常
                        if (e.getMessage() != null && e.getMessage().contains("Address already in use")) {
                            log.warn("Redis端口冲突，可能存在多个Redis实例: {}", e.getMessage());
                        } else if (e.getMessage() != null && e.getMessage().contains("NOAUTH")) {
                            log.warn("Redis认证失败，检查密码配置: {}", e.getMessage());
                        } else {
                            log.debug("Redis ping失败: {}", e.getMessage());
                        }
                        return null;
                    }
                });

                if ("PONG".equals(testResult)) {
                    updateConnectionStatus(true);
                    return true;
                } else {
                    log.debug("Redis ping返回异常结果: {}", testResult);
                    updateConnectionStatus(false);
                    return false;
                }
            } else {
                log.warn("RedisTemplate或连接工厂为null");
                updateConnectionStatus(false);
                return false;
            }

        } catch (Exception e) {
            // 改进异常处理，提供更详细的错误信息
            String errorMsg = e.getMessage() != null ? e.getMessage() : "未知错误";
            if (errorMsg.contains("Address already in use")) {
                log.warn("Redis连接检查失败 - 端口冲突: {}", errorMsg);
            } else if (errorMsg.contains("Connection refused")) {
                log.warn("Redis连接检查失败 - 连接被拒绝: {}", errorMsg);
            } else if (errorMsg.contains("timeout")) {
                log.warn("Redis连接检查失败 - 超时: {}", errorMsg);
            } else {
                log.debug("Redis连接检查失败: {} - {}", e.getClass().getSimpleName(), errorMsg);
            }
            updateConnectionStatus(false);
            return false;
        }
    }
    
    /**
     * 安全执行Redis操作 - 增强版本，支持熔断器状态感知
     */
    public <T> T safeExecute(RedisOperation<T> operation, T defaultValue) {
        try {
            // 首先检查连接工厂状态
            if (!isConnectionFactoryAvailable()) {
                log.debug("Redis连接工厂不可用，返回默认值");
                return defaultValue;
            }

            if (!isConnectionAvailable()) {
                log.warn("Redis连接不可用，返回默认值");
                return defaultValue;
            }

            T result = operation.execute();
            updateConnectionStatus(true);
            return result;

        } catch (Exception e) {
            // 检查是否是熔断器相关异常
            if (isCircuitBreakerException(e)) {
                log.debug("检测到熔断器异常，跳过连接恢复: {}", e.getMessage());
                // 对于熔断器异常，不更新连接状态，避免误判
                return defaultValue;
            }

            log.warn("Redis操作失败: {}", e.getMessage());
            updateConnectionStatus(false);

            // 如果是连接工厂停止错误，尝试恢复
            if (e.getMessage() != null && e.getMessage().contains("STOPPING")) {
                log.info("检测到连接工厂停止，尝试恢复连接");
                attemptConnectionRecovery();
            }

            return defaultValue;
        }
    }
    
    /**
     * 安全执行Redis操作（无返回值）- 增强版本，支持熔断器状态感知
     */
    public void safeExecute(VoidRedisOperation operation) {
        try {
            // 首先检查连接工厂状态
            if (!isConnectionFactoryAvailable()) {
                log.debug("Redis连接工厂不可用，跳过操作");
                return;
            }

            if (!isConnectionAvailable()) {
                log.warn("Redis连接不可用，跳过操作");
                return;
            }

            operation.execute();
            updateConnectionStatus(true);

        } catch (Exception e) {
            // 检查是否是熔断器相关异常
            if (isCircuitBreakerException(e)) {
                log.debug("检测到熔断器异常，跳过连接恢复: {}", e.getMessage());
                // 对于熔断器异常，不更新连接状态，避免误判
                return;
            }

            log.warn("Redis操作失败: {}", e.getMessage());
            updateConnectionStatus(false);

            // 如果是连接工厂停止错误，尝试恢复
            if (e.getMessage() != null && e.getMessage().contains("STOPPING")) {
                log.info("检测到连接工厂停止，尝试恢复连接");
                attemptConnectionRecovery();
            }
        }
    }

    /**
     * 检查是否是熔断器相关异常
     */
    private boolean isCircuitBreakerException(Exception e) {
        if (e == null) return false;

        String message = e.getMessage();
        if (message == null) return false;

        // 检查Resilience4j熔断器异常
        return message.contains("CallNotPermittedException") ||
               message.contains("CircuitBreaker") ||
               message.contains("HALF_OPEN") ||
               message.contains("does not permit further calls") ||
               e.getClass().getSimpleName().contains("CallNotPermittedException");
    }
    
    /**
     * 尝试恢复Redis连接 - 增强版本
     */
    public boolean attemptConnectionRecovery() {
        try {
            long currentFailures = consecutiveFailures.get();
            log.info("开始尝试恢复Redis连接，当前失败次数: {}", currentFailures);

            if (redisConnectionFactory instanceof LettuceConnectionFactory) {
                LettuceConnectionFactory lettuceFactory = (LettuceConnectionFactory) redisConnectionFactory;

                // 使用更保守的指数退避策略，减少系统负载
                long backoffDelay = Math.min(2000 * (1L << Math.min(currentFailures, 3)), 60000); // 最大60秒
                log.debug("等待 {}ms 后开始恢复连接", backoffDelay);
                if (!AsyncDelayUtils.safeSleep(backoffDelay)) {
                    log.debug("连接恢复延迟被中断");
                    return false;
                }

                // 多阶段恢复策略
                boolean recovered = false;

                // 第一阶段：轻量级恢复 - 只验证现有连接
                log.debug("第一阶段：验证现有连接");
                if (testRedisConnection()) {
                    log.info("现有连接验证成功，无需重置");
                    consecutiveFailures.set(0);
                    isConnectionHealthy.set(true);
                    return true;
                }

                // 第二阶段：谨慎重置连接，避免频繁重置
                if (currentFailures >= 3) { // 只有在连续失败3次以上才重置
                    log.debug("第二阶段：重置连接工厂（连续失败{}次）", currentFailures);
                    lettuceFactory.resetConnection();
                    lettuceFactory.afterPropertiesSet();

                    // 等待连接稳定
                    if (!AsyncDelayUtils.safeSleep(1000)) {
                        log.debug("连接稳定等待被中断");
                        return false;
                    }
                } else {
                    log.debug("第二阶段：跳过连接重置（失败次数{}未达到阈值）", currentFailures);
                }

                // 第三阶段：验证恢复结果，减少验证次数避免连接泄漏
                log.debug("第三阶段：验证连接恢复");
                int maxAttempts = 3; // 减少验证次数
                for (int attempt = 1; attempt <= maxAttempts; attempt++) {
                    if (testRedisConnection()) {
                        log.info("Redis连接恢复成功，尝试次数: {}", attempt);
                        consecutiveFailures.set(0);
                        isConnectionHealthy.set(true);
                        lastConnectionCheck.set(System.currentTimeMillis());
                        recovered = true;
                        break;
                    }

                    if (attempt < maxAttempts) {
                        long retryDelay = 2000 * attempt; // 增加递增延迟
                        log.debug("连接验证失败，{}ms后重试 ({}/{})", retryDelay, attempt, maxAttempts);
                        if (!AsyncDelayUtils.safeSleep(retryDelay)) {
                            log.debug("连接验证重试延迟被中断");
                            return false;
                        }
                    }
                }

                if (!recovered) {
                    log.warn("Redis连接恢复失败，经过{}次尝试仍无法连接", maxAttempts);
                    return false;
                }

                return true; // 恢复成功
            } else {
                log.warn("连接工厂不是LettuceConnectionFactory类型，无法执行恢复操作");
                return false;
            }

        } catch (Exception e) {
            log.error("Redis连接恢复过程中发生异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查连接健康状态 - 轻量级检查，不执行实际连接操作
     */
    public boolean isConnectionHealthy() {
        return isConnectionHealthy.get();
    }

    /**
     * 测试Redis连接 - 使用RedisTemplate的execute方法，自动管理连接
     */
    private boolean testRedisConnection() {
        try {
            // 使用RedisTemplate的execute方法，自动管理连接生命周期
            String result = redisTemplate.execute((RedisCallback<String>) connection -> {
                try {
                    return connection.ping();
                } catch (Exception e) {
                    log.debug("Redis ping失败: {}", e.getMessage());
                    return null;
                }
            });

            boolean isConnected = "PONG".equals(result);
            log.debug("Redis连接测试结果: {}", isConnected ? "成功" : "失败");
            return isConnected;

        } catch (Exception e) {
            log.debug("Redis连接测试失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 更新连接状态
     */
    private void updateConnectionStatus(boolean isHealthy) {
        if (isHealthy) {
            lastSuccessfulOperation.set(System.currentTimeMillis());
            long previousFailures = consecutiveFailures.getAndSet(0);

            if (!isConnectionHealthy.get()) {
                log.info("Redis连接状态已恢复，之前连续失败{}次", previousFailures);
                isConnectionHealthy.set(true);
            }
        } else {
            long failures = consecutiveFailures.incrementAndGet();

            // 更宽容的失败处理：只有在连续失败超过阈值时才标记为不健康
            if (isConnectionHealthy.get() && failures >= 8) { // 提高阈值从1到8
                log.warn("Redis连接状态变为不健康，连续失败{}次", failures);
                isConnectionHealthy.set(false);
            } else if (failures % 20 == 0) { // 减少日志频率从每10次到每20次
                log.warn("Redis连接持续不健康，连续失败{}次", failures);
            } else if (failures <= 5) {
                // 对于少量失败，只记录debug级别日志
                log.debug("Redis连接轻微失败，连续失败{}次", failures);
            }
        }
    }
    
    /**
     * 获取连接健康状态
     */
    public boolean isHealthy() {
        return isConnectionHealthy.get();
    }
    
    /**
     * 获取最后成功操作时间
     */
    public long getLastSuccessfulOperationTime() {
        return lastSuccessfulOperation.get();
    }

    /**
     * 获取连接状态信息
     */
    public ConnectionStatus getConnectionStatus() {
        return ConnectionStatus.builder()
                .isHealthy(isConnectionHealthy.get())
                .lastSuccessfulOperation(lastSuccessfulOperation.get())
                .lastConnectionCheck(lastConnectionCheck.get())
                .consecutiveFailures(consecutiveFailures.get())
                .build();
    }

    /**
     * 连接状态信息
     */
    @lombok.Builder
    @lombok.Data
    public static class ConnectionStatus {
        private boolean isHealthy;
        private long lastSuccessfulOperation;
        private long lastConnectionCheck;
        private long consecutiveFailures;
    }

    /**
     * 检查Redis连接工厂是否可用（不执行实际连接操作）
     */
    private boolean isConnectionFactoryAvailable() {
        try {
            if (redisTemplate == null || redisTemplate.getConnectionFactory() == null) {
                return false;
            }

            // 检查LettuceConnectionFactory的状态
            RedisConnectionFactory factory = redisTemplate.getConnectionFactory();
            if (factory instanceof org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory) {
                org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory lettuceFactory =
                    (org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory) factory;

                // 使用反射检查内部状态，避免触发连接操作
                try {
                    // 通过反射获取内部状态字段
                    java.lang.reflect.Field stateField = lettuceFactory.getClass().getDeclaredField("state");
                    stateField.setAccessible(true);
                    Object state = stateField.get(lettuceFactory);

                    // 检查状态是否为STOPPED或STOPPING
                    if (state != null) {
                        String stateStr = state.toString();
                        if ("STOPPED".equals(stateStr) || "STOPPING".equals(stateStr)) {
                            log.debug("Redis连接工厂状态为: {}", stateStr);
                            return false;
                        }
                    }

                    return true;

                } catch (NoSuchFieldException | IllegalAccessException e) {
                    // 如果反射失败，回退到连接测试方式
                    log.debug("无法通过反射检查连接工厂状态，回退到连接测试: {}", e.getMessage());

                    try {
                        // 尝试获取连接来检查状态，如果抛出STOPPING异常则说明正在关闭
                        RedisConnection connection = lettuceFactory.getConnection();
                        connection.close();
                        return true;
                    } catch (IllegalStateException ex) {
                        if (ex.getMessage() != null &&
                            (ex.getMessage().contains("STOPPING") || ex.getMessage().contains("STOPPED"))) {
                            log.debug("Redis连接工厂正在停止或已停止: {}", ex.getMessage());
                            return false;
                        }
                        // 其他异常可能是临时的，返回true继续尝试
                        return true;
                    } catch (Exception ex) {
                        log.debug("Redis连接工厂状态检查异常: {}", ex.getMessage());
                        return false;
                    }
                }
            }

            return true;

        } catch (Exception e) {
            log.debug("Redis连接工厂可用性检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Redis操作接口
     */
    @FunctionalInterface
    public interface RedisOperation<T> {
        T execute() throws Exception;
    }
    
    /**
     * 无返回值Redis操作接口
     */
    @FunctionalInterface
    public interface VoidRedisOperation {
        void execute() throws Exception;
    }
}
