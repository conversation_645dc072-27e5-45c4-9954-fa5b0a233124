package com.trading.market.service;

import com.trading.common.dto.KlineData;
import com.trading.common.dto.DepthData;
import com.trading.common.dto.TradeData;
import com.trading.common.dto.DataQualityStats;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * 市场数据服务接口
 * 提供完整的市场数据访问和管理功能
 * 集成多级缓存（本地+Redis）和双存储（MySQL+InfluxDB）
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public interface MarketDataService {

    // ==================== K线数据服务接口 ====================

    /**
     * 获取K线数据列表
     *
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @param limit 限制数量
     * @return K线数据列表
     */
    List<KlineData> getKlineData(String symbol, String interval, int limit);

    /**
     * 获取最新K线数据
     *
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @return 最新K线数据
     */
    Optional<KlineData> getLatestKlineData(String symbol, String interval);

    /**
     * 获取最早K线数据
     *
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @return 最早K线数据
     */
    Optional<KlineData> getEarliestKlineData(String symbol, String interval);

    /**
     * 异步保存K线数据
     *
     * @param klineData K线数据
     * @return 异步任务
     */
    CompletableFuture<Void> saveKlineDataAsync(KlineData klineData);

    /**
     * 批量保存K线数据
     *
     * @param klineDataList K线数据列表
     * @return 异步任务
     */
    CompletableFuture<Void> batchSaveKlineDataAsync(List<KlineData> klineDataList);

    /**
     * 获取指定时间范围的K线数据
     *
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return K线数据列表
     */
    List<KlineData> getKlineDataByTimeRange(String symbol, String interval, Instant startTime, Instant endTime);

    /**
     * 获取历史K线数据（为Python策略端优化）
     *
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return K线数据列表
     */
    List<KlineData> getHistoricalData(String symbol, String interval, Instant startTime, Instant endTime);

    // ==================== 深度数据服务接口 ====================

    /**
     * 获取深度数据列表
     *
     * @param symbol 交易对符号
     * @param levels 深度档位
     * @param limit 限制数量
     * @return 深度数据列表
     */
    List<DepthData> getDepthData(String symbol, Integer levels, int limit);

    /**
     * 获取最新深度数据
     *
     * @param symbol 交易对符号
     * @param levels 深度档位
     * @return 最新深度数据
     */
    Optional<DepthData> getLatestDepthData(String symbol, Integer levels);

    /**
     * 异步保存深度数据
     *
     * @param depthData 深度数据
     * @return 异步任务
     */
    CompletableFuture<Void> saveDepthDataAsync(DepthData depthData);

    /**
     * 批量保存深度数据
     *
     * @param depthDataList 深度数据列表
     * @return 异步任务
     */
    CompletableFuture<Void> batchSaveDepthDataAsync(List<DepthData> depthDataList);

    /**
     * 获取指定时间范围的深度数据
     *
     * @param symbol 交易对符号
     * @param levels 深度档位
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 深度数据列表
     */
    List<DepthData> getDepthDataByTimeRange(String symbol, Integer levels, Instant startTime, Instant endTime);

    // ==================== 市场数据服务接口 ====================
    
    /**
     * 异步保存交易数据
     *
     * @param tradeData 交易数据
     * @return 异步任务
     */
    CompletableFuture<Void> saveTradeDataAsync(TradeData tradeData);
    
    /**
     * 获取最新交易数据
     *
     * @param symbol 交易对符号
     * @return 最新交易数据
     */
    Optional<TradeData> getLatestTradeData(String symbol);

    /**
     * 异步保存市场数据
     *
     * @param tradeData 市场数据
     * @return 异步任务
     */
    CompletableFuture<Void> saveMarketDataAsync(TradeData tradeData);

    /**
     * 批量保存市场数据
     *
     * @param marketDataList 市场数据列表
     * @return 异步任务
     */
    CompletableFuture<Void> batchSaveMarketDataAsync(List<TradeData> marketDataList);


    // ==================== 数据质量管理接口 ====================

    /**
     * 获取数据质量统计
     *
     * @param symbol 交易对符号
     * @param dataType 数据类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 数据质量统计
     */
    DataQualityStats getDataQualityStats(String symbol, String dataType, Instant startTime, Instant endTime);

    /**
     * 保存数据质量统计
     *
     * @param stats 数据质量统计
     * @return 异步任务
     */
    CompletableFuture<Void> saveDataQualityStatsAsync(DataQualityStats stats);

    // ==================== 缓存管理接口 ====================

    /**
     * 预加载缓存数据
     *
     * @param symbols 交易对符号列表
     * @return 异步任务
     */
    CompletableFuture<Void> preloadCacheData(List<String> symbols);

    /**
     * 清除缓存
     *
     * @param symbol 交易对符号
     * @param dataType 数据类型
     */
    void clearCache(String symbol, String dataType);

    /**
     * 清除所有缓存
     */
    void clearAllCache();

    // ==================== 历史数据管理接口 ====================

    /**
     * 获取历史K线数据
     *
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @param days 天数
     * @return 异步任务
     */
    CompletableFuture<List<KlineData>> getHistoricalKlineData(String symbol, String interval, int days);

    /**
     * 获取历史深度数据
     *
     * @param symbol 交易对符号
     * @param levels 深度档位
     * @param days 天数
     * @return 异步任务
     */
    CompletableFuture<List<DepthData>> getHistoricalDepthData(String symbol, Integer levels, int days);

    /**
     * 获取历史市场数据
     *
     * @param symbol 交易对符号
     * @param dataType 数据类型
     * @param days 天数
     * @return 异步任务
     */
    CompletableFuture<List<TradeData>> getHistoricalMarketData(String symbol, String dataType, int days);

    /**
     * 收集历史K线数据
     *
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @param days 天数
     * @return 异步任务
     */
    CompletableFuture<Void> collectHistoricalKlineData(String symbol, String interval, int days);

    /**
     * 收集历史深度数据
     *
     * @param symbol 交易对符号
     * @param levels 深度档位
     * @param days 天数
     * @return 异步任务
     */
    CompletableFuture<Void> collectHistoricalDepthData(String symbol, Integer levels, int days);

    /**
     * 收集历史市场数据
     *
     * @param symbol 交易对符号
     * @param dataType 数据类型
     * @param days 天数
     * @return 异步任务
     */
    CompletableFuture<Void> collectHistoricalMarketData(String symbol, String dataType, int days);

    // ==================== 统计和监控接口 ====================

    /**
     * 获取数据统计信息
     *
     * @param symbol 交易对符号
     * @return 统计信息
     */
    Object getDataStatistics(String symbol);

    /**
     * 获取数据统计信息（所有交易对）
     *
     * @return 统计信息
     */
    Map<String, Object> getDataStatistics();

    /**
     * 获取总记录数
     *
     * @return 总记录数
     */
    long getTotalRecordsCount();

    /**
     * 获取缓存统计信息
     *
     * @return 缓存统计信息
     */
    Object getCacheStatistics();

    /**
     * 获取缓存状态
     *
     * @return 缓存状态
     */
    Object getCacheStats();

    /**
     * 获取系统健康状态
     *
     * @return 健康状态
     */
    Object getHealthStatus();

    // ==================== 数据验证接口 ====================

    /**
     * 验证数据完整性
     *
     * @param symbol 交易对符号
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 验证结果
     */
    CompletableFuture<Boolean> validateDataIntegrity(String symbol, Instant startTime, Instant endTime);

    /**
     * 修复数据缺失
     *
     * @param symbol 交易对符号
     * @param dataType 数据类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 异步任务
     */
    CompletableFuture<Void> repairMissingData(String symbol, String dataType, Instant startTime, Instant endTime);
}