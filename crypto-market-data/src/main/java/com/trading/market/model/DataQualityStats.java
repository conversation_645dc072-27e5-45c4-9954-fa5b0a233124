package com.trading.market.model;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 数据质量统计实体类
 * 支持InfluxDB时序存储和MySQL关系存储
 * 对应data_quality_stats表
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("data_quality_stats")
@Measurement(name = "data_quality_stats")
public class DataQualityStats {

    @TableId(type = IdType.AUTO)
    private Long id;

    @Column(tag = true)
    @TableField("symbol")
    private String symbol;

    @Column(tag = true)
    @TableField("data_type")
    private String dataType;

    @Column(timestamp = true)
    @TableField("date_hour")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "UTC")
    private LocalDateTime dateHour;

    @Column
    @TableField("total_count")
    private Long totalCount;

    @Column
    @TableField("success_count")
    private Long successCount;

    @Column
    @TableField("error_count")
    private Long errorCount;

    @Column
    @TableField("avg_latency")
    private Double avgLatency;

    @Column
    @TableField("max_latency")
    private Long maxLatency;

    @Column
    @TableField("min_quality_score")
    private Double minQualityScore;

    @Column
    @TableField("avg_quality_score")
    private Double avgQualityScore;

    @Column
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant updatedAt;

    public static DataQualityStats create(String symbol, String dataType, LocalDateTime dateHour) {
        return DataQualityStats.builder()
                .symbol(symbol)
                .dataType(dataType)
                .dateHour(dateHour)
                .totalCount(0L)
                .successCount(0L)
                .errorCount(0L)
                .avgLatency(0.0)
                .maxLatency(0L)
                .minQualityScore(1.0)
                .avgQualityScore(1.0)
                .createdAt(Instant.now())
                .build();
    }

    public Instant getTimestamp() {
        if (dateHour == null) {
            return null;
        }
        return dateHour.atZone(java.time.ZoneOffset.UTC).toInstant();
    }

    public double getSuccessRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        if (successCount == null) {
            return 0.0;
        }
        return (double) successCount / totalCount;
    }

    public double getErrorRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        if (errorCount == null) {
            return 0.0;
        }
        return (double) errorCount / totalCount;
    }

    public void incrementTotal() {
        if (totalCount == null) {
            totalCount = 0L;
        }
        totalCount++;
    }

    public void incrementSuccess() {
        if (successCount == null) {
            successCount = 0L;
        }
        successCount++;
        incrementTotal();
    }

    public void incrementError() {
        if (errorCount == null) {
            errorCount = 0L;
        }
        errorCount++;
        incrementTotal();
    }

    public void updateLatency(long latency) {
        if (avgLatency == null) {
            avgLatency = 0.0;
        }
        if (maxLatency == null) {
            maxLatency = 0L;
        }
        if (latency > maxLatency) {
            maxLatency = latency;
        }
        if (totalCount != null && totalCount > 0) {
            avgLatency = (avgLatency * (totalCount - 1) + latency) / totalCount;
        } else {
            avgLatency = (double) latency;
        }
    }
    
    public void updateQualityScore(double qualityScore) {
        if (minQualityScore == null) {
            minQualityScore = 1.0;
        }
        if (avgQualityScore == null) {
            avgQualityScore = 1.0;
        }
        if (qualityScore < minQualityScore) {
            minQualityScore = qualityScore;
        }
        if (totalCount != null && totalCount > 0) {
            avgQualityScore = (avgQualityScore * (totalCount - 1) + qualityScore) / totalCount;
        } else {
            avgQualityScore = qualityScore;
        }
    }
}
