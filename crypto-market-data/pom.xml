<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.trading</groupId>
        <artifactId>crypto-trading-system</artifactId>
        <version>1.0.0-SNAPSHOT</version>
    </parent>

    <artifactId>crypto-market-data</artifactId>
    <name>Crypto Market Data Module</name>
    <description>市场数据模块，负责实时数据收集、处理和存储</description>

    <properties>
        <!-- 所有版本统一由父POM管理，避免版本漂移 -->
    </properties>

    <dependencies>
        <!-- 内部依赖 -->
        <dependency>
            <groupId>com.trading</groupId>
            <artifactId>crypto-common</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.trading</groupId>
            <artifactId>crypto-sdk</artifactId>
            <version>${project.version}</version>
        </dependency>
        

        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-websocket</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Spring Cloud Context - 提供配置刷新功能 -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
        </dependency>



        <!-- MyBatis Plus for Spring Boot 3.x -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
        </dependency>

        <!-- MySQL Connector -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <scope>runtime</scope>
        </dependency>


        



        <!-- Redis - 使用Spring Boot默认的Lettuce客户端 -->











        <!-- 测试依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.json</groupId>
                    <artifactId>json</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.kafka</groupId>
            <artifactId>spring-kafka-test</artifactId>
            <scope>test</scope>
        </dependency>




        <!-- 嵌入式Redis - 开发模式用 -->
        <!--
        <dependency>
            <groupId>it.ozimov</groupId>
            <artifactId>embedded-redis</artifactId>
            <version>0.7.3</version>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-simple</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        -->

        <dependency>
            <groupId>com.h2database</groupId>
            <artifactId>h2</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- Testcontainers -->
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>testcontainers</artifactId>
            <version>1.19.3</version>
            <scope>test</scope>
        </dependency>
        
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>1.19.3</version>
            <scope>test</scope>
        </dependency>
        
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>kafka</artifactId>
            <version>1.19.3</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mysql</artifactId>
            <version>1.19.3</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>influxdb</artifactId>
            <version>1.19.3</version>
            <scope>test</scope>
        </dependency>

        <!-- Awaitility for asynchronous testing -->
        <dependency>
            <groupId>org.awaitility</groupId>
            <artifactId>awaitility</artifactId>
            <version>4.2.0</version>
            <scope>test</scope>
        </dependency>
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
    </dependencies>

    <build>
        <plugins>


            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.trading.market.MarketDataApplication</mainClass>
                    <!-- JVM内存优化配置 -->
                    <jvmArguments>
                        -Xms2g -Xmx4g
                        -XX:+UseG1GC
                        -XX:MaxGCPauseMillis=200
                        -XX:+UseStringDeduplication
                        -XX:+OptimizeStringConcat
                        -Djava.awt.headless=true
                    </jvmArguments>
                    <arguments>
                        <argument>--server.port=19527</argument>
                    </arguments>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <version>3.1.0</version>
                <configuration>
                    <mainClass>com.trading.market.MarketDataApplication</mainClass>
                </configuration>
            </plugin>
            <!-- Exclude resource-intensive performance tests to prevent VM crashes -->
            <!-- Surefire plugin configuration is now inherited from parent POM -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <!--
                    Fork a new JVM for each test. This is crucial for Testcontainers
                    to ensure a clean environment for each test, preventing resource conflicts
                    and ensuring proper cleanup of Docker containers.
                    -->
                    <forkCount>2C</forkCount>
                    <reuseForks>false</reuseForks>
                   <useSystemClassLoader>true</useSystemClassLoader>
                   <useManifestOnlyJar>true</useManifestOnlyJar>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
