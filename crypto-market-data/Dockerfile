# Stage 1: Build the application
FROM eclipse-temurin:21-jdk AS builder

# Install Maven
RUN apt-get update && apt-get install -y maven

WORKDIR /app

# Copy the pom.xml and download dependencies
COPY pom.xml .
RUN mvn dependency:go-offline -B

# Copy the rest of the source code
COPY src ./src

# Build the application
RUN mvn package -DskipTests

# Stage 2: Create the final image
FROM eclipse-temurin:21-jdk

WORKDIR /app

# Copy the jar file from the builder stage
COPY --from=builder /app/target/crypto-market-data-*.jar /app/app.jar

# Expose the application port
EXPOSE 19527

# Command to run the application
ENTRYPOINT ["java", "-jar", "/app/app.jar"]