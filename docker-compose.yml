version: '3.8'

services:
  # MySQL
  mysql:
    image: mysql:8.0
    container_name: mysql-8.0
    restart: always
    ports:
      - "13306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: binance_quant
      MYSQL_USER: nacos
      MYSQL_PASSWORD: nacos
    volumes:
      - mysql_data:/var/lib/mysql
      - ./mysql/init:/docker-entrypoint-initdb.d
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - binance-quant-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-uroot", "-proot"]
      interval: 5s
      timeout: 5s
      retries: 5

  # InfluxDB
  influxdb:
    image: influxdb:2.7
    container_name: influxdb
    restart: always
    ports:
      - "8086:8086"
    environment:
      - DOCKER_INFLUXDB_INIT_MODE=setup
      - DOCKER_INFLUXDB_INIT_USERNAME=admin
      - DOCKER_INFLUXDB_INIT_PASSWORD=admin123
      - DOCKER_INFLUXDB_INIT_ORG=binance
      - DOCKER_INFLUXDB_INIT_BUCKET=market_data
      - DOCKER_INFLUXDB_INIT_ADMIN_TOKEN=zmJ1sGNooabOZbuEWW3MdwUIeL9btWRXJgX_Y4KgTIxJ3GhCxsWqi25qRQr_4FqrcMMWEibD4LkD397IKG1H0w==
    volumes:
      - influxdb_data:/var/lib/influxdb2
    networks:
      - binance-quant-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8086/ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Redis
  redis:
    image: redis:7.0-alpine
    container_name: redis
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass root
    volumes:
      - redis_data:/data
    networks:
      - binance-quant-network
    healthcheck:
      test: ["CMD", "redis-cli", "-a", "root", "ping"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Zookeeper (required for Kafka)
  zookeeper:
    image: confluentinc/cp-zookeeper:5.5.1
    container_name: zookeeper
    ports:
      - "2181:2181"
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
      ZOO_4LW_COMMANDS_WHITELIST: "*"
    networks:
      - binance-quant-network

  # Kafka
  kafka:
    image: confluentinc/cp-kafka:5.5.1
    container_name: kafka
    depends_on:
      zookeeper:
        condition: service_started
    ports:
      - "9092:9092"
      - "29092:29092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092,PLAINTEXT_HOST://localhost:29092
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_INTER_BROKER_LISTENER_NAME: PLAINTEXT
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: "true"
    networks:
      - binance-quant-network
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 10s
      timeout: 10s
      retries: 5

  # Kafka UI (Optional but useful for development)
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: binance-kafka-ui
    depends_on:
      kafka:
        condition: service_started
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: binance-kafka
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:9092
      KAFKA_CLUSTERS_0_ZOOKEEPER: zookeeper:2181
    networks:
      - binance-quant-network

  gemini-balance:
    image: ghcr.io/snailyp/gemini-balance:latest
    container_name: gemini-balance
    restart: unless-stopped
    depends_on:
      mysql:
        condition: service_healthy
    ports:
      - "8000:8000"
    environment:
      - DATABASE_TYPE=mysql
      - MYSQL_HOST=mysql
      - MYSQL_PORT=3306
      - MYSQL_USER=nacos
      - MYSQL_PASSWORD=nacos
      - MYSQL_DATABASE=binance_quant
    env_file:
      - .env
    networks:
      - binance-quant-network

volumes:
  mysql_data:
  redis_data:
  influxdb_data:
  nacos_data:

networks:
  binance-quant-network:
    driver: bridge 
