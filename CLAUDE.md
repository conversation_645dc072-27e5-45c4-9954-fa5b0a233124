# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Architecture Overview

This is a high-performance cryptocurrency quantitative trading system with a hybrid architecture.

- **Core Backend**: Java 21, Spring Boot 3, <PERSON><PERSON>, MyBatis
- **Machine Learning Strategy**: Python, PyTorch, GPU acceleration
- **Monitoring**: Prometheus, Micrometer

## Module Architecture

The system uses a multi-module design with clear responsibilities for each module:

1.  **`crypto-common`**: The **core foundational module**.
    -   Provides shared utilities for all other Java modules.
    -   Includes standardized DTOs, exception handling, Resilience4j-based service governance (circuit breakers, rate limiters), Micrometer metrics, and concurrency tools (e.g., object pools).
    -   It is a mandatory dependency for all other Java modules.

2.  **`crypto-sdk`**: The **external API wrapper module**.
    -   Handles interaction with external exchange APIs (e.g., Binance), managing request signing, connection management, and data transformation.
    -   Relies on `crypto-common` for resilient and monitored API calls.

3.  **`crypto-market-data`**: The **market data processing module**.
    -   A runnable Spring Boot application responsible for collecting, processing, and storing real-time and historical market data.
    -   Serves as the data source for Python strategy training and backtesting.
    -   Relies on `crypto-common` for a highly available data pipeline and performance monitoring.

4.  **`crypto-ml-strategy`**: The **machine learning strategy module**.
    -   A separate Python project for developing, training, and executing trading strategies based on `PyTorch`.
    -   GPU acceleration is mandatory for large-scale data computation.

5.  **`crypto-trading-engine`**: The **trade execution engine**.
    -   Responsible for executing trading logic, managing orders, and positions.
    -   Relies on `crypto-sdk` for exchange interaction and `crypto-market-data` for real-time market data.

## Common Commands

- **Build the entire project**:
  ```bash
  mvn clean install
  ```

- **Build without running tests**:
  ```bash
  mvn clean install -DskipTests
  ```

- **Run the market data application**:
  ```bash
  mvn spring-boot:run -pl crypto-market-data
  ```

- **Run a specific test class**:
  ```bash
  mvn test -Dtest=com.trading.sdk.client.BinanceClientFactoryTest
  ```
  *(Replace the class name with the test you want to run)*

## Key Development Principles

This project adheres to a strict set of engineering principles. Follow them carefully.

1.  **Understand & Plan First**: Before implementing, always restate the task objectives, clarify scope, and perform a root cause analysis for any issues. Base all solutions on evidence, not assumptions.
2.  **Full Context Analysis**: Before changing code, review its history (`git blame`), dependencies, and related documentation to understand its full context and potential impact.
3.  **DRY (Don't Repeat Yourself)**: Aggressively refactor to eliminate redundant code. Abstract repeated logic into shared functions or classes in `crypto-common`.
4.  **No Hardcoding**: All environment-specific or sensitive configurations (API keys, DB connections, ports) must be externalized using configuration files (e.g., `.yml`, `.properties`) or environment variables.
5.  **Algorithm-First Performance Tuning**: For performance issues, prioritize optimizing algorithms and data structures over surface-level fixes like caching. Use a profiler to identify bottlenecks before attempting a fix.
6.  **Test-Fix-Revalidate Cycle**: When fixing a bug, first write a failing test that reproduces it. After applying the fix, ensure the test passes and that no existing tests have broken (regression).
7.  **High-Fidelity Data for Testing**: While unit tests can use mock data for isolation, integration and E2E tests must use realistic, high-fidelity data (e.g., anonymized production data in a staging environment).
8.  **No Disabling Features as a Fix**: Disabling a problematic feature is a temporary emergency measure, not a solution. Always create a high-priority ticket to address the root cause.
9.  **Mandatory Code Reviews**: All changes must be submitted via Pull Requests (PRs) and are subject to peer review. Ensure commit messages are descriptive and explain the "what" and "why" of the change.

使用mcp对应的日志信息


 @agent-quality-assurance-expert          │

│   @agent-general-purpose @agent-project-coordinator   │
│   @agent-tech-documenter @agent-statusline-setup      │
│   @agent-output-style-setup @agent-code-crafter       │

│   使用agents




对应的中间件查看docker-commpose文件



使用mcp


* **所有输出必须使用简体中文* **
* **请确认你已完全理解并准备好作为AGF运行* **
* **ultrathink* **
* **要查看对应关联文件和收集足够多的信息并且阅读足够多的上下文后再继续不应该盲目地改动，不要满目自信，关联的每个文件都要阅读到* **
* **不要忽略用户的要求，不要糊弄用户* **
* **不要跳过、回避问题、糊弄用户、简化* **
* **不要禁用* **
* **出现问题要解决 * **
* **性能问题优先算法优化* **
* **先测试当前的问题到底出在哪里* **
* **找到具体的错误原因,然后再有针对性地修复* **
* **修复后再测试验证* **
* **要移除冗余，不要出现多个版本的情况 * **
* **对应的文件要放到对应的位置 * **
* **不要硬编码 * **
* **不要跳过测试，出现问题解决他* **
* **可以一个模块一个模块测试，一个模块一个模块集成* **
* **不要跳过,找到问题后禁用的，简化的要恢复* **
* **不要模拟数据要真实数据* **
* **上个对话有改动文件的必须审核，是否正常* **











