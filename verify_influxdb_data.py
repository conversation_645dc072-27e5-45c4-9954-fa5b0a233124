# -*- coding: utf-8 -*-
import asyncio
import os
import pandas as pd
from datetime import datetime, timedelta
from influxdb_client import InfluxDBClient
from influxdb_client.client.exceptions import InfluxDBError
from dotenv import load_dotenv

# 加载 .env 文件中的环境变量
load_dotenv()

# --- 配置 ---
# InfluxDB 连接配置 (从环境变量获取)
INFLUXDB_URL = os.getenv("INFLUXDB_URL", "http://localhost:8086")
INFLUXDB_TOKEN = os.getenv("INFLUXDB_TOKEN", "my-super-secret-token")
INFLUXDB_ORG = os.getenv("INFLUXDB_ORG", "my-org")
INFLUXDB_BUCKET = os.getenv("INFLUXDB_BUCKET", "my-bucket")

# 需要验证的交易对和时间周期
SYMBOLS_TO_VERIFY = ["BTCUSDT", "ETHUSDT", "BNBUSDT", "SOLUSDT", "DOGEUSDT"]
INTERVALS_TO_VERIFY = ["1m", "3m", "5m", "15m", "30m", "1h", "4h", "1d"]

# 验证要求
MIN_DATA_DAYS = 90

def get_interval_seconds(interval: str) -> int:
    """将时间周期字符串转换为秒"""
    unit = interval[-1]
    value = int(interval[:-1])
    if unit == 'm':
        return value * 60
    elif unit == 'h':
        return value * 3600
    elif unit == 'd':
        return value * 86400
    return 0

async def verify_data_completeness():
    """
    连接到 InfluxDB 并验证指定交易对和时间周期的数据完整性。
    """
    report_lines = []
    header = "| {:<10} | {:<8} | {:<25} | {:<25} | {:<10} | {:<12} | {:<10} |".format(
        "Symbol", "Interval", "Oldest Record", "Newest Record", "Data Days", "Count Check", "Pass"
    )
    report_lines.append(header)
    report_lines.append("-" * len(header))

    async with InfluxDBClient(url=INFLUXDB_URL, token=INFLUXDB_TOKEN, org=INFLUXDB_ORG, timeout=30_000) as client:
        query_api = client.query_api()

        for symbol in SYMBOLS_TO_VERIFY:
            for interval in INTERVALS_TO_VERIFY:
                oldest_time, newest_time, count, data_days, count_ok, passed = "N/A", "N/A", 0, 0, "No", "No"
                try:
                    # 查询最早时间
                    first_query = f'''
                    from(bucket: "{INFLUXDB_BUCKET}")
                        |> range(start: 0)
                        |> filter(fn: (r) => r["_measurement"] == "kline_data" and r["symbol"] == "{symbol}" and r["interval"] == "{interval}")
                        |> first()
                        |> keep(columns: ["_time"])
                    '''

                    # 查询最晚时间
                    last_query = f'''
                    from(bucket: "{INFLUXDB_BUCKET}")
                        |> range(start: 0)
                        |> filter(fn: (r) => r["_measurement"] == "kline_data" and r["symbol"] == "{symbol}" and r["interval"] == "{interval}")
                        |> last()
                        |> keep(columns: ["_time"])
                    '''
                    
                    # 查询总数
                    count_query = f'''
                    from(bucket: "{INFLUXDB_BUCKET}")
                        |> range(start: 0)
                        |> filter(fn: (r) => r["_measurement"] == "kline_data" and r["symbol"] == "{symbol}" and r["interval"] == "{interval}")
                        |> count()
                    '''

                    first_df = await query_api.query_data_frame(first_query)
                    last_df = await query_api.query_data_frame(last_query)
                    count_df = await query_api.query_data_frame(count_query)

                    if not first_df.empty and not last_df.empty and not count_df.empty:
                        oldest_time_dt = pd.to_datetime(first_df.iloc['_time'])
                        newest_time_dt = pd.to_datetime(last_df.iloc['_time'])
                        oldest_time = oldest_time_dt.strftime('%Y-%m-%d %H:%M:%S')
                        newest_time = newest_time_dt.strftime('%Y-%m-%d %H:%M:%S')
                        count = count_df.iloc['_value']
                        
                        # 计算数据天数
                        data_days = (newest_time_dt - oldest_time_dt).days
                        
                        # 检查数据量
                        interval_seconds = get_interval_seconds(interval)
                        # 估算90天内的预期数量
                        expected_count = (MIN_DATA_DAYS * 86400) / interval_seconds if interval_seconds > 0 else 0
                        # 允许 10% 的误差
                        count_ok = "Yes" if count >= expected_count * 0.9 else "No"
                        
                        # 验证是否通过
                        passed = "Yes" if data_days >= MIN_DATA_DAYS and count_ok == "Yes" else "No"

                except (InfluxDBError, Exception) as e:
                    passed = "Error"
                    oldest_time = str(e)


                report_lines.append("| {:<10} | {:<8} | {:<25} | {:<25} | {:<10} | {:<12} | {:<10} |".format(
                    symbol, interval, str(oldest_time), str(newest_time), data_days, f"{count} ({count_ok})", passed
                ))

    # 打印报告
    print("\n--- InfluxDB Data Verification Report ---")
    print(f"Verification Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Required Data Days: {MIN_DATA_DAYS}")
    print("-" * len(header))
    for line in report_lines:
        print(line)
    print("-" * len(header))


if __name__ == "__main__":
    asyncio.run(verify_data_completeness())