
import requests
import pandas as pd
from binance.client import Client
from datetime import datetime, timedelta
import time
import json

# --- 配置 ---
# 本地服务API
RECOVERY_API_URL = "http://localhost:19527/api/v1/data-recovery/kline"

# InfluxDB 查询 API
INFLUX_QUERY_URL = "http://localhost:8086/api/v2/query?org=binance"
INFLUX_TOKEN = "zmJ1sGNooabOZbuEWW3MdwUIeL9btWRXJgX_Y4KgTIxJ3GhCxsWqi25qRQr_4FqrcMMWEibD4LkD397IKG1H0w=="

# 币安API (无需密钥即可获取公共数据)
binance_client = Client()

# 需要恢复的交易对和时间间隔
SYMBOLS_TO_RECOVER = ["ETHUSDT", "BNBUSDT", "SOLUSDT", "DOGEUSDT"]
INTERVALS_TO_RECOVER = ["1m", "5m", "15m", "1h", "4h", "1d"]

# --- 函数定义 ---

def get_latest_timestamp_from_influx(symbol, interval):
    """从InfluxDB查询指定交易对和时间间隔的最新时间戳"""
    flux_query = f'''
    from(bucket: "market_data")
      |> range(start: -90d)
      |> filter(fn: (r) => r._measurement == "kline_data" and r.symbol == "{symbol}" and r.interval == "{interval}")
      |> last()
      |> keep(columns: ["_time"])
    '''
    headers = {
        "Authorization": f"Token {INFLUX_TOKEN}",
        "Content-Type": "application/vnd.flux",
        "Accept": "application/csv"
    }
    try:
        response = requests.post(INFLUX_QUERY_URL, headers=headers, data=flux_query, timeout=10)
        response.raise_for_status()
        
        # 解析CSV结果
        lines = response.text.strip().split('\\n')
        if len(lines) > 1 and ',' in lines[-1]:
            latest_time_str = lines[-1].split(',')[3]
            # 移除可能存在的引号和Z
            latest_time_str = latest_time_str.replace('"', '').replace('Z', '')
            # 解析时间并转换为毫秒时间戳
            dt_object = datetime.fromisoformat(latest_time_str)
            return int(dt_object.timestamp() * 1000)
    except Exception as e:
        log_message(f"查询 {symbol}-{interval} 的最新时间戳失败: {e}", "ERROR")
    return None

def fetch_historical_klines(symbol, interval, start_ms, end_ms):
    """从币安获取历史K线数据"""
    log_message(f"正在从币安获取 {symbol}-{interval} 从 {datetime.fromtimestamp(start_ms/1000)} 到 {datetime.fromtimestamp(end_ms/1000)} 的数据...", "INFO")
    try:
        klines = binance_client.get_historical_klines(symbol, interval, start_str=str(start_ms), end_str=str(end_ms))
        return klines
    except Exception as e:
        log_message(f"从币安获取 {symbol}-{interval} 数据失败: {e}", "ERROR")
        return []

def format_kline_for_api(kline, symbol, interval):
    """将币安API返回的K线格式化为我们Java服务所需的JSON格式"""
    return {
        "symbol": symbol,
        "interval": interval,
        "openTime": datetime.fromtimestamp(kline[0] / 1000).isoformat() + "Z",
        "openPrice": float(kline[1]),
        "highPrice": float(kline[2]),
        "lowPrice": float(kline[3]),
        "closePrice": float(kline[4]),
        "volume": float(kline[5]),
        "closeTime": datetime.fromtimestamp(kline[6] / 1000).isoformat() + "Z",
        "quoteAssetVolume": float(kline[7]),
        "numberOfTrades": int(kline[8]),
        "takerBuyBaseAssetVolume": float(kline[9]),
        "takerBuyQuoteAssetVolume": float(kline[10])
    }

def post_data_to_recovery_api(data_batch):
    """将一批数据发送到恢复API"""
    try:
        response = requests.post(RECOVERY_API_URL, json=data_batch, timeout=30)
        response.raise_for_status()
        log_message(f"成功提交 {len(data_batch)} 条记录到恢复API。响应: {response.text}", "SUCCESS")
        return True
    except requests.exceptions.RequestException as e:
        log_message(f"提交数据到恢复API失败: {e}", "ERROR")
        return False

def log_message(message, level="INFO"):
    """简单的日志记录函数"""
    print(f"[{datetime.now().isoformat()}] [{level}] {message}")

# --- 主逻辑 ---
if __name__ == "__main__":
    log_message("--- 开始数据恢复流程 ---", "HEADER")
    
    end_dt = datetime.utcnow()
    
    for symbol in SYMBOLS_TO_RECOVER:
        for interval in INTERVALS_TO_RECOVER:
            log_message(f"--- 正在处理 {symbol} / {interval} ---", "INFO")
            
            latest_ts = get_latest_timestamp_from_influx(symbol, interval)
            
            # 如果找不到数据，就从90天前开始
            start_dt = datetime.fromtimestamp(latest_ts / 1000) if latest_ts else (end_dt - timedelta(days=90))
            
            log_message(f"本地最新数据时间: {start_dt}. 将从该时间点开始恢复。", "INFO")

            # 将时间转换为币安需要的毫秒时间戳
            start_ms = int(start_dt.timestamp() * 1000)
            end_ms = int(end_dt.timestamp() * 1000)

            if start_ms >= end_ms:
                log_message("本地数据已是最新，无需恢复。", "SUCCESS")
                continue

            klines = fetch_historical_klines(symbol, interval, start_ms, end_ms)
            
            if not klines:
                log_message("从币安未获取到新数据。", "INFO")
                continue
                
            log_message(f"从币安获取了 {len(klines)} 条新的K线数据。", "INFO")

            # 格式化并分批提交数据
            batch = []
            batch_size = 500 # 减少批次大小以避免请求体过大
            for kline in klines:
                formatted_kline = format_kline_for_api(kline, symbol, interval)
                batch.append(formatted_kline)
                if len(batch) >= batch_size:
                    post_data_to_recovery_api(batch)
                    batch.clear()
                    time.sleep(1) # 短暂休眠，避免请求过于频繁

            if batch: # 提交最后一批不足大小的数据
                post_data_to_recovery_api(batch)

            log_message(f"--- 完成 {symbol} / {interval} 的处理 ---", "INFO")
            time.sleep(2) # 处理完一个组合后休眠

    log_message("--- 数据恢复流程全部完成 ---", "HEADER")
