# CPU优化配置文件
# 专门为避免CUDA问题和数据不足问题而设计

# 数据配置
data:
  symbols: ["BTCUSDT", "ETHUSDT"]
  timeframes: ["1h", "4h"]
  min_data_points: 50  # 降低最小数据点要求
  use_generated_data: true  # 启用生成的测试数据
  fallback_to_synthetic: true  # 启用合成数据回退
  
  # 数据源配置
  sources:
    java_service:
      enabled: true
      base_url: "http://127.0.0.1:19527"
      timeout: 30
    generated_data:
      enabled: true
      data_dir: "data/generated"

# 模型配置
model:
  device: "cpu"  # 强制使用CPU
  mixed_precision: false  # 禁用混合精度
  feature_dim: 30
  hidden_dim: 64
  num_layers: 2
  dropout: 0.1
  
  # 集成学习配置
  ensemble:
    enabled: true
    num_models: 3  # 减少模型数量
    voting_strategy: "soft"

# 训练配置
training:
  batch_size: 16  # 较小的批次大小
  num_epochs: 10  # 较少的训练轮数
  learning_rate: 0.001
  weight_decay: 0.0001
  
  # 数据加载器配置
  dataloader:
    num_workers: 1  # 单线程避免问题
    pin_memory: false  # 禁用内存固定
    persistent_workers: false
  
  # 优化器配置
  optimizer:
    type: "Adam"
    betas: [0.9, 0.999]
    eps: 1e-8

# 存储配置
storage:
  mysql:
    enabled: false  # 暂时禁用MySQL避免连接问题
  redis:
    enabled: false  # 暂时禁用Redis
  influxdb:
    enabled: false  # 暂时禁用InfluxDB避免连接问题

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_logging: false  # 只输出到控制台

# 性能配置
performance:
  enable_profiling: false
  memory_optimization: true
  cpu_optimization: true
  
  # 禁用GPU相关优化
  gpu_optimization: false
  cuda_optimization: false

# API配置
api:
  deepseek:
    enabled: false  # 禁用外部API调用

# 监控配置
monitoring:
  prometheus:
    enabled: true
    port: 8000
  training_monitor:
    enabled: true
    save_plots: false  # 禁用图表保存避免依赖问题
