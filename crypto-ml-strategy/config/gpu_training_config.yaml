# GPU高效训练配置
# 专门为GPU训练、在线学习和信号生成优化

# 系统配置
system:
  timezone: "UTC"
  debug_mode: true
  threading:
    torch_num_threads: 8
    omp_num_threads: 8

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_logging: false

# 数据配置
data:
  symbols: ["BTCUSDT", "ETHUSDT"]
  timeframes: ["1h", "4h"]
  min_data_points: 50  # 降低要求以使用生成数据
  use_generated_data: true  # 启用生成数据
  fallback_to_synthetic: true
  
  # 数据源配置
  sources:
    java_service:
      enabled: true
      base_url: "http://127.0.0.1:19527"
      timeout: 30
    generated_data:
      enabled: true
      data_dir: "data/generated"
  
  # 数据分割
  split:
    train_ratio: 0.7
    val_ratio: 0.15
    test_ratio: 0.15
  
  # 特征工程
  features:
    technical_indicators: true
    price_features: true
    volume_features: true
    time_features: true
    lag_features: true
    rolling_features: true

# 模型配置
model:
  device: "cuda"  # 使用GPU
  mixed_precision: true  # 启用混合精度训练
  feature_dim: 30
  hidden_dim: 128  # 增加隐藏层维度
  num_layers: 3    # 增加层数
  dropout: 0.2
  
  # Transformer配置
  transformer:
    num_heads: 8
    ff_dim: 256
    num_layers: 4
  
  # 集成学习配置
  ensemble:
    enabled: true
    num_models: 5
    voting_strategy: "soft"
    diversity_loss_weight: 0.1

# 训练配置
training:
  batch_size: 64      # 增加批次大小利用GPU
  num_epochs: 50      # 增加训练轮数
  learning_rate: 0.001
  weight_decay: 0.0001
  gradient_clip_norm: 1.0
  
  # 学习率调度
  scheduler:
    type: "CosineAnnealingLR"
    T_max: 50
    eta_min: 0.00001
  
  # 早停配置
  early_stopping:
    enabled: true
    patience: 10
    min_delta: 0.001
  
  # 数据加载器配置
  dataloader:
    num_workers: 4
    pin_memory: true
    persistent_workers: true
    prefetch_factor: 2
  
  # 优化器配置
  optimizer:
    type: "AdamW"
    betas: [0.9, 0.999]
    eps: 1e-8
    amsgrad: true
  
  # 损失函数配置
  loss:
    type: "MSELoss"
    reduction: "mean"
  
  # 验证配置
  validation:
    frequency: 5  # 每5个epoch验证一次
    metrics: ["mse", "mae", "r2", "sharpe"]

# 在线学习配置
online_learning:
  enabled: true
  update_frequency: 100  # 每100个样本更新一次
  learning_rate: 0.0001  # 较小的学习率
  buffer_size: 1000
  
  # 概念漂移检测
  drift_detection:
    enabled: true
    window_size: 500
    threshold: 0.05
  
  # 模型适应
  adaptation:
    enabled: true
    adaptation_rate: 0.1
    min_samples: 50

# 推理配置
inference:
  batch_size: 32
  confidence_threshold: 0.6
  ensemble_voting: "weighted"
  
  # 信号生成
  signal_generation:
    enabled: true
    buy_threshold: 0.7   # 买入信号阈值
    sell_threshold: 0.3  # 卖出信号阈值
    hold_threshold: 0.1  # 持有信号阈值
    
    # 风险管理
    risk_management:
      max_position_size: 0.1  # 最大仓位10%
      stop_loss: 0.02         # 2%止损
      take_profit: 0.05       # 5%止盈

# 存储配置（暂时禁用避免连接问题）
storage:
  mysql:
    enabled: false
  redis:
    enabled: false
  influxdb:
    enabled: true  # 启用InfluxDB用于指标存储
    url: "${INFLUXDB_URL:http://localhost:8086}"
    token: ${INFLUXDB_TOKEN:zmJ1sGNooabOZbuEWW3MdwUIeL9btWRXJgX_Y4KgTIxJ3GhCxsWqi25qRQr_4FqrcMMWEibD4LkD397IKG1H0w==}
    org: "binance"
    bucket: "market-data"
    batch_size: 1000

# 性能配置
performance:
  enable_profiling: true
  memory_optimization: true
  gpu_optimization: true
  cuda_optimization: true
  
  # GPU内存管理
  gpu_memory:
    max_split_size_mb: 512
    memory_fraction: 0.8
    allow_growth: true
  
  # 批处理优化
  batch_optimization:
    enabled: true
    dynamic_batching: true
    max_batch_size: 128

# API配置
api:
  deepseek:
    enabled: false

# 监控配置
monitoring:
  prometheus:
    enabled: true
    port: 8000
  training_monitor:
    enabled: true
    save_plots: true
    plot_frequency: 10
  
  # 性能监控
  performance_monitor:
    enabled: true
    interval: 5
    metrics: ["gpu_utilization", "memory_usage", "throughput"]

# 风险管理配置
risk_management:
  enabled: true
  max_drawdown: 0.1
  var_confidence: 0.95
  position_sizing: "kelly"
  
  # 组合管理
  portfolio:
    max_positions: 5
    correlation_threshold: 0.8
    rebalance_frequency: "daily"
