data:
  data_requirements:
    adaptive_requirements: true
    fallback_strategy: use_available
    min_days_coverage: 2
    timeframe_specific_requirements:
      15m:
        min_data_points: 50
        preferred_coverage_hours: 12
      1d:
        min_data_points: 20
        preferred_coverage_hours: 168
      1h:
        min_data_points: 40
        preferred_coverage_hours: 24
      1m:
        min_data_points: 100
        preferred_coverage_hours: 2
      3m:
        min_data_points: 80
        preferred_coverage_hours: 4
      4h:
        min_data_points: 30
        preferred_coverage_hours: 48
      5m:
        min_data_points: 60
        preferred_coverage_hours: 6
  fallback_to_synthetic: true
  feature_engineering:
    cross_timeframe_features: true
    enable_multi_timeframe_features: true
    feature_prefix_by_timeframe: true
    technical_indicators:
      bollinger_bands:
        period: 20
        std: 2
      ema_periods:
      - 12
      - 26
      - 50
      macd_config:
        fast: 12
        signal: 9
        slow: 26
      rsi_period: 14
      sma_periods:
      - 5
      - 10
      - 20
      - 50
  min_data_points: 30
  multi_timeframe_config:
    adaptive_weighting: true
    enable_mixed_training: true
    feature_fusion_method: concatenate
    min_data_per_timeframe: 20
    primary_timeframes:
    - 5m
    - 15m
    - 1h
    secondary_timeframes:
    - 1m
    - 3m
    - 4h
    - 1d
    timeframe_weights:
      15m: 0.2
      1d: 0.05
      1h: 0.25
      1m: 0.08
      3m: 0.12
      4h: 0.15
      5m: 0.15
  symbols:
  - BTCUSDT
  - ETHUSDT
  - BNBUSDT
  - SOLUSDT
  - DOGEUSDT
  timeframes: &id001
  - 1m
  - 3m
  - 5m
  - 15m
  - 1h
  - 4h
  - 1d
  use_generated_data: true
model:
  # GPU优化配置
  device: 'auto'  # 自动选择GPU或CPU
  mixed_precision: true  # 启用混合精度训练
  compile_model: true  # 启用模型编译优化

  # 模型架构优化
  feature_dim: 217  # 7个时间框架 × 31个特征
  hidden_dim: 512   # 增大隐藏层提高表达能力
  num_layers: 4     # 增加层数提高学习能力
  dropout: 0.2      # 适度dropout防止过拟合

  multi_timeframe_fusion:
    enable: true
    fusion_method: attention_weighted  # 使用注意力机制

    # 注意力配置优化
    attention_config:
      num_heads: 16      # 增加注意力头数
      dropout: 0.1
      temperature: 0.8   # 降低温度增强注意力集中度
      layer_norm: true   # 启用层归一化

    # 时间框架编码器优化
    timeframe_encoders:
      1m:
        input_dim: 31
        hidden_dim: 128   # 增大隐藏层
        output_dim: 64    # 增大输出维度
        num_layers: 3
      3m:
        input_dim: 31
        hidden_dim: 128
        output_dim: 64
        num_layers: 3
      5m:
        input_dim: 31
        hidden_dim: 128
        output_dim: 64
        num_layers: 3
      15m:
        input_dim: 31
        hidden_dim: 128
        output_dim: 64
        num_layers: 3
      1h:
        input_dim: 31
        hidden_dim: 128
        output_dim: 64
        num_layers: 3
      4h:
        input_dim: 31
        hidden_dim: 128
        output_dim: 64
        num_layers: 3
      1d:
        input_dim: 31
        hidden_dim: 128
        output_dim: 64
        num_layers: 3
online_learning:
  multi_timeframe_streaming:
    data_sync_strategy: latest_available
    enable: true
    feature_update_frequency:
      15m: 900
      1d: 86400
      1h: 3600
      1m: 60
      3m: 180
      4h: 14400
      5m: 300
    timeframe_priorities: *id001
training:
  # GPU训练优化
  device: 'auto'
  mixed_precision: true
  gradient_clipping: 1.0
  accumulate_grad_batches: 4  # 梯度累积提高有效批次大小

  # 训练参数优化
  batch_size: 128        # 增大批次大小充分利用GPU
  learning_rate: 0.001   # 适中的学习率
  weight_decay: 0.0001   # L2正则化
  warmup_steps: 1000     # 学习率预热
  max_epochs: 100        # 增加训练轮数
  patience: 15           # 早停耐心值

  # 优化器配置
  optimizer: 'AdamW'     # 使用AdamW优化器
  scheduler: 'cosine'    # 余弦退火学习率调度

  # 数据加载优化
  num_workers: 8         # 多进程数据加载
  pin_memory: true       # 固定内存加速GPU传输
  prefetch_factor: 4     # 预取因子

  multi_timeframe_training:
    enable: true

    # 批次组成优化
    batch_composition:
      primary_timeframe_ratio: 0.7    # 增加主要时间框架比例
      secondary_timeframe_ratio: 0.3
      random_sampling: true
      stratified_sampling: true       # 分层采样

    # 损失权重优化
    loss_weighting:
      adaptive_weighting: true
      performance_based_adjustment: true
      dynamic_reweighting: true       # 动态重新加权

      # 优化的时间框架权重
      timeframe_weights:
        1m: 0.08
        3m: 0.12
        5m: 0.15
        15m: 0.20
        1h: 0.25    # 最高权重
        4h: 0.15
        1d: 0.05

    # 训练策略优化
    training_strategy:
      curriculum_learning: true       # 课程学习
      progressive_resizing: true      # 渐进式调整
      mixup_alpha: 0.2               # Mixup数据增强
      label_smoothing: 0.1           # 标签平滑

# 在线学习和推理优化配置
online_learning:
  # GPU推理优化
  device: 'auto'
  mixed_precision: true
  model_compilation: true    # 编译模型加速推理
  batch_inference: true      # 批量推理

  # 推理性能优化
  inference_config:
    max_batch_size: 64       # 推理批次大小
    cache_size: 1000         # 特征缓存大小
    prefetch_batches: 2      # 预取批次数
    async_inference: true    # 异步推理

  # 信号生成优化
  signal_generation:
    confidence_threshold: 0.65   # 提高置信度阈值
    signal_smoothing: true       # 信号平滑
    ensemble_voting: true        # 集成投票
    risk_adjusted_signals: true  # 风险调整信号

  # 多时间框架流式处理优化
  multi_timeframe_streaming:
    enable: true
    data_sync_strategy: latest_available
    parallel_processing: true    # 并行处理

    # 优化的更新频率
    feature_update_frequency:
      1m: 30      # 30秒更新（提高频率）
      3m: 90      # 90秒更新
      5m: 150     # 2.5分钟更新
      15m: 450    # 7.5分钟更新
      1h: 1800    # 30分钟更新
      4h: 7200    # 2小时更新
      1d: 43200   # 12小时更新

    # 时间框架优先级（按重要性排序）
    timeframe_priorities:
    - 1h    # 最高优先级
    - 15m
    - 5m
    - 4h
    - 3m
    - 1m
    - 1d

  # 在线学习策略
  learning_strategy:
    adaptive_learning_rate: true     # 自适应学习率
    incremental_training: true       # 增量训练
    concept_drift_detection: true    # 概念漂移检测
    model_ensemble_update: true      # 模型集成更新

  # 性能监控
  monitoring:
    real_time_metrics: true          # 实时指标
    performance_tracking: true       # 性能跟踪
    alert_system: true              # 告警系统
    auto_model_selection: true      # 自动模型选择
