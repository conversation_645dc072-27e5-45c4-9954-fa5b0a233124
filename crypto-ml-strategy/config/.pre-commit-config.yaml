# Pre-commit hooks configuration
repos:
  # 基础代码质量检查
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-docstring-first
      - id: check-executables-have-shebangs
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: mixed-line-ending
        args: ['--fix=lf']

  # Python代码格式化
  - repo: https://github.com/psf/black
    rev: 23.7.0
    hooks:
      - id: black
        language_version: python3
        args: ['--line-length=100']

  # Import排序
  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        args: ['--profile=black', '--line-length=100']

  # 代码风格检查
  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        args: ['--max-line-length=100', '--ignore=E203,W503,E501']
        additional_dependencies:
          - flake8-docstrings
          - flake8-bugbear
          - flake8-comprehensions

  # 类型检查
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.4.1
    hooks:
      - id: mypy
        args: ['--ignore-missing-imports', '--no-strict-optional']
        additional_dependencies:
          - types-requests
          - types-PyYAML
          - types-redis

  # 安全检查
  - repo: https://github.com/pycqa/bandit
    rev: 1.7.5
    hooks:
      - id: bandit
        args: ['-r', 'src/', '-f', 'json']
        exclude: tests/

  # Jupyter notebook清理
  - repo: https://github.com/nbQA-dev/nbQA
    rev: 1.7.0
    hooks:
      - id: nbqa-black
        additional_dependencies: [black==23.7.0]
      - id: nbqa-isort
        additional_dependencies: [isort==5.12.0]
      - id: nbqa-flake8
        additional_dependencies: [flake8==6.0.0]

  # 文档检查
  - repo: https://github.com/pycqa/pydocstyle
    rev: 6.3.0
    hooks:
      - id: pydocstyle
        args: ['--convention=google']

  # YAML格式化
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        types: [yaml]

  # Shell脚本检查
  - repo: https://github.com/shellcheck-py/shellcheck-py
    rev: v0.9.0.5
    hooks:
      - id: shellcheck

  # 密钥检测
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.4.0
    hooks:
      - id: detect-secrets
        args: ['--baseline', '.secrets.baseline']

# 全局配置
default_stages: [commit]
fail_fast: false

# CI配置
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: []
  submodules: false
