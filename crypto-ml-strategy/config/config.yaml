# ==============================================================================
# Crypto ML Strategy - Unified Configuration
#
# Single source of truth for all application settings.
# Environment-specific overrides can be placed in 'config.local.yaml'.
# Secrets should be provided via environment variables (e.g., ${API_KEY}).
# ==============================================================================

# ------------------------------------------------------------------------------
# System & Environment
# ------------------------------------------------------------------------------
system:
  timezone: "UTC"
  debug_mode: false
  threading:
    torch_num_threads: 8
    omp_num_threads: 8

# ------------------------------------------------------------------------------
# Logging Configuration
# ------------------------------------------------------------------------------
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/training.log"
  max_size: "100MB"
  backup_count: 5

# ------------------------------------------------------------------------------
# API Keys & External Services
# ------------------------------------------------------------------------------
api:
  binance:
    api_key: ${BINANCE_API_KEY:}
    secret_key: ${BINANCE_SECRET_KEY:}
    base_url: ${BINANCE_BASE_URL:https://api.binance.com}
    timeout: 10
    max_retries: 3
    retry_delay: 1
  deepseek:
    enabled: false
    api_key: ${DEEPSEEK_API_KEY:}
    base_url: "https://api.deepseek.com/v1"
    model: "deepseek-chat"
    max_concurrent_requests: 50
    max_retries: 3
    rate_limit:
      requests_per_minute: 150
    timeout: 30
    parameters:
      max_tokens: 2048
      temperature: 0.7
      top_p: 0.9

# ------------------------------------------------------------------------------
# Java Data Service (Backend API)
# ------------------------------------------------------------------------------
java_service:
  base_url: "http://127.0.0.1:19527" # Correct port for the Java backend
  timeout: 300 # Increased timeout for potentially long-running data queries

# ------------------------------------------------------------------------------
# Storage (Database, Cache, Files)
# ------------------------------------------------------------------------------
storage:
  base_path: "data"
  directories:
    raw: "data/raw"
    processed: "data/processed"
    models: "data/models"
    cache: "data/cache"
    logs: "logs"
  mysql:
    enabled: true
    host: "${MYSQL_HOST:localhost}"
    port: ${MYSQL_PORT:13306}  # 修复：使用实际的Docker映射端口
    database: "crypto_market_data"  # 修复：使用正确的数据库名
    username: "${MYSQL_USER:root}"
    password: "${MYSQL_PASSWORD:root}"
    pool_size: 10
    charset: "utf8mb4"
    autocommit: false
    connect_timeout: 10
    read_timeout: 30
    write_timeout: 30
  redis:
    enabled: true
    host: "${REDIS_HOST:localhost}"
    port: ${REDIS_PORT:6379}
    db: ${REDIS_DB:0}
    password: "${REDIS_PASSWORD:root}"
    max_connections: 100
    default_ttl: 7200
    metadata_logging: false
  influxdb:
    url: "${INFLUXDB_URL:http://localhost:8086}"
    token: ${INFLUXDB_TOKEN:zmJ1sGNooabOZbuEWW3MdwUIeL9btWRXJgX_Y4KgTIxJ3GhCxsWqi25qRQr_4FqrcMMWEibD4LkD397IKG1H0w==}
    org: "binance"
    bucket: "market_data"  # 修复：使用正确的bucket名称（下划线而非连字符）
    batch_size: 1000

# ------------------------------------------------------------------------------
# Kafka Configuration
# ------------------------------------------------------------------------------
kafka:
  bootstrap_servers: "localhost:29092"
  producer:
    acks: "all"
    retries: 3
    batch_size: 16384
    linger_ms: 5
    buffer_memory: 33554432
    compression_type: "lz4"
  consumer:
    group_id: "crypto-ml-strategy"
    auto_offset_reset: "latest"
    enable_auto_commit: false
    max_poll_records: 100
  topics:
    market_data: "crypto-market-data-kline"
    trade_signals: "trade-signals"

# ------------------------------------------------------------------------------
# Data Pipeline Configuration
# ------------------------------------------------------------------------------
data:
  symbols: ["BTCUSDT", "ETHUSDT", "BNBUSDT", "SOLUSDT", "DOGEUSDT"] # 扩展到所有可用交易对
  timeframes: ["1m", "3m", "5m", "15m", "1h", "4h", "1d"] # 全时间框架混合训练
  min_data_points: 30  # 降低要求以适应短时间框架
  use_generated_data: true  # 启用生成的测试数据
  fallback_to_synthetic: true  # 启用合成数据回退

  # 多时间框架混合训练配置
  multi_timeframe_config:
    enable_mixed_training: true  # 启用混合时间框架训练
    primary_timeframes: ["5m", "15m", "1h"]  # 主要时间框架（数据充足）
    secondary_timeframes: ["1m", "3m", "4h", "1d"]  # 次要时间框架（数据较少）
    min_data_per_timeframe: 20  # 每个时间框架最少数据量
    feature_fusion_method: "concatenate"  # 特征融合方法：concatenate/weighted_average/attention

  # 数据量优化配置
  data_requirements:
    min_days_coverage: 2    # 进一步降低到2天覆盖
    adaptive_requirements: true  # 启用自适应数据要求
    fallback_strategy: "use_available"  # 使用可用数据策略

  # 数据源优先级配置（新增）
  source_priority:
    - "influxdb"      # 🥇 主要数据源：InfluxDB
    - "java_service"  # 🥈 备用数据源：Java API
    - "generated"     # 🥉 最后回退：生成数据
  sources:
    generated_data:
      enabled: true
      data_dir: "data/generated"
  split:
    train_ratio: 0.7
    val_ratio: 0.15
    test_ratio: 0.15
    temporal_split: true
  target_column: "label"
  cleaning:
    handle_missing: true
    missing_method: "median"
    handle_outliers: true
    outlier_method: "iqr"
  scaling:
    normalize_features: true
    method: "standard"

# ------------------------------------------------------------------------------
# Feature Engineering
# ------------------------------------------------------------------------------
feature_engineering:
  technical_indicators:
    - "sma"
    - "ema"
    - "rsi"
    - "macd"
    - "bollinger_bands"
    - "stochastic_oscillator"
    - "williams_r"
    - "atr"
    - "obv"
    - "ichimoku_cloud"
  window_sizes: [14, 26, 52]
  # Additional feature configurations can go here

# ------------------------------------------------------------------------------
# Model Architecture
# ------------------------------------------------------------------------------
model:
  name: "UnifiedSignalFusionModel"
  device: "auto"
  sequence_length: 1
  output_dim: 3
  # feature_dim is now auto-calculated
  hidden_dims:
  num_heads: 8
  num_layers: 3
  dropout: 0.1
  use_attention: true
  use_lstm: true
  use_transformer: true
  use_gradient_checkpointing: false

# ------------------------------------------------------------------------------
# Training Pipeline
# ------------------------------------------------------------------------------
training:
  epochs: 10
  learning_rate: 0.001
  weight_decay: 0.0001
  optimizer: "adamw"
  scheduler: "cosine"
  grad_clip_norm: 1.0
  batch_size: 4096
  early_stopping:
    patience: 5
    min_delta: 0.001
  save_best_only: true
  checkpoint_path: "data/models"
  distillation:
    enabled: false
    mode: "disabled"
    temperature: 2.5
    alpha: 0.8
    beta: 0.2
    teacher_call_threshold: 0.5
    max_teacher_calls_per_epoch: 200

# ------------------------------------------------------------------------------
# GPU & Performance Optimization
# ------------------------------------------------------------------------------
gpu:
  compilation:
    enabled: true
    mode: "max-autotune"
  mixed_precision:
    enabled: true
  dataloader:
    num_workers: 4
    persistent_workers: true
    pin_memory: true
    prefetch_factor: 2

# ------------------------------------------------------------------------------
# Online Learning
# ------------------------------------------------------------------------------
online_learning:
  enabled: true
  buffer_size: 10000
  batch_size: 16
  learning_rate: 0.0001
  drift_detection_method: "statistical"
  drift_threshold: 0.05

# ------------------------------------------------------------------------------
# Risk Management
# ------------------------------------------------------------------------------
risk_management:
  enabled: true
  thresholds:
    volatility: { low: 0.3, medium: 0.6, high: 1.2, critical: 2.0 }
    drawdown: { low: 0.1, medium: 0.2, high: 0.4, critical: 0.6 }
    var: { low: 0.05, medium: 0.1, high: 0.2, critical: 0.4 }
    concentration: { low: 0.3, medium: 0.5, high: 0.7, critical: 0.9 }

# ------------------------------------------------------------------------------
# Monitoring & Visualization
# ------------------------------------------------------------------------------
monitoring:
  enable_live_plot: false
  update_interval: 100
visualization:
  enabled: false
  dashboard_port: 8501

# ------------------------------------------------------------------------------
# Online Mode Configuration (Real-time Trading Signals)
# ------------------------------------------------------------------------------
online:
  enabled: true
  symbol: "BTCUSDT"
  timeframe: "15m"  # 主要时间框架（修改为有数据的时间框架）
  timeframes: ["15m", "5m"]  # 多时间框架支持（修改为有数据的时间框架）
  signal_interval: 30  # 信号生成间隔（秒）
  max_signals_per_hour: 10
  confidence_threshold: 0.7

  # 实时数据获取配置（新增）
  realtime_data:
    cache_ttl: 300  # 缓存生存时间（秒）
    min_data_points: 20  # 最小数据点要求（降低）
    fallback_enabled: true  # 启用回退机制

# ------------------------------------------------------------------------------
# Logging Configuration
# ------------------------------------------------------------------------------
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file: "logs/crypto_ml.log"