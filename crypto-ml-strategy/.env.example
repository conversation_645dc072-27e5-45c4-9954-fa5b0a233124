# ===============================================================
# Environment Variables Example
#
# Copy this file to .env and fill in the values for your local development.
# Do NOT commit the .env file to version control.
# ===============================================================

# --- Binance API Credentials ---
# Required for fetching market data and executing trades.
BINANCE_API_KEY=
BINANCE_SECRET_KEY=

# --- DeepSeek API (Optional) ---
# Required only if you enable knowledge distillation with DeepSeek.
DEEPSEEK_API_KEY=

# --- Storage Services ---
# Configure connections to your database and cache instances.

# MySQL (if enabled in config.yaml)
MYSQL_HOST=localhost
MYSQL_PORT=13306
MYSQL_USER=root
MYSQL_PASSWORD=

# Redis (if enabled in config.yaml)
REDIS_HOST=localhost
REDIS_PORT=16379
REDIS_PASSWORD=

# InfluxDB (Required)
# The token needs 'write' access to the 'market-data' bucket.
INFLUXDB_URL=http://localhost:8086
INFLUXDB_TOKEN=
