# 第一阶段：构建依赖
FROM python:3.12-slim AS builder

WORKDIR /app

# 安装构建工具和系统依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc g++ \
    pkg-config \
    libhdf5-dev \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY requirements.txt .

# 安装依赖到虚拟环境
RUN python -m venv /venv
ENV PATH="/venv/bin:$PATH"

# 升级pip和安装构建工具
RUN pip install --upgrade pip setuptools wheel

# 单独安装CPU版本的torch以减小体积和加速
RUN pip install torch>=2.0.1,<2.2.0 --index-url https://download.pytorch.org/whl/cpu

# 安装其他依赖
RUN pip install --no-cache-dir -r requirements.txt

# 第二阶段：运行时
FROM python:3.12-slim

WORKDIR /app

# 从构建阶段复制虚拟环境
COPY --from=builder /venv /venv

# 设置环境变量
ENV PATH="/venv/bin:$PATH"

# 复制项目文件
COPY . .

# 运行测试
CMD ["python", "-m", "pytest", "tests/"]
