# 基础依赖
torch>=2.0.1,<2.2.0
numba>=0.59.0,<0.61.0
pandas-ta>=0.3.14b
transformers>=4.30.0,<5.0.0
numpy>=1.24.0,<2.0.0  # 修复：使用兼容版本
pandas>=2.0.0,<2.2.0
psutil>=5.9.0,<6.0.0
scikit-learn>=1.3.0,<1.5.0
matplotlib>=3.7.0,<4.0.0
aiohttp>=3.8.0,<4.0.0
aiofiles>=23.1.0,<24.0.0
plotly>=5.15.0,<6.0.0
streamlit>=1.25.0,<2.0.0
seaborn>=0.12.0,<0.13.0
diskcache>=5.6.0,<6.0.0
websockets>=11.0.0,<13.0.0
kafka-python>=2.0.0,<3.0.0
prometheus-client>=0.17.0,<1.0.0
requests>=2.31.0,<3.0.0
pynvml>=11.5.0,<12.0.0
pyarrow>=12.0.0,<15.0.0  # 添加PyArrow依赖

# 测试依赖
pytest==7.4.0
pytest-asyncio==0.21.1
pytest-cov==4.1.0
pytest-mock==3.12.0
responses==0.24.1
uvicorn
fastapi
imbalanced-learn
optuna
influxdb-client
mysql-connector-python
redis
PyYAML
