# Crypto ML Strategy

## 项目概述

这是一个基于机器学习的加密货币交易策略系统，集成了数据处理、模型训练、策略执行和风险管理等功能。

## 项目结构

```
crypto-ml-strategy/
├── src/                    # 源代码
│   ├── core/              # 核心应用逻辑
│   ├── data/              # 数据处理模块
│   ├── models/            # 机器学习模型
│   ├── strategy/          # 交易策略模块
│   ├── training/          # 模型训练模块
│   ├── risk/              # 风险管理模块
│   ├── utils/             # 工具函数
│   └── ...
├── tests/                 # 测试代码
│   ├── unit/              # 单元测试
│   ├── integration/       # 集成测试
│   └── ...
├── config/                # 配置文件
│   ├── environments/      # 环境配置
│   ├── models/            # 模型配置
│   └── strategies/        # 策略配置
├── data/                  # 数据文件
│   ├── raw/               # 原始数据
│   ├── processed/         # 处理后数据
│   └── ...
├── models/                # 模型文件
│   ├── checkpoints/       # 模型检查点
│   ├── versions/          # 模型版本
│   └── exports/           # 导出模型
├── docs/                  # 文档
├── scripts/               # 脚本文件
├── logs/                  # 日志文件
└── main.py               # 主入口文件
```

## 快速开始

1. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

2. 运行系统：
   ```bash
   python main.py --mode auto
   ```

## 主要功能

- 🤖 **自动模式**: 根据模型存在性自动选择训练或在线学习
- 📊 **数据处理**: 完整的数据清洗、特征工程和数据集管理
- 🧠 **机器学习**: 支持多种模型和训练策略
- 📈 **交易策略**: 技术分析、机器学习和混合信号生成
- ⚖️ **风险管理**: 多层次风险评估和控制
- 📱 **实时监控**: 性能监控和可视化
- 🔄 **在线学习**: 实时模型更新和适应

## 配置说明

系统使用YAML配置文件，主要配置包括：

- `config/models/model_config.yaml`: 模型相关配置
- `config/environments/performance_optimized.yaml`: 性能优化配置
- `config/environments/database.yaml`: 数据库配置

## 测试

运行所有测试：
```bash
python -m pytest tests/
```

运行特定测试：
```bash
python -m pytest tests/unit/
python -m pytest tests/integration/
```

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

MIT License
