#!/usr/bin/env python3
"""
深度学习策略模块主入口 (Deep Learning Strategy Module Main Entry Point)
"""

# Add project root to the Python path
import sys
from pathlib import Path
sys.path.insert(0, str(Path(__file__).parent.resolve()))

# Configure Numba before other imports
from src.utils.numba_config import configure_numba
configure_numba()

import asyncio
import argparse
import logging
import os
import torch

from src.app import AppOrchestrator

def setup_cli() -> argparse.ArgumentParser:
    """设置命令行接口 (CLI) 参数。"""
    parser = argparse.ArgumentParser(description="Crypto ML Strategy Main Application")
    
    parser.add_argument(
        "--mode", "-m", type=str,
        choices=["auto", "train", "online", "evaluate", "full_cycle"],
        default="auto",
        help="Running mode: 'auto' decides based on model existence, 'train' forces training, 'online' runs inference."
    )
    parser.add_argument("--debug", action="store_true", help="Enable debug logging.")
    parser.add_argument("--gpu", type=str, help="GPU device ID (e.g., '0', 'cpu').")
    
    # Training specific arguments
    train_group = parser.add_argument_group('Training Options')
    train_group.add_argument("--symbols", type=str, nargs='+', default=["BTCUSDT", "ETHUSDT"], help="List of symbols for training.")
    train_group.add_argument("--force-refresh", action="store_true", help="Force refresh dataset.")
    train_group.add_argument("--days", type=int, default=90, help="Number of days of historical data to collect.")
    train_group.add_argument("--epochs", "-e", type=int, help="Override number of training epochs.")
    train_group.add_argument("--batch_size", type=int, help="Override training batch size.")
    train_group.add_argument("--trainer", type=str, choices=["standard", "distillation"], default="distillation", help="Specify the trainer type.")
    train_group.add_argument("--timeframes", type=str, nargs='+', default=["1h", "4h"], help="List of timeframes for historical data (e.g., '1h', '4h', '1d').")
    
    # Online learning specific arguments
    online_group = parser.add_argument_group('Online Learning Options')
    online_group.add_argument("--symbol", "-s", type=str, default="BTCUSDT", help="Symbol for online learning and signal generation.")
    online_group.add_argument("--signal-interval", type=int, default=1, help="Interval for signal generation in seconds.")

    # Evaluation specific arguments
    eval_group = parser.add_argument_group('Evaluation Options')
    eval_group.add_argument("--checkpoint", type=str, help="Path to the model checkpoint for evaluation.")

    # Full Cycle specific arguments
    full_cycle_group = parser.add_argument_group('Full Cycle Options')
    full_cycle_group.add_argument("--fc-symbols", type=str, nargs='+', 
                                default=["BTCUSDT", "ETHUSDT", "BNBUSDT", "SOLUSDT", "DOGEUSDT"], 
                                help="Symbols for full-cycle training.")
    full_cycle_group.add_argument("--fc-timeframes", type=str, nargs='+', 
                                default=["1m", "5m", "15m", "30m", "1h", "4h", "1d"], 
                                help="Timeframes for full-cycle training.")
    # (Future arguments for this mode will go here)

    return parser

async def main():
    """主异步执行函数。"""
    print("DEBUG: main() started")
    parser = setup_cli()
    args = parser.parse_args()
    print("DEBUG: CLI args parsed")

    # --- Environment and System Setup ---
    if args.gpu:
        os.environ['CUDA_VISIBLE_DEVICES'] = args.gpu
    if args.debug:
        os.environ['CRYPTO__SYSTEM__DEBUG_MODE'] = 'true'
    
    print("DEBUG: Checking torch.cuda.is_available()")

    # 安全的CUDA检查，避免段错误
    cuda_available = False
    try:
        # 设置CUDA环境变量避免段错误
        os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
        os.environ['TORCH_USE_CUDA_DSA'] = '1'

        # 分步骤安全检查CUDA
        cuda_available = torch.cuda.is_available()
        print(f"DEBUG: torch.cuda.is_available() = {cuda_available}")

        if cuda_available and args.gpu != 'cpu':
            try:
                # 获取设备信息但不分配内存
                device_count = torch.cuda.device_count()
                device_name = torch.cuda.get_device_name(0) if device_count > 0 else "Unknown"
                print(f"DEBUG: Found {device_count} CUDA devices, primary: {device_name}")

                # 跳过内存测试，直接使用
                print("DEBUG: CUDA available, skipping memory test to avoid segfault")

            except Exception as e:
                print(f"DEBUG: CUDA device info failed: {e}")
                cuda_available = False
        else:
            print("DEBUG: CUDA not available or CPU mode requested")
            cuda_available = False
    except Exception as e:
        print(f"DEBUG: CUDA check failed: {e}, falling back to CPU")
        cuda_available = False
        if args.gpu != 'cpu':
            args.gpu = 'cpu'

    if cuda_available and args.gpu != 'cpu':
        print("DEBUG: CUDA is available, setting start method to 'spawn'")
        try:
            torch.multiprocessing.set_start_method('spawn', force=True)
            print("DEBUG: Start method set SUCCESSFULLY.")
        except RuntimeError as e:
            print(f"DEBUG: Failed to set start method: {e}")
    else:
        print("DEBUG: Using CPU mode")

    # --- Project Root Setup ---
    # Get the absolute path of the project root directory
    project_root = Path(__file__).parent.resolve()
    print(f"DEBUG: Project Root detected at: {project_root}")

    app = None
    try:
        print("DEBUG: Initializing AppOrchestrator...")
        # The project_root is no longer needed for AppOrchestrator initialization
        # as it's now determined internally and filesystem access is avoided.
        print("DEBUG: PRE-INITIALIZING AppOrchestrator...")
        app = AppOrchestrator()
        print("DEBUG: POST-INITIALIZING AppOrchestrator...")
        print("DEBUG: AppOrchestrator initialized. Calling app.initialize()...")
        await app.initialize()
        print("DEBUG: app.initialize() completed.")

        # --- Mode Dispatch ---
        mode = args.mode
        if mode == "auto":
            model_check = app.check_model_exists()
            mode = 'online' if model_check['model_exists'] else 'train'
            app.logger.info(f"🤖 Auto-mode selected: Running in '{mode}' mode.")

        # Manually check for required arguments in evaluate mode
        if mode == "evaluate" and not args.checkpoint:
            parser.error("the following arguments are required for 'evaluate' mode: --checkpoint")

        if mode == "train":
            await app.run_training(
                symbols=args.symbols,
                force_refresh=args.force_refresh,
                days=args.days,
                epochs=args.epochs,
                timeframes=args.timeframes
            )
        elif mode == "online":
            await app.run_online_learning_with_kafka(
                symbol=args.symbol,
                signal_interval=args.signal_interval
            )
        elif mode == "evaluate":
            await app.run_evaluation(
                checkpoint=args.checkpoint,
                symbols=args.symbols
            )
        elif mode == "full_cycle":
            await app.run_full_cycle_workflow(args)

    except Exception as e:
        logging.getLogger().critical(f"Application failed with a critical error: {e}", exc_info=True)
        sys.exit(1)
    finally:
        if app:
            await app.cleanup()

if __name__ == "__main__":
    asyncio.run(main())