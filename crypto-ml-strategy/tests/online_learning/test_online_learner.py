"""
在线学习器测试类
"""

import pytest
import torch
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from src.online_learning.online_learner import OnlineLearner
from src.models.base_model import BaseDeepLearningModel


class TestOnlineLearner:
    """在线学习器测试类"""

    def setup_method(self):
        """测试前设置"""
        self.config = {
            'model': {
                'feature_dim': 59,
                'hidden_dims': [256, 128, 64],
                'num_heads': 8,
                'num_layers': 3,
                'dropout': 0.2,
                'output_dim': 3
            },
            'training': {
                'batch_size': 32,
                'learning_rate': 0.001,
                'epochs': 1
            },
            'kafka': {
                'bootstrap_servers': ['localhost:9092'],
                'topics': ['crypto-market-data-kline']
            }
        }
        
        # Mock模型需要像一个torch模块一样运行
        self.mock_model = Mock(spec=BaseDeepLearningModel)
        # _initialize_components会访问模型参数来确定设备
        mock_param = torch.nn.Parameter(torch.randn(1, device='cpu'))
        self.mock_model.parameters.side_effect = lambda: iter([mock_param])
        self.mock_model.feature_dim = 59
        
        self.learner = OnlineLearner(config=self.config, model=self.mock_model)
        # 模拟.to(device)调用，它应该返回模型自身
        self.mock_model.to.return_value = self.mock_model

    def test_init_with_config(self):
        """测试使用配置初始化"""
        assert self.learner.config == self.config
        assert self.learner.model == self.mock_model
        assert self.learner.device is not None

    def test_init_without_model(self):
        """测试不提供模型时的初始化"""
        learner = OnlineLearner(self.config)
        assert learner.model is None

    @pytest.mark.skip(reason="Obsolete test: _extract_features is a removed private method.")
    def test_feature_extraction(self):
        """测试特征提取"""
        # 模拟市场数据
        market_data = {
            'symbol': 'BTCUSDT',
            'open': '50000.0',
            'high': '51000.0',
            'low': '49000.0',
            'close': '50500.0',
            'volume': '1000.0',
            'timestamp': 1640995200000
        }
        
        features = self.learner._extract_features(market_data)
        
        assert isinstance(features, list)
        assert len(features) > 0
        assert all(isinstance(f, (int, float)) for f in features)

    @pytest.mark.skip(reason="Obsolete test: _adjust_feature_dimensions is a removed private method.")
    def test_feature_dimension_adjustment(self):
        """测试特征维度调整"""
        # 测试特征数量少于目标维度
        features = [1.0, 2.0, 3.0]  # 只有3个特征
        adjusted = self.learner._adjust_feature_dimensions(features, target_dim=59)
        
        assert len(adjusted) == 59
        assert adjusted[:3] == features
        assert all(f == 0.0 for f in adjusted[3:])

    @pytest.mark.skip(reason="Obsolete test: _adjust_feature_dimensions is a removed private method.")
    def test_feature_dimension_truncation(self):
        """测试特征维度截取"""
        # 测试特征数量多于目标维度
        features = list(range(100))  # 100个特征
        adjusted = self.learner._adjust_feature_dimensions(features, target_dim=59)
        
        assert len(adjusted) == 59
        assert adjusted == list(range(59))

    @pytest.mark.skip(reason="Obsolete test: _validate_market_data is a removed private method.")
    def test_data_validation(self):
        """测试数据验证"""
        # 有效数据
        valid_data = {
            'symbol': 'BTCUSDT',
            'open': '50000.0',
            'high': '51000.0',
            'low': '49000.0',
            'close': '50500.0',
            'volume': '1000.0'
        }
        assert self.learner._validate_market_data(valid_data) is True

        # 无效数据（缺少必需字段）
        invalid_data = {
            'symbol': 'BTCUSDT',
            'open': '50000.0'
            # 缺少其他必需字段
        }
        assert self.learner._validate_market_data(invalid_data) is False

    @pytest.mark.skip(reason="Obsolete test: _process_batch is a removed private method.")
    def test_batch_processing(self):
        """测试批处理"""
        # 创建模拟数据批次
        batch_data = []
        for i in range(10):
            batch_data.append({
                'symbol': 'BTCUSDT',
                'open': f'{50000 + i}',
                'high': f'{51000 + i}',
                'low': f'{49000 + i}',
                'close': f'{50500 + i}',
                'volume': f'{1000 + i}',
                'timestamp': 1640995200000 + i * 60000
            })
        
        # 测试批处理不会抛出异常
        try:
            self.learner._process_batch(batch_data)
        except Exception as e:
            pytest.fail(f"批处理不应该抛出异常: {e}")

    @pytest.mark.asyncio
    async def test_model_update_with_incremental_update(self):
        """测试使用 incremental_update 进行模型更新"""
        # 创建模拟特征和标签
        features = np.random.randn(59).tolist()
        labels = np.random.randint(0, 3)
        new_data = {'features': features, 'labels': labels}

        # Mock 模型的返回值
        self.mock_model.return_value = {'signal': torch.randn(1, 3)}
        self.mock_model.train.return_value = None # 模拟 train() 方法

        try:
            result = await self.learner.incremental_update(new_data)
            assert result['update_type'] == 'incremental'
            assert 'loss' in result
            assert result['loss'] > 0
        except Exception as e:
            pytest.fail(f"incremental_update 不应引发异常: {e}")

    @pytest.mark.skip(reason="Obsolete test: _calculate_metrics is a removed private method.")
    def test_performance_metrics(self):
        """测试性能指标计算"""
        # 模拟预测和真实标签
        predictions = torch.tensor([[0.8, 0.1, 0.1], [0.2, 0.7, 0.1], [0.1, 0.2, 0.7]])
        labels = torch.tensor([0, 1, 2])
        
        metrics = self.learner._calculate_metrics(predictions, labels)
        
        assert isinstance(metrics, dict)
        assert 'accuracy' in metrics
        assert 'loss' in metrics
        assert 0.0 <= metrics['accuracy'] <= 1.0

    @pytest.mark.asyncio
    async def test_incremental_update_adds_to_buffer(self):
        """测试 incremental_update 将数据添加到缓冲区"""
        new_data = {'features': np.random.randn(59).tolist(), 'labels': 1}
        
        initial_size = len(self.learner.training_buffer)
        await self.learner.incremental_update(new_data)
        
        assert len(self.learner.training_buffer) == initial_size + 1

    @pytest.mark.skip(reason="Obsolete test: _adjust_learning_rate is a removed private method.")
    def test_learning_rate_adjustment(self):
        """测试学习率调整"""
        initial_lr = 0.001
        
        # 测试学习率调整不会抛出异常
        try:
            self.learner._adjust_learning_rate(epoch=5, total_epochs=10)
        except Exception as e:
            pytest.fail(f"学习率调整不应该抛出异常: {e}")

    @pytest.mark.asyncio
    async def test_error_handling_in_incremental_update(self):
        """测试 incremental_update 中的错误处理"""
        # 模拟模型的返回值以避免 'Mock' object is not subscriptable 错误
        self.mock_model.return_value = {'signal': torch.randn(1, 3)}
        # incremental_update 应该能处理没有 'labels' 的数据
        new_data_no_labels = {'features': np.random.randn(59).tolist()}
        try:
            result = await self.learner.incremental_update(new_data_no_labels)
            assert result['update_type'] == 'none' # 没有标签，不应该更新
        except Exception:
            pytest.fail("对于没有标签的数据，应该优雅地处理而不是引发异常")

    def test_get_learning_stats(self):
        """测试 get_learning_stats 公共方法"""
        stats = self.learner.get_learning_stats()
        assert 'buffer_size' in stats
        assert 'active_version' in stats
        assert 'learning_rate' in stats

    @pytest.mark.asyncio
    async def test_async_learning_process(self):
        """测试异步学习过程"""
        # Mock Kafka客户端 - 目标是其在被导入模块中的路径
        with patch('kafka.kafka_client.KafkaClient') as mock_kafka_class:
            mock_kafka_instance = Mock()
            # 模拟 consume_market_data 是一个异步方法
            async def mock_consume(*args, **kwargs):
                return 0
            mock_kafka_instance.consume_market_data = mock_consume
            mock_kafka_class.return_value = mock_kafka_instance
            
            try:
                # 调用 learner 的公共方法
                await self.learner.start_online_learning_async(timeout=1)
            except Exception as e:
                pytest.fail(f"异步学习过程不应该抛出异常: {e}")


if __name__ == '__main__':
    pytest.main([__file__])
