"""
日志输出频率优化测试
分析和优化系统中的日志输出频率，减少I/O开销
"""
import sys
from pathlib import Path
import torch
import torch.nn as nn
import pytest
import asyncio
from typing import List

sys.path.append(str(Path(__file__).resolve().parent.parent.parent))

from src.training.unified_trainer import UnifiedTrainer, TrainerConfig, TrainingCallback, LoggerCallback
from torch.utils.data import DataLoader, TensorDataset

class TestLoggingFrequencyOptimization:
    """日志输出频率优化测试类"""

    def setup_method(self, method):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"\\n🔧 测试设备: {self.device}")

    def test_default_logging_behavior(self):
        """测试TrainerConfig默认情况下是否包含LoggerCallback。"""
        print(f"\\n🧪 Test: Default Logging Behavior")
        config = TrainerConfig()
        # The logic for default callbacks is now in the UnifiedTrainer's constructor.
        # We must instantiate the trainer to check the final callback list.
        model = nn.Sequential(nn.Linear(10, 2))
        trainer = UnifiedTrainer(model=model, config=config, device=self.device)
        
        has_logger = any(isinstance(cb, LoggerCallback) for cb in trainer.callbacks)
        print(f"📊 Default trainer has LoggerCallback: {has_logger}")
        assert has_logger, "A default UnifiedTrainer should include a LoggerCallback."

    @pytest.mark.asyncio
    async def test_disabled_logging_via_empty_callbacks(self, batch_size: int = 2048):
        """测试通过传递空回调列表来禁用日志记录。"""
        if not torch.cuda.is_available():
            pytest.skip("CUDA不可用，跳过此测试")

        print(f"\\n🧪 Test: Disabled Logging via Empty Callbacks")
        model = nn.Sequential(nn.Linear(10, 2)).to(self.device)
        # Pass an empty list to override default callbacks
        config = TrainerConfig(callbacks=[])
        trainer = UnifiedTrainer(model=model, config=config, device=self.device)

        has_logger = any(isinstance(cb, LoggerCallback) for cb in trainer.config.callbacks)
        print(f"📊 Trainer with empty callbacks list has LoggerCallback: {has_logger}")
        assert not has_logger, "Trainer should have no LoggerCallback when an empty list is provided."

        # Also run a quick training session to ensure it doesn't crash
        try:
            num_samples = batch_size * 10
            features = torch.randn(num_samples, 10)
            labels = torch.randint(0, 2, (num_samples,))
            dataset = TensorDataset(features, labels)
            dataloader = DataLoader(dataset, batch_size=batch_size)
            await trainer.train(dataloader)
            print("✅ Trainer ran successfully with logging disabled.")
        except Exception as e:
            pytest.fail(f"Trainer failed to run with logging disabled: {e}")
