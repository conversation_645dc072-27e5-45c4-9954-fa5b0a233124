"""
最终日志优化验证测试
验证500批次日志间隔的最优配置
"""

import time
import sys
from pathlib import Path
import torch
import torch.nn as nn
import numpy as np
import pytest
import asyncio

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).resolve().parent.parent.parent))

from src.training.unified_trainer import UnifiedTrainer, TrainerConfig
from torch.utils.data import DataLoader, TensorDataset


class TestFinalLoggingOptimization:
    """最终日志优化验证测试类"""
    
    def setup_method(self, method):
        """初始化测试"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"\\n🔧 测试设备: {self.device}")
    
    def _create_test_model(self, input_size: int = 59) -> nn.Module:
        """创建测试模型"""
        model = nn.Sequential(
            nn.Linear(input_size, 128), nn.ReLU(),
            nn.Linear(128, 64), nn.ReLU(),
            nn.Linear(64, 3)
        )
        return model.to(self.device)
    
    def _create_test_dataloader(self, batch_size: int, input_size: int, num_batches: int = 100) -> DataLoader:
        """创建测试数据加载器"""
        num_samples = batch_size * num_batches
        features = torch.randn(num_samples, input_size)
        labels = torch.randint(0, 3, (num_samples,))
        dataset = TensorDataset(features, labels)
        
        is_cuda = self.device.type == 'cuda'
        return DataLoader(
            dataset, batch_size=batch_size, shuffle=False,
            num_workers=4 if is_cuda else 0, pin_memory=is_cuda, persistent_workers=is_cuda
        )

    async def _run_single_epoch(self, dataloader):
        """运行一个单独的训练周期并返回性能指标"""
        model = self._create_test_model()
        train_config = TrainerConfig(epochs=1, use_amp=True, use_compile=False)
        trainer = UnifiedTrainer(model=model, config=train_config, device=self.device)
        
        start_time = time.time()
        await trainer.train(dataloader, val_loader=None)
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        total_time = time.time() - start_time
        
        total_samples = len(dataloader.dataset)
        throughput = total_samples / total_time if total_time > 0 else 0
        avg_loss = trainer.state.metrics.get('train_loss', -1)

        return throughput, avg_loss

    @pytest.mark.asyncio
    async def test_final_optimized_performance(self, batch_size: int = 8192):
        """测试最终优化后的单次运行性能（冒烟测试）"""
        if not torch.cuda.is_available():
            pytest.skip("CUDA不可用，跳过此测试")
            
        print(f"\\n🧪 Smoke Test: Final Optimized Performance (log interval=500)")
        dataloader = self._create_test_dataloader(batch_size, 59, num_batches=100) # Reduced batches
        
        throughput, avg_loss = await self._run_single_epoch(dataloader)
        
        print(f"📊 Final Optimized Performance: Throughput={throughput:.0f} samples/s, Avg Loss={avg_loss:.4f}")
        
        assert throughput > 0, "Throughput must be positive"
        assert avg_loss > 0, f"Average loss {avg_loss} must be positive"

    @pytest.mark.asyncio
    async def test_stability_multiple_runs(self, batch_size: int = 4096): # Reduced batch size
        """测试多次运行的稳定性（仅供观察）"""
        if not torch.cuda.is_available():
            pytest.skip("CUDA不可用，跳过此测试")

        print(f"\\n🧪 Observation Test: Stability of Final Config (3 runs)")
        
        throughputs = []
        dataloader = self._create_test_dataloader(batch_size, 59, num_batches=50) # Reduced batches for speed
        
        for run in range(3):
            print(f"🔍 Stability Run {run + 1}/3...")
            try:
                throughput, _ = await self._run_single_epoch(dataloader)
                throughputs.append(throughput)
                print(f"   - Run {run + 1} Throughput: {throughput:.0f} samples/s")
            except Exception as e:
                pytest.fail(f"Stability run {run + 1} failed: {e}")
        
        assert len(throughputs) == 3, "Must complete 3 stability runs"
        
        # 验证所有吞吐量都是有效的正数，这是一个基本的冒烟测试
        for throughput in throughputs:
            assert throughput > 0, f"Throughput should be a positive value, but got {throughput}"
        print(f"Stability check passed with throughputs: {throughputs}")
