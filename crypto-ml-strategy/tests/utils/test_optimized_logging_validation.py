"""
优化后日志频率验证测试
验证日志频率优化后的实际性能提升
"""

import time
import sys
from pathlib import Path
import torch
import torch.nn as nn
import numpy as np
import pytest
import asyncio
from typing import List

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).resolve().parent.parent.parent))

from src.training.unified_trainer import UnifiedTrainer, TrainerConfig, TrainingCallback

# --- Mocks and Helpers ---

class MockLoggerCallback(TrainingCallback):
    """一个用于捕获日志调用的回调。"""
    def __init__(self, interval=1):
        self.logs = []
        self.interval = interval

    async def on_step_end(self, trainer: "UnifiedTrainer", loss: torch.Tensor):
        if trainer.state.step % self.interval == 0:
            log_message = f"Step {trainer.state.step}, Loss: {loss.item()}"
            self.logs.append(log_message)

# --- Test Class ---

class TestOptimizedLoggingValidation:
    """优化后日志频率验证测试类"""
    
    def setup_method(self, method):
        """初始化测试"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"\\n🔧 测试设备: {self.device}")
    
    def _create_test_model(self, input_size: int = 59) -> nn.Module:
        """创建测试模型"""
        return nn.Sequential(nn.Linear(input_size, 3)).to(self.device)
    
    def _create_test_dataloader(self, batch_size: int, num_batches: int = 100):
        """创建测试数据加载器"""
        num_samples = batch_size * num_batches
        features = torch.randn(num_samples, 59)
        labels = torch.randint(0, 3, (num_samples,))
        dataset = torch.utils.data.TensorDataset(features, labels)
        
        is_cuda = self.device.type == 'cuda'
        return torch.utils.data.DataLoader(
            dataset, batch_size=batch_size, num_workers=2 if is_cuda else 0, pin_memory=is_cuda
        )

    async def _run_trainer(self, callbacks: List[TrainingCallback], dataloader):
        """运行训练器并返回性能"""
        model = self._create_test_model()
        config = TrainerConfig(epochs=1, use_amp=True, callbacks=callbacks)
        trainer = UnifiedTrainer(model=model, config=config, device=self.device)
        
        start_time = time.time()
        await trainer.train(dataloader, val_loader=None)
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
            
        total_time = time.time() - start_time
        throughput = len(dataloader.dataset) / total_time if total_time > 0 else 0
        return throughput

    @pytest.mark.asyncio
    async def test_optimized_trainer_performance(self, batch_size: int = 8192):
        """测试优化后的训练器性能（无日志回调）"""
        if not torch.cuda.is_available():
            pytest.skip("CUDA不可用，跳过此测试")
            
        print(f"\\n🧪 测试优化后的训练器性能 (无日志)")
        dataloader = self._create_test_dataloader(batch_size, num_batches=200)
        
        throughput = await self._run_trainer([], dataloader)
        
        print(f"📊 优化后训练性能: 吞吐量={throughput:.0f} samples/s")
        assert throughput > 0, "吞吐量必须为正数"

    @pytest.mark.asyncio
    async def test_different_log_intervals(self, batch_size: int = 4096):
        """测试不同日志间隔对性能的影响"""
        if not torch.cuda.is_available():
            pytest.skip("CUDA不可用，跳过此测试")
            
        print(f"\\n🧪 测试不同日志间隔的性能影响")
        
        log_intervals = [50, 500]
        results = {}
        
        for interval in log_intervals:
            print(f"\\n🔍 测试日志间隔: {interval}")
            dataloader = self._create_test_dataloader(batch_size, num_batches=150)
            mock_callback = MockLoggerCallback(interval=interval)
            
            throughput = await self._run_trainer([mock_callback], dataloader)
            
            results[interval] = {
                'throughput': throughput,
                'log_count': len(mock_callback.logs)
            }
            
            print(f"   - 吞吐量: {throughput:.0f} samples/s")
            print(f"   - 日志次数: {len(mock_callback.logs)}")

        # 断言
        assert results[50]['log_count'] > results[500]['log_count'], "日志较多时，调用次数应更多"
        print("   - 性能对比仅供观察，不作断言")
