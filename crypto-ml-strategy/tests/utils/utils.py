"""
测试辅助工具模块
Test Utilities Module
"""
import pandas as pd
import numpy as np
import torch

class CustomDictDataset(torch.utils.data.Dataset):
    """一个兼容DataLoader的字典式数据集"""
    def __init__(self, features, labels):
        self.features = torch.from_numpy(features)
        self.labels = torch.from_numpy(labels)

    def __len__(self):
        return len(self.features)

    def __getitem__(self, idx):
        return {'features': self.features[idx], 'labels': self.labels[idx]}

def create_ohlcv_dataframe(
    size: int = 200, 
    initial_price: float = 50000.0, 
    seed: int = 42
) -> pd.DataFrame:
    """
    创建一个模拟的OHLCV DataFrame，用于测试。

    Args:
        size: 数据点的数量。
        initial_price: 初始价格。
        seed: 随机种子。

    Returns:
        一个包含OHLCV数据的Pandas DataFrame。
    """
    np.random.seed(seed)
    dates = pd.date_range(start='2024-01-01', periods=size, freq='h')
    
    # 生成价格数据 (随机游走)
    price_changes = np.random.normal(0, 0.02, size)
    prices = [initial_price]
    for change in price_changes[1:]:
        new_price = prices[-1] * (1 + change)
        prices.append(max(new_price, 1000))
    
    # 创建OHLCV数据
    data = []
    for i, (date, price) in enumerate(zip(dates, prices)):
        volatility = np.random.uniform(0.005, 0.03)
        high = price * (1 + volatility)
        low = price * (1 - volatility)
        open_price = prices[i-1] if i > 0 else price
        close_price = price
        volume = np.random.uniform(100, 1000)
        
        data.append({
            'timestamp': date,
            'open': open_price,
            'high': high,
            'low': low,
            'close': close_price,
            'volume': volume
        })
    
    df = pd.DataFrame(data)
    df.set_index('timestamp', inplace=True)
    return df
