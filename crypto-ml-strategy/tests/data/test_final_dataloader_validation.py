"""
最终数据加载器验证测试
验证优化后的数据加载器配置在实际应用中的效果
"""

import time
import sys
from pathlib import Path
import torch
import torch.nn as nn
import numpy as np
from typing import Dict
import logging
from torch.utils.data import Dataset, DataLoader

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.core.application import CryptoMLApplication


class TestFinalDataLoaderValidation:
    """最终数据加载器验证测试类"""
    
    def setup_method(self, method):
        """初始化测试"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger = logging.getLogger(__name__)
        
        print(f"🔧 测试设备: {self.device}")
        if torch.cuda.is_available():
            print(f"🔧 GPU: {torch.cuda.get_device_name(0)}")
    
    def test_optimized_dataloader_in_application(self):
        """测试优化后的数据加载器在应用中的表现"""
        print(f"\n🧪 测试优化数据加载器在应用中的表现")
        
        try:
            # 创建应用实例
            app = CryptoMLApplication()
            
            # 检查数据加载器配置
            config = app.config
            dataloader_config = config.get('optimization', {}).get('dataloader', {})
            
            print(f"📊 当前数据加载器配置:")
            print(f"   - num_workers: {dataloader_config.get('num_workers', 'default')}")
            print(f"   - pin_memory: {dataloader_config.get('pin_memory', 'default')}")
            print(f"   - prefetch_factor: {dataloader_config.get('prefetch_factor', 'default')}")
            print(f"   - persistent_workers: {dataloader_config.get('persistent_workers', 'default')}")
            
            return True
            
        except Exception as e:
            print(f"❌ 应用测试失败: {e}")
            return False
    
    def benchmark_final_configuration(self):
        """基准测试最终配置"""
        print(f"\n🧪 基准测试最终优化配置")
        
        # 创建测试数据
        class SimpleDataset(Dataset):
            def __init__(self, num_samples, input_size):
                self.features = torch.randn(num_samples, input_size)
                self.labels = torch.randint(0, 3, (num_samples,))
            
            def __len__(self):
                return len(self.features)
            
            def __getitem__(self, idx):
                return self.features[idx], self.labels[idx]
        
        dataset = SimpleDataset(100000, 59)
        
        # 最终优化配置
        final_loader = DataLoader(
            dataset,
            batch_size=8192,
            shuffle=True,
            num_workers=16,
            pin_memory=True,
            persistent_workers=True,
            prefetch_factor=8,
            drop_last=True,
            multiprocessing_context='fork'
        )
        
        # 创建模型
        model = nn.Sequential(
            nn.Linear(59, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)
        ).to(self.device)
        
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        # 性能测试
        print(f"🔍 开始最终配置性能测试...")
        
        start_time = time.time()
        total_samples = 0
        total_loss = 0.0
        
        for i, (features, labels) in enumerate(final_loader):
            # 数据传输
            features = features.to(self.device, non_blocking=True)
            labels = labels.to(self.device, non_blocking=True)
            
            # 前向传播
            outputs = model(features)
            loss = criterion(outputs, labels)
            
            # 反向传播
            optimizer.zero_grad(set_to_none=True)
            loss.backward()
            optimizer.step()
            
            total_samples += features.size(0)
            total_loss += loss.item()
            
            if i >= 50:  # 测试50个批次
                break
        
        end_time = time.time()
        total_time = end_time - start_time
        throughput = total_samples / total_time
        avg_loss = total_loss / (i + 1)
        
        print(f"📊 最终配置性能结果:")
        print(f"   - 吞吐量: {throughput:.0f} samples/s")
        print(f"   - 总时间: {total_time:.3f}s")
        print(f"   - 平均损失: {avg_loss:.4f}")
        print(f"   - 处理批次: {i + 1}")
        print(f"   - 总样本数: {total_samples}")
        
        if torch.cuda.is_available():
            peak_memory = torch.cuda.max_memory_allocated() / (1024**3)
            print(f"   - 峰值GPU内存: {peak_memory:.2f}GB")
        
        return {
            'throughput': throughput,
            'total_time': total_time,
            'avg_loss': avg_loss,
            'total_samples': total_samples
        }
    
    def compare_with_baseline(self):
        """与基线配置对比"""
        print(f"\n🧪 与基线配置对比")
        
        # 基线配置 (来自之前的测试结果)
        baseline_throughput = 105457  # samples/s
        
        # 测试最终配置
        final_results = self.benchmark_final_configuration()
        final_throughput = final_results['throughput']
        
        # 计算改进
        improvement = (final_throughput - baseline_throughput) / baseline_throughput * 100
        
        print(f"\n📊 性能对比总结:")
        print(f"   - 基线吞吐量: {baseline_throughput:.0f} samples/s")
        print(f"   - 优化后吞吐量: {final_throughput:.0f} samples/s")
        print(f"   - 性能提升: {improvement:+.1f}%")
        
        if improvement > 50:
            print(f"✅ 数据加载器优化效果卓越!")
        elif improvement > 30:
            print(f"✅ 数据加载器优化效果显著!")
        elif improvement > 10:
            print(f"🟡 数据加载器优化有一定效果")
        else:
            print(f"❌ 数据加载器优化效果不明显")
        
        return improvement
    
    def test_memory_efficiency(self):
        """测试内存效率"""
        print(f"\n🧪 测试内存效率")
        
        if not torch.cuda.is_available():
            print("⚠️ CUDA不可用，跳过内存效率测试")
            return
        
        # 清理GPU内存
        torch.cuda.empty_cache()
        torch.cuda.reset_peak_memory_stats()
        
        initial_memory = torch.cuda.memory_allocated()
        
        # 运行一次基准测试
        results = self.benchmark_final_configuration()
        
        peak_memory = torch.cuda.max_memory_allocated()
        memory_used = (peak_memory - initial_memory) / (1024**3)  # GB
        
        print(f"📊 内存效率分析:")
        print(f"   - 初始GPU内存: {initial_memory / (1024**3):.2f}GB")
        print(f"   - 峰值GPU内存: {peak_memory / (1024**3):.2f}GB")
        print(f"   - 内存使用量: {memory_used:.2f}GB")
        print(f"   - 内存效率: {results['throughput'] / memory_used:.0f} samples/s/GB")
        
        return {
            'memory_used_gb': memory_used,
            'memory_efficiency': results['throughput'] / memory_used if memory_used > 0 else 0
        }
    
    def validate_configuration_stability(self):
        """验证配置稳定性"""
        print(f"\n🧪 验证配置稳定性")
        
        # 多次运行测试，检查稳定性
        throughputs = []
        
        for run in range(3):
            print(f"🔍 运行 {run + 1}/3...")
            
            try:
                results = self.benchmark_final_configuration()
                throughputs.append(results['throughput'])
                print(f"   - 吞吐量: {results['throughput']:.0f} samples/s")
                
            except Exception as e:
                print(f"   - ❌ 运行失败: {e}")
                throughputs.append(0)
        
        # 计算稳定性指标
        valid_throughputs = [t for t in throughputs if t > 0]
        
        if len(valid_throughputs) >= 2:
            avg_throughput = sum(valid_throughputs) / len(valid_throughputs)
            std_throughput = (sum((t - avg_throughput) ** 2 for t in valid_throughputs) / len(valid_throughputs)) ** 0.5
            cv = std_throughput / avg_throughput * 100  # 变异系数
            
            print(f"\n📊 稳定性分析:")
            print(f"   - 平均吞吐量: {avg_throughput:.0f} samples/s")
            print(f"   - 标准差: {std_throughput:.0f} samples/s")
            print(f"   - 变异系数: {cv:.1f}%")
            
            if cv < 5:
                print(f"✅ 配置非常稳定 (CV < 5%)")
            elif cv < 10:
                print(f"🟡 配置较为稳定 (CV < 10%)")
            else:
                print(f"❌ 配置不够稳定 (CV >= 10%)")
            
            return {
                'avg_throughput': avg_throughput,
                'std_throughput': std_throughput,
                'cv_percent': cv,
                'stable': cv < 10
            }
        else:
            print(f"❌ 测试运行失败过多，无法评估稳定性")
            return None


def main():
    """主测试函数"""
    print("🚀 开始最终数据加载器验证测试...")
    
    try:
        test_instance = TestFinalDataLoaderValidation()
        
        # 测试1: 应用中的配置验证
        print("\n" + "="*60)
        print("测试1: 应用中的数据加载器配置验证")
        test_instance.test_optimized_dataloader_in_application()
        
        # 测试2: 性能对比
        print("\n" + "="*60)
        print("测试2: 与基线配置性能对比")
        improvement = test_instance.compare_with_baseline()
        
        # 测试3: 内存效率
        print("\n" + "="*60)
        print("测试3: 内存效率测试")
        test_instance.test_memory_efficiency()
        
        # 测试4: 配置稳定性
        print("\n" + "="*60)
        print("测试4: 配置稳定性验证")
        stability_results = test_instance.validate_configuration_stability()
        
        # 最终总结
        print("\n" + "="*60)
        print("🎯 数据加载器优化最终总结")
        print(f"   - 性能提升: {improvement:+.1f}%")
        
        if stability_results:
            print(f"   - 配置稳定性: {'✅ 稳定' if stability_results['stable'] else '❌ 不稳定'}")
            print(f"   - 平均吞吐量: {stability_results['avg_throughput']:.0f} samples/s")
        
        if improvement > 30 and (not stability_results or stability_results['stable']):
            print(f"🏆 数据加载器优化成功！配置已达到生产就绪状态")
        elif improvement > 10:
            print(f"✅ 数据加载器优化有效，可以应用到生产环境")
        else:
            print(f"⚠️ 数据加载器优化效果有限，建议进一步调优")
        
        print("\n✅ 最终数据加载器验证测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
