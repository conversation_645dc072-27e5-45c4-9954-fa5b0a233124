"""
平衡数据加载器配置测试
测试12workers配置在完整训练循环中的表现
"""

import time
import sys
from pathlib import Path
import torch
import torch.nn as nn
import numpy as np
from typing import Dict
import logging
from torch.utils.data import Dataset, DataLoader

import pytest
# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))


class TestBalancedDataLoaderConfig:
    """平衡数据加载器配置测试类"""
    
    @pytest.fixture(scope="class")
    def setup_class(self):
        """Setup for the test class"""
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        logger = logging.getLogger(__name__)
        
        print(f"🔧 测试设备: {device}")
        if torch.cuda.is_available():
            print(f"🔧 GPU: {torch.cuda.get_device_name(0)}")
        return {"device": device, "logger": logger}
    
    def create_test_dataset(self, num_samples: int = 100000, input_size: int = 59):
        """创建测试数据集"""
        class SimpleDataset(Dataset):
            def __init__(self, num_samples, input_size):
                self.features = torch.randn(num_samples, input_size)
                self.labels = torch.randint(0, 3, (num_samples,))
            
            def __len__(self):
                return len(self.features)
            
            def __getitem__(self, idx):
                return self.features[idx], self.labels[idx]
        
        return SimpleDataset(num_samples, input_size)
    
    def create_test_model(self, device, input_size: int = 59) -> nn.Module:
        """创建测试模型"""
        model = nn.Sequential(
            nn.Linear(input_size, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)
        )
        return model.to(device)
    
    def test_configuration_comparison(self, setup_class, batch_size: int = 8192):
        """对比不同worker配置在完整训练循环中的表现"""
        device = setup_class["device"]
        
        print(f"\n🧪 对比不同worker配置在完整训练循环中的表现")
        
        dataset = self.create_test_dataset(100000)
        model = self.create_test_model(device)
        
        configs = [
            {'name': 'baseline_4w', 'num_workers': 4, 'pin_memory': False, 'prefetch_factor': 2},
            {'name': 'optimized_8w', 'num_workers': 8, 'pin_memory': True, 'prefetch_factor': 4},
            {'name': 'balanced_12w', 'num_workers': 12, 'pin_memory': True, 'prefetch_factor': 6},
            {'name': 'aggressive_16w', 'num_workers': 16, 'pin_memory': True, 'prefetch_factor': 8},
        ]
        
        results = {}
        
        for config in configs:
            print(f"\n🔍 测试配置: {config['name']}")
            
            try:
                # 创建数据加载器
                loader = DataLoader(
                    dataset,
                    batch_size=batch_size,
                    shuffle=True,
                    num_workers=config['num_workers'],
                    pin_memory=config['pin_memory'],
                    persistent_workers=True,
                    prefetch_factor=config['prefetch_factor'],
                    drop_last=True,
                    multiprocessing_context='fork'
                )
                
                # 重新创建模型以确保公平比较
                test_model = self.create_test_model(device)
                criterion = nn.CrossEntropyLoss()
                optimizer = torch.optim.AdamW(test_model.parameters(), lr=1e-3)
                
                # 完整训练循环测试
                start_time = time.time()
                total_samples = 0
                total_loss = 0.0
                
                for i, (features, labels) in enumerate(loader):
                    # 数据传输
                    features = features.to(device, non_blocking=config['pin_memory'])
                    labels = labels.to(device, non_blocking=config['pin_memory'])
                    
                    # 前向传播
                    outputs = test_model(features)
                    loss = criterion(outputs, labels)
                    
                    # 反向传播
                    optimizer.zero_grad(set_to_none=True)
                    loss.backward()
                    optimizer.step()
                    
                    total_samples += features.size(0)
                    total_loss += loss.item()
                    
                    if i >= 30:  # 测试30个批次
                        break
                
                end_time = time.time()
                total_time = end_time - start_time
                throughput = total_samples / total_time
                avg_loss = total_loss / (i + 1)
                
                results[config['name']] = {
                    'throughput': throughput,
                    'total_time': total_time,
                    'avg_loss': avg_loss,
                    'total_samples': total_samples,
                    'config': config
                }
                
                print(f"   - 吞吐量: {throughput:.0f} samples/s")
                print(f"   - 总时间: {total_time:.3f}s")
                print(f"   - 平均损失: {avg_loss:.4f}")
                
                # 清理GPU内存
                del test_model
                torch.cuda.empty_cache() if torch.cuda.is_available() else None
                
            except Exception as e:
                print(f"   - ❌ 配置失败: {e}")
                results[config['name']] = None
        
        return results
    
    def analyze_results(self, results: Dict):
        """分析测试结果"""
        print(f"\n📊 完整训练循环配置对比:")
        print(f"{'配置':<15} {'吞吐量(samples/s)':<20} {'总时间(s)':<12} {'相对基线提升':<15}")
        print("-" * 70)
        
        baseline_throughput = None
        best_config = None
        best_throughput = 0
        
        for config_name, result in results.items():
            if result is not None:
                throughput = result['throughput']
                total_time = result['total_time']
                
                if 'baseline' in config_name:
                    baseline_throughput = throughput
                    improvement_str = "0.0%"
                else:
                    if baseline_throughput:
                        improvement = (throughput - baseline_throughput) / baseline_throughput * 100
                        improvement_str = f"{improvement:+.1f}%"
                    else:
                        improvement_str = "N/A"
                
                print(f"{config_name:<15} {throughput:<20.0f} {total_time:<12.3f} {improvement_str:<15}")
                
                if throughput > best_throughput:
                    best_throughput = throughput
                    best_config = config_name
        
        print(f"\n🏆 最佳配置: {best_config}")
        
        if baseline_throughput and best_throughput > baseline_throughput:
            best_improvement = (best_throughput - baseline_throughput) / baseline_throughput * 100
            print(f"🎯 最佳提升: {best_improvement:+.1f}%")
            
            if best_improvement > 20:
                print(f"✅ 数据加载器优化效果显著!")
            elif best_improvement > 10:
                print(f"🟡 数据加载器优化有一定效果")
            else:
                print(f"❌ 数据加载器优化效果有限")
        
        return best_config, best_throughput
    
    @pytest.mark.parametrize("best_config_name", ["balanced_12w"])
    def test_stability_of_best_config(self, setup_class, best_config_name: str):
        """测试最佳配置的稳定性"""
        device = setup_class["device"]
        print(f"\n🧪 测试最佳配置稳定性: {best_config_name}")
        
        # 根据最佳配置名称确定参数
        if '12w' in best_config_name:
            num_workers = 12
            prefetch_factor = 6
        elif '8w' in best_config_name:
            num_workers = 8
            prefetch_factor = 4
        elif '16w' in best_config_name:
            num_workers = 16
            prefetch_factor = 8
        else:
            num_workers = 4
            prefetch_factor = 2
        
        dataset = self.create_test_dataset(100000)
        
        throughputs = []
        
        for run in range(5):
            print(f"🔍 稳定性测试 {run + 1}/5...")
            
            try:
                loader = DataLoader(
                    dataset,
                    batch_size=8192,
                    shuffle=True,
                    num_workers=num_workers,
                    pin_memory=True,
                    persistent_workers=True,
                    prefetch_factor=prefetch_factor,
                    drop_last=True,
                    multiprocessing_context='fork'
                )
                
                model = self.create_test_model()
                criterion = nn.CrossEntropyLoss()
                optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
                
                start_time = time.time()
                total_samples = 0
                
                for i, (features, labels) in enumerate(loader):
                    features = features.to(device, non_blocking=True)
                    labels = labels.to(device, non_blocking=True)
                    
                    outputs = model(features)
                    loss = criterion(outputs, labels)
                    
                    optimizer.zero_grad(set_to_none=True)
                    loss.backward()
                    optimizer.step()
                    
                    total_samples += features.size(0)
                    
                    if i >= 20:  # 较短的测试
                        break
                
                end_time = time.time()
                throughput = total_samples / (end_time - start_time)
                throughputs.append(throughput)
                
                print(f"   - 吞吐量: {throughput:.0f} samples/s")
                
                del model
                torch.cuda.empty_cache() if torch.cuda.is_available() else None
                
            except Exception as e:
                print(f"   - ❌ 运行失败: {e}")
                throughputs.append(0)
        
        # 分析稳定性
        valid_throughputs = [t for t in throughputs if t > 0]
        
        if len(valid_throughputs) >= 3:
            avg_throughput = sum(valid_throughputs) / len(valid_throughputs)
            std_throughput = (sum((t - avg_throughput) ** 2 for t in valid_throughputs) / len(valid_throughputs)) ** 0.5
            cv = std_throughput / avg_throughput * 100
            
            print(f"\n📊 稳定性分析:")
            print(f"   - 平均吞吐量: {avg_throughput:.0f} samples/s")
            print(f"   - 标准差: {std_throughput:.0f} samples/s")
            print(f"   - 变异系数: {cv:.1f}%")
            
            if cv < 5:
                print(f"✅ 配置非常稳定 (CV < 5%)")
                stability = "excellent"
            elif cv < 10:
                print(f"🟡 配置较为稳定 (CV < 10%)")
                stability = "good"
            else:
                print(f"❌ 配置不够稳定 (CV >= 10%)")
                stability = "poor"
            
            return {
                'avg_throughput': avg_throughput,
                'cv_percent': cv,
                'stability': stability
            }
        else:
            print(f"❌ 测试失败过多，无法评估稳定性")
            return None


def main():
    """主测试函数"""
    print("🚀 开始平衡数据加载器配置测试...")
    
    try:
        test_instance = TestBalancedDataLoaderConfig()
        
        # 测试1: 配置对比
        print("\n" + "="*60)
        print("测试1: 不同worker配置在完整训练循环中的对比")
        results = test_instance.test_configuration_comparison()
        
        # 测试2: 结果分析
        print("\n" + "="*60)
        print("测试2: 结果分析")
        best_config, best_throughput = test_instance.analyze_results(results)
        
        # 测试3: 稳定性测试
        if best_config:
            print("\n" + "="*60)
            print("测试3: 最佳配置稳定性测试")
            stability_results = test_instance.test_stability_of_best_config(best_config)
            
            # 最终推荐
            print("\n" + "="*60)
            print("🎯 最终推荐配置")
            print(f"   - 推荐配置: {best_config}")
            print(f"   - 预期吞吐量: {best_throughput:.0f} samples/s")
            
            if stability_results:
                print(f"   - 稳定性: {stability_results['stability']}")
                print(f"   - 变异系数: {stability_results['cv_percent']:.1f}%")
                
                if stability_results['stability'] in ['excellent', 'good']:
                    print(f"✅ 推荐在生产环境中使用此配置")
                else:
                    print(f"⚠️ 建议进一步调优以提高稳定性")
        
        print("\n✅ 平衡数据加载器配置测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
