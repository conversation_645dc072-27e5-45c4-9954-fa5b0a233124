"""
InfluxDB客户端测试类
"""

import pytest
import asyncio
import logging
import pandas as pd
from datetime import datetime
from unittest.mock import Mock, patch, AsyncMock
from src.data.influxdb_client import InfluxDBDataClient


class TestInfluxDBDataClient:
    """InfluxDB数据客户端测试类"""

    def setup_method(self):
        """测试前设置"""
        self.config = {
            'url': 'http://localhost:8086',
            'token': 'OEXRH5Ne5C20pVtpOkWRuwXhzTcuVJ2ePQphAdEbvN6ceKg7es-827-nPrg3riHFjnIQTc6vBXYguWk3s9YfkA==',
            'org': 'crypto-trading',
            'bucket': 'market-data',
            'timeout': 30,
            'verify_ssl': False
        }

        # 全局Mock，防止任何真实的网络调用
        self.mock_influx_patch = patch('src.data.influxdb_client.InfluxDBClient')
        self.mock_influx_constructor = self.mock_influx_patch.start()
        self.mock_influx_instance = self.mock_influx_constructor.return_value

        self.mock_test_conn_patch = patch.object(InfluxDBDataClient, '_test_connection', new_callable=AsyncMock)
        self.mock_test_conn = self.mock_test_conn_patch.start()
        self.mock_test_conn.return_value = None

        self.client = InfluxDBDataClient(self.config)

    def teardown_method(self):
        """测试后清理"""
        patch.stopall()

    def test_init_with_config(self):
        """测试使用配置初始化"""
        assert self.client.url == 'http://localhost:8086'
        assert self.client.token == 'OEXRH5Ne5C20pVtpOkWRuwXhzTcuVJ2ePQphAdEbvN6ceKg7es-827-nPrg3riHFjnIQTc6vBXYguWk3s9YfkA=='
        assert self.client.org == 'crypto-trading'
        assert self.client.bucket == 'market-data'

    def test_init_without_config(self):
        """测试不提供配置初始化时是否引发ValueError"""
        # 停止在setup_method中启动的全局patch，以便测试初始化
        self.mock_influx_patch.stop()
        self.mock_test_conn_patch.stop()
        
        with pytest.raises(ValueError, match="InfluxDB configuration is required."):
            _ = InfluxDBDataClient()
            
        # 重新启动patch，以免影响其他测试
        self.mock_influx_patch.start()
        self.mock_test_conn_patch.start()

    @pytest.mark.asyncio
    async def test_connect_success(self):
        """测试成功连接"""
        with patch('src.data.influxdb_client.InfluxDBClient') as mock_influx:
            mock_client = Mock()
            mock_influx.return_value = mock_client
            mock_client.query_api.return_value = Mock()
            
            # Mock _test_connection
            with patch.object(self.client, '_test_connection', new_callable=AsyncMock) as mock_test:
                mock_test.return_value = None
                
                result = await self.client.connect()
                assert result is True
                assert self.client.client is not None

    @pytest.mark.asyncio
    async def test_connect_timeout(self):
        """测试连接超时"""
        with patch('src.data.influxdb_client.InfluxDBClient') as mock_influx:
            mock_client = Mock()
            mock_influx.return_value = mock_client
            
            # Mock _test_connection to raise timeout
            with patch.object(self.client, '_test_connection', new_callable=AsyncMock) as mock_test:
                mock_test.side_effect = asyncio.TimeoutError()
                
                result = await self.client.connect()
                assert result is False
                assert self.client.client is None

    @pytest.mark.asyncio
    async def test_connect_failure(self):
        """测试连接失败"""
        # 模拟在_test_connection阶段失败
        self.mock_test_conn.side_effect = Exception("Connection test failed")
        
        result = await self.client.connect()
        assert result is False
        assert self.client.client is None

    def test_config_validation(self):
        """测试配置验证"""
        # 测试必需的配置项
        required_fields = ['url', 'token', 'org', 'bucket']
        for field in required_fields:
            assert hasattr(self.client, field)
            assert getattr(self.client, field) is not None

    def test_symbols_configuration(self):
        """测试交易对配置"""
        assert isinstance(self.client.symbols, list)
        assert len(self.client.symbols) > 0
        assert 'BTCUSDT' in self.client.symbols

    def test_timeframes_configuration(self):
        """测试时间框架配置"""
        assert isinstance(self.client.timeframes, list)
        assert len(self.client.timeframes) > 0
        assert '1h' in self.client.timeframes

    @pytest.mark.asyncio
    async def test_fetch_historical_data_no_connection(self):
        """测试未连接时获取历史数据会引发错误"""
        self.client.client = None  # 模拟未连接状态
        with pytest.raises(RuntimeError, match="InfluxDB连接不可用"):
            await self.client.fetch_historical_data('BTCUSDT', '1h', 100)


    def test_logger_initialization(self):
        """测试日志器初始化"""
        assert self.client.logger is not None
        assert isinstance(self.client.logger, logging.Logger)

    def test_available_flag(self):
        """测试可用性标志"""
        assert hasattr(self.client, 'available')
        assert isinstance(self.client.available, bool)

    @pytest.mark.asyncio
    async def test_fetch_historical_data_success(self):
        """测试成功获取和处理历史数据"""
        # 模拟一个已连接的客户端
        self.client.client = Mock()
        self.client.query_api = Mock()

        # 准备模拟的查询结果
        mock_df = pd.DataFrame({
            '_time': [pd.to_datetime('2023-01-01T01:00:00Z')],
            'result': ['_result'],
            'table': [0],
            'symbol': ['BTCUSDT'],
            'interval': ['1h'],
            'open': [100.0],
            'high': [110.0],
            'low': [90.0],
            'close': [105.0],
            'volume': [1000.0]
        })
        # 正确设置 mock 返回值
        self.client.query_api.query_data_frame.return_value = mock_df

        # 调用被测方法
        result_df = await self.client.fetch_historical_data(symbol='BTCUSDT', timeframe='1h')

        # 验证
        self.client.query_api.query_data_frame.assert_called_once()
        assert not result_df.empty
        assert len(result_df) == 1
        
        # 修复断言：'timestamp' 应该是索引，而不是列
        expected_columns = ['open', 'high', 'low', 'close', 'volume']
        assert all(col in result_df.columns for col in expected_columns)
        assert pd.api.types.is_datetime64_any_dtype(result_df.index)
        assert result_df.index.name == 'timestamp'
        assert result_df['open'].iloc[0] == 100.0



if __name__ == '__main__':
    pytest.main([__file__])
