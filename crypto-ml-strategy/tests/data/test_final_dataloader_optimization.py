"""
最终数据加载器优化验证
验证基于实测结果的最终优化配置
"""

import time
import sys
from pathlib import Path
import torch
import torch.nn as nn
import numpy as np
from typing import Dict
import logging
from torch.utils.data import Dataset, DataLoader

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))


class TestFinalDataLoaderOptimization:
    """最终数据加载器优化验证类"""
    
    def setup_method(self, method):
        """初始化测试"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger = logging.getLogger(__name__)
        
        print(f"🔧 测试设备: {self.device}")
        if torch.cuda.is_available():
            print(f"🔧 GPU: {torch.cuda.get_device_name(0)}")
            print(f"🔧 CPU核心数: 6 (物理)")
    
    def create_test_dataset(self, num_samples: int = 100000, input_size: int = 59):
        """创建测试数据集"""
        class SimpleDataset(Dataset):
            def __init__(self, num_samples, input_size):
                self.features = torch.randn(num_samples, input_size)
                self.labels = torch.randint(0, 3, (num_samples,))
            
            def __len__(self):
                return len(self.features)
            
            def __getitem__(self, idx):
                return self.features[idx], self.labels[idx]
        
        return SimpleDataset(num_samples, input_size)
    
    def create_test_model(self, input_size: int = 59) -> nn.Module:
        """创建测试模型"""
        model = nn.Sequential(
            nn.Linear(input_size, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)
        )
        return model.to(self.device)
    
    def test_final_optimized_config(self, batch_size: int = 8192):
        """测试最终优化配置"""
        print(f"\n🧪 测试最终优化配置 (6 workers + pin_memory + prefetch_factor=4)")
        
        dataset = self.create_test_dataset(100000)
        
        # 最终优化配置
        loader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=6,  # 匹配CPU核心数
            pin_memory=True,
            persistent_workers=True,
            prefetch_factor=4,
            drop_last=True,
            multiprocessing_context='fork'
        )
        
        model = self.create_test_model()
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        # 性能测试
        print(f"🔍 开始最终配置性能测试...")
        
        start_time = time.time()
        total_samples = 0
        total_loss = 0.0
        
        for i, (features, labels) in enumerate(loader):
            # 数据传输
            features = features.to(self.device, non_blocking=True)
            labels = labels.to(self.device, non_blocking=True)
            
            # 前向传播
            outputs = model(features)
            loss = criterion(outputs, labels)
            
            # 反向传播
            optimizer.zero_grad(set_to_none=True)
            loss.backward()
            optimizer.step()
            
            total_samples += features.size(0)
            total_loss += loss.item()
            
            if i >= 50:  # 测试50个批次
                break
        
        end_time = time.time()
        total_time = end_time - start_time
        throughput = total_samples / total_time
        avg_loss = total_loss / (i + 1)
        
        print(f"📊 最终优化配置性能:")
        print(f"   - 吞吐量: {throughput:.0f} samples/s")
        print(f"   - 总时间: {total_time:.3f}s")
        print(f"   - 平均损失: {avg_loss:.4f}")
        print(f"   - 处理批次: {i + 1}")
        print(f"   - 总样本数: {total_samples}")
        
        if torch.cuda.is_available():
            peak_memory = torch.cuda.max_memory_allocated() / (1024**3)
            print(f"   - 峰值GPU内存: {peak_memory:.2f}GB")
        
        return {
            'throughput': throughput,
            'total_time': total_time,
            'avg_loss': avg_loss,
            'total_samples': total_samples
        }
    
    def test_stability_multiple_runs(self):
        """测试多次运行的稳定性"""
        print(f"\n🧪 测试最终配置稳定性 (5次运行)")
        
        throughputs = []
        
        for run in range(5):
            print(f"🔍 稳定性测试 {run + 1}/5...")
            
            try:
                results = self.test_final_optimized_config()
                throughputs.append(results['throughput'])
                print(f"   - 运行 {run + 1} 吞吐量: {results['throughput']:.0f} samples/s")
                
                # 清理GPU内存
                torch.cuda.empty_cache() if torch.cuda.is_available() else None
                
            except Exception as e:
                print(f"   - ❌ 运行 {run + 1} 失败: {e}")
                throughputs.append(0)
        
        # 分析稳定性
        valid_throughputs = [t for t in throughputs if t > 0]
        
        if len(valid_throughputs) >= 3:
            avg_throughput = sum(valid_throughputs) / len(valid_throughputs)
            std_throughput = (sum((t - avg_throughput) ** 2 for t in valid_throughputs) / len(valid_throughputs)) ** 0.5
            cv = std_throughput / avg_throughput * 100
            
            print(f"\n📊 稳定性分析:")
            print(f"   - 平均吞吐量: {avg_throughput:.0f} samples/s")
            print(f"   - 标准差: {std_throughput:.0f} samples/s")
            print(f"   - 变异系数: {cv:.1f}%")
            print(f"   - 最小值: {min(valid_throughputs):.0f} samples/s")
            print(f"   - 最大值: {max(valid_throughputs):.0f} samples/s")
            
            if cv < 5:
                print(f"✅ 配置非常稳定 (CV < 5%)")
                stability = "excellent"
            elif cv < 10:
                print(f"🟡 配置较为稳定 (CV < 10%)")
                stability = "good"
            else:
                print(f"❌ 配置不够稳定 (CV >= 10%)")
                stability = "poor"
            
            return {
                'avg_throughput': avg_throughput,
                'cv_percent': cv,
                'stability': stability,
                'min_throughput': min(valid_throughputs),
                'max_throughput': max(valid_throughputs)
            }
        else:
            print(f"❌ 测试失败过多，无法评估稳定性")
            return None
    
    def compare_with_original_baseline(self, final_results):
        """与原始基线对比"""
        print(f"\n🧪 与原始基线配置对比")
        
        # 原始基线：4 workers, no pin_memory, prefetch_factor=2
        original_baseline_throughput = 105457  # 来自第一次测试
        
        final_throughput = final_results['avg_throughput']
        improvement = (final_throughput - original_baseline_throughput) / original_baseline_throughput * 100
        
        print(f"📊 性能对比:")
        print(f"   - 原始基线 (4w, no pin): {original_baseline_throughput:.0f} samples/s")
        print(f"   - 最终优化 (6w, pin): {final_throughput:.0f} samples/s")
        print(f"   - 性能提升: {improvement:+.1f}%")
        
        if improvement > 20:
            print(f"✅ 数据加载器优化效果显著!")
        elif improvement > 10:
            print(f"🟡 数据加载器优化有一定效果")
        elif improvement > 0:
            print(f"🟡 数据加载器优化有轻微提升")
        else:
            print(f"❌ 数据加载器优化未达到预期")
        
        return improvement
    
    def generate_final_recommendations(self, stability_results, improvement):
        """生成最终建议"""
        print(f"\n🎯 数据加载器优化最终建议")
        
        print(f"📋 推荐配置:")
        print(f"   - num_workers: 6 (匹配CPU核心数)")
        print(f"   - pin_memory: True")
        print(f"   - persistent_workers: True")
        print(f"   - prefetch_factor: 4")
        print(f"   - multiprocessing_context: 'fork'")
        
        print(f"\n📊 性能指标:")
        if stability_results:
            print(f"   - 平均吞吐量: {stability_results['avg_throughput']:.0f} samples/s")
            print(f"   - 稳定性: {stability_results['stability']}")
            print(f"   - 变异系数: {stability_results['cv_percent']:.1f}%")
        print(f"   - 相对基线提升: {improvement:+.1f}%")
        
        print(f"\n🔍 关键洞察:")
        print(f"   1. 数据加载器workers数量应该匹配CPU核心数")
        print(f"   2. 过多workers会导致上下文切换开销")
        print(f"   3. pin_memory对GPU训练有显著帮助")
        print(f"   4. prefetch_factor应该适中，避免内存压力")
        
        print(f"\n🚀 生产环境建议:")
        if stability_results and stability_results['stability'] in ['excellent', 'good'] and improvement > 0:
            print(f"   ✅ 推荐在生产环境中使用此配置")
            print(f"   ✅ 配置已经过充分测试和验证")
        else:
            print(f"   ⚠️ 建议在生产环境中进一步测试")
            print(f"   ⚠️ 可能需要根据具体硬件调整参数")


def main():
    """主测试函数"""
    print("🚀 开始最终数据加载器优化验证...")
    
    try:
        test_instance = TestFinalDataLoaderOptimization()
        
        # 测试1: 稳定性测试
        print("\n" + "="*60)
        print("测试1: 最终配置稳定性验证")
        stability_results = test_instance.test_stability_multiple_runs()
        
        # 测试2: 与基线对比
        if stability_results:
            print("\n" + "="*60)
            print("测试2: 与原始基线对比")
            improvement = test_instance.compare_with_original_baseline(stability_results)
            
            # 测试3: 生成最终建议
            print("\n" + "="*60)
            print("测试3: 生成最终建议")
            test_instance.generate_final_recommendations(stability_results, improvement)
        
        print("\n✅ 最终数据加载器优化验证完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
