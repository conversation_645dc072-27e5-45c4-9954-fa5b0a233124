"""
Kafka客户端测试类
"""

import pytest
import asyncio
import logging
from unittest.mock import Mock, patch, AsyncMock
from src.kafka.kafka_client import KafkaClient, KAFKA_AVAILABLE

# Skip all tests in this file if Kafka is not available
pytestmark = pytest.mark.skipif(not KAFKA_AVAILABLE, reason="kafka-python library not installed")


class TestKafkaClient:
    """Kafka客户端测试类"""

    def setup_method(self):
        """测试前设置"""
        self.config = {
            'bootstrap_servers': 'localhost:9092',
            'topics': {
                'market_data': 'test-market-data',
                'trade_signals': 'test-trade-signals'
            }
        }
        # We patch the KafkaProducer and KafkaConsumer directly to avoid any network calls
        self.mock_producer_patch = patch('src.kafka.kafka_client.KafkaProducer').start()
        self.mock_consumer_patch = patch('src.kafka.kafka_client.KafkaConsumer').start()
        self.mock_producer_instance = self.mock_producer_patch.return_value
        self.mock_consumer_instance = self.mock_consumer_patch.return_value
        
        self.client = KafkaClient(config=self.config)

    def teardown_method(self):
        """测试后清理"""
        patch.stopall()

    def test_init_with_config(self):
        """测试使用有效配置初始化"""
        assert self.client.bootstrap_servers == 'localhost:9092'
        assert self.client.topics['market_data'] == 'test-market-data'

    def test_init_without_config(self):
        """测试不提供配置初始化时是否引发ValueError"""
        with pytest.raises(ValueError, match="Kafka configuration is required."):
            _ = KafkaClient()

    def test_create_producer_success(self):
        """测试成功创建生产者"""
        producer = self.client._create_producer()
        assert producer is not None
        self.mock_producer_patch.assert_called_once()

    def test_create_consumer_success(self):
        """测试成功创建消费者"""
        consumer = self.client._create_consumer(topics=['test-topic'], group_id='test-group')
        assert consumer is not None
        self.mock_consumer_patch.assert_called_once()
        # We can also assert some of the arguments passed to the constructor
        args, kwargs = self.mock_consumer_patch.call_args
        assert 'test-topic' in args
        assert kwargs['bootstrap_servers'] == 'localhost:9092'
        assert kwargs['group_id'] == 'test-group'
        
    @pytest.mark.asyncio
    async def test_send_signal_success(self):
        """测试成功发送信号"""
        self.client.producer = self.mock_producer_instance
        mock_future = Mock()
        mock_record_metadata = Mock()
        mock_record_metadata.partition = 1
        mock_future.get.return_value = mock_record_metadata
        self.mock_producer_instance.send.return_value = mock_future

        success = await self.client.send_signal(signal_data={'action': 'BUY'}, symbol='BTCUSDT')

        assert success is True
        self.mock_producer_instance.send.assert_called_once()

    @pytest.mark.asyncio
    async def test_consume_market_data_success(self):
        """测试成功消费市场数据"""
        # Configure the mock consumer to return some messages
        mock_message = Mock()
        mock_message.value = {'symbol': 'BTCUSDT', 'price': 50000}
        self.mock_consumer_instance.poll.return_value = {('test-topic', 0): [mock_message]}
        self.mock_consumer_instance.topics.return_value = {'test-market-data'}
        
        # This client instance will now create a mocked consumer
        test_client = KafkaClient(config=self.config)
        
        callback_mock = Mock()

        # We run consumption for a very short time
        processed_count = await test_client.consume_market_data(callback=callback_mock, timeout_seconds=1)

        assert processed_count >= 1
        callback_mock.assert_called_with({'symbol': 'BTCUSDT', 'price': 50000})
        self.mock_consumer_patch.assert_called()

    def test_stop_method(self):
        """测试停止方法"""
        self.client.producer = self.mock_producer_instance
        # Simulate consumer is also created
        self.client.consumer = self.mock_consumer_instance
        self.client.is_running = True

        self.client.stop()

        assert self.client.is_running is False
        self.mock_producer_instance.close.assert_called_once()
        self.mock_consumer_instance.close.assert_called_once()


    def test_logger_initialization(self):
        """测试日志器初始化"""
        assert isinstance(self.client.logger, logging.Logger)
