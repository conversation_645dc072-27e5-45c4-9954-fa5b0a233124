import pytest

"""
多数据源集成测试
测试多数据源集成器和多时间框架信号融合功能
"""

import pytest
import asyncio
import time
import numpy as np
from unittest.mock import Mock, AsyncMock, patch
from src.data.multi_source_integrator import MultiSourceDataIntegrator, OrderBookData, TradeData
from src.signal.multi_timeframe_fusion import MultiTimeframeFusion, TimeframeSignal


class TestMultiSourceDataIntegrator:
    """多数据源集成器测试"""
    
    @pytest.fixture
    def integrator_config(self):
        """集成器配置"""
        return {
            'symbols': ['BTCUSDT', 'ETHUSDT'],
            'large_order_thresholds': {
                'BTCUSDT': 10.0,
                'ETHUSDT': 100.0
            }
        }
    
    @pytest.fixture
    def integrator(self, integrator_config):
        """多数据源集成器"""
        return MultiSourceDataIntegrator(integrator_config)
    
    def test_integrator_initialization(self, integrator):
        """测试集成器初始化"""
        assert integrator.config is not None
        assert 'BTCUSDT' in integrator.large_order_thresholds
        assert not integrator.is_running
    
    @pytest.mark.asyncio
    async def test_process_orderbook_data(self, integrator):
        """测试订单簿数据处理"""
        symbol = "BTCUSDT"
        mock_data = {
            'bids': [['50000.0', '1.0'], ['49999.0', '2.0']],
            'asks': [['50001.0', '1.5'], ['50002.0', '2.5']]
        }
        
        await integrator._process_orderbook_data(symbol, mock_data)
        
        # 检查数据是否被正确处理
        assert len(integrator.orderbook_cache[symbol]) == 1
        orderbook = integrator.orderbook_cache[symbol][0]
        
        assert orderbook.symbol == symbol
        assert len(orderbook.bids) == 2
        assert len(orderbook.asks) == 2
        assert orderbook.spread > 0
    
    @pytest.mark.asyncio
    async def test_process_trade_data(self, integrator):
        """测试交易数据处理"""
        symbol = "BTCUSDT"
        mock_data = {
            'p': '50000.0',  # price
            'q': '0.5',      # quantity
            'm': False,      # is buyer maker
            'T': int(time.time() * 1000),  # timestamp
            't': '12345'     # trade id
        }
        
        await integrator._process_trade_data(symbol, mock_data)
        
        # 检查数据是否被正确处理
        assert len(integrator.trade_cache[symbol]) == 1
        trade = integrator.trade_cache[symbol][0]
        
        assert trade.symbol == symbol
        assert trade.price == 50000.0
        assert trade.quantity == 0.5
        assert trade.side == 'sell'  # m=False表示卖方
    
    @pytest.mark.asyncio
    async def test_large_order_detection(self, integrator):
        """测试大单检测"""
        symbol = "BTCUSDT"
        
        # 小单
        small_order = {
            'p': '50000.0',
            'q': '0.0001',  # 价值5美元，小于阈值10
            'm': True,
            'T': int(time.time() * 1000),
            't': '12345'
        }
        
        await integrator._process_trade_data(symbol, small_order)
        trade = integrator.trade_cache[symbol][-1]
        assert not trade.is_large_order
        
        # 大单
        large_order = {
            'p': '50000.0',
            'q': '0.001',   # 价值50美元，大于阈值10
            'm': True,
            'T': int(time.time() * 1000),
            't': '12346'
        }
        
        await integrator._process_trade_data(symbol, large_order)
        trade = integrator.trade_cache[symbol][-1]
        assert trade.is_large_order
    
    @pytest.mark.asyncio
    async def test_volume_profile_analysis(self, integrator):
        """测试成交量分析"""
        symbol = "BTCUSDT"
        
        # 添加一些模拟交易数据
        current_time = time.time()
        for i in range(20):
            trade = TradeData(
                symbol=symbol,
                timestamp=current_time - i * 10,  # 过去200秒的交易
                price=50000 + np.random.normal(0, 100),
                quantity=np.random.uniform(0.1, 1.0),
                side='buy' if i % 2 == 0 else 'sell',
                is_large_order=False,
                trade_id=str(i)
            )
            integrator.trade_cache[symbol].append(trade)
        
        await integrator._analyze_volume_profile(symbol)
        
        # 检查成交量分析结果
        assert len(integrator.volume_profile_cache[symbol]) == 1
        profile = integrator.volume_profile_cache[symbol][0]
        
        assert profile.symbol == symbol
        assert profile.poc > 0
        assert profile.value_area_high > profile.value_area_low
    
    def test_get_integrated_features(self, integrator):
        """测试集成特征获取"""
        symbol = "BTCUSDT"
        
        # 添加模拟数据
        orderbook = OrderBookData(
            symbol=symbol,
            timestamp=time.time(),
            bids=[(50000, 1.0), (49999, 2.0)],
            asks=[(50001, 1.5), (50002, 2.5)],
            bid_depth=150000,
            ask_depth=175000,
            spread=0.00002,
            imbalance=-0.1
        )
        integrator.orderbook_cache[symbol].append(orderbook)
        
        trade = TradeData(
            symbol=symbol,
            timestamp=time.time(),
            price=50000,
            quantity=0.5,
            side='buy',
            is_large_order=True,
            trade_id='test'
        )
        integrator.trade_cache[symbol].append(trade)
        
        features = integrator.get_integrated_features(symbol)
        
        assert 'avg_spread' in features
        assert 'avg_imbalance' in features
        assert 'trade_count' in features
        assert 'large_order_count' in features
        assert features['large_order_count'] == 1
    
    def test_data_quality_report(self, integrator):
        """测试数据质量报告"""
        report = integrator.get_data_quality_report()
        
        assert 'metrics' in report
        assert 'cache_sizes' in report
        assert 'connection_status' in report


class TestMultiTimeframeFusion:
    """多时间框架信号融合测试"""
    
    @pytest.fixture
    def fusion_config(self):
        """融合器配置"""
        return {
            'timeframes': ['1m', '5m', '15m', '1h'],
            'timeframe_weights': {
                '1m': 0.1,
                '5m': 0.2,
                '15m': 0.3,
                '1h': 0.4
            },
            'consensus_threshold': 0.6,
            'min_timeframes': 2
        }
    
    @pytest.fixture
    def fusion_engine(self, fusion_config):
        """多时间框架融合器"""
        return MultiTimeframeFusion(fusion_config)
    
    def test_fusion_initialization(self, fusion_engine):
        """测试融合器初始化"""
        assert len(fusion_engine.timeframes) == 4
        assert fusion_engine.consensus_threshold == 0.6
        assert fusion_engine.min_timeframes == 2
    
    @pytest.mark.asyncio
    async def test_add_timeframe_signal(self, fusion_engine):
        """测试添加时间框架信号"""
        signal = TimeframeSignal(
            timeframe='1m',
            symbol='BTCUSDT',
            timestamp=time.time(),
            action='BUY',
            confidence=0.8,
            price=50000,
            features={'rsi': 70, 'macd': 0.5},
            strength=0.9
        )
        
        await fusion_engine.add_timeframe_signal(signal)
        
        # 检查信号是否被正确添加
        assert len(fusion_engine.signal_cache['BTCUSDT']['1m']) == 1
        cached_signal = fusion_engine.signal_cache['BTCUSDT']['1m'][0]
        assert cached_signal.action == 'BUY'
        assert cached_signal.confidence == 0.8
    
    def test_can_fuse_signals(self, fusion_engine):
        """测试信号融合条件检查"""
        symbol = 'BTCUSDT'
        current_time = time.time()
        
        # 添加足够的时间框架信号
        for tf in ['1m', '5m', '15m']:
            signal = TimeframeSignal(
                timeframe=tf,
                symbol=symbol,
                timestamp=current_time,
                action='BUY',
                confidence=0.7,
                price=50000,
                features={},
                strength=0.8
            )
            fusion_engine.signal_cache[symbol][tf].append(signal)
        
        # 应该可以融合（3个时间框架 >= min_timeframes=2）
        assert fusion_engine._can_fuse_signals(symbol) is True
        
        # 清空缓存
        fusion_engine.signal_cache[symbol].clear()
        
        # 只有一个时间框架，不应该融合
        signal = TimeframeSignal(
            timeframe='1m',
            symbol=symbol,
            timestamp=current_time,
            action='BUY',
            confidence=0.7,
            price=50000,
            features={},
            strength=0.8
        )
        fusion_engine.signal_cache[symbol]['1m'].append(signal)
        
        assert fusion_engine._can_fuse_signals(symbol) is False
    
    def test_weighted_fusion(self, fusion_engine):
        """测试基于权重的信号融合"""
        timeframe_signals = {
            '1m': TimeframeSignal('1m', 'BTCUSDT', time.time(), 'BUY', 0.8, 50000, {}, 0.9),
            '5m': TimeframeSignal('5m', 'BTCUSDT', time.time(), 'BUY', 0.7, 50000, {}, 0.8),
            '15m': TimeframeSignal('15m', 'BTCUSDT', time.time(), 'SELL', 0.6, 50000, {}, 0.7),
            '1h': TimeframeSignal('1h', 'BTCUSDT', time.time(), 'BUY', 0.9, 50000, {}, 0.95)
        }
        
        result = fusion_engine._weighted_fusion(timeframe_signals)
        
        assert 'action' in result
        assert 'confidence' in result
        assert 'action_scores' in result
        
        # 由于BUY信号权重更高，应该选择BUY
        assert result['action'] == 'BUY'
        assert result['confidence'] > 0
    
    def test_calculate_consensus(self, fusion_engine):
        """测试一致性计算"""
        # 高一致性信号
        high_consensus_signals = {
            '1m': TimeframeSignal('1m', 'BTCUSDT', time.time(), 'BUY', 0.8, 50000, {}, 0.9),
            '5m': TimeframeSignal('5m', 'BTCUSDT', time.time(), 'BUY', 0.7, 50000, {}, 0.8),
            '15m': TimeframeSignal('15m', 'BTCUSDT', time.time(), 'BUY', 0.75, 50000, {}, 0.85)
        }
        
        high_consensus = fusion_engine._calculate_consensus(high_consensus_signals)
        
        # 低一致性信号
        low_consensus_signals = {
            '1m': TimeframeSignal('1m', 'BTCUSDT', time.time(), 'BUY', 0.8, 50000, {}, 0.9),
            '5m': TimeframeSignal('5m', 'BTCUSDT', time.time(), 'SELL', 0.3, 50000, {}, 0.4),
            '15m': TimeframeSignal('15m', 'BTCUSDT', time.time(), 'HOLD', 0.9, 50000, {}, 0.2)
        }
        
        low_consensus = fusion_engine._calculate_consensus(low_consensus_signals)
        
        # 高一致性应该大于低一致性
        assert high_consensus > low_consensus
        assert 0 <= high_consensus <= 1
        assert 0 <= low_consensus <= 1
    
    def test_calculate_risk_score(self, fusion_engine):
        """测试风险分数计算"""
        timeframe_signals = {
            '1m': TimeframeSignal('1m', 'BTCUSDT', time.time(), 'BUY', 0.8, 50000, {}, 0.9),
            '5m': TimeframeSignal('5m', 'BTCUSDT', time.time(), 'BUY', 0.7, 50000, {}, 0.8)
        }
        
        risk_score = fusion_engine._calculate_risk_score(timeframe_signals)
        
        assert 0 <= risk_score <= 1
    
    @pytest.mark.asyncio
    async def test_fuse_signals_integration(self, fusion_engine):
        """测试完整的信号融合流程"""
        symbol = 'BTCUSDT'
        current_time = time.time()
        
        # 添加多个时间框架信号
        signals = [
            TimeframeSignal('1m', symbol, current_time, 'BUY', 0.8, 50000, {'rsi': 70}, 0.9),
            TimeframeSignal('5m', symbol, current_time, 'BUY', 0.7, 50000, {'rsi': 65}, 0.8),
            TimeframeSignal('15m', symbol, current_time, 'BUY', 0.75, 50000, {'rsi': 68}, 0.85)
        ]
        
        for signal in signals:
            fusion_engine.signal_cache[symbol][signal.timeframe].append(signal)
        
        # 执行融合
        fused_signal = await fusion_engine._fuse_signals(symbol)
        
        assert fused_signal is not None
        assert fused_signal.symbol == symbol
        assert fused_signal.final_action in ['BUY', 'SELL', 'HOLD']
        assert 0 <= fused_signal.final_confidence <= 1
        assert 0 <= fused_signal.consensus_score <= 1
        assert 0 <= fused_signal.risk_score <= 1
        assert len(fused_signal.timeframe_signals) == 3
    
    def test_get_fusion_stats(self, fusion_engine):
        """测试融合统计获取"""
        # 模拟一些统计数据
        fusion_engine.fusion_stats['total_signals'] = 10
        fusion_engine.fusion_stats['consensus_signals'] = 7
        
        stats = fusion_engine.get_fusion_stats()
        
        assert 'total_signals' in stats
        assert 'consensus_signals' in stats
        assert 'consensus_rate' in stats
        assert stats['consensus_rate'] == 0.7


class TestIntegrationWorkflow:
    """集成工作流测试"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self):
        """端到端工作流测试"""
        # 创建集成器和融合器
        integrator_config = {
            'symbols': ['BTCUSDT'],
            'large_order_thresholds': {'BTCUSDT': 10.0}
        }
        integrator = MultiSourceDataIntegrator(integrator_config)
        
        fusion_config = {
            'timeframes': ['1m', '5m'],
            'min_timeframes': 2
        }
        fusion_engine = MultiTimeframeFusion(fusion_config)
        
        # 模拟数据流
        symbol = 'BTCUSDT'
        
        # 1. 添加订单簿数据
        orderbook_data = {
            'bids': [['50000.0', '1.0']],
            'asks': [['50001.0', '1.0']]
        }
        await integrator._process_orderbook_data(symbol, orderbook_data)
        
        # 2. 添加交易数据
        trade_data = {
            'p': '50000.0',
            'q': '0.5',
            'm': True,
            'T': int(time.time() * 1000),
            't': '12345'
        }
        await integrator._process_trade_data(symbol, trade_data)
        
        # 3. 获取集成特征
        features = integrator.get_integrated_features(symbol)
        assert len(features) > 0
        
        # 4. 创建时间框架信号
        signals = [
            TimeframeSignal('1m', symbol, time.time(), 'BUY', 0.8, 50000, features, 0.9),
            TimeframeSignal('5m', symbol, time.time(), 'BUY', 0.7, 50000, features, 0.8)
        ]
        
        # 5. 添加到融合器
        for signal in signals:
            await fusion_engine.add_timeframe_signal(signal)
        
        # 6. 验证融合结果
        assert fusion_engine.fusion_stats['total_signals'] > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
