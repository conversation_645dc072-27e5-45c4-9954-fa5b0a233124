import pytest

"""
集成测试 - 一个模块一个模块测试和集成
"""


import pytest
import torch
import numpy as np
import asyncio
import tempfile
import os
import unittest
import time
from unittest.mock import Mock, patch, MagicMock, AsyncMock
from pathlib import Path
import yaml

from src.core.application import CryptoMLApplication as CryptoMLStrategy
from src.data.unified_pipeline.data_manager import DataManager
from src.models.unified_fusion_model import UnifiedSignalFusionModel
from src.training.unified_trainer import UnifiedTrainer, TrainerConfig
from src.monitoring.gpu_monitor import GPUMonitor
from tests.utils.utils import CustomDictDataset
from src.utils.config import ConfigManager


@pytest.fixture
def temp_config(tmp_path):
    """Creates a temporary config file for testing."""
    config_dir = tmp_path / "config"
    config_dir.mkdir()
    config_file = config_dir / "config.yaml"
    
    config_data = {
        'storage': {
            'directories': {
                'raw': str(tmp_path / "raw"),
                'processed': str(tmp_path / "processed"),
                'models': str(tmp_path / "models")
            }
        },
        'influxdb': {
            'url': 'http://mock-influxdb:8086',
            'token': 'mock-token',
            'org': 'mock-org',
            'bucket': 'mock-bucket'
        },
        'data': {
            'symbols': ['BTCUSDT'],
            'timeframes': ['1h'],
            'feature_engineering': {
                'technical_indicators': ['sma', 'rsi'],
                'window_sizes': [14, 28]
            }
        }
    }
    
    with open(config_file, 'w') as f:
        yaml.dump(config_data, f)
        
    (tmp_path / "raw").mkdir(exist_ok=True)
    (tmp_path / "processed").mkdir(exist_ok=True)
    (tmp_path / "models").mkdir(exist_ok=True)

    return config_dir


class TestModuleIntegration:
    """模块集成测试类"""
    
    @pytest.fixture
    def temp_data_dir(self):
        """创建临时数据目录"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create subdirectories to mimic real structure
            (Path(temp_dir) / 'raw').mkdir()
            (Path(temp_dir) / 'processed').mkdir()
            (Path(temp_dir) / 'models').mkdir()
            yield temp_dir
    
    @pytest.fixture
    def sample_data(self):
        """创建样本数据"""
        np.random.seed(42)
        num_samples = 1000
        feature_dim = 25
        
        features = np.random.randn(num_samples, feature_dim).astype(np.float32)
        labels = np.random.randint(0, 3, num_samples).astype(np.int64)
        
        return features, labels
    
    @pytest.mark.asyncio
    async def test_datamanager_module_integration(self, temp_config):
        """测试 DataManager 模块的端到端集成 (重构)"""
        import pandas as pd

        # 1. 使用夹具加载配置
        config_manager = ConfigManager(config_dir=temp_config)
        project_root = temp_config.parent

        raw_dir = Path(config_manager.get('storage.directories.raw'))

        # 2. 准备假的原始数据
        symbol = "BTCUSDT"
        raw_data = {
            "timestamp": pd.to_datetime(pd.date_range(start="2023-01-01", periods=200, freq='h')),
            "open": np.random.rand(200) * 100 + 50000,
            "high": np.random.rand(200) * 100 + 50100,
            "low": np.random.rand(200) * 100 + 49900,
            "close": np.random.rand(200) * 100 + 50000,
            "volume": np.random.rand(200) * 10 + 100,
        }
        raw_df = pd.DataFrame(raw_data)
        raw_df.set_index('timestamp', inplace=True)
        # We don't need to save to CSV for this test, as we mock the data sources.

        # 3. 模拟外部依赖
        mock_influx_client = MagicMock()
        mock_influx_client.connect = AsyncMock(return_value=True)
        mock_influx_client.fetch_historical_data = AsyncMock(return_value=pd.DataFrame()) # Return empty to fall back
        mock_influx_client.close = AsyncMock()

        with patch('src.data.unified_pipeline.data_manager.InfluxDBDataClient', return_value=mock_influx_client), \
             patch('src.data.unified_pipeline.data_manager.JavaDataService') as mock_java_service, \
             patch('torch.cuda.is_available', return_value=False):

            mock_java_instance = mock_java_service.return_value
            mock_java_instance.is_available = AsyncMock(return_value=True)
            # IMPORTANT: Ensure the mocked service returns the dataframe with the correct DatetimeIndex
            mock_java_instance.fetch_historical_data = AsyncMock(return_value=raw_df)

            # 4. 运行 DataManager
            data_manager = DataManager(config=config_manager.get('data'), project_root=project_root)

            train_loader, _, _ = await data_manager.run_full_pipeline(
                symbols=[symbol],
                dataloader_kwargs={'batch_size': 32, 'num_workers': 0}
            )

            # 5. 验证
            assert train_loader is not None, "DataManager should produce a valid train_loader"

    
    def test_model_module(self, sample_data):
        """测试模型模块"""
        features, labels = sample_data
        
        # 创建模型
        model_config = {
            'feature_dim': features.shape[1],
            'hidden_dims': [128, 64],
            'num_layers': 2,
            'dropout': 0.1,
            'output_dim': 3,
            'use_transformer': False,
            'use_lstm': False,
            'device': 'cpu',
        }
        
        model = UnifiedSignalFusionModel(**model_config)
        
        # 测试模型前向传播
        features_tensor = torch.from_numpy(features[:32])
        
        with torch.no_grad():
            output = model(features_tensor)
        
        # 验证输出
        assert isinstance(output, dict), "模型输出应该是字典"
        assert 'signal' in output, "输出应该包含signal"
        assert output['signal'].shape[0] == 32, "批次大小应该正确"
        assert output['signal'].shape[1] == 3, "类别数应该正确"
        
        print(f"✅ 模型模块测试通过: {output['signal'].shape}")
    
    
    def test_gpu_monitor_module(self):
        """测试GPU监控模块，包括在强制CPU模式下的行为"""
        with patch.dict(os.environ, {"FORCE_CPU_TESTING": "true"}):
            monitor = GPUMonitor()
            assert monitor.get_status() is None, "在强制CPU模式下，监控器应返回None"

        if torch.cuda.is_available():
            with patch.dict(os.environ, {"FORCE_CPU_TESTING": "false"}):
                monitor = GPUMonitor()
                try:
                    device = torch.device('cuda')
                    data = torch.randn(500, 500, device=device)
                    time.sleep(0.5)
                    
                    metrics = monitor.get_status()
                    # 修复：由于GPUMonitor会返回空字典而不是None，所以调整断言
                    assert metrics is not None, "在GPU模式下，监控器应该返回指标"
                    assert 'gpu_utilization_percent' in metrics
                finally:
                    monitor.shutdown()
        else:
            pytest.skip("CUDA不可用，跳过GPU模式下的监控测试")

    
    @pytest.mark.asyncio
    async def test_end_to_end_integration(self, temp_data_dir, sample_data):
        """端到端集成测试"""
        features, labels = sample_data

        test_config = {
            'model': {
                'feature_dim': features.shape[1],
                'hidden_dims': [64, 32],
                'num_heads': 4,
                'num_layers': 2,
                'dropout': 0.1,
                'output_dim': 3,
                'use_transformer': False,
                'use_lstm': False,
            },
            'training': {
                'use_amp': False,
                'use_compile': False,
                'learning_rate': 0.001,
                'epochs': 1,
                'batch_size': 64
            },
            'deepseek': {'enabled': False},
            'monitoring': {'enable_live_plot': False},
            'database': {'mysql': {'enabled': False}, 'redis': {'enabled': False}},
            'data': {
                'influxdb': {
                    'url': 'http://mock-influxdb:8086',
                    'token': 'mock-token',
                    'org': 'mock-org',
                    'bucket': 'mock-bucket'
                }
            }
        }

        app = CryptoMLStrategy(
            config=test_config,
            device=torch.device('cpu'),
            project_root=Path(temp_data_dir)
        )
        await app.initialize()
        assert app.trainer is not None

        from torch.utils.data import DataLoader
        train_dataset = CustomDictDataset(features, labels)
        train_loader = DataLoader(train_dataset, batch_size=64)

        await app.trainer.train(train_loader, val_loader=None)

        assert app.trainer.state.metrics is not None, "训练后应该有度量指标"
        assert 'train_loss' in app.trainer.state.metrics, "度量指标应包含 'train_loss'"
        final_loss = app.trainer.state.metrics['train_loss']
        assert final_loss > 0, f"训练损失应为正数, 但得到 {final_loss}"
        print(f"✅ 端到端集成测试通过: loss={final_loss:.4f}")
    
    def test_performance_integration(self, sample_data):
        """性能集成测试"""
        if not torch.cuda.is_available():
            pytest.skip("Skipping GPU-specific test: CUDA not available.")
            
        features, labels = sample_data
        device = torch.device('cuda')
        
        large_batch_size = 512
        large_features = np.tile(features, (2, 1))[:large_batch_size]
        
        model_config = {
            'feature_dim': features.shape[1],
            'hidden_dims': [128, 64],
            'num_layers': 2,
            'dropout': 0.1,
            'output_dim': 3,
            'use_transformer': False,
            'use_lstm': False,
            'device': 'cuda',
        }
        model = UnifiedSignalFusionModel(**model_config).to(device)
        
        features_tensor = torch.from_numpy(large_features).to(device)
        
        with torch.no_grad():
            for _ in range(5):
                _ = model(features_tensor)
        
        torch.cuda.synchronize()
        
        import time
        start_time = time.time()
        with torch.no_grad():
            for _ in range(10):
                _ = model(features_tensor)
        torch.cuda.synchronize()
        end_time = time.time()
        
        avg_time = (end_time - start_time) / 10
        throughput = large_batch_size / avg_time
        
        print(f"✅ 性能集成测试通过: {throughput:.0f} samples/s")
        
        assert throughput > 100, "吞吐量应该大于100 samples/s"
        assert avg_time < 1.0, "平均推理时间应该小于1秒"
