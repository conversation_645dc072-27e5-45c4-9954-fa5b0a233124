import pytest

#!/usr/bin/env python3
"""
系统集成测试 - 验证Java端修复和Python端功能
测试真实数据流和特征工程的完整流程

@author: Crypto Trading System
@since: 1.0.0
"""

import sys
import os
import logging
import pandas as pd
import numpy as np
from pathlib import Path
import time
import asyncio
from typing import Dict, List, Any
import pytest
from unittest.mock import patch

# 导入我们的模块
from src.data.unified_pipeline.feature_engine import FeatureEngine
from src.data.cross_symbol_analyzer import create_cross_symbol_analyzer
from src.data.numba_accelerated_features import NumbaAcceleratedFeatures
from tests.utils.utils import create_ohlcv_dataframe

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TestSystemIntegration:
    """系统集成测试类"""
    
    @pytest.fixture(scope="class")
    def setup_components(self):
        """测试组件初始化，使用class作用域以复用"""
        logger = logging.getLogger(self.__class__.__name__)
        test_symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        
        # 创建测试数据
        sample_data = create_ohlcv_dataframe(size=200)
        
        # 初始化组件
        feature_engineer = FeatureEngine(config={})
        cross_symbol_analyzer = create_cross_symbol_analyzer(test_symbols)
        
        return {
            "logger": logger,
            "symbols": test_symbols,
            "sample_data": sample_data,
            "feature_engineer": feature_engineer,
            "cross_symbol_analyzer": cross_symbol_analyzer,
        }
    
    def test_feature_engineering_basic(self, setup_components):
        """测试基础特征工程"""
        logger = setup_components["logger"]
        feature_engineer = setup_components["feature_engineer"]
        sample_data = setup_components["sample_data"]
        
        logger.info("🧪 测试基础特征工程...")
        
        # 执行特征工程
        features = feature_engineer.create_all_features(sample_data)
        
        # 验证结果
        assert isinstance(features, pd.DataFrame)
        assert features.shape[1] > 20, "应生成超过20个特征"
        assert features.shape[0] == sample_data.shape[0]
        
        # 检查特征质量
        inf_count = np.isinf(features.select_dtypes(include=[np.number])).sum().sum()
        assert inf_count == 0, "特征中不应包含无穷大值"
        
        nan_ratio = features.isnull().sum().sum() / (features.shape[0] * features.shape[1])
        assert nan_ratio < 0.1, f"NaN值比例过高: {nan_ratio:.2%}"
        
        logger.info(f"✅ 基础特征工程测试通过: {features.shape[1]}个特征")
    
    def test_advanced_indicators(self, setup_components):
        """测试高级技术指标"""
        logger = setup_components["logger"]
        feature_engineer = setup_components["feature_engineer"]
        sample_data = setup_components["sample_data"]

        logger.info("🧪 测试高级技术指标...")
        
        features = feature_engineer.create_all_features(sample_data)
        
        # 检查真实存在的指标
        assert 'rsi' in features.columns, "应包含RSI指标"
        assert 'macd' in features.columns, "应包含MACD指标"
        assert 'bb_upper' in features.columns, "应包含布林带指标"
        
        logger.info(f"✅ 高级技术指标测试通过")
    
    def test_cross_symbol_analysis(self, setup_components):
        """测试币种间分析"""
        logger = setup_components["logger"]
        test_symbols = setup_components["symbols"]
        sample_data = setup_components["sample_data"]
        cross_symbol_analyzer = setup_components["cross_symbol_analyzer"]

        logger.info("🧪 测试币种间分析...")
        
        # 创建多币种数据
        symbol_data = {}
        # 为测试实例化一个特征引擎
        numba_engine = NumbaAcceleratedFeatures()

        for symbol in test_symbols:
            # 为每个币种创建略有不同的数据
            data = sample_data.copy()
            # 添加一些随机变化
            noise = np.random.normal(0, 0.001, len(data))
            for col in ['open', 'high', 'low', 'close']:
                data[col] = data[col] * (1 + noise)
            
            # 修复：使用正确的方法名并传递NumPy数组
            kline_data_np = {
                'open': data['open'].values,
                'high': data['high'].values,
                'low': data['low'].values,
                'close': data['close'].values,
                'volume': data['volume'].values if 'volume' in data.columns else None
            }
            indicator_df = numba_engine.create_technical_indicators_np(kline_data_np, index=data.index)
            data = pd.concat([data, indicator_df], axis=1)
            symbol_data[symbol] = data
        
        # 测试相关性分析
        correlation_matrix = cross_symbol_analyzer.analyze_cross_correlations(
            symbol_data, ['close_price', 'volume', 'rsi']
        )
        
        assert isinstance(correlation_matrix, pd.DataFrame)
        assert correlation_matrix.shape[0] == len(test_symbols)
        assert correlation_matrix.shape[1] == len(test_symbols)
        
        # 测试领先滞后关系
        lead_lag_results = cross_symbol_analyzer.analyze_lead_lag_relationships(
            symbol_data, max_lag=5
        )
        
        assert isinstance(lead_lag_results, list)
        
        # 测试市场情绪传导
        sentiment_results = cross_symbol_analyzer.analyze_market_sentiment_contagion(
            symbol_data, ['rsi', 'macd']
        )
        
        assert 'contagion_strength' in sentiment_results
        assert 'network_metrics' in sentiment_results
        
        logger.info(f"✅ 币种间分析测试通过: 相关性矩阵{correlation_matrix.shape}, {len(lead_lag_results)}个领先滞后关系")
    
    def test_performance_benchmark(self, setup_components):
        """测试性能基准"""
        logger = setup_components["logger"]
        feature_engineer = setup_components["feature_engineer"]

        logger.info("🧪 测试性能基准...")
        
        # 测试大数据量处理
        large_data = create_ohlcv_dataframe(size=1000)
        
        # 测试特征工程性能
        start_time = time.time()
        features = feature_engineer.create_all_features(large_data)
        feature_time = time.time() - start_time
        
        # 性能要求
        assert feature_time < 30, f"特征工程耗时过长: {feature_time:.2f}秒"
        
        throughput = len(large_data) / (feature_time + 0.001)
        logger.info(f"✅ 性能基准测试通过: 特征工程{feature_time:.2f}s, 吞吐量{throughput:.1f}行/秒")
    
