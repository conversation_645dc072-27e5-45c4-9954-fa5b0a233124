import pytest

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import numpy as np

from src.app import AppOrchestrator

@pytest.fixture
def orchestrator():
    """Fixture to create an AppOrchestrator instance with fresh, isolated mocks."""
    # Create fresh, isolated mocks for each test run to prevent state leakage.
    mock_app_instance = AsyncMock()
    mock_app_instance.risk_manager = AsyncMock()
    mock_app_instance.model = MagicMock()

    with patch('src.app.CryptoMLApplication', return_value=mock_app_instance):

        orch = AppOrchestrator()
        orch.app = mock_app_instance 

        # Mock the model_manager to avoid AttributeError
        mock_model_manager = MagicMock()
        mock_model_manager.get_model.return_value = orch.app.model
        orch.model_manager = mock_model_manager
        
        return orch


@pytest.mark.asyncio
async def test_generate_trading_signal_success_path(orchestrator):
    """
    Test the successful path of generating a trading signal,
    including prediction, filtering, and risk calculations.
    """
    # 1. Setup Mocks
    symbol = "BTCUSDT"
    mock_app = orchestrator.app
    mock_risk_manager = orchestrator.app.risk_manager

    # Mock model prediction
    mock_app.run_prediction.return_value = {
        'probabilities': [np.array([0.1, 0.1, 0.8])],
        'predictions': [2]  # Strong BUY signal
    }
    # This mock is also needed for the final signal assembly
    orchestrator.app._get_latest_market_data = AsyncMock(return_value={'close': 65000.0})


    # Mock risk framework to approve the signal
    from src.risk import TradeDecision
    mock_risk_manager.process_trade_signal.return_value = TradeDecision(
        is_approved=True,
        position_size=10000.0,
        stop_loss_price=64000.0,
        take_profit_price=68000.0,
        reason="Test approval",
        alerts=[]
    )

    # 2. Execute the method within a context that mocks the feature generation
    with patch.object(orchestrator, '_get_latest_features_for_symbol', new_callable=AsyncMock) as mock_get_features:
        mock_get_features.return_value = np.random.rand(1, 59)  # Return a valid feature array
        final_signal = await orchestrator._generate_trading_signal(symbol)

    # 3. Assertions
    assert final_signal is not None
    assert final_signal['symbol'] == symbol
    assert final_signal['action'] == 'BUY'
    assert final_signal['confidence'] == pytest.approx(0.8)
    assert final_signal['position_size'] == 10000.0
    assert final_signal['stop_loss'] == 64000.0
    assert final_signal['take_profit'] == 68000.0
    assert final_signal['source'] == 'UnifiedSignalFusionModel'
    assert final_signal['risk_assessment']['reason'] == "Test approval"

    # Assert mocks were called
    mock_get_features.assert_awaited_once_with(symbol)
    mock_app.run_prediction.assert_awaited_once()
    mock_risk_manager.process_trade_signal.assert_awaited_once()


@pytest.mark.asyncio
async def test_generate_trading_signal_filtered_by_risk(orchestrator):
    """
    Test the scenario where the signal is generated but filtered out by the risk manager.
    """
    # 1. Setup Mocks
    symbol = "ETHUSDT"
    mock_app = orchestrator.app
    mock_risk_manager = orchestrator.app.risk_manager

    mock_app.run_prediction.return_value = {
        'probabilities': [np.array([0.1, 0.8, 0.1])],
        'predictions': [1]  # Strong SELL signal
    }
    # This mock is essential, its absence was causing a hidden exception.
    orchestrator.app._get_latest_market_data = AsyncMock(return_value={'close': 65000.0})
    
    # Risk manager rejects the signal
    from src.risk import TradeDecision
    mock_risk_manager.process_trade_signal.return_value = TradeDecision(
        is_approved=False,
        position_size=0,
        stop_loss_price=0,
        take_profit_price=0,
        reason="Risk too high",
        alerts=[]
    )

    # 2. Execute
    with patch.object(orchestrator, '_get_latest_features_for_symbol', new_callable=AsyncMock) as mock_get_features:
        mock_get_features.return_value = np.random.rand(1, 59)
        final_signal = await orchestrator._generate_trading_signal(symbol)

    # 3. Assertions
    assert final_signal is None
    mock_get_features.assert_awaited_once_with(symbol)
    mock_app.run_prediction.assert_awaited_once()
    mock_risk_manager.process_trade_signal.assert_awaited_once()


@pytest.mark.asyncio
async def test_generate_trading_signal_no_prediction(orchestrator):
    """
    Test the scenario where the model does not return a valid prediction.
    """
    # 1. Setup Mocks
    symbol = "BNBUSDT"
    mock_app = orchestrator.app
    mock_risk_manager = orchestrator.app.risk_manager

    # Model returns no prediction
    mock_app.run_prediction.return_value = {'predictions': []}

    # 2. Execute
    with patch.object(orchestrator, '_get_latest_features_for_symbol', new_callable=AsyncMock) as mock_get_features:
        mock_get_features.return_value = np.random.rand(1, 59)
        final_signal = await orchestrator._generate_trading_signal(symbol)

    # 3. Assertions
    assert final_signal is None
    mock_get_features.assert_awaited_once_with(symbol)
    mock_app.run_prediction.assert_awaited_once()
    # Ensure risk manager is not even called
    mock_risk_manager.process_trade_signal.assert_not_called()