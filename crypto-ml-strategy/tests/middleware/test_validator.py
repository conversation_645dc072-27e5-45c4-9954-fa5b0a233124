
import pytest
import pandas as pd
from unittest.mock import MagicMock, patch
from middleware.validator import DataValidationMiddleware

@pytest.fixture
def mock_config():
    """Provides a mock configuration for the middleware."""
    return {
        'required_columns': ['open', 'high', 'low', 'close', 'volume'],
        'dlq_path': '/tmp/test_dlq.log'
    }

@pytest.fixture
def middleware_instance(mock_config, mocker):
    """Provides a DataValidationMiddleware instance with mocked dependencies."""
    # This is the key fix: Use side_effect to return a *new* mock each time
    # Counter is called. This ensures counters are independent and avoids
    # the real Prometheus registry.
    mock_counter = mocker.patch('middleware.validator.Counter')
    mock_counter.side_effect = [MagicMock(), MagicMock(), MagicMock()]  # Provide new mocks for each call

    mocker.patch('middleware.validator.Gauge')
    
    # Mock the logger at the module level where it is defined
    mocker.patch('middleware.validator.logger')
    
    # Instantiate the middleware with the mock config
    middleware = DataValidationMiddleware(config=mock_config)
    
    # Spy on methods with side effects
    mocker.spy(middleware, '_write_to_dlq')
    
    return middleware

@pytest.fixture
def valid_dataframe():
    """Provides a valid pandas DataFrame."""
    data = {
        "open": [45000.0, 45100.0],
        "high": [45500.0, 45600.0],
        "low": [44900.0, 45050.0],
        "close": [45300.0, 45400.0],
        "volume": [1000.0, 1200.0],
    }
    return pd.DataFrame(data)

# Test Case: TC-HP-01 - Happy Path Validation
def test_process_valid_dataframe_happy_path(middleware_instance, valid_dataframe):
    """Tests that a valid DataFrame passes through the middleware without changes."""
    # Arrange
    middleware = middleware_instance
    
    # Act
    processed_df = middleware.process(valid_dataframe)
    
    # Assert
    pd.testing.assert_frame_equal(processed_df, valid_dataframe)
    assert middleware._write_to_dlq.call_count == 0
    assert middleware.invalid_rows_counter.inc.call_count == 0
    middleware.processed_rows_counter.inc.assert_called_once_with(len(valid_dataframe))

# Test Case: TC-SCHEMA-01 - Invalid Data Schema (Missing Column)
def test_process_missing_column(middleware_instance, valid_dataframe):
    """Tests that a DataFrame with a missing required column is entirely rejected."""
    # Arrange
    middleware = middleware_instance
    invalid_df = valid_dataframe.drop(columns=['close'])
    
    # Act
    processed_df = middleware.process(invalid_df)
    
    # Assert
    assert processed_df.empty
    middleware._write_to_dlq.assert_called_once()
    assert middleware.invalid_rows_counter.inc.call_count == 1
    
# Test Case: TC-LOGIC-01 - Invalid Data (Non-Numeric Value)
def test_process_non_numeric_value(middleware_instance, valid_dataframe):
    """Tests that rows with non-numeric data are identified and sent to DLQ."""
    # Arrange
    middleware = middleware_instance
    invalid_df = valid_dataframe.copy()
    invalid_df.loc[0, 'high'] = 'not-a-number'
    
    # Act
    processed_df = middleware.process(invalid_df)
    
    # Assert
    assert len(processed_df) == 1
    assert processed_df.index.tolist() == [1]
    middleware._write_to_dlq.assert_called_once()
    # Check that the invalid_rows_counter was incremented by 1
    middleware.invalid_rows_counter.inc.assert_called_once_with(1)

def test_process_empty_dataframe(middleware_instance):
    """Tests that an empty DataFrame is handled gracefully."""
    # Arrange
    middleware = middleware_instance
    empty_df = pd.DataFrame()
    
    # Act
    processed_df = middleware.process(empty_df)
    
    # Assert
    assert processed_df.empty
    assert middleware._write_to_dlq.call_count == 0
    
def test_dlq_writing_integration(mock_config, mocker):
    """
    Unit test to ensure the DLQ writing mechanism works as expected.
    """
    # Arrange
    mocker.patch('middleware.validator.logger')
    # Create a fresh instance to test the method in isolation
    middleware = DataValidationMiddleware(config=mock_config)
    invalid_df = pd.DataFrame({"open": [1], "high": ["invalid"], "low": [1], "close": [1], "volume": [1]})
    
    # Use mocker's specialized tool for mocking file I/O
    mock_open_file = mocker.patch('middleware.validator.open', mocker.mock_open())

    # Act
    middleware._write_to_dlq(invalid_df)
    
    # Assert
    # Check that open was called correctly
    mock_open_file.assert_called_once_with(mock_config['dlq_path'], 'a')
    # Check that the write method on the file handle was called
    mock_open_file().write.assert_called()

