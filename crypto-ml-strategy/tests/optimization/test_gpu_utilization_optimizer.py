"""
GPU利用率优化器测试
测试GPU利用率优化功能
"""

import pytest
import os
import torch
import torch.nn as nn
import numpy as np
from unittest.mock import Mock, patch, MagicMock
from src.optimization.gpu_utilization_optimizer import (
    GPUUtilizationOptimizer,
    GPUUtilizationConfig,
    create_gpu_utilization_optimizer
)


class SimpleTestModel(nn.Module):
    """简单测试模型"""
    
    def __init__(self, input_dim=100, hidden_dim=64, output_dim=3):
        super().__init__()
        self.layers = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, output_dim)
        )
    
    def forward(self, x):
        return self.layers(x)


@patch('src.optimization.gpu_utilization_optimizer.torch.compile', lambda model, **kwargs: model)
class TestGPUUtilizationOptimizer:
    """GPU利用率优化器测试"""
    
    @pytest.fixture
    def config(self):
        """测试配置"""
        return GPUUtilizationConfig(
            target_utilization=0.8,
            min_batch_size=32,
            max_batch_size=512,
            batch_size_step=32,
            warmup_iterations=2,
            measurement_iterations=3
        )
    
    @pytest.fixture
    def optimizer(self, config):
        """GPU利用率优化器"""
        return GPUUtilizationOptimizer(config)
    
    @pytest.fixture
    def model(self):
        """测试模型"""
        return SimpleTestModel()
    
    @pytest.fixture
    def sample_input(self):
        """样本输入"""
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        return torch.randn(16, 100, device=device)
    
    def test_optimizer_initialization(self, optimizer):
        """测试优化器初始化"""
        assert optimizer.config is not None
        assert optimizer.device is not None
        assert optimizer.optimization_stats is not None
        assert not optimizer.monitoring_active
    
    def test_config_creation(self):
        """测试配置创建"""
        config = GPUUtilizationConfig()
        assert config.target_utilization == 0.85
        assert config.min_batch_size == 512
        assert config.max_batch_size == 16384
    
    def test_create_optimizer_function(self):
        """测试优化器创建函数"""
        optimizer = create_gpu_utilization_optimizer(target_utilization=0.9)
        assert optimizer.config.target_utilization == 0.9
        assert isinstance(optimizer, GPUUtilizationOptimizer)
    
    @pytest.mark.skipif(not torch.cuda.is_available(), reason="CUDA not available")
    def test_find_optimal_batch_size_cuda(self, optimizer, model, sample_input):
        """测试CUDA环境下的批次大小优化"""
        model = model.to(optimizer.device)
        sample_input = sample_input.to(optimizer.device)
        
        # Mock监控方法以避免实际GPU监控
        with patch.object(optimizer, 'start_gpu_monitoring'), \
             patch.object(optimizer, 'stop_gpu_monitoring'):
            
            optimal_batch_size = optimizer.find_optimal_batch_size(model, sample_input)
            
            assert optimal_batch_size >= optimizer.config.min_batch_size
            assert optimal_batch_size <= optimizer.config.max_batch_size
            assert optimal_batch_size % optimizer.config.batch_size_step == 0
    
    def test_find_optimal_batch_size_cpu(self, optimizer, model, sample_input):
        """测试CPU环境下的批次大小优化"""
        # 强制使用CPU
        optimizer.device = torch.device('cpu')
        model = model.to(optimizer.device)
        sample_input = sample_input.to(optimizer.device)
        
        optimal_batch_size = optimizer.find_optimal_batch_size(model, sample_input)
        
        assert optimal_batch_size >= optimizer.config.min_batch_size
        assert optimal_batch_size <= optimizer.config.max_batch_size
    
    def test_test_batch_size_performance(self, optimizer, model, sample_input):
        """测试批次大小性能测试"""
        model = model.to(optimizer.device)
        sample_input = sample_input.to(optimizer.device)
        
        batch_size = 64
        throughput = optimizer._test_batch_size(model, sample_input, batch_size)
        
        assert throughput > 0
        assert isinstance(throughput, float)
    
    def test_optimize_dataloader_config(self, optimizer):
        """测试数据加载器配置优化"""
        dataset_size = 10000
        config = optimizer.optimize_dataloader_config(dataset_size)
        
        assert 'num_workers' in config
        assert 'pin_memory' in config
        assert 'persistent_workers' in config
        assert 'prefetch_factor' in config
        
        assert config['num_workers'] > 0
        assert config['pin_memory'] is True
        assert config['persistent_workers'] is True
    
    def test_optimize_model_for_training(self, optimizer, model):
        """测试模型训练优化"""
        optimized_model = optimizer.optimize_model_for_training(model)
        
        # 检查模型是否在正确的设备上
        # 由于 torch.device('cuda') != torch.device('cuda:0')，我们比较 .type 属性
        assert next(optimized_model.parameters()).device.type == optimizer.device.type
        
        # 检查模型是否仍然可调用
        sample_input = torch.randn(4, 100, device=optimizer.device)
        output = optimized_model(sample_input)
        assert output.shape == (4, 3)
    
    def test_setup_cuda_optimizations(self, optimizer):
        """测试CUDA优化设置"""
        force_cpu = os.environ.get('FORCE_CPU_TESTING', 'false').lower() == 'true'
        if force_cpu:
            pytest.skip("Skipping GPU-specific test: CPU forced.")
        if not torch.cuda.is_available():
            pytest.skip("Skipping GPU-specific test: CUDA not available.")
            
        # 这个测试不会抛出异常就算成功
        optimizer.setup_cuda_optimizations()
        
        # 检查一些CUDA设置
        assert torch.backends.cudnn.benchmark is True
    
    def test_get_current_gpu_utilization_empty(self, optimizer):
        """测试空历史记录时的GPU利用率获取"""
        utilization = optimizer.get_current_gpu_utilization()
        
        assert 'gpu_utilization' in utilization
        assert 'memory_utilization' in utilization
        assert utilization['gpu_utilization'] == 0.0
        assert utilization['memory_utilization'] == 0.0
    
    def test_get_current_gpu_utilization_with_data(self, optimizer):
        """测试有数据时的GPU利用率获取"""
        # 添加一些模拟数据
        optimizer.gpu_utilization_history.extend([50, 60, 70, 80])
        optimizer.memory_usage_history.extend([0.5, 0.6, 0.7, 0.8])
        
        utilization = optimizer.get_current_gpu_utilization()
        
        assert utilization['gpu_utilization'] > 0
        assert utilization['memory_utilization'] > 0
    
    def test_generate_optimization_report(self, optimizer):
        """测试优化报告生成"""
        # 设置一些统计数据
        optimizer.optimization_stats['optimized_batch_size'] = 1024
        optimizer.gpu_utilization_history.extend([30, 40, 50])  # 低利用率
        optimizer.memory_usage_history.extend([0.5, 0.6, 0.7])
        
        report = optimizer.generate_optimization_report()
        
        assert 'optimization_stats' in report
        assert 'current_utilization' in report
        assert 'recommendations' in report
        
        # 检查是否有低GPU利用率的建议
        recommendations = report['recommendations']
        assert any('GPU利用率过低' in rec for rec in recommendations)
    
    @patch('threading.Thread')
    def test_start_stop_gpu_monitoring(self, mock_thread, optimizer):
        """测试GPU监控启动和停止"""
        if optimizer.device.type != 'cuda':
            pytest.skip("Skipping GPU-only test")

        mock_thread_instance = Mock()
        mock_thread.return_value = mock_thread_instance
        
        # 测试启动监控
        optimizer.start_gpu_monitoring()
        assert optimizer.monitoring_active is True
        mock_thread.assert_called_once()
        mock_thread_instance.start.assert_called_once()
        
        # 测试停止监控
        optimizer.stop_gpu_monitoring()
        assert optimizer.monitoring_active is False
        mock_thread_instance.join.assert_called_once()
    
    def test_apply_optimizations_integration(self, optimizer, model, sample_input):
        """测试完整优化流程"""
        dataset_size = 5000
        
        # Mock一些方法以避免实际的GPU操作
        with patch.object(optimizer, 'start_gpu_monitoring'), \
             patch.object(optimizer, 'stop_gpu_monitoring'):
            
            optimized_model, optimal_batch_size, dataloader_config = optimizer.apply_optimizations(
                model, sample_input, dataset_size
            )
            
            # 检查返回值
            assert optimized_model is not None
            assert optimal_batch_size > 0
            assert isinstance(dataloader_config, dict)
            
            # 检查数据加载器配置
            assert 'num_workers' in dataloader_config
            assert 'pin_memory' in dataloader_config
    
    def test_memory_safety_in_batch_size_testing(self, optimizer, model, sample_input):
        """测试批次大小测试中的内存安全"""
        # 模拟高内存使用情况
        optimizer.memory_usage_history.extend([0.98, 0.99, 0.99])  # 高内存使用
        
        model = model.to(optimizer.device)
        sample_input = sample_input.to(optimizer.device)
        
        with patch.object(optimizer, 'start_gpu_monitoring'), \
             patch.object(optimizer, 'stop_gpu_monitoring'):
            
            optimal_batch_size = optimizer.find_optimal_batch_size(model, sample_input)
            
            # 应该返回一个合理的批次大小，即使内存使用很高
            assert optimal_batch_size >= optimizer.config.min_batch_size


class TestGPUUtilizationConfig:
    """GPU利用率配置测试"""
    
    def test_default_config(self):
        """测试默认配置"""
        config = GPUUtilizationConfig()
        
        assert config.target_utilization == 0.85
        assert config.min_batch_size == 512
        assert config.max_batch_size == 16384
        assert config.batch_size_step == 256
        assert config.memory_safety_margin == 0.05
        assert config.warmup_iterations == 10
        assert config.measurement_iterations == 20
        assert config.enable_async_loading is True
        assert config.enable_prefetch is True
        assert config.num_workers_multiplier == 2.0
    
    def test_custom_config(self):
        """测试自定义配置"""
        config = GPUUtilizationConfig(
            target_utilization=0.9,
            min_batch_size=128,
            max_batch_size=2048,
            batch_size_step=128
        )
        
        assert config.target_utilization == 0.9
        assert config.min_batch_size == 128
        assert config.max_batch_size == 2048
        assert config.batch_size_step == 128


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
