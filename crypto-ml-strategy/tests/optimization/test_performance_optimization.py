"""
性能优化测试
测试批量信号处理、风险管理等功能
"""

import pytest
import asyncio
import time
import numpy as np
import torch
from unittest.mock import Mock, AsyncMock, patch
from src.core.batch_signal_processor import BatchSignalProcessor, SignalRequest
from src.risk import (
    UnifiedRiskFramework,
    UnifiedRiskConfig,
    TradeRulesConfig,
    RiskLevel,
    TradeDecision
)


class TestBatchSignalProcessor:
    """批量信号处理器测试"""
    
    @pytest.fixture
    def mock_model(self):
        """模拟模型"""
        model = Mock()
        model.return_value = torch.randn(2, 3)  # 模拟输出，支持批量
        return model
    
    @pytest.fixture
    def mock_data_client(self):
        """模拟数据客户端"""
        client = Mock()
        return client
    
    @pytest.fixture
    def mock_kafka_client(self):
        """模拟Kafka客户端"""
        client = Mock()
        client.send_signal = AsyncMock()
        return client
    
    @pytest.fixture
    def batch_processor(self, mock_model, mock_data_client, mock_kafka_client):
        """批量信号处理器"""
        config = {
            'batch_size': 4,
            'max_wait_time': 0.1,
            'max_queue_size': 100
        }
        processor = BatchSignalProcessor(
            mock_model, mock_data_client, mock_kafka_client, config
        )
        processor._test_mode = True  # 标记为测试模式
        return processor
    
    def test_batch_processor_initialization(self, batch_processor):
        """测试批量处理器初始化"""
        assert batch_processor.batch_size == 4
        assert batch_processor.max_wait_time == 0.1
        assert not batch_processor.is_running
    
    def test_start_stop_processor(self, batch_processor):
        """测试启动和停止处理器"""
        batch_processor.start()
        assert batch_processor.is_running
        assert batch_processor.batch_thread is not None
        
        batch_processor.stop()
        assert not batch_processor.is_running
    
    @pytest.mark.asyncio
    async def test_request_signal(self, batch_processor):
        """测试信号请求"""
        batch_processor.start()
        
        # 发送请求
        result = await batch_processor.request_signal("BTCUSDT", priority=1)
        assert result is True
        
        # 检查队列
        assert not batch_processor.request_queue.empty()
        
        batch_processor.stop()
    
    def test_collect_batch(self, batch_processor):
        """测试批量收集"""
        # 添加请求到队列
        for i in range(3):
            request = SignalRequest(f"SYMBOL{i}", time.time(), 1)
            batch_processor.request_queue.put(request)
        
        # 收集批次
        batch = batch_processor._collect_batch()
        assert len(batch) == 3
        assert all(isinstance(req, SignalRequest) for req in batch)
    
    def test_prepare_features(self, batch_processor):
        """测试特征准备"""
        market_data = np.random.randn(60, 5).astype(np.float32)
        features = batch_processor._prepare_features(market_data)
        
        assert isinstance(features, torch.Tensor)
        assert features.shape == (1, 60, 5)
    
    def test_batch_inference(self, batch_processor):
        """测试批量推理"""
        features = torch.randn(1, 60, 5)
        predictions = batch_processor._batch_inference(features, 2)
        
        assert len(predictions) == 2
        assert all(isinstance(pred, np.ndarray) for pred in predictions)
    
    def test_create_signal_result(self, batch_processor):
        """测试信号结果创建"""
        request = SignalRequest("BTCUSDT", time.time(), 1)
        prediction = np.array([0.1, 0.8, 0.1])
        market_data = np.random.randn(60, 5)
        market_data[-1, 3] = 50000.0  # 设置收盘价
        
        result = batch_processor._create_signal_result(
            "BTCUSDT", request, prediction, market_data
        )
        
        assert result.symbol == "BTCUSDT"
        assert result.action in ['BUY', 'SELL', 'HOLD']
        assert 0 <= result.confidence <= 1
        assert result.price == 50000.0


class TestUnifiedRiskFramework:
    """Unified Risk Framework test"""

    @pytest.fixture
    def risk_framework(self):
        """Risk Framework"""
        config = UnifiedRiskConfig(
            trade_rules=TradeRulesConfig(
                min_confidence=0.6,
                max_daily_trades=10,
                min_time_between_trades_sec=60,
                volatility_threshold=0.05
            ),
            portfolio_thresholds={
                'levels': {'critical': 0.8, 'high': 0.6, 'medium': 0.4}
            }
        )
        return UnifiedRiskFramework(config)

    def test_framework_initialization(self, risk_framework):
        """Test framework initialization"""
        assert risk_framework.config.trade_rules.min_confidence == 0.6
        assert risk_framework.portfolio_engine is not None
        assert risk_framework.trade_governor is not None
        assert risk_framework.monitor is not None

    @pytest.mark.asyncio
    async def test_process_trade_signal_approved(self, risk_framework):
        """Test processing a high-confidence trade signal"""
        signal = {
            'symbol': 'BTCUSDT',
            'signal_data': {
                'action': 'BUY',
                'confidence': 0.8
            }
        }
        # Provide necessary context for the stateless governor
        context = {
            'price_history': {'BTCUSDT': [50000, 50100, 50050] * 10}, # Sufficient data
            'daily_trades': {},
            'last_trade_times': {},
            'account_balance': 100000
        }
        risk_framework.update_portfolio_state(context)

        decision = await risk_framework.process_trade_signal(signal)
        assert isinstance(decision, TradeDecision)
        assert decision.is_approved is True
        assert decision.position_size > 0
        assert decision.stop_loss_price > 0
        assert decision.take_profit_price > 0

    @pytest.mark.asyncio
    async def test_process_trade_signal_rejected_low_confidence(self, risk_framework):
        """Test processing a low-confidence trade signal"""
        signal = {
            'symbol': 'BTCUSDT',
            'signal_data': {
                'action': 'BUY',
                'confidence': 0.3
            }
        }
        context = {
            'price_history': {'BTCUSDT': [50000, 50100, 50050] * 10},
            'daily_trades': {},
            'last_trade_times': {},
            'account_balance': 100000
        }
        risk_framework.update_portfolio_state(context)
        
        decision = await risk_framework.process_trade_signal(signal)
        assert isinstance(decision, TradeDecision)
        assert decision.is_approved is False
        assert "confidence" in decision.reason.lower()

    @pytest.mark.asyncio
    async def test_assess_portfolio_risk(self, risk_framework):
        """Test portfolio risk assessment"""
        portfolio_data = {
            'positions': {
                'BTCUSDT': {'quantity': 1, 'current_price': 50000},
                'ETHUSDT': {'quantity': 10, 'current_price': 3000}
            },
            'returns': np.random.randn(100) * 0.01,
            'leverage': 2.0
        }
        assessment = risk_framework.assess_portfolio(portfolio_data)
        assert assessment is not None
        assert isinstance(assessment.risk_level, RiskLevel)
        assert assessment.overall_score >= 0
        assert 'var' in assessment.individual_risks

    @pytest.mark.asyncio
    async def test_monitor_start_stop(self, risk_framework):
        """Test the real-time monitor start and stop"""
        risk_framework.start_monitoring()
        assert risk_framework.monitor.is_running
        await risk_framework.stop_monitoring()
        assert not risk_framework.monitor.is_running


class TestPerformanceIntegration:
    """性能优化集成测试"""
    
    @pytest.mark.asyncio
    async def test_end_to_end_signal_processing(self):
        """端到端信号处理测试"""
        # 创建模拟组件
        model = Mock()
        model.return_value = torch.tensor([[0.1, 0.8, 0.1]])
        
        data_client = Mock()
        kafka_client = Mock()
        kafka_client.send_signal = AsyncMock()
        
        # 创建处理器
        config = {'batch_size': 2, 'max_wait_time': 0.1}
        processor = BatchSignalProcessor(model, data_client, kafka_client, config)
        
        # 创建风险管理器
        risk_config = UnifiedRiskConfig(
            trade_rules=TradeRulesConfig(
                min_confidence=0.5,
                static_stop_loss_pct=0.02,
                risk_reward_ratio=2.0
            )
        )
        risk_manager = UnifiedRiskFramework(risk_config)
        
        # 启动处理器
        processor.start()
        
        try:
            # 发送信号请求
            await processor.request_signal("BTCUSDT")
            await processor.request_signal("ETHUSDT")
            
            # 等待处理
            await asyncio.sleep(0.2)
            
            # 验证结果
            assert processor.stats['total_requests'] >= 2
            
        finally:
            processor.stop()
    
    def test_performance_metrics_collection(self):
        """性能指标收集测试"""
        config = {'batch_size': 4, 'max_wait_time': 0.1}
        processor = BatchSignalProcessor(Mock(), Mock(), Mock(), config)
        
        # 模拟性能数据
        processor._update_stats(4, 0.05)
        processor._update_stats(3, 0.04)
        
        stats = processor.get_stats()
        
        assert stats['total_batches'] == 2
        assert stats['avg_batch_size'] > 0
        assert stats['avg_processing_time'] > 0
        assert stats['throughput'] > 0


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
