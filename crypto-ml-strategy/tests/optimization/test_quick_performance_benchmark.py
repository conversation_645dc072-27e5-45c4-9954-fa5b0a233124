"""
快速性能基准测试
验证关键优化效果和GPU利用率
"""

import time
import sys
import os
from pathlib import Path
import torch
import torch.nn as nn
import numpy as np
from typing import Dict
import subprocess

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))


class TestQuickPerformanceBenchmark:
    """快速性能基准测试类"""
    
    def setup_method(self, method):
        """初始化测试"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔧 测试设备: {self.device}")
        if torch.cuda.is_available():
            print(f"🔧 GPU: {torch.cuda.get_device_name(0)}")
    
    def create_test_model(self, input_size: int = 59) -> nn.Module:
        """创建测试模型"""
        model = nn.Sequential(
            nn.Linear(input_size, 512),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)
        )
        return model.to(self.device)
    
    def get_gpu_utilization(self):
        """获取GPU利用率"""
        try:
            result = subprocess.run([
                'nvidia-smi', 
                '--query-gpu=utilization.gpu,memory.used,memory.total',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=2, check=True)
            
            if result.returncode == 0:
                values = result.stdout.strip().split(', ')
                gpu_util = float(values[0])
                memory_used = float(values[1])
                memory_total = float(values[2])
                memory_percent = (memory_used / memory_total) * 100
                
                return {
                    'gpu_utilization': gpu_util,
                    'memory_used': memory_used,
                    'memory_total': memory_total,
                    'memory_percent': memory_percent
                }
        except (FileNotFoundError, subprocess.CalledProcessError, subprocess.TimeoutExpired) as e:
            print(f"⚠️无法执行nvidia-smi: {e}")
            pass
        
        return None
    
    def test_optimized_dataloader_performance(self):
        """测试优化后的数据加载器性能"""
        print(f"\n🧪 测试优化后的数据加载器性能")
        
        batch_size = 8192
        num_batches = 100
        
        # 优化后的数据加载器配置
        class TestDataset(torch.utils.data.Dataset):
            def __init__(self, num_samples):
                self.num_samples = num_samples
            
            def __len__(self):
                return self.num_samples
            
            def __getitem__(self, idx):
                return torch.randn(59), torch.randint(0, 3, (1,)).item()
        
        dataset = TestDataset(batch_size * num_batches)
        
        # 优化配置
        optimized_loader = torch.utils.data.DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=6,
            pin_memory=True,
            persistent_workers=True,
            prefetch_factor=4,
            drop_last=True
        )
        
        model = self.create_test_model()
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        print("🔍 开始数据加载器性能测试...")
        
        start_time = time.time()
        total_samples = 0
        
        for batch_idx, (features, labels) in enumerate(optimized_loader):
            features = features.to(self.device, non_blocking=True)
            labels = labels.to(self.device, non_blocking=True)
            
            outputs = model(features)
            loss = criterion(outputs, labels)
            
            optimizer.zero_grad(set_to_none=True)
            loss.backward()
            optimizer.step()
            
            total_samples += features.size(0)
            
            # 每50批次输出一次进度（优化后的日志频率）
            if batch_idx % 50 == 0 and batch_idx > 0:
                current_time = time.time() - start_time
                current_throughput = total_samples / current_time
                print(f"   - 批次 {batch_idx}: {current_throughput:.0f} samples/s")
        
        end_time = time.time()
        total_time = end_time - start_time
        throughput = total_samples / total_time
        
        print(f"📊 数据加载器性能结果:")
        print(f"   - 总时间: {total_time:.3f}s")
        print(f"   - 吞吐量: {throughput:.0f} samples/s")
        print(f"   - 总样本数: {total_samples}")
        
        assert throughput > 50000, f"数据加载器吞吐量 {throughput:.0f} 低于50k"
    
    def test_torch_compile_effect(self):
        """测试torch.compile效果"""
        print(f"\n🧪 测试torch.compile效果")
        
        batch_size = 8192
        test_input = torch.randn(batch_size, 59, device=self.device)
        test_labels = torch.randint(0, 3, (batch_size,), device=self.device)
        
        # 测试未编译模型
        model_uncompiled = self.create_test_model()
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model_uncompiled.parameters(), lr=1e-3)
        
        # 预热
        for _ in range(5):
            outputs = model_uncompiled(test_input)
            loss = criterion(outputs, test_labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        
        # 测试未编译性能
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        start_time = time.time()
        for _ in range(50):
            outputs = model_uncompiled(test_input)
            loss = criterion(outputs, test_labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        uncompiled_time = time.time() - start_time
        
        # 测试编译模型
        model_compiled = self.create_test_model()
        
        # 检查torch.compile是否可用
        if hasattr(torch, 'compile'):
            try:
                model_compiled = torch.compile(model_compiled, mode='reduce-overhead')
                print("✅ torch.compile已启用")
            except Exception as e:
                print(f"⚠️ torch.compile启用失败: {e}")
                model_compiled = model_compiled
        else:
            print("⚠️ torch.compile不可用")
        
        optimizer_compiled = torch.optim.AdamW(model_compiled.parameters(), lr=1e-3)
        
        # 预热编译模型
        for _ in range(5):
            outputs = model_compiled(test_input)
            loss = criterion(outputs, test_labels)
            optimizer_compiled.zero_grad()
            loss.backward()
            optimizer_compiled.step()
        
        # 测试编译性能
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        start_time = time.time()
        for _ in range(50):
            outputs = model_compiled(test_input)
            loss = criterion(outputs, test_labels)
            optimizer_compiled.zero_grad()
            loss.backward()
            optimizer_compiled.step()
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        compiled_time = time.time() - start_time
        
        improvement = (uncompiled_time - compiled_time) / uncompiled_time * 100
        
        print(f"📊 torch.compile效果:")
        print(f"   - 未编译时间: {uncompiled_time:.3f}s")
        print(f"   - 编译后时间: {compiled_time:.3f}s")
        print(f"   - 性能提升: {improvement:+.1f}%")
        
        assert improvement > -10, f"torch.compile 导致性能下降超过10%: {improvement:.1f}%"
    
    def test_gpu_utilization_monitoring(self):
        """测试GPU利用率监控"""
        print(f"\n🧪 测试GPU利用率监控")
        
        if not torch.cuda.is_available():
            print("⚠️ CUDA不可用，跳过GPU利用率测试")
            return None
        
        model = self.create_test_model()
        batch_size = 8192
        
        # 创建大量GPU负载
        test_data = []
        for _ in range(100):
            features = torch.randn(batch_size, 59, device=self.device)
            labels = torch.randint(0, 3, (batch_size,), device=self.device)
            test_data.append((features, labels))
        
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        # 获取初始GPU状态
        metrics_before = self.get_gpu_utilization()
        assert metrics_before is not None, "获取初始GPU状态失败"

        print("🔍 开始GPU负载测试...")
        
        start_time = time.time()
        
        for i, (features, labels) in enumerate(test_data):
            outputs = model(features)
            loss = criterion(outputs, labels)
            optimizer.zero_grad(set_to_none=True)
            loss.backward()
            optimizer.step()

        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        end_time = time.time()
        
        # 获取最终GPU状态
        metrics_after = self.get_gpu_utilization()
        assert metrics_after is not None, "获取最终GPU状态失败"

        total_time = end_time - start_time
        total_samples = len(test_data) * batch_size
        throughput = total_samples / total_time

        print(f"📊 GPU内存使用 (前): {metrics_before['memory_used']:.1f}MB -> (后): {metrics_after['memory_used']:.1f}MB")
        print(f"📊 GPU利用率 (前): {metrics_before['gpu_utilization']:.1f}% -> (后): {metrics_after['gpu_utilization']:.1f}%")
        print(f"   - 吞吐量: {throughput:.0f} samples/s")
        print(f"   - 总时间: {total_time:.3f}s")

        assert metrics_after['memory_used'] > metrics_before['memory_used'], "计算后GPU内存使用量应增加"



