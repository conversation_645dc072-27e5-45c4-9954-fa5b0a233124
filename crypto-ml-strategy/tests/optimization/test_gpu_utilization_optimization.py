"""
GPU利用率优化测试
专门测试和优化GPU利用率，目标达到>80%
"""

import time
import sys
import os
from pathlib import Path
import torch
import torch.nn as nn
import numpy as np
from typing import Dict
import subprocess

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))


class GPUUtilizationOptimizationTest:
    """GPU利用率优化测试类"""
    
    def setup_method(self, method):
        """初始化测试"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔧 测试设备: {self.device}")
        if torch.cuda.is_available():
            print(f"🔧 GPU: {torch.cuda.get_device_name(0)}")
            print(f"🔧 GPU内存: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f}GB")
    
    def get_gpu_utilization(self):
        """获取GPU利用率"""
        try:
            result = subprocess.run([
                'nvidia-smi', 
                '--query-gpu=utilization.gpu,memory.used,memory.total',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=2)
            
            if result.returncode == 0:
                values = result.stdout.strip().split(', ')
                gpu_util = float(values[0])
                memory_used = float(values[1])
                memory_total = float(values[2])
                memory_percent = (memory_used / memory_total) * 100
                
                return {
                    'gpu_utilization': gpu_util,
                    'memory_used': memory_used,
                    'memory_total': memory_total,
                    'memory_percent': memory_percent
                }
        except:
            pass
        
        return None
    
    def create_gpu_intensive_model(self, complexity_level: int = 1):
        """创建GPU密集型模型"""
        if complexity_level == 1:
            # 基础复杂度
            model = nn.Sequential(
                nn.Linear(59, 1024),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(1024, 1024),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(1024, 512),
                nn.ReLU(),
                nn.Linear(512, 3)
            )
        elif complexity_level == 2:
            # 中等复杂度
            model = nn.Sequential(
                nn.Linear(59, 2048),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(2048, 2048),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(2048, 1024),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(1024, 1024),
                nn.ReLU(),
                nn.Linear(1024, 512),
                nn.ReLU(),
                nn.Linear(512, 3)
            )
        else:
            # 高复杂度
            model = nn.Sequential(
                nn.Linear(59, 4096),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(4096, 4096),
                nn.ReLU(),
                nn.Dropout(0.2),
                nn.Linear(4096, 2048),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(2048, 2048),
                nn.ReLU(),
                nn.Dropout(0.1),
                nn.Linear(2048, 1024),
                nn.ReLU(),
                nn.Linear(1024, 512),
                nn.ReLU(),
                nn.Linear(512, 3)
            )
        
        return model.to(self.device)
    
    def test_different_batch_sizes(self):
        """测试不同批次大小对GPU利用率的影响"""
        print(f"\n🧪 测试不同批次大小对GPU利用率的影响")
        
        batch_sizes = [4096, 8192, 16384, 32768]
        results = {}
        
        for batch_size in batch_sizes:
            print(f"\n🔍 测试批次大小: {batch_size}")
            
            try:
                model = self.create_gpu_intensive_model(complexity_level=2)
                criterion = nn.CrossEntropyLoss()
                optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
                
                # 创建测试数据
                test_data = []
                for _ in range(50):
                    features = torch.randn(batch_size, 59, device=self.device)
                    labels = torch.randint(0, 3, (batch_size,), device=self.device)
                    test_data.append((features, labels))
                
                gpu_utilizations = []
                start_time = time.time()
                
                for i, (features, labels) in enumerate(test_data):
                    outputs = model(features)
                    loss = criterion(outputs, labels)
                    optimizer.zero_grad(set_to_none=True)
                    loss.backward()
                    optimizer.step()
                    
                    # 每5批次检查GPU利用率
                    if i % 5 == 0:
                        gpu_metrics = self.get_gpu_utilization()
                        if gpu_metrics:
                            gpu_utilizations.append(gpu_metrics['gpu_utilization'])
                
                end_time = time.time()
                total_time = end_time - start_time
                total_samples = len(test_data) * batch_size
                throughput = total_samples / total_time
                
                if gpu_utilizations:
                    avg_gpu_util = sum(gpu_utilizations) / len(gpu_utilizations)
                    max_gpu_util = max(gpu_utilizations)
                    
                    results[batch_size] = {
                        'avg_gpu_utilization': avg_gpu_util,
                        'max_gpu_utilization': max_gpu_util,
                        'throughput': throughput,
                        'total_time': total_time
                    }
                    
                    print(f"   - 平均GPU利用率: {avg_gpu_util:.1f}%")
                    print(f"   - 峰值GPU利用率: {max_gpu_util:.1f}%")
                    print(f"   - 吞吐量: {throughput:.0f} samples/s")
                
                # 清理内存
                del model, test_data
                torch.cuda.empty_cache()
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"   - ❌ 内存不足，跳过批次大小 {batch_size}")
                    torch.cuda.empty_cache()
                else:
                    raise e
        
        return results
    
    def test_different_model_complexities(self):
        """测试不同模型复杂度对GPU利用率的影响"""
        print(f"\n🧪 测试不同模型复杂度对GPU利用率的影响")
        
        complexities = [1, 2, 3]
        complexity_names = ["基础", "中等", "高级"]
        batch_size = 8192
        results = {}
        
        for complexity, name in zip(complexities, complexity_names):
            print(f"\n🔍 测试模型复杂度: {name}")
            
            try:
                model = self.create_gpu_intensive_model(complexity_level=complexity)
                criterion = nn.CrossEntropyLoss()
                optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
                
                # 创建测试数据
                test_data = []
                for _ in range(50):
                    features = torch.randn(batch_size, 59, device=self.device)
                    labels = torch.randint(0, 3, (batch_size,), device=self.device)
                    test_data.append((features, labels))
                
                gpu_utilizations = []
                start_time = time.time()
                
                for i, (features, labels) in enumerate(test_data):
                    outputs = model(features)
                    loss = criterion(outputs, labels)
                    optimizer.zero_grad(set_to_none=True)
                    loss.backward()
                    optimizer.step()
                    
                    # 每5批次检查GPU利用率
                    if i % 5 == 0:
                        gpu_metrics = self.get_gpu_utilization()
                        if gpu_metrics:
                            gpu_utilizations.append(gpu_metrics['gpu_utilization'])
                
                end_time = time.time()
                total_time = end_time - start_time
                total_samples = len(test_data) * batch_size
                throughput = total_samples / total_time
                
                if gpu_utilizations:
                    avg_gpu_util = sum(gpu_utilizations) / len(gpu_utilizations)
                    max_gpu_util = max(gpu_utilizations)
                    
                    results[name] = {
                        'avg_gpu_utilization': avg_gpu_util,
                        'max_gpu_utilization': max_gpu_util,
                        'throughput': throughput,
                        'total_time': total_time
                    }
                    
                    print(f"   - 平均GPU利用率: {avg_gpu_util:.1f}%")
                    print(f"   - 峰值GPU利用率: {max_gpu_util:.1f}%")
                    print(f"   - 吞吐量: {throughput:.0f} samples/s")
                
                # 清理内存
                del model, test_data
                torch.cuda.empty_cache()
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"   - ❌ 内存不足，跳过复杂度 {name}")
                    torch.cuda.empty_cache()
                else:
                    raise e
        
        return results
    
    def test_continuous_high_load(self):
        """测试持续高负载下的GPU利用率"""
        print(f"\n🧪 测试持续高负载下的GPU利用率")
        
        # 使用最优配置
        batch_size = 16384  # 更大的批次
        model = self.create_gpu_intensive_model(complexity_level=3)  # 最复杂模型
        
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        print("🔍 开始持续高负载测试...")
        
        gpu_utilizations = []
        memory_usages = []
        
        start_time = time.time()
        
        try:
            for i in range(200):  # 更多批次
                features = torch.randn(batch_size, 59, device=self.device)
                labels = torch.randint(0, 3, (batch_size,), device=self.device)
                
                outputs = model(features)
                loss = criterion(outputs, labels)
                optimizer.zero_grad(set_to_none=True)
                loss.backward()
                optimizer.step()
                
                # 每2批次检查GPU利用率
                if i % 2 == 0:
                    gpu_metrics = self.get_gpu_utilization()
                    if gpu_metrics:
                        gpu_utilizations.append(gpu_metrics['gpu_utilization'])
                        memory_usages.append(gpu_metrics['memory_percent'])
                        
                        if i % 20 == 0:
                            print(f"   - 批次 {i}: GPU利用率 {gpu_metrics['gpu_utilization']:.1f}%, 内存 {gpu_metrics['memory_percent']:.1f}%")
        
        except RuntimeError as e:
            if "out of memory" in str(e):
                print(f"   - ⚠️ 内存不足，测试在批次 {i} 停止")
                torch.cuda.empty_cache()
            else:
                raise e
        
        end_time = time.time()
        total_time = end_time - start_time
        
        if gpu_utilizations:
            avg_gpu_util = sum(gpu_utilizations) / len(gpu_utilizations)
            max_gpu_util = max(gpu_utilizations)
            avg_memory = sum(memory_usages) / len(memory_usages)
            max_memory = max(memory_usages)
            
            print(f"\n📊 持续高负载测试结果:")
            print(f"   - 平均GPU利用率: {avg_gpu_util:.1f}%")
            print(f"   - 峰值GPU利用率: {max_gpu_util:.1f}%")
            print(f"   - 平均内存使用: {avg_memory:.1f}%")
            print(f"   - 峰值内存使用: {max_memory:.1f}%")
            print(f"   - 测试时长: {total_time:.3f}s")
            print(f"   - GPU利用率目标(>80%): {'✅ 达成' if avg_gpu_util >= 80 else '❌ 未达成'}")
            
            return {
                'avg_gpu_utilization': avg_gpu_util,
                'max_gpu_utilization': max_gpu_util,
                'avg_memory_usage': avg_memory,
                'max_memory_usage': max_memory,
                'target_achieved': avg_gpu_util >= 80
            }
        
        return None


def main():
    """主测试函数"""
    print("🚀 开始GPU利用率优化测试...")
    print("="*60)
    
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，无法进行GPU利用率测试")
        return
    
    try:
        test_instance = GPUUtilizationOptimizationTest()
        
        # 测试1: 不同批次大小
        print("\n" + "="*60)
        print("测试1: 不同批次大小对GPU利用率的影响")
        batch_results = test_instance.test_different_batch_sizes()
        
        # 测试2: 不同模型复杂度
        print("\n" + "="*60)
        print("测试2: 不同模型复杂度对GPU利用率的影响")
        complexity_results = test_instance.test_different_model_complexities()
        
        # 测试3: 持续高负载
        print("\n" + "="*60)
        print("测试3: 持续高负载GPU利用率测试")
        high_load_results = test_instance.test_continuous_high_load()
        
        # 最终总结
        print("\n" + "="*60)
        print("🎯 GPU利用率优化测试总结")
        print("="*60)
        
        # 分析批次大小结果
        if batch_results:
            print(f"📊 批次大小影响分析:")
            best_batch_size = None
            best_gpu_util = 0
            
            for batch_size, result in batch_results.items():
                gpu_util = result['avg_gpu_utilization']
                print(f"   - 批次大小 {batch_size}: GPU利用率 {gpu_util:.1f}%")
                
                if gpu_util > best_gpu_util:
                    best_gpu_util = gpu_util
                    best_batch_size = batch_size
            
            print(f"   - 最佳批次大小: {best_batch_size} (GPU利用率: {best_gpu_util:.1f}%)")
        
        # 分析模型复杂度结果
        if complexity_results:
            print(f"\n📊 模型复杂度影响分析:")
            best_complexity = None
            best_gpu_util = 0
            
            for complexity, result in complexity_results.items():
                gpu_util = result['avg_gpu_utilization']
                print(f"   - {complexity}复杂度: GPU利用率 {gpu_util:.1f}%")
                
                if gpu_util > best_gpu_util:
                    best_gpu_util = gpu_util
                    best_complexity = complexity
            
            print(f"   - 最佳复杂度: {best_complexity} (GPU利用率: {best_gpu_util:.1f}%)")
        
        # 分析持续高负载结果
        if high_load_results:
            print(f"\n📊 持续高负载测试:")
            print(f"   - 平均GPU利用率: {high_load_results['avg_gpu_utilization']:.1f}%")
            print(f"   - 峰值GPU利用率: {high_load_results['max_gpu_utilization']:.1f}%")
            print(f"   - 目标达成(>80%): {'✅' if high_load_results['target_achieved'] else '❌'}")
        
        # 生成优化建议
        print(f"\n🎯 GPU利用率优化建议:")
        
        if high_load_results and high_load_results['target_achieved']:
            print(f"✅ 通过增加模型复杂度和批次大小，成功达到>80%GPU利用率目标")
            print(f"📋 推荐配置:")
            print(f"   - 批次大小: 16384+")
            print(f"   - 模型复杂度: 高级(4096+ hidden units)")
            print(f"   - 内存使用: {high_load_results['max_memory_usage']:.1f}%")
        else:
            print(f"⚠️ 当前配置未达到80%GPU利用率目标")
            print(f"📋 优化建议:")
            print(f"   - 增加批次大小到16384或更高")
            print(f"   - 增加模型复杂度(更多层和更大hidden size)")
            print(f"   - 考虑使用梯度累积来模拟更大批次")
            print(f"   - 检查数据加载器是否成为瓶颈")
        
        print("\n✅ GPU利用率优化测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
