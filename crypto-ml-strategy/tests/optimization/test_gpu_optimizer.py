"""
GPU优化器验证测试类
验证GPU优化器的各项功能是否正常工作
"""

import time
import sys
from pathlib import Path
import torch
import numpy as np
import pytest

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.optimization.gpu_utilization_optimizer import create_gpu_utilization_optimizer


class TestGPUOptimizer:
    """GPU优化器测试类"""
    
    def setup_method(self, method):
        """初始化测试"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔧 测试设备: {self.device}")
        
        if torch.cuda.is_available():
            print(f"🔧 GPU: {torch.cuda.get_device_name(0)}")
            print(f"🔧 GPU内存: {torch.cuda.get_device_properties(0).total_memory / (1024**3):.1f}GB")
    
    @pytest.fixture(scope="class")
    def optimizer(self):
        """Fixture to create and provide the GPU optimizer for the test class."""
        print("\\n🔧 Fixture: Creating GPU optimizer...")
        start_time = time.time()
        optimizer = create_gpu_utilization_optimizer(target_utilization=0.8)
        creation_time = time.time() - start_time
        print(f"🔧 Fixture: Optimizer created in {creation_time:.3f}s")
        assert optimizer is not None, "GPU optimizer creation failed in fixture"
        assert creation_time < 1.0, f"优化器创建耗时过长: {creation_time:.3f}s"
        return optimizer

    def test_gpu_optimizer_creation_properties(self, optimizer):
        """测试GPU优化器创建后的属性"""
        print("\\n🧪 测试GPU优化器创建属性...")
        assert optimizer is not None, "GPU优化器创建失败"
        assert optimizer.config.target_utilization == 0.8, "目标利用率不匹配"
    
    
    def test_batch_size_optimization(self, optimizer):
        """测试批次大小优化（通过配置验证）"""
        print("\n🧪 测试批次大小配置...")

        # 验证批次大小配置
        config = optimizer.config

        print(f"📊 批次大小配置:")
        print(f"   - 最小批次大小: {config.min_batch_size}")
        print(f"   - 最大批次大小: {config.max_batch_size}")
        print(f"   - 批次大小步长: {config.batch_size_step}")

        # 验证配置合理性
        assert config.min_batch_size > 0, f"最小批次大小无效: {config.min_batch_size}"
        assert config.max_batch_size > config.min_batch_size, f"最大批次大小配置错误"
        assert config.batch_size_step > 0, f"批次步长无效: {config.batch_size_step}"

        # 测试批次大小范围
        test_batch_sizes = [512, 1024, 2048, 4096, 8192, 16384]
        for batch_size in test_batch_sizes:
            if config.min_batch_size <= batch_size <= config.max_batch_size:
                print(f"   - ✅ 批次大小 {batch_size} 在有效范围内")
            else:
                print(f"   - ⚠️ 批次大小 {batch_size} 超出范围")
    
    def test_dataloader_config_optimization(self, optimizer):
        """测试数据加载器配置优化"""
        print("\n🧪 测试数据加载器配置优化...")
        
        test_sizes = [10000, 50000, 100000]
        
        for dataset_size in test_sizes:
            start_time = time.time()
            config = optimizer.optimize_dataloader_config(dataset_size)
            config_time = time.time() - start_time
            
            print(f"📊 数据集大小: {dataset_size}")
            print(f"   - num_workers: {config['num_workers']}")
            print(f"   - prefetch_factor: {config['prefetch_factor']}")
            print(f"   - pin_memory: {config['pin_memory']}")
            print(f"   - 配置优化耗时: {config_time:.6f}s")
            
            # 验证配置合理性
            assert config['num_workers'] > 0, f"worker数量无效: {config['num_workers']}"
            assert config['prefetch_factor'] > 0, f"预取因子无效: {config['prefetch_factor']}"
            assert isinstance(config['pin_memory'], bool), "pin_memory应为布尔值"
            assert config_time < 0.1, f"配置优化耗时过长: {config_time:.6f}s"
    
    def test_cuda_optimizations(self, optimizer):
        """测试CUDA优化设置"""
        print("\n🧪 测试CUDA优化设置...")
        
        if not torch.cuda.is_available():
            print("⚠️ CUDA不可用，跳过CUDA优化测试")
            return
        
        start_time = time.time()
        optimizer.setup_cuda_optimizations()
        setup_time = time.time() - start_time
        
        print(f"📈 CUDA优化设置耗时: {setup_time:.3f}s")
        
        # 验证CUDA设置
        print(f"📊 CUDA设置验证:")
        print(f"   - torch.backends.cudnn.benchmark: {torch.backends.cudnn.benchmark}")
        print(f"   - torch.backends.cudnn.deterministic: {torch.backends.cudnn.deterministic}")
        
        assert setup_time < 1.0, f"CUDA优化设置耗时过长: {setup_time:.3f}s"
    
    def test_gpu_utilization_monitoring(self, optimizer):
        """测试GPU利用率监控"""
        print("\n🧪 测试GPU利用率监控...")

        if not torch.cuda.is_available():
            print("⚠️ CUDA不可用，跳过GPU监控测试")
            return

        # 记录初始内存状态
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated()

        start_time = time.time()
        gpu_info = optimizer.get_current_gpu_utilization()
        monitoring_time = time.time() - start_time

        print(f"📈 GPU监控耗时: {monitoring_time:.3f}s")
        print(f"📊 初始内存使用: {initial_memory / (1024**2):.1f}MB")
        print(f"📊 GPU利用率信息: {gpu_info}")

        # 测试内存分配
        test_tensor = torch.randn(1000, 1000, device=self.device)
        allocated_memory = torch.cuda.memory_allocated()
        print(f"📊 测试后内存使用: {allocated_memory / (1024**2):.1f}MB")

        del test_tensor
        torch.cuda.empty_cache()

        assert monitoring_time < 1.0, f"GPU监控耗时过长: {monitoring_time:.3f}s"
        assert isinstance(gpu_info, dict), "GPU信息应为字典类型"
    
    def test_optimization_report(self, optimizer):
        """测试优化报告生成"""
        print("\n🧪 测试优化报告生成...")

        start_time = time.time()
        report = optimizer.generate_optimization_report()
        report_time = time.time() - start_time

        print(f"📈 报告生成耗时: {report_time:.3f}s")
        print(f"📊 优化报告: {report}")

        assert report_time < 1.0, f"报告生成耗时过长: {report_time:.3f}s"
        assert isinstance(report, dict), "优化报告应为字典类型"
    
    def test_batch_size_vs_gpu_utilization(self, optimizer):
        """测试批次大小与GPU利用率的关系"""
        print("\n🧪 测试批次大小与GPU利用率关系...")
        
        if not torch.cuda.is_available():
            print("⚠️ CUDA不可用，跳过GPU利用率测试")
            return
        
        # 创建测试模型
        model = torch.nn.Sequential(
            torch.nn.Linear(59, 256),  # 匹配实际特征数
            torch.nn.ReLU(),
            torch.nn.Linear(256, 256),
            torch.nn.ReLU(),
            torch.nn.Linear(256, 1)
        ).to(self.device)
        
        batch_sizes = [512, 1024, 2048, 4096, 8192, 16384]
        
        for batch_size in batch_sizes:
            try:
                x = torch.randn(batch_size, 59, device=self.device)
                
                # 预热
                for _ in range(5):
                    _ = model(x)
                
                torch.cuda.synchronize()
                
                # 性能测试
                start_time = time.time()
                for _ in range(20):
                    output = model(x)
                    loss = output.mean()
                    loss.backward()
                    
                torch.cuda.synchronize()
                end_time = time.time()
                
                total_time = end_time - start_time
                throughput = (20 * batch_size) / total_time
                memory_used = torch.cuda.memory_allocated() / (1024**3)
                
                print(f"📊 批次大小: {batch_size}")
                print(f"   - 吞吐量: {throughput:.0f} samples/sec")
                print(f"   - GPU内存: {memory_used:.2f}GB")
                print(f"   - 平均批次时间: {total_time/20:.3f}s")
                
                # 清理内存
                del x, output, loss
                torch.cuda.empty_cache()
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"📊 批次大小: {batch_size} - ❌ GPU内存不足")
                    break
                else:
                    raise e


