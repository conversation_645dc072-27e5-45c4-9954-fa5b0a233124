"""
torch.compile效果验证测试
专门测试torch.compile是否真正提升了模型性能
"""

import time
import sys
from pathlib import Path
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.models.unified_fusion_model import UnifiedSignalFusionModel


class TestTorchCompilePerformance:
    """torch.compile性能测试类"""
    
    def setup_method(self, method):
        """初始化测试"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger = logging.getLogger(__name__)
        
        print(f"🔧 测试设备: {self.device}")
        if torch.cuda.is_available():
            print(f"🔧 GPU: {torch.cuda.get_device_name(0)}")
            print(f"🔧 PyTorch版本: {torch.__version__}")
            print(f"🔧 CUDA版本: {torch.version.cuda}")
    
    def create_test_model(self, input_size: int = 59) -> nn.Module:
        """创建测试模型"""
        # 使用简单但有代表性的模型进行torch.compile测试
        model = nn.Sequential(
            nn.Linear(input_size, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Linear(64, 3)
        )

        return model.to(self.device)
    
    def create_test_data(self, batch_size: int, input_size: int, num_batches: int = 50) -> List[Tuple[torch.Tensor, torch.Tensor]]:
        """创建测试数据"""
        data_batches = []
        for _ in range(num_batches):
            x = torch.randn(batch_size, input_size, device=self.device)
            y = torch.randint(0, 3, (batch_size,), device=self.device)
            data_batches.append((x, y))
        return data_batches
    
    def benchmark_model(self, model: nn.Module, data_batches: List[Tuple[torch.Tensor, torch.Tensor]], 
                       warmup_batches: int = 10, test_name: str = "Model") -> Dict[str, float]:
        """基准测试模型性能"""
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.Adam(model.parameters(), lr=0.001)
        
        # 预热
        print(f"🔥 {test_name} 预热中...")
        model.train()
        for i in range(warmup_batches):
            x, y = data_batches[i % len(data_batches)]
            optimizer.zero_grad()
            output = model(x)
            loss = criterion(output, y)
            loss.backward()
            optimizer.step()
        
        # 同步GPU
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        # 性能测试
        print(f"📊 {test_name} 性能测试中...")
        start_time = time.time()
        
        total_samples = 0
        total_loss = 0.0
        
        for x, y in data_batches:
            optimizer.zero_grad()
            output = model(x)
            loss = criterion(output, y)
            loss.backward()
            optimizer.step()
            
            total_samples += x.size(0)
            total_loss += loss.item()
        
        # 同步GPU
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        return {
            'total_time': total_time,
            'samples_per_second': total_samples / total_time,
            'avg_loss': total_loss / len(data_batches),
            'total_samples': total_samples,
            'batches_processed': len(data_batches)
        }
    
    def test_compile_vs_no_compile(self, batch_size: int = 8192, input_size: int = 59):
        """测试编译vs非编译模型性能"""
        print(f"\n🧪 测试torch.compile效果 (batch_size={batch_size})")
        
        # 创建测试数据
        data_batches = self.create_test_data(batch_size, input_size, num_batches=30)
        print(f"📊 测试数据: {len(data_batches)}个批次, 每批次{batch_size}样本")
        
        # 测试1: 非编译模型
        print("\n🔍 测试1: 非编译模型")
        model_no_compile = self.create_test_model(input_size)
        results_no_compile = self.benchmark_model(model_no_compile, data_batches, test_name="非编译模型")
        
        # 清理内存
        del model_no_compile
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        # 测试2: 编译模型
        print("\n🔍 测试2: torch.compile编译模型")
        model_compile = self.create_test_model(input_size)
        
        # 编译模型
        compile_start = time.time()
        model_compile = torch.compile(
            model_compile,
            mode="reduce-overhead",  # 使用与配置相同的模式
            dynamic=True,
            fullgraph=False
        )
        compile_time = time.time() - compile_start
        print(f"📈 模型编译耗时: {compile_time:.3f}s")
        
        results_compile = self.benchmark_model(model_compile, data_batches, test_name="编译模型")
        
        # 性能对比
        self.compare_results(results_no_compile, results_compile, compile_time)
        
        # 清理内存
        del model_compile
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        return results_no_compile, results_compile
    
    def compare_results(self, results_no_compile: Dict[str, float], results_compile: Dict[str, float], compile_time: float):
        """对比测试结果"""
        print(f"\n📊 性能对比结果:")
        print(f"{'指标':<20} {'非编译':<15} {'编译后':<15} {'改进':<15}")
        print("-" * 70)
        
        # 总时间对比
        time_improvement = (results_no_compile['total_time'] - results_compile['total_time']) / results_no_compile['total_time'] * 100
        print(f"{'总时间(s)':<20} {results_no_compile['total_time']:<15.3f} {results_compile['total_time']:<15.3f} {time_improvement:>+.1f}%")
        
        # 吞吐量对比
        throughput_improvement = (results_compile['samples_per_second'] - results_no_compile['samples_per_second']) / results_no_compile['samples_per_second'] * 100
        print(f"{'吞吐量(samples/s)':<20} {results_no_compile['samples_per_second']:<15.0f} {results_compile['samples_per_second']:<15.0f} {throughput_improvement:>+.1f}%")
        
        # 平均损失对比
        loss_change = (results_compile['avg_loss'] - results_no_compile['avg_loss']) / results_no_compile['avg_loss'] * 100
        print(f"{'平均损失':<20} {results_no_compile['avg_loss']:<15.4f} {results_compile['avg_loss']:<15.4f} {loss_change:>+.1f}%")
        
        print(f"\n📈 编译开销: {compile_time:.3f}s")
        
        # 总结
        if throughput_improvement > 5:
            print(f"✅ torch.compile显著提升性能: +{throughput_improvement:.1f}% 吞吐量")
        elif throughput_improvement > 0:
            print(f"🟡 torch.compile轻微提升性能: +{throughput_improvement:.1f}% 吞吐量")
        else:
            print(f"❌ torch.compile未提升性能: {throughput_improvement:.1f}% 吞吐量")
        
        return throughput_improvement
    
    def test_different_batch_sizes(self):
        """测试不同批次大小下的编译效果"""
        print(f"\n🧪 测试不同批次大小下的torch.compile效果")
        
        batch_sizes = [1024, 2048, 4096, 8192]
        improvements = []
        
        for batch_size in batch_sizes:
            try:
                print(f"\n{'='*50}")
                print(f"测试批次大小: {batch_size}")
                
                results_no_compile, results_compile = self.test_compile_vs_no_compile(batch_size)
                improvement = (results_compile['samples_per_second'] - results_no_compile['samples_per_second']) / results_no_compile['samples_per_second'] * 100
                improvements.append((batch_size, improvement))
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    print(f"❌ 批次大小 {batch_size} 导致GPU内存不足")
                    break
                else:
                    raise e
        
        # 总结不同批次大小的效果
        print(f"\n📊 不同批次大小的torch.compile效果总结:")
        print(f"{'批次大小':<10} {'性能提升':<15}")
        print("-" * 25)
        for batch_size, improvement in improvements:
            print(f"{batch_size:<10} {improvement:>+.1f}%")
        
        return improvements
    
    def test_compile_modes(self, batch_size: int = 8192):
        """测试不同编译模式的效果"""
        print(f"\n🧪 测试不同torch.compile模式的效果")
        
        modes = ["default", "reduce-overhead", "max-autotune"]
        results = {}
        
        # 创建测试数据
        data_batches = self.create_test_data(batch_size, 59, num_batches=20)
        
        for mode in modes:
            try:
                print(f"\n🔍 测试编译模式: {mode}")
                
                model = self.create_test_model(59)
                
                # 编译模型
                compile_start = time.time()
                model = torch.compile(model, mode=mode, dynamic=True, fullgraph=False)
                compile_time = time.time() - compile_start
                
                # 性能测试
                result = self.benchmark_model(model, data_batches, test_name=f"模式-{mode}")
                result['compile_time'] = compile_time
                results[mode] = result
                
                print(f"📈 编译时间: {compile_time:.3f}s")
                print(f"📈 吞吐量: {result['samples_per_second']:.0f} samples/s")
                
                # 清理内存
                del model
                torch.cuda.empty_cache() if torch.cuda.is_available() else None
                
            except Exception as e:
                print(f"❌ 模式 {mode} 测试失败: {e}")
                results[mode] = None
        
        # 对比不同模式
        print(f"\n📊 不同编译模式对比:")
        print(f"{'模式':<15} {'编译时间(s)':<12} {'吞吐量':<15} {'总时间(s)':<12}")
        print("-" * 60)
        
        for mode, result in results.items():
            if result:
                print(f"{mode:<15} {result['compile_time']:<12.3f} {result['samples_per_second']:<15.0f} {result['total_time']:<12.3f}")
            else:
                print(f"{mode:<15} {'失败':<12} {'失败':<15} {'失败':<12}")
        
        return results


def main():
    """主测试函数"""
    print("🚀 开始torch.compile效果验证测试...")
    
    if not torch.cuda.is_available():
        print("⚠️ CUDA不可用，torch.compile效果可能不明显")
    
    try:
        test_instance = TestTorchCompilePerformance()
        
        # 测试1: 基本编译vs非编译对比
        print("\n" + "="*60)
        print("测试1: 基本torch.compile效果验证")
        test_instance.test_compile_vs_no_compile()
        
        # 测试2: 不同批次大小的编译效果
        print("\n" + "="*60)
        print("测试2: 不同批次大小的编译效果")
        test_instance.test_different_batch_sizes()
        
        # 测试3: 不同编译模式对比
        print("\n" + "="*60)
        print("测试3: 不同编译模式对比")
        test_instance.test_compile_modes()
        
        print("\n✅ torch.compile效果验证测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
