"""
max-autotune稳定性测试器的测试类
"""

import unittest
import torch
import torch.nn as nn
import sys
from pathlib import Path
from unittest.mock import patch, MagicMock
import tempfile
import json

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.testing.max_autotune_stability_tester import MaxAutotuneStabilityTester, StabilityMetrics


class TestMaxAutotuneStabilityTester(unittest.TestCase):
    """max-autotune稳定性测试器测试类"""
    
    def setUp(self):
        """设置测试环境"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.tester = MaxAutotuneStabilityTester(self.device, test_duration_hours=0.01)  # 短时间测试
    
    def test_initialization(self):
        """测试初始化"""
        self.assertEqual(self.tester.device, self.device)
        self.assertEqual(self.tester.test_duration_hours, 0.01)
        self.assertEqual(self.tester.batch_size, 16384)
        self.assertEqual(self.tester.input_size, 59)
        self.assertEqual(self.tester.num_classes, 3)
        self.assertFalse(self.tester.is_running)
        self.assertEqual(len(self.tester.metrics_history), 0)
        self.assertEqual(len(self.tester.error_history), 0)
    
    def test_create_test_model(self):
        """测试模型创建"""
        model = self.tester.create_test_model()
        
        # 检查模型结构
        self.assertIsInstance(model, nn.Sequential)
        self.assertEqual(len(model), 14)  # 6个Linear + 5个ReLU + 3个Dropout
        
        # 检查输入输出维度
        test_input = torch.randn(10, self.tester.input_size, device=self.device)
        output = model(test_input)
        self.assertEqual(output.shape, (10, self.tester.num_classes))
    
    @patch('subprocess.run')
    def test_get_gpu_metrics_success(self, mock_run):
        """测试GPU指标获取成功"""
        # 模拟nvidia-smi输出
        mock_run.return_value.returncode = 0
        mock_run.return_value.stdout = "85, 4096, 6144, 65"
        
        metrics = self.tester.get_gpu_metrics()
        
        self.assertIsNotNone(metrics)
        self.assertEqual(metrics['gpu_utilization'], 85.0)
        self.assertEqual(metrics['memory_used_mb'], 4096.0)
        self.assertEqual(metrics['memory_total_mb'], 6144.0)
        self.assertAlmostEqual(metrics['memory_percent'], 66.67, places=1)
        self.assertEqual(metrics['temperature'], 65.0)
    
    @patch('subprocess.run')
    def test_get_gpu_metrics_failure(self, mock_run):
        """测试GPU指标获取失败"""
        mock_run.return_value.returncode = 1
        
        metrics = self.tester.get_gpu_metrics()
        self.assertIsNone(metrics)
    
    @unittest.skipIf(not torch.cuda.is_available(), "CUDA not available")
    def test_compile_model_with_timing(self):
        """测试模型编译和计时"""
        model = self.tester.create_test_model()
        
        compiled_model, compile_time = self.tester.compile_model_with_timing(model)
        
        # 检查编译结果
        self.assertIsNotNone(compiled_model)
        self.assertGreaterEqual(compile_time, 0)
        
        # 检查编译后模型可以正常运行
        test_input = torch.randn(10, self.tester.input_size, device=self.device)
        output = compiled_model(test_input)
        self.assertEqual(output.shape, (10, self.tester.num_classes))
    
    @unittest.skipIf(not torch.cuda.is_available(), "CUDA not available")
    def test_run_training_iteration(self):
        """测试训练迭代"""
        model = self.tester.create_test_model()
        
        # 尝试编译模型
        try:
            compiled_model, _ = self.tester.compile_model_with_timing(model)
        except:
            compiled_model = model  # 如果编译失败，使用原始模型
        
        optimizer = torch.optim.AdamW(compiled_model.parameters(), lr=1e-3)
        criterion = nn.CrossEntropyLoss()
        
        stats = self.tester.run_training_iteration(compiled_model, optimizer, criterion)
        
        # 检查返回的统计数据
        self.assertIn('loss', stats)
        self.assertIn('forward_time_ms', stats)
        self.assertIn('backward_time_ms', stats)
        self.assertIn('step_time_ms', stats)
        self.assertIn('total_time_ms', stats)
        
        # 检查数值合理性
        self.assertGreater(stats['loss'], 0)
        self.assertGreater(stats['forward_time_ms'], 0)
        self.assertGreater(stats['backward_time_ms'], 0)
        self.assertGreater(stats['step_time_ms'], 0)
        self.assertGreater(stats['total_time_ms'], 0)
    
    @patch('psutil.cpu_percent')
    @patch('psutil.virtual_memory')
    def test_collect_metrics(self, mock_memory, mock_cpu):
        """测试指标收集"""
        # 模拟系统指标
        mock_cpu.return_value = 75.5
        mock_memory.return_value.percent = 60.2
        
        # 模拟训练统计
        training_stats = {
            'loss': 1.234,
            'forward_time_ms': 10.5,
            'backward_time_ms': 15.2,
            'step_time_ms': 5.1,
            'total_time_ms': 30.8
        }
        
        with patch.object(self.tester, 'get_gpu_metrics') as mock_gpu:
            mock_gpu.return_value = {
                'gpu_utilization': 95.0,
                'memory_used_mb': 5000,
                'memory_percent': 80.0,
                'temperature': 70.0
            }
            
            metrics = self.tester.collect_metrics(training_stats, 1000.0)
        
        # 检查指标
        self.assertIsInstance(metrics, StabilityMetrics)
        self.assertEqual(metrics.gpu_utilization, 95.0)
        self.assertAlmostEqual(metrics.gpu_memory_used_gb, 5000/1024, places=2)
        self.assertEqual(metrics.gpu_memory_percent, 80.0)
        self.assertEqual(metrics.gpu_temperature, 70.0)
        self.assertEqual(metrics.cpu_percent, 75.5)
        self.assertEqual(metrics.ram_percent, 60.2)
        self.assertEqual(metrics.loss_value, 1.234)
        self.assertEqual(metrics.compilation_time_ms, 1000.0)
        
        # 检查吞吐量计算
        expected_throughput = self.tester.batch_size / (30.8 / 1000)
        self.assertAlmostEqual(metrics.throughput_samples_per_sec, expected_throughput, places=0)
    
    def test_detect_performance_degradation_insufficient_data(self):
        """测试性能衰减检测 - 数据不足"""
        result = self.tester.detect_performance_degradation()
        self.assertEqual(result['status'], 'insufficient_data')
    
    def test_detect_performance_degradation_stable(self):
        """测试性能衰减检测 - 稳定状态"""
        # 创建稳定的指标历史
        from datetime import datetime
        
        for i in range(200):
            metrics = StabilityMetrics(
                timestamp=datetime.now(),
                gpu_utilization=95.0,
                gpu_memory_used_gb=4.0,
                gpu_memory_percent=65.0,
                gpu_temperature=70.0,
                cpu_percent=75.0,
                ram_percent=60.0,
                throughput_samples_per_sec=25000.0,  # 稳定的吞吐量
                loss_value=1.0,
                compilation_time_ms=1000.0,
                forward_time_ms=10.0,
                backward_time_ms=15.0,
                step_time_ms=5.0
            )
            self.tester.metrics_history.append(metrics)
        
        result = self.tester.detect_performance_degradation()
        
        self.assertEqual(result['status'], 'stable')
        self.assertEqual(len(result['issues']), 0)
        self.assertLess(abs(result['throughput_degradation_percent']), 1.0)
    
    def test_detect_performance_degradation_degraded(self):
        """测试性能衰减检测 - 性能衰减"""
        from datetime import datetime
        
        # 创建早期高性能指标
        for i in range(100):
            metrics = StabilityMetrics(
                timestamp=datetime.now(),
                gpu_utilization=95.0,
                gpu_memory_used_gb=4.0,
                gpu_memory_percent=60.0,  # 早期较低内存使用
                gpu_temperature=70.0,
                cpu_percent=75.0,
                ram_percent=60.0,
                throughput_samples_per_sec=25000.0,  # 早期高吞吐量
                loss_value=1.0,
                compilation_time_ms=1000.0,
                forward_time_ms=10.0,
                backward_time_ms=15.0,
                step_time_ms=5.0
            )
            self.tester.metrics_history.append(metrics)
        
        # 创建后期低性能指标
        for i in range(100):
            metrics = StabilityMetrics(
                timestamp=datetime.now(),
                gpu_utilization=95.0,
                gpu_memory_used_gb=4.0,
                gpu_memory_percent=75.0,  # 后期较高内存使用
                gpu_temperature=70.0,
                cpu_percent=75.0,
                ram_percent=60.0,
                throughput_samples_per_sec=22000.0,  # 后期低吞吐量
                loss_value=1.0,
                compilation_time_ms=1000.0,
                forward_time_ms=10.0,
                backward_time_ms=15.0,
                step_time_ms=5.0
            )
            self.tester.metrics_history.append(metrics)
        
        result = self.tester.detect_performance_degradation()
        
        self.assertEqual(result['status'], 'degraded')
        self.assertGreater(len(result['issues']), 0)
        self.assertGreater(result['throughput_degradation_percent'], 5.0)
    
    def test_save_report(self):
        """测试报告保存"""
        test_report = {
            'test_summary': {'status': 'completed'},
            'performance_stats': {'avg_throughput': 25000},
            'stability_rating': 'excellent'
        }
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # 临时修改报告目录
            original_path = Path("reports")
            test_path = Path(temp_dir) / "reports"
            
            with patch('pathlib.Path') as mock_path:
                mock_path.return_value = test_path
                
                # 确保目录存在
                test_path.mkdir(exist_ok=True)
                
                # 保存报告
                report_path = self.tester.save_report(test_report, "test_report.json")
                
                # 检查文件是否创建
                self.assertTrue(Path(report_path).exists())
                
                # 检查文件内容
                with open(report_path, 'r') as f:
                    loaded_report = json.load(f)
                
                self.assertEqual(loaded_report['test_summary']['status'], 'completed')
                self.assertEqual(loaded_report['performance_stats']['avg_throughput'], 25000)
                self.assertEqual(loaded_report['stability_rating'], 'excellent')


if __name__ == '__main__':
    unittest.main()
