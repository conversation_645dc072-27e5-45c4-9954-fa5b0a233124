"""
GPU Timeline Profiler测试
使用torch.profiler监控GPU timeline和数据加载器性能
"""

import time
import sys
from pathlib import Path
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List
import logging
from torch.utils.data import Dataset, DataLoader
from torch.profiler import profile, record_function, ProfilerActivity, schedule

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))


class TestGPUTimelineProfiler:
    """GPU Timeline Profiler测试类"""
    
    def setup_method(self, method):
        """初始化测试"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger = logging.getLogger(__name__)
        
        print(f"🔧 测试设备: {self.device}")
        if torch.cuda.is_available():
            print(f"🔧 GPU: {torch.cuda.get_device_name(0)}")
            print(f"🔧 CUDA版本: {torch.version.cuda}")
    
    def create_test_dataset(self, num_samples: int = 50000, input_size: int = 59):
        """创建测试数据集"""
        class SimpleDataset(Dataset):
            def __init__(self, features, labels):
                self.features = torch.from_numpy(features)
                self.labels = torch.from_numpy(labels)
            
            def __len__(self):
                return len(self.features)
            
            def __getitem__(self, idx):
                return self.features[idx], self.labels[idx]
        
        features = np.random.randn(num_samples, input_size).astype(np.float32)
        labels = np.random.randint(0, 3, size=num_samples, dtype=np.int64)
        
        return SimpleDataset(features, labels)
    
    def create_test_model(self, input_size: int = 59) -> nn.Module:
        """创建测试模型"""
        model = nn.Sequential(
            nn.Linear(input_size, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)
        )
        return model.to(self.device)
    
    def profile_baseline_dataloader(self, batch_size: int = 8192):
        """Profile基线数据加载器"""
        print(f"\n🧪 Profile基线数据加载器 (4 workers)")
        
        if not torch.cuda.is_available():
            print("⚠️ CUDA不可用，跳过GPU profiling")
            return
        
        dataset = self.create_test_dataset(50000)
        model = self.create_test_model()
        
        # 基线数据加载器
        loader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=4,
            pin_memory=False,
            persistent_workers=False,
            prefetch_factor=2
        )
        
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        # 创建profiler
        with profile(
            activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
            schedule=schedule(wait=1, warmup=1, active=3, repeat=1),
            on_trace_ready=lambda prof: prof.export_chrome_trace("logs/baseline_dataloader_trace.json"),
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        ) as prof:
            
            for step, (features, labels) in enumerate(loader):
                with record_function("data_loading"):
                    features = features.to(self.device, non_blocking=False)  # 基线不使用non_blocking
                    labels = labels.to(self.device, non_blocking=False)
                
                with record_function("forward_pass"):
                    outputs = model(features)
                    loss = criterion(outputs, labels)
                
                with record_function("backward_pass"):
                    optimizer.zero_grad()
                    loss.backward()
                    optimizer.step()
                
                prof.step()
                
                if step >= 5:  # 只profile几个步骤
                    break
        
        print(f"📊 基线profiler结果已保存到: logs/baseline_dataloader_trace.json")
    
    def profile_optimized_dataloader(self, batch_size: int = 8192):
        """Profile优化后的数据加载器"""
        print(f"\n🧪 Profile优化数据加载器 (16 workers)")
        
        if not torch.cuda.is_available():
            print("⚠️ CUDA不可用，跳过GPU profiling")
            return
        
        dataset = self.create_test_dataset(50000)
        model = self.create_test_model()
        
        # 优化数据加载器
        loader = DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=True,
            num_workers=16,
            pin_memory=True,
            persistent_workers=True,
            prefetch_factor=8,
            multiprocessing_context='fork'
        )
        
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        # 创建profiler
        with profile(
            activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA],
            schedule=schedule(wait=1, warmup=1, active=3, repeat=1),
            on_trace_ready=lambda prof: prof.export_chrome_trace("logs/optimized_dataloader_trace.json"),
            record_shapes=True,
            profile_memory=True,
            with_stack=True
        ) as prof:
            
            for step, (features, labels) in enumerate(loader):
                with record_function("data_loading_optimized"):
                    features = features.to(self.device, non_blocking=True)  # 使用non_blocking
                    labels = labels.to(self.device, non_blocking=True)
                
                with record_function("forward_pass"):
                    outputs = model(features)
                    loss = criterion(outputs, labels)
                
                with record_function("backward_pass"):
                    optimizer.zero_grad(set_to_none=True)  # 优化的zero_grad
                    loss.backward()
                    optimizer.step()
                
                prof.step()
                
                if step >= 5:  # 只profile几个步骤
                    break
        
        print(f"📊 优化profiler结果已保存到: logs/optimized_dataloader_trace.json")
    
    def analyze_profiler_results(self):
        """分析profiler结果"""
        print(f"\n🧪 分析Profiler结果")
        
        # 这里可以添加更详细的profiler结果分析
        # 比如解析JSON文件，提取关键指标等
        
        print(f"📊 Profiler分析:")
        print(f"   - 基线trace: logs/baseline_dataloader_trace.json")
        print(f"   - 优化trace: logs/optimized_dataloader_trace.json")
        print(f"   - 可以在Chrome浏览器中打开chrome://tracing/查看详细timeline")
        
        # 提供分析建议
        print(f"\n🔍 分析建议:")
        print(f"   1. 查看GPU利用率时间线，寻找空闲时间")
        print(f"   2. 对比数据加载时间，验证优化效果")
        print(f"   3. 检查CPU-GPU数据传输重叠情况")
        print(f"   4. 观察内存分配模式")
    
    def benchmark_dataloader_configurations(self):
        """基准测试不同数据加载器配置"""
        print(f"\n🧪 基准测试数据加载器配置")
        
        dataset = self.create_test_dataset(100000)
        
        configs = [
            {'name': 'baseline', 'num_workers': 4, 'pin_memory': False, 'prefetch_factor': 2},
            {'name': 'optimized_8w', 'num_workers': 8, 'pin_memory': True, 'prefetch_factor': 4},
            {'name': 'optimized_12w', 'num_workers': 12, 'pin_memory': True, 'prefetch_factor': 6},
            {'name': 'optimized_16w', 'num_workers': 16, 'pin_memory': True, 'prefetch_factor': 8},
        ]
        
        results = {}
        
        for config in configs:
            print(f"\n🔍 测试配置: {config['name']}")
            
            try:
                loader = DataLoader(
                    dataset,
                    batch_size=8192,
                    shuffle=True,
                    num_workers=config['num_workers'],
                    pin_memory=config['pin_memory'],
                    persistent_workers=True,
                    prefetch_factor=config['prefetch_factor'],
                    drop_last=True
                )
                
                # 测试数据加载速度
                start_time = time.time()
                total_samples = 0
                
                for i, (features, labels) in enumerate(loader):
                    total_samples += features.size(0)
                    if i >= 20:  # 测试20个批次
                        break
                
                end_time = time.time()
                total_time = end_time - start_time
                throughput = total_samples / total_time
                
                results[config['name']] = {
                    'throughput': throughput,
                    'total_time': total_time,
                    'config': config
                }
                
                print(f"   - 吞吐量: {throughput:.0f} samples/s")
                print(f"   - 总时间: {total_time:.3f}s")
                
            except Exception as e:
                print(f"   - ❌ 配置失败: {e}")
                results[config['name']] = None
        
        # 对比结果
        print(f"\n📊 配置对比结果:")
        baseline_throughput = results.get('baseline', {}).get('throughput', 0)
        
        for name, result in results.items():
            if result:
                if baseline_throughput > 0:
                    improvement = (result['throughput'] - baseline_throughput) / baseline_throughput * 100
                    print(f"   - {name}: {result['throughput']:.0f} samples/s ({improvement:+.1f}%)")
                else:
                    print(f"   - {name}: {result['throughput']:.0f} samples/s")
        
        return results
    
    def test_gpu_utilization_with_profiler(self):
        """测试GPU利用率与profiler结合"""
        print(f"\n🧪 测试GPU利用率监控")
        
        if not torch.cuda.is_available():
            print("⚠️ CUDA不可用，跳过GPU利用率测试")
            return
        
        dataset = self.create_test_dataset(50000)
        model = self.create_test_model()
        
        # 使用优化配置
        loader = DataLoader(
            dataset,
            batch_size=8192,
            shuffle=True,
            num_workers=16,
            pin_memory=True,
            persistent_workers=True,
            prefetch_factor=8
        )
        
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        # 监控GPU利用率
        print(f"🔍 开始GPU利用率监控...")
        
        start_time = time.time()
        total_samples = 0
        
        for i, (features, labels) in enumerate(loader):
            # 数据传输
            features = features.to(self.device, non_blocking=True)
            labels = labels.to(self.device, non_blocking=True)
            
            # 前向传播
            outputs = model(features)
            loss = criterion(outputs, labels)
            
            # 反向传播
            optimizer.zero_grad(set_to_none=True)
            loss.backward()
            optimizer.step()
            
            total_samples += features.size(0)
            
            # 每10个批次输出一次GPU内存使用情况
            if i % 10 == 0:
                memory_allocated = torch.cuda.memory_allocated() / (1024**3)  # GB
                memory_reserved = torch.cuda.memory_reserved() / (1024**3)   # GB
                print(f"   批次 {i}: GPU内存 {memory_allocated:.2f}GB / {memory_reserved:.2f}GB")
            
            if i >= 30:  # 测试30个批次
                break
        
        end_time = time.time()
        total_time = end_time - start_time
        throughput = total_samples / total_time
        
        print(f"\n📊 GPU利用率测试结果:")
        print(f"   - 总吞吐量: {throughput:.0f} samples/s")
        print(f"   - 总时间: {total_time:.3f}s")
        print(f"   - 峰值GPU内存: {torch.cuda.max_memory_allocated() / (1024**3):.2f}GB")


def main():
    """主测试函数"""
    print("🚀 开始GPU Timeline Profiler测试...")
    
    # 确保logs目录存在
    Path("logs").mkdir(exist_ok=True)
    
    try:
        test_instance = TestGPUTimelineProfiler()
        
        # 测试1: Profile基线数据加载器
        print("\n" + "="*60)
        print("测试1: Profile基线数据加载器")
        test_instance.profile_baseline_dataloader()
        
        # 测试2: Profile优化数据加载器
        print("\n" + "="*60)
        print("测试2: Profile优化数据加载器")
        test_instance.profile_optimized_dataloader()
        
        # 测试3: 分析profiler结果
        print("\n" + "="*60)
        print("测试3: 分析Profiler结果")
        test_instance.analyze_profiler_results()
        
        # 测试4: 基准测试配置
        print("\n" + "="*60)
        print("测试4: 基准测试数据加载器配置")
        test_instance.benchmark_dataloader_configurations()
        
        # 测试5: GPU利用率监控
        print("\n" + "="*60)
        print("测试5: GPU利用率监控")
        test_instance.test_gpu_utilization_with_profiler()
        
        print("\n✅ GPU Timeline Profiler测试完成！")
        print("🎯 请查看logs/目录下的trace文件，在Chrome浏览器中打开chrome://tracing/进行详细分析")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
