"""
当前优化验证测试
验证已实施的所有优化效果
"""

import asyncio
import pytest
import os
import time
import torch
import torch.nn as nn
import subprocess
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.training.unified_trainer import UnifiedTrainer, TrainerConfig


def get_gpu_metrics():
    """获取GPU指标"""
    try:
        result = subprocess.run([
            'nvidia-smi', 
            '--query-gpu=utilization.gpu,memory.used,memory.total,temperature.gpu',
            '--format=csv,noheader,nounits'
        ], capture_output=True, text=True, timeout=2)
        
        if result.returncode == 0:
            values = result.stdout.strip().split(', ')
            return {
                'gpu_utilization': float(values[0]),
                'memory_used_mb': float(values[1]),
                'memory_total_mb': float(values[2]),
                'memory_percent': (float(values[1]) / float(values[2])) * 100,
                'temperature': float(values[3])
            }
    except:
        pass
    
    return None


def create_optimized_model():
    """创建优化后的模型"""
    return nn.Sequential(
        nn.Linear(59, 4096),
        nn.ReLU(),
        nn.Dropout(0.2),
        nn.Linear(4096, 4096),
        nn.ReLU(),
        nn.Dropout(0.2),
        nn.Linear(4096, 2048),
        nn.ReLU(),
        nn.Dropout(0.1),
        nn.Linear(2048, 1024),
        nn.ReLU(),
        nn.Linear(1024, 512),
        nn.ReLU(),
        nn.Linear(512, 3)
    )


def create_optimized_dataloader(batch_size, num_batches=100):
    """创建优化后的数据加载器"""
    class OptimizedDataset(torch.utils.data.Dataset):
        def __init__(self, num_samples):
            self.num_samples = num_samples
        
        def __len__(self):
            return self.num_samples
        
        def __getitem__(self, idx):
            return {
                'features': torch.randn(59),
                'labels': torch.randint(0, 3, (1,)).item()
            }
    
    dataset = OptimizedDataset(batch_size * num_batches)
    
    # 使用优化后的数据加载器配置
    dataloader = torch.utils.data.DataLoader(
        dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=2,  # 恢复并行加载，但使用较少的工作进程以适应测试环境
        pin_memory=True,
        persistent_workers=False, # 修复：在测试中禁用持久化worker，防止资源泄漏
        prefetch_factor=4,
        drop_last=True,
        multiprocessing_context='fork'
    )
    return dataloader


@pytest.mark.skip(reason="This GPU-intensive test is too slow for regular CI, causing timeouts.")
@pytest.mark.asyncio
async def test_current_optimizations():
    """测试当前所有优化的综合效果"""
    print("🧪 测试当前所有优化的综合效果")
    
    force_cpu = os.environ.get('FORCE_CPU_TESTING', 'false').lower() == 'true'
    if force_cpu:
        pytest.skip("Skipping GPU-specific test: CPU forced.")
    if not torch.cuda.is_available():
        pytest.skip("Skipping GPU-specific test: CUDA not available.")
    device = torch.device('cuda')
    
    # 创建优化后的模型
    model = create_optimized_model()
    
    # 创建高性能训练器（包含所有优化）
    train_config = TrainerConfig(
        use_amp=True,
        use_compile=False,  # Disable torch.compile due to test instability
        learning_rate=1e-3,
        epochs=1, # 在测试中我们只关心一个epoch的性能
        device=device
    )
    trainer = UnifiedTrainer(
        model=model,
        config=train_config
    )
    
    # 测试不同批次大小
    batch_sizes = [16384, 32768, 65536]
    results = {}
    
    for batch_size in batch_sizes:
        print(f"\n🔍 测试批次大小: {batch_size}")
        
        try:
            # 创建数据加载器
            train_loader = create_optimized_dataloader(batch_size, num_batches=50)
            
            
            # 运行一个完整的epoch来进行测试
            start_time = time.time()
            await trainer.train(train_loader, val_loader=None)
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # 从trainer的状态中获取样本总数
            total_samples = trainer.state.step * batch_size
            throughput = total_samples / total_time if total_time > 0 else 0
            
            # 获取GPU指标
            gpu_metrics = None  # get_gpu_metrics() # DEBUG: 暂时禁用，以验证是否是 subprocess 导致挂起
            
            results[batch_size] = {
                'throughput': throughput,
                'total_time': total_time,
                'total_samples': total_samples,
                'gpu_metrics': gpu_metrics
            }
            
            if gpu_metrics:
                print(f"✅ 批次大小 {batch_size}:")
                print(f"   - 吞吐量: {throughput:.0f} samples/s")
                print(f"   - GPU利用率: {gpu_metrics['gpu_utilization']:.1f}%")
                print(f"   - 内存使用: {gpu_metrics['memory_percent']:.1f}%")
                print(f"   - GPU温度: {gpu_metrics['temperature']:.1f}°C")
            else:
                print(f"✅ 批次大小 {batch_size}: {throughput:.0f} samples/s")
            
            # 清理内存
            del train_loader
            torch.cuda.empty_cache()
            
        except RuntimeError as e:
            if "out of memory" in str(e):
                print(f"❌ 批次大小 {batch_size}: 内存不足")
                torch.cuda.empty_cache()
                break
            else:
                raise e
    
    return results


@pytest.mark.skip(reason="This GPU-intensive test is too slow for regular CI, causing timeouts.")
def test_mixed_precision_effect():
    """测试混合精度效果"""
    print(f"\n🧪 测试混合精度效果对比")
    
    force_cpu = os.environ.get('FORCE_CPU_TESTING', 'false').lower() == 'true'
    if force_cpu:
        pytest.skip("Skipping GPU-specific test: CPU forced.")
    if not torch.cuda.is_available():
        pytest.skip("Skipping GPU-specific test: CUDA not available.")
    device = torch.device('cuda')
    batch_size = 32768
    
    # 测试1: 不使用混合精度
    print("🔍 测试1: FP32 (不使用混合精度)")
    model_fp32 = create_optimized_model().to(device)
    
    compiled_model_fp32 = model_fp32
    
    features = torch.randn(batch_size, 59, device=device)
    labels = torch.randint(0, 3, (batch_size,), device=device)
    
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.AdamW(compiled_model_fp32.parameters(), lr=1e-3)
    
    # 预热
    for _ in range(5):
        optimizer.zero_grad(set_to_none=True)
        outputs = compiled_model_fp32(features)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()
    
    torch.cuda.synchronize()
    
    # 性能测试
    start_time = time.time()
    for _ in range(50):
        optimizer.zero_grad(set_to_none=True)
        outputs = compiled_model_fp32(features)
        loss = criterion(outputs, labels)
        loss.backward()
        optimizer.step()
    
    torch.cuda.synchronize()
    fp32_time = time.time() - start_time
    fp32_throughput = (batch_size * 50) / fp32_time
    
    print(f"📊 FP32性能: {fp32_throughput:.0f} samples/s")
    
    # 清理
    del model_fp32, compiled_model_fp32, optimizer
    torch.cuda.empty_cache()
    
    # 测试2: 使用混合精度
    print("\n🔍 测试2: FP16 (混合精度)")
    model_fp16 = create_optimized_model().to(device)
    
    compiled_model_fp16 = model_fp16
    
    optimizer = torch.optim.AdamW(compiled_model_fp16.parameters(), lr=1e-3)
    scaler = torch.amp.GradScaler('cuda')
    
    # 预热
    for _ in range(5):
        optimizer.zero_grad(set_to_none=True)
        
        with torch.autocast(device_type='cuda', dtype=torch.float16):
            outputs = compiled_model_fp16(features)
            loss = criterion(outputs, labels)
        
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()
    
    torch.cuda.synchronize()
    
    # 性能测试
    start_time = time.time()
    for _ in range(50):
        optimizer.zero_grad(set_to_none=True)
        
        with torch.autocast(device_type='cuda', dtype=torch.float16):
            outputs = compiled_model_fp16(features)
            loss = criterion(outputs, labels)
        
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()
    
    torch.cuda.synchronize()
    fp16_time = time.time() - start_time
    fp16_throughput = (batch_size * 50) / fp16_time
    
    print(f"📊 FP16性能: {fp16_throughput:.0f} samples/s")
    
    # 计算提升
    improvement = (fp16_throughput - fp32_throughput) / fp32_throughput * 100
    print(f"📊 混合精度提升: {improvement:+.1f}%")
    
    return {
        'fp32_throughput': fp32_throughput,
        'fp16_throughput': fp16_throughput,
        'improvement': improvement
    }


async def main():
    """主测试函数"""
    print("🚀 开始当前优化验证测试...")
    print("="*60)
    
    force_cpu = os.environ.get('FORCE_CPU_TESTING', 'false').lower() == 'true'
    if force_cpu:
        print("❌ 已强制使用CPU，无法进行GPU优化测试")
        return
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，无法进行GPU优化测试")
        return
    
    print(f"🔧 GPU: {torch.cuda.get_device_name(0)}")
    print(f"🔧 PyTorch版本: {torch.__version__}")
    
    # 测试1: 当前所有优化的综合效果
    print("\n" + "="*60)
    print("测试1: 当前所有优化的综合效果")
    optimization_results = await test_current_optimizations()
    
    # 测试2: 混合精度效果
    print("\n" + "="*60)
    print("测试2: 混合精度效果对比")
    mixed_precision_results = test_mixed_precision_effect()
    
    # 最终总结
    print("\n" + "="*60)
    print("🎯 当前优化验证总结")
    print("="*60)
    
    if optimization_results:
        best_batch_size = max(optimization_results.keys(), 
                             key=lambda k: optimization_results[k]['throughput'])
        best_result = optimization_results[best_batch_size]
        
        print(f"📊 最佳批次大小配置: {best_batch_size}")
        print(f"   - 吞吐量: {best_result['throughput']:.0f} samples/s")
        
        if best_result['gpu_metrics']:
            gpu_metrics = best_result['gpu_metrics']
            print(f"   - GPU利用率: {gpu_metrics['gpu_utilization']:.1f}%")
            print(f"   - 内存使用: {gpu_metrics['memory_percent']:.1f}%")
            print(f"   - GPU温度: {gpu_metrics['temperature']:.1f}°C")
    
    print(f"\n📊 混合精度优化:")
    print(f"   - FP32吞吐量: {mixed_precision_results['fp32_throughput']:.0f} samples/s")
    print(f"   - FP16吞吐量: {mixed_precision_results['fp16_throughput']:.0f} samples/s")
    print(f"   - 性能提升: {mixed_precision_results['improvement']:+.1f}%")
    
    # 评估整体优化效果
    if optimization_results:
        max_throughput = max(r['throughput'] for r in optimization_results.values())
        max_gpu_util = max(r['gpu_metrics']['gpu_utilization'] 
                          for r in optimization_results.values() 
                          if r['gpu_metrics'])
        
        print(f"\n🎯 整体优化评估:")
        print(f"   - 最大吞吐量: {max_throughput:.0f} samples/s")
        print(f"   - 最大GPU利用率: {max_gpu_util:.1f}%")
        
        if max_gpu_util >= 95 and max_throughput >= 20000:
            print(f"✅ 优化效果优秀！GPU利用率和吞吐量都达到预期")
        elif max_gpu_util >= 90 or max_throughput >= 15000:
            print(f"🟡 优化效果良好，还有进一步提升空间")
        else:
            print(f"❌ 优化效果有限，需要进一步调优")
    
    print("\n✅ 当前优化验证测试完成！")


if __name__ == "__main__":
    asyncio.run(main())
