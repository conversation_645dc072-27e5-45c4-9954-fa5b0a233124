"""
torch.compile集成测试
验证torch.compile在实际应用中的启用状态和性能效果
"""

import time
import sys
import os
from pathlib import Path
import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List
import logging

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.training import create_trainer
from src.core.application import CryptoMLApplication

# 为测试重新定义已删除的常量
# 在新的框架中，这些是通过配置字典控制的
TORCH_COMPILE_SAFE = hasattr(torch, 'compile')
TORCH_COMPILE_BACKEND = None 
TORCH_COMPILE_MODE = 'reduce-overhead'


class TestTorchCompileIntegration:
    """torch.compile集成测试类"""
    
    def setup_method(self, method):
        """初始化测试"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger = logging.getLogger(__name__)
        
        print(f"🔧 测试设备: {self.device}")
        if torch.cuda.is_available():
            print(f"🔧 GPU: {torch.cuda.get_device_name(0)}")
            print(f"🔧 PyTorch版本: {torch.__version__}")
    
    def test_torch_compile_environment_setup(self):
        """测试torch.compile环境设置"""
        print(f"\n🧪 测试torch.compile环境设置")
        
        # 检查环境变量
        torch_compile_safe = os.environ.get('TORCH_COMPILE_SAFE', 'false').lower() == 'true'
        print(f"📊 环境变量 TORCH_COMPILE_SAFE: {torch_compile_safe}")
        
        # 检查PyTorch版本
        pytorch_version = torch.__version__
        print(f"📊 PyTorch版本: {pytorch_version}")
        
        # 检查torch.compile可用性
        has_compile = hasattr(torch, 'compile')
        print(f"📊 torch.compile可用: {has_compile}")
        
        # 检查训练器中的配置
        print(f"📊 训练器配置:")
        print(f"   - TORCH_COMPILE_SAFE: {TORCH_COMPILE_SAFE}")
        print(f"   - TORCH_COMPILE_BACKEND: {TORCH_COMPILE_BACKEND}")
        print(f"   - TORCH_COMPILE_MODE: {TORCH_COMPILE_MODE}")
        
        return {
            'env_enabled': torch_compile_safe,
            'pytorch_has_compile': has_compile,
            'trainer_safe': TORCH_COMPILE_SAFE,
            'backend': TORCH_COMPILE_BACKEND,
            'mode': TORCH_COMPILE_MODE
        }
    
    def test_model_compilation_in_trainer(self):
        """测试训练器中的模型编译"""
        print(f"\n🧪 测试训练器中的模型编译")
        
        # 创建简单模型
        model = nn.Sequential(
            nn.Linear(59, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)
        )
        
        # 使用 create_trainer
        train_config = {
            'use_amp': True,
            'use_compile': True, # 启用编译
            'learning_rate': 1e-3,
            'epochs': 1
        }
        trainer = create_trainer(
            model=model,
            train_config=train_config,
            device=self.device
        )
        
        # 检查模型是否被编译
        # UnifiedTrainer 在 __init__ 中进行编译
        is_compiled = hasattr(trainer.model, '_orig_mod') or isinstance(trainer.model, torch._dynamo.eval_frame.OptimizedModule)
        print(f"📊 模型编译状态: {'✅ 已编译' if is_compiled else '❌ 未编译'}")
        
        if is_compiled:
            print(f"📊 编译后模型类型: {type(trainer.model)}")
            if hasattr(trainer.model, '_orig_mod'):
                 print(f"📊 原始模型类型: {type(trainer.model._orig_mod)}")
        
        return {
            'is_compiled': is_compiled,
            'model_type': str(type(trainer.model)),
            'trainer_created': True
        }
    
    def test_compiled_vs_uncompiled_performance(self):
        """测试编译vs未编译模型性能"""
        print(f"\n🧪 测试编译vs未编译模型性能")
        
        # 创建测试数据
        batch_size = 8192
        num_batches = 30
        
        test_data = []
        for _ in range(num_batches):
            features = torch.randn(batch_size, 59, device=self.device)
            labels = torch.randint(0, 3, (batch_size,), device=self.device)
            test_data.append((features, labels))
        
        print(f"📊 测试数据: {num_batches}个批次, 每批次{batch_size}样本")
        
        # 测试1: 未编译模型
        print("\n🔍 测试1: 未编译模型")
        model_uncompiled = nn.Sequential(
            nn.Linear(59, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)
        ).to(self.device)
        
        uncompiled_time = self._benchmark_model(model_uncompiled, test_data, "未编译模型")
        
        # 清理内存
        del model_uncompiled
        torch.cuda.empty_cache() if torch.cuda.is_available() else None
        
        # 测试2: 编译模型
        print("\n🔍 测试2: 编译模型")
        model_compiled = nn.Sequential(
            nn.Linear(59, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)
        ).to(self.device)
        
        # 编译模型
        if TORCH_COMPILE_SAFE:
            compile_start = time.time()
            compile_kwargs = {'mode': TORCH_COMPILE_MODE or 'reduce-overhead'}
            if TORCH_COMPILE_BACKEND:
                compile_kwargs['backend'] = TORCH_COMPILE_BACKEND
            
            model_compiled = torch.compile(model_compiled, **compile_kwargs)
            compile_time = time.time() - compile_start
            print(f"📈 模型编译耗时: {compile_time:.3f}s")
        else:
            print("⚠️ torch.compile未启用，跳过编译测试")
            return None
        
        compiled_time = self._benchmark_model(model_compiled, test_data, "编译模型")
        
        # 性能对比
        improvement = (uncompiled_time - compiled_time) / uncompiled_time * 100
        throughput_improvement = (1/compiled_time - 1/uncompiled_time) / (1/uncompiled_time) * 100
        
        print(f"\n📊 性能对比结果:")
        print(f"   - 未编译模型时间: {uncompiled_time:.3f}s")
        print(f"   - 编译模型时间: {compiled_time:.3f}s")
        print(f"   - 时间改进: {improvement:+.1f}%")
        print(f"   - 吞吐量提升: {throughput_improvement:+.1f}%")
        
        if throughput_improvement > 10:
            print(f"✅ torch.compile显著提升性能!")
        elif throughput_improvement > 0:
            print(f"🟡 torch.compile轻微提升性能")
        else:
            print(f"❌ torch.compile未提升性能")
        
        return {
            'uncompiled_time': uncompiled_time,
            'compiled_time': compiled_time,
            'time_improvement': improvement,
            'throughput_improvement': throughput_improvement
        }
    
    def _benchmark_model(self, model, test_data, model_name):
        """基准测试模型性能"""
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        # 预热
        for i in range(3):
            features, labels = test_data[i]
            outputs = model(features)
            loss = criterion(outputs, labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        
        # 同步GPU
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        # 性能测试
        start_time = time.time()
        
        for features, labels in test_data:
            outputs = model(features)
            loss = criterion(outputs, labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        
        # 同步GPU
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"📈 {model_name}性能: {total_time:.3f}s")
        
        return total_time
    
    def test_application_torch_compile_integration(self):
        """测试应用程序中的torch.compile集成"""
        print(f"\n🧪 测试应用程序中的torch.compile集成")
        
        try:
            # 检查配置文件中的编译设置
            from src.utils.config import load_config
            config = load_config()
            
            compilation_config = config.get('optimization', {}).get('compilation', {})
            print(f"📊 配置文件编译设置:")
            print(f"   - enabled: {compilation_config.get('enabled', False)}")
            print(f"   - mode: {compilation_config.get('mode', 'default')}")
            print(f"   - dynamic: {compilation_config.get('dynamic', True)}")
            print(f"   - fullgraph: {compilation_config.get('fullgraph', False)}")
            
            return {
                'config_enabled': compilation_config.get('enabled', False),
                'config_mode': compilation_config.get('mode', 'default'),
                'config_loaded': True
            }
            
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            return {
                'config_loaded': False,
                'error': str(e)
            }
    
    def test_epoch_time_measurement(self):
        """测试epoch时间测量"""
        print(f"\n🧪 测试epoch时间测量")
        
        # 创建简单的训练循环来测量epoch时间
        model = nn.Sequential(
            nn.Linear(59, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)
        ).to(self.device)
        
        # 创建测试数据
        batch_size = 8192
        num_batches = 20  # 模拟一个小epoch
        
        test_data = []
        for _ in range(num_batches):
            features = torch.randn(batch_size, 59, device=self.device)
            labels = torch.randint(0, 3, (batch_size,), device=self.device)
            test_data.append((features, labels))
        
        # 测试未编译模型的epoch时间
        print("🔍 测试未编译模型epoch时间...")
        uncompiled_epoch_time = self._measure_epoch_time(model, test_data, "未编译")
        
        # 编译模型并测试
        if TORCH_COMPILE_SAFE:
            print("🔍 测试编译模型epoch时间...")
            compile_kwargs = {'mode': TORCH_COMPILE_MODE or 'reduce-overhead'}
            if TORCH_COMPILE_BACKEND:
                compile_kwargs['backend'] = TORCH_COMPILE_BACKEND
            
            compiled_model = torch.compile(model, **compile_kwargs)
            compiled_epoch_time = self._measure_epoch_time(compiled_model, test_data, "编译")
            
            # 计算改进
            epoch_improvement = (uncompiled_epoch_time - compiled_epoch_time) / uncompiled_epoch_time * 100
            
            print(f"\n📊 Epoch时间对比:")
            print(f"   - 未编译epoch时间: {uncompiled_epoch_time:.3f}s")
            print(f"   - 编译epoch时间: {compiled_epoch_time:.3f}s")
            print(f"   - Epoch时间改进: {epoch_improvement:+.1f}%")
            
            return {
                'uncompiled_epoch_time': uncompiled_epoch_time,
                'compiled_epoch_time': compiled_epoch_time,
                'epoch_improvement': epoch_improvement
            }
        else:
            print("⚠️ torch.compile未启用，无法测试编译模型")
            return {
                'uncompiled_epoch_time': uncompiled_epoch_time,
                'compiled_epoch_time': None,
                'epoch_improvement': None
            }
    
    def _measure_epoch_time(self, model, test_data, model_type):
        """测量epoch时间"""
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
        
        # 预热
        for i in range(2):
            features, labels = test_data[i]
            outputs = model(features)
            loss = criterion(outputs, labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        
        # 同步GPU
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        # 测量epoch时间
        epoch_start = time.time()
        
        for features, labels in test_data:
            outputs = model(features)
            loss = criterion(outputs, labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
        
        # 同步GPU
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        epoch_time = time.time() - epoch_start
        
        print(f"📈 {model_type}模型epoch时间: {epoch_time:.3f}s")
        
        return epoch_time


def main():
    """主测试函数"""
    print("🚀 开始torch.compile集成测试...")
    
    try:
        test_instance = TestTorchCompileIntegration()
        
        # 测试1: 环境设置检查
        print("\n" + "="*60)
        print("测试1: torch.compile环境设置检查")
        env_results = test_instance.test_torch_compile_environment_setup()
        
        # 测试2: 训练器中的模型编译
        print("\n" + "="*60)
        print("测试2: 训练器中的模型编译")
        trainer_results = test_instance.test_model_compilation_in_trainer()
        
        # 测试3: 性能对比
        print("\n" + "="*60)
        print("测试3: 编译vs未编译性能对比")
        performance_results = test_instance.test_compiled_vs_uncompiled_performance()
        
        # 测试4: 应用程序集成
        print("\n" + "="*60)
        print("测试4: 应用程序torch.compile集成")
        app_results = test_instance.test_application_torch_compile_integration()
        
        # 测试5: Epoch时间测量
        print("\n" + "="*60)
        print("测试5: Epoch时间测量")
        epoch_results = test_instance.test_epoch_time_measurement()
        
        # 最终总结
        print("\n" + "="*60)
        print("🎯 torch.compile集成测试总结")
        
        if env_results['trainer_safe'] and trainer_results['is_compiled']:
            print("✅ torch.compile已成功启用")
            
            if performance_results and performance_results['throughput_improvement'] > 0:
                print(f"✅ 性能提升: {performance_results['throughput_improvement']:+.1f}%")
            
            if epoch_results and epoch_results['epoch_improvement']:
                print(f"✅ Epoch时间改进: {epoch_results['epoch_improvement']:+.1f}%")
            
            print("🚀 torch.compile优化已成功集成到系统中!")
        else:
            print("❌ torch.compile未正确启用，需要检查配置")
        
        print("\n✅ torch.compile集成测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
