#!/usr/bin/env python3
"""
真实场景torch.compile验证测试
使用更大的模型和更多迭代来验证torch.compile效果
"""

import os
import sys
import time
import torch
import torch.nn as nn
import numpy as np

def create_realistic_model(input_size=59):
    """创建更真实的模型（类似实际使用的模型）"""
    return nn.Sequential(
        nn.Linear(input_size, 512),
        nn.ReLU(),
        nn.Dropout(0.2),
        nn.Linear(512, 512),
        nn.ReLU(),
        nn.Dropout(0.2),
        nn.Linear(512, 256),
        nn.ReLU(),
        nn.Dropout(0.1),
        nn.Linear(256, 256),
        nn.ReLU(),
        nn.Linear(256, 128),
        nn.ReLU(),
        nn.Linear(128, 64),
        nn.ReLU(),
        nn.Linear(64, 3)
    )

def benchmark_model(model, test_data, model_name, num_epochs=3):
    """基准测试模型性能"""
    criterion = nn.CrossEntropyLoss()
    optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
    
    print(f"🔍 基准测试 {model_name}...")
    
    # 预热
    for i in range(3):
        features, labels = test_data[i]
        outputs = model(features)
        loss = criterion(outputs, labels)
        optimizer.zero_grad()
        loss.backward()
        optimizer.step()
    
    # 同步GPU
    if torch.cuda.is_available():
        torch.cuda.synchronize()
    
    # 性能测试
    start_time = time.time()
    total_samples = 0
    
    for epoch in range(num_epochs):
        for features, labels in test_data:
            outputs = model(features)
            loss = criterion(outputs, labels)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            total_samples += features.size(0)
    
    # 同步GPU
    if torch.cuda.is_available():
        torch.cuda.synchronize()
    
    end_time = time.time()
    total_time = end_time - start_time
    throughput = total_samples / total_time
    
    print(f"📊 {model_name}结果:")
    print(f"   - 总时间: {total_time:.3f}s")
    print(f"   - 吞吐量: {throughput:.0f} samples/s")
    print(f"   - 总样本数: {total_samples}")
    
    return total_time, throughput

def main():
    print("🚀 真实场景torch.compile验证测试")
    print("="*60)
    
    # 检查环境
    torch_compile_safe = os.environ.get('TORCH_COMPILE_SAFE', 'false').lower() == 'true'
    print(f"📊 TORCH_COMPILE_SAFE: {torch_compile_safe}")
    print(f"📊 PyTorch版本: {torch.__version__}")
    print(f"📊 CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"📊 GPU: {torch.cuda.get_device_name(0)}")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建真实规模的测试数据
    batch_size = 8192
    num_batches = 20
    input_size = 59
    
    print(f"\n📊 测试配置:")
    print(f"   - 批次大小: {batch_size}")
    print(f"   - 批次数量: {num_batches}")
    print(f"   - 输入维度: {input_size}")
    print(f"   - 总样本数: {batch_size * num_batches}")
    
    # 生成测试数据
    test_data = []
    for _ in range(num_batches):
        features = torch.randn(batch_size, input_size, device=device)
        labels = torch.randint(0, 3, (batch_size,), device=device)
        test_data.append((features, labels))
    
    print("\n" + "="*60)
    print("🧪 性能对比测试")
    
    # 测试1: 未编译模型
    print("\n🔍 测试1: 未编译模型")
    model_uncompiled = create_realistic_model(input_size).to(device)
    uncompiled_time, uncompiled_throughput = benchmark_model(
        model_uncompiled, test_data, "未编译模型"
    )
    
    # 清理内存
    del model_uncompiled
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
    
    # 测试2: 编译模型
    print("\n🔍 测试2: 编译模型")
    model_compiled = create_realistic_model(input_size).to(device)
    
    if torch_compile_safe and hasattr(torch, 'compile'):
        # 编译模型
        compile_start = time.time()
        model_compiled = torch.compile(
            model_compiled, 
            mode='reduce-overhead',
            dynamic=True,
            fullgraph=False
        )
        compile_time = time.time() - compile_start
        print(f"📈 模型编译耗时: {compile_time:.3f}s")
        
        compiled_time, compiled_throughput = benchmark_model(
            model_compiled, test_data, "编译模型"
        )
        
        # 性能对比
        time_improvement = (uncompiled_time - compiled_time) / uncompiled_time * 100
        throughput_improvement = (compiled_throughput - uncompiled_throughput) / uncompiled_throughput * 100
        
        print("\n" + "="*60)
        print("📊 性能对比结果:")
        print(f"{'指标':<20} {'未编译':<15} {'编译后':<15} {'改进':<15}")
        print("-" * 70)
        print(f"{'总时间(s)':<20} {uncompiled_time:<15.3f} {compiled_time:<15.3f} {time_improvement:>+.1f}%")
        print(f"{'吞吐量(samples/s)':<20} {uncompiled_throughput:<15.0f} {compiled_throughput:<15.0f} {throughput_improvement:>+.1f}%")
        print(f"{'编译开销(s)':<20} {'N/A':<15} {compile_time:<15.3f} {'N/A':<15}")
        
        # 计算编译回本时间
        if time_improvement > 0:
            payback_time = compile_time / (time_improvement / 100 * uncompiled_time) * (uncompiled_time / (batch_size * num_batches))
            print(f"{'编译回本时间(s)':<20} {'N/A':<15} {payback_time:<15.1f} {'N/A':<15}")
        
        print("\n🎯 torch.compile效果评估:")
        if throughput_improvement > 20:
            print("✅ torch.compile显著提升性能!")
            result = "excellent"
        elif throughput_improvement > 10:
            print("✅ torch.compile明显提升性能!")
            result = "good"
        elif throughput_improvement > 0:
            print("🟡 torch.compile轻微提升性能")
            result = "fair"
        else:
            print("❌ torch.compile未提升性能")
            result = "poor"
        
        # 检查模型编译状态
        is_compiled = hasattr(model_compiled, '_orig_mod')
        print(f"\n📊 编译状态验证:")
        print(f"   - 模型已编译: {is_compiled}")
        print(f"   - 编译模型类型: {type(model_compiled)}")
        
        # 最终总结
        print("\n" + "="*60)
        print("🎯 torch.compile验证总结:")
        print(f"   ✅ torch.compile已启用: {torch_compile_safe}")
        print(f"   ✅ 模型编译成功: {is_compiled}")
        print(f"   ✅ 性能评估: {result}")
        print(f"   ✅ 吞吐量提升: {throughput_improvement:+.1f}%")
        print(f"   ✅ 编译开销: {compile_time:.3f}s")
        
        if result in ['excellent', 'good']:
            print("\n🚀 torch.compile优化效果显著，已成功启用!")
            return True
        elif result == 'fair':
            print("\n🟡 torch.compile优化效果一般，但已正常工作")
            return True
        else:
            print("\n⚠️ torch.compile优化效果有限，但功能正常")
            return True
    
    else:
        print("❌ torch.compile未启用或不可用")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 真实场景torch.compile验证测试完成!")
        sys.exit(0)
    else:
        print("\n❌ 真实场景torch.compile验证测试失败!")
        sys.exit(1)
