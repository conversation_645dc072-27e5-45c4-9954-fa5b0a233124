"""
性能基准测试
"""

import pytest
import torch
import numpy as np
import time
import logging
from typing import Dict, List
from unittest.mock import Mock, patch

from src.core.application import CryptoMLApplication as Application
from src.training import create_trainer
from src.models.unified_fusion_model import UnifiedSignalFusionModel
from src.monitoring.gpu_monitor import GPUMonitor


@pytest.mark.skip(reason="Performance benchmark tests are too slow for regular CI runs and can be unreliable on inconsistent hardware.")
class TestPerformanceBenchmark:
    """性能基准测试类"""
    
    @pytest.fixture
    def setup_test_environment(self):
        """设置测试环境"""
        # 配置日志
        logging.basicConfig(level=logging.INFO)
        
        # 创建测试配置
        test_config = {
            'model': {
                'feature_dim': 25,
                'hidden_dim': 128,
                'num_layers': 3,
                'dropout': 0.1,
                'num_classes': 3
            },
            'training': {
                'batch_size': 1024,
                'learning_rate': 0.001,
                'epochs': 2
            }
        }
        
        return test_config
    
    def test_batch_size_performance(self, setup_test_environment):
        """测试不同批次大小的性能"""
        config = setup_test_environment
        
        if not torch.cuda.is_available():
            pytest.skip("CUDA不可用，跳过GPU性能测试")
        
        device = torch.device('cuda')
        model = UnifiedSignalFusionModel(**config['model']).to(device)
        
        batch_sizes = [256, 512, 1024, 2048, 4096]
        results = {}
        
        for batch_size in batch_sizes:
            try:
                # 创建测试数据
                features = torch.randn(batch_size, config['model']['feature_dim'], device=device)
                labels = torch.randint(0, 3, (batch_size,), device=device)
                
                # 预热
                with torch.no_grad():
                    _ = model(features)
                
                torch.cuda.synchronize()
                
                # 性能测试
                start_time = time.time()
                num_iterations = 10
                
                for _ in range(num_iterations):
                    with torch.no_grad():
                        output = model(features)
                        loss = torch.nn.functional.cross_entropy(output['signal'], labels)
                
                torch.cuda.synchronize()
                end_time = time.time()
                
                # 计算指标
                total_time = end_time - start_time
                avg_time = total_time / num_iterations
                throughput = batch_size / avg_time
                memory_used = torch.cuda.memory_allocated() / (1024**2)  # MB
                
                results[batch_size] = {
                    'avg_time': avg_time,
                    'throughput': throughput,
                    'memory_mb': memory_used
                }
                
                print(f"批次大小 {batch_size}: {throughput:.0f} samples/s, {memory_used:.0f}MB")
                
                # 清理内存
                del features, labels, output
                torch.cuda.empty_cache()
                
            except RuntimeError as e:
                if "out of memory" in str(e).lower():
                    print(f"批次大小 {batch_size}: 内存不足")
                    break
                else:
                    raise e
        
        # 验证结果
        assert len(results) > 0, "至少应该有一个批次大小测试成功"
        
        # 找到最优批次大小
        best_batch_size = max(results.keys(), key=lambda x: results[x]['throughput'])
        print(f"最优批次大小: {best_batch_size} (吞吐量: {results[best_batch_size]['throughput']:.0f} samples/s)")
        assert best_batch_size is not None
    
    def test_dataloader_performance(self, setup_test_environment):
        """测试DataLoader性能"""
        config = setup_test_environment
        
        # 创建测试数据
        num_samples = 10000
        features = np.random.randn(num_samples, config['model']['feature_dim']).astype(np.float32)
        labels = np.random.randint(0, 3, num_samples).astype(np.int64)
        
        from torch.utils.data import TensorDataset, DataLoader
        
        dataset = TensorDataset(torch.from_numpy(features), torch.from_numpy(labels))
        
        # 测试不同worker配置
        worker_configs = [0, 2, 4, 8]
        results = {}
        
        for num_workers in worker_configs:
            dataloader = DataLoader(
                dataset,
                batch_size=1024,
                shuffle=True,
                num_workers=num_workers,
                pin_memory=torch.cuda.is_available(),
                persistent_workers=num_workers > 0,
                prefetch_factor=4 if num_workers > 0 else None
            )
            
            # 性能测试
            start_time = time.time()
            batch_count = 0
            
            for batch_features, batch_labels in dataloader:
                batch_count += 1
                if batch_count >= 5:  # 只测试前5个批次
                    break
            
            end_time = time.time()
            avg_time_per_batch = (end_time - start_time) / batch_count
            
            results[num_workers] = {
                'avg_time_per_batch': avg_time_per_batch,
                'batches_per_sec': 1.0 / avg_time_per_batch
            }
            
            print(f"Workers {num_workers}: {avg_time_per_batch:.3f}s/batch")
        
        # 验证结果
        assert len(results) > 0, "DataLoader性能测试应该有结果"
        best_config = min(results, key=lambda w: results[w]['avg_time_per_batch'])
        print(f"最优worker配置: {best_config}")
        assert best_config is not None
    
    def test_gpu_monitoring_performance(self, setup_test_environment):
        """测试GPU监控性能"""
        if not torch.cuda.is_available():
            pytest.skip("CUDA不可用，跳过GPU监控测试")

        monitor = GPUMonitor()
        assert monitor.is_initialized, "GPUMonitor初始化失败"

        # 获取计算前的GPU状态
        status_before = monitor.get_status()
        assert status_before is not None, "获取初始GPU状态失败"

        # 创建并执行计算负载
        device = torch.device('cuda')
        data = torch.randn(4096, 4096, device=device)
        for _ in range(20):
            data = torch.matmul(data, data)
        torch.cuda.synchronize() # 等待计算完成

        # 获取计算后的GPU状态
        status_after = monitor.get_status()
        assert status_after is not None, "获取最终GPU状态失败"

        print(f"GPU利用率 (前): {status_before['gpu_utilization_percent']}% -> (后): {status_after['gpu_utilization_percent']}%")
        print(f"内存使用 (前): {status_before['memory_used_mb']:.1f}MB -> (后): {status_after['memory_used_mb']:.1f}MB")

        # 验证计算后内存使用量增加，这是一个比利用率更可靠的指标
        assert status_after['memory_used_mb'] > status_before['memory_used_mb'], "GPU内存使用量应在计算后显著上升"

        monitor.shutdown()
        assert True
    
    def test_model_compilation_performance(self, setup_test_environment):
        """测试模型编译性能"""
        config = setup_test_environment
        
        if not torch.cuda.is_available():
            pytest.skip("CUDA不可用，跳过模型编译测试")
        
        if not hasattr(torch, 'compile'):
            pytest.skip("PyTorch版本不支持torch.compile")
        
        device = torch.device('cuda')
        model = UnifiedSignalFusionModel(**config['model']).to(device)
        
        # 创建测试数据
        batch_size = 1024
        features = torch.randn(batch_size, config['model']['feature_dim'], device=device)
        
        # 测试原始模型性能
        model.eval()
        with torch.no_grad():
            # 预热
            for _ in range(5):
                _ = model(features)
            
            torch.cuda.synchronize()
            start_time = time.time()
            
            for _ in range(20):
                output = model(features)
            
            torch.cuda.synchronize()
            original_time = time.time() - start_time
        
        # 编译模型
        try:
            compiled_model = torch.compile(model, mode='default')
            
            # 测试编译后模型性能
            compiled_model.eval()
            with torch.no_grad():
                # 预热编译后的模型
                for _ in range(5):
                    _ = compiled_model(features)
                
                torch.cuda.synchronize()
                start_time = time.time()
                
                for _ in range(20):
                    output = compiled_model(features)
                
                torch.cuda.synchronize()
                compiled_time = time.time() - start_time
            
            speedup = original_time / compiled_time
            print(f"原始模型时间: {original_time:.3f}s")
            print(f"编译后时间: {compiled_time:.3f}s")
            print(f"加速比: {speedup:.2f}x")
            
            # 验证编译确实有效果（至少不应该更慢）
            assert compiled_time <= original_time * 1.1, "编译后的模型不应该明显更慢"
            assert speedup > 0.9, "加速比不应为负或过低"
            
        except Exception as e:
            pytest.skip(f"模型编译失败: {e}")
    
    def test_memory_optimization(self, setup_test_environment):
        """测试内存优化"""
        config = setup_test_environment
        
        if not torch.cuda.is_available():
            pytest.skip("CUDA不可用，跳过内存优化测试")
        
        device = torch.device('cuda')
        
        # 清理初始内存
        torch.cuda.empty_cache()
        initial_memory = torch.cuda.memory_allocated()
        
        # 创建模型和数据
        model = UnifiedSignalFusionModel(**config['model']).to(device)
        batch_size = 2048
        features = torch.randn(batch_size, config['model']['feature_dim'], device=device)
        features = np.nan_to_num(features.cpu().numpy())
        features = torch.from_numpy(features).to(device)
        
        # 测试内存使用
        memory_after_model = torch.cuda.memory_allocated()
        
        # 前向传播
        with torch.no_grad():
            output = model(features)
        
        memory_after_forward = torch.cuda.memory_allocated()
        
        # 清理
        del output, features, model
        torch.cuda.empty_cache()
        
        final_memory = torch.cuda.memory_allocated()
        
        print(f"初始内存: {initial_memory / 1024**2:.1f}MB")
        print(f"模型加载后: {memory_after_model / 1024**2:.1f}MB")
        print(f"前向传播后: {memory_after_forward / 1024**2:.1f}MB")
        print(f"清理后: {final_memory / 1024**2:.1f}MB")
        
        # 验证内存管理
        forward_memory = memory_after_forward - memory_after_model
        
        assert forward_memory > 0, "前向传播应该占用额外内存"
        assert final_memory <= memory_after_model, "清理后内存应该小于等于模型加载后的内存"
        assert final_memory < memory_after_forward, "清理后内存应该小于前向传播后的内存"
