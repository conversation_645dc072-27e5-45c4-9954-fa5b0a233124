#!/usr/bin/env python3
"""
torch.compile应用集成验证
验证torch.compile在实际应用中的集成状态
"""

import os
import sys
import time
import torch
import torch.nn as nn
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

def test_trainer_torch_compile_integration():
    """测试训练器中的torch.compile集成"""
    print("🧪 测试训练器中的torch.compile集成")
    
    try:
        from src.training.high_performance_trainer import HighPerformanceTrainer, TORCH_COMPILE_SAFE, TORCH_COMPILE_BACKEND, TORCH_COMPILE_MODE
        
        print(f"📊 训练器torch.compile状态:")
        print(f"   - TORCH_COMPILE_SAFE: {TORCH_COMPILE_SAFE}")
        print(f"   - TORCH_COMPILE_BACKEND: {TORCH_COMPILE_BACKEND}")
        print(f"   - TORCH_COMPILE_MODE: {TORCH_COMPILE_MODE}")
        
        # 创建测试模型
        model = nn.Sequential(
            nn.Linear(59, 256),
            nn.ReLU(),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)
        )
        
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建训练器并启用编译
        trainer = HighPerformanceTrainer(
            model=model,
            device=device,
            mixed_precision=True,
            compile_model=True  # 启用编译
        )
        
        # 检查模型是否被编译
        is_compiled = hasattr(trainer.model, '_orig_mod')
        print(f"   - 模型已编译: {is_compiled}")
        print(f"   - 编译模型类型: {type(trainer.model)}")
        
        if is_compiled:
            print("✅ 训练器中torch.compile集成成功")
            return True
        else:
            print("❌ 训练器中torch.compile未启用")
            return False
            
    except Exception as e:
        print(f"❌ 训练器torch.compile集成测试失败: {e}")
        return False

def test_config_torch_compile_settings():
    """测试配置文件中的torch.compile设置"""
    print("\n🧪 测试配置文件中的torch.compile设置")
    
    try:
        from src.utils.config import get_config

        config = get_config()
        compilation_config = config.get('optimization', {}).get('compilation', {})
        
        print(f"📊 配置文件编译设置:")
        print(f"   - enabled: {compilation_config.get('enabled', False)}")
        print(f"   - mode: {compilation_config.get('mode', 'default')}")
        print(f"   - dynamic: {compilation_config.get('dynamic', True)}")
        print(f"   - fullgraph: {compilation_config.get('fullgraph', False)}")
        
        # 检查训练配置中的编译设置
        training_config = config.get('training', {})
        compile_model = training_config.get('compile_model', False)
        print(f"   - training.compile_model: {compile_model}")
        
        if compilation_config.get('enabled', False) and compile_model:
            print("✅ 配置文件中torch.compile已正确启用")
            return True
        else:
            print("❌ 配置文件中torch.compile未完全启用")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件torch.compile设置测试失败: {e}")
        return False

def test_environment_variables():
    """测试环境变量设置"""
    print("\n🧪 测试环境变量设置")
    
    # 检查关键环境变量
    torch_compile_safe = os.environ.get('TORCH_COMPILE_SAFE', 'false').lower() == 'true'
    
    print(f"📊 环境变量状态:")
    print(f"   - TORCH_COMPILE_SAFE: {torch_compile_safe}")
    
    if torch_compile_safe:
        print("✅ 环境变量正确设置")
        return True
    else:
        print("❌ 环境变量未正确设置")
        return False

def test_pytorch_compatibility():
    """测试PyTorch兼容性"""
    print("\n🧪 测试PyTorch兼容性")
    
    print(f"📊 PyTorch环境:")
    print(f"   - PyTorch版本: {torch.__version__}")
    print(f"   - CUDA版本: {torch.version.cuda}")
    print(f"   - CUDA可用: {torch.cuda.is_available()}")
    
    if torch.cuda.is_available():
        print(f"   - GPU: {torch.cuda.get_device_name(0)}")
        print(f"   - GPU计算能力: {torch.cuda.get_device_capability(0)}")
    
    # 检查torch.compile可用性
    has_compile = hasattr(torch, 'compile')
    print(f"   - torch.compile可用: {has_compile}")
    
    if has_compile:
        # 测试基本编译功能
        try:
            test_model = nn.Linear(10, 5)
            if torch.cuda.is_available():
                test_model = test_model.cuda()
            
            compiled_test = torch.compile(test_model, mode='reduce-overhead')
            test_input = torch.randn(1, 10)
            if torch.cuda.is_available():
                test_input = test_input.cuda()
            
            _ = compiled_test(test_input)
            print("✅ PyTorch torch.compile兼容性测试通过")
            return True
            
        except Exception as e:
            print(f"❌ PyTorch torch.compile兼容性测试失败: {e}")
            return False
    else:
        print("❌ torch.compile不可用")
        return False

def test_application_startup_with_compile():
    """测试应用启动时的torch.compile集成"""
    print("\n🧪 测试应用启动时的torch.compile集成")
    
    try:
        # 这里我们不实际启动完整应用，而是测试关键组件
        print("📊 模拟应用启动torch.compile检查...")
        
        # 检查环境
        env_ok = os.environ.get('TORCH_COMPILE_SAFE', 'false').lower() == 'true'
        pytorch_ok = hasattr(torch, 'compile')
        
        print(f"   - 环境变量检查: {'✅' if env_ok else '❌'}")
        print(f"   - PyTorch支持检查: {'✅' if pytorch_ok else '❌'}")
        
        if env_ok and pytorch_ok:
            print("✅ 应用启动torch.compile集成准备就绪")
            return True
        else:
            print("❌ 应用启动torch.compile集成未就绪")
            return False
            
    except Exception as e:
        print(f"❌ 应用启动torch.compile集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 torch.compile应用集成验证")
    print("="*60)
    
    # 运行所有测试
    tests = [
        ("环境变量设置", test_environment_variables),
        ("PyTorch兼容性", test_pytorch_compatibility),
        ("配置文件设置", test_config_torch_compile_settings),
        ("训练器集成", test_trainer_torch_compile_integration),
        ("应用启动集成", test_application_startup_with_compile),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*60}")
        print(f"测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print(f"\n{'='*60}")
    print("🎯 torch.compile应用集成验证总结")
    print(f"{'='*60}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name:<20} {status}")
        if result:
            passed += 1
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🚀 torch.compile已完全集成并准备就绪!")
        print("   - 环境变量已正确设置")
        print("   - 配置文件已正确配置")
        print("   - 训练器已集成torch.compile")
        print("   - 应用启动时将自动启用torch.compile")
        return True
    elif passed >= total * 0.8:
        print("\n🟡 torch.compile基本集成完成，但有部分问题")
        print("   - 大部分功能正常")
        print("   - 建议检查失败的测试项")
        return True
    else:
        print("\n❌ torch.compile集成存在重大问题")
        print("   - 需要检查配置和环境设置")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ torch.compile应用集成验证完成!")
        sys.exit(0)
    else:
        print("\n❌ torch.compile应用集成验证失败!")
        sys.exit(1)
