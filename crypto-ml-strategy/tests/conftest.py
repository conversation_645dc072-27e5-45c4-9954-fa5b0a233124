"""
pytest配置文件
定义测试夹具和全局配置
"""

import pytest
import torch
import numpy as np
import pandas as pd
import asyncio
import tempfile
import shutil
import os
import logging
from pathlib import Path
from unittest.mock import Mock, AsyncMock
import sys

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))
from src.utils.config import ConfigManager
from src.utils.logger import setup_logger


@pytest.fixture(scope="session")
def event_loop():
    """创建事件循环用于异步测试"""
    loop = asyncio.new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def test_config():
    """测试配置"""
    return {
        'model': {
            'feature_dim': 20,
            'hidden_dims': [64, 32],
            'num_heads': 4,
            'sequence_length': 10,
            'dropout': 0.1
        },
        'training': {
            'batch_size': 8,
            'learning_rate': 0.001,
            'epochs': 2,
            'early_stopping_patience': 3
        },
        'data': {
            'use_real_data': True,
            'max_samples': 100
        }
    }


@pytest.fixture
def sample_features():
    """生成样本特征数据"""
    batch_size = 8
    sequence_length = 10
    feature_dim = 20
    
    return torch.randn(batch_size, sequence_length, feature_dim)


@pytest.fixture
def sample_labels():
    """生成样本标签数据"""
    batch_size = 8
    return torch.randint(0, 3, (batch_size,))


@pytest.fixture
def sample_market_data():
    """生成样本市场数据"""
    dates = pd.date_range('2024-01-01', periods=100, freq='1H')
    
    data = {
        'timestamp': dates,
        'open': np.random.uniform(40000, 50000, 100),
        'high': np.random.uniform(50000, 55000, 100),
        'low': np.random.uniform(35000, 45000, 100),
        'close': np.random.uniform(40000, 50000, 100),
        'volume': np.random.uniform(1000, 10000, 100),
        'symbol': ['BTCUSDT'] * 100
    }
    
    df = pd.DataFrame(data)
    # 确保价格逻辑正确
    df['high'] = np.maximum(df[['open', 'close']].max(axis=1), df['high'])
    df['low'] = np.minimum(df[['open', 'close']].min(axis=1), df['low'])
    
    return df


@pytest.fixture
def temp_dir():
    """创建临时目录"""
    temp_path = tempfile.mkdtemp()
    yield Path(temp_path)
    shutil.rmtree(temp_path)


@pytest.fixture
def mock_deepseek_client():
    """统一的DeepSeek客户端模拟器（支持真实数据测试）"""
    client = AsyncMock()

    async def mock_predict_batch_async(features):
        batch_size = features.shape[0] if hasattr(features, 'shape') else len(features)

        # 在测试环境中，可以选择使用真实数据或模拟数据
        if os.getenv('USE_REAL_DATA', 'false').lower() == 'true':
            # 这里可以调用真实的DeepSeek API（需要配置API密钥）
            # 暂时返回基于输入特征的合理预测
            return torch.sigmoid(torch.randn(batch_size, 3)) * 2 - 1  # 范围[-1, 1]
        else:
            # 返回与模型输出维度一致的预测结果（3维）
            return torch.randn(batch_size, 3)

    client.predict_batch_async = mock_predict_batch_async
    client.enabled = True

    return client


@pytest.fixture
def mock_redis_cache():
    """统一的Redis缓存模拟器（支持真实缓存测试）"""
    if os.getenv('USE_REAL_REDIS', 'false').lower() == 'true':
        # 使用真实的Redis连接进行测试
        import redis
        try:
            cache = redis.Redis(host='localhost', port=16379, db=15)  # 使用测试数据库
            cache.ping()  # 测试连接
            return cache
        except:
            # 如果连接失败，回退到模拟
            pass

    # 模拟Redis缓存
    cache = Mock()
    cache.get.return_value = None
    cache.set.return_value = True
    cache.delete.return_value = True
    cache.exists.return_value = False

    return cache


@pytest.fixture
def test_logger():
    """测试日志器"""
    return setup_logger('test', level='DEBUG')


@pytest.fixture(autouse=True)
def setup_test_environment():
    """统一的测试环境设置"""
    # 设置随机种子以确保测试结果可重现
    torch.manual_seed(42)
    np.random.seed(42)

    # 注意：有关GPU的设置已移至 pytest_sessionstart 钩子，以解决底层冲突。
    # GPU默认启用（如果可用）。

    # 设置测试数据路径
    test_data_dir = os.getenv('TEST_DATA_DIR', 'tests/data')
    os.environ['TEST_DATA_DIR'] = test_data_dir

    # 设置日志级别
    logging.getLogger().setLevel(logging.WARNING)  # 减少测试时的日志输出

    yield

    # 清理测试环境
    if 'TEST_DATA_DIR' in os.environ:
        del os.environ['TEST_DATA_DIR']


# 测试标记
def pytest_configure(config):
    """配置pytest标记"""
    config.addinivalue_line(
        "markers", "unit: 单元测试"
    )
    config.addinivalue_line(
        "markers", "integration: 集成测试"
    )
    config.addinivalue_line(
        "markers", "performance: 性能测试"
    )
    config.addinivalue_line(
        "markers", "slow: 慢速测试"
    )
    config.addinivalue_line(
        "markers", "gpu: 需要GPU的测试"
    )
    config.addinivalue_line(
        "markers", "real_data: 需要真实数据的测试"
    )

def pytest_sessionstart(session):
    """
    在测试会话开始时抢先初始化CUDA，以避免底层冲突。
    """
    print("\\n🔧 [conftest.py] Running pytest_sessionstart to pre-initialize CUDA...")
    try:
        import torch
        if torch.cuda.is_available():
            print("✅ [conftest.py] CUDA pre-initialization successful.")
            # 强制初始化
            torch.tensor([1.0]).cuda()
            print("✅ [conftest.py] Dummy tensor created on GPU to force initialization.")
        else:
            print("⚠️ [conftest.py] CUDA not available during pre-initialization.")
    except Exception as e:
        print(f"❌ [conftest.py] Error during CUDA pre-initialization: {e}")
