import pytest
from src.core.application import CryptoMLApplication
from unittest.mock import MagicMock

class TestFeatureDimCalculation:
    """测试特征维度自动计算功能"""
    
    def test_feature_dim_calculation(self):
        """测试当配置缺少feature_dim时能正确计算"""
        # 模拟配置
        config = {
            'feature_engineering': {
                'technical_indicators': ['sma', 'rsi', 'macd'],
                'window_sizes': [5, 10, 20]
            },
            'model': {
                'hidden_dims': [64], 'num_heads': 4, 'num_layers': 2, 'dropout': 0.1, 'output_dim': 3
            }
        }
        
        # 创建应用实例
        app = CryptoMLApplication(config=config, device='cpu', project_root=MagicMock())
        
        # 初始化模型
        app._initialize_model()
        
        # 验证特征维度计算正确
        assert app.config['model']['feature_dim'] == 9  # 3指标 * 3窗口
        
    def test_existing_feature_dim_respected(self):
        """测试当配置已存在feature_dim时直接使用"""
        # 模拟配置
        config = {
            'model': {
                'feature_dim': 128,
                'hidden_dims': [64], 'num_heads': 4, 'num_layers': 2, 'dropout': 0.1, 'output_dim': 3
            }
        }
        
        # 创建应用实例
        app = CryptoMLApplication(config=config, device='cpu', project_root=MagicMock())
        
        # 初始化模型
        app._initialize_model()
        
        # 验证特征维度未被修改
        assert app.config['model']['feature_dim'] == 128