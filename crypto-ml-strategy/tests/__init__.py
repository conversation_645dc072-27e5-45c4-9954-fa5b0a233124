"""
深度学习策略模块测试包
提供完整的测试体系，包括单元测试、集成测试、性能测试等
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / 'src'))

# 测试配置
TEST_CONFIG = {
    'use_gpu': False,  # 测试时默认使用CPU
    'batch_size': 8,   # 小批次用于测试
    'max_samples': 100,  # 限制测试样本数量
    'timeout': 30,     # 测试超时时间（秒）
    'real_data': True,  # 使用真实数据进行测试
}

# 测试数据路径
TEST_DATA_DIR = project_root / 'tests' / 'data'
TEST_OUTPUT_DIR = project_root / 'tests' / 'output'

# 确保测试目录存在
TEST_DATA_DIR.mkdir(exist_ok=True)
TEST_OUTPUT_DIR.mkdir(exist_ok=True)
