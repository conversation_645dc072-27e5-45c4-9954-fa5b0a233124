import pytest
import asyncio
import time
import sys
from pathlib import Path
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
import numpy as np
import logging

# Add project root to path
sys.path.append(str(Path(__file__).resolve().parent.parent.parent))

from src.training.unified_trainer import UnifiedTrainer, TrainerConfig

@pytest.mark.asyncio
class TestTrainingLoopOptimization:
    """
    Refactored test class for training loop performance.
    Focuses on verifying the performance impact of UnifiedTrainer's built-in
    optimization features (AMP, torch.compile) via its public API.
    """

    def setup_method(self, method):
        """Set up the test environment before each test."""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"\\n🔧 [Setup] Using device: {self.device}")
        if torch.cuda.is_available():
            self.logger.info(f"🔧 [Setup] GPU: {torch.cuda.get_device_name(0)}")

    def _create_test_model(self, input_size: int = 59) -> nn.Module:
        """Create a simple sequential model for testing."""
        model = nn.Sequential(
            nn.Linear(input_size, 128), nn.ReLU(),
            nn.Linear(128, 64), nn.ReLU(),
            nn.Linear(64, 3)
        )
        return model.to(self.device)

    def _create_test_dataloader(self, batch_size: int, input_size: int, num_batches: int = 50) -> DataLoader:
        """Create a test DataLoader."""
        num_samples = batch_size * num_batches
        features = torch.randn(num_samples, input_size)
        labels = torch.randint(0, 3, (num_samples,))
        dataset = TensorDataset(features, labels)
        
        is_cuda = self.device.type == 'cuda'
        return DataLoader(
            dataset, batch_size=batch_size, num_workers=4 if is_cuda else 0,
            pin_memory=is_cuda, persistent_workers=is_cuda, drop_last=True
        )

    async def _run_training_and_measure(self, config_name: str, trainer: UnifiedTrainer, loader: DataLoader) -> float:
        """Helper to encapsulate the training and timing logic."""
        self.logger.info(f"🔥 [Run] Warming up: {config_name}...")
        warmup_loader = self._create_test_dataloader(loader.batch_size, loader.dataset.tensors[0].shape[1], num_batches=5)
        await trainer.train(warmup_loader, val_loader=None)
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
            
        self.logger.info(f"📊 [Run] Starting performance test: {config_name}...")
        start_time = time.time()
        
        await trainer.train(loader, val_loader=None)
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
            
        duration = time.time() - start_time
        throughput = len(loader.dataset) / duration if duration > 0 else 0
        
        self.logger.info(f"📈 [Result] {config_name} - Throughput: {throughput:.0f} samples/s, Total time: {duration:.3f}s")
        return throughput

    async def test_training_loop_performance_improvement(self, batch_size: int = 4096):
        """
        Runs baseline vs. optimized training loops to ensure they both complete without errors.
        Performance is printed for observation but not asserted due to environmental flakiness.
        """
        if not torch.cuda.is_available() or not hasattr(torch, 'compile'):
            pytest.skip("CUDA or torch.compile not available, skipping performance comparison.")

        self.logger.info(f"\\n🧪 [Test] Starting training loop smoke test (batch_size={batch_size})")

        input_size = 59
        num_epochs = 2
        dataloader = self._create_test_dataloader(batch_size, input_size)

        # Baseline trainer (AMP=Off, Compile=Off)
        model_baseline = self._create_test_model(input_size)
        config_baseline = TrainerConfig(epochs=num_epochs, use_amp=False, use_compile=False)
        trainer_baseline = UnifiedTrainer(model=model_baseline, config=config_baseline, device=self.device)
        
        # Optimized trainer (AMP=On, Compile=On)
        model_optimized = self._create_test_model(input_size)
        config_optimized = TrainerConfig(epochs=num_epochs, use_amp=True, use_compile=True)
        trainer_optimized = UnifiedTrainer(model=model_optimized, config=config_optimized, device=self.device)

        # Act: Run both trainers
        throughput_baseline = await self._run_training_and_measure(
            "Baseline (AMP=Off, Compile=Off)", trainer_baseline, dataloader
        )
        throughput_optimized = await self._run_training_and_measure(
            "Optimized (AMP=On, Compile=On)", trainer_optimized, dataloader
        )

        # Assert: The only assertion is that both runs produced a valid throughput number.
        assert throughput_baseline > 0, "Baseline training failed to produce a valid throughput."
        assert throughput_optimized > 0, "Optimized training failed to produce a valid throughput."
        
        improvement = (throughput_optimized - throughput_baseline) / throughput_baseline * 100
        self.logger.info(f"\\n✅ [Observe] Performance improvement: {improvement:+.1f}% (for observation only)")
