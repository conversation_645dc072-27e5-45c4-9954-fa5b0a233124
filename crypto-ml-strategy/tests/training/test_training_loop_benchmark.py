"""
训练循环性能基准测试
对比优化前后的训练循环性能
"""

import time
import sys
from pathlib import Path
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from typing import Dict, List

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent.parent))

from src.training import create_trainer


class TrainingLoopBenchmark:
    """训练循环性能基准测试"""
    
    def setup_method(self, method):
        """初始化基准测试"""
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"🔧 基准测试设备: {self.device}")
        if torch.cuda.is_available():
            print(f"🔧 GPU: {torch.cuda.get_device_name(0)}")
    
    def create_test_model(self, input_size: int = 59) -> nn.Module:
        """创建测试模型"""
        model = nn.Sequential(
            nn.Linear(input_size, 256),
            nn.<PERSON><PERSON>(),
            nn.Dropout(0.2),
            nn.Linear(256, 256),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 3)
        )
        return model.to(self.device)
    
    def create_test_batches(self, batch_size: int, num_batches: int = 100) -> List[Dict[str, torch.Tensor]]:
        """创建测试批次数据"""
        batches = []
        for _ in range(num_batches):
            batch = {
                'features': torch.randn(batch_size, 59, device=self.device),
                'labels': torch.randint(0, 3, (batch_size,), device=self.device)
            }
            batches.append(batch)
        return batches
    
    def benchmark_original_training_loop(self, batch_size: int = 8192, num_batches: int = 100):
        """基准测试原始训练循环"""
        print(f"\n🧪 基准测试原始训练循环 (batch_size={batch_size}, batches={num_batches})")
        
        model = self.create_test_model()
        batches = self.create_test_batches(batch_size, num_batches)
        
        optimizer = optim.AdamW(model.parameters(), lr=1e-3)
        criterion = nn.CrossEntropyLoss()
        scaler = torch.cuda.amp.GradScaler()
        
        # 预热
        for i in range(5):
            self._original_train_step(batches[i], model, optimizer, criterion, scaler)
        
        # 同步GPU
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        # 基准测试
        start_time = time.time()
        total_samples = 0
        
        for batch in batches:
            result = self._original_train_step(batch, model, optimizer, criterion, scaler)
            total_samples += result['batch_size']
        
        # 同步GPU
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        return {
            'total_time': total_time,
            'throughput': total_samples / total_time,
            'total_samples': total_samples
        }
    
    def _original_train_step(self, batch, model, optimizer, criterion, scaler):
        """原始训练步骤（未优化版本）"""
        features = batch['features']
        labels = batch['labels']
        
        # 前向传播
        with torch.cuda.amp.autocast():
            outputs = model(features)
            loss = criterion(outputs, labels)
        
        # 反向传播
        optimizer.zero_grad()  # 使用原始的zero_grad
        scaler.scale(loss).backward()
        scaler.step(optimizer)
        scaler.update()
        
        # 频繁的损失值获取（原始方式）
        loss_value = loss.item()
        
        # 频繁的内存清理（原始方式）
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        return {
            'loss': loss_value,
            'batch_size': features.size(0)
        }
    
    def benchmark_optimized_training_loop(self, batch_size: int = 8192, num_batches: int = 100):
        """基准测试优化后的训练循环"""
        print(f"\n🧪 基准测试优化后训练循环 (batch_size={batch_size}, batches={num_batches})")
        
        model = self.create_test_model()
        batches = self.create_test_batches(batch_size, num_batches)
        
        # 使用优化后的训练器
        train_config = {
            'use_amp': True,
            'use_compile': True,
            'learning_rate': 1e-3,
            'epochs': 1,
            'memory_efficient': True
        }
        trainer = create_trainer(
            model=model,
            train_config=train_config,
            device=self.device
        )

        # 为了使用 trainer.train，我们需要一个 DataLoader
        from torch.utils.data import TensorDataset, DataLoader
        all_features = torch.cat([b['features'] for b in batches])
        all_labels = torch.cat([b['labels'] for b in batches])
        dataset = TensorDataset(all_features, all_labels)
        dataloader = DataLoader(dataset, batch_size=batch_size)

        # 预热 (新的trainer在内部处理预热)
        
        # 同步GPU
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        # 基准测试
        import asyncio
        start_time = time.time()
        
        asyncio.run(trainer.train(dataloader, val_loader=None))
        
        # 同步GPU
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        end_time = time.time()
        total_time = end_time - start_time
        total_samples = len(all_features)
        
        return {
            'total_time': total_time,
            'throughput': total_samples / total_time if total_time > 0 else 0,
            'total_samples': total_samples
        }
    
    def benchmark_core_optimizations(self, batch_size: int = 8192):
        """基准测试核心优化项"""
        print(f"\n🧪 基准测试核心优化项")
        
        model = self.create_test_model()
        batches = self.create_test_batches(batch_size, 50)
        
        optimizer = optim.AdamW(model.parameters(), lr=1e-3)
        criterion = nn.CrossEntropyLoss()
        scaler = torch.cuda.amp.GradScaler()
        
        # 测试1: zero_grad优化
        print("🔍 测试zero_grad优化...")
        
        # 原始方式
        start_time = time.time()
        for batch in batches[:25]:
            features = batch['features']
            labels = batch['labels']
            
            with torch.cuda.amp.autocast():
                outputs = model(features)
                loss = criterion(outputs, labels)
            
            optimizer.zero_grad()  # 原始方式
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
        
        original_zero_grad_time = time.time() - start_time
        
        # 优化方式
        start_time = time.time()
        for batch in batches[25:]:
            features = batch['features']
            labels = batch['labels']
            
            with torch.cuda.amp.autocast():
                outputs = model(features)
                loss = criterion(outputs, labels)
            
            optimizer.zero_grad(set_to_none=True)  # 优化方式
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
        
        optimized_zero_grad_time = time.time() - start_time
        
        zero_grad_improvement = (original_zero_grad_time - optimized_zero_grad_time) / original_zero_grad_time * 100
        
        print(f"   - 原始zero_grad时间: {original_zero_grad_time:.3f}s")
        print(f"   - 优化zero_grad时间: {optimized_zero_grad_time:.3f}s")
        print(f"   - zero_grad优化提升: {zero_grad_improvement:+.1f}%")
        
        return {
            'zero_grad_improvement': zero_grad_improvement
        }
    
    def run_comprehensive_benchmark(self):
        """运行综合基准测试"""
        print("🚀 开始综合训练循环性能基准测试...")
        
        results = {}
        
        # 测试不同批次大小
        batch_sizes = [2048, 4096, 8192]
        
        for batch_size in batch_sizes:
            print(f"\n{'='*60}")
            print(f"测试批次大小: {batch_size}")
            
            # 原始训练循环
            original_results = self.benchmark_original_training_loop(batch_size, 50)
            
            # 优化后训练循环
            optimized_results = self.benchmark_optimized_training_loop(batch_size, 50)
            
            # 计算改进
            time_improvement = (original_results['total_time'] - optimized_results['total_time']) / original_results['total_time'] * 100
            throughput_improvement = (optimized_results['throughput'] - original_results['throughput']) / original_results['throughput'] * 100
            
            print(f"\n📊 批次大小 {batch_size} 性能对比:")
            print(f"{'指标':<20} {'原始':<15} {'优化后':<15} {'改进':<15}")
            print("-" * 70)
            print(f"{'总时间(s)':<20} {original_results['total_time']:<15.3f} {optimized_results['total_time']:<15.3f} {time_improvement:>+.1f}%")
            print(f"{'吞吐量(samples/s)':<20} {original_results['throughput']:<15.0f} {optimized_results['throughput']:<15.0f} {throughput_improvement:>+.1f}%")
            
            results[batch_size] = {
                'original': original_results,
                'optimized': optimized_results,
                'time_improvement': time_improvement,
                'throughput_improvement': throughput_improvement
            }
            
            if throughput_improvement > 10:
                print(f"✅ 批次大小 {batch_size}: 显著性能提升 (+{throughput_improvement:.1f}%)")
            elif throughput_improvement > 0:
                print(f"🟡 批次大小 {batch_size}: 轻微性能提升 (+{throughput_improvement:.1f}%)")
            else:
                print(f"❌ 批次大小 {batch_size}: 性能下降 ({throughput_improvement:.1f}%)")
        
        # 核心优化测试
        print(f"\n{'='*60}")
        print("核心优化项测试")
        core_results = self.benchmark_core_optimizations()
        results['core_optimizations'] = core_results
        
        # 总结
        print(f"\n📈 训练循环优化总结:")
        avg_throughput_improvement = np.mean([results[bs]['throughput_improvement'] for bs in batch_sizes])
        print(f"   - 平均吞吐量提升: {avg_throughput_improvement:+.1f}%")
        print(f"   - zero_grad优化提升: {core_results['zero_grad_improvement']:+.1f}%")
        
        if avg_throughput_improvement > 10:
            print(f"✅ 训练循环优化总体效果显著!")
        elif avg_throughput_improvement > 0:
            print(f"🟡 训练循环优化有一定效果")
        else:
            print(f"❌ 训练循环优化需要进一步改进")
        
        return results


def main():
    """主函数"""
    benchmark = TrainingLoopBenchmark()
    results = benchmark.run_comprehensive_benchmark()
    
    print("\n✅ 训练循环性能基准测试完成！")


if __name__ == "__main__":
    main()
