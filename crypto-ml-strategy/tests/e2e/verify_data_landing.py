
import os
import sys
import time
from datetime import datetime, timedelta
from influxdb_client import InfluxDBC<PERSON>, Point
from influxdb_client.client.write_api import SYNCHRONOUS

# --- Add project root to sys.path to allow imports from src ---
# This is a common pattern for test/scripting files.
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
src_path = os.path.join(project_root, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

from utils.config import get_config_manager
from utils.logger import setup_logger

# --- Configuration ---
TEST_SYMBOL = "E2E_TEST_BTCUSDT"
QUERY_RETRIES = 10
RETRY_DELAY_SECONDS = 2

def main():
    """
    Main function to run the E2E data landing verification.
    """
    # 1. Setup Logger and Config
    # We point the config manager to the project's config directory
    config_manager = get_config_manager(config_dir=os.path.join(project_root, 'config'))
    log = setup_logger("e2e_verifier", level=config_manager.get("logging.level", "INFO"))

    log.info("E2E Verification Script Started.")
    log.info(f"Project root detected at: {project_root}")

    # 2. Get InfluxDB configuration
    influx_config = config_manager.get('storage.influxdb')
    if not influx_config or not influx_config.get('enabled'):
        log.critical("InfluxDB is not enabled in the configuration. Aborting.")
        sys.exit(1)

    url = f"http://{influx_config['host']}:{influx_config['port']}"
    token = influx_config['token']
    org = influx_config['org']
    bucket = influx_config['bucket']

    log.info(f"Connecting to InfluxDB at {url} in org '{org}'...")

    try:
        with InfluxDBClient(url=url, token=token, org=org) as client:
            query_api = client.query_api()
            log.info("Successfully connected to InfluxDB.")

            # 3. Query for the test data with retries
            for attempt in range(QUERY_RETRIES):
                log.info(f"Querying for symbol '{TEST_SYMBOL}' (Attempt {attempt + 1}/{QUERY_RETRIES})...")
                
                # Query for data written in the last 5 minutes to narrow the search
                time_filter = (datetime.utcnow() - timedelta(minutes=5)).isoformat() + "Z"

                query = f'''
                from(bucket: "{bucket}")
                  |> range(start: -5m)
                  |> filter(fn: (r) => r["_measurement"] == "kline_data")
                  |> filter(fn: (r) => r["symbol"] == "{TEST_SYMBOL}")
                  |> sort(columns: ["_time"], desc: true)
                  |> limit(n: 1)
                '''
                
                tables = query_api.query(query, org=org)

                if tables and tables[0].records:
                    record = tables[0].records[0]
                    log.info(f"SUCCESS: Record found for symbol '{TEST_SYMBOL}'.")
                    log.debug(f"Record details: time={record.get_time()}, close_price={record.get_value()}")

                    # 4. Assertions
                    # The injected data has a specific close price of 1000.5
                    expected_close = 1000.5
                    actual_close = record.get_value()

                    if record.get_field() == "close_price" and abs(actual_close - expected_close) < 0.001:
                        log.info(f"SUCCESS: Close price assertion passed. Expected: {expected_close}, Got: {actual_close}")
                        print("SUCCESS: E2E data verification passed.")
                        sys.exit(0)
                    else:
                        log.error(f"FAILURE: Assertion failed. Expected close_price={expected_close}, but got {record.get_field()}={actual_close}.")
                        print(f"FAILURE: E2E data verification failed on assertion. Record: {record.values}")
                        sys.exit(1)

                log.warning("Record not found. Retrying after delay...")
                time.sleep(RETRY_DELAY_SECONDS)

            log.critical(f"FAILURE: Record for symbol '{TEST_SYMBOL}' not found after {QUERY_RETRIES} retries.")
            print("FAILURE: E2E data verification failed. Timed out waiting for data.")
            sys.exit(1)

    except Exception as e:
        log.critical(f"An unexpected error occurred: {e}", exc_info=True)
        print(f"FAILURE: E2E verification script crashed. Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
