#!/usr/bin/env python3
"""
神经架构搜索 (NAS) - 自动寻找最优模型架构
"""

import os
import torch
import logging
import optuna
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from dataclasses import dataclass
import time
import json

from src.models.unified_fusion_model import UnifiedSignalFusionModel
from src.utils.advanced_gpu_optimizer import AdvancedGPUOptimizer


@dataclass
class NASConfig:
    """NAS配置"""
    n_trials: int = 50
    timeout: int = 3600  # 1小时
    max_epochs: int = 5  # 每个试验的最大训练轮数
    early_stopping_patience: int = 3
    gpu_utilization_weight: float = 0.3  # GPU利用率权重
    accuracy_weight: float = 0.5  # 准确率权重
    speed_weight: float = 0.2  # 训练速度权重


class NeuralArchitectureSearch:
    """神经架构搜索器"""
    
    def __init__(self, config: Optional[NASConfig] = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or NASConfig()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.gpu_optimizer = AdvancedGPUOptimizer()
        
        # 创建Optuna study
        self.study = optuna.create_study(
            direction='maximize',
            study_name='neural_architecture_search',
            storage=None,  # 内存存储
            load_if_exists=True
        )
        
        self.best_architecture = None
        self.best_score = 0.0
        self.trial_results = []
    
    def define_search_space(self, trial: optuna.Trial) -> Dict[str, Any]:
        """定义架构搜索空间"""
        
        # 基础参数
        feature_dim = 39  # 固定特征维度
        
        # 隐藏层配置 - 搜索不同的层数和维度组合
        num_hidden_layers = trial.suggest_int('num_hidden_layers', 3, 8)
        
        # 动态生成隐藏层维度
        hidden_dims = []
        base_dim = trial.suggest_categorical('base_hidden_dim', [512, 1024, 2048, 4096])
        
        for i in range(num_hidden_layers):
            # 每层递减
            layer_dim = max(64, base_dim // (2 ** i))
            hidden_dims.append(layer_dim)
        
        # Transformer配置
        num_heads = trial.suggest_categorical('num_heads', [8, 16, 32, 64])
        num_layers = trial.suggest_int('num_layers', 6, 32)
        
        # 确保num_heads能被hidden_dims[0]整除
        while hidden_dims[0] % num_heads != 0:
            num_heads = num_heads // 2
            if num_heads < 4:
                num_heads = 4
                break
        
        # 高级配置
        intermediate_size = trial.suggest_categorical('intermediate_size', [2048, 4096, 8192, 16384])
        dropout = trial.suggest_float('dropout', 0.05, 0.3)
        
        # 架构选择
        use_deep_residual = trial.suggest_categorical('use_deep_residual', [True, False])
        use_gradient_checkpointing = trial.suggest_categorical('use_gradient_checkpointing', [True, False])
        
        # 训练配置
        batch_size = trial.suggest_categorical('batch_size', [4096, 8192, 16384])
        learning_rate = trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True)
        
        architecture = {
            'feature_dim': feature_dim,
            'hidden_dims': hidden_dims,
            'num_heads': num_heads,
            'num_layers': num_layers,
            'dropout': dropout,
            'output_dim': 3,
            'sequence_length': 1,
            'use_attention': True,
            'use_lstm': True,
            'use_transformer': True,
            'intermediate_size': intermediate_size,
            'max_position_embeddings': 1024,
            'use_gradient_checkpointing': use_gradient_checkpointing,
            'enable_flash_attention': True,
            'enable_tensor_cores': True,
            'enable_fused_ops': True,
            'use_deep_residual': use_deep_residual,
            'batch_size': batch_size,
            'learning_rate': learning_rate
        }
        
        return architecture
    
    def evaluate_architecture(self, architecture: Dict[str, Any], 
                            train_data: Tuple[np.ndarray, np.ndarray],
                            val_data: Tuple[np.ndarray, np.ndarray]) -> Dict[str, float]:
        """评估架构性能"""
        
        try:
            # 创建模型
            model = UnifiedSignalFusionModel(**{k: v for k, v in architecture.items() 
                                               if k not in ['batch_size', 'learning_rate']})
            
            # GPU优化
            model = self.gpu_optimizer.optimize_model_for_gpu(model)
            
            # 计算模型参数数量
            total_params = sum(p.numel() for p in model.parameters())
            
            # 创建优化器
            optimizer = torch.optim.AdamW(
                model.parameters(), 
                lr=architecture['learning_rate'],
                weight_decay=1e-4
            )
            
            criterion = torch.nn.CrossEntropyLoss()
            
            # 准备数据
            train_features, train_labels = train_data
            val_features, val_labels = val_data
            
            batch_size = architecture['batch_size']
            
            # 训练循环
            model.train()
            best_val_accuracy = 0.0
            training_times = []
            gpu_utilizations = []
            
            for epoch in range(self.config.max_epochs):
                epoch_start = time.time()
                
                # 训练
                for i in range(0, len(train_features), batch_size):
                    batch_features = torch.tensor(
                        train_features[i:i+batch_size], 
                        dtype=torch.float32, 
                        device=self.device
                    )
                    batch_labels = torch.tensor(
                        train_labels[i:i+batch_size], 
                        dtype=torch.long, 
                        device=self.device
                    )
                    
                    optimizer.zero_grad()
                    
                    # 混合精度训练
                    with torch.cuda.amp.autocast():
                        outputs = model(batch_features)
                        loss = criterion(outputs['signal'], batch_labels)
                    
                    loss.backward()
                    optimizer.step()
                
                epoch_time = time.time() - epoch_start
                training_times.append(epoch_time)
                
                # 验证
                model.eval()
                val_correct = 0
                val_total = 0
                
                with torch.no_grad():
                    for i in range(0, len(val_features), batch_size):
                        batch_features = torch.tensor(
                            val_features[i:i+batch_size], 
                            dtype=torch.float32, 
                            device=self.device
                        )
                        batch_labels = torch.tensor(
                            val_labels[i:i+batch_size], 
                            dtype=torch.long, 
                            device=self.device
                        )
                        
                        with torch.cuda.amp.autocast():
                            outputs = model(batch_features)
                        
                        _, predicted = torch.max(outputs['signal'], 1)
                        val_total += batch_labels.size(0)
                        val_correct += (predicted == batch_labels).sum().item()
                
                val_accuracy = val_correct / val_total
                best_val_accuracy = max(best_val_accuracy, val_accuracy)
                
                # 获取GPU利用率
                gpu_info = self.gpu_optimizer.monitor_gpu_utilization()
                if gpu_info:
                    gpu_utilizations.append(gpu_info.get('memory_allocated_percent', 0))
                
                model.train()
            
            # 计算性能指标
            avg_training_time = np.mean(training_times)
            avg_gpu_utilization = np.mean(gpu_utilizations) if gpu_utilizations else 0
            
            # 计算吞吐量
            samples_per_epoch = len(train_features)
            throughput = samples_per_epoch / avg_training_time
            
            results = {
                'accuracy': best_val_accuracy,
                'training_time': avg_training_time,
                'gpu_utilization': avg_gpu_utilization,
                'throughput': throughput,
                'total_params': total_params,
                'model_size_mb': total_params * 4 / (1024**2)  # FP32
            }
            
            # 清理GPU内存
            del model, optimizer
            torch.cuda.empty_cache()
            
            return results
            
        except Exception as e:
            self.logger.error(f"❌ 架构评估失败: {e}")
            # 返回最差分数
            return {
                'accuracy': 0.0,
                'training_time': float('inf'),
                'gpu_utilization': 0.0,
                'throughput': 0.0,
                'total_params': 0,
                'model_size_mb': 0.0
            }
    
    def objective(self, trial: optuna.Trial, 
                  train_data: Tuple[np.ndarray, np.ndarray],
                  val_data: Tuple[np.ndarray, np.ndarray]) -> float:
        """Optuna目标函数"""
        
        # 定义架构
        architecture = self.define_search_space(trial)
        
        self.logger.info(f"🧪 试验 {trial.number}: 评估架构...")
        self.logger.info(f"  隐藏层: {architecture['hidden_dims']}")
        self.logger.info(f"  注意力头: {architecture['num_heads']}")
        self.logger.info(f"  Transformer层: {architecture['num_layers']}")
        self.logger.info(f"  批次大小: {architecture['batch_size']}")
        
        # 评估架构
        results = self.evaluate_architecture(architecture, train_data, val_data)
        
        # 计算综合分数
        accuracy_score = results['accuracy']
        gpu_score = min(results['gpu_utilization'] / 100.0, 1.0)  # 归一化到[0,1]
        speed_score = min(results['throughput'] / 50000, 1.0)  # 归一化，50k samples/s为满分
        
        # 加权综合分数
        composite_score = (
            self.config.accuracy_weight * accuracy_score +
            self.config.gpu_utilization_weight * gpu_score +
            self.config.speed_weight * speed_score
        )
        
        # 记录结果
        trial_result = {
            'trial_number': trial.number,
            'architecture': architecture,
            'results': results,
            'composite_score': composite_score
        }
        self.trial_results.append(trial_result)
        
        self.logger.info(f"  准确率: {accuracy_score:.3f}")
        self.logger.info(f"  GPU利用率: {results['gpu_utilization']:.1f}%")
        self.logger.info(f"  吞吐量: {results['throughput']:.0f} samples/s")
        self.logger.info(f"  综合分数: {composite_score:.3f}")
        
        return composite_score
    
    def search(self, train_data: Tuple[np.ndarray, np.ndarray],
               val_data: Tuple[np.ndarray, np.ndarray]) -> Dict[str, Any]:
        """执行神经架构搜索"""
        
        self.logger.info(f"🚀 开始神经架构搜索...")
        self.logger.info(f"  试验次数: {self.config.n_trials}")
        self.logger.info(f"  超时时间: {self.config.timeout}秒")
        
        # 创建目标函数
        objective_func = lambda trial: self.objective(trial, train_data, val_data)
        
        # 执行搜索
        self.study.optimize(
            objective_func, 
            n_trials=self.config.n_trials,
            timeout=self.config.timeout
        )
        
        # 获取最佳结果
        self.best_architecture = self.study.best_params
        self.best_score = self.study.best_value
        
        self.logger.info(f"🏆 架构搜索完成!")
        self.logger.info(f"  最佳分数: {self.best_score:.4f}")
        self.logger.info(f"  最佳架构: {self.best_architecture}")
        
        # 生成详细报告
        search_results = {
            'best_architecture': self.best_architecture,
            'best_score': self.best_score,
            'n_trials': len(self.study.trials),
            'search_time': sum(t.duration.total_seconds() for t in self.study.trials if t.duration),
            'trial_results': self.trial_results[-10:],  # 最后10个试验结果
            'study': self.study
        }
        
        return search_results
    
    def save_results(self, results: Dict[str, Any], save_path: str):
        """保存搜索结果"""
        
        # 准备可序列化的结果
        serializable_results = {
            'best_architecture': results['best_architecture'],
            'best_score': results['best_score'],
            'n_trials': results['n_trials'],
            'search_time': results['search_time'],
            'trial_results': results['trial_results']
        }
        
        with open(save_path, 'w') as f:
            json.dump(serializable_results, f, indent=2)
        
        self.logger.info(f"💾 搜索结果已保存到: {save_path}")


def create_nas_optimizer(config_dict: Dict[str, Any] = None) -> NeuralArchitectureSearch:
    """创建NAS优化器"""
    if config_dict:
        config = NASConfig(**config_dict)
    else:
        config = NASConfig()
    
    return NeuralArchitectureSearch(config)
