"""
超参数优化器 - 自动调参提高模型性能
"""

import optuna
import torch
import numpy as np
from typing import Dict, Any, List, Optional, Callable, Tuple
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import accuracy_score, f1_score, precision_score, recall_score
import logging
import json
import tempfile
from pathlib import Path
from datetime import datetime


import asyncio
import pandas as pd


class HyperparameterOptimizer:
    """超参数优化器"""
    
    def __init__(
        self,
        model_class,
        objective_metric: str = 'f1_weighted',
        n_trials: int = 100,
        cv_folds: int = 5,
        random_state: int = 42,
        study_name: Optional[str] = None,
        storage: Optional[str] = None
    ):
        """
        初始化超参数优化器
        
        Args:
            model_class: 模型类
            objective_metric: 优化目标指标
            n_trials: 优化试验次数
            cv_folds: 交叉验证折数
            random_state: 随机种子
            study_name: 研究名称
            storage: 存储后端
        """
        self.model_class = model_class
        self.objective_metric = objective_metric
        self.n_trials = n_trials
        self.cv_folds = cv_folds
        self.random_state = random_state
        
        # 创建Optuna研究
        self.study_name = study_name or f"hyperopt_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.study = optuna.create_study(
            direction='maximize',
            study_name=self.study_name,
            storage=storage
        )
        
        self.logger = logging.getLogger(__name__)
        
        # 最佳参数和结果
        self.best_params = None
        self.best_score = None
        self.optimization_history = []
    
    def define_search_space(self, trial: optuna.Trial) -> Dict[str, Any]:
        """定义搜索空间"""
        params = {
            # 模型架构参数
            'feature_dim': trial.suggest_categorical('feature_dim', [32, 64, 128, 256]),
            'hidden_dims': self._suggest_hidden_dims(trial),
            'num_heads': trial.suggest_categorical('num_heads', [4, 8, 12, 16]),
            'num_layers': trial.suggest_int('num_layers', 2, 6),
            'dropout': trial.suggest_float('dropout', 0.1, 0.5),
            
            # 训练参数
            'learning_rate': trial.suggest_float('learning_rate', 1e-5, 1e-2, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64, 128]),
            'weight_decay': trial.suggest_float('weight_decay', 1e-6, 1e-3, log=True),
            
            # 集成参数（如果适用）
            'n_models': trial.suggest_int('n_models', 3, 7),
            'ensemble_method': trial.suggest_categorical('ensemble_method', 
                                                       ['voting', 'weighted_voting', 'stacking', 'attention']),
            'diversity_weight': trial.suggest_float('diversity_weight', 0.01, 0.3),
            
            # 正则化参数
            'label_smoothing': trial.suggest_float('label_smoothing', 0.0, 0.2),
            'gradient_clip': trial.suggest_float('gradient_clip', 0.5, 2.0),
            
            # 数据增强参数
            'noise_std': trial.suggest_float('noise_std', 0.0, 0.1),
            'mixup_alpha': trial.suggest_float('mixup_alpha', 0.0, 0.4),
        }
        
        return params
    
    def _suggest_hidden_dims(self, trial: optuna.Trial) -> List[int]:
        """建议隐藏层维度"""
        n_layers = trial.suggest_int('n_hidden_layers', 2, 5)
        hidden_dims = []
        
        for i in range(n_layers):
            dim = trial.suggest_categorical(f'hidden_dim_{i}', [64, 128, 256, 512])
            hidden_dims.append(dim)
        
        # 确保维度递减
        hidden_dims.sort(reverse=True)
        return hidden_dims
    
    def objective(self, trial: optuna.Trial, X: np.ndarray, y: np.ndarray) -> float:
        """
        优化目标函数 - 已重构为与主应用训练流程集成。
        """
        # 使用临时目录来处理交叉验证的临时数据文件
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            try:
                # 1. 定义本次试验的超参数
                params = self.define_search_space(trial)
                
                # 2. 实例化AppOrchestrator以运行真实的训练流程
                from src.app import AppOrchestrator
                orchestrator = AppOrchestrator()
                orchestrator.initialize() # 确保应用被初始化

                # 3. 配置训练参数
                training_kwargs = {
                    'learning_rate': params['learning_rate'],
                    'batch_size': params['batch_size'],
                    'epochs': 15,  # 增加epoch以获得更可靠的评估
                }

                # 4. 执行交叉验证
                cv = StratifiedKFold(n_splits=self.cv_folds, shuffle=True, random_state=self.random_state)
                scores = []

                for i, (train_idx, val_idx) in enumerate(cv.split(X, y)):
                    X_train, y_train = X[train_idx], y[train_idx]
                    X_val, y_val = X[val_idx], y[val_idx]

                    # 将内存中的数据保存到临时Parquet文件，以适配当前基于文件的训练管道
                    # 注意：这是一个桥接步骤。理想的长期解决方案是重构管道以接受内存数据。
                    
                    # 合并为一个DataFrame以便于保存
                    # 假设X的列是特征，y是目标
                    num_features = X_train.shape[1]
                    feature_cols = [f'feature_{j}' for j in range(num_features)]
                    
                    train_df = pd.DataFrame(X_train, columns=feature_cols)
                    train_df['target'] = y_train
                    
                    val_df = pd.DataFrame(X_val, columns=feature_cols)
                    val_df['target'] = y_val

                    # 为了让 `create_dataloaders_from_file` 工作，它需要一个统一的文件
                    # 和一个数据分割比例。我们将训练和验证数据合并，然后让它重新分割。
                    combined_df = pd.concat([train_df, val_df], ignore_index=True)
                    temp_data_path = temp_path / f"fold_{i}_data.parquet"
                    combined_df.to_parquet(temp_data_path)

                    # 计算验证集在此次合并中的比例
                    val_ratio = len(val_df) / len(combined_df)

                    # 覆盖data_path并设置分割比例
                    fold_kwargs = training_kwargs.copy()
                    fold_kwargs['data_path'] = str(temp_data_path)
                    fold_kwargs['data_split'] = (1.0 - val_ratio, val_ratio, 0.0) # (train, val, test)

                    # 使用asyncio.run()来执行异步的训练方法
                    results = asyncio.run(orchestrator.app.run_training(**fold_kwargs))
                    
                    # 从结果中提取最终的验证分数
                    # 这需要run_training返回可解析的结果
                    final_epoch_key = f"epoch_{training_kwargs['epochs'] - 1}"
                    if results and final_epoch_key in results:
                        score = results[final_epoch_key]['val'].get('accuracy', 0.0)
                    else:
                        score = 0.0 # 训练失败或未返回结果
                    
                    scores.append(score)

                avg_score = np.mean(scores)
                
                self.optimization_history.append({
                    'trial': trial.number,
                    'params': params,
                    'score': avg_score,
                })
                
                return avg_score
                
            except Exception as e:
                self.logger.warning(f"Trial {trial.number} failed: {e}", exc_info=True)
                return 0.0
    
    def _train_and_evaluate(self, model, X_train: np.ndarray, y_train: np.ndarray,
                          X_val: np.ndarray, y_val: np.ndarray, params: Dict[str, Any]) -> float:
        """训练和评估模型"""
        # 转换为PyTorch张量
        X_train_tensor = np.array(X_train, dtype=np.float32)
        y_train_tensor = np.array(y_train, dtype=np.int64)
        X_val_tensor = np.array(X_val, dtype=np.float32)
        y_val_tensor = np.array(y_val, dtype=np.int64)
        
        # 创建数据加载器
        train_dataset = torch.utils.data.TensorDataset(X_train_tensor, y_train_tensor)
        train_loader = torch.utils.data.DataLoader(
            train_dataset, 
            batch_size=params['batch_size'], 
            shuffle=True
        )
        
        # 优化器
        optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=params['learning_rate'],
            weight_decay=params['weight_decay']
        )
        
        # 训练
        model.train()
        for epoch in range(10):  # 快速训练
            for batch_X, batch_y in train_loader:
                optimizer.zero_grad()
                
                outputs = model(batch_X.unsqueeze(1))  # 添加序列维度
                loss = torch.nn.CrossEntropyLoss()(outputs['signal'], batch_y)
                
                loss.backward()
                torch.nn.utils.clip_grad_norm_(model.parameters(), params['gradient_clip'])
                optimizer.step()
        
        # 评估
        model.eval()
        with torch.no_grad():
            outputs = model(X_val_tensor.unsqueeze(1))
            predictions = torch.argmax(outputs['signal'], dim=-1).cpu().numpy()
        
        # 计算指标
        if self.objective_metric == 'accuracy':
            score = accuracy_score(y_val, predictions)
        elif self.objective_metric == 'f1_weighted':
            score = f1_score(y_val, predictions, average='weighted')
        elif self.objective_metric == 'f1_macro':
            score = f1_score(y_val, predictions, average='macro')
        elif self.objective_metric == 'precision_weighted':
            score = precision_score(y_val, predictions, average='weighted')
        elif self.objective_metric == 'recall_weighted':
            score = recall_score(y_val, predictions, average='weighted')
        else:
            score = f1_score(y_val, predictions, average='weighted')
        
        return score
    
    def optimize(self, X: np.ndarray, y: np.ndarray) -> Dict[str, Any]:
        """执行超参数优化"""
        self.logger.info(f"开始超参数优化，目标指标: {self.objective_metric}")
        self.logger.info(f"试验次数: {self.n_trials}, 交叉验证折数: {self.cv_folds}")
        
        # 创建目标函数
        objective_func = lambda trial: self.objective(trial, X, y)
        
        # 执行优化
        self.study.optimize(objective_func, n_trials=self.n_trials)
        
        # 获取最佳结果
        self.best_params = self.study.best_params
        self.best_score = self.study.best_value
        
        self.logger.info(f"优化完成！最佳分数: {self.best_score:.4f}")
        self.logger.info(f"最佳参数: {self.best_params}")
        
        return {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'n_trials': len(self.study.trials),
            'study': self.study
        }
    
    def get_optimization_results(self) -> Dict[str, Any]:
        """获取优化结果"""
        return {
            'best_params': self.best_params,
            'best_score': self.best_score,
            'optimization_history': self.optimization_history,
            'study_summary': {
                'n_trials': len(self.study.trials),
                'best_trial': self.study.best_trial.number,
                'best_value': self.study.best_value
            }
        }
    
    def save_results(self, filepath: str):
        """保存优化结果"""
        results = self.get_optimization_results()
        
        # 确保目录存在
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        with open(filepath, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        self.logger.info(f"优化结果已保存到: {filepath}")
    
    def load_results(self, filepath: str):
        """加载优化结果"""
        with open(filepath, 'r') as f:
            results = json.load(f)
        
        self.best_params = results['best_params']
        self.best_score = results['best_score']
        self.optimization_history = results['optimization_history']
        
        self.logger.info(f"优化结果已从 {filepath} 加载")
    
    def create_optimized_model(self, **override_params):
        """使用最佳参数创建模型"""
        if self.best_params is None:
            raise ValueError("请先执行优化或加载优化结果")
        
        # 合并参数
        params = self.best_params.copy()
        params.update(override_params)
        
        return self.model_class(**params)


class BayesianOptimizer(HyperparameterOptimizer):
    """贝叶斯优化器"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 使用TPE采样器
        self.study = optuna.create_study(
            direction='maximize',
            study_name=self.study_name,
            sampler=optuna.samplers.TPESampler(seed=self.random_state)
        )


class MultiObjectiveOptimizer:
    """多目标优化器"""
    
    def __init__(self, model_class, objectives: List[str] = ['f1_weighted', 'accuracy']):
        self.model_class = model_class
        self.objectives = objectives
        
        self.study = optuna.create_study(
            directions=['maximize'] * len(objectives),
            sampler=optuna.samplers.NSGAIISampler()
        )
    
    def objective(self, trial: optuna.Trial, X: np.ndarray, y: np.ndarray) -> List[float]:
        """多目标优化函数"""
        # 实现多目标评估逻辑
        pass


def optimize_hyperparameters(model_class, X: np.ndarray, y: np.ndarray,
                           objective_metric: str = 'f1_weighted',
                           n_trials: int = 50) -> Dict[str, Any]:
    """便捷的超参数优化函数"""
    optimizer = HyperparameterOptimizer(
        model_class=model_class,
        objective_metric=objective_metric,
        n_trials=n_trials
    )
    
    return optimizer.optimize(X, y)
