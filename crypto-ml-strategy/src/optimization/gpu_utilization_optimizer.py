"""
GPU利用率优化器
专门针对训练阶段的GPU利用率优化，解决GPU利用率过低问题
"""

import os
import time
import torch
import torch.nn as nn
import numpy as np
import logging
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass
import psutil
import threading
from collections import deque

@dataclass
class GPUUtilizationConfig:
    """GPU利用率优化配置"""
    target_utilization: float = 0.85  # 目标GPU利用率
    min_batch_size: int = 512
    max_batch_size: int = 16384
    batch_size_step: int = 256
    memory_safety_margin: float = 0.05  # 5%安全边际
    warmup_iterations: int = 10
    measurement_iterations: int = 20
    enable_async_loading: bool = True
    enable_prefetch: bool = True
    num_workers_multiplier: float = 2.0

class GPUUtilizationOptimizer:
    """GPU利用率优化器"""
    
    def __init__(self, config: GPUUtilizationConfig = None):
        self.config = config or GPUUtilizationConfig()
        self.logger = logging.getLogger(__name__)
        
        # 检查环境变量以强制禁用CUDA
        force_cpu = os.environ.get('FORCE_CPU_TESTING', 'false').lower() == 'true'

        # GPU监控
        self.device = torch.device('cuda' if not force_cpu and torch.cuda.is_available() else 'cpu')
        if force_cpu:
            self.logger.warning("FORCE_CPU_TESTING is set. GPU-related optimizations are disabled.")
        
        self.gpu_utilization_history = deque(maxlen=100)
        self.memory_usage_history = deque(maxlen=100)
        
        # 性能统计
        self.optimization_stats = {
            'original_batch_size': None,
            'optimized_batch_size': None,
            'original_utilization': None,
            'optimized_utilization': None,
            'throughput_improvement': None
        }
        
        # 监控线程
        self.monitoring_active = False
        self.monitor_thread = None
    
    def start_gpu_monitoring(self):
        """启动GPU监控"""
        if self.device.type != 'cuda':
            return
        
        self.monitoring_active = True
        self.monitor_thread = threading.Thread(target=self._monitor_gpu_usage, daemon=True)
        self.monitor_thread.start()
        self.logger.info("🔍 GPU监控已启动")
    
    def stop_gpu_monitoring(self):
        """停止GPU监控"""
        self.monitoring_active = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        self.logger.info("⏹️ GPU监控已停止")
    
    def _monitor_gpu_usage(self):
        """监控GPU使用情况"""
        try:
            import pynvml
            pynvml.nvmlInit()
            handle = pynvml.nvmlDeviceGetHandleByIndex(0)

            while self.monitoring_active:
                try:
                    # 获取GPU利用率
                    utilization = pynvml.nvmlDeviceGetUtilizationRates(handle)
                    gpu_util = utilization.gpu

                    # 获取内存使用
                    memory_info = pynvml.nvmlDeviceGetMemoryInfo(handle)
                    memory_util = memory_info.used / memory_info.total

                    self.gpu_utilization_history.append(gpu_util)
                    self.memory_usage_history.append(memory_util)

                    time.sleep(0.5)  # 每0.5秒监控一次

                except Exception as e:
                    self.logger.debug(f"GPU监控错误: {e}")
                    time.sleep(1.0)

        except ImportError:
            self.logger.warning("⚠️ pynvml未安装，使用torch监控")
            self._monitor_gpu_usage_torch()
        except Exception as e:
            self.logger.error(f"❌ GPU监控失败: {e}")
            self._monitor_gpu_usage_torch()  # 回退到torch监控
    
    def _monitor_gpu_usage_torch(self):
        """使用torch监控GPU使用情况"""
        while self.monitoring_active:
            try:
                if not os.environ.get('FORCE_CPU_TESTING', 'false').lower() == 'true' and torch.cuda.is_available():
                    memory_info = torch.cuda.memory_stats()
                    allocated = memory_info.get('allocated_bytes.all.current', 0)
                    reserved = memory_info.get('reserved_bytes.all.current', 0)
                    
                    total_memory = torch.cuda.get_device_properties(0).total_memory
                    memory_util = allocated / total_memory
                    
                    self.memory_usage_history.append(memory_util)
                    
                time.sleep(1.0)
                
            except Exception as e:
                self.logger.debug(f"Torch GPU监控错误: {e}")
                time.sleep(2.0)
    
    def find_optimal_batch_size(self, model: nn.Module, sample_input: torch.Tensor) -> int:
        """找到最优批次大小"""
        self.logger.info("🔍 开始寻找最优批次大小...")
        
        model.eval()
        best_batch_size = self.config.min_batch_size
        best_throughput = 0
        
        # 启动监控
        self.start_gpu_monitoring()
        
        try:
            for batch_size in range(
                self.config.min_batch_size,
                self.config.max_batch_size + 1,
                self.config.batch_size_step
            ):
                try:
                    throughput = self._test_batch_size(model, sample_input, batch_size)
                    
                    if throughput > best_throughput:
                        best_throughput = throughput
                        best_batch_size = batch_size
                    
                    self.logger.info(f"📊 批次大小 {batch_size}: {throughput:.2f} samples/sec")
                    
                    # 检查内存使用
                    if self.memory_usage_history:
                        current_memory = self.memory_usage_history[-1]
                        if current_memory > (1.0 - self.config.memory_safety_margin):
                            self.logger.warning(f"⚠️ 内存使用过高 ({current_memory:.1%})，停止测试")
                            break
                    
                except RuntimeError as e:
                    if "out of memory" in str(e).lower():
                        self.logger.warning(f"⚠️ 批次大小 {batch_size} 内存不足")
                        break
                    else:
                        raise e
                
                # 清理GPU内存
                torch.cuda.empty_cache()
                time.sleep(0.5)
        
        finally:
            self.stop_gpu_monitoring()
        
        self.optimization_stats['optimized_batch_size'] = best_batch_size
        self.logger.info(f"🎯 最优批次大小: {best_batch_size} (吞吐量: {best_throughput:.2f} samples/sec)")
        
        return best_batch_size
    
    def _test_batch_size(self, model: nn.Module, sample_input: torch.Tensor, batch_size: int) -> float:
        """测试特定批次大小的性能"""
        # 创建测试批次
        input_shape = sample_input.shape[1:]  # 去掉batch维度
        test_input = torch.randn(batch_size, *input_shape, device=self.device, dtype=sample_input.dtype)
        
        # 预热
        with torch.no_grad():
            for _ in range(self.config.warmup_iterations):
                _ = model(test_input)
        
        torch.cuda.synchronize()
        
        # 性能测试
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(self.config.measurement_iterations):
                _ = model(test_input)
        
        torch.cuda.synchronize()
        end_time = time.time()
        
        # 计算吞吐量
        total_samples = batch_size * self.config.measurement_iterations
        elapsed_time = end_time - start_time
        throughput = total_samples / elapsed_time
        
        return throughput
    
    def optimize_dataloader_config(self, dataset_size: int) -> Dict[str, Any]:
        """🔥 优化数据加载器配置 - 基于完整训练循环实测结果"""
        cpu_count = psutil.cpu_count(logical=False)

        # 🔥 重要发现：在完整训练循环中，workers数量应该匹配CPU核心数
        # 过多workers会导致上下文切换开销和资源竞争

        if dataset_size >= 100000:
            # 大数据集：使用CPU核心数，确保充分利用但不过度竞争
            optimal_workers = cpu_count
            prefetch_factor = max(2, cpu_count // 2)
        elif dataset_size >= 50000:
            # 中等数据集：使用CPU核心数的75%
            optimal_workers = max(4, int(cpu_count * 0.75))
            prefetch_factor = max(2, optimal_workers // 2)
        else:
            # 小数据集：使用较少workers避免开销
            optimal_workers = max(2, cpu_count // 2)
            prefetch_factor = 2

        # 确保不超过CPU核心数太多（最多1.5倍）
        optimal_workers = min(optimal_workers, int(cpu_count * 1.5))

        config = {
            'num_workers': optimal_workers,
            'pin_memory': True,
            'persistent_workers': True,
            'prefetch_factor': prefetch_factor,
            'drop_last': True,
            'multiprocessing_context': 'spawn' if os.name == 'nt' else 'fork'
        }

        self.logger.info(f"🔧 数据加载器优化配置 (数据集: {dataset_size}, CPU核心: {cpu_count}): {config}")
        return config
    
    def optimize_model_for_training(self, model: nn.Module) -> nn.Module:
        """为训练优化模型"""
        self.logger.info("🚀 开始模型训练优化...")
        
        # 移动到GPU
        model = model.to(self.device)

        # 启用torch.compile（如果可用）- 在混合精度之前编译
        compile_success = False
        if hasattr(torch, 'compile'):
            try:
                model = torch.compile(
                    model,
                    mode='max-autotune',
                    fullgraph=False,
                    dynamic=True
                )
                compile_success = True
                self.logger.info("✅ 模型编译优化完成")
            except Exception as e:
                self.logger.warning(f"⚠️ 模型编译失败: {e}")

        # 启用混合精度训练（仅在编译失败时使用half()）
        if not compile_success and hasattr(model, 'half') and self.device.type == 'cuda':
            try:
                model = model.half()
                self.logger.info("✅ 混合精度（half）已启用")
            except Exception as e:
                self.logger.warning(f"⚠️ 混合精度启用失败: {e}")
        
        # 启用梯度检查点（对于大模型）
        if hasattr(model, 'gradient_checkpointing_enable'):
            try:
                model.gradient_checkpointing_enable()
                self.logger.info("✅ 梯度检查点已启用")
            except Exception as e:
                self.logger.debug(f"梯度检查点启用失败: {e}")
        
        return model
    
    def setup_cuda_optimizations(self):
        """设置CUDA优化"""
        if self.device.type != 'cuda':
            return
        
        # 启用cuDNN benchmark
        torch.backends.cudnn.benchmark = True
        torch.backends.cudnn.deterministic = False
        
        # 启用TF32（如果支持）
        if hasattr(torch.backends.cuda, 'matmul'):
            torch.backends.cuda.matmul.allow_tf32 = True
        if hasattr(torch.backends.cudnn, 'allow_tf32'):
            torch.backends.cudnn.allow_tf32 = True
        
        # 设置内存分配策略
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = (
            'max_split_size_mb:512,'
            'roundup_power2_divisions:16,'
            'garbage_collection_threshold:0.8'
        )
        
        self.logger.info("✅ CUDA优化设置完成")
    
    def get_current_gpu_utilization(self) -> Dict[str, float]:
        """获取当前GPU利用率"""
        if not self.gpu_utilization_history or not self.memory_usage_history:
            return {'gpu_utilization': 0.0, 'memory_utilization': 0.0}
        
        return {
            'gpu_utilization': np.mean(list(self.gpu_utilization_history)[-10:]),
            'memory_utilization': np.mean(list(self.memory_usage_history)[-10:])
        }
    
    def generate_optimization_report(self) -> Dict[str, Any]:
        """生成优化报告"""
        current_stats = self.get_current_gpu_utilization()
        
        report = {
            'optimization_stats': self.optimization_stats.copy(),
            'current_utilization': current_stats,
            'recommendations': []
        }
        
        # 生成建议
        gpu_util = current_stats.get('gpu_utilization', 0)
        memory_util = current_stats.get('memory_utilization', 0)
        
        if gpu_util < 50:
            report['recommendations'].append("GPU利用率过低，建议增加批次大小或启用模型并行")
        
        if memory_util < 0.7:
            report['recommendations'].append("GPU内存使用率较低，可以进一步增加批次大小")
        
        if memory_util > 0.95:
            report['recommendations'].append("GPU内存使用率过高，建议减少批次大小或启用梯度检查点")
        
        return report
    
    def apply_optimizations(self, model: nn.Module, sample_input: torch.Tensor, 
                          dataset_size: int) -> Tuple[nn.Module, int, Dict[str, Any]]:
        """应用所有优化"""
        self.logger.info("🚀 开始应用GPU利用率优化...")
        
        # 设置CUDA优化
        self.setup_cuda_optimizations()
        
        # 优化模型
        optimized_model = self.optimize_model_for_training(model)
        
        # 找到最优批次大小
        optimal_batch_size = self.find_optimal_batch_size(optimized_model, sample_input)
        
        # 优化数据加载器配置
        dataloader_config = self.optimize_dataloader_config(dataset_size)
        
        # 生成报告
        report = self.generate_optimization_report()
        
        self.logger.info("✅ GPU利用率优化完成")
        return optimized_model, optimal_batch_size, dataloader_config


def create_gpu_utilization_optimizer(target_utilization: float = 0.85) -> GPUUtilizationOptimizer:
    """创建GPU利用率优化器"""
    config = GPUUtilizationConfig(target_utilization=target_utilization)
    return GPUUtilizationOptimizer(config)
