"""
超大批次大小优化器
实施梯度累积和动态批次大小调整以最大化GPU利用率
"""

import time
import torch
import torch.nn as nn
import logging
import subprocess
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import numpy as np


@dataclass
class BatchConfig:
    """批次配置"""
    physical_batch_size: int    # 实际GPU批次大小
    logical_batch_size: int     # 逻辑批次大小（通过梯度累积实现）
    accumulation_steps: int     # 梯度累积步数
    memory_usage_percent: float # 内存使用百分比


class LargeBatchOptimizer:
    """超大批次大小优化器"""
    
    def __init__(self, device: torch.device):
        """初始化优化器"""
        self.device = device
        self.logger = logging.getLogger(__name__)
        
        # 获取GPU内存信息
        if torch.cuda.is_available():
            self.total_memory = torch.cuda.get_device_properties(0).total_memory
            self.total_memory_gb = self.total_memory / (1024**3)
        else:
            self.total_memory = 0
            self.total_memory_gb = 0
        
        self.logger.info(f"🔧 GPU内存: {self.total_memory_gb:.1f}GB")
    
    def get_gpu_utilization(self) -> Tuple[float, float]:
        """获取GPU利用率和内存使用"""
        try:
            result = subprocess.run([
                'nvidia-smi', 
                '--query-gpu=utilization.gpu,memory.used,memory.total',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=2)
            
            if result.returncode == 0:
                values = result.stdout.strip().split(', ')
                gpu_util = float(values[0])
                memory_used = float(values[1])
                memory_total = float(values[2])
                memory_percent = (memory_used / memory_total) * 100
                return gpu_util, memory_percent
        except:
            pass
        
        return 0.0, 0.0
    
    def find_max_physical_batch_size(self, model: nn.Module, input_shape: Tuple[int, ...],
                                    safety_margin: float = 0.1) -> int:
        """找到最大物理批次大小"""
        self.logger.info("🔍 寻找最大物理批次大小...")
        
        # 二分搜索最大批次大小
        min_batch = 1024
        max_batch = 262144  # 256K
        best_batch = min_batch
        
        while min_batch <= max_batch:
            mid_batch = (min_batch + max_batch) // 2
            
            try:
                # 测试批次大小
                test_input = torch.randn(mid_batch, *input_shape[1:], device=self.device)
                test_labels = torch.randint(0, 3, (mid_batch,), device=self.device)
                
                # 前向传播
                with torch.no_grad():
                    outputs = model(test_input)
                    loss = nn.CrossEntropyLoss()(outputs, test_labels)
                
                # 反向传播测试
                optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
                optimizer.zero_grad()
                outputs = model(test_input)
                loss = nn.CrossEntropyLoss()(outputs, test_labels)
                loss.backward()
                optimizer.step()
                
                # 检查内存使用
                memory_used = torch.cuda.memory_allocated()
                memory_percent = memory_used / self.total_memory
                
                if memory_percent < (1.0 - safety_margin):
                    best_batch = mid_batch
                    min_batch = mid_batch + 1
                    self.logger.info(f"✅ 批次大小 {mid_batch}: 内存使用 {memory_percent:.1%}")
                else:
                    max_batch = mid_batch - 1
                    self.logger.info(f"⚠️ 批次大小 {mid_batch}: 内存使用过高 {memory_percent:.1%}")
                
                # 清理内存
                del test_input, test_labels, outputs, loss
                torch.cuda.empty_cache()
                
            except RuntimeError as e:
                if "out of memory" in str(e):
                    max_batch = mid_batch - 1
                    self.logger.info(f"❌ 批次大小 {mid_batch}: 内存不足")
                    torch.cuda.empty_cache()
                else:
                    raise e
        
        self.logger.info(f"🏆 最大物理批次大小: {best_batch}")
        return best_batch
    
    def test_gradient_accumulation(self, model: nn.Module, physical_batch_size: int,
                                 accumulation_steps_list: List[int]) -> Dict[int, Dict[str, Any]]:
        """测试不同梯度累积步数的性能"""
        self.logger.info("🧪 测试梯度累积性能...")
        
        results = {}
        
        for accumulation_steps in accumulation_steps_list:
            logical_batch_size = physical_batch_size * accumulation_steps
            
            self.logger.info(f"🔍 测试梯度累积: {accumulation_steps}步, "
                           f"逻辑批次: {logical_batch_size}")
            
            try:
                # 创建测试数据
                test_batches = []
                for _ in range(accumulation_steps):
                    features = torch.randn(physical_batch_size, 59, device=self.device)
                    labels = torch.randint(0, 3, (physical_batch_size,), device=self.device)
                    test_batches.append((features, labels))
                
                criterion = nn.CrossEntropyLoss()
                optimizer = torch.optim.AdamW(model.parameters(), lr=1e-3)
                
                # 预热
                for _ in range(3):
                    optimizer.zero_grad()
                    total_loss = 0
                    
                    for features, labels in test_batches:
                        outputs = model(features)
                        loss = criterion(outputs, labels) / accumulation_steps
                        loss.backward()
                        total_loss += loss.item()
                    
                    optimizer.step()
                
                torch.cuda.synchronize()
                
                # 性能测试
                start_time = time.time()
                num_iterations = 20
                total_samples = 0
                
                for _ in range(num_iterations):
                    optimizer.zero_grad()
                    total_loss = 0
                    
                    for features, labels in test_batches:
                        outputs = model(features)
                        loss = criterion(outputs, labels) / accumulation_steps
                        loss.backward()
                        total_loss += loss.item()
                    
                    optimizer.step()
                    total_samples += logical_batch_size
                
                torch.cuda.synchronize()
                
                end_time = time.time()
                total_time = end_time - start_time
                throughput = total_samples / total_time
                
                # 获取GPU利用率
                gpu_util, memory_percent = self.get_gpu_utilization()
                
                results[accumulation_steps] = {
                    'logical_batch_size': logical_batch_size,
                    'throughput': throughput,
                    'gpu_utilization': gpu_util,
                    'memory_percent': memory_percent,
                    'avg_step_time': total_time / num_iterations
                }
                
                self.logger.info(f"📊 累积步数 {accumulation_steps}: {throughput:.0f} samples/s, "
                               f"GPU: {gpu_util:.1f}%, 内存: {memory_percent:.1f}%")
                
                # 清理
                del test_batches
                torch.cuda.empty_cache()
                
            except Exception as e:
                self.logger.error(f"❌ 梯度累积测试失败 {accumulation_steps}: {e}")
                torch.cuda.empty_cache()
        
        return results
    
    def find_optimal_batch_config(self, model: nn.Module) -> BatchConfig:
        """找到最优批次配置"""
        self.logger.info("🔍 寻找最优批次配置...")
        
        # 1. 找到最大物理批次大小
        max_physical_batch = self.find_max_physical_batch_size(model, (1, 59))
        
        # 2. 测试不同的梯度累积配置
        accumulation_steps_list = [1, 2, 4, 8, 16, 32]
        accumulation_results = self.test_gradient_accumulation(
            model, max_physical_batch, accumulation_steps_list
        )
        
        # 3. 找到最优配置（综合考虑吞吐量和GPU利用率）
        best_config = None
        best_score = 0
        
        for steps, result in accumulation_results.items():
            # 综合得分：吞吐量 * GPU利用率 * 内存效率
            memory_efficiency = min(result['memory_percent'] / 80, 1.0)  # 80%为目标
            score = (result['throughput'] * 
                    (result['gpu_utilization'] / 100) * 
                    memory_efficiency)
            
            if score > best_score:
                best_score = score
                best_config = BatchConfig(
                    physical_batch_size=max_physical_batch,
                    logical_batch_size=result['logical_batch_size'],
                    accumulation_steps=steps,
                    memory_usage_percent=result['memory_percent']
                )
        
        if best_config:
            self.logger.info(f"🏆 最优批次配置:")
            self.logger.info(f"   - 物理批次: {best_config.physical_batch_size}")
            self.logger.info(f"   - 逻辑批次: {best_config.logical_batch_size}")
            self.logger.info(f"   - 累积步数: {best_config.accumulation_steps}")
            self.logger.info(f"   - 内存使用: {best_config.memory_usage_percent:.1f}%")
        
        return best_config
    
    def implement_gradient_accumulation_trainer(self, model: nn.Module, 
                                              config: BatchConfig) -> 'GradientAccumulationTrainer':
        """实现梯度累积训练器"""
        return GradientAccumulationTrainer(model, config, self.device)


class GradientAccumulationTrainer:
    """梯度累积训练器"""
    
    def __init__(self, model: nn.Module, config: BatchConfig, device: torch.device):
        """初始化训练器"""
        self.model = model
        self.config = config
        self.device = device
        self.logger = logging.getLogger(__name__)
        
        # 编译模型
        try:
            self.model = torch.compile(self.model, mode='max-autotune')
            self.logger.info("✅ 模型编译成功 (max-autotune)")
        except Exception as e:
            self.logger.warning(f"⚠️ 模型编译失败: {e}")
    
    def train_step(self, dataloader, optimizer, criterion) -> Dict[str, Any]:
        """执行一个训练步骤（包含梯度累积）"""
        optimizer.zero_grad(set_to_none=True)
        
        total_loss = 0
        total_samples = 0
        step_start_time = time.time()
        
        # 梯度累积循环
        for i, batch in enumerate(dataloader):
            if i >= self.config.accumulation_steps:
                break
            
            features, labels = batch
            features = features.to(self.device, non_blocking=True)
            labels = labels.to(self.device, non_blocking=True)
            
            # 前向传播
            outputs = self.model(features)
            loss = criterion(outputs, labels) / self.config.accumulation_steps
            
            # 反向传播
            loss.backward()
            
            total_loss += loss.item()
            total_samples += features.size(0)
        
        # 优化器步骤
        optimizer.step()
        
        step_time = time.time() - step_start_time
        
        return {
            'loss': total_loss,
            'samples': total_samples,
            'step_time': step_time,
            'throughput': total_samples / step_time
        }


def create_test_model():
    """创建测试模型"""
    return nn.Sequential(
        nn.Linear(59, 4096),
        nn.ReLU(),
        nn.Dropout(0.2),
        nn.Linear(4096, 4096),
        nn.ReLU(),
        nn.Dropout(0.2),
        nn.Linear(4096, 2048),
        nn.ReLU(),
        nn.Dropout(0.1),
        nn.Linear(2048, 1024),
        nn.ReLU(),
        nn.Linear(1024, 512),
        nn.ReLU(),
        nn.Linear(512, 3)
    )


def main():
    """主测试函数"""
    print("🚀 开始超大批次大小优化测试...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，无法进行GPU优化测试")
        return
    
    optimizer = LargeBatchOptimizer(device)
    
    # 创建测试模型
    model = create_test_model().to(device)
    
    # 寻找最优批次配置
    optimal_config = optimizer.find_optimal_batch_config(model)
    
    if optimal_config:
        print(f"\n🎯 最优批次配置:")
        print(f"   - 物理批次大小: {optimal_config.physical_batch_size}")
        print(f"   - 逻辑批次大小: {optimal_config.logical_batch_size}")
        print(f"   - 梯度累积步数: {optimal_config.accumulation_steps}")
        print(f"   - 内存使用: {optimal_config.memory_usage_percent:.1f}%")
        
        # 创建梯度累积训练器
        trainer = optimizer.implement_gradient_accumulation_trainer(model, optimal_config)
        print(f"✅ 梯度累积训练器创建成功")
    
    print("\n✅ 超大批次大小优化测试完成！")


if __name__ == "__main__":
    main()
