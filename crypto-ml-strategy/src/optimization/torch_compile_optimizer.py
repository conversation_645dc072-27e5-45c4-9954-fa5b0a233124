"""
torch.compile激进优化器
测试和应用最激进的torch.compile配置以最大化GPU利用率
"""

import time
import torch
import torch.nn as nn
import logging
import subprocess
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
import numpy as np


@dataclass
class CompileConfig:
    """编译配置"""
    mode: str
    backend: Optional[str] = None
    fullgraph: bool = False
    dynamic: bool = True
    options: Optional[Dict[str, Any]] = None


class TorchCompileOptimizer:
    """torch.compile激进优化器"""
    
    def __init__(self, device: torch.device):
        """初始化优化器"""
        self.device = device
        self.logger = logging.getLogger(__name__)
        
        # 激进优化配置列表（按激进程度排序）
        self.aggressive_configs = [
            # 最激进配置
            CompileConfig(
                mode='max-autotune',
                backend='inductor',
                fullgraph=True,
                dynamic=False,
                options={'triton.cudagraphs': True, 'max_autotune': True}
            ),
            CompileConfig(
                mode='max-autotune',
                backend='inductor',
                fullgraph=False,
                dynamic=True,
                options={'triton.cudagraphs': True}
            ),
            # 中等激进配置
            CompileConfig(
                mode='max-autotune',
                backend='aot_eager',
                fullgraph=True,
                dynamic=False
            ),
            CompileConfig(
                mode='max-autotune',
                backend='inductor',
                fullgraph=False,
                dynamic=True
            ),
            # 保守但优化的配置
            CompileConfig(
                mode='reduce-overhead',
                backend='inductor',
                fullgraph=False,
                dynamic=True
            ),
            # 基线配置
            CompileConfig(
                mode='default',
                backend=None,
                fullgraph=False,
                dynamic=True
            )
        ]
    
    def get_available_backends(self) -> List[str]:
        """获取可用的编译后端"""
        try:
            if hasattr(torch, '_dynamo'):
                return torch._dynamo.list_backends()
            else:
                return ['inductor', 'aot_eager']
        except:
            return ['inductor', 'aot_eager']
    
    def test_compile_config(self, model: nn.Module, config: CompileConfig, 
                          test_input: torch.Tensor, num_iterations: int = 50) -> Dict[str, Any]:
        """测试特定编译配置的性能"""
        self.logger.info(f"🧪 测试编译配置: mode={config.mode}, backend={config.backend}, "
                        f"fullgraph={config.fullgraph}")
        
        try:
            # 编译模型
            compile_kwargs = {
                'mode': config.mode,
                'fullgraph': config.fullgraph,
                'dynamic': config.dynamic
            }
            
            if config.backend:
                compile_kwargs['backend'] = config.backend
            
            if config.options:
                compile_kwargs['options'] = config.options
            
            compiled_model = torch.compile(model, **compile_kwargs)
            
            # 预热
            for _ in range(10):
                with torch.no_grad():
                    _ = compiled_model(test_input)
            
            if torch.cuda.is_available():
                torch.cuda.synchronize()
            
            # 性能测试
            start_time = time.time()
            
            for _ in range(num_iterations):
                with torch.no_grad():
                    output = compiled_model(test_input)
            
            if torch.cuda.is_available():
                torch.cuda.synchronize()
            
            end_time = time.time()
            
            total_time = end_time - start_time
            avg_time = total_time / num_iterations
            throughput = test_input.size(0) / avg_time
            
            # 获取GPU利用率
            gpu_util = self._get_gpu_utilization()
            
            result = {
                'success': True,
                'avg_time': avg_time,
                'throughput': throughput,
                'total_time': total_time,
                'gpu_utilization': gpu_util,
                'config': config,
                'error': None
            }
            
            self.logger.info(f"✅ 配置测试成功: {throughput:.0f} samples/s, GPU: {gpu_util:.1f}%")
            return result
            
        except Exception as e:
            self.logger.warning(f"❌ 配置测试失败: {e}")
            return {
                'success': False,
                'avg_time': float('inf'),
                'throughput': 0,
                'total_time': float('inf'),
                'gpu_utilization': 0,
                'config': config,
                'error': str(e)
            }
    
    def _get_gpu_utilization(self) -> float:
        """获取当前GPU利用率"""
        try:
            result = subprocess.run([
                'nvidia-smi', 
                '--query-gpu=utilization.gpu',
                '--format=csv,noheader,nounits'
            ], capture_output=True, text=True, timeout=2)
            
            if result.returncode == 0:
                return float(result.stdout.strip())
        except:
            pass
        
        return 0.0
    
    def find_optimal_compile_config(self, model: nn.Module, 
                                  input_shape: Tuple[int, ...]) -> Tuple[CompileConfig, Dict[str, Any]]:
        """找到最优的编译配置"""
        self.logger.info("🔍 寻找最优torch.compile配置...")
        
        # 创建测试输入
        test_input = torch.randn(*input_shape, device=self.device)
        
        best_config = None
        best_result = None
        best_score = 0
        
        results = []
        
        for config in self.aggressive_configs:
            # 跳过不可用的后端
            if config.backend and config.backend not in self.get_available_backends():
                self.logger.info(f"⏭️ 跳过不可用后端: {config.backend}")
                continue
            
            result = self.test_compile_config(model, config, test_input)
            results.append(result)
            
            if result['success']:
                # 计算综合得分（吞吐量 * GPU利用率）
                score = result['throughput'] * (result['gpu_utilization'] / 100)
                
                if score > best_score:
                    best_score = score
                    best_config = config
                    best_result = result
            
            # 清理编译缓存
            if hasattr(torch, '_dynamo'):
                torch._dynamo.reset()
        
        if best_config:
            self.logger.info(f"🏆 最优配置: mode={best_config.mode}, backend={best_config.backend}")
            self.logger.info(f"📊 性能: {best_result['throughput']:.0f} samples/s, "
                           f"GPU: {best_result['gpu_utilization']:.1f}%")
        else:
            self.logger.warning("⚠️ 未找到可用的编译配置")
        
        return best_config, best_result, results
    
    def apply_optimal_config_to_trainer(self, trainer_class, optimal_config: CompileConfig):
        """将最优配置应用到训练器"""
        self.logger.info("🔧 应用最优torch.compile配置到训练器...")
        
        # 更新环境变量
        import os
        os.environ['TORCH_COMPILE_MODE'] = optimal_config.mode
        if optimal_config.backend:
            os.environ['TORCH_COMPILE_BACKEND'] = optimal_config.backend
        
        # 更新全局配置
        if hasattr(trainer_class, 'TORCH_COMPILE_MODE'):
            trainer_class.TORCH_COMPILE_MODE = optimal_config.mode
        if hasattr(trainer_class, 'TORCH_COMPILE_BACKEND'):
            trainer_class.TORCH_COMPILE_BACKEND = optimal_config.backend
        
        self.logger.info("✅ torch.compile配置已更新")
    
    def create_optimized_model(self, model: nn.Module, config: CompileConfig) -> nn.Module:
        """使用最优配置创建编译模型"""
        try:
            compile_kwargs = {
                'mode': config.mode,
                'fullgraph': config.fullgraph,
                'dynamic': config.dynamic
            }
            
            if config.backend:
                compile_kwargs['backend'] = config.backend
            
            if config.options:
                compile_kwargs['options'] = config.options
            
            compiled_model = torch.compile(model, **compile_kwargs)
            
            self.logger.info(f"✅ 模型编译成功: {compile_kwargs}")
            return compiled_model
            
        except Exception as e:
            self.logger.error(f"❌ 模型编译失败: {e}")
            return model
    
    def benchmark_training_step(self, model: nn.Module, config: CompileConfig,
                              batch_size: int = 8192, num_steps: int = 100) -> Dict[str, Any]:
        """基准测试完整的训练步骤"""
        self.logger.info(f"🧪 基准测试训练步骤: batch_size={batch_size}")
        
        # 编译模型
        compiled_model = self.create_optimized_model(model, config)
        
        # 创建测试数据
        features = torch.randn(batch_size, 59, device=self.device)
        labels = torch.randint(0, 3, (batch_size,), device=self.device)
        
        criterion = nn.CrossEntropyLoss()
        optimizer = torch.optim.AdamW(compiled_model.parameters(), lr=1e-3)
        
        # 预热
        for _ in range(10):
            optimizer.zero_grad(set_to_none=True)
            outputs = compiled_model(features)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        # 性能测试
        start_time = time.time()
        total_samples = 0
        
        for _ in range(num_steps):
            optimizer.zero_grad(set_to_none=True)
            outputs = compiled_model(features)
            loss = criterion(outputs, labels)
            loss.backward()
            optimizer.step()
            total_samples += batch_size
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        end_time = time.time()
        total_time = end_time - start_time
        throughput = total_samples / total_time
        
        # 获取GPU利用率
        gpu_util = self._get_gpu_utilization()
        
        result = {
            'throughput': throughput,
            'total_time': total_time,
            'avg_step_time': total_time / num_steps,
            'gpu_utilization': gpu_util,
            'total_samples': total_samples
        }
        
        self.logger.info(f"📊 训练步骤性能: {throughput:.0f} samples/s, GPU: {gpu_util:.1f}%")
        
        return result


def create_test_model(input_size: int = 59, complexity: str = 'high') -> nn.Module:
    """创建测试模型"""
    if complexity == 'high':
        model = nn.Sequential(
            nn.Linear(input_size, 4096),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(4096, 4096),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(4096, 2048),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(2048, 1024),
            nn.ReLU(),
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Linear(512, 3)
        )
    elif complexity == 'medium':
        model = nn.Sequential(
            nn.Linear(input_size, 2048),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(2048, 1024),
            nn.ReLU(),
            nn.Linear(1024, 512),
            nn.ReLU(),
            nn.Linear(512, 3)
        )
    else:  # basic
        model = nn.Sequential(
            nn.Linear(input_size, 512),
            nn.ReLU(),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Linear(256, 3)
        )
    
    return model


def main():
    """主测试函数"""
    print("🚀 开始torch.compile激进优化测试...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    if not torch.cuda.is_available():
        print("❌ CUDA不可用，无法进行GPU优化测试")
        return
    
    optimizer = TorchCompileOptimizer(device)
    
    # 创建测试模型
    model = create_test_model(complexity='high').to(device)
    
    # 寻找最优配置
    optimal_config, best_result, all_results = optimizer.find_optimal_compile_config(
        model, (16384, 59)  # 大批次测试
    )
    
    if optimal_config:
        # 基准测试训练步骤
        training_result = optimizer.benchmark_training_step(
            model, optimal_config, batch_size=16384
        )
        
        print(f"\n🎯 最优torch.compile配置:")
        print(f"   - 模式: {optimal_config.mode}")
        print(f"   - 后端: {optimal_config.backend}")
        print(f"   - 全图: {optimal_config.fullgraph}")
        print(f"   - 动态: {optimal_config.dynamic}")
        print(f"   - 训练吞吐量: {training_result['throughput']:.0f} samples/s")
        print(f"   - GPU利用率: {training_result['gpu_utilization']:.1f}%")
    
    print("\n✅ torch.compile激进优化测试完成！")


if __name__ == "__main__":
    main()
