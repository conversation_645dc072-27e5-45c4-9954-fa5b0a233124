"""
策略性能评估器
负责评估策略的历史表现和实时性能
"""

import logging
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta
import asyncio

from src.strategy.engines.strategy_engine import StrategyDecision


@dataclass
class PerformanceMetrics:
    """性能指标"""
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    calmar_ratio: float
    sortino_ratio: float
    var_95: float  # 95% VaR
    cvar_95: float  # 95% CVaR


@dataclass
class TradeAnalysis:
    """交易分析"""
    total_trades: int
    winning_trades: int
    losing_trades: int
    avg_win: float
    avg_loss: float
    largest_win: float
    largest_loss: float
    avg_trade_duration: float
    avg_return_per_trade: float


class StrategyEvaluator:
    """策略性能评估器"""
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化策略评估器
        
        Args:
            config: 配置参数
        """
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # 评估参数
        self.risk_free_rate = self.config.get('risk_free_rate', 0.02)  # 2%
        self.benchmark_return = self.config.get('benchmark_return', 0.08)  # 8%
        self.evaluation_window = self.config.get('evaluation_window', 252)  # 1年
        
        # 历史数据
        self.portfolio_values = []
        self.returns_history = []
        self.decisions_history = []
        
    async def evaluate_strategy(self, 
                              portfolio_history: List[Dict[str, Any]],
                              decisions_history: List[StrategyDecision],
                              benchmark_data: Optional[pd.DataFrame] = None) -> Dict[str, Any]:
        """
        评估策略性能
        
        Args:
            portfolio_history: 投资组合历史数据
            decisions_history: 决策历史数据
            benchmark_data: 基准数据
            
        Returns:
            评估结果
        """
        try:
            # 计算性能指标
            performance_metrics = await self._calculate_performance_metrics(portfolio_history)
            
            # 分析交易
            trade_analysis = await self._analyze_trades(decisions_history)
            
            # 风险分析
            risk_analysis = await self._analyze_risks(portfolio_history)
            
            # 基准比较
            benchmark_comparison = await self._compare_with_benchmark(
                portfolio_history, benchmark_data
            )
            
            # 时间序列分析
            time_series_analysis = await self._analyze_time_series(portfolio_history)
            
            evaluation_result = {
                'performance_metrics': performance_metrics,
                'trade_analysis': trade_analysis,
                'risk_analysis': risk_analysis,
                'benchmark_comparison': benchmark_comparison,
                'time_series_analysis': time_series_analysis,
                'evaluation_timestamp': datetime.now(),
                'evaluation_period': {
                    'start': portfolio_history[0]['timestamp'] if portfolio_history else None,
                    'end': portfolio_history[-1]['timestamp'] if portfolio_history else None,
                    'duration_days': len(portfolio_history)
                }
            }
            
            self.logger.info("✅ 策略评估完成")
            return evaluation_result
            
        except Exception as e:
            self.logger.error(f"策略评估失败: {e}")
            raise
            
    async def _calculate_performance_metrics(self, 
                                           portfolio_history: List[Dict[str, Any]]) -> PerformanceMetrics:
        """计算性能指标"""
        if not portfolio_history:
            return PerformanceMetrics(0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)
            
        # 提取投资组合价值
        values = [p['total_value'] for p in portfolio_history]
        returns = np.diff(values) / values[:-1]
        
        # 基本指标
        total_return = (values[-1] - values[0]) / values[0]
        annualized_return = (1 + total_return) ** (252 / len(values)) - 1
        volatility = np.std(returns) * np.sqrt(252)
        
        # 夏普比率
        excess_returns = returns - self.risk_free_rate / 252
        sharpe_ratio = np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(252) if np.std(excess_returns) > 0 else 0
        
        # 最大回撤
        cumulative_returns = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = (cumulative_returns - running_max) / running_max
        max_drawdown = np.min(drawdowns)
        
        # 胜率
        win_rate = np.sum(returns > 0) / len(returns) if len(returns) > 0 else 0
        
        # 盈亏比
        winning_returns = returns[returns > 0]
        losing_returns = returns[returns < 0]
        avg_win = np.mean(winning_returns) if len(winning_returns) > 0 else 0
        avg_loss = np.mean(losing_returns) if len(losing_returns) > 0 else 0
        profit_factor = abs(avg_win / avg_loss) if avg_loss != 0 else 0
        
        # 卡尔马比率
        calmar_ratio = annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
        
        # 索提诺比率
        downside_returns = returns[returns < 0]
        downside_volatility = np.std(downside_returns) * np.sqrt(252) if len(downside_returns) > 0 else 0
        sortino_ratio = (annualized_return - self.risk_free_rate) / downside_volatility if downside_volatility > 0 else 0
        
        # VaR和CVaR
        var_95 = np.percentile(returns, 5)
        cvar_95 = np.mean(returns[returns <= var_95]) if np.any(returns <= var_95) else 0
        
        return PerformanceMetrics(
            total_return=total_return,
            annualized_return=annualized_return,
            volatility=volatility,
            sharpe_ratio=sharpe_ratio,
            max_drawdown=max_drawdown,
            win_rate=win_rate,
            profit_factor=profit_factor,
            calmar_ratio=calmar_ratio,
            sortino_ratio=sortino_ratio,
            var_95=var_95,
            cvar_95=cvar_95
        )
        
    async def _analyze_trades(self, decisions_history: List[StrategyDecision]) -> TradeAnalysis:
        """分析交易"""
        if not decisions_history:
            return TradeAnalysis(0, 0, 0, 0, 0, 0, 0, 0, 0)
            
        # 统计交易
        buy_decisions = [d for d in decisions_history if d.action == 'BUY']
        sell_decisions = [d for d in decisions_history if d.action == 'SELL']
        
        total_trades = len(buy_decisions) + len(sell_decisions)
        
        # 计算收益（简化版本，实际需要配对买卖）
        returns = []
        for decision in decisions_history:
            # 这里需要更复杂的逻辑来计算实际收益
            # 暂时使用置信度作为代理
            estimated_return = (decision.confidence - 0.5) * 0.1  # 简化计算
            returns.append(estimated_return)
            
        winning_trades = sum(1 for r in returns if r > 0)
        losing_trades = sum(1 for r in returns if r < 0)
        
        avg_win = np.mean([r for r in returns if r > 0]) if winning_trades > 0 else 0
        avg_loss = np.mean([r for r in returns if r < 0]) if losing_trades > 0 else 0
        largest_win = max(returns) if returns else 0
        largest_loss = min(returns) if returns else 0
        
        # 平均交易持续时间（简化）
        avg_trade_duration = 1.0  # 假设1天
        
        avg_return_per_trade = np.mean(returns) if returns else 0
        
        return TradeAnalysis(
            total_trades=total_trades,
            winning_trades=winning_trades,
            losing_trades=losing_trades,
            avg_win=avg_win,
            avg_loss=avg_loss,
            largest_win=largest_win,
            largest_loss=largest_loss,
            avg_trade_duration=avg_trade_duration,
            avg_return_per_trade=avg_return_per_trade
        )
        
    async def _analyze_risks(self, portfolio_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析风险"""
        if not portfolio_history:
            return {}
            
        values = [p['total_value'] for p in portfolio_history]
        returns = np.diff(values) / values[:-1]
        
        # 风险指标
        risk_metrics = {
            'volatility': np.std(returns) * np.sqrt(252),
            'skewness': float(pd.Series(returns).skew()),
            'kurtosis': float(pd.Series(returns).kurtosis()),
            'var_99': np.percentile(returns, 1),
            'var_95': np.percentile(returns, 5),
            'var_90': np.percentile(returns, 10),
            'beta': 1.0,  # 需要基准数据计算
            'tracking_error': 0.0,  # 需要基准数据计算
            'information_ratio': 0.0  # 需要基准数据计算
        }
        
        return risk_metrics
        
    async def _compare_with_benchmark(self, 
                                    portfolio_history: List[Dict[str, Any]],
                                    benchmark_data: Optional[pd.DataFrame]) -> Dict[str, Any]:
        """与基准比较"""
        if not portfolio_history:
            return {}
            
        portfolio_values = [p['total_value'] for p in portfolio_history]
        portfolio_returns = np.diff(portfolio_values) / portfolio_values[:-1]
        
        comparison = {
            'portfolio_total_return': (portfolio_values[-1] - portfolio_values[0]) / portfolio_values[0],
            'portfolio_annualized_return': (1 + (portfolio_values[-1] - portfolio_values[0]) / portfolio_values[0]) ** (252 / len(portfolio_values)) - 1,
            'benchmark_total_return': self.benchmark_return,
            'benchmark_annualized_return': self.benchmark_return,
            'alpha': 0.0,  # 需要基准数据计算
            'beta': 1.0,   # 需要基准数据计算
            'correlation': 0.0,  # 需要基准数据计算
            'tracking_error': 0.0,  # 需要基准数据计算
            'information_ratio': 0.0  # 需要基准数据计算
        }
        
        return comparison
        
    async def _analyze_time_series(self, portfolio_history: List[Dict[str, Any]]) -> Dict[str, Any]:
        """时间序列分析"""
        if not portfolio_history:
            return {}
            
        values = [p['total_value'] for p in portfolio_history]
        timestamps = [p['timestamp'] for p in portfolio_history]
        
        # 时间序列特征
        analysis = {
            'trend': 'upward' if values[-1] > values[0] else 'downward',
            'volatility_regime': 'high' if np.std(values) > np.mean(values) * 0.1 else 'low',
            'drawdown_periods': [],  # 需要更复杂的计算
            'recovery_periods': [],  # 需要更复杂的计算
            'performance_by_period': {
                'daily': {},
                'weekly': {},
                'monthly': {}
            }
        }
        
        return analysis
        
    def generate_report(self, evaluation_result: Dict[str, Any]) -> str:
        """生成评估报告"""
        report = f"""
策略性能评估报告
================

评估时间: {evaluation_result['evaluation_timestamp']}
评估期间: {evaluation_result['evaluation_period']['duration_days']} 天

性能指标:
--------
总收益率: {evaluation_result['performance_metrics'].total_return:.2%}
年化收益率: {evaluation_result['performance_metrics'].annualized_return:.2%}
波动率: {evaluation_result['performance_metrics'].volatility:.2%}
夏普比率: {evaluation_result['performance_metrics'].sharpe_ratio:.2f}
最大回撤: {evaluation_result['performance_metrics'].max_drawdown:.2%}
胜率: {evaluation_result['performance_metrics'].win_rate:.2%}

交易分析:
--------
总交易次数: {evaluation_result['trade_analysis'].total_trades}
盈利交易: {evaluation_result['trade_analysis'].winning_trades}
亏损交易: {evaluation_result['trade_analysis'].losing_trades}
平均盈利: {evaluation_result['trade_analysis'].avg_win:.2%}
平均亏损: {evaluation_result['trade_analysis'].avg_loss:.2%}

风险分析:
--------
VaR (95%): {evaluation_result['performance_metrics'].var_95:.2%}
CVaR (95%): {evaluation_result['performance_metrics'].cvar_95:.2%}
索提诺比率: {evaluation_result['performance_metrics'].sortino_ratio:.2f}
"""
        return report
