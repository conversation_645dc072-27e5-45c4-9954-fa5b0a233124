#!/usr/bin/env python3
"""
交易信号发送模块
实现实时交易信号生成和发送功能
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path
import pandas as pd
import numpy as np
import torch

from src.models.unified_fusion_model import UnifiedSignalFusionModel
from src.data.enhanced_feature_engineering import EnhancedFeatureEngineer
from src.kafka.kafka_client import KafkaClient
from src.storage.redis_cache import RedisCache
from src.utils.logger import setup_logger


@dataclass
class TradingSignal:
    """交易信号数据结构"""
    symbol: str
    signal: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float
    price: float
    timestamp: datetime
    features: Dict[str, float]
    model_version: str
    signal_id: str


@dataclass
class SignalConfig:
    """信号配置"""
    confidence_threshold: float = 0.7
    signal_interval: int = 60  # 秒
    max_signals_per_hour: int = 10
    symbols: List[str] = None
    enable_kafka: bool = True
    enable_redis: bool = True
    enable_file_output: bool = True


class TradingSignalSender:
    """交易信号发送器"""
    
    def __init__(self, model_path: str, config: SignalConfig = None):
        self.logger = setup_logger(__name__)
        self.model_path = model_path
        self.config = config or SignalConfig()
        
        # 初始化组件
        self.model = None
        self.feature_engineer = None
        self.kafka_client = None
        self.redis_cache = None
        
        # 信号历史
        self.signal_history = []
        self.last_signal_time = {}
        
        # 运行状态
        self.is_running = False
        self.signal_count = {}
        
        # 输出目录
        self.output_dir = Path("signals")
        self.output_dir.mkdir(exist_ok=True)
        
        self.logger.info(f"🎯 交易信号发送器初始化完成")
    
    async def initialize(self):
        """初始化所有组件"""
        self.logger.info("🔧 初始化交易信号发送器...")
        
        try:
            # 加载模型
            await self._load_model()
            
            # 初始化特征工程
            self.feature_engineer = EnhancedFeatureEngineer({})
            
            # 初始化Kafka客户端
            if self.config.enable_kafka:
                try:
                    from src.utils.config import get_config_manager
                    kafka_config = get_config_manager().get('kafka', {})
                    self.kafka_client = KafkaClient(kafka_config)
                    await self.kafka_client.initialize()
                    self.logger.info("✅ Kafka客户端初始化成功")
                except Exception as e:
                    self.logger.warning(f"⚠️ Kafka客户端初始化失败: {e}")
                    self.config.enable_kafka = False
            
            # 初始化Redis缓存
            if self.config.enable_redis:
                try:
                    self.redis_cache = RedisCache()
                    self.logger.info("✅ Redis缓存初始化成功")
                except Exception as e:
                    self.logger.warning(f"⚠️ Redis缓存初始化失败: {e}")
                    self.config.enable_redis = False
            
            self.logger.info("✅ 交易信号发送器初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 初始化失败: {e}")
            raise
    
    async def _load_model(self):
        """加载训练好的模型"""
        self.logger.info(f"📥 加载模型: {self.model_path}")
        
        try:
            import joblib

            # 尝试不同的加载方式
            try:
                # 首先尝试torch.load
                checkpoint = torch.load(self.model_path, map_location='cpu')
                is_torch_format = True
            except:
                # 如果失败，尝试joblib.load
                checkpoint = joblib.load(self.model_path)
                is_torch_format = False
            
            if is_torch_format:
                # PyTorch格式
                # 获取模型配置
                model_config = checkpoint.get('model_config', {
                    'feature_dim': 93,
                    'hidden_dims': [256, 128, 64],
                    'num_heads': 4,
                    'num_layers': 1,
                    'dropout': 0.1,
                    'output_dim': 3,
                    'sequence_length': 1,
                    'use_attention': True,
                    'use_lstm': True,
                    'use_transformer': True,
                    'device': 'cuda:0' if torch.cuda.is_available() else 'cpu'
                })

                # 创建模型
                self.model = UnifiedSignalFusionModel(**model_config)

                # 加载权重 - 修复集成模型兼容性
                if 'model_state_dict' in checkpoint:
                    state_dict = checkpoint['model_state_dict']

                    # 检查是否是集成模型格式
                    if any(key.startswith('base_models.') for key in state_dict.keys()):
                        self.logger.info("🔄 检测到集成模型，提取第一个基础模型...")
                        # 提取第一个基础模型的权重
                        single_model_state_dict = {}
                        for key, value in state_dict.items():
                            if key.startswith('base_models.0.'):
                                # 移除 'base_models.0.' 前缀
                                new_key = key[len('base_models.0.'):]
                                single_model_state_dict[new_key] = value

                        if single_model_state_dict:
                            self.model.load_state_dict(single_model_state_dict)
                            self.logger.info("✅ 成功加载集成模型中的第一个基础模型")
                        else:
                            self.logger.warning("⚠️ 未找到有效的基础模型权重，使用随机初始化")
                    else:
                        # 标准单一模型格式
                        self.model.load_state_dict(state_dict)
                        self.logger.info("✅ 成功加载单一模型")
                else:
                    # 直接是state_dict
                    state_dict = checkpoint
                    if any(key.startswith('base_models.') for key in state_dict.keys()):
                        # 集成模型格式
                        single_model_state_dict = {}
                        for key, value in state_dict.items():
                            if key.startswith('base_models.0.'):
                                new_key = key[len('base_models.0.'):]
                                single_model_state_dict[new_key] = value

                        if single_model_state_dict:
                            self.model.load_state_dict(single_model_state_dict)
                            self.logger.info("✅ 成功加载集成模型中的第一个基础模型")
                        else:
                            self.logger.warning("⚠️ 未找到有效的基础模型权重，使用随机初始化")
                    else:
                        self.model.load_state_dict(state_dict)
            else:
                # Joblib格式 - 直接是模型对象
                if hasattr(checkpoint, 'state_dict'):
                    self.model = checkpoint
                else:
                    # 如果不是模型对象，创建默认模型
                    model_config = {
                        'feature_dim': 93,
                        'hidden_dims': [256, 128, 64],
                        'num_heads': 4,
                        'num_layers': 1,
                        'dropout': 0.1,
                        'output_dim': 3,
                        'sequence_length': 1,
                        'use_attention': True,
                        'use_lstm': True,
                        'use_transformer': True,
                        'device': 'cuda:0' if torch.cuda.is_available() else 'cpu'
                    }
                    self.model = UnifiedSignalFusionModel(**model_config)
            
            # 移动到设备
            device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
            self.model = self.model.to(device)
            self.model.eval()
            
            self.logger.info(f"✅ 模型加载成功，设备: {device}")
            
        except Exception as e:
            self.logger.error(f"❌ 模型加载失败: {e}")
            raise
    
    async def generate_signal(self, symbol: str) -> Optional[TradingSignal]:
        """生成交易信号"""
        try:
            # 检查信号频率限制
            if not self._can_send_signal(symbol):
                return None
            
            # 获取最新数据
            latest_data = await self._get_latest_data(symbol)
            if latest_data is None:
                return None
            
            # 特征工程
            features = await self._extract_features(latest_data, symbol)
            if features is None:
                return None
            
            # 模型预测
            prediction = await self._predict(features)
            if prediction is None:
                return None
            
            # 生成信号
            signal = self._create_signal(symbol, prediction, features, latest_data)
            
            # 检查信号质量
            if signal and signal.confidence >= self.config.confidence_threshold:
                return signal
            
            return None
            
        except Exception as e:
            self.logger.error(f"❌ 生成信号失败 {symbol}: {e}")
            return None
    
    async def _get_latest_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """获取最新市场数据"""
        try:
            # 这里应该从实时数据源获取最新数据
            # 暂时使用模拟数据
            current_time = datetime.now()
            
            data = {
                'timestamp': [current_time],
                'symbol': [symbol],
                'close': [119000.0],  # 模拟价格
                'volume': [1.0],
                'label': [1]  # 占位符
            }
            
            return pd.DataFrame(data)
            
        except Exception as e:
            self.logger.error(f"❌ 获取最新数据失败 {symbol}: {e}")
            return None
    
    async def _extract_features(self, data: pd.DataFrame, symbol: str) -> Optional[np.ndarray]:
        """提取特征"""
        try:
            # 使用增强特征工程
            enhanced_data = await self.feature_engineer.enhance_dataset_with_depth_and_trades(data, symbol)

            # 提取特征列
            feature_cols = [col for col in enhanced_data.columns if col not in ['timestamp', 'symbol', 'label']]
            features = enhanced_data[feature_cols].values

            # 🔥 修复：确保特征维度匹配模型期望
            # 检查模型的实际输入维度
            model_input_dim = None
            for param in self.model.parameters():
                if len(param.shape) == 2:  # 找到第一个线性层
                    model_input_dim = param.shape[1]
                    break

            if model_input_dim is None:
                model_input_dim = 59  # 从错误信息中得知模型期望59维

            current_features = features.shape[1]

            if current_features < model_input_dim:
                # 如果特征不够，用零填充
                padding = np.zeros((features.shape[0], model_input_dim - current_features))
                features = np.concatenate([features, padding], axis=1)
                self.logger.debug(f"特征维度填充: {current_features} → {model_input_dim}")
            elif current_features > model_input_dim:
                # 如果特征太多，截取前面的
                features = features[:, :model_input_dim]
                self.logger.debug(f"特征维度截取: {current_features} → {model_input_dim}")
            
            # 标准化（这里应该使用训练时的scaler）
            from sklearn.preprocessing import StandardScaler
            scaler = StandardScaler()
            features_scaled = scaler.fit_transform(features)
            
            return features_scaled
            
        except Exception as e:
            self.logger.error(f"❌ 特征提取失败 {symbol}: {e}")
            return None
    
    async def _predict(self, features: np.ndarray) -> Optional[Dict[str, float]]:
        """模型预测"""
        try:
            # 转换为张量
            device = next(self.model.parameters()).device
            features_tensor = torch.FloatTensor(features).to(device)
            
            # 添加序列维度
            if features_tensor.dim() == 2:
                features_tensor = features_tensor.unsqueeze(1)  # [batch, 1, features]
            
            # 预测
            with torch.no_grad():
                output = self.model(features_tensor)
                logits = output['signal']
                probabilities = torch.softmax(logits, dim=-1)
                
                # 转换为numpy
                probs = probabilities.cpu().numpy()[0]  # 取第一个样本
                
                return {
                    'buy_prob': float(probs[2]),    # 假设2是买入
                    'sell_prob': float(probs[0]),   # 假设0是卖出
                    'hold_prob': float(probs[1])    # 假设1是持有
                }
                
        except Exception as e:
            self.logger.error(f"❌ 模型预测失败: {e}")
            return None
    
    def _create_signal(self, symbol: str, prediction: Dict[str, float], 
                      features: np.ndarray, data: pd.DataFrame) -> Optional[TradingSignal]:
        """创建交易信号"""
        try:
            # 确定信号类型
            max_prob = max(prediction.values())
            
            if prediction['buy_prob'] == max_prob:
                signal_type = 'BUY'
                confidence = prediction['buy_prob']
            elif prediction['sell_prob'] == max_prob:
                signal_type = 'SELL'
                confidence = prediction['sell_prob']
            else:
                signal_type = 'HOLD'
                confidence = prediction['hold_prob']
            
            # 创建信号
            signal = TradingSignal(
                symbol=symbol,
                signal=signal_type,
                confidence=confidence,
                price=float(data['close'].iloc[0]),
                timestamp=datetime.now(),
                features={f'feature_{i}': float(val) for i, val in enumerate(features[0])},
                model_version="v1.0",
                signal_id=f"{symbol}_{int(time.time())}"
            )
            
            return signal
            
        except Exception as e:
            self.logger.error(f"❌ 创建信号失败: {e}")
            return None
    
    def _can_send_signal(self, symbol: str) -> bool:
        """检查是否可以发送信号"""
        current_time = time.time()
        
        # 检查时间间隔
        if symbol in self.last_signal_time:
            if current_time - self.last_signal_time[symbol] < self.config.signal_interval:
                return False
        
        # 检查小时频率限制
        hour_key = f"{symbol}_{int(current_time // 3600)}"
        if hour_key not in self.signal_count:
            self.signal_count[hour_key] = 0
        
        if self.signal_count[hour_key] >= self.config.max_signals_per_hour:
            return False
        
        return True
    
    async def send_signal(self, signal: TradingSignal):
        """发送交易信号"""
        try:
            self.logger.info(f"📤 发送交易信号: {signal.symbol} {signal.signal} (置信度: {signal.confidence:.2f})")
            
            # 更新计数器
            current_time = time.time()
            self.last_signal_time[signal.symbol] = current_time
            hour_key = f"{signal.symbol}_{int(current_time // 3600)}"
            self.signal_count[hour_key] = self.signal_count.get(hour_key, 0) + 1
            
            # 添加到历史
            self.signal_history.append(signal)
            
            # 发送到Kafka
            if self.config.enable_kafka and self.kafka_client:
                await self._send_to_kafka(signal)
            
            # 保存到Redis
            if self.config.enable_redis and self.redis_cache:
                await self._save_to_redis(signal)
            
            # 保存到文件
            if self.config.enable_file_output:
                await self._save_to_file(signal)
            
        except Exception as e:
            self.logger.error(f"❌ 发送信号失败: {e}")
    
    async def _send_to_kafka(self, signal: TradingSignal):
        """发送信号到Kafka"""
        try:
            # 准备信号数据
            signal_data = asdict(signal)
            signal_data['timestamp'] = signal.timestamp.isoformat()

            # 使用KafkaClient的send_signal方法
            success = await self.kafka_client.send_signal(signal_data, signal.symbol)
            if success:
                self.logger.debug(f"✅ 信号已发送到Kafka: {signal.symbol}")
            else:
                self.logger.warning(f"⚠️ Kafka信号发送失败: {signal.symbol}")

        except Exception as e:
            self.logger.error(f"❌ Kafka发送失败: {e}")
    
    async def _save_to_redis(self, signal: TradingSignal):
        """保存信号到Redis"""
        try:
            key = f"signal:{signal.symbol}:latest"
            value = asdict(signal)
            value['timestamp'] = signal.timestamp.isoformat()

            # 使用RedisCache的正确参数：ttl而不是expire
            success = await self.redis_cache.set_async(key, json.dumps(value), ttl=3600)
            if success:
                self.logger.debug(f"✅ 信号已保存到Redis: {key}")
            else:
                self.logger.warning(f"⚠️ Redis信号保存失败: {key}")

        except Exception as e:
            self.logger.error(f"❌ Redis保存失败: {e}")
    
    async def _save_to_file(self, signal: TradingSignal):
        """保存信号到文件"""
        try:
            date_str = signal.timestamp.strftime("%Y%m%d")
            file_path = self.output_dir / f"signals_{date_str}.jsonl"
            
            signal_data = asdict(signal)
            signal_data['timestamp'] = signal.timestamp.isoformat()
            
            with open(file_path, 'a', encoding='utf-8') as f:
                f.write(json.dumps(signal_data) + '\n')
            
            self.logger.debug(f"✅ 信号已保存到文件: {file_path}")
            
        except Exception as e:
            self.logger.error(f"❌ 文件保存失败: {e}")
    
    async def start_signal_loop(self):
        """开始信号生成循环"""
        self.logger.info("🚀 开始交易信号生成循环...")
        self.is_running = True
        
        symbols = self.config.symbols or ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        
        while self.is_running:
            try:
                for symbol in symbols:
                    if not self.is_running:
                        break
                    
                    signal = await self.generate_signal(symbol)
                    if signal:
                        await self.send_signal(signal)
                
                # 等待下一次循环
                await asyncio.sleep(self.config.signal_interval)
                
            except Exception as e:
                self.logger.error(f"❌ 信号循环错误: {e}")
                await asyncio.sleep(10)  # 错误后等待10秒
    
    def stop(self):
        """停止信号发送"""
        self.logger.info("⏹️ 停止交易信号发送...")
        self.is_running = False
