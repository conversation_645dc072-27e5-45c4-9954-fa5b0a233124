"""
策略级风险管理器适配器
该模块提供了一个适配器，将策略层对风险管理的需求连接到系统级的 UnifiedRiskFramework。
"""

from typing import Dict, Any, Optional

# 从系统级风险模块导入核心组件
from src.risk import (
    UnifiedRiskFramework,
    UnifiedRiskConfig,
    TradeDecision,
    RiskLevel
)
from .position_risk_assessment import PositionRiskAssessment


class StrategyRiskAdapter:
    """
    策略风险适配器。
    将旧的 StrategyRiskManager 接口适配到新的 UnifiedRiskFramework。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化适配器。

        Args:
            config: 包含 risk_management 配置的字典。
        """
        risk_config_dict = config.get('risk_management', {}) if config else {}
        
        # 1. 创建 UnifiedRiskConfig 数据类
        unified_config = UnifiedRiskConfig(
            portfolio_thresholds=risk_config_dict.get('portfolio_thresholds', {}),
            trade_rules=risk_config_dict.get('trade_rules', {}),
            monitoring_interval=risk_config_dict.get('monitoring_interval', 10.0)
        )
        
        # 2. 实例化内部的 UnifiedRiskFramework
        # 注意：这里的 alert_callback 可以根据需要进行配置
        self._risk_framework = UnifiedRiskFramework(unified_config)
        self._risk_framework.start_monitoring()

    async def assess_risk(self, position: Dict[str, Any], portfolio: Optional[Dict[str, Any]] = None) -> PositionRiskAssessment:
        """
        评估单个交易或持仓的风险。
        这是适配器方法，它将调用委托给 UnifiedRiskFramework。
        """
        # 1. 将 'position' 字典转换为 'signal' 字典
        signal = self._convert_position_to_signal(position)
        
        # 2. 更新框架的内部状态（如果提供了投资组合信息）
        if portfolio:
            self._risk_framework.update_portfolio_state(portfolio)
            
        # 3. 调用核心风险框架的交易信号处理方法
        trade_decision = await self._risk_framework.process_trade_signal(signal)
        
        # 4. 将返回的 TradeDecision 转换回策略层期望的 PositionRiskAssessment
        return self._convert_decision_to_assessment(trade_decision, position)

    def _convert_position_to_signal(self, position: Dict[str, Any]) -> Dict[str, Any]:
        """将持仓/交易意图字典转换为信号字典"""
        return {
            'symbol': position.get('symbol', 'UNKNOWN'),
            'signal_data': {
                'action': position.get('side', 'BUY').upper(),
                'confidence': position.get('confidence', 0.75),  # 提供一个默认置信度
            },
            'timestamp': datetime.now().isoformat()
        }

    def _convert_decision_to_assessment(self, decision: TradeDecision, original_position: Dict[str, Any]) -> PositionRiskAssessment:
        """将 TradeDecision 对象转换为 PositionRiskAssessment 对象"""
        if decision.is_approved:
            risk_level = RiskLevel.LOW # Simplified mapping
        else:
            risk_level = RiskLevel.HIGH # Simplified mapping

        return PositionRiskAssessment(
            risk_level=risk_level,
            risk_score=1.0 - decision.position_size / (original_position.get('account_balance', 100000) * 0.2) if decision.is_approved else 0.8,
            position_size_limit=decision.position_size,
            stop_loss_price=decision.stop_loss_price,
            take_profit_price=decision.take_profit_price,
            max_leverage=3.0,  # Placeholder, should come from config or decision
            recommendations=[decision.reason],
            metadata={
                'manager': 'StrategyRiskAdapter -> UnifiedRiskFramework',
                'timestamp': time.time(),
                'alerts': [alert.__dict__ for alert in decision.alerts]
            }
        )
