"""
投资组合管理器模块
负责投资组合管理和资产配置
"""

import logging
import numpy as np
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum

# 确保logger可用
logger = logging.getLogger(__name__)


@dataclass
class Position:
    """持仓信息"""
    symbol: str
    amount: float
    avg_price: float
    current_price: float
    side: str = 'long'  # long or short
    leverage: float = 1.0
    timestamp: float = field(default_factory=time.time)

    @property
    def market_value(self) -> float:
        """市场价值"""
        return abs(self.amount) * self.current_price

    @property
    def unrealized_pnl(self) -> float:
        """未实现盈亏"""
        if self.side == 'long':
            return self.amount * (self.current_price - self.avg_price)
        else:
            return self.amount * (self.avg_price - self.current_price)

    @property
    def unrealized_pnl_pct(self) -> float:
        """未实现盈亏百分比"""
        if self.avg_price == 0:
            return 0.0
        return self.unrealized_pnl / (abs(self.amount) * self.avg_price)


@dataclass
class PortfolioSummary:
    """投资组合摘要"""
    total_value: float
    cash_balance: float
    total_invested: float
    unrealized_pnl: float
    unrealized_pnl_pct: float
    positions_count: int
    allocation: Dict[str, float]
    risk_metrics: Dict[str, float]
    timestamp: float = field(default_factory=time.time)


class PortfolioManager:
    """投资组合管理器基类"""

    def __init__(self, initial_cash: float = 10000.0, config: Optional[Dict] = None):
        self.config = config or {}
        self.cash_balance = initial_cash
        self.initial_cash = initial_cash
        self.positions: Dict[str, Position] = {}
        self.transaction_history: List[Dict] = []
        self.logger = logging.getLogger(self.__class__.__name__)  # 添加logger

        # 配置参数
        self.max_positions = self.config.get('max_positions', 10)
        self.min_cash_reserve = self.config.get('min_cash_reserve', 0.1)  # 10%现金储备
        self.rebalance_threshold = self.config.get('rebalance_threshold', 0.05)  # 5%偏差触发再平衡

    def add_position(self, symbol: str, amount: float, price: float, side: str = 'long', leverage: float = 1.0) -> bool:
        """添加或更新持仓 - 优化版本"""
        try:
            # 输入验证
            if not symbol or not isinstance(symbol, str):
                return False
            
            # 安全的数值转换
            try:
                amount = float(amount) if amount is not None else 0.0
                price = float(price) if price is not None else 0.0
                leverage = float(leverage) if leverage is not None else 1.0
            except (ValueError, TypeError):
                return False
            
            # 处理边界情况
            if price <= 0 or amount == 0:
                return False
            
            position_value = abs(amount) * price
            
            # 检查现金余额 - 只有买入时才需要现金
            if side in ['long', 'buy'] and amount > 0:
                if position_value > self.cash_balance:
                    return False
            
            # 检查最大持仓数量
            if symbol not in self.positions and len(self.positions) >= self.max_positions:
                return False
            
            if symbol in self.positions:
                # 更新现有持仓
                existing = self.positions[symbol]
                
                # 计算新的总数量和总成本
                old_total_cost = existing.amount * existing.avg_price
                new_cost = amount * price
                new_total_amount = existing.amount + amount
                
                if new_total_amount != 0:
                    # 更新平均价格
                    new_avg_price = (old_total_cost + new_cost) / new_total_amount
                    existing.amount = new_total_amount
                    existing.avg_price = new_avg_price
                    existing.current_price = price
                    existing.timestamp = time.time()
                else:
                    # 平仓 - 移除持仓
                    del self.positions[symbol]
            else:
                # 新建持仓
                self.positions[symbol] = Position(
                    symbol=symbol,
                    amount=amount,
                    avg_price=price,
                    current_price=price,
                    side=side,
                    leverage=leverage
                )
            
            # 更新现金余额 - 修复计算逻辑
            if side in ['long', 'buy']:
                if amount > 0:  # 买入
                    self.cash_balance -= position_value
                else:  # 卖出（负数量）
                    self.cash_balance += position_value
            elif side in ['short', 'sell']:
                if amount > 0:  # 做空开仓
                    self.cash_balance += position_value  # 做空获得现金
                else:  # 做空平仓
                    self.cash_balance -= position_value
            
            # 记录交易
            self.transaction_history.append({
                'symbol': symbol,
                'amount': amount,
                'price': price,
                'side': side,
                'leverage': leverage,
                'timestamp': time.time(),
                'type': 'position_change',
                'cash_balance_after': self.cash_balance
            })
            
            return True

        except Exception as e:
            self.logger.error(f"添加持仓失败: {e}")
            return False

    def get_position(self, symbol: str) -> Optional[Position]:
        """获取指定符号的持仓"""
        return self.positions.get(symbol)

    def close_position(self, symbol: str, price: float) -> bool:
        """平仓指定持仓"""
        try:
            if symbol not in self.positions:
                return False

            position = self.positions[symbol]

            # 计算平仓价值
            position_value = abs(position.amount) * price

            # 更新现金余额
            if position.side in ['long', 'buy']:
                self.cash_balance += position_value
            elif position.side in ['short', 'sell']:
                self.cash_balance -= position_value

            # 记录交易
            self.transaction_history.append({
                'symbol': symbol,
                'amount': -position.amount,  # 负数表示平仓
                'price': price,
                'side': 'close',
                'timestamp': time.time(),
                'type': 'close_position'
            })

            # 移除持仓
            del self.positions[symbol]

            return True

        except Exception as e:
            self.logger.error(f"平仓失败: {e}")
            return False

    def get_portfolio_value(self, current_prices: Dict[str, float]) -> float:
        """计算投资组合总价值"""
        try:
            total_value = self.cash_balance

            for symbol, position in self.positions.items():
                current_price = current_prices.get(symbol, position.current_price)
                position_value = abs(position.amount) * current_price

                if position.side in ['long', 'buy']:
                    total_value += position_value
                elif position.side in ['short', 'sell']:
                    # 做空持仓的价值计算
                    entry_value = abs(position.amount) * position.avg_price
                    pnl = entry_value - position_value  # 做空的盈亏
                    total_value += entry_value + pnl

            return total_value

        except Exception as e:
            self.logger.error(f"计算投资组合价值失败: {e}")
            return self.cash_balance

    def update_position_price(self, symbol: str, new_price: float) -> bool:
        """更新持仓的当前价格"""
        try:
            if symbol in self.positions:
                self.positions[symbol].current_price = new_price
                return True
            return False
        except Exception as e:
            self.logger.error(f"更新持仓价格失败: {e}")
            return False

    def get_allocation(self, current_prices: Optional[Dict[str, float]] = None) -> Dict[str, float]:
        """获取资产配置"""
        # 如果没有提供当前价格，使用持仓的当前价格
        if current_prices is None:
            current_prices = {symbol: pos.current_price for symbol, pos in self.positions.items()}

        total_value = self.get_portfolio_value(current_prices)  # 修复：使用正确的方法名
        if total_value == 0:
            return {}

        allocation = {}

        # 现金配置
        allocation['CASH'] = self.cash_balance / total_value

        # 各持仓配置
        for symbol, position in self.positions.items():
            allocation[symbol] = position.market_value / total_value

        return allocation

    def get_portfolio_summary(self, current_prices: Optional[Dict[str, float]] = None) -> PortfolioSummary:
        """获取投资组合摘要"""
        # 如果没有提供当前价格，使用持仓的当前价格
        if current_prices is None:
            current_prices = {symbol: pos.current_price for symbol, pos in self.positions.items()}

        total_value = self.get_portfolio_value(current_prices)  # 修复：使用正确的方法名
        total_invested = self.initial_cash - self.cash_balance

        # 计算未实现盈亏
        unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        unrealized_pnl_pct = unrealized_pnl / total_invested if total_invested > 0 else 0.0

        # 计算风险指标
        risk_metrics = self._calculate_risk_metrics()

        return PortfolioSummary(
            total_value=total_value,
            cash_balance=self.cash_balance,
            total_invested=total_invested,
            unrealized_pnl=unrealized_pnl,
            unrealized_pnl_pct=unrealized_pnl_pct,
            positions_count=len(self.positions),
            allocation=self.get_allocation(),
            risk_metrics=risk_metrics
        )

    def _calculate_risk_metrics(self) -> Dict[str, float]:
        """计算风险指标"""
        if not self.positions:
            return {'volatility': 0.0, 'max_position_weight': 0.0, 'concentration_risk': 0.0}

        # 使用当前价格计算总价值
        current_prices = {symbol: pos.current_price for symbol, pos in self.positions.items()}
        total_value = self.get_portfolio_value(current_prices)
        position_weights = [pos.market_value / total_value for pos in self.positions.values()]

        # 最大持仓权重
        max_position_weight = max(position_weights) if position_weights else 0.0

        # 集中度风险（赫芬达尔指数）
        concentration_risk = sum(w**2 for w in position_weights)

        # 简化的波动率估算
        volatility = np.std(position_weights) if len(position_weights) > 1 else 0.0

        return {
            'volatility': volatility,
            'max_position_weight': max_position_weight,
            'concentration_risk': concentration_risk
        }

    def suggest_rebalancing(self, target_allocation: Dict[str, float], current_prices: Optional[Dict[str, float]] = None) -> List[Dict[str, Any]]:
        """建议再平衡操作"""
        current_allocation = self.get_allocation(current_prices)
        # 使用当前价格计算总价值
        if current_prices is None:
            current_prices = {symbol: pos.current_price for symbol, pos in self.positions.items()}
        total_value = self.get_portfolio_value(current_prices)
        suggestions = []

        for symbol, target_weight in target_allocation.items():
            if symbol == 'CASH':
                continue

            current_weight = current_allocation.get(symbol, 0.0)
            weight_diff = target_weight - current_weight

            if abs(weight_diff) > self.rebalance_threshold:
                target_value = target_weight * total_value
                current_value = current_weight * total_value
                value_diff = target_value - current_value

                if symbol in self.positions:
                    current_price = self.positions[symbol].current_price
                    amount_diff = value_diff / current_price

                    suggestions.append({
                        'symbol': symbol,
                        'action': 'buy' if amount_diff > 0 else 'sell',
                        'amount': abs(amount_diff),
                        'current_weight': current_weight,
                        'target_weight': target_weight,
                        'weight_diff': weight_diff
                    })

        return suggestions

    def calculate_risk_metrics(self) -> Dict[str, float]:
        """计算投资组合风险指标"""
        try:
            if not self.positions:
                return {
                    'portfolio_var': 0.0,
                    'max_drawdown': 0.0,
                    'sharpe_ratio': 0.0,
                    'volatility': 0.0,
                    'concentration_risk': 0.0,
                    'leverage_ratio': 1.0
                }

            # 计算投资组合价值
            current_prices = {symbol: pos.current_price for symbol, pos in self.positions.items()}
            total_value = self.get_portfolio_value(current_prices)
            if total_value <= 0:
                return self._get_default_risk_metrics()

            # 计算集中度风险
            allocation = self.get_allocation()
            concentration_risk = max(allocation.values()) if allocation else 0.0

            # 计算杠杆比率
            total_exposure = sum(abs(pos.amount * pos.current_price * pos.leverage)
                               for pos in self.positions.values())
            leverage_ratio = total_exposure / total_value if total_value > 0 else 1.0

            # 计算波动率（简化版本）
            position_values = [pos.market_value for pos in self.positions.values()]
            if len(position_values) > 1:
                volatility = np.std(position_values) / np.mean(position_values)
            else:
                volatility = 0.1  # 默认波动率

            # 计算VaR（简化版本，假设正态分布）
            confidence_level = 0.95
            z_score = 1.645  # 95%置信度
            portfolio_var = total_value * volatility * z_score

            # 计算最大回撤（简化版本）
            max_drawdown = min(0.0, -concentration_risk * 0.2)  # 简化计算

            # 计算夏普比率（简化版本）
            risk_free_rate = 0.02  # 假设2%无风险利率
            expected_return = 0.08  # 假设8%预期收益
            sharpe_ratio = (expected_return - risk_free_rate) / volatility if volatility > 0 else 0.0

            return {
                'portfolio_var': portfolio_var,
                'max_drawdown': max_drawdown,
                'sharpe_ratio': sharpe_ratio,
                'volatility': volatility,
                'concentration_risk': concentration_risk,
                'leverage_ratio': leverage_ratio
            }

        except Exception as e:
            self.logger.error(f"计算风险指标失败: {e}")
            return self._get_default_risk_metrics()

    def _get_default_risk_metrics(self) -> Dict[str, float]:
        """获取默认风险指标"""
        return {
            'portfolio_var': 0.0,
            'max_drawdown': 0.0,
            'sharpe_ratio': 0.0,
            'volatility': 0.0,
            'concentration_risk': 0.0,
            'leverage_ratio': 1.0
        }


class AdvancedPortfolioManager(PortfolioManager):
    """高级投资组合管理器"""

    def __init__(self, initial_cash: float = 10000.0, config: Optional[Dict] = None):
        super().__init__(initial_cash, config)
        self.risk_budget = self.config.get('risk_budget', 0.02)  # 2%风险预算
        self.correlation_matrix = {}
        self.return_history = {}

    def calculate_optimal_allocation(self, expected_returns: Dict[str, float],
                                   risk_tolerance: float = 0.5) -> Dict[str, float]:
        """计算最优资产配置（简化的均值方差优化）"""
        symbols = list(expected_returns.keys())
        n_assets = len(symbols)

        if n_assets == 0:
            return {'CASH': 1.0}

        # 简化的等权重配置作为基准
        equal_weight = (1.0 - self.min_cash_reserve) / n_assets
        allocation = {symbol: equal_weight for symbol in symbols}
        allocation['CASH'] = self.min_cash_reserve

        # 根据预期收益调整权重
        total_expected_return = sum(expected_returns.values())
        if total_expected_return > 0:
            for symbol in symbols:
                weight_adjustment = expected_returns[symbol] / total_expected_return
                allocation[symbol] *= (1 + weight_adjustment * risk_tolerance)

        # 归一化权重
        total_weight = sum(allocation.values())
        if total_weight > 0:
            allocation = {k: v / total_weight for k, v in allocation.items()}

        return allocation

    def calculate_portfolio_var(self, confidence: float = 0.95, time_horizon: int = 1) -> float:
        """计算投资组合VaR"""
        if not self.positions:
            return 0.0

        # 简化的VaR计算
        current_prices = {symbol: pos.current_price for symbol, pos in self.positions.items()}
        total_value = self.get_portfolio_value(current_prices)
        portfolio_volatility = self._calculate_risk_metrics()['volatility']

        # 假设正态分布
        z_score = 1.645 if confidence == 0.95 else 2.33  # 95%或99%置信度
        var = total_value * portfolio_volatility * z_score * np.sqrt(time_horizon)

        return var

    def optimize_position_sizing(self, signal_strength: float, risk_assessment: Dict) -> float:
        """优化仓位大小（Kelly公式的简化版本）"""
        win_rate = risk_assessment.get('win_rate', 0.5)
        avg_win = risk_assessment.get('avg_win', 0.02)
        avg_loss = risk_assessment.get('avg_loss', 0.01)

        if avg_loss == 0:
            return 0.0

        # Kelly公式: f = (bp - q) / b
        # 其中 b = avg_win/avg_loss, p = win_rate, q = 1-win_rate
        b = avg_win / avg_loss
        p = win_rate
        q = 1 - win_rate

        kelly_fraction = (b * p - q) / b

        # 调整Kelly比例（通常使用25-50%的Kelly比例）
        adjusted_fraction = kelly_fraction * 0.25 * signal_strength

        # 限制最大仓位
        max_position_fraction = self.config.get('max_position_fraction', 0.2)
        final_fraction = min(max(adjusted_fraction, 0.0), max_position_fraction)

        return final_fraction
