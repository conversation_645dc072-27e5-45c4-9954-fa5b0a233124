"""
策略执行引擎
负责协调信号生成、投资组合管理和风险控制
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime
import pandas as pd

from src.strategy.generators.signal_generator import SignalGenerator, HybridSignalGenerator
from src.strategy.managers.portfolio_manager import PortfolioManager, AdvancedPortfolioManager
from src.strategy.managers import StrategyRiskAdapter


@dataclass
class StrategySignal:
    """策略信号"""
    symbol: str
    signal_type: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float
    timestamp: datetime
    price: float
    metadata: Dict[str, Any]


@dataclass
class StrategyDecision:
    """策略决策"""
    symbol: str
    action: str  # 'BUY', 'SELL', 'HOLD'
    quantity: float
    price: float
    confidence: float
    risk_score: float
    timestamp: datetime
    reasoning: str


class StrategyEngine:
    """策略执行引擎"""
    
    def __init__(self, 
                 signal_generator: SignalGenerator,
                 portfolio_manager: PortfolioManager,
                 risk_manager: StrategyRiskAdapter,
                 config: Dict[str, Any] = None):
        """
        初始化策略引擎
        
        Args:
            signal_generator: 信号生成器
            portfolio_manager: 投资组合管理器
            risk_manager: 风险管理器
            config: 配置参数
        """
        self.signal_generator = signal_generator
        self.portfolio_manager = portfolio_manager
        self.risk_manager = risk_manager
        self.config = config or {}
        
        self.logger = logging.getLogger(__name__)
        self.is_running = False
        self.decisions_history = []
        
        # 策略参数
        self.min_confidence_threshold = self.config.get('min_confidence_threshold', 0.6)
        self.max_position_size = self.config.get('max_position_size', 0.1)  # 10%
        self.rebalance_frequency = self.config.get('rebalance_frequency', 3600)  # 1小时
        
    async def start(self):
        """启动策略引擎"""
        if self.is_running:
            self.logger.warning("策略引擎已在运行")
            return
            
        self.is_running = True
        self.logger.info("🚀 策略引擎启动")
        
        try:
            await self._run_strategy_loop()
        except Exception as e:
            self.logger.error(f"策略引擎运行错误: {e}")
            raise
        finally:
            self.is_running = False
            
    async def stop(self):
        """停止策略引擎"""
        self.is_running = False
        self.logger.info("⏹️ 策略引擎停止")
        
    async def _run_strategy_loop(self):
        """运行策略主循环"""
        while self.is_running:
            try:
                # 1. 生成信号
                signals = await self._generate_signals()
                
                # 2. 风险评估
                risk_assessment = await self._assess_risks(signals)
                
                # 3. 生成决策
                decisions = await self._make_decisions(signals, risk_assessment)
                
                # 4. 执行决策
                await self._execute_decisions(decisions)
                
                # 5. 更新投资组合
                await self._update_portfolio()
                
                # 等待下一个周期
                await asyncio.sleep(self.rebalance_frequency)
                
            except Exception as e:
                self.logger.error(f"策略循环错误: {e}")
                await asyncio.sleep(60)  # 错误后等待1分钟
                
    async def _generate_signals(self) -> List[StrategySignal]:
        """生成交易信号"""
        try:
            # 获取市场数据
            market_data = await self._get_market_data()
            
            # 生成信号
            raw_signals = await self.signal_generator.generate_signals(market_data)
            
            # 转换为策略信号格式
            signals = []
            for symbol, signal_data in raw_signals.items():
                signal = StrategySignal(
                    symbol=symbol,
                    signal_type=signal_data.get('signal', 'HOLD'),
                    confidence=signal_data.get('confidence', 0.0),
                    timestamp=datetime.now(),
                    price=signal_data.get('price', 0.0),
                    metadata=signal_data.get('metadata', {})
                )
                signals.append(signal)
                
            self.logger.info(f"生成 {len(signals)} 个交易信号")
            return signals
            
        except Exception as e:
            self.logger.error(f"信号生成失败: {e}")
            return []
            
    async def _assess_risks(self, signals: List[StrategySignal]) -> Dict[str, float]:
        """评估风险"""
        try:
            risk_scores = {}
            
            for signal in signals:
                # 构建持仓信息用于风险评估
                position_info = {
                    'symbol': signal.symbol,
                    'side': 'BUY' if signal.signal_type == 'BUY' else 'SELL',
                    'confidence': signal.confidence,
                    'price': signal.price,
                    'amount': 1000.0,  # 假设标准仓位大小
                }

                # 获取当前投资组合
                current_portfolio = self.portfolio_manager.get_current_portfolio()

                # 评估风险
                risk_assessment = await self.risk_manager.assess_risk(position_info, current_portfolio)
                risk_scores[signal.symbol] = risk_assessment.risk_score
                
            return risk_scores
            
        except Exception as e:
            self.logger.error(f"风险评估失败: {e}")
            return {}
            
    async def _make_decisions(self, 
                            signals: List[StrategySignal], 
                            risk_assessment: Dict[str, float]) -> List[StrategyDecision]:
        """制定交易决策"""
        decisions = []
        
        try:
            for signal in signals:
                # 检查信号置信度
                if signal.confidence < self.min_confidence_threshold:
                    continue
                    
                # 检查风险分数
                risk_score = risk_assessment.get(signal.symbol, 1.0)
                if risk_score > 0.8:  # 高风险，跳过
                    continue
                    
                # 计算仓位大小
                position_size = await self._calculate_position_size(signal, risk_score)
                
                if position_size > 0:
                    decision = StrategyDecision(
                        symbol=signal.symbol,
                        action=signal.signal_type,
                        quantity=position_size,
                        price=signal.price,
                        confidence=signal.confidence,
                        risk_score=risk_score,
                        timestamp=datetime.now(),
                        reasoning=f"信号置信度: {signal.confidence:.2f}, 风险分数: {risk_score:.2f}"
                    )
                    decisions.append(decision)
                    
            self.logger.info(f"生成 {len(decisions)} 个交易决策")
            return decisions
            
        except Exception as e:
            self.logger.error(f"决策制定失败: {e}")
            return []
            
    async def _calculate_position_size(self, signal: StrategySignal, risk_score: float) -> float:
        """计算仓位大小"""
        try:
            # 基础仓位大小（基于置信度）
            base_size = signal.confidence * self.max_position_size
            
            # 风险调整
            risk_adjusted_size = base_size * (1 - risk_score)
            
            # 投资组合约束
            portfolio_adjusted_size = await self.portfolio_manager.calculate_optimal_position_size(
                symbol=signal.symbol,
                target_size=risk_adjusted_size
            )
            
            return max(0, portfolio_adjusted_size)
            
        except Exception as e:
            self.logger.error(f"仓位大小计算失败: {e}")
            return 0
            
    async def _execute_decisions(self, decisions: List[StrategyDecision]):
        """执行交易决策"""
        for decision in decisions:
            try:
                # 执行交易
                result = await self.portfolio_manager.execute_trade(
                    symbol=decision.symbol,
                    action=decision.action,
                    quantity=decision.quantity,
                    price=decision.price
                )
                
                if result['success']:
                    self.logger.info(f"✅ 执行交易: {decision.symbol} {decision.action} {decision.quantity}")
                    self.decisions_history.append(decision)
                else:
                    self.logger.warning(f"❌ 交易执行失败: {result.get('error', 'Unknown error')}")
                    
            except Exception as e:
                self.logger.error(f"交易执行错误: {e}")
                
    async def _update_portfolio(self):
        """更新投资组合状态"""
        try:
            await self.portfolio_manager.update_portfolio()
            self.logger.debug("投资组合状态已更新")
        except Exception as e:
            self.logger.error(f"投资组合更新失败: {e}")
            
    async def _get_market_data(self) -> Dict[str, pd.DataFrame]:
        """获取市场数据"""
        # 这里应该从数据源获取最新的市场数据
        # 暂时返回空字典，实际实现时需要连接到数据源
        return {}
        
    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取策略性能指标"""
        if not self.decisions_history:
            return {}
            
        total_decisions = len(self.decisions_history)
        avg_confidence = sum(d.confidence for d in self.decisions_history) / total_decisions
        avg_risk_score = sum(d.risk_score for d in self.decisions_history) / total_decisions
        
        return {
            'total_decisions': total_decisions,
            'average_confidence': avg_confidence,
            'average_risk_score': avg_risk_score,
            'last_decision_time': self.decisions_history[-1].timestamp if self.decisions_history else None
        }
