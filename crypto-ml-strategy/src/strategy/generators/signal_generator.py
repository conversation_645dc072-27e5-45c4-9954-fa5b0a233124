"""
信号生成器模块
负责生成交易信号
"""

import numpy as np
import pandas as pd
import torch
import asyncio
import time
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass
from enum import Enum


class SignalType(Enum):
    """信号类型"""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"


@dataclass
class TradingSignal:
    """交易信号数据类"""
    symbol: str
    signal_type: SignalType
    confidence: float
    price: float
    timestamp: float
    features: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class SignalGenerator:
    """信号生成器基类"""
    
    def __init__(self, config: Optional[Dict] = None):
        self.config = config or {}
        self.min_confidence = self.config.get('min_confidence', 0.6)
        self.lookback_period = self.config.get('lookback_period', 20)
        
    def generate_signal(self, data: Dict[str, Any]) -> TradingSignal:
        """生成交易信号"""
        raise NotImplementedError("子类必须实现此方法")
    
    async def generate_signal_async(self, data: Dict[str, Any]) -> TradingSignal:
        """异步生成交易信号"""
        return self.generate_signal(data)


class TechnicalSignalGenerator(SignalGenerator):
    """技术分析信号生成器"""
    
    def __init__(self, config: Optional[Dict] = None):
        super().__init__(config)
        self.rsi_period = self.config.get('rsi_period', 14)
        self.ma_short = self.config.get('ma_short', 10)
        self.ma_long = self.config.get('ma_long', 30)
    
    def calculate_rsi(self, prices: List[float]) -> float:
        """计算RSI指标"""
        if len(prices) < self.rsi_period + 1:
            return 50.0  # 默认中性值
        
        deltas = np.diff(prices)
        gains = np.where(deltas > 0, deltas, 0)
        losses = np.where(deltas < 0, -deltas, 0)
        
        avg_gain = np.mean(gains[-self.rsi_period:])
        avg_loss = np.mean(losses[-self.rsi_period:])
        
        if avg_loss == 0:
            return 100.0
        
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_moving_averages(self, prices: List[float]) -> Dict[str, float]:
        """计算移动平均线"""
        if len(prices) < self.ma_long:
            return {'ma_short': prices[-1] if prices else 0, 'ma_long': prices[-1] if prices else 0}
        
        ma_short = np.mean(prices[-self.ma_short:])
        ma_long = np.mean(prices[-self.ma_long:])
        
        return {'ma_short': ma_short, 'ma_long': ma_long}
    
    def generate_signal(self, data: Dict[str, Any]) -> TradingSignal:
        """基于技术分析生成信号 - 优化版本"""
        symbol = data.get('symbol', 'UNKNOWN')
        current_price = data.get('price', 0.0)
        price_history = data.get('price_history', [current_price])
        volume = data.get('volume', 0.0)
        
        # 确保价格历史包含当前价格
        if current_price not in price_history:
            price_history = list(price_history) + [current_price]
        
        # 计算技术指标
        rsi = self.calculate_rsi(price_history)
        ma_data = self.calculate_moving_averages(price_history)
        
        # 初始化信号参数
        signal_type = SignalType.HOLD
        confidence = 0.5  # 基础置信度
        signal_strength = 0.0  # 信号强度
        
        # RSI策略 - 更敏感的阈值
        if rsi < 25:  # 极度超卖
            signal_type = SignalType.BUY
            signal_strength += 0.5
        elif rsi < 35:  # 严重超卖
            signal_type = SignalType.BUY
            signal_strength += 0.3
        elif rsi < 45:  # 轻度超卖
            if signal_type == SignalType.HOLD:
                signal_type = SignalType.BUY
            signal_strength += 0.1
        elif rsi > 75:  # 极度超买
            signal_type = SignalType.SELL
            signal_strength += 0.5
        elif rsi > 65:  # 严重超买
            signal_type = SignalType.SELL
            signal_strength += 0.3
        elif rsi > 55:  # 轻度超买
            if signal_type == SignalType.HOLD:
                signal_type = SignalType.SELL
            signal_strength += 0.1
        
        # 移动平均线策略 - 更敏感的阈值
        ma_diff = (ma_data['ma_short'] - ma_data['ma_long']) / ma_data['ma_long'] if ma_data['ma_long'] > 0 else 0

        if abs(ma_diff) > 0.005:  # 0.5%以上差异就考虑（更敏感）
            if ma_diff > 0.02:  # 强烈金叉信号
                signal_type = SignalType.BUY
                signal_strength += 0.4
            elif ma_diff > 0.01:  # 中等金叉信号
                if signal_type == SignalType.HOLD:
                    signal_type = SignalType.BUY
                signal_strength += 0.2
            elif ma_diff > 0:  # 轻微金叉信号
                if signal_type == SignalType.BUY:
                    signal_strength += 0.3
                elif signal_type == SignalType.HOLD:
                    signal_type = SignalType.BUY
                    signal_strength += 0.15
            else:  # 死叉
                if signal_type == SignalType.SELL:
                    signal_strength += 0.3
                elif signal_type == SignalType.HOLD:
                    signal_type = SignalType.SELL
                    signal_strength += 0.15
        
        # 成交量确认 - 更严格的条件
        avg_volume = data.get('avg_volume', volume)
        volume_ratio = volume / avg_volume if avg_volume > 0 else 1.0
        
        if volume_ratio > 2.0:  # 成交量放大2倍以上
            signal_strength += 0.2
        elif volume_ratio > 1.5:  # 成交量放大1.5倍
            signal_strength += 0.1
        elif volume_ratio < 0.5:  # 成交量萎缩
            signal_strength *= 0.8  # 降低信号强度
        
        # 价格趋势确认
        if len(price_history) >= 3:
            recent_trend = (price_history[-1] - price_history[-3]) / price_history[-3]
            
            if abs(recent_trend) > 0.05:  # 5%以上变化
                if recent_trend > 0 and signal_type == SignalType.BUY:
                    signal_strength += 0.1
                elif recent_trend < 0 and signal_type == SignalType.SELL:
                    signal_strength += 0.1
        
        # 计算最终置信度
        final_confidence = 0.5 + signal_strength
        final_confidence = min(max(final_confidence, 0.0), 1.0)
        
        # 应用更严格的最小置信度检查
        min_confidence_threshold = self.min_confidence
        if signal_type != SignalType.HOLD and final_confidence < min_confidence_threshold:
            signal_type = SignalType.HOLD
            final_confidence = 0.5
        
        # 增加不确定性检查
        uncertainty_factors = 0
        
        # RSI在中性区间
        if 40 <= rsi <= 60:
            uncertainty_factors += 1
        
        # MA差异很小
        if abs(ma_diff) < 0.01:
            uncertainty_factors += 1
        
        # 成交量正常
        if 0.8 <= volume_ratio <= 1.2:
            uncertainty_factors += 1
        
        # 如果不确定性因子过多，倾向于HOLD
        if uncertainty_factors >= 2 and final_confidence < 0.7:
            signal_type = SignalType.HOLD
            final_confidence = 0.5
        
        return TradingSignal(
            symbol=symbol,
            signal_type=signal_type,
            confidence=final_confidence,
            price=current_price,
            timestamp=time.time(),
            features={
                'rsi': rsi,
                'ma_short': ma_data['ma_short'],
                'ma_long': ma_data['ma_long'],
                'ma_diff_pct': ma_diff * 100,
                'volume': volume,
                'avg_volume': avg_volume,
                'volume_ratio': volume_ratio,
                'signal_strength': signal_strength,
                'uncertainty_factors': uncertainty_factors
            },
            metadata={
                'generator': 'TechnicalSignalGenerator',
                'version': '1.1_optimized',
                'optimization': 'improved_signal_diversity'
            }
        )
class MLSignalGenerator(SignalGenerator):
    """机器学习信号生成器"""
    
    def __init__(self, model=None, config: Optional[Dict] = None):
        super().__init__(config)
        self.model = model
        # 移除硬编码的特征维度。维度应该由模型或数据源本身决定。
        if model and hasattr(model, 'feature_dim'):
            self.feature_dim = model.feature_dim
        else:
            # 提供一个合理的默认值，但警告用户这可能不是最优的
            self.feature_dim = self.config.get('feature_dim', 64) # 默认为64，但应在配置中明确设置
    
    def prepare_features(self, data: Dict[str, Any]) -> np.ndarray:
        """
        准备机器学习特征 - 彻底重构版本
        - 增加更多有意义的技术指标
        - 实现缺失的关键特征
        - 移除脆弱的零填充逻辑
        """
        price_history = np.array(data.get('price_history', []), dtype=np.float32)
        volume_history = np.array(data.get('volume_history', []), dtype=np.float32)
        
        if len(price_history) < self.lookback_period:
            # 如果数据不足，返回零向量，并由上层逻辑处理
            return np.zeros(self.feature_dim, dtype=np.float32)

        # 使用pandas进行高效计算
        df = pd.DataFrame({'price': price_history, 'volume': volume_history[-len(price_history):]})

        features = {}

        # 1. 价格和回报率特征
        features['price'] = df['price'].iloc[-1]
        features['log_return'] = np.log(df['price'] / df['price'].shift(1)).iloc[-1]
        
        # 2. 移动平均线 (MA)
        for period in [5, 10, 20, 30]:
            ma = df['price'].rolling(window=period).mean()
            features[f'ma_{period}'] = ma.iloc[-1]
            features[f'price_div_ma_{period}'] = df['price'].iloc[-1] / ma.iloc[-1] if ma.iloc[-1] > 0 else 1.0

        # 3. 指数移动平均线 (EMA)
        for period in [5, 10, 20, 30]:
            ema = df['price'].ewm(span=period, adjust=False).mean()
            features[f'ema_{period}'] = ema.iloc[-1]
            features[f'ema_div_ema_long'] = ema.iloc[-1] / df['price'].ewm(span=60, adjust=False).mean().iloc[-1] if ema.iloc[-1] > 0 else 1.0

        # 4. 波动率特征
        features['volatility_30'] = df['price'].pct_change().rolling(window=30).std().iloc[-1] * np.sqrt(30)
        
        # 5. RSI
        delta = df['price'].diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
        rs = gain / loss
        features['rsi'] = (100 - (100 / (1 + rs))).iloc[-1]

        # 6. MACD
        ema_12 = df['price'].ewm(span=12, adjust=False).mean()
        ema_26 = df['price'].ewm(span=26, adjust=False).mean()
        macd_line = ema_12 - ema_26
        signal_line = macd_line.ewm(span=9, adjust=False).mean()
        features['macd'] = macd_line.iloc[-1]
        features['macd_signal'] = signal_line.iloc[-1]
        features['macd_hist'] = (macd_line - signal_line).iloc[-1]

        # 7. 布林带
        ma_20 = df['price'].rolling(window=20).mean()
        std_20 = df['price'].rolling(window=20).std()
        upper_band = ma_20 + (std_20 * 2)
        lower_band = ma_20 - (std_20 * 2)
        features['bollinger_upper'] = upper_band.iloc[-1]
        features['bollinger_lower'] = lower_band.iloc[-1]
        features['bollinger_width'] = (upper_band - lower_band).iloc[-1] / ma_20.iloc[-1] if ma_20.iloc[-1] > 0 else 0

        # 8. 成交量特征
        features['volume'] = df['volume'].iloc[-1]
        features['volume_ma_20'] = df['volume'].rolling(window=20).mean().iloc[-1]

        # 9. 时间特征
        timestamp = data.get('timestamp', time.time())
        features['hour_of_day'] = (timestamp % 86400) / 3600
        features['day_of_week'] = (timestamp // 86400) % 7

        # 10. 缺失的关键特征 (spread, buy_sell_ratio)
        # 假设这些数据在 'data' 字典中可用
        high_prices = np.array(data.get('high_history', price_history))
        low_prices = np.array(data.get('low_history', price_history))
        features['spread'] = (high_prices[-1] - low_prices[-1]) / features['price'] if features['price'] > 0 else 0
        
        # 假设有买卖量数据
        buy_volume = data.get('buy_volume', 0)
        sell_volume = data.get('sell_volume', 0)
        total_volume = buy_volume + sell_volume
        features['buy_sell_ratio'] = buy_volume / total_volume if total_volume > 0 else 0.5

        # 保证特征顺序一致
        feature_keys = sorted(features.keys())
        final_features = [features[key] for key in feature_keys]
        
        # 动态调整 self.feature_dim
        current_dim = len(final_features)
        if self.feature_dim != current_dim:
            # 这是第一次或维度已更改，记录下来
            # 注意：这应该在模型初始化时同步，这里只做警告
            pass

        # 填充或截断到模型期望的维度 (作为最后的保险)
        if len(final_features) < self.feature_dim:
            final_features.extend([0.0] * (self.feature_dim - len(final_features)))
        else:
            final_features = final_features[:self.feature_dim]

        return np.array(final_features, dtype=np.float32)
    
    def generate_signal(self, data: Dict[str, Any]) -> TradingSignal:
        """基于机器学习模型生成信号"""
        symbol = data.get('symbol', 'UNKNOWN')
        current_price = data.get('price', 0.0)
        
        if self.model is None:
            # 如果没有模型，使用技术分析作为后备
            tech_generator = TechnicalSignalGenerator(self.config)
            return tech_generator.generate_signal(data)
        
        try:
            # 准备特征
            features = self.prepare_features(data)
            
            # 模型预测
            if hasattr(self.model, 'predict'):
                # sklearn风格模型
                prediction = self.model.predict([features])[0]
                confidence = 0.8
            elif hasattr(self.model, '__call__'):
                # PyTorch模型
                import torch
                with torch.no_grad():
                    features_tensor = np.array(features, dtype=np.float32).unsqueeze(0)
                    output = self.model(features_tensor)
                    
                    if isinstance(output, dict):
                        logits = output.get('signal', output.get('logits'))
                    else:
                        logits = output

                    # 检查模型输出的形状，以确定是分类还是回归
                    if len(logits.shape) > 1 and logits.shape[-1] > 1:
                        # 分类任务 (BUY, SELL, HOLD)
                        probabilities = torch.softmax(logits, dim=-1)
                        confidence, prediction = torch.max(probabilities, dim=-1)
                        confidence = confidence.item()
                        prediction = prediction.item()
                        signal_mapping = {0: SignalType.SELL, 1: SignalType.HOLD, 2: SignalType.BUY}
                        signal_type = signal_mapping.get(prediction, SignalType.HOLD)
                    else:
                        # 回归任务 (信号强度, e.g., -1.0 to 1.0)
                        # 使用tanh将输出归一化到[-1, 1]范围
                        signal_strength = torch.tanh(logits.squeeze()).item()
                        
                        # 将信号强度转换为信号类型和置信度
                        confidence = abs(signal_strength)
                        if signal_strength > 0.1: # 增加一个小的死区，避免在0附近抖动
                            signal_type = SignalType.BUY
                        elif signal_strength < -0.1:
                            signal_type = SignalType.SELL
                        else:
                            signal_type = SignalType.HOLD
                        
                        # 为了与下游兼容，我们仍然需要一个'prediction'值
                        prediction = 1 # HOLD as default
                        if signal_type == SignalType.BUY:
                            prediction = 2
                        elif signal_type == SignalType.SELL:
                            prediction = 0
            else:
                raise ValueError("不支持的模型类型")
            
            # 转换预测结果 (这部分逻辑已移至上面处理)
            # signal_mapping = {0: SignalType.SELL, 1: SignalType.HOLD, 2: SignalType.BUY}
            # signal_type = signal_mapping.get(prediction, SignalType.HOLD)
            
            # 检查最小置信度
            if confidence < self.min_confidence:
                signal_type = SignalType.HOLD
                confidence = 0.5
            
            return TradingSignal(
                symbol=symbol,
                signal_type=signal_type,
                confidence=confidence,
                price=current_price,
                timestamp=time.time(),
                features={
                    'ml_features': features.tolist(),
                    'prediction': prediction,
                    'model_confidence': confidence
                },
                metadata={
                    'generator': 'MLSignalGenerator',
                    'version': '1.0',
                    'model_type': type(self.model).__name__
                }
            )
        
        except Exception as e:
            # 出错时使用技术分析作为后备
            tech_generator = TechnicalSignalGenerator(self.config)
            signal = tech_generator.generate_signal(data)
            signal.metadata['fallback_reason'] = str(e)
            return signal


class HybridSignalGenerator(SignalGenerator):
    """混合信号生成器 - 结合技术分析和机器学习"""
    
    def __init__(self, model=None, config: Optional[Dict] = None):
        super().__init__(config)
        self.tech_generator = TechnicalSignalGenerator(config)
        self.ml_generator = MLSignalGenerator(model, config)
        self.tech_weight = self.config.get('tech_weight', 0.4)
        self.ml_weight = self.config.get('ml_weight', 0.6)
    
    def generate_signal(self, data: Dict[str, Any]) -> TradingSignal:
        """生成混合信号"""
        symbol = data.get('symbol', 'UNKNOWN')
        current_price = data.get('price', 0.0)
        
        # 获取技术分析信号
        tech_signal = self.tech_generator.generate_signal(data)
        
        # 获取机器学习信号
        ml_signal = self.ml_generator.generate_signal(data)
        
        # 信号权重计算
        signal_scores = {
            SignalType.BUY: 0.0,
            SignalType.HOLD: 0.0,
            SignalType.SELL: 0.0
        }
        
        # 技术分析权重
        signal_scores[tech_signal.signal_type] += self.tech_weight * tech_signal.confidence
        
        # 机器学习权重
        signal_scores[ml_signal.signal_type] += self.ml_weight * ml_signal.confidence
        
        # 选择最高分数的信号
        final_signal_type = max(signal_scores.items(), key=lambda x: x[1])[0]
        final_confidence = signal_scores[final_signal_type]
        
        # 检查最小置信度
        if final_confidence < self.min_confidence:
            final_signal_type = SignalType.HOLD
            final_confidence = 0.5
        
        return TradingSignal(
            symbol=symbol,
            signal_type=final_signal_type,
            confidence=final_confidence,
            price=current_price,
            timestamp=time.time(),
            features={
                'tech_signal': {
                    'type': tech_signal.signal_type.value,
                    'confidence': tech_signal.confidence,
                    'features': tech_signal.features
                },
                'ml_signal': {
                    'type': ml_signal.signal_type.value,
                    'confidence': ml_signal.confidence,
                    'features': ml_signal.features
                },
                'signal_scores': {k.value: v for k, v in signal_scores.items()}
            },
            metadata={
                'generator': 'HybridSignalGenerator',
                'version': '1.0',
                'tech_weight': self.tech_weight,
                'ml_weight': self.ml_weight
            }
        )
