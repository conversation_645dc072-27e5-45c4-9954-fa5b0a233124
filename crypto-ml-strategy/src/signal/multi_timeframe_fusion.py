"""
多时间框架信号融合器
实现1m、5m、15m、1h、4h多时间框架信号融合，提高预测准确性
"""

import asyncio
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
from collections import deque, defaultdict
import torch
import torch.nn as nn

@dataclass
class TimeframeSignal:
    """时间框架信号"""
    timeframe: str
    symbol: str
    timestamp: float
    action: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float
    price: float
    features: Dict[str, float]
    strength: float  # 信号强度

@dataclass
class FusedSignal:
    """融合信号"""
    symbol: str
    timestamp: float
    final_action: str
    final_confidence: float
    timeframe_signals: Dict[str, TimeframeSignal]
    fusion_weights: Dict[str, float]
    consensus_score: float
    risk_score: float

class MultiTimeframeFusion:
    """多时间框架信号融合器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 时间框架配置 - 支持全时间框架混合训练
        self.timeframes = config.get('timeframes', ['1m', '3m', '5m', '15m', '1h', '4h', '1d'])
        self.timeframe_weights = config.get('timeframe_weights', {
            '1m': 0.08,   # 短期噪音较多，权重最低
            '3m': 0.12,   # 短期趋势
            '5m': 0.15,   # 短期趋势
            '15m': 0.20,  # 中短期趋势
            '1h': 0.25,   # 中期趋势，权重较高
            '4h': 0.15,   # 长期趋势
            '1d': 0.05    # 超长期趋势，权重较低
        })
        
        # 信号缓存
        self.signal_cache = defaultdict(lambda: defaultdict(lambda: deque(maxlen=100)))
        
        # 融合模型
        self.fusion_model = self._create_fusion_model()
        
        # 一致性阈值
        self.consensus_threshold = config.get('consensus_threshold', 0.6)
        self.min_timeframes = config.get('min_timeframes', 3)
        
        # 性能统计
        self.fusion_stats = {
            'total_signals': 0,
            'consensus_signals': 0,
            'accuracy_by_timeframe': defaultdict(list),
            'fusion_accuracy': []
        }
    
    def _create_fusion_model(self) -> nn.Module:
        """创建信号融合神经网络"""
        class SignalFusionNet(nn.Module):
            def __init__(self, num_timeframes: int, feature_dim: int = 10):
                super().__init__()
                
                # 时间框架特征编码器 - 支持全时间框架
                self.timeframe_encoders = nn.ModuleDict({
                    tf: nn.Sequential(
                        nn.Linear(feature_dim, 32),
                        nn.ReLU(),
                        nn.Dropout(0.1),
                        nn.Linear(32, 16)
                    ) for tf in ['1m', '3m', '5m', '15m', '1h', '4h', '1d']
                })
                
                # 注意力机制
                self.attention = nn.MultiheadAttention(
                    embed_dim=16, 
                    num_heads=4, 
                    dropout=0.1,
                    batch_first=True
                )
                
                # 融合层
                self.fusion_layer = nn.Sequential(
                    nn.Linear(16 * num_timeframes, 64),
                    nn.ReLU(),
                    nn.Dropout(0.2),
                    nn.Linear(64, 32),
                    nn.ReLU(),
                    nn.Linear(32, 3)  # BUY, SELL, HOLD
                )
                
                # 置信度预测
                self.confidence_layer = nn.Sequential(
                    nn.Linear(16 * num_timeframes, 32),
                    nn.ReLU(),
                    nn.Linear(32, 1),
                    nn.Sigmoid()
                )
            
            def forward(self, timeframe_features: Dict[str, torch.Tensor]) -> Tuple[torch.Tensor, torch.Tensor]:
                # 编码各时间框架特征
                encoded_features = []
                for tf in ['1m', '5m', '15m', '1h', '4h']:
                    if tf in timeframe_features:
                        encoded = self.timeframe_encoders[tf](timeframe_features[tf])
                        encoded_features.append(encoded)
                    else:
                        # 缺失时间框架用零填充
                        encoded_features.append(torch.zeros(timeframe_features[list(timeframe_features.keys())[0]].shape[0], 16))
                
                # 堆叠特征
                stacked_features = torch.stack(encoded_features, dim=1)  # (batch, timeframes, features)
                
                # 注意力机制
                attended_features, attention_weights = self.attention(
                    stacked_features, stacked_features, stacked_features
                )
                
                # 展平用于融合
                flattened = attended_features.flatten(start_dim=1)
                
                # 预测动作和置信度
                action_logits = self.fusion_layer(flattened)
                confidence = self.confidence_layer(flattened)
                
                return action_logits, confidence
        
        return SignalFusionNet(len(self.timeframes))
    
    async def add_timeframe_signal(self, signal: TimeframeSignal):
        """添加时间框架信号"""
        try:
            self.signal_cache[signal.symbol][signal.timeframe].append(signal)
            
            # 检查是否可以进行融合
            if self._can_fuse_signals(signal.symbol):
                fused_signal = await self._fuse_signals(signal.symbol)
                if fused_signal:
                    await self._emit_fused_signal(fused_signal)
            
        except Exception as e:
            self.logger.error(f"❌ 添加时间框架信号失败: {e}")
    
    def _can_fuse_signals(self, symbol: str) -> bool:
        """检查是否可以进行信号融合"""
        current_time = time.time()
        valid_timeframes = 0
        
        for timeframe in self.timeframes:
            signals = self.signal_cache[symbol][timeframe]
            if signals:
                # 检查最新信号是否在有效时间内
                latest_signal = signals[-1]
                max_age = self._get_max_signal_age(timeframe)
                if current_time - latest_signal.timestamp <= max_age:
                    valid_timeframes += 1
        
        return valid_timeframes >= self.min_timeframes
    
    def _get_max_signal_age(self, timeframe: str) -> float:
        """获取信号最大有效期（秒）"""
        age_mapping = {
            '1m': 120,    # 2分钟
            '5m': 600,    # 10分钟
            '15m': 1800,  # 30分钟
            '1h': 7200,   # 2小时
            '4h': 14400   # 4小时
        }
        return age_mapping.get(timeframe, 300)
    
    async def _fuse_signals(self, symbol: str) -> Optional[FusedSignal]:
        """融合多时间框架信号"""
        try:
            current_time = time.time()
            timeframe_signals = {}
            
            # 收集有效的时间框架信号
            for timeframe in self.timeframes:
                signals = self.signal_cache[symbol][timeframe]
                if signals:
                    latest_signal = signals[-1]
                    max_age = self._get_max_signal_age(timeframe)
                    if current_time - latest_signal.timestamp <= max_age:
                        timeframe_signals[timeframe] = latest_signal
            
            if len(timeframe_signals) < self.min_timeframes:
                return None
            
            # 方法1：基于权重的简单融合
            weighted_fusion = self._weighted_fusion(timeframe_signals)
            
            # 方法2：神经网络融合（如果有足够数据）
            if len(timeframe_signals) >= 4:
                nn_fusion = await self._neural_network_fusion(timeframe_signals)
                if nn_fusion:
                    # 结合两种方法
                    final_action = nn_fusion['action'] if nn_fusion['confidence'] > weighted_fusion['confidence'] else weighted_fusion['action']
                    final_confidence = max(nn_fusion['confidence'], weighted_fusion['confidence'])
                else:
                    final_action = weighted_fusion['action']
                    final_confidence = weighted_fusion['confidence']
            else:
                final_action = weighted_fusion['action']
                final_confidence = weighted_fusion['confidence']
            
            # 计算一致性分数
            consensus_score = self._calculate_consensus(timeframe_signals)
            
            # 计算风险分数
            risk_score = self._calculate_risk_score(timeframe_signals)
            
            # 应用一致性过滤
            if consensus_score < self.consensus_threshold:
                final_action = 'HOLD'
                final_confidence *= 0.5  # 降低置信度
            
            fused_signal = FusedSignal(
                symbol=symbol,
                timestamp=current_time,
                final_action=final_action,
                final_confidence=final_confidence,
                timeframe_signals=timeframe_signals,
                fusion_weights=self.timeframe_weights,
                consensus_score=consensus_score,
                risk_score=risk_score
            )
            
            # 更新统计
            self.fusion_stats['total_signals'] += 1
            if consensus_score >= self.consensus_threshold:
                self.fusion_stats['consensus_signals'] += 1
            
            return fused_signal
            
        except Exception as e:
            self.logger.error(f"❌ 信号融合失败 {symbol}: {e}")
            return None
    
    def _weighted_fusion(self, timeframe_signals: Dict[str, TimeframeSignal]) -> Dict[str, Any]:
        """基于权重的信号融合"""
        action_scores = {'BUY': 0, 'SELL': 0, 'HOLD': 0}
        total_weight = 0
        weighted_confidence = 0
        
        for timeframe, signal in timeframe_signals.items():
            weight = self.timeframe_weights.get(timeframe, 0.2)
            total_weight += weight
            
            # 累加动作分数
            action_scores[signal.action] += weight * signal.confidence
            weighted_confidence += weight * signal.confidence
        
        # 归一化
        if total_weight > 0:
            for action in action_scores:
                action_scores[action] /= total_weight
            weighted_confidence /= total_weight
        
        # 选择最高分数的动作
        final_action = max(action_scores, key=action_scores.get)
        
        return {
            'action': final_action,
            'confidence': weighted_confidence,
            'action_scores': action_scores
        }
    
    async def _neural_network_fusion(self, timeframe_signals: Dict[str, TimeframeSignal]) -> Optional[Dict[str, Any]]:
        """神经网络信号融合"""
        try:
            # 准备特征
            timeframe_features = {}
            
            for timeframe, signal in timeframe_signals.items():
                # 构建特征向量
                features = [
                    signal.confidence,
                    signal.strength,
                    1.0 if signal.action == 'BUY' else (-1.0 if signal.action == 'SELL' else 0.0),
                    signal.price,
                    time.time() - signal.timestamp,  # 信号年龄
                ]
                
                # 添加技术指标特征
                if signal.features:
                    features.extend([
                        signal.features.get('rsi', 50) / 100,
                        signal.features.get('macd', 0),
                        signal.features.get('bb_position', 0.5),
                        signal.features.get('volume_ratio', 1.0),
                        signal.features.get('momentum', 0)
                    ])
                else:
                    features.extend([0.5, 0, 0.5, 1.0, 0])  # 默认值
                
                timeframe_features[timeframe] = torch.tensor(features, dtype=torch.float32).unsqueeze(0)
            
            # 神经网络推理
            with torch.no_grad():
                action_logits, confidence = self.fusion_model(timeframe_features)
                
                # 转换为动作
                action_probs = torch.softmax(action_logits, dim=1)
                action_idx = torch.argmax(action_probs, dim=1).item()
                actions = ['SELL', 'HOLD', 'BUY']
                final_action = actions[action_idx]
                final_confidence = confidence.item()
            
            return {
                'action': final_action,
                'confidence': final_confidence,
                'action_probs': action_probs.squeeze().tolist()
            }
            
        except Exception as e:
            self.logger.error(f"❌ 神经网络融合失败: {e}")
            return None
    
    def _calculate_consensus(self, timeframe_signals: Dict[str, TimeframeSignal]) -> float:
        """计算信号一致性分数"""
        if not timeframe_signals:
            return 0.0
        
        actions = [signal.action for signal in timeframe_signals.values()]
        confidences = [signal.confidence for signal in timeframe_signals.values()]
        
        # 计算动作一致性
        action_counts = {}
        for action in actions:
            action_counts[action] = action_counts.get(action, 0) + 1
        
        max_count = max(action_counts.values())
        action_consensus = max_count / len(actions)
        
        # 计算置信度一致性（方差越小越一致）
        confidence_variance = np.var(confidences) if len(confidences) > 1 else 0
        confidence_consensus = 1.0 / (1.0 + confidence_variance)
        
        # 综合一致性分数
        consensus_score = 0.7 * action_consensus + 0.3 * confidence_consensus
        
        return consensus_score
    
    def _calculate_risk_score(self, timeframe_signals: Dict[str, TimeframeSignal]) -> float:
        """计算风险分数"""
        if not timeframe_signals:
            return 1.0  # 最高风险
        
        risk_factors = []
        
        # 信号强度分散度
        strengths = [signal.strength for signal in timeframe_signals.values()]
        strength_variance = np.var(strengths) if len(strengths) > 1 else 0
        risk_factors.append(strength_variance)
        
        # 时间框架覆盖度
        coverage = len(timeframe_signals) / len(self.timeframes)
        risk_factors.append(1.0 - coverage)
        
        # 信号新鲜度
        current_time = time.time()
        ages = [current_time - signal.timestamp for signal in timeframe_signals.values()]
        avg_age = np.mean(ages)
        max_age = max([self._get_max_signal_age(tf) for tf in timeframe_signals.keys()])
        age_risk = avg_age / max_age if max_age > 0 else 1.0
        risk_factors.append(age_risk)
        
        # 综合风险分数
        risk_score = np.mean(risk_factors)
        return min(risk_score, 1.0)
    
    async def _emit_fused_signal(self, fused_signal: FusedSignal):
        """发出融合信号"""
        try:
            self.logger.info(f"🔀 融合信号: {fused_signal.symbol} {fused_signal.final_action} "
                           f"(置信度: {fused_signal.final_confidence:.3f}, "
                           f"一致性: {fused_signal.consensus_score:.3f}, "
                           f"风险: {fused_signal.risk_score:.3f})")
            
            # 这里可以发送到Kafka或其他消息系统
            # await self.kafka_client.send_fused_signal(fused_signal)
            
        except Exception as e:
            self.logger.error(f"❌ 发出融合信号失败: {e}")
    
    def get_fusion_stats(self) -> Dict[str, Any]:
        """获取融合统计信息"""
        consensus_rate = (self.fusion_stats['consensus_signals'] / 
                         self.fusion_stats['total_signals']) if self.fusion_stats['total_signals'] > 0 else 0
        
        return {
            'total_signals': self.fusion_stats['total_signals'],
            'consensus_signals': self.fusion_stats['consensus_signals'],
            'consensus_rate': consensus_rate,
            'cache_sizes': {
                symbol: {tf: len(cache) for tf, cache in tf_cache.items()}
                for symbol, tf_cache in self.signal_cache.items()
            }
        }
    
    def get_latest_fused_signal(self, symbol: str) -> Optional[FusedSignal]:
        """获取最新的融合信号"""
        try:
            if self._can_fuse_signals(symbol):
                return asyncio.create_task(self._fuse_signals(symbol))
            return None
        except Exception as e:
            self.logger.error(f"❌ 获取最新融合信号失败: {e}")
            return None
