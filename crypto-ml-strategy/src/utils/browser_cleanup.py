#!/usr/bin/env python3
"""
Browser进程清理工具
修复choreographer.browser_async的unclean kill警告
"""

import os
import sys
import signal
import psutil
import logging
import atexit
import threading
import time
from typing import List, Optional
from pathlib import Path


class BrowserProcessManager:
    """浏览器进程管理器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.browser_processes: List[psutil.Process] = []
        self.cleanup_registered = False
        self._lock = threading.Lock()
        
        # 注册清理函数
        self.register_cleanup()
    
    def register_cleanup(self):
        """注册清理函数"""
        if not self.cleanup_registered:
            atexit.register(self.cleanup_all_browsers)
            self.cleanup_registered = True
            self.logger.debug("✅ Browser cleanup registered")
    
    def track_browser_process(self, pid: int):
        """跟踪浏览器进程"""
        try:
            process = psutil.Process(pid)
            with self._lock:
                self.browser_processes.append(process)
            self.logger.debug(f"🔍 Tracking browser process: {pid}")
        except psutil.NoSuchProcess:
            self.logger.warning(f"⚠️ Process {pid} not found")
    
    def find_browser_processes(self) -> List[psutil.Process]:
        """查找所有浏览器相关进程"""
        browser_processes = []
        
        # 浏览器进程名称模式
        browser_patterns = [
            'chrome', 'chromium', 'firefox', 'safari', 'edge',
            'kaleido', 'plotly-orca', 'orca', 'electron'
        ]
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    proc_name = proc_info.get('name', '').lower()
                    cmdline_list = proc_info.get('cmdline', [])

                    # 安全地处理cmdline
                    if isinstance(cmdline_list, list):
                        cmdline = ' '.join(cmdline_list).lower()
                    else:
                        cmdline = str(cmdline_list).lower() if cmdline_list else ''

                    # 检查是否是浏览器进程
                    is_browser = any(pattern in proc_name for pattern in browser_patterns)
                    is_browser = is_browser or any(pattern in cmdline for pattern in browser_patterns)

                    # 特别检查kaleido相关进程
                    if 'kaleido' in cmdline or 'plotly' in cmdline:
                        is_browser = True

                    if is_browser:
                        browser_processes.append(proc)
                        self.logger.debug(f"🔍 Found browser process: {proc.pid} - {proc_name}")

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue
                    
        except Exception as e:
            self.logger.error(f"❌ Error finding browser processes: {e}")
        
        return browser_processes
    
    def cleanup_browser_process(self, process: psutil.Process, timeout: int = 5) -> bool:
        """清理单个浏览器进程"""
        try:
            if not process.is_running():
                return True
            
            self.logger.debug(f"🧹 Cleaning up browser process: {process.pid}")
            
            # 尝试优雅关闭
            try:
                process.terminate()
                process.wait(timeout=timeout)
                self.logger.debug(f"✅ Browser process {process.pid} terminated gracefully")
                return True
            except psutil.TimeoutExpired:
                # 强制关闭
                self.logger.warning(f"⚠️ Force killing browser process: {process.pid}")
                process.kill()
                process.wait(timeout=2)
                return True
                
        except (psutil.NoSuchProcess, psutil.AccessDenied):
            # 进程已经不存在或无权限
            return True
        except Exception as e:
            self.logger.error(f"❌ Failed to cleanup browser process {process.pid}: {e}")
            return False
    
    def cleanup_all_browsers(self):
        """清理所有浏览器进程"""
        self.logger.info("🧹 Starting browser cleanup...")
        
        # 清理跟踪的进程
        with self._lock:
            tracked_processes = self.browser_processes.copy()
            self.browser_processes.clear()
        
        # 清理跟踪的进程
        for process in tracked_processes:
            self.cleanup_browser_process(process)
        
        # 查找并清理其他浏览器进程
        found_processes = self.find_browser_processes()
        for process in found_processes:
            self.cleanup_browser_process(process)
        
        self.logger.info("✅ Browser cleanup completed")
    
    def cleanup_kaleido_processes(self):
        """专门清理kaleido相关进程"""
        self.logger.info("🧹 Cleaning up kaleido processes...")

        try:
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    proc_info = proc.info
                    cmdline_list = proc_info.get('cmdline', [])

                    # 安全地处理cmdline
                    if isinstance(cmdline_list, list):
                        cmdline = ' '.join(cmdline_list).lower()
                    else:
                        cmdline = str(cmdline_list).lower() if cmdline_list else ''

                    if 'kaleido' in cmdline or 'plotly' in cmdline:
                        self.logger.debug(f"🔍 Found kaleido process: {proc.pid}")
                        self.cleanup_browser_process(proc)

                except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
                    continue

        except Exception as e:
            self.logger.error(f"❌ Error cleaning kaleido processes: {e}")


# 全局浏览器进程管理器
_browser_manager = None


def get_browser_manager() -> BrowserProcessManager:
    """获取全局浏览器进程管理器"""
    global _browser_manager
    if _browser_manager is None:
        _browser_manager = BrowserProcessManager()
    return _browser_manager


def setup_browser_cleanup():
    """设置浏览器清理"""
    manager = get_browser_manager()

    # 不注册信号处理器，避免与主程序冲突
    # 改为使用atexit进行清理
    import atexit
    atexit.register(manager.cleanup_all_browsers)

    logging.getLogger(__name__).info("✅ Browser cleanup setup completed (using atexit)")


def cleanup_browsers_on_exit():
    """退出时清理浏览器"""
    manager = get_browser_manager()
    manager.cleanup_all_browsers()


def patch_plotly_kaleido():
    """修补plotly的kaleido使用，确保进程正确清理"""
    try:
        import plotly.io as pio
        
        # 保存原始的write_image方法
        original_write_image = pio.write_image
        
        def patched_write_image(*args, **kwargs):
            """修补的write_image方法"""
            manager = get_browser_manager()
            
            try:
                # 执行原始方法
                result = original_write_image(*args, **kwargs)
                
                # 清理kaleido进程
                manager.cleanup_kaleido_processes()
                
                return result
            except Exception as e:
                # 即使出错也要清理
                manager.cleanup_kaleido_processes()
                raise e
        
        # 替换方法
        pio.write_image = patched_write_image
        
        logging.getLogger(__name__).info("✅ Plotly kaleido patched for cleanup")
        
    except ImportError:
        logging.getLogger(__name__).debug("📝 Plotly not available, skipping patch")
    except Exception as e:
        logging.getLogger(__name__).error(f"❌ Failed to patch plotly: {e}")


def initialize_browser_cleanup():
    """初始化浏览器清理"""
    setup_browser_cleanup()
    patch_plotly_kaleido()
    
    logging.getLogger(__name__).info("🚀 Browser cleanup initialized")


if __name__ == "__main__":
    # 测试脚本
    logging.basicConfig(level=logging.INFO)
    
    manager = get_browser_manager()
    
    print("🔍 Finding browser processes...")
    processes = manager.find_browser_processes()
    
    if processes:
        print(f"Found {len(processes)} browser processes:")
        for proc in processes:
            try:
                print(f"  - PID: {proc.pid}, Name: {proc.name()}")
            except:
                print(f"  - PID: {proc.pid}, Name: <unknown>")
        
        print("\n🧹 Cleaning up...")
        manager.cleanup_all_browsers()
    else:
        print("No browser processes found")
