"""
统一异常处理工具
提供一致的异常处理策略和错误恢复机制
"""

import asyncio
import functools
import logging
import traceback
import time
from typing import Any, Callable, Dict, Optional, Type, Union, List
from dataclasses import dataclass
from enum import Enum
import sys


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class RetryConfig:
    """重试配置"""
    max_retries: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True


@dataclass
class ErrorContext:
    """错误上下文信息"""
    function_name: str
    module_name: str
    error_type: str
    error_message: str
    severity: ErrorSeverity
    timestamp: float
    traceback_info: str
    retry_count: int = 0


class ExceptionHandler:
    """统一异常处理器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.error_stats: Dict[str, int] = {}
        self.circuit_breakers: Dict[str, Dict[str, Any]] = {}
    
    def handle_exception(
        self,
        error: Exception,
        context: str = "",
        severity: ErrorSeverity = ErrorSeverity.MEDIUM,
        reraise: bool = True
    ) -> Optional[Any]:
        """
        处理异常
        
        Args:
            error: 异常对象
            context: 错误上下文
            severity: 错误严重程度
            reraise: 是否重新抛出异常
        """
        error_type = type(error).__name__
        error_msg = str(error)
        
        # 记录错误统计
        self.error_stats[error_type] = self.error_stats.get(error_type, 0) + 1
        
        # 构建错误上下文
        frame = sys._getframe(1) if hasattr(sys, '_getframe') else None
        function_name = frame.f_code.co_name if frame else "unknown"
        module_name = frame.f_globals.get('__name__', 'unknown') if frame else "unknown"
        
        error_context = ErrorContext(
            function_name=function_name,
            module_name=module_name,
            error_type=error_type,
            error_message=error_msg,
            severity=severity,
            timestamp=time.time(),
            traceback_info=traceback.format_exc()
        )
        
        # 记录日志
        self._log_error(error_context, context)
        
        # 根据严重程度决定处理策略
        if severity == ErrorSeverity.CRITICAL:
            self.logger.critical(f"Critical error in {context}: {error_msg}")
            if reraise:
                raise error
        elif severity == ErrorSeverity.HIGH:
            self.logger.error(f"High severity error in {context}: {error_msg}")
            if reraise:
                raise error
        elif severity == ErrorSeverity.MEDIUM:
            self.logger.warning(f"Medium severity error in {context}: {error_msg}")
            if reraise:
                raise error
        else:
            self.logger.info(f"Low severity error in {context}: {error_msg}")
        
        return None
    
    def _log_error(self, error_context: ErrorContext, context: str):
        """记录错误日志"""
        log_msg = (
            f"Exception in {error_context.module_name}.{error_context.function_name}: "
            f"{error_context.error_type}: {error_context.error_message}"
        )
        
        if context:
            log_msg = f"{context} - {log_msg}"
        
        if error_context.severity == ErrorSeverity.CRITICAL:
            self.logger.critical(log_msg)
            self.logger.debug(f"Traceback: {error_context.traceback_info}")
        elif error_context.severity == ErrorSeverity.HIGH:
            self.logger.error(log_msg)
            self.logger.debug(f"Traceback: {error_context.traceback_info}")
        elif error_context.severity == ErrorSeverity.MEDIUM:
            self.logger.warning(log_msg)
        else:
            self.logger.info(log_msg)
    
    def with_retry(
        self,
        retry_config: Optional[RetryConfig] = None,
        exceptions: tuple = (Exception,),
        context: str = ""
    ):
        """
        重试装饰器
        
        Args:
            retry_config: 重试配置
            exceptions: 需要重试的异常类型
            context: 上下文信息
        """
        config = retry_config or RetryConfig()
        
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                last_exception = None
                
                for attempt in range(config.max_retries + 1):
                    try:
                        return func(*args, **kwargs)
                    except exceptions as e:
                        last_exception = e
                        
                        if attempt == config.max_retries:
                            self.handle_exception(
                                e,
                                f"{context} - Final attempt failed",
                                ErrorSeverity.HIGH
                            )
                            raise e
                        
                        # 计算延迟时间
                        delay = min(
                            config.base_delay * (config.exponential_base ** attempt),
                            config.max_delay
                        )
                        
                        if config.jitter:
                            import random
                            delay *= (0.5 + random.random() * 0.5)
                        
                        self.logger.warning(
                            f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                            f"Retrying in {delay:.2f}s..."
                        )
                        
                        time.sleep(delay)
                
                # 这里不应该到达，但为了类型安全
                if last_exception:
                    raise last_exception
            
            return wrapper
        return decorator
    
    def with_async_retry(
        self,
        retry_config: Optional[RetryConfig] = None,
        exceptions: tuple = (Exception,),
        context: str = ""
    ):
        """
        异步重试装饰器
        """
        config = retry_config or RetryConfig()
        
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                last_exception = None
                
                for attempt in range(config.max_retries + 1):
                    try:
                        return await func(*args, **kwargs)
                    except exceptions as e:
                        last_exception = e
                        
                        if attempt == config.max_retries:
                            self.handle_exception(
                                e,
                                f"{context} - Final attempt failed",
                                ErrorSeverity.HIGH
                            )
                            raise e
                        
                        # 计算延迟时间
                        delay = min(
                            config.base_delay * (config.exponential_base ** attempt),
                            config.max_delay
                        )
                        
                        if config.jitter:
                            import random
                            delay *= (0.5 + random.random() * 0.5)
                        
                        self.logger.warning(
                            f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                            f"Retrying in {delay:.2f}s..."
                        )
                        
                        await asyncio.sleep(delay)
                
                # 这里不应该到达，但为了类型安全
                if last_exception:
                    raise last_exception
            
            return wrapper
        return decorator
    
    def get_error_stats(self) -> Dict[str, int]:
        """获取错误统计"""
        return self.error_stats.copy()
    
    def reset_error_stats(self):
        """重置错误统计"""
        self.error_stats.clear()


# 全局异常处理器实例
_global_exception_handler = None


def get_exception_handler(logger: Optional[logging.Logger] = None) -> ExceptionHandler:
    """获取全局异常处理器实例"""
    global _global_exception_handler
    if _global_exception_handler is None:
        _global_exception_handler = ExceptionHandler(logger)
    return _global_exception_handler


def handle_exception(
    error: Exception,
    context: str = "",
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    reraise: bool = True
) -> Optional[Any]:
    """便捷的异常处理函数"""
    handler = get_exception_handler()
    return handler.handle_exception(error, context, severity, reraise)


def with_retry(
    retry_config: Optional[RetryConfig] = None,
    exceptions: tuple = (Exception,),
    context: str = ""
):
    """便捷的重试装饰器"""
    handler = get_exception_handler()
    return handler.with_retry(retry_config, exceptions, context)


def with_async_retry(
    retry_config: Optional[RetryConfig] = None,
    exceptions: tuple = (Exception,),
    context: str = ""
):
    """便捷的异步重试装饰器"""
    handler = get_exception_handler()
    return handler.with_async_retry(retry_config, exceptions, context)
