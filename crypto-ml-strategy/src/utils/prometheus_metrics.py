"""
Prometheus监控指标
为Python端提供监控指标暴露
"""

import time
import threading
import logging
from typing import Dict, Any, Optional
from prometheus_client import Counter, Histogram, Gauge, start_http_server, CollectorRegistry, REGISTRY
import torch
import psutil
import os


class PrometheusMetrics:
    """Prometheus指标收集器"""
    
    def __init__(self, port: int = 9102, registry: Optional[CollectorRegistry] = None):
        """
        初始化Prometheus指标
        
        Args:
            port: HTTP服务端口
            registry: 指标注册表
        """
        self.port = port
        self.registry = registry or REGISTRY
        self.logger = logging.getLogger(__name__)
        self.server_started = False
        
        # 定义指标
        self._init_metrics()
        
        # 启动后台监控线程
        self._start_monitoring_thread()
    
    def _init_metrics(self):
        """初始化所有指标"""
        # 训练相关指标
        self.training_loss = Gauge(
            'crypto_ml_training_loss',
            'Current training loss',
            registry=self.registry
        )
        
        self.training_accuracy = Gauge(
            'crypto_ml_training_accuracy',
            'Current training accuracy',
            registry=self.registry
        )
        
        self.training_epochs_total = Counter(
            'crypto_ml_training_epochs_total',
            'Total number of training epochs',
            registry=self.registry
        )
        
        self.training_batch_duration = Histogram(
            'crypto_ml_training_batch_duration_seconds',
            'Time spent processing training batches',
            registry=self.registry
        )
        
        # GPU相关指标
        if torch.cuda.is_available():
            self.gpu_memory_allocated = Gauge(
                'crypto_ml_gpu_memory_allocated_bytes',
                'GPU memory allocated',
                registry=self.registry
            )
            
            self.gpu_memory_reserved = Gauge(
                'crypto_ml_gpu_memory_reserved_bytes',
                'GPU memory reserved',
                registry=self.registry
            )
            
            self.gpu_utilization = Gauge(
                'crypto_ml_gpu_utilization_percent',
                'GPU utilization percentage',
                registry=self.registry
            )
        
        # 系统指标
        self.cpu_usage = Gauge(
            'crypto_ml_cpu_usage_percent',
            'CPU usage percentage',
            registry=self.registry
        )
        
        self.memory_usage = Gauge(
            'crypto_ml_memory_usage_bytes',
            'Memory usage in bytes',
            registry=self.registry
        )
        
        # 数据处理指标
        self.data_loading_duration = Histogram(
            'crypto_ml_data_loading_duration_seconds',
            'Time spent loading data',
            registry=self.registry
        )
        
        self.cache_hits_total = Counter(
            'crypto_ml_cache_hits_total',
            'Total cache hits',
            ['cache_type'],
            registry=self.registry
        )
        
        self.cache_misses_total = Counter(
            'crypto_ml_cache_misses_total',
            'Total cache misses',
            ['cache_type'],
            registry=self.registry
        )
        
        # DeepSeek API指标
        self.deepseek_api_requests_total = Counter(
            'crypto_ml_deepseek_api_requests_total',
            'Total DeepSeek API requests',
            ['status'],
            registry=self.registry
        )
        
        self.deepseek_api_duration = Histogram(
            'crypto_ml_deepseek_api_duration_seconds',
            'DeepSeek API request duration',
            registry=self.registry
        )
    
    def start_http_server(self):
        """启动HTTP服务器"""
        if not self.server_started:
            try:
                start_http_server(self.port, registry=self.registry)
                self.server_started = True
                self.logger.info(f"✅ Prometheus metrics server started on port {self.port}")
                self.logger.info(f"📊 Metrics endpoint: http://localhost:{self.port}/metrics")
            except Exception as e:
                self.logger.error(f"❌ Failed to start Prometheus server: {e}")
    
    def _start_monitoring_thread(self):
        """启动后台监控线程"""
        def monitor_system():
            while True:
                try:
                    self._update_system_metrics()
                    time.sleep(10)  # 每10秒更新一次
                except Exception as e:
                    self.logger.error(f"System monitoring error: {e}")
                    time.sleep(30)  # 出错时等待30秒
        
        thread = threading.Thread(target=monitor_system, daemon=True)
        thread.start()
        self.logger.info("🔄 System monitoring thread started")
    
    def _update_system_metrics(self):
        """更新系统指标"""
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        self.cpu_usage.set(cpu_percent)
        
        # 内存使用
        memory = psutil.virtual_memory()
        self.memory_usage.set(memory.used)
        
        # GPU指标
        if torch.cuda.is_available():
            try:
                self.gpu_memory_allocated.set(torch.cuda.memory_allocated())
                self.gpu_memory_reserved.set(torch.cuda.memory_reserved())
                
                # GPU利用率（需要nvidia-ml-py库，这里用内存使用率近似）
                total_memory = torch.cuda.get_device_properties(0).total_memory
                used_memory = torch.cuda.memory_allocated()
                gpu_util = (used_memory / total_memory) * 100
                self.gpu_utilization.set(gpu_util)
                
            except Exception as e:
                self.logger.debug(f"GPU metrics update failed: {e}")
    
    # 便捷方法
    def record_training_loss(self, loss: float):
        """记录训练损失"""
        self.training_loss.set(loss)
    
    def record_training_accuracy(self, accuracy: float):
        """记录训练准确率"""
        self.training_accuracy.set(accuracy)
    
    def increment_training_epochs(self):
        """增加训练轮数"""
        self.training_epochs_total.inc()
    
    def record_batch_duration(self, duration: float):
        """记录批次处理时间"""
        self.training_batch_duration.observe(duration)
    
    def record_data_loading_duration(self, duration: float):
        """记录数据加载时间"""
        self.data_loading_duration.observe(duration)
    
    def record_cache_hit(self, cache_type: str = 'default'):
        """记录缓存命中"""
        self.cache_hits_total.labels(cache_type=cache_type).inc()
    
    def record_cache_miss(self, cache_type: str = 'default'):
        """记录缓存未命中"""
        self.cache_misses_total.labels(cache_type=cache_type).inc()
    
    def record_deepseek_request(self, status: str, duration: Optional[float] = None):
        """记录DeepSeek API请求"""
        self.deepseek_api_requests_total.labels(status=status).inc()
        if duration is not None:
            self.deepseek_api_duration.observe(duration)


# 全局实例
_metrics_instance = None

def get_metrics() -> PrometheusMetrics:
    """获取全局指标实例"""
    global _metrics_instance
    if _metrics_instance is None:
        _metrics_instance = PrometheusMetrics()
    return _metrics_instance

def start_metrics_server(port: int = 9102):
    """启动指标服务器"""
    metrics = get_metrics()
    metrics.port = port
    metrics.start_http_server()
    return metrics
