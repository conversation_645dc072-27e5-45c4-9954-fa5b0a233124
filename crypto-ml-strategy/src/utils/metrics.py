"""
指标计算模块
提供各种机器学习和金融指标的计算功能
"""

import numpy as np
import pandas as pd
import torch
from typing import Dict, List, Optional, Tuple, Union, Any
from sklearn.metrics import (
    accuracy_score, precision_score, recall_score, f1_score,
    confusion_matrix, classification_report, roc_auc_score,
    mean_squared_error, mean_absolute_error, r2_score
)
import logging


def calculate_metrics(
    y_true: Union[np.ndarray, List],
    y_pred: Union[np.ndarray, List],
    y_prob: Optional[Union[np.ndarray, List]] = None,
    task_type: str = 'classification'
) -> Dict[str, float]:
    """
    计算各种评估指标
    
    Args:
        y_true: 真实标签
        y_pred: 预测标签
        y_prob: 预测概率 (可选)
        task_type: 任务类型 ('classification' 或 'regression')
        
    Returns:
        指标字典
    """
    # 转换为numpy数组 (简化版，不支持torch.Tensor)
    # torch相关代码已移除
    
    y_true = np.array(y_true)
    y_pred = np.array(y_pred)
    
    metrics = {}
    
    if task_type == 'classification':
        # 分类指标
        metrics['accuracy'] = accuracy_score(y_true, y_pred)
        metrics['precision'] = precision_score(y_true, y_pred, average='weighted', zero_division=0)
        metrics['recall'] = recall_score(y_true, y_pred, average='weighted', zero_division=0)
        metrics['f1_score'] = f1_score(y_true, y_pred, average='weighted', zero_division=0)
        
        # 如果有概率预测，计算AUC
        if y_prob is not None:
            try:
                if len(np.unique(y_true)) == 2:  # 二分类
                    metrics['auc'] = roc_auc_score(y_true, y_prob[:, 1] if y_prob.ndim > 1 else y_prob)
                else:  # 多分类
                    metrics['auc'] = roc_auc_score(y_true, y_prob, multi_class='ovr', average='weighted')
            except Exception:
                metrics['auc'] = 0.0
        
        # 混淆矩阵相关指标
        cm = confusion_matrix(y_true, y_pred)
        if cm.shape[0] == cm.shape[1]:  # 确保是方阵
            metrics['confusion_matrix'] = cm.tolist()
            
            # 计算每类的精确率和召回率
            for i in range(cm.shape[0]):
                if cm[i, :].sum() > 0:  # 避免除零
                    metrics[f'precision_class_{i}'] = cm[i, i] / cm[i, :].sum()
                if cm[:, i].sum() > 0:
                    metrics[f'recall_class_{i}'] = cm[i, i] / cm[:, i].sum()
    
    elif task_type == 'regression':
        # 回归指标
        metrics['mse'] = mean_squared_error(y_true, y_pred)
        metrics['rmse'] = np.sqrt(metrics['mse'])
        metrics['mae'] = mean_absolute_error(y_true, y_pred)
        metrics['r2'] = r2_score(y_true, y_pred)
        
        # 平均绝对百分比误差
        mask = y_true != 0
        if mask.any():
            metrics['mape'] = np.mean(np.abs((y_true[mask] - y_pred[mask]) / y_true[mask])) * 100
        else:
            metrics['mape'] = float('inf')
    
    return metrics


def calculate_financial_metrics(
    returns: Union[np.ndarray, pd.Series],
    benchmark_returns: Optional[Union[np.ndarray, pd.Series]] = None,
    risk_free_rate: float = 0.02
) -> Dict[str, float]:
    """
    计算金融指标
    
    Args:
        returns: 收益率序列
        benchmark_returns: 基准收益率序列
        risk_free_rate: 无风险利率 (年化)
        
    Returns:
        金融指标字典
    """
    if isinstance(returns, pd.Series):
        returns = returns.values
    returns = np.array(returns)
    
    # 基础统计
    metrics = {
        'total_return': np.prod(1 + returns) - 1,
        'annualized_return': np.mean(returns) * 252,
        'volatility': np.std(returns) * np.sqrt(252),
        'skewness': pd.Series(returns).skew(),
        'kurtosis': pd.Series(returns).kurtosis()
    }
    
    # 风险调整收益指标
    daily_rf = risk_free_rate / 252
    excess_returns = returns - daily_rf
    
    if metrics['volatility'] > 0:
        metrics['sharpe_ratio'] = np.mean(excess_returns) * np.sqrt(252) / metrics['volatility']
    else:
        metrics['sharpe_ratio'] = 0.0
    
    # Sortino比率
    downside_returns = returns[returns < 0]
    if len(downside_returns) > 0:
        downside_deviation = np.std(downside_returns) * np.sqrt(252)
        if downside_deviation > 0:
            metrics['sortino_ratio'] = (metrics['annualized_return'] - risk_free_rate) / downside_deviation
        else:
            metrics['sortino_ratio'] = 0.0
    else:
        metrics['sortino_ratio'] = float('inf')
    
    # 最大回撤
    cumulative_returns = np.cumprod(1 + returns)
    peak = np.maximum.accumulate(cumulative_returns)
    drawdown = (cumulative_returns - peak) / peak
    metrics['max_drawdown'] = np.min(drawdown)
    
    # Calmar比率
    if abs(metrics['max_drawdown']) > 0:
        metrics['calmar_ratio'] = metrics['annualized_return'] / abs(metrics['max_drawdown'])
    else:
        metrics['calmar_ratio'] = float('inf')
    
    # 如果有基准收益率，计算相对指标
    if benchmark_returns is not None:
        if isinstance(benchmark_returns, pd.Series):
            benchmark_returns = benchmark_returns.values
        benchmark_returns = np.array(benchmark_returns)
        
        if len(benchmark_returns) == len(returns):
            # Beta
            covariance = np.cov(returns, benchmark_returns)[0, 1]
            benchmark_variance = np.var(benchmark_returns)
            if benchmark_variance > 0:
                metrics['beta'] = covariance / benchmark_variance
            else:
                metrics['beta'] = 0.0
            
            # Alpha
            benchmark_return = np.mean(benchmark_returns) * 252
            metrics['alpha'] = metrics['annualized_return'] - (risk_free_rate + metrics['beta'] * (benchmark_return - risk_free_rate))
            
            # 信息比率
            active_returns = returns - benchmark_returns
            tracking_error = np.std(active_returns) * np.sqrt(252)
            if tracking_error > 0:
                metrics['information_ratio'] = np.mean(active_returns) * np.sqrt(252) / tracking_error
                metrics['tracking_error'] = tracking_error
            else:
                metrics['information_ratio'] = 0.0
                metrics['tracking_error'] = 0.0
    
    return metrics


def calculate_trading_metrics(
    signals: Union[np.ndarray, List],
    prices: Union[np.ndarray, pd.Series],
    transaction_cost: float = 0.001
) -> Dict[str, float]:
    """
    计算交易指标
    
    Args:
        signals: 交易信号 (1: 买入, 0: 持有, -1: 卖出)
        prices: 价格序列
        transaction_cost: 交易成本 (比例)
        
    Returns:
        交易指标字典
    """
    signals = np.array(signals)
    prices = np.array(prices)
    
    # 计算持仓
    positions = np.zeros_like(signals)
    current_position = 0
    
    for i, signal in enumerate(signals):
        if signal == 1:  # 买入信号
            current_position = 1
        elif signal == -1:  # 卖出信号
            current_position = 0
        positions[i] = current_position
    
    # 计算收益
    price_returns = np.diff(prices) / prices[:-1]
    strategy_returns = positions[:-1] * price_returns
    
    # 考虑交易成本
    position_changes = np.diff(np.concatenate([[0], positions]))
    trade_costs = np.abs(position_changes) * transaction_cost
    strategy_returns -= trade_costs[1:]  # 第一个交易成本在第二个时间点生效
    
    # 计算指标
    metrics = {
        'total_trades': np.sum(np.abs(position_changes)),
        'win_rate': 0.0,
        'avg_win': 0.0,
        'avg_loss': 0.0,
        'profit_factor': 0.0,
        'max_consecutive_wins': 0,
        'max_consecutive_losses': 0
    }
    
    if len(strategy_returns) > 0:
        # 胜率和平均盈亏
        winning_trades = strategy_returns[strategy_returns > 0]
        losing_trades = strategy_returns[strategy_returns < 0]
        
        if len(strategy_returns) > 0:
            metrics['win_rate'] = len(winning_trades) / len(strategy_returns)
        
        if len(winning_trades) > 0:
            metrics['avg_win'] = np.mean(winning_trades)
        
        if len(losing_trades) > 0:
            metrics['avg_loss'] = np.mean(losing_trades)
            
            # 盈亏比
            if abs(metrics['avg_loss']) > 0:
                metrics['profit_factor'] = metrics['avg_win'] / abs(metrics['avg_loss'])
        
        # 连续盈亏
        consecutive_wins = 0
        consecutive_losses = 0
        max_wins = 0
        max_losses = 0
        
        for ret in strategy_returns:
            if ret > 0:
                consecutive_wins += 1
                consecutive_losses = 0
                max_wins = max(max_wins, consecutive_wins)
            elif ret < 0:
                consecutive_losses += 1
                consecutive_wins = 0
                max_losses = max(max_losses, consecutive_losses)
            else:
                consecutive_wins = 0
                consecutive_losses = 0
        
        metrics['max_consecutive_wins'] = max_wins
        metrics['max_consecutive_losses'] = max_losses
    
    return metrics


def calculate_model_performance_metrics(
    train_metrics: Dict[str, float],
    val_metrics: Dict[str, float],
    test_metrics: Optional[Dict[str, float]] = None
) -> Dict[str, Any]:
    """
    计算模型性能综合指标
    
    Args:
        train_metrics: 训练集指标
        val_metrics: 验证集指标
        test_metrics: 测试集指标 (可选)
        
    Returns:
        综合性能指标
    """
    performance = {
        'train_metrics': train_metrics,
        'val_metrics': val_metrics,
        'overfitting_score': 0.0,
        'generalization_score': 0.0,
        'overall_score': 0.0
    }
    
    if test_metrics:
        performance['test_metrics'] = test_metrics
    
    # 过拟合评分 (训练集和验证集性能差异)
    if 'accuracy' in train_metrics and 'accuracy' in val_metrics:
        performance['overfitting_score'] = train_metrics['accuracy'] - val_metrics['accuracy']
    elif 'f1_score' in train_metrics and 'f1_score' in val_metrics:
        performance['overfitting_score'] = train_metrics['f1_score'] - val_metrics['f1_score']
    
    # 泛化能力评分 (验证集性能)
    if 'accuracy' in val_metrics:
        performance['generalization_score'] = val_metrics['accuracy']
    elif 'f1_score' in val_metrics:
        performance['generalization_score'] = val_metrics['f1_score']
    
    # 综合评分 (考虑性能和过拟合)
    performance['overall_score'] = performance['generalization_score'] - abs(performance['overfitting_score'])
    
    return performance


class MetricsTracker:
    """指标跟踪器"""
    
    def __init__(self):
        self.metrics_history = []
        self.logger = logging.getLogger(__name__)
    
    def add_metrics(self, metrics: Dict[str, float], epoch: Optional[int] = None, phase: str = 'train'):
        """添加指标"""
        entry = {
            'epoch': epoch,
            'phase': phase,
            'timestamp': pd.Timestamp.now(),
            'metrics': metrics.copy()
        }
        self.metrics_history.append(entry)
        self.logger.debug(f"Added metrics for epoch {epoch}, phase {phase}")
    
    def get_best_metrics(self, metric_name: str, phase: str = 'val') -> Dict[str, Any]:
        """获取最佳指标"""
        phase_metrics = [entry for entry in self.metrics_history if entry['phase'] == phase]
        
        if not phase_metrics:
            return {}
        
        if metric_name in ['loss', 'mse', 'mae']:  # 越小越好的指标
            best_entry = min(phase_metrics, key=lambda x: x['metrics'].get(metric_name, float('inf')))
        else:  # 越大越好的指标
            best_entry = max(phase_metrics, key=lambda x: x['metrics'].get(metric_name, 0))
        
        return best_entry
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        if not self.metrics_history:
            return {}
        
        summary = {
            'total_entries': len(self.metrics_history),
            'phases': list(set(entry['phase'] for entry in self.metrics_history)),
            'epochs': list(set(entry['epoch'] for entry in self.metrics_history if entry['epoch'] is not None))
        }
        
        # 获取最新指标
        latest_entry = self.metrics_history[-1]
        summary['latest_metrics'] = latest_entry['metrics']
        summary['latest_epoch'] = latest_entry['epoch']
        summary['latest_phase'] = latest_entry['phase']
        
        return summary
    
    def export_to_dataframe(self) -> pd.DataFrame:
        """导出为DataFrame"""
        if not self.metrics_history:
            return pd.DataFrame()
        
        rows = []
        for entry in self.metrics_history:
            row = {
                'epoch': entry['epoch'],
                'phase': entry['phase'],
                'timestamp': entry['timestamp']
            }
            row.update(entry['metrics'])
            rows.append(row)
        
        return pd.DataFrame(rows)
    
    def clear(self):
        """清空历史记录"""
        self.metrics_history.clear()
        self.logger.info("Metrics history cleared")
