"""
统一配置管理工具 (Unified Configuration Management Tool)
"""

import os
import yaml
import json
from typing import Dict, Any, Optional, Union
from pathlib import Path
import logging
from dataclasses import dataclass, asdict
import re

# --- Data Classes for Typed Configuration ---

@dataclass
class DatabaseConfig:
    """数据库配置"""
    enabled: bool = False
    host: str = "localhost"
    port: int = 3306
    database: str = "crypto_ml_strategy"
    username: str = "root"
    password: str = ""
    pool_size: int = 10
    max_overflow: int = 20

@dataclass
class RedisConfig:
    """Redis配置"""
    enabled: bool = False
    host: str = "localhost"
    port: int = 16379
    db: int = 0
    password: Optional[str] = None
    max_connections: int = 50

@dataclass
class ModelConfig:
    """模型配置"""
    name: str = "UnifiedSignalFusionModel"
    feature_dim: int = 59
    hidden_dims: list = None
    num_heads: int = 8
    num_layers: int = 6
    dropout: float = 0.1
    sequence_length: int = 20
    output_dim: int = 3
    device: str = "auto"
    use_gradient_checkpointing: bool = False

    def __post_init__(self):
        if self.hidden_dims is None:
            self.hidden_dims = [256, 128, 64]

@dataclass
class TrainingConfig:
    """训练配置"""
    epochs: int = 10
    learning_rate: float = 0.001
    weight_decay: float = 0.0001
    optimizer: str = "adamw"
    scheduler: str = "cosine"
    grad_clip_norm: float = 1.0
    batch_size: int = 32

# --- Main ConfigManager Class ---

class ConfigManager:
    """
    统一配置管理器 (Unified Configuration Manager)

    加载顺序 (优先级从高到低):
    1. 环境变量 (e.g., CRYPTO__MODEL__DEVICE=cpu)
    2. 本地配置文件 (config/config.local.yaml) - 用于本地覆盖，不提交到git
    3. 主配置文件 (config/config.yaml)
    """

    def __init__(self, config_dir: Optional[Path] = None):
        """
        Initializes the ConfigManager.
        It finds the project root and loads configuration from YAML files and environment variables.
        """
        self.config_data = {}
        self.logger = logging.getLogger(__name__)

        if config_dir:
            self.project_root = config_dir.parent
            self.config_dir = config_dir
        else:
            # 自动发现项目根目录（包含 'pyproject.toml' 或 '.git' 的目录）
            self.project_root = self._find_project_root()
            self.config_dir = self.project_root / "config"

        self.logger.info(f"✅ Project root found at: {self.project_root}")
        self.logger.info(f"✅ Loading configuration from: {self.config_dir}")
        
        self._load_config()

    def _load_config(self):
        """执行完整的配置加载流程。"""
        # 依次加载基础配置、本地覆盖配置，最后是环境变量
        self._load_yaml_file(self.config_dir / "config.yaml")
        self._load_yaml_file(self.config_dir / "config.local.yaml")
        self._resolve_environment_references()
        self._load_environment_variables()

    def _find_project_root(self, current_path: Path = None) -> Path:
        """向上查找项目根目录。"""
        if current_path is None:
            current_path = Path.cwd()
        
        if (current_path / 'pyproject.toml').exists() or \
           (current_path / '.git').exists() or \
           (current_path / 'config').is_dir():
            return current_path
        
        if current_path.parent == current_path:
            raise FileNotFoundError("无法自动定位项目根目录。请确保项目中包含 'pyproject.toml', '.git' 或 'config' 文件夹。")
            
        return self._find_project_root(current_path.parent)

    def _load_yaml_file(self, file_path: Path):
        """加载单个YAML文件并合并到配置中。"""
        if file_path.exists():
            self.logger.info(f"🔄 Loading config file: {file_path}")
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = yaml.safe_load(f)
                if data:
                    self._deep_merge(self.config_data, data)
            except Exception as e:
                self.logger.error(f"❌ Failed to load or parse YAML file {file_path}: {e}")
        else:
            self.logger.debug(f"Config file not found, skipping: {file_path}")



    def _load_environment_variables(self):
        """
        从环境变量加载配置。
        使用 'CRYPTO__' 作为前缀, '__' 作为层级分隔符。
        例如: CRYPTO__MODEL__DEVICE=cpu 会覆盖 model.device 的值。
        """
        prefix = "CRYPTO__"
        loaded_count = 0
        for env_var, value in os.environ.items():
            if env_var.startswith(prefix):
                path = env_var[len(prefix):].replace("__", ".").lower()
                self._set_nested_value(self.config_data, path, value)
                loaded_count += 1
        
        if loaded_count > 0:
            self.logger.info(f"✅ Loaded and merged {loaded_count} settings from environment variables.")

    def _resolve_environment_references(self):
        """递归解析配置中的环境变量引用, e.g., ${VAR_NAME:default}."""
        def resolve_value(value):
            if isinstance(value, str):
                pattern = r'\$\{([^}:]+)(?::([^}]*))?\}'
                
                def replace_env_var(match):
                    var_name = match.group(1)
                    default_value = match.group(2) if match.group(2) is not None else ''
                    return os.getenv(var_name, default_value)
                
                return re.sub(pattern, replace_env_var, value)
            elif isinstance(value, dict):
                return {k: resolve_value(v) for k, v in value.items()}
            elif isinstance(value, list):
                return [resolve_value(item) for item in value]
            else:
                return value
        
        self.config_data = resolve_value(self.config_data)

    def _deep_merge(self, base_dict: dict, update_dict: dict):
        """深度合并字典。"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict.get(key), dict) and isinstance(value, dict):
                self._deep_merge(base_dict[key], value)
            else:
                base_dict[key] = value

    def _set_nested_value(self, data: dict, path: str, value: Any):
        """根据点分隔的路径设置嵌套字典值。"""
        keys = path.split('.')
        current = data
        for key in keys[:-1]:
            current = current.setdefault(key, {})
        current[keys[-1]] = self._convert_type(value)

    def _convert_type(self, value: str) -> Any:
        """尝试将字符串值转换为适当的类型。"""
        if not isinstance(value, str):
            return value
        
        val_lower = value.lower()
        if val_lower in ('true', 'false'):
            return val_lower == 'true'
        
        try:
            if '.' in value:
                return float(value)
            return int(value)
        except ValueError:
            return value

    def get(self, path: str, default: Any = None) -> Any:
        """
        使用点表示法获取配置值。
        e.g., 'storage.mysql.host'
        """
        keys = path.split('.')
        current = self.config_data
        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return default

    def set(self, path: str, value: Any):
        """
        使用点表示法设置或覆盖一个配置值。
        主要用于测试或运行时动态调整。
        e.g., 'storage.mysql.host'
        """
        self._set_nested_value(self.config_data, path, value)
        self.logger.info(f"Configuration value set/overridden: {path} = {value}")

    def to_dict(self) -> Dict[str, Any]:
        """返回整个配置的深拷贝字典。"""
        return json.loads(json.dumps(self.config_data))

# --- Singleton Instance ---
_config_manager_instance: Optional[ConfigManager] = None

def get_config_manager(config_dir: Optional[Path] = None) -> "ConfigManager":
    """
    获取ConfigManager的单例实例。
    如果提供了 config_dir，则会创建一个新的实例。
    """
    global _config_manager_instance
    if _config_manager_instance is None or config_dir is not None:
        _config_manager_instance = ConfigManager(config_dir=config_dir)
    return _config_manager_instance

if __name__ == "__main__":
    # Example usage and testing
    logging.basicConfig(level=logging.INFO)
    
    # Set some environment variables for testing
    os.environ['CRYPTO__MODEL__DEVICE'] = 'cpu'
    os.environ['CRYPTO__LOGGING__LEVEL'] = 'DEBUG'
    os.environ['API_KEY_SECRET'] = 'my-secret-key'

    # Create a dummy config structure for testing
    config_dir = Path("./temp_config")
    config_dir.mkdir(exist_ok=True)
    
    (config_dir / "config.yaml").write_text("""
system:
  debug_mode: false
api:
  key: "${API_KEY_SECRET}"
model:
  name: "BaseModel"
  device: "cuda:0"
""")

    (config_dir / "config.local.yaml").write_text("""
system:
  debug_mode: true
storage:
  mysql:
    host: "localhost"
""")

    print("--- Loading Configuration ---")
    manager = ConfigManager(config_dir=config_dir)
    
    print("\n--- Final Merged Configuration ---")
    print(json.dumps(manager.to_dict(), indent=2))

    print("\n--- Testing get() method ---")
    print(f"Model Device (from env): {manager.get('model.device')}")
    print(f"Debug Mode (from local): {manager.get('system.debug_mode')}")
    print(f"MySQL Host (from local): {manager.get('storage.mysql.host')}")
    print(f"API Key (from env reference): {manager.get('api.key')}")
    print(f"Default value test: {manager.get('non.existent.key', 'default')}")

    # Cleanup
    import shutil
    shutil.rmtree(config_dir)