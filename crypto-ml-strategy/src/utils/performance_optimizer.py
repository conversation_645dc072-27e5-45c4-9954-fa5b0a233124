"""
性能优化器
解决数据管道、模型前向、显存占用等性能瓶颈
"""

# 配置Numba以禁用调试输出 - 移除重复调用，由main.py统一配置
# from .numba_config import configure_numba
# configure_numba()  # 注释掉，避免重复配置

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from typing import Dict, Any, Optional, List, Tuple
import logging
import asyncio
import multiprocessing as mp
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import psutil
import gc
import os
from dataclasses import dataclass
from torch.cuda.amp import autocast, GradScaler
from torch.utils.data import DataLoader, IterableDataset
import time

# 尝试导入可选依赖
try:
    import pyarrow as pa
    import pyarrow.parquet as pq
    PYARROW_AVAILABLE = True
except ImportError:
    PYARROW_AVAILABLE = False
    pa = None
    pq = None

# 导入Numba装饰器
try:
    from numba import jit, prange
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    # 创建装饰器的替代品
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

    def prange(x):
        return range(x)


# ==============================================================================
# Migrated from dynamic_batch_optimizer.py
# ==============================================================================
@dataclass
class BatchSizeConfig:
    """批次大小配置"""
    initial_batch_size: int = 4096  # 🔥 基于性能测试的最优批次大小
    min_batch_size: int = 256      # 🔥 提高最小批次大小
    max_batch_size: int = 8192     # 🔥 提高最大批次大小
    target_memory_usage: float = 0.9  # 提高到90%以更好利用GPU内存
    memory_safety_margin: float = 0.05  # 减少安全边际
    adjustment_factor: float = 1.3  # 更温和的调整因子


class DynamicBatchOptimizer:
    """动态批次大小优化器"""
    
    def __init__(self, device: torch.device, config: Optional[BatchSizeConfig] = None):
        if isinstance(device, str):
            self.device = torch.device(device)
        else:
            self.device = device

        self.config = config or BatchSizeConfig()
        self.logger = logging.getLogger(__name__)

        self.current_batch_size = self.config.initial_batch_size
        self.memory_history = []
        self.performance_history = []
        self.last_adjustment_time = 0
        self.adjustment_cooldown = 30

        if self.device.type == 'cuda':
            self.total_memory = torch.cuda.get_device_properties(self.device).total_memory
            self.logger.info(f"🔧 动态批次优化器初始化 - GPU总内存: {self.total_memory / (1024**3):.1f}GB")
        else:
            self.total_memory = 0
            self.logger.warning("⚠️ 非CUDA设备，动态批次优化器功能受限")
    
    def get_memory_stats(self) -> Dict[str, float]:
        if self.device.type != 'cuda':
            return {}
        
        allocated = torch.cuda.memory_allocated(self.device)
        reserved = torch.cuda.memory_reserved(self.device)
        max_allocated = torch.cuda.max_memory_allocated(self.device)
        
        return {
            'allocated_gb': allocated / (1024**3),
            'reserved_gb': reserved / (1024**3),
            'max_allocated_gb': max_allocated / (1024**3),
            'total_gb': self.total_memory / (1024**3),
            'allocated_percent': (allocated / self.total_memory) * 100 if self.total_memory > 0 else 0,
            'reserved_percent': (reserved / self.total_memory) * 100 if self.total_memory > 0 else 0,
            'max_allocated_percent': (max_allocated / self.total_memory) * 100 if self.total_memory > 0 else 0
        }

    def should_adjust_batch_size(self) -> bool:
        current_time = time.time()
        if current_time - self.last_adjustment_time < self.adjustment_cooldown:
            return False
        if len(self.memory_history) < 3:
            return False
        return True
    
    def calculate_optimal_batch_size(self, current_memory_usage: float) -> int:
        target_usage = self.config.target_memory_usage
        safety_margin = self.config.memory_safety_margin
        
        if current_memory_usage < target_usage * 0.6:
            new_batch_size = int(self.current_batch_size * self.config.adjustment_factor)
        elif current_memory_usage > target_usage + safety_margin:
            new_batch_size = int(self.current_batch_size / self.config.adjustment_factor)
        else:
            new_batch_size = self.current_batch_size
        
        new_batch_size = max(self.config.min_batch_size, min(self.config.max_batch_size, new_batch_size))
        return new_batch_size
    
    def update_batch_size(self) -> Tuple[int, bool]:
        if self.device.type != 'cuda':
            return self.current_batch_size, False
        
        memory_stats = self.get_memory_stats()
        current_usage = memory_stats.get('allocated_percent', 0) / 100
        self.memory_history.append(current_usage)
        
        if len(self.memory_history) > 10:
            self.memory_history.pop(0)
        
        if not self.should_adjust_batch_size():
            return self.current_batch_size, False
        
        new_batch_size = self.calculate_optimal_batch_size(current_usage)
        changed = new_batch_size != self.current_batch_size
        
        if changed:
            self.logger.info(f"🔄 批次大小调整: {self.current_batch_size} → {new_batch_size} (内存使用: {current_usage:.1%})")
            self.current_batch_size = new_batch_size
            self.last_adjustment_time = time.time()
            torch.cuda.empty_cache()
        
        return new_batch_size, changed

# ==============================================================================

# 临时替代jit装饰器
def jit(*args, **kwargs):
    def decorator(func):
        return func
    if len(args) == 1 and callable(args[0]):
        return args[0]
    return decorator


# 临时替代cuda模块
class MockCuda:
    @staticmethod
    def is_available():
        return False

cuda = MockCuda()
import cachetools


class OptimizedDataset(IterableDataset):
    """优化的可迭代数据集"""
    
    def __init__(
        self,
        data_path: str,
        sequence_length: int,
        feature_dim: int,
        chunk_size: int = 10000,
        use_mmap: bool = True
    ):
        self.data_path = data_path
        self.sequence_length = sequence_length
        self.feature_dim = feature_dim
        self.chunk_size = chunk_size
        self.use_mmap = use_mmap
        
        # 预计算数据信息
        self._compute_data_info()
    
    def _compute_data_info(self):
        """预计算数据信息"""
        if self.data_path.endswith('.parquet'):
            # 使用PyArrow读取元数据
            parquet_file = pq.ParquetFile(self.data_path)
            self.total_rows = parquet_file.metadata.num_rows
        else:
            # 快速计算CSV行数
            with open(self.data_path, 'r') as f:
                self.total_rows = sum(1 for _ in f) - 1  # 减去header
    
    def __iter__(self):
        """迭代器实现"""
        if self.data_path.endswith('.parquet'):
            yield from self._iter_parquet()
        else:
            yield from self._iter_csv()
    
    def _iter_parquet(self):
        """Parquet文件迭代"""
        parquet_file = pq.ParquetFile(self.data_path)
        
        for batch in parquet_file.iter_batches(batch_size=self.chunk_size):
            df = batch.to_pandas()
            yield from self._process_chunk(df)
    
    def _iter_csv(self):
        """CSV文件迭代"""
        for chunk in pd.read_csv(self.data_path, chunksize=self.chunk_size):
            yield from self._process_chunk(chunk)
    
    def _process_chunk(self, df: pd.DataFrame):
        """处理数据块"""
        # 提取特征和标签
        feature_cols = [col for col in df.columns if col != 'target']
        features = df[feature_cols].values.astype(np.float32)
        labels = df['target'].values.astype(np.int64)
        
        # 重塑为序列格式
        n_samples = len(features) // self.sequence_length
        if n_samples > 0:
            # 确保特征维度正确
            total_features = features.shape[1]
            if total_features % self.feature_dim == 0:
                seq_features = features[:n_samples * self.sequence_length].reshape(
                    n_samples, self.sequence_length, self.feature_dim
                )
                seq_labels = labels[:n_samples * self.sequence_length:self.sequence_length]
                
                for i in range(n_samples):
                    yield np.array(seq_features[i], dtype=np.float32), np.array([seq_labels[i]], dtype=np.int64)


class VectorizedDataCleaner:
    """向量化数据清洗器"""
    
    @staticmethod
    @jit(nopython=True, cache=True, nogil=True)
    def _detect_outliers_iqr_numba(data: np.ndarray) -> np.ndarray:
        """使用Numba加速的IQR异常值检测"""
        q1 = np.percentile(data, 25)
        q3 = np.percentile(data, 75)
        iqr = q3 - q1
        lower_bound = q1 - 1.5 * iqr
        upper_bound = q3 + 1.5 * iqr
        return (data < lower_bound) | (data > upper_bound)
    
    @staticmethod
    def clean_data_vectorized(df: pd.DataFrame) -> pd.DataFrame:
        """向量化数据清洗"""
        # 使用PyArrow backend加速（如果可用）
        if PYARROW_AVAILABLE and hasattr(pd, 'ArrowDtype'):
            try:
                for col in df.select_dtypes(include=[np.number]).columns:
                    df[col] = df[col].astype('float64[pyarrow]')
            except Exception:
                # 如果PyArrow转换失败，继续使用标准pandas
                pass

        # 向量化缺失值处理
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].fillna(df[numeric_cols].median())

        # 向量化异常值处理
        for col in numeric_cols:
            outliers = VectorizedDataCleaner._detect_outliers_iqr_numba(
                df[col].values
            )
            if outliers.any():
                median_val = df[col].median()
                df.loc[outliers, col] = median_val

        return df


@dataclass
class GPUOptimizationConfig:
    """GPU优化配置"""
    enable_tensor_cores: bool = True
    enable_flash_attention: bool = True
    enable_fused_ops: bool = True
    enable_cudnn_benchmark: bool = True
    enable_tf32: bool = True
    memory_pool_size: str = "6GB"
    max_split_size_mb: int = 1024
    gradient_accumulation_steps: int = 8
    use_gradient_checkpointing: bool = True


class AdvancedGPUOptimizer:
    """高级GPU优化器"""
    
    def __init__(self, config: Optional[GPUOptimizationConfig] = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or GPUOptimizationConfig()
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        if torch.cuda.is_available():
            self.gpu_properties = torch.cuda.get_device_properties(0)
            self.total_memory = self.gpu_properties.total_memory
            self.logger.info(f"🚀 初始化高级GPU优化器: {self.gpu_properties.name}")
        else:
            self.logger.warning("⚠️ CUDA不可用，GPU优化功能受限")
    
    def apply_cuda_optimizations(self):
        """应用CUDA优化设置"""
        if not torch.cuda.is_available():
            return
        
        self.logger.info("🔧 应用高级CUDA优化...")
        
        # 启用CUDNN基准测试
        if self.config.enable_cudnn_benchmark:
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.deterministic = False
            self.logger.info("✅ CUDNN基准测试已启用")
        
        # 启用TF32加速
        if self.config.enable_tf32:
            torch.backends.cuda.matmul.allow_tf32 = True
            torch.backends.cudnn.allow_tf32 = True
            self.logger.info("✅ TF32加速已启用")
        
        # 设置内存分配策略
        memory_fraction = 0.95  # 使用95%的GPU内存
        torch.cuda.set_per_process_memory_fraction(memory_fraction)
        
        # 配置内存池
        allocator_config = f"max_split_size_mb:{self.config.max_split_size_mb},roundup_power2_divisions:32,garbage_collection_threshold:0.8"
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = allocator_config
        
        self.logger.info(f"✅ GPU内存优化完成 (使用{memory_fraction*100}%内存)")
    
    def enable_tensor_cores(self, model: torch.nn.Module):
        """启用Tensor Core优化"""
        if not self.config.enable_tensor_cores:
            return model
        
        self.logger.info("🔥 启用Tensor Core优化...")
        
        # 确保模型使用适合Tensor Core的数据类型和维度
        def optimize_for_tensor_cores(module):
            for name, child in module.named_children():
                if isinstance(child, torch.nn.Linear):
                    # 确保线性层维度是8的倍数（Tensor Core要求）
                    in_features = child.in_features
                    out_features = child.out_features
                    
                    # 调整到最近的8的倍数
                    new_in_features = ((in_features + 7) // 8) * 8
                    new_out_features = ((out_features + 7) // 8) * 8
                    
                    if new_in_features != in_features or new_out_features != out_features:
                        # 创建新的线性层
                        new_linear = torch.nn.Linear(new_in_features, new_out_features, 
                                                    bias=child.bias is not None)
                        
                        # 复制权重（如果维度匹配）
                        with torch.no_grad():
                            if new_in_features >= in_features and new_out_features >= out_features:
                                new_linear.weight[:out_features, :in_features] = child.weight
                                if child.bias is not None:
                                    new_linear.bias[:out_features] = child.bias
                        
                        setattr(module, name, new_linear)
                        self.logger.info(f"🔧 优化层 {name}: {in_features}x{out_features} → {new_in_features}x{new_out_features}")
                
                optimize_for_tensor_cores(child)
        
        optimize_for_tensor_cores(model)
        self.logger.info("✅ Tensor Core优化完成")
        return model
    
    def apply_gradient_accumulation(self, optimizer: torch.optim.Optimizer, 
                                  loss: torch.Tensor, step: int) -> bool:
        """应用梯度累积"""
        if not hasattr(self, '_accumulated_steps'):
            self._accumulated_steps = 0
        
        # 缩放损失
        loss = loss / self.config.gradient_accumulation_steps
        loss.backward()
        
        self._accumulated_steps += 1
        
        # 检查是否需要更新参数
        if self._accumulated_steps >= self.config.gradient_accumulation_steps:
            optimizer.step()
            optimizer.zero_grad()
            self._accumulated_steps = 0
            return True  # 表示执行了参数更新
        
        return False  # 表示只是累积梯度
    
    def optimize_model_for_gpu(self, model: torch.nn.Module) -> torch.nn.Module:
        """为GPU优化模型"""
        self.logger.info("🚀 开始模型GPU优化...")
        
        # 移动到GPU
        model = model.to(self.device)
        
        # 启用Tensor Core优化
        model = self.enable_tensor_cores(model)
        
        # 启用梯度检查点（如果配置启用）
        if self.config.use_gradient_checkpointing:
            self.enable_gradient_checkpointing(model)
        
        # 编译模型（PyTorch 2.0+）
        if hasattr(torch, 'compile'):
            try:
                model = torch.compile(
                    model,
                    mode='max-autotune',
                    fullgraph=False,
                    dynamic=True
                )
                self.logger.info("✅ 模型编译优化完成")
            except Exception as e:
                self.logger.warning(f"⚠️ 模型编译失败: {e}")
        
        self.logger.info("✅ 模型GPU优化完成")
        return model
    
    def enable_gradient_checkpointing(self, model: torch.nn.Module):
        """启用梯度检查点"""
        self.logger.info("🔧 启用梯度检查点...")
        
        def apply_checkpointing(module):
            for name, child in module.named_children():
                # 对Transformer层应用梯度检查点
                if hasattr(child, 'forward') and 'transformer' in name.lower():
                    original_forward = child.forward
                    
                    def checkpointed_forward(*args, **kwargs):
                        return torch.utils.checkpoint.checkpoint(
                            original_forward, *args, **kwargs, use_reentrant=False
                        )
                    
                    child.forward = checkpointed_forward
                    self.logger.info(f"🔧 应用梯度检查点: {name}")
                
                apply_checkpointing(child)
        
        apply_checkpointing(model)
        self.logger.info("✅ 梯度检查点启用完成")
    
    def create_optimized_dataloader(self, dataset, batch_size: int, **kwargs):
        """创建优化的数据加载器"""
        # 计算最优worker数量
        num_workers = min(24, os.cpu_count() or 1)
        
        # 优化的DataLoader配置
        dataloader_config = {
            'batch_size': batch_size,
            'shuffle': kwargs.get('shuffle', True),
            'num_workers': num_workers,
            'pin_memory': True,
            'persistent_workers': True,
            'prefetch_factor': 12,  # 大幅增加预取
            'drop_last': True,
            'multiprocessing_context': 'spawn'
        }
        
        dataloader_config.update(kwargs)
        
        self.logger.info(f"🔧 创建优化DataLoader: batch_size={batch_size}, workers={num_workers}")
        
        return torch.utils.data.DataLoader(dataset, **dataloader_config)
    
    def monitor_gpu_utilization(self) -> Dict[str, float]:
        """监控GPU利用率"""
        if not torch.cuda.is_available():
            return {}
        
        try:
            # 获取GPU内存信息
            allocated = torch.cuda.memory_allocated()
            reserved = torch.cuda.memory_reserved()
            total = self.total_memory
            
            # 计算利用率
            memory_util = (allocated / total) * 100
            reserved_util = (reserved / total) * 100
            
            return {
                'memory_allocated_percent': memory_util,
                'memory_reserved_percent': reserved_util,
                'memory_allocated_gb': allocated / (1024**3),
                'memory_reserved_gb': reserved / (1024**3),
                'memory_total_gb': total / (1024**3)
            }
        except Exception as e:
            self.logger.error(f"❌ GPU监控失败: {e}")
            return {}
    
    def optimize_for_inference(self, model: torch.nn.Module) -> torch.nn.Module:
        """为推理优化模型"""
        self.logger.info("🔧 优化模型用于推理...")
        
        # 设置为评估模式
        model.eval()
        
        # 禁用梯度计算
        for param in model.parameters():
            param.requires_grad = False
        
        # 应用推理优化
        if hasattr(torch, 'jit') and hasattr(torch.jit, 'optimize_for_inference'):
            try:
                model = torch.jit.optimize_for_inference(model)
                self.logger.info("✅ JIT推理优化完成")
            except Exception as e:
                self.logger.warning(f"⚠️ JIT优化失败: {e}")
        
        self.logger.info("✅ 推理优化完成")
        return model
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化总结"""
        gpu_info = self.monitor_gpu_utilization()
        
        return {
            'device': str(self.device),
            'gpu_name': self.gpu_properties.name if torch.cuda.is_available() else 'N/A',
            'optimizations_enabled': {
                'tensor_cores': self.config.enable_tensor_cores,
                'flash_attention': self.config.enable_flash_attention,
                'fused_ops': self.config.enable_fused_ops,
                'cudnn_benchmark': self.config.enable_cudnn_benchmark,
                'tf32': self.config.enable_tf32,
                'gradient_checkpointing': self.config.use_gradient_checkpointing,
                'gradient_accumulation': self.config.gradient_accumulation_steps > 1
            },
            'memory_config': {
                'max_split_size_mb': self.config.max_split_size_mb,
                'memory_pool_size': self.config.memory_pool_size
            },
            'current_gpu_status': gpu_info
        }


class AsyncAPIOptimizer:
    """异步API优化器"""
    
    def __init__(self, max_concurrent: int = 50):
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.session = None
        
        # 本地缓存
        self.local_cache = cachetools.TTLCache(maxsize=1000, ttl=300)
    
    async def initialize(self):
        """初始化异步会话"""
        import aiohttp
        
        connector = aiohttp.TCPConnector(
            limit=self.max_concurrent,
            limit_per_host=20,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        
        timeout = aiohttp.ClientTimeout(total=30, connect=10)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout
        )
    
    async def batch_api_call(
        self,
        requests: List[Dict[str, Any]],
        api_func,
        use_cache: bool = True
    ) -> List[Any]:
        """批量异步API调用"""
        tasks = []
        
        for request in requests:
            # 检查本地缓存
            cache_key = str(hash(str(request)))
            if use_cache and cache_key in self.local_cache:
                tasks.append(asyncio.create_task(
                    self._return_cached_result(self.local_cache[cache_key])
                ))
            else:
                tasks.append(asyncio.create_task(
                    self._limited_api_call(request, api_func, cache_key, use_cache)
                ))
        
        return await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _limited_api_call(
        self,
        request: Dict[str, Any],
        api_func,
        cache_key: str,
        use_cache: bool
    ):
        """限制并发的API调用"""
        async with self.semaphore:
            result = await api_func(request)
            
            if use_cache and result is not None:
                self.local_cache[cache_key] = result
            
            return result
    
    async def _return_cached_result(self, result):
        """返回缓存结果"""
        return result
    
    async def close(self):
        """关闭会话"""
        if self.session:
            await self.session.close()


class ParallelRiskCalculator:
    """并行风险计算器"""
    
    def __init__(self, n_workers: Optional[int] = None):
        self.n_workers = n_workers or mp.cpu_count()
        self.executor = ProcessPoolExecutor(max_workers=self.n_workers)
    
    @staticmethod
    @jit(nopython=True, parallel=True, cache=True, nogil=True)
    def monte_carlo_var_numba(
        returns: np.ndarray,
        confidence_level: float,
        n_simulations: int = 10000
    ) -> float:
        """使用Numba并行化的蒙特卡洛VaR计算"""
        mean_return = np.mean(returns)
        std_return = np.std(returns)

        # 并行生成随机数
        simulated_returns = np.random.normal(
            mean_return, std_return, n_simulations
        )

        percentile = (1 - confidence_level) * 100
        var_value = np.percentile(simulated_returns, percentile)

        return var_value
    
    async def calculate_portfolio_risk_parallel(
        self,
        portfolio_data: Dict[str, Any]
    ) -> Dict[str, float]:
        """并行计算投资组合风险"""
        returns = np.array(portfolio_data.get('returns', []))
        
        if len(returns) == 0:
            return {}
        
        # 并行计算不同风险指标
        tasks = [
            asyncio.get_event_loop().run_in_executor(
                self.executor,
                self.monte_carlo_var_numba,
                returns, 0.95, 10000
            ),
            asyncio.get_event_loop().run_in_executor(
                self.executor,
                self.monte_carlo_var_numba,
                returns, 0.99, 10000
            ),
            asyncio.get_event_loop().run_in_executor(
                self.executor,
                self._calculate_max_drawdown,
                returns
            )
        ]
        
        var_95, var_99, max_drawdown = await asyncio.gather(*tasks)
        
        return {
            'var_95': var_95,
            'var_99': var_99,
            'max_drawdown': max_drawdown
        }
    
    @staticmethod
    def _calculate_max_drawdown(returns: np.ndarray) -> float:
        """计算最大回撤"""
        cumulative_returns = np.cumprod(1 + returns)
        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - peak) / peak
        return np.min(drawdown)
    
    def close(self):
        """关闭执行器"""
        self.executor.shutdown(wait=True)


class PerformanceOptimizer:
    """性能优化器主类"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 子优化器
        gpu_opt_config_dict = self.config.get('gpu_optimization', {})
        gpu_opt_config = GPUOptimizationConfig(**gpu_opt_config_dict)
        self.gpu_optimizer = AdvancedGPUOptimizer(config=gpu_opt_config)

        # 动态批次大小优化器
        batch_opt_config_dict = self.config.get('dynamic_batch_optimization', {})
        batch_opt_config = BatchSizeConfig(**batch_opt_config_dict)
        self.batch_optimizer = DynamicBatchOptimizer(device=self.gpu_optimizer.device, config=batch_opt_config)

        self.api_optimizer = AsyncAPIOptimizer()
        self.risk_calculator = ParallelRiskCalculator()
        
        # 性能监控
        self.performance_stats = {
            'memory_usage': [],
            'gpu_memory_usage': [],
            'api_latency': [],
            'throughput': []
        }
    
    def update_dynamic_batch_size(self) -> Tuple[int, bool]:
        """Convenience method to update the dynamic batch size."""
        if self.batch_optimizer:
            return self.batch_optimizer.update_batch_size()
        # Return a default value if the optimizer is not available
        return self.config.get('initial_batch_size', 512), False

    async def initialize(self):
        """初始化优化器"""
        await self.api_optimizer.initialize()
        
        # 设置PyTorch优化
        self.gpu_optimizer.apply_cuda_optimizations()
        
        self.logger.info("Performance optimizer initialized")
    
    def _configure_pytorch_optimizations(self):
        """配置PyTorch优化"""
        # This method is now handled by self.gpu_optimizer.apply_cuda_optimizations()
        pass
    
    async def optimize_model(self, model: nn.Module) -> nn.Module:
        """优化模型"""
        # GPU显存优化
        model = self.gpu_optimizer.optimize_model_for_gpu(model)

        # 模型编译优化 (PyTorch 2.0+), now handled inside optimize_model_for_gpu
        # if hasattr(torch, 'compile'):
        #     model = torch.compile(model, mode='max-autotune')

        return model

    def optimize_model_sync(self, model: nn.Module) -> nn.Module:
        """同步优化模型"""
        # GPU显存优化
        model = self.gpu_optimizer.optimize_model_for_gpu(model)

        # 暂时禁用模型编译以避免维度不匹配问题
        # if hasattr(torch, 'compile'):
        #     try:
        #         model = torch.compile(model, mode='default')
        #         self.logger.info("Model compilation enabled")
        #     except Exception as e:
        #         self.logger.warning(f"Model compilation failed: {e}")

        return model
    
    def create_optimized_dataloader(
        self,
        data_path: str,
        batch_size: int,
        sequence_length: int,
        feature_dim: int,
        num_workers: int = 4
    ) -> DataLoader:
        """创建优化的数据加载器"""
        return self.gpu_optimizer.create_optimized_dataloader(
            dataset=OptimizedDataset(
                data_path=data_path,
                sequence_length=sequence_length,
                feature_dim=feature_dim,
                chunk_size=batch_size * 10
            ),
            batch_size=batch_size,
            num_workers=num_workers
        )
    
    async def cleanup(self):
        """清理资源"""
        await self.api_optimizer.close()
        self.risk_calculator.close()
        
        # 清理GPU缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        # 强制垃圾回收
        gc.collect()
        
        self.logger.info("Performance optimizer cleanup completed")
