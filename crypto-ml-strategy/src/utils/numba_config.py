"""
Numba配置模块
配置Numba以优化性能并减少调试输出
"""

import os
import warnings

# 🔥 修复：使用环境变量作为全局标志，确保跨进程/模块只配置一次
_NUMBA_CONFIG_ENV_VAR = 'CRYPTO_ML_NUMBA_CONFIGURED'

def configure_numba():
    """配置Numba环境"""
    # 检查是否已经配置过
    if os.environ.get(_NUMBA_CONFIG_ENV_VAR) == 'true':
        return  # 已经配置过，直接返回

    try:
        # 禁用Numba的调试输出
        os.environ['NUMBA_DISABLE_JIT'] = '0'
        os.environ['NUMBA_WARNINGS'] = '0'
        os.environ['NUMBA_DISABLE_PERFORMANCE_WARNINGS'] = '1'
        os.environ['NUMBA_DISABLE_INTEL_SVML'] = '1'  # 禁用Intel SVML警告
        os.environ['NUMBA_DISABLE_TBB'] = '1'  # 禁用TBB警告

        # 设置Numba缓存目录
        os.environ['NUMBA_CACHE_DIR'] = '.numba_cache'

        # 禁用相关警告
        warnings.filterwarnings('ignore', category=UserWarning, module='numba')
        warnings.filterwarnings('ignore', category=DeprecationWarning, module='numba')
        warnings.filterwarnings('ignore', category=FutureWarning, module='numba')

        # 设置配置完成标志
        os.environ[_NUMBA_CONFIG_ENV_VAR] = 'true'

        # 🔥 修复：只在主进程中输出一次
        import sys
        if hasattr(sys, '_getframe'):
            # 检查调用栈，只在main.py直接调用时输出
            frame = sys._getframe(1)
            if 'main.py' in frame.f_code.co_filename:
                print("✅ Numba配置完成")

    except Exception as e:
        # 即使配置失败也设置标志，避免重复尝试
        os.environ[_NUMBA_CONFIG_ENV_VAR] = 'true'
        # 只在调试模式下输出错误
        if os.environ.get('CRYPTO_ML_DEBUG') == 'true':
            print(f"⚠️ Numba配置失败: {e}")
        # 继续运行，不影响主程序

if __name__ == "__main__":
    configure_numba()
