"""
性能监控器
监控系统各组件的性能指标，提供实时性能监控和告警功能
"""

import time
import psutil
import logging
import numpy as np
import threading
import json
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass, field
from datetime import datetime

# 尝试导入可选依赖
try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    torch = None

try:
    import pynvml
    GPU_AVAILABLE = True
    pynvml.nvmlInit()
except (ImportError, Exception):
    GPU_AVAILABLE = False
    pynvml = None

@dataclass
class PerformanceMetrics:
    """性能指标"""
    cpu_usage: float
    memory_usage: float
    gpu_usage: Optional[float]
    gpu_memory: Optional[float]
    execution_time: float
    timestamp: datetime

class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.metrics_history = []
        
    def start_monitoring(self, operation_name: str):
        """开始监控"""
        self.operation_name = operation_name
        self.start_time = time.time()
        self.start_metrics = self._collect_metrics()
        
    def stop_monitoring(self) -> PerformanceMetrics:
        """停止监控并返回指标"""
        end_time = time.time()
        end_metrics = self._collect_metrics()
        
        execution_time = end_time - self.start_time
        
        metrics = PerformanceMetrics(
            cpu_usage=end_metrics['cpu_usage'],
            memory_usage=end_metrics['memory_usage'],
            gpu_usage=end_metrics.get('gpu_usage'),
            gpu_memory=end_metrics.get('gpu_memory'),
            execution_time=execution_time,
            timestamp=datetime.now()
        )
        
        self.metrics_history.append(metrics)
        
        self.logger.info(f"📊 {self.operation_name} 性能指标:")
        self.logger.info(f"  执行时间: {execution_time:.4f}秒")
        self.logger.info(f"  CPU使用率: {metrics.cpu_usage:.1f}%")
        self.logger.info(f"  内存使用: {metrics.memory_usage:.1f}%")
        if metrics.gpu_usage is not None:
            self.logger.info(f"  GPU使用率: {metrics.gpu_usage:.1f}%")
            self.logger.info(f"  GPU内存: {metrics.gpu_memory:.1f}%")
        
        return metrics
    
    def _collect_metrics(self) -> Dict[str, Any]:
        """收集系统指标"""
        metrics = {
            'cpu_usage': psutil.cpu_percent(),
            'memory_usage': psutil.virtual_memory().percent
        }
        
        # GPU指标
        if torch.cuda.is_available():
            try:
                gpu_usage = torch.cuda.utilization()
                gpu_memory = torch.cuda.memory_allocated() / torch.cuda.max_memory_allocated() * 100
                metrics['gpu_usage'] = gpu_usage
                metrics['gpu_memory'] = gpu_memory
            except:
                pass
        
        return metrics
    
    def get_average_metrics(self) -> Optional[Dict[str, float]]:
        """获取平均性能指标"""
        if not self.metrics_history:
            return None
        
        avg_metrics = {
            'avg_cpu_usage': np.mean([m.cpu_usage for m in self.metrics_history]),
            'avg_memory_usage': np.mean([m.memory_usage for m in self.metrics_history]),
            'avg_execution_time': np.mean([m.execution_time for m in self.metrics_history])
        }
        
        gpu_usages = [m.gpu_usage for m in self.metrics_history if m.gpu_usage is not None]
        if gpu_usages:
            avg_metrics['avg_gpu_usage'] = np.mean(gpu_usages)
        
        return avg_metrics

# 性能监控装饰器
def monitor_performance(operation_name: str = None):
    """性能监控装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            monitor = PerformanceMonitor()
            name = operation_name or func.__name__
            monitor.start_monitoring(name)
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                monitor.stop_monitoring()
        return wrapper
    return decorator
