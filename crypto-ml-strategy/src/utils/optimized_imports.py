"""
优化的导入管理器
统一管理所有模块的导入，减少重复导入和循环依赖
"""

# 标准库导入
import os
import sys
import time
import logging
import asyncio
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta

# 第三方库导入
import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split

# 配置日志
def setup_logging(level=logging.INFO):
    """设置统一的日志配置"""
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('logs/system.log', mode='a')
        ]
    )
    return logging.getLogger(__name__)

# 常用工具函数
def safe_divide(a, b, default=0.0):
    """安全除法"""
    return a / b if b != 0 else default

def safe_log(x, default=0.0):
    """安全对数"""
    return np.log(x) if x > 0 else default

def safe_sqrt(x, default=0.0):
    """安全平方根"""
    return np.sqrt(x) if x >= 0 else default

# 性能装饰器
def timing_decorator(func):
    """性能计时装饰器"""
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger = logging.getLogger(func.__module__)
        logger.debug(f"{func.__name__} 执行时间: {end_time - start_time:.4f}秒")
        return result
    return wrapper

def cache_decorator(maxsize=128):
    """简单的缓存装饰器"""
    def decorator(func):
        cache = {}
        def wrapper(*args, **kwargs):
            key = str(args) + str(sorted(kwargs.items()))
            if key not in cache:
                if len(cache) >= maxsize:
                    cache.clear()  # 简单的清理策略
                cache[key] = func(*args, **kwargs)
            return cache[key]
        return wrapper
    return decorator
