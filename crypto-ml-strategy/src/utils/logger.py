"""
日志工具
提供统一的日志配置和管理功能
"""

import logging
import logging.handlers
import sys
import os
from pathlib import Path
from typing import Optional, Dict, Any
import json
from datetime import datetime
import traceback


class JSONFormatter(logging.Formatter):
    """JSON格式的日志格式化器"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # 添加异常信息
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # 添加额外字段
        if hasattr(record, 'extra_fields'):
            log_entry.update(record.extra_fields)
        
        return json.dumps(log_entry, ensure_ascii=False)


class ColoredFormatter(logging.Formatter):
    """彩色控制台日志格式化器"""
    
    # ANSI颜色代码
    COLORS = {
        'DEBUG': '\033[36m',    # 青色
        'INFO': '\033[32m',     # 绿色
        'WARNING': '\033[33m',  # 黄色
        'ERROR': '\033[31m',    # 红色
        'CRITICAL': '\033[35m', # 紫色
        'RESET': '\033[0m'      # 重置
    }
    
    def format(self, record):
        # 添加颜色
        level_color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
        record.levelname = f"{level_color}{record.levelname}{self.COLORS['RESET']}"
        
        # 格式化消息
        formatted = super().format(record)
        
        return formatted


class StructuredLogger(logging.LoggerAdapter):
    """结构化日志记录器，支持上下文"""
    def process(self, msg, kwargs):
        # 将kwargs中的额外信息合并到extra字典中
        if 'extra' not in kwargs:
            kwargs['extra'] = {}
        kwargs['extra'].update(self.extra)
        return msg, kwargs
    
    def debug(self, message: str, **kwargs):
        exc_info = kwargs.pop('exc_info', None)
        self.logger.debug(message, extra=kwargs, exc_info=exc_info)
    
    def info(self, message: str, **kwargs):
        exc_info = kwargs.pop('exc_info', None)
        self.logger.info(message, extra=kwargs, exc_info=exc_info)
    
    def warning(self, message: str, **kwargs):
        exc_info = kwargs.pop('exc_info', None)
        self.logger.warning(message, extra=kwargs, exc_info=exc_info)
    
    def error(self, message: str, **kwargs):
        # 🔧 修复: 正确处理 exc_info，避免与 'extra' 冲突
        # 当调用 logger.error(..., exc_info=True) 时，
        # exc_info 会被收集到 kwargs 中。我们需要将其从中取出，
        # 并作为独立的关键字参数传递给底层的 logger，
        # 而不是让它留在将被赋给 'extra' 的 kwargs 字典中。
        exc_info = kwargs.pop('exc_info', True)
        self.logger.error(message, extra=kwargs, exc_info=exc_info)
    
    def critical(self, message: str, **kwargs):
        exc_info = kwargs.pop('exc_info', True)
        self.logger.critical(message, extra=kwargs, exc_info=exc_info)
    
    def exception(self, message: str, **kwargs):
        """记录异常信息"""
        kwargs['exception'] = traceback.format_exc()
        self.logger.exception(message, extra=kwargs)


def setup_logger(
    name: str = "crypto_ml_strategy",
    level: str = "INFO",
    log_file: Optional[str] = None,
    max_file_size: int = 100 * 1024 * 1024,  # 100MB
    backup_count: int = 5,
    console_output: bool = True,
    json_format: bool = False
) -> StructuredLogger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        level: 日志级别
        log_file: 日志文件路径
        log_format: 日志格式 ('standard', 'detailed', 'simple')
        max_file_size: 最大文件大小 (字节)
        backup_count: 备份文件数量
        console_output: 是否输出到控制台
        json_format: 是否使用JSON格式
        
    Returns:
        配置好的日志记录器
    """
    logger = logging.getLogger(name)
    
    # 清除现有的处理器
    logger.handlers.clear()
    
    # 设置日志级别
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(numeric_level)
    
    # 定义日志格式
    format_string = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    if json_format:
        formatter = JSONFormatter()
        console_formatter = JSONFormatter()
    else:
        formatter = logging.Formatter(format_string)
        console_formatter = ColoredFormatter(format_string)
    
    # 控制台处理器
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(console_formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保日志目录存在
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 使用RotatingFileHandler进行日志轮转
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )

        # 确保文件处理器也设置为相同的日志级别
        file_handler.setLevel(numeric_level)

        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return StructuredLogger(logger, {})


def setup_training_logger(
    model_name: str,
    experiment_name: str,
    log_dir: str = "logs"
) -> logging.Logger:
    """
    设置训练专用日志记录器
    
    Args:
        model_name: 模型名称
        experiment_name: 实验名称
        log_dir: 日志目录
        
    Returns:
        训练日志记录器
    """
    # 创建日志文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"{model_name}_{experiment_name}_{timestamp}.log"
    log_file = os.path.join(log_dir, "training", log_filename)
    
    logger_name = f"training.{model_name}.{experiment_name}"
    
    return setup_logger(
        name=logger_name,
        level="INFO",
        log_file=log_file,
        log_format="detailed",
        console_output=True,
        json_format=False
    )


def setup_api_logger(log_dir: str = "logs") -> logging.Logger:
    """
    设置API专用日志记录器
    
    Args:
        log_dir: 日志目录
        
    Returns:
        API日志记录器
    """
    log_file = os.path.join(log_dir, "api", "api.log")
    
    return setup_logger(
        name="api",
        level="INFO",
        log_file=log_file,
        log_format="standard",
        console_output=True,
        json_format=True  # API日志使用JSON格式便于分析
    )


def setup_error_logger(log_dir: str = "logs") -> logging.Logger:
    """
    设置错误专用日志记录器
    
    Args:
        log_dir: 日志目录
        
    Returns:
        错误日志记录器
    """
    log_file = os.path.join(log_dir, "errors", "errors.log")
    
    logger = setup_logger(
        name="errors",
        level="ERROR",
        log_file=log_file,
        log_format="detailed",
        console_output=False,
        json_format=True
    )
    
    return logger


class LoggerContext:
    """日志上下文管理器"""
    
    def __init__(self, logger: StructuredLogger, **kwargs):
        self.logger = logger
        self.context_data = kwargs
        self.original_context = self.logger.extra.copy()

    def __enter__(self):
        self.logger.extra.update(self.context_data)
        return self.logger

    def __exit__(self, exc_type, exc_value, traceback):
        self.logger.extra = self.original_context


def log_function_call(logger: logging.Logger):
    """函数调用日志装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            func_name = func.__name__
            logger.debug(f"Calling function: {func_name}")
            
            try:
                result = func(*args, **kwargs)
                logger.debug(f"Function {func_name} completed successfully")
                return result
            except Exception as e:
                logger.error(f"Function {func_name} failed: {str(e)}")
                raise
        
        return wrapper
    return decorator


def log_execution_time(logger: logging.Logger):
    """执行时间日志装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            import time
            
            func_name = func.__name__
            start_time = time.time()
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                logger.info(f"Function {func_name} executed in {execution_time:.4f} seconds")
                return result
            except Exception as e:
                execution_time = time.time() - start_time
                logger.error(f"Function {func_name} failed after {execution_time:.4f} seconds: {str(e)}")
                raise
        
        return wrapper
    return decorator


# 全局日志记录器
_global_logger = None


def get_logger(name: Optional[str] = None) -> logging.Logger:
    """
    获取全局日志记录器
    
    Args:
        name: 日志记录器名称
        
    Returns:
        日志记录器
    """
    global _global_logger
    
    if _global_logger is None:
        _global_logger = setup_logger()
    
    if name:
        return logging.getLogger(name)
    
    return _global_logger


def configure_logging_from_config(config: Dict[str, Any]):
    """
    从配置字典配置日志
    
    Args:
        config: 日志配置字典
    """
    logging_config = config.get('logging', {})
    
    setup_logger(
        level=logging_config.get('level', 'INFO'),
        log_file=logging_config.get('file'),
        log_format=logging_config.get('format', 'standard'),
        max_file_size=logging_config.get('max_size', 100 * 1024 * 1024),
        backup_count=logging_config.get('backup_count', 5),
        console_output=logging_config.get('console_output', True),
        json_format=logging_config.get('json_format', False)
    )


if __name__ == "__main__":
    # 测试日志功能
    logger = setup_logger("test", level="DEBUG", log_file="test.log")
    
    logger.debug("This is a debug message")
    logger.info("This is an info message")
    logger.warning("This is a warning message")
    logger.error("This is an error message")
    logger.critical("This is a critical message")
    
    # 测试结构化日志
    structured_logger = StructuredLogger("test_structured")
    structured_logger.info("Structured log message", user_id=123, action="login")
    
    # 测试上下文管理器
    with LoggerContext(logger, request_id="req_123", user_id="user_456"):
        logger.info("Message with context")
    
    print("日志测试完成")
