#!/usr/bin/env python3
"""
在线推理引擎 - 实时市场预测和信号生成
"""

import asyncio
import logging
import torch
import numpy as np
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
import json
import time

from src.models.unified_fusion_model import UnifiedSignalFusionModel
from src.utils.advanced_gpu_optimizer import AdvancedGPUOptimizer
from src.storage.unified_cache import get_cache
from src.data.enhanced_feature_engineering import EnhancedFeatureEngineer


class OnlineInferenceEngine:
    """在线推理引擎"""
    
    def __init__(self, model_path: str = None, config: Dict[str, Any] = None):
        self.logger = logging.getLogger(__name__)
        self.config = config or {}
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 模型和优化器
        self.model = None
        self.gpu_optimizer = AdvancedGPUOptimizer()
        self.feature_engineer = EnhancedFeatureEngineer()
        
        # 缓存系统
        self.cache = get_cache()
        
        # 推理状态
        self.is_running = False
        self.inference_count = 0
        self.last_inference_time = None
        
        # 性能统计
        self.inference_times = []
        self.prediction_history = []
        self.confidence_scores = []
        
        # 加载模型
        if model_path:
            self.load_model(model_path)
    
    def load_model(self, model_path: str):
        """加载训练好的模型"""
        try:
            self.logger.info(f"🔄 加载模型: {model_path}")
            
            # 加载模型状态
            checkpoint = torch.load(model_path, map_location=self.device)
            
            # 获取模型配置
            model_config = checkpoint.get('model_config', self.config.get('model', {}))
            
            # 创建模型
            self.model = UnifiedSignalFusionModel(**model_config)

            # 加载权重 - 修复集成模型兼容性
            state_dict = checkpoint['model_state_dict']

            # 检查是否是集成模型格式
            if any(key.startswith('base_models.') for key in state_dict.keys()):
                self.logger.info("🔄 检测到集成模型，提取第一个基础模型...")
                # 提取第一个基础模型的权重
                single_model_state_dict = {}
                for key, value in state_dict.items():
                    if key.startswith('base_models.0.'):
                        # 移除 'base_models.0.' 前缀
                        new_key = key[len('base_models.0.'):]
                        single_model_state_dict[new_key] = value

                if single_model_state_dict:
                    self.model.load_state_dict(single_model_state_dict)
                    self.logger.info("✅ 成功加载集成模型中的第一个基础模型")
                else:
                    self.logger.warning("⚠️ 未找到有效的基础模型权重，使用随机初始化")
            else:
                # 标准单一模型格式
                self.model.load_state_dict(state_dict)
                self.logger.info("✅ 成功加载单一模型")
            
            # GPU优化
            self.model = self.gpu_optimizer.optimize_for_inference(self.model)
            
            self.logger.info("✅ 模型加载完成")
            
        except Exception as e:
            self.logger.error(f"❌ 模型加载失败: {e}")
            raise
    
    def preprocess_market_data(self, market_data: Dict[str, Any]) -> torch.Tensor:
        """预处理市场数据"""
        try:
            # 提取基础特征
            features = []
            
            # 价格特征
            if 'close' in market_data:
                features.append(market_data['close'])
            if 'open' in market_data:
                features.append(market_data['open'])
            if 'high' in market_data:
                features.append(market_data['high'])
            if 'low' in market_data:
                features.append(market_data['low'])
            if 'volume' in market_data:
                features.append(market_data['volume'])
            
            # 技术指标
            technical_indicators = ['rsi', 'macd', 'bb_upper', 'bb_lower', 'sma_20', 'ema_12']
            for indicator in technical_indicators:
                if indicator in market_data:
                    features.append(market_data[indicator])
                else:
                    features.append(0.0)  # 默认值
            
            # 确保特征数量匹配模型输入
            target_dim = self.config.get('model', {}).get('feature_dim', 59)  # 🔥 修复：统一特征维度
            while len(features) < target_dim:
                features.append(0.0)
            
            features = features[:target_dim]  # 截断到目标维度
            
            # 转换为张量
            features_tensor = torch.tensor(features, dtype=torch.float32, device=self.device)
            
            # 添加批次维度
            if features_tensor.dim() == 1:
                features_tensor = features_tensor.unsqueeze(0)
            
            return features_tensor
            
        except Exception as e:
            self.logger.error(f"❌ 数据预处理失败: {e}")
            raise
    
    async def predict(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """执行预测"""
        if not self.model:
            raise RuntimeError("模型未加载")
        
        start_time = time.time()
        
        try:
            # 检查缓存
            cache_key = self._generate_cache_key(market_data)
            cached_result = self.cache.get(cache_key)
            
            if cached_result is not None:
                self.logger.debug("🎯 使用缓存预测结果")
                return cached_result
            
            # 预处理数据
            features = self.preprocess_market_data(market_data)
            
            # 执行推理
            self.model.eval()
            with torch.no_grad():
                with torch.cuda.amp.autocast():
                    outputs = self.model(features)
            
            # 处理输出
            signal_probs = torch.softmax(outputs['signal'], dim=-1)
            signal_class = torch.argmax(signal_probs, dim=-1)
            confidence = torch.max(signal_probs, dim=-1)[0]
            
            # 转换为可读格式
            signal_names = ['SELL', 'HOLD', 'BUY']
            predicted_signal = signal_names[signal_class.item()]
            confidence_score = confidence.item()
            
            # 生成预测结果
            prediction = {
                'signal': predicted_signal,
                'confidence': confidence_score,
                'probabilities': {
                    'SELL': signal_probs[0][0].item(),
                    'HOLD': signal_probs[0][1].item(),
                    'BUY': signal_probs[0][2].item()
                },
                'timestamp': datetime.now().isoformat(),
                'market_data': market_data,
                'inference_time_ms': (time.time() - start_time) * 1000
            }
            
            # 缓存结果
            self.cache.put(cache_key, prediction)
            
            # 更新统计
            self.inference_count += 1
            self.last_inference_time = datetime.now()
            self.inference_times.append(prediction['inference_time_ms'])
            self.prediction_history.append(prediction)
            self.confidence_scores.append(confidence_score)
            
            # 保持历史记录在合理范围内
            if len(self.prediction_history) > 1000:
                self.prediction_history = self.prediction_history[-1000:]
                self.inference_times = self.inference_times[-1000:]
                self.confidence_scores = self.confidence_scores[-1000:]
            
            return prediction
            
        except Exception as e:
            self.logger.error(f"❌ 预测失败: {e}")
            raise
    
    def _generate_cache_key(self, market_data: Dict[str, Any]) -> str:
        """生成缓存键"""
        # 使用主要市场数据生成哈希键
        key_data = {
            'close': market_data.get('close', 0),
            'volume': market_data.get('volume', 0),
            'timestamp': market_data.get('timestamp', '')
        }
        return str(hash(json.dumps(key_data, sort_keys=True)))
    
    async def start_realtime_inference(self, data_callback: Callable, 
                                     signal_callback: Callable = None,
                                     interval: float = 1.0):
        """启动实时推理"""
        self.logger.info("🚀 启动实时推理引擎...")
        self.is_running = True
        
        try:
            while self.is_running:
                try:
                    # 获取市场数据
                    market_data = await data_callback()
                    
                    if market_data:
                        # 执行预测
                        prediction = await self.predict(market_data)
                        
                        # 发送信号
                        if signal_callback and prediction['confidence'] > 0.7:
                            await signal_callback(prediction)
                        
                        self.logger.debug(f"📊 预测: {prediction['signal']} (置信度: {prediction['confidence']:.3f})")
                    
                    # 等待下一次推理
                    await asyncio.sleep(interval)
                    
                except Exception as e:
                    self.logger.error(f"❌ 实时推理错误: {e}")
                    await asyncio.sleep(interval)
        
        finally:
            self.is_running = False
            self.logger.info("⏹️ 实时推理引擎已停止")
    
    def stop_realtime_inference(self):
        """停止实时推理"""
        self.is_running = False
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        if not self.inference_times:
            return {}
        
        return {
            'total_inferences': self.inference_count,
            'avg_inference_time_ms': np.mean(self.inference_times),
            'min_inference_time_ms': np.min(self.inference_times),
            'max_inference_time_ms': np.max(self.inference_times),
            'avg_confidence': np.mean(self.confidence_scores) if self.confidence_scores else 0,
            'last_inference_time': self.last_inference_time.isoformat() if self.last_inference_time else None,
            'cache_stats': self.cache.get_stats() if hasattr(self.cache, 'get_stats') else {}
        }
    
    def get_recent_predictions(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的预测结果"""
        return self.prediction_history[-limit:] if self.prediction_history else []
    
    def export_predictions(self, file_path: str):
        """导出预测历史"""
        try:
            export_data = {
                'performance_stats': self.get_performance_stats(),
                'predictions': self.prediction_history,
                'export_time': datetime.now().isoformat()
            }
            
            with open(file_path, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            self.logger.info(f"📁 预测历史已导出到: {file_path}")
            
        except Exception as e:
            self.logger.error(f"❌ 导出失败: {e}")


# 全局推理引擎实例
_inference_engine = None


def get_inference_engine(model_path: str = None, config: Dict[str, Any] = None) -> OnlineInferenceEngine:
    """获取推理引擎实例"""
    global _inference_engine
    
    if _inference_engine is None:
        _inference_engine = OnlineInferenceEngine(model_path, config)
    
    return _inference_engine


async def predict_market_signal(market_data: Dict[str, Any]) -> Dict[str, Any]:
    """预测市场信号（便捷函数）"""
    engine = get_inference_engine()
    return await engine.predict(market_data)
