"""
Redis缓存管理器
实现多级缓存、数据缓存、会话管理等功能
"""

import redis
import json
import pickle
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Callable, Tuple
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
import hashlib
import time
import threading
import asyncio
from contextlib import contextmanager
import cachetools
import os

# 尝试导入MySQL支持
try:
    import mysql.connector
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False

from src.utils.config import get_config_manager


@dataclass
class CacheStats:
    """缓存统计信息"""
    total_keys: int
    memory_usage: int
    hit_rate: float
    miss_rate: float
    evicted_keys: int
    expired_keys: int


class RedisCache:
    """
    Redis缓存管理器
    
    提供多级缓存、数据序列化、过期管理等功能
    """
    
    def __init__(
        self,
        host: str = "localhost",
        port: int = 16379,
        db: int = 0,
        password: Optional[str] = None,
        max_connections: int = 50,
        socket_timeout: int = 5,
        socket_connect_timeout: int = 5,
        retry_on_timeout: bool = True,
        health_check_interval: int = 30
    ):
        # 初始化日志记录器（必须在其他操作之前）
        self.logger = logging.getLogger(__name__)

        # Redis连接配置
        config_manager = get_config_manager()
        redis_config = config_manager.get('storage.redis', {})

        self.host = redis_config.get('host', host)
        self.port = redis_config.get('port', port)
        self.db = redis_config.get('db', db)
        self.password = redis_config.get('password', password)
        
        # 连接池配置
        self.max_connections = max_connections
        self.socket_timeout = socket_timeout
        self.socket_connect_timeout = socket_connect_timeout
        self.retry_on_timeout = retry_on_timeout
        self.health_check_interval = health_check_interval

        # 🔥 添加enabled属性
        self.enabled = redis_config.get('enabled', True)

        # 初始化Redis连接池
        if self.enabled:
            try:
                self.pool = redis.ConnectionPool(
                    host=self.host,
                    port=self.port,
                    db=self.db,
                    password=self.password,
                    max_connections=self.max_connections,
                    socket_timeout=self.socket_timeout,
                    socket_connect_timeout=self.socket_connect_timeout,
                    retry_on_timeout=self.retry_on_timeout,
                    health_check_interval=self.health_check_interval
                )

                self.redis_client = redis.Redis(connection_pool=self.pool)

                # 测试连接
                self.redis_client.ping()
                self.logger.info("Redis连接成功")

            except Exception as e:
                self.logger.warning(f"Redis连接失败，禁用Redis缓存: {e}")
                self.enabled = False
                self.redis_client = None
        else:
            self.logger.info("Redis缓存已禁用")
            self.redis_client = None
        
        # 缓存配置
        self.default_ttl = 3600  # 默认1小时过期
        self.key_prefix = "crypto_ml:"
        
        # 缓存层级定义
        self.cache_levels = {
            'L1': {'ttl': 300, 'prefix': 'l1:'},      # 5分钟 - 热数据
            'L2': {'ttl': 1800, 'prefix': 'l2:'},     # 30分钟 - 温数据
            'L3': {'ttl': 7200, 'prefix': 'l3:'},     # 2小时 - 冷数据
            'PERSISTENT': {'ttl': -1, 'prefix': 'persist:'}  # 持久化
        }
        
        # 统计信息
        self.stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0,
            'l3_hits': 0,
            'l3_misses': 0,
            'mysql_writes': 0,
            'mysql_errors': 0
        }

        # 线程锁
        self._lock = threading.Lock()

        # L3内存缓存（LFU策略）- 基于性能测试优化
        l3_cache_size = int(os.environ.get('L3_CACHE_SIZE', 50000))  # 增加到5万条
        self.l3_cache = cachetools.LFUCache(maxsize=l3_cache_size)
        self.l3_lock = threading.RLock()

        # 性能优化配置
        self.batch_size = 100  # 批量操作大小
        self.pipeline_enabled = True  # 启用Redis pipeline

        # MySQL元数据存储
        self.mysql_config = None
        self.mysql_pool = None
        # 🔥 优化：使元数据日志记录可配置
        self.metadata_logging_enabled = redis_config.get('metadata_logging', False)

        if MYSQL_AVAILABLE and self.metadata_logging_enabled:
            self._init_mysql_connection()

        self.logger.info(f"Redis缓存初始化完成: {self.host}:{self.port}/{self.db}")
        self.logger.info(f"L3内存缓存大小: {l3_cache_size}")

        # 测试连接
        self._test_connection()
    
    def _init_mysql_connection(self):
        """初始化MySQL连接"""
        try:
            # 修复：优先从配置文件读取MySQL配置，然后才是环境变量
            config_manager = get_config_manager()
            mysql_config_from_file = config_manager.get('storage.mysql', {})

            self.mysql_config = {
                'host': mysql_config_from_file.get('host', os.environ.get('MYSQL_HOST', 'localhost')),
                'port': int(mysql_config_from_file.get('port', os.environ.get('MYSQL_PORT', 3306))),
                'user': mysql_config_from_file.get('username', os.environ.get('MYSQL_USER', 'root')),
                'password': mysql_config_from_file.get('password', os.environ.get('MYSQL_PASSWORD', '')),
                'database': mysql_config_from_file.get('database', os.environ.get('MYSQL_DATABASE', 'crypto_trading')),
                'charset': 'utf8mb4',
                'autocommit': True
            }

            # 创建连接池
            self.mysql_pool = mysql.connector.pooling.MySQLConnectionPool(
                pool_name="cache_metadata_pool",
                pool_size=5,
                **self.mysql_config
            )

            # 创建元数据表
            self._create_metadata_table()

            self.logger.info("MySQL元数据存储初始化成功")

        except Exception as e:
            self.logger.warning(f"MySQL初始化失败，将跳过元数据存储: {e}")
            self.mysql_pool = None

    def _create_metadata_table(self):
        """创建缓存元数据表"""
        if not self.mysql_pool:
            return

        create_table_sql = """
        CREATE TABLE IF NOT EXISTS cache_metadata (
            id BIGINT AUTO_INCREMENT PRIMARY KEY,
            cache_key VARCHAR(255) NOT NULL,
            cache_type VARCHAR(50) NOT NULL,
            data_type VARCHAR(50),
            size_bytes BIGINT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            accessed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            ttl_seconds INT,
            hit_count BIGINT DEFAULT 0,
            INDEX idx_cache_key (cache_key),
            INDEX idx_cache_type (cache_type),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        """

        try:
            connection = self.mysql_pool.get_connection()
            cursor = connection.cursor()
            cursor.execute(create_table_sql)
            connection.commit()
            cursor.close()
            connection.close()

        except Exception as e:
            self.logger.error(f"创建元数据表失败: {e}")

    def _test_connection(self):
        """测试Redis连接"""
        try:
            self.redis_client.ping()
            self.logger.info(f"Redis connection established: {self.host}:{self.port}/{self.db}")
        except Exception as e:
            self.logger.error(f"Redis connection failed: {e}")
            raise
    
    def _generate_key(self, key: str, level: str = 'L2') -> str:
        """生成缓存键"""
        prefix = self.cache_levels[level]['prefix']
        return f"{self.key_prefix}{prefix}{key}"
    
    def _serialize_value(self, value: Any) -> bytes:
        """序列化值"""
        if isinstance(value, (str, int, float, bool)):
            return json.dumps(value).encode('utf-8')
        elif isinstance(value, (dict, list)):
            return json.dumps(value, default=str).encode('utf-8')
        elif isinstance(value, np.ndarray):
            return pickle.dumps(value)
        elif isinstance(value, pd.DataFrame):
            return pickle.dumps(value)
        else:
            return pickle.dumps(value)
    
    def _deserialize_value(self, data: bytes) -> Any:
        """反序列化值"""
        try:
            # 尝试JSON反序列化
            return json.loads(data.decode('utf-8'))
        except (json.JSONDecodeError, UnicodeDecodeError):
            # 使用pickle反序列化
            return pickle.loads(data)
    
    def set(
        self,
        key: str,
        value: Any,
        ttl: Optional[int] = None,
        level: str = 'L2',
        nx: bool = False,
        xx: bool = False
    ) -> bool:
        """
        设置缓存值
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 过期时间(秒)，None使用默认值
            level: 缓存级别
            nx: 仅当键不存在时设置
            xx: 仅当键存在时设置
            
        Returns:
            success: 是否设置成功
        """
        try:
            # 🔥 总是存储到L3内存缓存
            self._set_l3_cache(key, value, ttl)

            # 🔥 检查Redis是否可用
            if not self.enabled or not self.redis_client:
                with self._lock:
                    self.stats['sets'] += 1
                self.logger.debug(f"Cache set to L3 only: {key} (Redis disabled)")
                return True

            cache_key = self._generate_key(key, level)
            serialized_value = self._serialize_value(value)

            # 确定TTL
            if ttl is None:
                ttl = self.cache_levels[level]['ttl']

            # 设置Redis缓存
            if ttl > 0:
                result = self.redis_client.setex(
                    cache_key, ttl, serialized_value
                )
            else:
                result = self.redis_client.set(
                    cache_key, serialized_value, nx=nx, xx=xx
                )

            if result:
                with self._lock:
                    self.stats['sets'] += 1

                # 异步写入MySQL元数据
                if self.metadata_logging_enabled:
                    self._write_cache_metadata_async(key, level, value, ttl)

                self.logger.debug(f"Cache set: {cache_key} (level: {level}, ttl: {ttl})")
                return True

            return False

        except Exception as e:
            with self._lock:
                self.stats['errors'] += 1
            self.logger.error(f"Cache set error: {e}")
            # 即使Redis失败，L3缓存仍然可用
            return True
    
    def get(self, key: str, level: str = 'L2', default: Any = None) -> Any:
        """
        获取缓存值

        Args:
            key: 缓存键
            level: 缓存级别
            default: 默认值

        Returns:
            value: 缓存值或默认值
        """
        try:
            # 首先尝试L3内存缓存
            l3_value = self._get_l3_cache(key)
            if l3_value is not None:
                with self._lock:
                    self.stats['hits'] += 1
                    self.stats['l3_hits'] += 1
                self.logger.debug(f"L3 cache hit: {key}")
                return l3_value

            # 🔥 检查Redis是否可用
            if not self.enabled or not self.redis_client:
                with self._lock:
                    self.stats['misses'] += 1
                    self.stats['l3_misses'] += 1
                return default

            # 然后尝试Redis缓存
            cache_key = self._generate_key(key, level)
            data = self.redis_client.get(cache_key)

            if data is not None:
                with self._lock:
                    self.stats['hits'] += 1

                value = self._deserialize_value(data)

                # 回写到L3缓存
                self._set_l3_cache(key, value)

                self.logger.debug(f"Redis cache hit: {cache_key}")
                return value
            else:
                with self._lock:
                    self.stats['misses'] += 1
                    self.stats['l3_misses'] += 1

                self.logger.debug(f"Cache miss: {cache_key}")
                return default

        except Exception as e:
            with self._lock:
                self.stats['errors'] += 1
            self.logger.error(f"Cache get error: {e}")
            return default
    
    def _get_l3_cache(self, key: str) -> Any:
        """从L3内存缓存获取值"""
        try:
            with self.l3_lock:
                return self.l3_cache.get(key)
        except Exception as e:
            self.logger.debug(f"L3缓存获取失败: {e}")
            return None

    def _set_l3_cache(self, key: str, value: Any, ttl: Optional[int] = None):
        """设置L3内存缓存"""
        try:
            with self.l3_lock:
                self.l3_cache[key] = value
        except Exception as e:
            self.logger.debug(f"L3缓存设置失败: {e}")

    def _write_cache_metadata_async(self, key: str, level: str, value: Any, ttl: Optional[int]):
        """异步写入缓存元数据到MySQL"""
        if not self.mysql_pool:
            return

        def write_metadata():
            try:
                connection = self.mysql_pool.get_connection()
                cursor = connection.cursor()

                # 计算数据大小
                size_bytes = len(pickle.dumps(value)) if value is not None else 0
                data_type = type(value).__name__

                # 插入或更新元数据
                sql = """
                INSERT INTO cache_metadata
                (cache_key, cache_type, data_type, size_bytes, ttl_seconds, hit_count)
                VALUES (%s, %s, %s, %s, %s, 1)
                ON DUPLICATE KEY UPDATE
                accessed_at = CURRENT_TIMESTAMP,
                hit_count = hit_count + 1,
                size_bytes = VALUES(size_bytes),
                ttl_seconds = VALUES(ttl_seconds)
                """

                cursor.execute(sql, (key, level, data_type, size_bytes, ttl))
                connection.commit()
                cursor.close()
                connection.close()

                with self._lock:
                    self.stats['mysql_writes'] += 1

            except Exception as e:
                with self._lock:
                    self.stats['mysql_errors'] += 1
                self.logger.debug(f"MySQL元数据写入失败: {e}")

        # 在后台线程中执行
        threading.Thread(target=write_metadata, daemon=True).start()

    def get_multi_level(self, key: str, levels: List[str] = None) -> Tuple[Any, str]:
        """
        多级缓存获取
        
        Args:
            key: 缓存键
            levels: 缓存级别列表，按优先级排序
            
        Returns:
            value: 缓存值
            hit_level: 命中的缓存级别
        """
        if levels is None:
            levels = ['L1', 'L2', 'L3']
        
        for level in levels:
            value = self.get(key, level)
            if value is not None:
                # 如果在低级别缓存命中，提升到高级别
                if level != levels[0]:
                    self.set(key, value, level=levels[0])
                
                return value, level
        
        return None, None
    
    def set_multi_level(
        self,
        key: str,
        value: Any,
        levels: List[str] = None
    ) -> Dict[str, bool]:
        """
        多级缓存设置
        
        Args:
            key: 缓存键
            value: 缓存值
            levels: 缓存级别列表
            
        Returns:
            results: 各级别设置结果
        """
        if levels is None:
            levels = ['L1', 'L2', 'L3']
        
        results = {}
        for level in levels:
            results[level] = self.set(key, value, level=level)
        
        return results
    
    def delete(self, key: str, level: str = 'L2') -> bool:
        """删除缓存"""
        try:
            cache_key = self._generate_key(key, level)
            result = self.redis_client.delete(cache_key)
            
            if result:
                with self._lock:
                    self.stats['deletes'] += 1
                
                self.logger.debug(f"Cache deleted: {cache_key}")
                return True
            
            return False
            
        except Exception as e:
            with self._lock:
                self.stats['errors'] += 1
            self.logger.error(f"Cache delete error: {e}")
            return False
    
    def delete_pattern(self, pattern: str, level: str = 'L2') -> int:
        """按模式删除缓存"""
        try:
            cache_pattern = self._generate_key(pattern, level)
            keys = self.redis_client.keys(cache_pattern)
            
            if keys:
                deleted_count = self.redis_client.delete(*keys)
                with self._lock:
                    self.stats['deletes'] += deleted_count
                
                self.logger.info(f"Deleted {deleted_count} keys matching pattern: {cache_pattern}")
                return deleted_count
            
            return 0
            
        except Exception as e:
            with self._lock:
                self.stats['errors'] += 1
            self.logger.error(f"Cache delete pattern error: {e}")
            return 0
    
    def exists(self, key: str, level: str = 'L2') -> bool:
        """检查缓存是否存在"""
        try:
            cache_key = self._generate_key(key, level)
            return bool(self.redis_client.exists(cache_key))
        except Exception as e:
            self.logger.error(f"Cache exists check error: {e}")
            return False
    
    def expire(self, key: str, ttl: int, level: str = 'L2') -> bool:
        """设置缓存过期时间"""
        try:
            cache_key = self._generate_key(key, level)
            return bool(self.redis_client.expire(cache_key, ttl))
        except Exception as e:
            self.logger.error(f"Cache expire error: {e}")
            return False
    
    def ttl(self, key: str, level: str = 'L2') -> int:
        """获取缓存剩余过期时间"""
        try:
            cache_key = self._generate_key(key, level)
            return self.redis_client.ttl(cache_key)
        except Exception as e:
            self.logger.error(f"Cache TTL error: {e}")
            return -1
    
    def increment(self, key: str, amount: int = 1, level: str = 'L2') -> Optional[int]:
        """递增计数器"""
        try:
            cache_key = self._generate_key(key, level)
            return self.redis_client.incrby(cache_key, amount)
        except Exception as e:
            self.logger.error(f"Cache increment error: {e}")
            return None
    
    def decrement(self, key: str, amount: int = 1, level: str = 'L2') -> Optional[int]:
        """递减计数器"""
        try:
            cache_key = self._generate_key(key, level)
            return self.redis_client.decrby(cache_key, amount)
        except Exception as e:
            self.logger.error(f"Cache decrement error: {e}")
            return None
    
    def hash_set(self, name: str, mapping: Dict[str, Any], level: str = 'L2') -> bool:
        """设置哈希表"""
        try:
            cache_key = self._generate_key(name, level)
            
            # 序列化哈希表值
            serialized_mapping = {}
            for k, v in mapping.items():
                serialized_mapping[k] = self._serialize_value(v)
            
            result = self.redis_client.hset(cache_key, mapping=serialized_mapping)
            
            # 设置过期时间
            ttl = self.cache_levels[level]['ttl']
            if ttl > 0:
                self.redis_client.expire(cache_key, ttl)
            
            return bool(result)
            
        except Exception as e:
            self.logger.error(f"Hash set error: {e}")
            return False
    
    def hash_get(self, name: str, key: str, level: str = 'L2') -> Any:
        """获取哈希表值"""
        try:
            cache_key = self._generate_key(name, level)
            data = self.redis_client.hget(cache_key, key)
            
            if data is not None:
                return self._deserialize_value(data)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Hash get error: {e}")
            return None
    
    def hash_get_all(self, name: str, level: str = 'L2') -> Dict[str, Any]:
        """获取整个哈希表"""
        try:
            cache_key = self._generate_key(name, level)
            data = self.redis_client.hgetall(cache_key)
            
            result = {}
            for k, v in data.items():
                key = k.decode('utf-8') if isinstance(k, bytes) else k
                result[key] = self._deserialize_value(v)
            
            return result
            
        except Exception as e:
            self.logger.error(f"Hash get all error: {e}")
            return {}
    
    def list_push(self, name: str, *values, level: str = 'L2') -> Optional[int]:
        """向列表推入值"""
        try:
            cache_key = self._generate_key(name, level)
            
            # 序列化值
            serialized_values = [self._serialize_value(v) for v in values]
            
            result = self.redis_client.lpush(cache_key, *serialized_values)
            
            # 设置过期时间
            ttl = self.cache_levels[level]['ttl']
            if ttl > 0:
                self.redis_client.expire(cache_key, ttl)
            
            return result
            
        except Exception as e:
            self.logger.error(f"List push error: {e}")
            return None
    
    def list_pop(self, name: str, level: str = 'L2') -> Any:
        """从列表弹出值"""
        try:
            cache_key = self._generate_key(name, level)
            data = self.redis_client.lpop(cache_key)
            
            if data is not None:
                return self._deserialize_value(data)
            
            return None
            
        except Exception as e:
            self.logger.error(f"List pop error: {e}")
            return None
    
    def list_range(self, name: str, start: int = 0, end: int = -1, level: str = 'L2') -> List[Any]:
        """获取列表范围"""
        try:
            cache_key = self._generate_key(name, level)
            data_list = self.redis_client.lrange(cache_key, start, end)
            
            result = []
            for data in data_list:
                result.append(self._deserialize_value(data))
            
            return result
            
        except Exception as e:
            self.logger.error(f"List range error: {e}")
            return []
    
    def clear_level(self, level: str) -> int:
        """清空指定级别的缓存"""
        pattern = f"{self.key_prefix}{self.cache_levels[level]['prefix']}*"
        return self.delete_pattern(pattern.replace(self.key_prefix, ''), level)
    
    def clear_all(self) -> bool:
        """清空所有缓存"""
        try:
            pattern = f"{self.key_prefix}*"
            keys = self.redis_client.keys(pattern)
            
            if keys:
                deleted_count = self.redis_client.delete(*keys)
                self.logger.info(f"Cleared {deleted_count} cache keys")
                return True
            
            return True
            
        except Exception as e:
            self.logger.error(f"Clear all cache error: {e}")
            return False

    def get_memory_usage(self) -> Dict[str, Any]:
        """
        获取缓存内存使用情况

        Returns:
            memory_info: 内存使用信息字典
        """
        try:
            # 获取Redis内存信息
            redis_info = self.redis_client.info('memory')

            # 获取L3内存缓存大小
            l3_memory_size = sum(
                len(str(key)) + len(str(value))
                for key, value in self.l3_cache.items()
            )

            memory_info = {
                'redis_used_memory': redis_info.get('used_memory', 0),
                'redis_used_memory_human': redis_info.get('used_memory_human', '0B'),
                'redis_used_memory_peak': redis_info.get('used_memory_peak', 0),
                'redis_used_memory_peak_human': redis_info.get('used_memory_peak_human', '0B'),
                'redis_memory_fragmentation_ratio': redis_info.get('mem_fragmentation_ratio', 0),
                'l3_cache_memory_bytes': l3_memory_size,
                'l3_cache_entries': len(self.l3_cache),
                'total_memory_usage': redis_info.get('used_memory', 0) + l3_memory_size
            }

            return memory_info

        except Exception as e:
            self.logger.error(f"Failed to get memory usage: {e}")
            return {
                'redis_used_memory': 0,
                'redis_used_memory_human': '0B',
                'redis_used_memory_peak': 0,
                'redis_used_memory_peak_human': '0B',
                'redis_memory_fragmentation_ratio': 0,
                'l3_cache_memory_bytes': 0,
                'l3_cache_entries': 0,
                'total_memory_usage': 0
            }

    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        try:
            # 🔥 返回字典格式，便于访问
            total_hits = self.stats['hits']
            total_misses = self.stats['misses']
            total_requests = total_hits + total_misses

            hit_rate = (total_hits / total_requests) if total_requests > 0 else 0
            miss_rate = (total_misses / total_requests) if total_requests > 0 else 0

            stats_dict = {
                'hits': total_hits,
                'misses': total_misses,
                'hit_rate': hit_rate,
                'miss_rate': miss_rate,
                'l3_hits': self.stats['l3_hits'],
                'l3_misses': self.stats['l3_misses'],
                'sets': self.stats['sets'],
                'deletes': self.stats['deletes'],
                'errors': self.stats['errors'],
                'mysql_writes': self.stats['mysql_writes'],
                'mysql_errors': self.stats['mysql_errors']
            }

            # 如果Redis可用，添加Redis统计
            if self.enabled and self.redis_client:
                try:
                    info = self.redis_client.info()
                    stats_dict.update({
                        'total_keys': info.get('db0', {}).get('keys', 0),
                        'memory_usage': info.get('used_memory', 0),
                        'evicted_keys': info.get('evicted_keys', 0),
                        'expired_keys': info.get('expired_keys', 0)
                    })
                except:
                    pass

            return stats_dict

        except Exception as e:
            self.logger.error(f"Get stats error: {e}")
            return {
                'hits': 0, 'misses': 0, 'hit_rate': 0.0, 'miss_rate': 0.0,
                'l3_hits': 0, 'l3_misses': 0, 'sets': 0, 'deletes': 0,
                'errors': 0, 'mysql_writes': 0, 'mysql_errors': 0
            }
    
    def health_check(self) -> bool:
        """健康检查"""
        try:
            return self.redis_client.ping()
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False
    
    @contextmanager
    def pipeline(self):
        """Redis管道上下文管理器"""
        pipe = self.redis_client.pipeline()
        try:
            yield pipe
            pipe.execute()
        except Exception as e:
            self.logger.error(f"Pipeline error: {e}")
            raise
    
    async def get_async(self, key: str, level: str = 'L2') -> Any:
        """异步获取缓存值"""
        try:
            cache_key = self._generate_key(key, level)
            data = await asyncio.to_thread(self.redis_client.get, cache_key)

            if data is not None:
                with self._lock:
                    self.stats['hits'] += 1

                result = self._deserialize_value(data)
                self.logger.debug(f"Cache hit: {cache_key}")
                return result
            else:
                with self._lock:
                    self.stats['misses'] += 1

                self.logger.debug(f"Cache miss: {cache_key}")
                return None

        except Exception as e:
            with self._lock:
                self.stats['errors'] += 1
            self.logger.error(f"Cache async get error: {e}")
            return None

    async def set_async(self, key: str, value: Any, ttl: int = None, level: str = 'L2') -> bool:
        """异步设置缓存值"""
        try:
            cache_key = self._generate_key(key, level)
            serialized_value = self._serialize_value(value)

            # 使用级别配置的TTL
            if ttl is None:
                ttl = self.cache_levels[level]['ttl']

            # 设置缓存
            if ttl > 0:
                result = await asyncio.to_thread(
                    self.redis_client.setex,
                    cache_key, ttl, serialized_value
                )
            else:
                result = await asyncio.to_thread(
                    self.redis_client.set,
                    cache_key, serialized_value
                )

            if result:
                with self._lock:
                    self.stats['sets'] += 1

                self.logger.debug(f"Cache set: {cache_key}")
                return True

            return False

        except Exception as e:
            with self._lock:
                self.stats['errors'] += 1
            self.logger.error(f"Cache async set error: {e}")
            return False

    async def delete_async(self, key: str, level: str = 'L2') -> bool:
        """异步删除缓存值"""
        try:
            cache_key = self._generate_key(key, level)
            result = await asyncio.to_thread(self.redis_client.delete, cache_key)

            if result:
                with self._lock:
                    self.stats['deletes'] += 1

                self.logger.debug(f"Cache deleted: {cache_key}")
                return True

            return False

        except Exception as e:
            with self._lock:
                self.stats['errors'] += 1
            self.logger.error(f"Cache async delete error: {e}")
            return False

    async def exists_async(self, key: str, level: str = 'L2') -> bool:
        """异步检查缓存是否存在"""
        try:
            cache_key = self._generate_key(key, level)
            result = await asyncio.to_thread(self.redis_client.exists, cache_key)
            return bool(result)
        except Exception as e:
            self.logger.error(f"Cache async exists check error: {e}")
            return False

    def close(self):
        """关闭Redis连接"""
        try:
            self.redis_client.close()
            self.logger.info("Redis connection closed")
        except Exception as e:
            self.logger.error(f"Redis close error: {e}")


# 缓存装饰器
def cache_result(
    ttl: int = 3600,
    level: str = 'L2',
    key_func: Optional[Callable] = None
):
    """
    缓存结果装饰器
    
    Args:
        ttl: 过期时间
        level: 缓存级别
        key_func: 自定义键生成函数
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                # 默认键生成
                key_parts = [func.__name__]
                key_parts.extend(str(arg) for arg in args)
                key_parts.extend(f"{k}={v}" for k, v in sorted(kwargs.items()))
                cache_key = hashlib.md5(":".join(key_parts).encode()).hexdigest()
            
            # 尝试从缓存获取
            cache = RedisCache()  # 这里应该使用单例模式
            result = cache.get(cache_key, level)
            
            if result is not None:
                return result
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            cache.set(cache_key, result, ttl, level)
            
            return result
        
        return wrapper
    return decorator
