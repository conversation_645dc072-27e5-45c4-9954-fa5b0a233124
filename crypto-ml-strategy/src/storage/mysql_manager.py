"""
MySQL存储管理器
实现数据持久化、版本管理、查询优化等功能
"""

import mysql.connector
from mysql.connector import Error
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any
import logging
from dataclasses import dataclass
from datetime import datetime, timedelta
import json
import pickle
import base64
from contextlib import contextmanager
import threading
from sqlalchemy import create_engine, text
from sqlalchemy.pool import QueuePool

from src.utils.config import get_config_manager


@dataclass
class TableSchema:
    """表结构定义"""
    table_name: str
    columns: Dict[str, str]  # 列名: 数据类型
    primary_key: Optional[str] = None
    indexes: Optional[List[str]] = None
    foreign_keys: Optional[Dict[str, str]] = None


class MySQLManager:
    """
    MySQL存储管理器

    提供数据库连接管理、数据存储、查询优化等功能
    """

    _instance = None
    _initialized = False

    def __new__(cls, *args, **kwargs):
        """单例模式，避免重复初始化"""
        if cls._instance is None:
            cls._instance = super(MySQLManager, cls).__new__(cls)
        return cls._instance

    def __init__(
        self,
        host: str = "localhost",
        port: int = 3306,
        database: str = "crypto_ml_strategy",
        username: str = "root",
        password: str = "",
        pool_size: int = 10,
        max_overflow: int = 20,
        pool_timeout: int = 30,
        pool_recycle: int = 3600
    ):
        # 避免重复初始化
        if self._initialized:
            return

        # 初始化日志记录器
        self.logger = logging.getLogger(__name__)

        # 数据库连接配置
        config_manager = get_config_manager()
        mysql_config = config_manager.get('storage.mysql', {})

        # 检查是否启用MySQL
        self.enabled = mysql_config.get('enabled', True)

        # 🔥 修复：即使禁用也要初始化engine为None，避免close时出错
        self.engine = None

        if not self.enabled:
            self.logger.info("MySQL已禁用，跳过连接初始化")
            self._initialized = True
            return

        self.host = mysql_config.get('host', host)
        self.port = mysql_config.get('port', port)
        self.database = mysql_config.get('database', database)
        self.username = mysql_config.get('username', username)
        self.password = mysql_config.get('password', password)

        # 调试信息
        self.logger.debug(f"MySQL配置: host={self.host}, port={self.port}, database={self.database}, username={self.username}, password={'***' if self.password else 'None'}")
        self.logger.debug(f"原始配置: {mysql_config}")
        
        # 连接池配置
        self.pool_size = pool_size
        self.max_overflow = max_overflow
        self.pool_timeout = pool_timeout
        self.pool_recycle = pool_recycle
        
        # 初始化logger（必须在其他操作之前）
        self.logger = logging.getLogger(__name__)

        # SQLAlchemy引擎
        self.engine = None
        self._init_engine()

        # 表结构定义
        self.table_schemas = self._define_table_schemas()

        # 线程锁
        self._lock = threading.Lock()

        # 批量写入缓冲区
        self.metrics_buffer = []
        self.buffer_lock = threading.Lock()
        self.max_buffer_size = 100

        # 初始化数据库
        self._init_database()
    
    def _init_engine(self):
        """初始化SQLAlchemy引擎"""
        connection_string = (
            f"mysql+mysqlconnector://{self.username}:{self.password}"
            f"@{self.host}:{self.port}/{self.database}"
        )
        
        self.engine = create_engine(
            connection_string,
            poolclass=QueuePool,
            pool_size=self.pool_size,
            max_overflow=self.max_overflow,
            pool_timeout=self.pool_timeout,
            pool_recycle=self.pool_recycle,
            echo=False
        )
        
        self.logger.info(f"MySQL engine initialized: {self.host}:{self.port}/{self.database}")
    
    def _define_table_schemas(self) -> Dict[str, TableSchema]:
        """定义表结构"""
        schemas = {}
        
        # 1. 训练指标表
        schemas['training_metrics'] = TableSchema(
            table_name='training_metrics',
            columns={
                'id': 'BIGINT AUTO_INCREMENT',
                'model_id': 'VARCHAR(100)',
                'epoch': 'INT',
                'step': 'BIGINT',
                'timestamp': 'TIMESTAMP',
                'train_loss': 'FLOAT',
                'val_loss': 'FLOAT',
                'train_accuracy': 'FLOAT',
                'val_accuracy': 'FLOAT',
                'learning_rate': 'FLOAT',
                'gpu_memory': 'FLOAT',
                'batch_time': 'FLOAT',
                'additional_metrics': 'JSON',
                'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
            },
            primary_key='id',
            indexes=['model_id', 'timestamp', 'epoch']
        )
        
        # 2. 模型版本表
        schemas['model_versions'] = TableSchema(
            table_name='model_versions',
            columns={
                'id': 'BIGINT AUTO_INCREMENT',
                'version_id': 'VARCHAR(100) UNIQUE',
                'model_name': 'VARCHAR(100)',
                'model_config': 'JSON',
                'model_state': 'LONGBLOB',
                'performance_metrics': 'JSON',
                'training_samples': 'BIGINT',
                'drift_detected': 'BOOLEAN DEFAULT FALSE',
                'notes': 'TEXT',
                'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
                'updated_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'
            },
            primary_key='id',
            indexes=['version_id', 'model_name', 'created_at']
        )
        
        # 3. 风险评估表
        schemas['risk_assessments'] = TableSchema(
            table_name='risk_assessments',
            columns={
                'id': 'BIGINT AUTO_INCREMENT',
                'portfolio_id': 'VARCHAR(100)',
                'timestamp': 'TIMESTAMP',
                'overall_risk_score': 'FLOAT',
                'risk_level': 'VARCHAR(20)',
                'individual_risks': 'JSON',
                'risk_alerts': 'JSON',
                'recommendations': 'JSON',
                'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
            },
            primary_key='id',
            indexes=['portfolio_id', 'timestamp', 'risk_level']
        )
        
        # 4. 交易信号表
        schemas['trading_signals'] = TableSchema(
            table_name='trading_signals',
            columns={
                'id': 'BIGINT AUTO_INCREMENT',
                'symbol': 'VARCHAR(20)',
                'timestamp': 'TIMESTAMP',
                'signal_type': 'VARCHAR(10)',  # BUY, SELL, HOLD
                'confidence': 'FLOAT',
                'model_version': 'VARCHAR(100)',
                'features': 'JSON',
                'risk_score': 'FLOAT',
                'executed': 'BOOLEAN DEFAULT FALSE',
                'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
            },
            primary_key='id',
            indexes=['symbol', 'timestamp', 'signal_type', 'model_version']
        )
        
        # 5. 数据集版本表
        schemas['dataset_versions'] = TableSchema(
            table_name='dataset_versions',
            columns={
                'id': 'BIGINT AUTO_INCREMENT',
                'version_id': 'VARCHAR(100) UNIQUE',
                'dataset_name': 'VARCHAR(100)',
                'file_path': 'VARCHAR(500)',
                'file_size': 'BIGINT',
                'row_count': 'BIGINT',
                'column_count': 'INT',
                'data_quality_score': 'FLOAT',
                'preprocessing_config': 'JSON',
                'feature_list': 'JSON',
                'created_at': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP'
            },
            primary_key='id',
            indexes=['version_id', 'dataset_name', 'created_at']
        )
        
        return schemas
    
    def _init_database(self):
        """初始化数据库和表"""
        try:
            # 创建所有表
            for schema in self.table_schemas.values():
                self._create_table(schema)
            
            self.logger.info("Database initialization completed")
            # 标记初始化完成
            self._initialized = True

        except Exception as e:
            self.logger.error(f"Database initialization failed: {e}")
            raise
    
    def _create_table(self, schema: TableSchema):
        """创建表"""
        # 构建CREATE TABLE语句
        columns_sql = []
        for col_name, col_type in schema.columns.items():
            columns_sql.append(f"`{col_name}` {col_type}")
        
        if schema.primary_key:
            columns_sql.append(f"PRIMARY KEY (`{schema.primary_key}`)")
        
        create_sql = f"""
        CREATE TABLE IF NOT EXISTS `{schema.table_name}` (
            {', '.join(columns_sql)}
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
        """
        
        with self.engine.connect() as conn:
            conn.execute(text(create_sql))
            conn.commit()
        
        # 创建索引
        if schema.indexes:
            for index_col in schema.indexes:
                if index_col != schema.primary_key:  # 主键已经有索引
                    index_name = f"idx_{schema.table_name}_{index_col}"
                    index_sql = f"""
                    CREATE INDEX IF NOT EXISTS `{index_name}` 
                    ON `{schema.table_name}` (`{index_col}`)
                    """
                    try:
                        with self.engine.connect() as conn:
                            conn.execute(text(index_sql))
                            conn.commit()
                    except Exception as e:
                        # 索引可能已存在，忽略错误
                        pass
        
        self.logger.info(f"Table '{schema.table_name}' created/verified")
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接上下文管理器"""
        if not self.enabled:
            # 返回一个空的上下文管理器
            yield None
            return

        conn = None
        try:
            conn = self.engine.connect()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            self.logger.error(f"Database connection error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    def save_training_metrics(
        self,
        model_id: str,
        epoch: int,
        step: int,
        metrics: Dict[str, Any]
    ) -> bool:
        """保存训练指标"""
        if not self.enabled:
            self.logger.debug("MySQL已禁用，跳过训练指标保存")
            return True

        try:
            data = {
                'model_id': model_id,
                'epoch': epoch,
                'step': step,
                'timestamp': datetime.now(),
                'train_loss': metrics.get('train_loss'),
                'val_loss': metrics.get('val_loss'),
                'train_accuracy': metrics.get('train_accuracy'),
                'val_accuracy': metrics.get('val_accuracy'),
                'learning_rate': metrics.get('learning_rate'),
                'gpu_memory': metrics.get('gpu_memory'),
                'batch_time': metrics.get('batch_time'),
                'additional_metrics': json.dumps(metrics.get('additional_metrics', {}))
            }
            
            df = pd.DataFrame([data])
            df.to_sql(
                'training_metrics',
                self.engine,
                if_exists='append',
                index=False,
                method='multi'
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save training metrics: {e}")
            return False
    
    def save_model_version(
        self,
        version_id: str,
        model_name: str,
        model_config: Dict[str, Any],
        model_state: Dict[str, Any],
        performance_metrics: Dict[str, float],
        training_samples: int,
        drift_detected: bool = False,
        notes: str = ""
    ) -> bool:
        """保存模型版本"""
        try:
            # 序列化模型状态
            model_state_bytes = pickle.dumps(model_state)
            
            data = {
                'version_id': version_id,
                'model_name': model_name,
                'model_config': json.dumps(model_config),
                'model_state': model_state_bytes,
                'performance_metrics': json.dumps(performance_metrics),
                'training_samples': training_samples,
                'drift_detected': drift_detected,
                'notes': notes
            }
            
            # 使用原生SQL插入BLOB数据
            insert_sql = """
            INSERT INTO model_versions 
            (version_id, model_name, model_config, model_state, performance_metrics, 
             training_samples, drift_detected, notes)
            VALUES (%(version_id)s, %(model_name)s, %(model_config)s, %(model_state)s, 
                    %(performance_metrics)s, %(training_samples)s, %(drift_detected)s, %(notes)s)
            ON DUPLICATE KEY UPDATE
            model_config = VALUES(model_config),
            model_state = VALUES(model_state),
            performance_metrics = VALUES(performance_metrics),
            training_samples = VALUES(training_samples),
            drift_detected = VALUES(drift_detected),
            notes = VALUES(notes),
            updated_at = CURRENT_TIMESTAMP
            """
            
            with self.get_connection() as conn:
                conn.execute(text(insert_sql), data)
                conn.commit()
            
            self.logger.info(f"Model version saved: {version_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save model version: {e}")
            return False
    
    def load_model_version(self, version_id: str) -> Optional[Dict[str, Any]]:
        """加载模型版本"""
        try:
            query = """
            SELECT version_id, model_name, model_config, model_state, 
                   performance_metrics, training_samples, drift_detected, 
                   notes, created_at, updated_at
            FROM model_versions 
            WHERE version_id = %s
            """
            
            with self.get_connection() as conn:
                result = conn.execute(text(query), {'version_id': version_id}).fetchone()
            
            if result:
                # 反序列化模型状态
                model_state = pickle.loads(result.model_state)
                
                return {
                    'version_id': result.version_id,
                    'model_name': result.model_name,
                    'model_config': json.loads(result.model_config),
                    'model_state': model_state,
                    'performance_metrics': json.loads(result.performance_metrics),
                    'training_samples': result.training_samples,
                    'drift_detected': result.drift_detected,
                    'notes': result.notes,
                    'created_at': result.created_at,
                    'updated_at': result.updated_at
                }
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to load model version: {e}")
            return None
    
    def save_risk_assessment(
        self,
        portfolio_id: str,
        risk_assessment: Dict[str, Any]
    ) -> bool:
        """保存风险评估"""
        try:
            data = {
                'portfolio_id': portfolio_id,
                'timestamp': datetime.now(),
                'overall_risk_score': risk_assessment.get('overall_risk_score'),
                'risk_level': risk_assessment.get('risk_level'),
                'individual_risks': json.dumps(risk_assessment.get('individual_risks', {})),
                'risk_alerts': json.dumps(risk_assessment.get('risk_alerts', [])),
                'recommendations': json.dumps(risk_assessment.get('recommendations', []))
            }
            
            df = pd.DataFrame([data])
            df.to_sql(
                'risk_assessments',
                self.engine,
                if_exists='append',
                index=False,
                method='multi'
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save risk assessment: {e}")
            return False
    
    def save_trading_signal(
        self,
        symbol: str,
        signal_type: str,
        confidence: float,
        model_version: str,
        features: Dict[str, Any],
        risk_score: float
    ) -> bool:
        """保存交易信号"""
        try:
            data = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'signal_type': signal_type,
                'confidence': confidence,
                'model_version': model_version,
                'features': json.dumps(features),
                'risk_score': risk_score,
                'executed': False
            }
            
            df = pd.DataFrame([data])
            df.to_sql(
                'trading_signals',
                self.engine,
                if_exists='append',
                index=False,
                method='multi'
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to save trading signal: {e}")
            return False
    
    def get_training_metrics(
        self,
        model_id: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 1000
    ) -> pd.DataFrame:
        """获取训练指标"""
        try:
            query = """
            SELECT * FROM training_metrics 
            WHERE model_id = %(model_id)s
            """
            params = {'model_id': model_id}
            
            if start_time:
                query += " AND timestamp >= %(start_time)s"
                params['start_time'] = start_time
            
            if end_time:
                query += " AND timestamp <= %(end_time)s"
                params['end_time'] = end_time
            
            query += " ORDER BY timestamp DESC LIMIT %(limit)s"
            params['limit'] = limit
            
            df = pd.read_sql(query, self.engine, params=params)
            return df
            
        except Exception as e:
            self.logger.error(f"Failed to get training metrics: {e}")
            return pd.DataFrame()
    
    def get_model_versions(
        self,
        model_name: Optional[str] = None,
        limit: int = 50
    ) -> pd.DataFrame:
        """获取模型版本列表"""
        try:
            query = """
            SELECT version_id, model_name, performance_metrics, training_samples,
                   drift_detected, created_at, updated_at
            FROM model_versions
            """
            params = {}
            
            if model_name:
                query += " WHERE model_name = %(model_name)s"
                params['model_name'] = model_name
            
            query += " ORDER BY created_at DESC LIMIT %(limit)s"
            params['limit'] = limit
            
            df = pd.read_sql(query, self.engine, params=params)
            return df
            
        except Exception as e:
            self.logger.error(f"Failed to get model versions: {e}")
            return pd.DataFrame()
    
    def cleanup_old_data(self, days_to_keep: int = 30):
        """清理旧数据"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            
            # 清理旧的训练指标
            with self.get_connection() as conn:
                result = conn.execute(
                    text("DELETE FROM training_metrics WHERE created_at < %s"),
                    {'cutoff_date': cutoff_date}
                )
                deleted_metrics = result.rowcount
                
                # 清理旧的风险评估
                result = conn.execute(
                    text("DELETE FROM risk_assessments WHERE created_at < %s"),
                    {'cutoff_date': cutoff_date}
                )
                deleted_assessments = result.rowcount
                
                # 清理旧的交易信号
                result = conn.execute(
                    text("DELETE FROM trading_signals WHERE created_at < %s"),
                    {'cutoff_date': cutoff_date}
                )
                deleted_signals = result.rowcount
                
                conn.commit()
            
            self.logger.info(
                f"Cleaned up old data: {deleted_metrics} metrics, "
                f"{deleted_assessments} assessments, {deleted_signals} signals"
            )
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old data: {e}")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            stats = {}
            
            for table_name in self.table_schemas.keys():
                with self.get_connection() as conn:
                    # 获取行数
                    result = conn.execute(
                        text(f"SELECT COUNT(*) as count FROM {table_name}")
                    ).fetchone()
                    row_count = result.count if result else 0
                    
                    # 获取表大小
                    result = conn.execute(text("""
                        SELECT 
                            ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                        FROM information_schema.TABLES 
                        WHERE table_schema = DATABASE() AND table_name = %s
                    """), {'table_name': table_name}).fetchone()
                    
                    size_mb = result.size_mb if result else 0
                    
                    stats[table_name] = {
                        'row_count': row_count,
                        'size_mb': size_mb
                    }
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get database stats: {e}")
            return {}
    
    def close(self):
        """关闭数据库连接"""
        try:
            # 🔥 修复：安全检查engine属性是否存在且不为None
            if hasattr(self, 'engine') and self.engine is not None:
                self.engine.dispose()
                self.logger.info("MySQL connection closed")
            elif hasattr(self, 'enabled') and not self.enabled:
                self.logger.debug("MySQL was disabled, no connection to close")
            else:
                self.logger.debug("No MySQL engine to close")
        except Exception as e:
            self.logger.error(f"Error closing MySQL connection: {e}")
