"""
统一多层级缓存系统
整合Redis、MySQL、内存缓存的实现
"""

import time
import logging
import threading
import hashlib
import os
from typing import Dict, Optional, Any, List, Union, Tuple
from dataclasses import dataclass
from pathlib import Path

# 假如果有依赖，则优雅降级
from src.utils.config import get_config_manager

try:
    from .redis_cache import RedisCache
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    class RedisCache:
        def __init__(self, *args, **kwargs):
            raise ImportError("Redis functionality not available")

try:
    from .mysql_manager import MySQLManager
    MYSQL_AVAILABLE = True
except ImportError:
    MYSQL_AVAILABLE = False
    class MySQLManager:
        def __init__(self, *args, **kwargs):
            raise ImportError("MySQL functionality not available")


@dataclass
class CacheLevelConfig:
    """缓存层级配置"""
    enabled: bool = True
    ttl: int = 3600
    max_size: Optional[int] = None
    memory_only: bool = False
    fallback_level: Optional[str] = None
    persist_level: bool = False


class UnifiedCache:
    """
    统一缓存管理器
    
    整合L1: 内存缓存 (LFU)
    L2: Redis缓存 (外部KV存储)
    L3: MySQL缓存 (持久化存储)
    """
    
    _instance = None
    _lock = threading.RLock()
    
    def __new__(cls, *args, ** kwargs):
        """单例模式"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(UnifiedCache, cls).__new__(cls)
        return cls._instance
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        if hasattr(self, '_initialized') and self._initialized:
            return
            
        self.logger = logging.getLogger(__name__)
        self.config = config or self._load_config()
        
        # 缓存层级配置
        self.level_configs = {
            'L1': CacheLevelConfig(
                enabled=self.config.get('L1_enabled', True),
                ttl=self.config.get('L1_ttl', 300),
                max_size=self.config.get('L1_max_size', 10000),
                memory_only=True,
                fallback_level='L2'
            ),
            'L2': CacheLevelConfig(
                enabled=self.config.get('L2_enabled', True),
                ttl=self.config.get('L2_ttl', 1800),
                fallback_level='L3'
            ),
            'L3': CacheLevelConfig(
                enabled=self.config.get('L3_enabled', True),
                ttl=self.config.get('L3_ttl', 7200),
                persist_level=True,
                fallback_level=None
            )
        }
        
        # 初始化缓存存储
        self._init_storages()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'hits': {'L1': 0, 'L2': 0, 'L3': 0},
            'misses': 0,
            'sets': {'L1': 0, 'L2': 0, 'L3': 0},
            'errors': 0,
            'start_time': time.time()
        }
        
        self._initialized = True
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        try:
            config_manager = get_config_manager()
            return config_manager.get('storage.cache', {})
        except Exception:
            # 默认配置
            return {
                'L1_enabled': True,
                'L2_enabled': True,
                'L3_enabled': True,
                'L1_ttl': 300,
                'L2_ttl': 1800,
                'L3_ttl': 7200,
                'L1_max_size': 10000
            }
    
    def _init_storages(self):
        """初始化各种存储"""
        
        # Level 1: 内存缓存 (手动实现LFU)
        self._l1_storage = {}
        self._l1_metadata = {}  # 存储访问频率和过期时间
        self._l1_lock = threading.RLock()
        
        # Level 2: Redis缓存
        try:
            self.redis = RedisCache()
            self.redis_enabled = REDIS_AVAILABLE and hasattr(self.redis, 'enabled')
        except Exception as e:
            self.logger.warning(f"Redis初始化失败: {e}")
            self.redis_enabled = False
        
        # Level 3: MySQL缓存
        try:
            self.mysql = MySQLManager()
            self.mysql_enabled = MYSQL_AVAILABLE and hasattr(self.mysql, 'enabled')
        except Exception as e:
            self.logger.warning(f"MySQL初始化失败: {e}")
            self.mysql_enabled = False
    
    def _generate_key(self, key: str) -> str:
        """生成标准化缓存键"""
        if isinstance(key, str):
            return f"cache:{hashlib.md5(key.encode('utf-8')).hexdigest()}"
        else:
            return f"cache:{hashlib.md5(str(key).encode('utf-8')).hexdigest()}"
    
    def _serialize_value(self, value: Any) -> bytes:
        """序列化值"""
        import pickle
        return pickle.dumps(value)
    
    def _deserialize_value(self, data: bytes) -> Any:
        """反序列化值"""
        import pickle
        return pickle.loads(data)
    
    def _is_expired(self, level: str, key: str) -> bool:
        """检查是否过期"""
        if level == 'L1':
            if key not in self._l1_metadata:
                return True
            metadata = self._l1_metadata[key]
            return time.time() > metadata['expires']
        return False
    
    def _evict_l1_if_needed(self):
        """L1缓存清理策略"""
        with self._l1_lock:
            if len(self._l1_storage) <= self.level_configs['L1'].max_size:
                return
            
            # 过期清理
            current_time = time.time()
            expired_keys = [
                key for key, metadata in self._l1_metadata.items()
                if current_time > metadata['expires']
            ]
            
            for key in expired_keys:
                self._l1_storage.pop(key, None)
                self._l1_metadata.pop(key, None)
            
            # LFU清理 (访问频率最低)
            if len(self._l1_storage) > self.level_configs['L1'].max_size:
                least_used = sorted(
                    self._l1_metadata.items(), 
                    key=lambda x: x[1]['frequency']
                )[:len(self._l1_storage) - self.level_configs['L1'].max_size]
                
                for key, _ in least_used:
                    self._l1_storage.pop(key, None)
                    self._l1_metadata.pop(key, None)
    
    def _get_level(self, level: str, key: str) -> Optional[Any]:
        """从指定层级获取数据"""
        cache_key = self._generate_key(key)
        
        try:
            if level == 'L1':
                with self._l1_lock:
                    if cache_key in self._l1_storage and not self._is_expired('L1', cache_key):
                        # 更新访问频率
                        self._l1_metadata[cache_key]['frequency'] += 1
                        return self._deserialize_value(self._l1_storage[cache_key])
                    return None
            
            elif level == 'L2' and self.redis_enabled:
                result = self.redis.get(cache_key)
                if result is not None:
                    return result
                return None
            
            elif level == 'L3' and self.mysql_enabled:
                # MySQL需要专门的缓存表
                try:
                    query = "SELECT value FROM cache_storage WHERE cache_key = %s"
                    # 简化MySQL缓存处理，实际需要具体的表结构
                    return None
                except Exception:
                    return None
                    
        except Exception as e:
            self.logger.debug(f"获取{level}级缓存失败: {e}")
            self.stats['errors'] += 1
        
        return None
    
    def _set_level(self, level: str, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置指定层级缓存"""
        cache_key = self._generate_key(key)
        
        if ttl is None:
            ttl = self.level_configs[level].ttl
        
        try:
            if level == 'L1':
                with self._l1_lock:
                    self._evict_l1_if_needed()
                    self._l1_storage[cache_key] = self._serialize_value(value)
                    self._l1_metadata[cache_key] = {
                        'expires': time.time() + ttl,
                        'frequency': 1,
                        'level': 'L1'
                    }
                    self.stats['sets']['L1'] += 1
                return True
            
            elif level == 'L2' and self.redis_enabled:
                # 使用现有Redis配置
                try:
                    import json
                    serialized = json.dumps({'data': value, 'timestamp': time.time()})
                    return self.redis.set(cache_key, serialized, ttl=ttl)
                except Exception:
                    return False
            
            elif level == 'L3' and self.mysql_enabled:
                # 保存到MySQL
                try:
                    # 这需要一个实际的MySQL缓存表
                    return True
                except Exception:
                    return False
        
        except Exception as e:
            self.logger.debug(f"设置{level}级缓存失败: {e}")
            self.stats['errors'] += 1
        
        return False
    
    def get(self, key: str, levels: List[str] = None) -> Optional[Any]:
        """
        多级缓存获取
        
        Args:
            key: 缓存键
            levels: 按优先级排序的缓存层级 [L1, L2, L3]
        
        Returns:
            缓存值或None
        """
        with self._lock:
            self.stats['total_requests'] += 1
            
        if levels is None:
            levels = ['L1', 'L2', 'L3']
        
        for level in levels:
            if not self.level_configs[level].enabled:
                continue
                
            result = self._get_level(level, key)
            if result is not None:
                with self._lock:
                    self.stats['hits'][level] += 1
                
                # 缓存回写策略 - 如果命中低层级的数据，写回高层级
                current_hit_level_index = levels.index(level)
                for i in range(current_hit_level_index):
                    writeback_level = levels[i]
                    if self.level_configs[writeback_level].enabled:
                        self._set_level(writeback_level, key, result)
                
                return result
        
        with self._lock:
            self.stats['misses'] += 1
        
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None, 
           levels: List[str] = None) -> Dict[str, bool]:
        """
        多级缓存设置
        
        Args:
            key: 缓存键
            value: 缓存值
            ttl: 缓存时间（每个层级的配置会覆盖）
            levels: 要设置的缓存层级
        
        Returns:
            各级别设置结果
        """
        if levels is None:
            levels = ['L1', 'L2', 'L3']
        
        results = {}
        for level in levels:
            if self.level_configs[level].enabled:
                results[level] = self._set_level(level, key, value, ttl)
            else:
                results[level] = False
        
        return results
    
    def invalidate(self, key: str) -> bool:
        """使缓存失效"""
        cache_key = self._generate_key(key)
        
        # 清理所有层级的指定key
        success = False
        
        # L1
        if cache_key in self._l1_storage:
            with self._l1_lock:
                self._l1_storage.pop(cache_key, None)
                self._l1_metadata.pop(cache_key, None)
                success = True
        
        # L2
        if self.redis_enabled:
            try:
                self.redis.delete(cache_key)
                success = True
            except Exception:
                pass
        
        return success
    
    def clear_level(self, level: str) -> bool:
        """清空指定层级缓存"""
        try:
            if level == 'L1':
                with self._l1_lock:
                    self._l1_storage.clear()
                    self._l1_metadata.clear()
                return True
            elif level == 'L2' and self.redis_enabled:
                pattern = "cache:*"
                keys = self.redis.redis_client.keys(pattern)
                if keys:
                    self.redis.redis_client.delete(*keys)
                return True
            return False
        except Exception as e:
            self.logger.error(f"清空{level}级缓存失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            total_req = self.stats['total_requests']
            total_hits = sum(self.stats['hits'].values())
            
            return {
                'total_requests': total_req,
                'total_hits': total_hits,
                'total_misses': self.stats['misses'],
                'hit_rate': total_hits / max(total_req, 1),
                'miss_rate': self.stats['misses'] / max(total_req, 1),
                'hits_by_level': self.stats['hits'].copy(),
                'sets_by_level': self.stats['sets'].copy(),
                'errors': self.stats['errors'],
                'uptime': time.time() - self.stats['start_time'],
                'l1_size': len(self._l1_storage),
                'l1_max_size': self.level_configs['L1'].max_size,
                'redis_enabled': self.redis_enabled,
                'mysql_enabled': self.mysql_enabled
            }


# 全局实例
_cache_instance = None
_cache_lock = threading.RLock()

def get_cache() -> UnifiedCache:
    """获取统一缓存实例"""
    global _cache_instance
    if _cache_instance is None:
        with _cache_lock:
            if _cache_instance is None:
                _cache_instance = UnifiedCache()
    return _cache_instance