"""
统一信号融合深度学习模型
基于qlib架构设计，集成多种策略信号的深度学习模型
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.nn import MultiheadAttention, TransformerEncoder, TransformerEncoderLayer
import numpy as np
from typing import Dict, Tuple, Optional, List
import logging

from .base_model import BaseDeepLearningModel
# Removed obsolete import: from src.utils.gpu_utils import get_device, move_to_device


class UnifiedSignalFusionModel(BaseDeepLearningModel):
    """
    统一信号融合模型
    
    将LPPL、Hematread、BMSB、SuperTrend等策略信号作为特征输入，
    使用深度学习网络进行信号融合和决策
    """
    
    def __init__(
        self,
        feature_dim: int = 64,
        hidden_dims: List[int] = [256, 128, 64],
        num_heads: int = 8,
        num_layers: int = 3,
        dropout: float = 0.2,
        sequence_length: int = 20,
        output_dim: int = 3,  # BUY, SELL, HOLD
        use_transformer: bool = True,
        use_lstm: bool = True,
        use_attention: bool = True,
        use_residual: bool = True,
        use_layer_norm: bool = True,
        activation: str = 'gelu',
        device: Optional[str] = "cpu", # Changed: device is now a required argument
        # 🔥 GPU优化参数
        intermediate_size: int = None,
        max_position_embeddings: int = 512,
        use_gradient_checkpointing: bool = False,
        **kwargs
    ):
        # Changed: Pass device to parent constructor
        super().__init__(device=device, **kwargs)

        self.feature_dim = feature_dim
        self.hidden_dims = hidden_dims
        self.num_heads = num_heads
        self.num_layers = num_layers
        self.dropout = dropout
        self.sequence_length = sequence_length
        self.output_dim = output_dim
        self.use_transformer = use_transformer
        self.use_lstm = use_lstm
        self.use_attention = use_attention
        self.use_residual = use_residual
        self.use_layer_norm = use_layer_norm
        self.activation = activation

        # 设备配置由父类处理
        # self.device is now set by BaseDeepLearningModel

        # 🔥 GPU优化参数
        self.intermediate_size = intermediate_size or (hidden_dims[0] * 4)  # 默认4倍隐藏层大小
        self.max_position_embeddings = max_position_embeddings
        self.use_gradient_checkpointing = use_gradient_checkpointing

        # 激活函数
        self.activation_fn = self._get_activation_fn(activation)

        # 构建模型组件
        self._build_model()
        
        # 移动到设备
        self.to(self.device)
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"UnifiedSignalFusionModel initialized on {self.device}")

    def update_feature_dim(self, new_feature_dim: int):
        """动态更新模型的输入特征维度并重建相关层。"""
        if new_feature_dim != self.feature_dim:
            self.logger.info(f"Updating feature dimension from {self.feature_dim} to {new_feature_dim}")
            self.feature_dim = new_feature_dim
            # 重建依赖于 feature_dim 的组件
            self.feature_encoder = self._build_feature_encoder().to(self.device)
            self.logger.info("Feature encoder has been rebuilt with the new dimension.")

    def _get_activation_fn(self, activation: str):
        """获取激活函数"""
        activation_map = {
            'relu': nn.ReLU(),
            'gelu': nn.GELU(),
            'swish': nn.SiLU(),
            'tanh': nn.Tanh(),
            'leaky_relu': nn.LeakyReLU(0.1)
        }
        return activation_map.get(activation, nn.GELU())

    def _build_model(self):
        """构建模型架构"""
        
        # 1. 特征编码器
        self.feature_encoder = self._build_feature_encoder()
        
        # 2. 注意力机制 - 修复维度匹配
        if self.use_attention:
            # 使用特征编码器的输出维度
            attention_dim = self.hidden_dims[-1]
            self.attention_layer = MultiheadAttention(
                embed_dim=attention_dim,
                num_heads=self.num_heads,
                dropout=self.dropout,
                batch_first=True
            )
        
        # 3. Transformer编码器 - 修复CUDA配置问题
        if self.use_transformer:
            # 使用特征编码器的输出维度
            transformer_dim = self.hidden_dims[-1]

            # 确保维度至少为8，且为8的倍数（适配CUDA kernel）
            if transformer_dim < 8:
                self.logger.warning(f"⚠️ Transformer维度({transformer_dim})过小，调整为8")
                transformer_dim = 8
                self.hidden_dims[-1] = 8
            elif transformer_dim % 8 != 0:
                # 调整为8的倍数，避免CUDA kernel配置错误
                transformer_dim = ((transformer_dim + 7) // 8) * 8
                self.hidden_dims[-1] = transformer_dim
                self.logger.info(f"🔧 调整Transformer维度为8的倍数: {transformer_dim}")

            # 动态调整头数以确保维度兼容性和CUDA优化
            original_heads = self.num_heads
            # 优先选择能整除且适合CUDA的头数
            valid_heads = [h for h in [8, 4, 2, 1] if transformer_dim % h == 0 and transformer_dim // h >= 8]
            if valid_heads:
                self.num_heads = valid_heads[0]  # 选择最大的有效头数
            else:
                # 如果没有找到合适的头数，强制设置为1
                self.num_heads = 1
                self.logger.warning(f"⚠️ 强制设置头数为1以避免CUDA错误")

            if original_heads != self.num_heads:
                self.logger.info(f"🔧 调整注意力头数: {original_heads} -> {self.num_heads}")

            # 确保前馈网络维度为8的倍数
            dim_feedforward = max(transformer_dim * 2, 64)
            dim_feedforward = ((dim_feedforward + 7) // 8) * 8  # 调整为8的倍数

            # 验证配置的有效性
            head_dim = transformer_dim // self.num_heads
            if head_dim < 8:
                self.logger.error(f"❌ 每个注意力头的维度({head_dim})过小，最小需要8")
                self.use_transformer = False
                self.logger.warning("⚠️ 禁用Transformer以避免CUDA错误")
            else:
                try:
                    # 额外的CUDA兼容性检查
                    if transformer_dim % 8 != 0 or dim_feedforward % 8 != 0:
                        self.logger.warning(f"⚠️ 维度不是8的倍数，强制调整: dim={transformer_dim}, ff={dim_feedforward}")
                        transformer_dim = ((transformer_dim + 7) // 8) * 8
                        dim_feedforward = ((dim_feedforward + 7) // 8) * 8
                        self.hidden_dims[-1] = transformer_dim

                    # 最终验证：确保所有参数都符合CUDA要求
                    final_head_dim = transformer_dim // self.num_heads
                    if final_head_dim < 8 or transformer_dim < 8 or dim_feedforward < 8:
                        self.logger.error(f"❌ 最终验证失败: dim={transformer_dim}, heads={self.num_heads}, head_dim={final_head_dim}, ff={dim_feedforward}")
                        self.use_transformer = False
                        self.logger.warning("⚠️ 禁用Transformer以避免CUDA错误")
                    else:
                        # 使用更保守的配置以避免CUDA错误
                        encoder_layer = TransformerEncoderLayer(
                            d_model=transformer_dim,
                            nhead=self.num_heads,
                            dim_feedforward=dim_feedforward,
                            dropout=self.dropout,
                            batch_first=True,
                            norm_first=False,  # 使用Post-LN，更稳定
                            activation='relu'  # 使用ReLU，避免GELU的CUDA问题
                        )
                        self.transformer_encoder = TransformerEncoder(
                            encoder_layer,
                            num_layers=min(self.num_layers, 2)  # 限制层数以减少内存使用
                        )
                        self.logger.info(f"✅ Transformer编码器初始化成功: dim={transformer_dim}, heads={self.num_heads}, head_dim={final_head_dim}, ff_dim={dim_feedforward}")

                except Exception as e:
                    self.logger.error(f"❌ Transformer编码器初始化失败: {e}")
                    # 禁用Transformer作为fallback
                    self.use_transformer = False
                    self.logger.warning("⚠️ 已禁用Transformer编码器，使用其他组件")
        
        # 4. LSTM层 - 修复输入维度匹配
        if self.use_lstm:
            # 使用特征编码器的输出维度作为LSTM输入
            lstm_input_dim = self.hidden_dims[-1]
            self.lstm = nn.LSTM(
                input_size=lstm_input_dim,
                hidden_size=self.hidden_dims[-1],
                num_layers=2,
                dropout=self.dropout,
                batch_first=True,
                bidirectional=True
            )
            lstm_output_dim = self.hidden_dims[-1] * 2  # 双向LSTM
        else:
            # 如果不使用LSTM，输出维度是特征编码器的最后一层
            lstm_output_dim = self.hidden_dims[-1]

        # 5. 信号融合网络
        self.fusion_network = self._build_fusion_network(lstm_output_dim)
        
        # 6. 输出层
        self.output_layer = self._build_output_layer()
        
        # 7. 置信度估计层
        self.confidence_layer = nn.Sequential(
            nn.Linear(self.hidden_dims[-1], self.hidden_dims[-1] // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dims[-1] // 2, 1),
            nn.Sigmoid()
        )
    
    def _build_feature_encoder(self) -> nn.Module:
        """构建特征编码器"""
        layers = []
        input_dim = self.feature_dim
        
        for hidden_dim in self.hidden_dims:
            layers.extend([
                nn.Linear(input_dim, hidden_dim),
                nn.BatchNorm1d(hidden_dim),
                nn.ReLU(),
                nn.Dropout(self.dropout)
            ])
            input_dim = hidden_dim
        
        return nn.Sequential(*layers)
    
    def _build_fusion_network(self, input_dim: int) -> nn.Module:
        """构建信号融合网络"""
        return nn.Sequential(
            nn.Linear(input_dim, self.hidden_dims[-1]),
            nn.BatchNorm1d(self.hidden_dims[-1]),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dims[-1], self.hidden_dims[-1]),
            nn.ReLU()
        )
    
    def _build_output_layer(self) -> nn.Module:
        """构建输出层"""
        return nn.Sequential(
            nn.Linear(self.hidden_dims[-1], self.hidden_dims[-1] // 2),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dims[-1] // 2, self.output_dim)
        )
    
    def forward(
        self, 
        features: torch.Tensor,
        return_attention: bool = False
    ) -> Dict[str, torch.Tensor]:
        """
        前向传播
        
        Args:
            features: 输入特征 [batch_size, sequence_length, feature_dim]
            return_attention: 是否返回注意力权重
            
        Returns:
            Dict包含:
                - signal: 交易信号 [batch_size, output_dim]
                - confidence: 置信度 [batch_size, 1]
                - attention_weights: 注意力权重 (可选)
        """
        # 🔥 处理不同维度的输入
        if features.dim() == 2:
            # 2D输入: (batch_size, feat_dim)
            # feature_encoder可以直接处理2D数据，无需序列化
            encoded_features = self.feature_encoder(features)
            # 为后续层准备3D输出 (batch_size, 1, hidden_dim)
            encoded_features = encoded_features.unsqueeze(1)
            batch_size, _, _ = encoded_features.shape
        elif features.dim() == 3:
            # 3D输入: (batch_size, seq_len, feat_dim)
            batch_size, seq_len, feat_dim = features.shape
            features_flat = features.view(-1, feat_dim)
            encoded_features_flat = self.feature_encoder(features_flat)
            encoded_features = encoded_features_flat.view(batch_size, seq_len, -1)
        else:
            raise ValueError(f"输入特征维度不支持: {features.shape}，期望2D或3D")

        # 处理batch_size=1的情况，设置为eval模式避免BatchNorm问题
        was_training = False
        if batch_size == 1 and self.training:
            self.eval()
            was_training = True

        try:
            # 序列处理层 (Attention, Transformer, LSTM)
            # 注意：对于2D输入，序列长度为1
            sequence_output = encoded_features[:, -1, :] # 取序列的最后一个输出

            # 2. 注意力机制
            attention_weights = None
            if self.use_attention and encoded_features.shape[1] > 1:
                # 🔥 修复：根据是否需要返回注意力权重来决定参数
                need_weights = return_attention
                attention_output = self.attention_layer(
                    encoded_features, encoded_features, encoded_features,
                    need_weights=need_weights
                )
                if isinstance(attention_output, tuple):
                    attended_features, attention_weights = attention_output
                else:
                    attended_features = attention_output
                encoded_features = attended_features + encoded_features  # 残差连接

            # 3. Transformer编码 - 修复CUDA配置问题
            if self.use_transformer and hasattr(self, 'transformer_encoder') and encoded_features.shape[1] > 1:
                try:
                    # 确保输入形状正确
                    if encoded_features.dim() != 3:
                        self.logger.error(f"❌ Transformer输入维度错误: {encoded_features.shape}, 期望3维")
                        raise ValueError(f"Expected 3D input for Transformer, got {encoded_features.shape}")

                    # 检查特征维度并调整
                    current_dim = encoded_features.shape[-1]
                    expected_dim = self.hidden_dims[-1]

                    if current_dim != expected_dim:
                        self.logger.warning(f"⚠️ 特征维度不匹配: {current_dim} vs {expected_dim}, 进行调整")
                        # 使用线性层调整维度
                        if not hasattr(self, 'dim_adapter'):
                            self.dim_adapter = nn.Linear(current_dim, expected_dim).to(self.device)
                        encoded_features = self.dim_adapter(encoded_features)

                    # 确保输入是连续的内存布局，避免CUDA错误
                    encoded_features = encoded_features.contiguous()

                    # 检查输入是否包含NaN或Inf
                    if torch.isnan(encoded_features).any() or torch.isinf(encoded_features).any():
                        self.logger.warning("⚠️ 检测到NaN或Inf值，进行清理")
                        encoded_features = torch.nan_to_num(encoded_features, nan=0.0, posinf=1.0, neginf=-1.0)

                    # 运行时CUDA兼容性检查
                    batch_size, seq_len, feature_dim = encoded_features.shape
                    if feature_dim % 8 != 0:
                        self.logger.warning(f"⚠️ 运行时特征维度不是8的倍数: {feature_dim}")
                        # 临时禁用Transformer以避免CUDA错误
                        self.use_transformer = False
                        self.logger.warning("⚠️ 临时禁用Transformer以避免CUDA错误")
                        return encoded_features  # 直接返回原始特征

                    # 使用梯度检查点减少内存使用
                    try:
                        from torch.utils.checkpoint import checkpoint
                        if self.training:
                            transformer_output = checkpoint(
                                self.transformer_encoder, encoded_features, use_reentrant=False
                            )
                        else:
                            transformer_output = self.transformer_encoder(encoded_features)
                    except ImportError:
                        # 如果checkpoint不可用，直接使用transformer
                        transformer_output = self.transformer_encoder(encoded_features)

                    encoded_features = transformer_output

                except RuntimeError as e:
                    if "CUDA error: invalid configuration argument" in str(e):
                        self.logger.error(f"❌ CUDA配置错误: {e}")
                        self.logger.warning("⚠️ 永久禁用Transformer以避免CUDA错误")
                        self.use_transformer = False
                        # 删除Transformer组件以释放内存并避免重复错误
                        if hasattr(self, 'transformer_encoder'):
                            delattr(self, 'transformer_encoder')
                        # 继续使用原始编码特征
                    else:
                        self.logger.error(f"❌ Transformer编码失败: {e}")
                        self.logger.warning("⚠️ 跳过Transformer编码，使用原始特征")
                except Exception as e:
                    self.logger.error(f"❌ Transformer编码失败: {e}")
                    self.logger.warning("⚠️ 跳过Transformer编码，使用原始特征")
                    # 继续使用原始编码特征
            
            # 4. LSTM处理
            if self.use_lstm:
                lstm_output, (hidden, cell) = self.lstm(encoded_features)
                # 取最后时刻的输出
                sequence_output = lstm_output[:, -1, :]
            else:
                sequence_output = encoded_features[:, -1, :] # 如果没有序列处理，也取最后一个（唯一的）时间步

            # 5. 信号融合
            # 确保输入是2D的 (N, C)
            if sequence_output.dim() != 2:
                self.logger.warning(f"Reshaping input for fusion_network from {sequence_output.shape} to 2D")
                sequence_output = sequence_output.view(batch_size, -1)
                
            # 修复：当 use_lstm=True 但序列长度为1时，LSTM层被跳过，
            # 导致 sequence_output 维度 (64) 与 fusion_network 的期望输入 (128) 不匹配。
            # 在这里进行维度匹配。
            expected_fusion_dim = self.fusion_network[0].in_features
            if sequence_output.shape[1] != expected_fusion_dim:
                # 检查是否是双向LSTM输出维度不匹配的情况
                if sequence_output.shape[1] * 2 == expected_fusion_dim:
                    self.logger.warning(
                        f"Mismatched dimension for fusion network. "
                        f"Got {sequence_output.shape[1]}, expected {expected_fusion_dim}. "
                        f"Assuming skipped bidirectional LSTM and duplicating features."
                    )
                    sequence_output = torch.cat([sequence_output, sequence_output], dim=1)

            fused_features = self.fusion_network(sequence_output)

            # 6. 输出预测
            signal_logits = self.output_layer(fused_features)

            # 7. 置信度估计
            confidence = self.confidence_layer(fused_features)

            # 8. 应用softmax获得概率分布
            signal_probs = F.softmax(signal_logits, dim=-1)

            result = {
                'signal': signal_logits,
                'signal_probs': signal_probs,
                'confidence': confidence,
                'fused_features': fused_features
            }

            if return_attention and attention_weights is not None:
                result['attention_weights'] = attention_weights

            return result

        finally:
            # 恢复训练模式
            if was_training:
                self.train()
    
    def predict_signal(
        self, 
        features: torch.Tensor,
        threshold: float = 0.6
    ) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        预测交易信号
        
        Args:
            features: 输入特征
            threshold: 置信度阈值
            
        Returns:
            signals: 交易信号 (0: HOLD, 1: BUY, 2: SELL)
            confidences: 置信度
        """
        self.eval()
        with torch.no_grad():
            features = move_to_device(features, self.device)
            output = self.forward(features)
            
            signal_probs = output['signal_probs']
            confidences = output['confidence']
            
            # 获取最大概率的信号
            signals = torch.argmax(signal_probs, dim=-1)
            
            # 低置信度时设为HOLD
            low_confidence_mask = confidences.squeeze() < threshold
            signals[low_confidence_mask] = 0  # HOLD
            
            return signals, confidences
    
    def get_feature_importance(self, features: torch.Tensor) -> torch.Tensor:
        """
        获取特征重要性
        
        Args:
            features: 输入特征
            
        Returns:
            feature_importance: 特征重要性分数
        """
        self.eval()
        features = move_to_device(features, self.device)
        features.requires_grad_(True)
        
        output = self.forward(features)
        signal_logits = output['signal']
        
        # 计算梯度
        gradients = torch.autograd.grad(
            outputs=signal_logits.sum(),
            inputs=features,
            create_graph=True
        )[0]
        
        # 计算特征重要性
        importance = torch.abs(gradients).mean(dim=(0, 1))
        
        return importance
    
    def save_model(self, path: str):
        """保存模型"""
        torch.save({
            'model_state_dict': self.state_dict(),
            'model_config': {
                'feature_dim': self.feature_dim,
                'hidden_dims': self.hidden_dims,
                'num_heads': self.num_heads,
                'num_layers': self.num_layers,
                'dropout': self.dropout,
                'sequence_length': self.sequence_length,
                'output_dim': self.output_dim,
                'use_transformer': self.use_transformer,
                'use_lstm': self.use_lstm,
                'use_attention': self.use_attention
            }
        }, path)
        self.logger.info(f"Model saved to {path}")
    
    @classmethod
    def load_model(cls, path: str, device: Optional[str] = None):
        """加载模型 (使用joblib替代torch.load)"""
        import joblib
        checkpoint = joblib.load(path)
        model_config = checkpoint['model_config']

        model = cls(device=device, **model_config)
        # model.load_state_dict(checkpoint['model_state_dict'])  # sklearn模式下不需要

        return model
