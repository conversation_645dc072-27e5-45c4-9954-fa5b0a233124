"""
集成学习模型 - 提高预测准确度和鲁棒性
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Any, Optional, Tuple
from .unified_fusion_model import UnifiedSignalFusionModel
from .base_model import BaseDeepLearningModel
import logging
try:
    # 🔥 引入<PERSON>ocal<PERSON>oss
    from training.advanced_loss_functions import FocalLoss, create_advanced_loss_function, calculate_class_weights
except ImportError:
    FocalLoss = None # type: ignore
    create_advanced_loss_function = None
    calculate_class_weights = None


class EnsembleModel(BaseDeepLearningModel):
    """集成学习模型"""
    
    def __init__(
        self,
        feature_dim: int = 64,
        hidden_dims: List[int] = [256, 128, 64],
        num_heads: int = 8,
        num_layers: int = 3,
        dropout: float = 0.2,
        output_dim: int = 3,
        n_models: int = 5,
        ensemble_method: str = 'voting',  # 'voting', 'stacking', 'bagging'
        diversity_weight: float = 0.1,
        loss_type: str = 'focal', # 🔥 新增：允许配置损失函数
        focal_gamma: float = 2.0,   # 🔥 新增：Focal Loss的gamma参数
        device: Optional[str] = None,
        **kwargs
    ):
        super().__init__(**kwargs)
        
        self.feature_dim = feature_dim
        self.hidden_dims = hidden_dims if hidden_dims is not None else [256, 128, 64]
        self.num_heads = num_heads
        self.num_layers = num_layers
        self.dropout = dropout
        self.output_dim = output_dim
        self.n_models = n_models
        self.ensemble_method = ensemble_method
        self.diversity_weight = diversity_weight
        self.loss_type = loss_type
        self.focal_gamma = focal_gamma
        
        # 设备
        self.device = device or ('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建多个基础模型
        self.base_models = nn.ModuleList([
            self._create_base_model(i) for i in range(n_models)
        ])
        
        # 集成层
        if ensemble_method == 'stacking':
            self.stacking_layer = nn.Sequential(
                nn.Linear(output_dim * n_models, hidden_dims[-1]),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dims[-1], output_dim)
            )
        elif ensemble_method == 'meta_learning':
            self.meta_learner = nn.Sequential(
                nn.Linear(n_models * output_dim + self.feature_dim, hidden_dims[0]),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dims[0], hidden_dims[1]),
                nn.ReLU(),
                nn.Dropout(dropout),
                nn.Linear(hidden_dims[1], n_models),
                nn.Softmax(dim=-1)
            )
        
        # 注意力权重（用于加权投票）
        if ensemble_method == 'attention':
            self.attention_weights = nn.Parameter(torch.ones(n_models) / n_models)
        
        self.logger = logging.getLogger(__name__)

    def update_feature_dim(self, new_feature_dim: int):
        """
        动态更新所有基础模型的输入特征维度。
        """
        if new_feature_dim != self.feature_dim:
            self.logger.info(f"Updating feature dimension for all base models from {self.feature_dim} to {new_feature_dim}")
            self.feature_dim = new_feature_dim
            for model in self.base_models:
                if hasattr(model, 'update_feature_dim'):
                    model.update_feature_dim(new_feature_dim)
            self.logger.info("All base models have been updated with the new feature dimension.")
        
    def _create_base_model(self, model_idx: int) -> nn.Module:
        """创建基础模型"""
        # 为每个模型添加一些随机性
        model_config = {
            'feature_dim': self.feature_dim,
            'hidden_dims': self.hidden_dims.copy(),
            'num_heads': self.num_heads,
            'num_layers': self.num_layers,
            'dropout': self.dropout + (model_idx * 0.01),  # 轻微调整dropout
            'output_dim': self.output_dim,
            'device': self.device
        }
        
        # 为不同模型使用不同的架构变体
        if model_idx % 3 == 0:
            model_config['use_transformer'] = True
            model_config['use_lstm'] = True
        elif model_idx % 3 == 1:
            model_config['use_transformer'] = True
            model_config['use_lstm'] = False
        else:
            model_config['use_transformer'] = False
            model_config['use_lstm'] = True
        
        return UnifiedSignalFusionModel(**model_config)
    
    def forward(self, x: torch.Tensor, return_individual: bool = False) -> Dict[str, torch.Tensor]:
        """前向传播"""
        batch_size = x.size(0)
        
        # 获取所有基础模型的预测
        individual_outputs = []
        individual_probs = []
        individual_confidences = []
        
        for i, model in enumerate(self.base_models):
            output = model(x)
            individual_outputs.append(output['signal'])
            individual_probs.append(output['signal_probs'])
            individual_confidences.append(output['confidence'])
        
        # 集成预测
        if self.ensemble_method == 'voting':
            # 简单投票
            ensemble_probs = torch.stack(individual_probs).mean(dim=0)
            ensemble_logits = torch.log(ensemble_probs + 1e-8)
        
        elif self.ensemble_method == 'weighted_voting':
            # 加权投票（基于置信度）
            confidences = torch.stack(individual_confidences)  # [n_models, batch_size, 1]
            weights = F.softmax(confidences.squeeze(-1), dim=0)  # [n_models, batch_size]
            
            probs_stack = torch.stack(individual_probs)  # [n_models, batch_size, output_dim]
            ensemble_probs = torch.sum(probs_stack * weights.unsqueeze(-1), dim=0)
            ensemble_logits = torch.log(ensemble_probs + 1e-8)
        
        elif self.ensemble_method == 'attention':
            # 注意力加权
            attention_weights = F.softmax(self.attention_weights, dim=0)
            probs_stack = torch.stack(individual_probs)  # [n_models, batch_size, output_dim]
            ensemble_probs = torch.sum(probs_stack * attention_weights.view(-1, 1, 1), dim=0)
            ensemble_logits = torch.log(ensemble_probs + 1e-8)
        
        elif self.ensemble_method == 'stacking':
            # 堆叠集成
            stacked_logits = torch.cat(individual_outputs, dim=-1)
            ensemble_logits = self.stacking_layer(stacked_logits)
            ensemble_probs = F.softmax(ensemble_logits, dim=-1)

        elif self.ensemble_method == 'meta_learning':
            # 元学习集成
            ensemble_logits, ensemble_probs = self._meta_learning_ensemble(individual_outputs, x)
        
        else:
            # 默认简单平均
            ensemble_probs = torch.stack(individual_probs).mean(dim=0)
            ensemble_logits = torch.log(ensemble_probs + 1e-8)
        
        # 集成置信度
        ensemble_confidence = torch.stack(individual_confidences).mean(dim=0)
        
        # 计算多样性（预测的方差）
        diversity = self._calculate_diversity(individual_probs)
        
        result = {
            'signal': ensemble_logits,
            'signal_probs': ensemble_probs,
            'confidence': ensemble_confidence,
            'diversity': diversity
        }
        
        if return_individual:
            result.update({
                'individual_outputs': individual_outputs,
                'individual_probs': individual_probs,
                'individual_confidences': individual_confidences
            })
        
        return result
    
    def _calculate_diversity(self, individual_probs: List[torch.Tensor]) -> torch.Tensor:
        """计算预测多样性"""
        probs_stack = torch.stack(individual_probs)  # [n_models, batch_size, output_dim]
        mean_probs = probs_stack.mean(dim=0)  # [batch_size, output_dim]
        
        # 计算方差作为多样性指标
        diversity = torch.var(probs_stack, dim=0).mean(dim=-1, keepdim=True)  # [batch_size, 1]
        return diversity
    
    def calculate_ensemble_loss(self, outputs: Dict[str, torch.Tensor], 
                              targets: torch.Tensor, 
                              teacher_probs: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算集成损失
        
        🔥 增强: 支持Focal Loss以处理类别不平衡问题并修复多样性损失的计算
        """
        # 1. 主要损失计算
        if self.loss_type == 'focal' and FocalLoss is not None:
            # 使用Focal Loss
            # 注意：这里的alpha（类别权重）暂未实现，因为需要整个数据集的标签分布
            focal_loss_fn = FocalLoss(gamma=self.focal_gamma, reduction='mean')
            main_loss = focal_loss_fn(outputs['signal'], targets)
        else:
            # 默认使用Cross Entropy Loss
            if self.loss_type != 'cross_entropy' and self.loss_type != 'focal':
                self.logger.warning(f"指定的损失类型 '{self.loss_type}' 或 FocalLoss 不可用，回退到CrossEntropyLoss。")
                # self.loss_type = 'cross_entropy' # 避免重复警告
            main_loss = F.cross_entropy(outputs['signal'], targets)

        # 2. 多样性损失（鼓励模型间的差异） - 🔥 修复和改进
        if 'individual_outputs' in outputs and self.diversity_weight > 0:
            # 基于logits计算多样性，比基于probs更稳定
            all_logits = torch.stack(outputs['individual_outputs']) # [n_models, batch, features]
            mean_logits = all_logits.mean(dim=0)
            
            # 使用L1损失作为差异性度量，比方差更鲁棒
            diversity_loss = 0.0
            for logits in all_logits:
                diversity_loss += F.l1_loss(logits, mean_logits)
            
            # 我们希望最大化差异，即最小化负的差异性
            # 除以模型数量进行归一化
            diversity_loss = - (diversity_loss / len(all_logits))
            
            main_loss += self.diversity_weight * diversity_loss
        
        # 3. 知识蒸馏损失
        if teacher_probs is not None:
            kd_loss = F.kl_div(
                F.log_softmax(outputs['signal'] / 2.0, dim=-1), # 使用温度进行平滑
                teacher_probs,
                reduction='batchmean'
            )
            main_loss += 0.5 * kd_loss
        
        return main_loss
    
    def predict_with_uncertainty(self, x: torch.Tensor) -> Dict[str, torch.Tensor]:
        """带不确定性的预测"""
        self.eval()
        with torch.no_grad():
            outputs = self.forward(x, return_individual=True)
            
            # 计算预测不确定性
            individual_probs = torch.stack(outputs['individual_probs'])  # [n_models, batch_size, output_dim]
            
            # 认知不确定性（模型间的分歧）
            epistemic_uncertainty = torch.var(individual_probs, dim=0).mean(dim=-1, keepdim=True)
            
            # 随机不确定性（预测的熵）
            ensemble_probs = outputs['signal_probs']
            aleatoric_uncertainty = -torch.sum(ensemble_probs * torch.log(ensemble_probs + 1e-8), dim=-1, keepdim=True)
            
            # 总不确定性
            total_uncertainty = epistemic_uncertainty + aleatoric_uncertainty
            
            outputs.update({
                'epistemic_uncertainty': epistemic_uncertainty,
                'aleatoric_uncertainty': aleatoric_uncertainty,
                'total_uncertainty': total_uncertainty
            })
            
            return outputs
    
    def get_model_importance(self) -> Dict[int, float]:
        """获取各个基础模型的重要性"""
        if self.ensemble_method == 'attention':
            weights = F.softmax(self.attention_weights, dim=0)
            return {i: float(weights[i]) for i in range(self.n_models)}
        else:
            # 均等重要性
            return {i: 1.0 / self.n_models for i in range(self.n_models)}
    
    def save_ensemble(self, path: str):
        """保存集成模型"""
        torch.save({
            'state_dict': self.state_dict(),
            'config': {
                'feature_dim': self.feature_dim,
                'hidden_dims': self.hidden_dims,
                'num_heads': self.num_heads,
                'num_layers': self.num_layers,
                'dropout': self.dropout,
                'output_dim': self.output_dim,
                'n_models': self.n_models,
                'ensemble_method': self.ensemble_method,
                'diversity_weight': self.diversity_weight
            }
        }, path)
        self.logger.info(f"Ensemble model saved to: {path}")
    
    @classmethod
    def load_ensemble(cls, path: str, device: Optional[str] = None):
        """加载集成模型"""
        checkpoint = torch.load(path, map_location=device)
        model = cls(**checkpoint['config'], device=device)
        model.load_state_dict(checkpoint['state_dict'])
        return model

    def _meta_learning_ensemble(self, individual_outputs: List[torch.Tensor], features: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """执行元学习集成的逻辑"""
        # 展平预测结果
        stacked_predictions = torch.stack(individual_outputs, dim=1)
        batch_size, n_models, output_dim = stacked_predictions.shape
        flattened_predictions = stacked_predictions.view(batch_size, -1)

        # 获取原始特征的全局表示
        # 🔥 修复: 确保在使用前将输入特征移动到正确的设备
        features = features.to(self.device)
        if features.dim() > 2:
            global_features = torch.mean(features, dim=1)
        else:
            global_features = features

        # 组合特征
        combined_features = torch.cat([flattened_predictions, global_features], dim=-1)

        # 元学习器预测权重
        model_weights = self.meta_learner(combined_features).unsqueeze(-1)  # (batch, n_models, 1)

        # 加权组合
        weighted_predictions = stacked_predictions * model_weights
        ensemble_logits = torch.sum(weighted_predictions, dim=1)
        ensemble_probs = F.softmax(ensemble_logits, dim=-1)
        
        return ensemble_logits, ensemble_probs




class AdaptiveEnsemble(EnsembleModel):
    """自适应集成模型"""
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        
        # 动态权重网络
        self.weight_network = nn.Sequential(
            nn.Linear(self.feature_dim, self.hidden_dims[-1]),
            nn.ReLU(),
            nn.Dropout(self.dropout),
            nn.Linear(self.hidden_dims[-1], self.n_models),
            nn.Softmax(dim=-1)
        )
    
    def forward(self, x: torch.Tensor, return_individual: bool = False) -> Dict[str, torch.Tensor]:
        """自适应前向传播"""
        # 计算动态权重
        dynamic_weights = self.weight_network(x.mean(dim=1))  # [batch_size, n_models]
        
        # 获取基础模型预测
        individual_outputs = []
        individual_probs = []
        individual_confidences = []
        
        for model in self.base_models:
            output = model(x)
            individual_outputs.append(output['signal'])
            individual_probs.append(output['signal_probs'])
            individual_confidences.append(output['confidence'])
        
        # 动态加权集成
        probs_stack = torch.stack(individual_probs)  # [n_models, batch_size, output_dim]
        ensemble_probs = torch.sum(
            probs_stack * dynamic_weights.T.unsqueeze(-1), dim=0
        )  # [batch_size, output_dim]
        
        ensemble_logits = torch.log(ensemble_probs + 1e-8)
        ensemble_confidence = torch.stack(individual_confidences).mean(dim=0)
        diversity = self._calculate_diversity(individual_probs)
        
        result = {
            'signal': ensemble_logits,
            'signal_probs': ensemble_probs,
            'confidence': ensemble_confidence,
            'diversity': diversity,
            'dynamic_weights': dynamic_weights
        }
        
        if return_individual:
            result.update({
                'individual_outputs': individual_outputs,
                'individual_probs': individual_probs,
                'individual_confidences': individual_confidences
            })
        
        return result
