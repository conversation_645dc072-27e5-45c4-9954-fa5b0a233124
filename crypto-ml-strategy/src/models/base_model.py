"""
深度学习模型基类
基于qlib架构设计的模型基类
"""

import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, Union, Tuple
import logging
from pathlib import Path

# Removed obsolete import: from src.utils.gpu_utils import get_device, move_to_device
from src.utils.metrics import calculate_metrics


class BaseDeepLearningModel(nn.Module, ABC):
    """
    深度学习模型基类
    
    提供通用的模型接口和功能，包括：
    - GPU支持
    - 模型保存/加载
    - 性能评估
    - 日志记录
    """
    
    def __init__(
        self,
        model_name: str = "BaseModel",
        device: Optional[str] = "cpu", # Changed: device is now a required argument, defaults to "cpu"
        seed: Optional[int] = None,
        **kwargs
    ):
        super().__init__()
        
        self.model_name = model_name
        # Changed: device is now passed directly
        self.device = device if isinstance(device, torch.device) else torch.device(device)
        
        # 设置随机种子
        if seed is not None:
            self.set_seed(seed)
        
        # 初始化日志
        self.logger = logging.getLogger(f"{__name__}.{model_name}")
        
        # 模型状态
        self.is_fitted = False
        self.training_history = []
        
        # 性能指标
        self.best_score = None
        self.best_epoch = None
    
    @staticmethod
    def set_seed(seed: int):
        """设置随机种子"""
        torch.manual_seed(seed)
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        np.random.seed(seed)
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    
    @abstractmethod
    def forward(self, *args, **kwargs):
        """前向传播 - 子类必须实现"""
        pass
    
    def predict(
        self, 
        data: Union[torch.Tensor, np.ndarray, pd.DataFrame],
        batch_size: int = 1000,
        return_probs: bool = False
    ) -> Union[np.ndarray, Tuple[np.ndarray, np.ndarray]]:
        """
        模型预测
        
        Args:
            data: 输入数据
            batch_size: 批次大小
            return_probs: 是否返回概率
            
        Returns:
            predictions: 预测结果
            probabilities: 概率分布 (可选)
        """
        self.eval()
        
        # 数据预处理
        if isinstance(data, (pd.DataFrame, np.ndarray)):
            # Ensure data is a tensor before moving to device
            data = torch.from_numpy(np.array(data.values if isinstance(data, pd.DataFrame) else data, dtype=np.float32))
        
        # Changed: Use the tensor's own .to() method
        data = data.to(self.device)
        
        predictions = []
        probabilities = []
        
        with torch.no_grad():
            for i in range(0, len(data), batch_size):
                batch_data = data[i:i + batch_size]
                
                # 前向传播
                output = self.forward(batch_data)
                
                if isinstance(output, dict):
                    batch_pred = output.get('signal', output.get('predictions'))
                    batch_prob = output.get('signal_probs', output.get('probabilities'))
                else:
                    batch_pred = output
                    batch_prob = torch.softmax(output, dim=-1) if output.dim() > 1 else None
                
                predictions.append(batch_pred.cpu().numpy())
                if batch_prob is not None:
                    probabilities.append(batch_prob.cpu().numpy())
        
        predictions = np.concatenate(predictions, axis=0)
        
        if return_probs and probabilities:
            probabilities = np.concatenate(probabilities, axis=0)
            return predictions, probabilities
        
        return predictions
    
    def evaluate(
        self,
        data: Union[torch.Tensor, np.ndarray, pd.DataFrame],
        labels: Union[torch.Tensor, np.ndarray, pd.Series],
        metrics: Optional[list] = None
    ) -> Dict[str, float]:
        """
        模型评估
        
        Args:
            data: 输入数据
            labels: 真实标签
            metrics: 评估指标列表
            
        Returns:
            evaluation_results: 评估结果字典
        """
        if metrics is None:
            metrics = ['accuracy', 'precision', 'recall', 'f1', 'auc']
        
        # 获取预测结果
        predictions, probabilities = self.predict(data, return_probs=True)
        
        # 转换标签格式
        if isinstance(labels, (pd.Series, torch.Tensor)):
            labels = labels.values if isinstance(labels, pd.Series) else labels.cpu().numpy()
        
        # 计算评估指标
        results = calculate_metrics(
            y_true=labels,
            y_pred=predictions,
            y_prob=probabilities,
            metrics=metrics
        )
        
        self.logger.info(f"Evaluation results: {results}")
        return results
    
    def save_checkpoint(
        self,
        path: Union[str, Path],
        epoch: int,
        optimizer_state: Optional[Dict] = None,
        scheduler_state: Optional[Dict] = None,
        additional_info: Optional[Dict] = None
    ):
        """
        保存模型检查点
        
        Args:
            path: 保存路径
            epoch: 当前轮次
            optimizer_state: 优化器状态
            scheduler_state: 调度器状态
            additional_info: 额外信息
        """
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.state_dict(),
            'model_name': self.model_name,
            'is_fitted': self.is_fitted,
            'training_history': self.training_history,
            'best_score': self.best_score,
            'best_epoch': self.best_epoch
        }
        
        if optimizer_state:
            checkpoint['optimizer_state_dict'] = optimizer_state
        
        if scheduler_state:
            checkpoint['scheduler_state_dict'] = scheduler_state
        
        if additional_info:
            checkpoint.update(additional_info)
        
        torch.save(checkpoint, path)
        self.logger.info(f"Checkpoint saved to {path}")
    
    def load_checkpoint(
        self,
        path: Union[str, Path],
        load_optimizer: bool = False,
        load_scheduler: bool = False
    ) -> Dict[str, Any]:
        """
        加载模型检查点
        
        Args:
            path: 检查点路径
            load_optimizer: 是否加载优化器状态
            load_scheduler: 是否加载调度器状态
            
        Returns:
            checkpoint_info: 检查点信息
        """
        checkpoint = torch.load(checkpoint_path, map_location=self.device)
        
        # 加载模型状态
        self.load_state_dict(checkpoint['model_state_dict'])
        self.is_fitted = checkpoint.get('is_fitted', False)
        self.training_history = checkpoint.get('training_history', [])
        self.best_score = checkpoint.get('best_score')
        self.best_epoch = checkpoint.get('best_epoch')
        
        result = {
            'epoch': checkpoint.get('epoch', 0),
            'model_name': checkpoint.get('model_name', self.model_name)
        }
        
        if load_optimizer and 'optimizer_state_dict' in checkpoint:
            result['optimizer_state_dict'] = checkpoint['optimizer_state_dict']
        
        if load_scheduler and 'scheduler_state_dict' in checkpoint:
            result['scheduler_state_dict'] = checkpoint['scheduler_state_dict']
        
        self.logger.info(f"Checkpoint loaded from {path}")
        return result
    
    def get_model_size(self) -> Dict[str, Union[int, float]]:
        """
        获取模型大小信息
        
        Returns:
            model_size_info: 模型大小信息
        """
        param_count = sum(p.numel() for p in self.parameters())
        trainable_param_count = sum(p.numel() for p in self.parameters() if p.requires_grad)
        
        # 计算模型大小 (MB)
        param_size = sum(p.numel() * p.element_size() for p in self.parameters())
        buffer_size = sum(b.numel() * b.element_size() for b in self.buffers())
        model_size_mb = (param_size + buffer_size) / (1024 ** 2)
        
        return {
            'total_parameters': param_count,
            'trainable_parameters': trainable_param_count,
            'non_trainable_parameters': param_count - trainable_param_count,
            'model_size_mb': model_size_mb
        }
    
    def freeze_layers(self, layer_names: list):
        """
        冻结指定层的参数
        
        Args:
            layer_names: 要冻结的层名称列表
        """
        for name, param in self.named_parameters():
            if any(layer_name in name for layer_name in layer_names):
                param.requires_grad = False
                self.logger.info(f"Frozen layer: {name}")
    
    def unfreeze_layers(self, layer_names: list):
        """
        解冻指定层的参数
        
        Args:
            layer_names: 要解冻的层名称列表
        """
        for name, param in self.named_parameters():
            if any(layer_name in name for layer_name in layer_names):
                param.requires_grad = True
                self.logger.info(f"Unfrozen layer: {name}")
    
    def get_layer_names(self) -> list:
        """获取所有层的名称"""
        return [name for name, _ in self.named_modules()]
    
    def summary(self) -> str:
        """
        获取模型摘要信息
        
        Returns:
            model_summary: 模型摘要字符串
        """
        size_info = self.get_model_size()
        
        summary = f"""
Model: {self.model_name}
Device: {self.device}
Fitted: {self.is_fitted}
Total Parameters: {size_info['total_parameters']:,}
Trainable Parameters: {size_info['trainable_parameters']:,}
Model Size: {size_info['model_size_mb']:.2f} MB
Best Score: {self.best_score}
Best Epoch: {self.best_epoch}
        """.strip()
        
        return summary
    
    def __repr__(self) -> str:
        return f"{self.__class__.__name__}(name={self.model_name}, device={self.device})"
