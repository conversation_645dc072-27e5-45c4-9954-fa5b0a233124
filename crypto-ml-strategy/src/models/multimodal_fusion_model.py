#!/usr/bin/env python3
"""
多模态融合模型 - 增加计算复杂度，提高GPU利用率
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
import math
from .base_model import BaseDeepLearningModel


class MultiHeadCrossAttention(nn.Module):
    """多头交叉注意力机制"""
    
    def __init__(self, d_model: int, num_heads: int, dropout: float = 0.1):
        super().__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        self.w_q = nn.Linear(d_model, d_model, bias=False)
        self.w_k = nn.Linear(d_model, d_model, bias=False)
        self.w_v = nn.Linear(d_model, d_model, bias=False)
        self.w_o = nn.Linear(d_model, d_model)
        
        self.dropout = nn.Dropout(dropout)
        self.layer_norm = nn.LayerNorm(d_model)
        
    def forward(self, query: torch.Tensor, key: torch.Tensor, value: torch.Tensor) -> torch.Tensor:
        batch_size = query.size(0)
        
        # 线性变换
        Q = self.w_q(query).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, -1, self.num_heads, self.d_k).transpose(1, 2)
        
        # 计算注意力
        scores = torch.matmul(Q, K.transpose(-2, -1)) / math.sqrt(self.d_k)
        attn_weights = F.softmax(scores, dim=-1)
        attn_weights = self.dropout(attn_weights)
        
        # 应用注意力
        context = torch.matmul(attn_weights, V)
        context = context.transpose(1, 2).contiguous().view(batch_size, -1, self.d_model)
        
        # 输出投影
        output = self.w_o(context)
        
        # 残差连接和层归一化
        return self.layer_norm(output + query)


class ModalityEncoder(nn.Module):
    """模态编码器"""
    
    def __init__(self, input_dim: int, hidden_dim: int, num_layers: int = 4):
        super().__init__()
        
        layers = []
        current_dim = input_dim
        
        for i in range(num_layers):
            layers.extend([
                nn.Linear(current_dim, hidden_dim),
                nn.LayerNorm(hidden_dim),
                nn.GELU(),
                nn.Dropout(0.1)
            ])
            current_dim = hidden_dim
        
        self.encoder = nn.Sequential(*layers)
        self.output_projection = nn.Linear(hidden_dim, hidden_dim)
        
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        encoded = self.encoder(x)
        return self.output_projection(encoded)


class MultiModalFusionModel(BaseDeepLearningModel):
    """多模态融合模型 - 大幅增加计算复杂度"""
    
    def __init__(
        self,
        feature_dim: int = 39,
        hidden_dim: int = 2048,
        num_heads: int = 32,
        num_layers: int = 24,
        num_modalities: int = 4,
        dropout: float = 0.1,
        output_dim: int = 3,
        intermediate_size: int = 8192,
        device: Optional[str] = None,
        **kwargs
    ):
        super().__init__(device=device, **kwargs)
        
        self.feature_dim = feature_dim
        self.hidden_dim = hidden_dim
        self.num_heads = num_heads
        self.num_layers = num_layers
        self.num_modalities = num_modalities
        
        # 🔥 多模态输入处理
        modality_dim = feature_dim // num_modalities
        self.modality_encoders = nn.ModuleList([
            ModalityEncoder(modality_dim, hidden_dim, num_layers=6)
            for _ in range(num_modalities)
        ])
        
        # 🔥 位置编码
        self.positional_encoding = nn.Parameter(
            torch.randn(1, num_modalities, hidden_dim) * 0.02
        )
        
        # 🔥 多层交叉注意力
        self.cross_attention_layers = nn.ModuleList([
            MultiHeadCrossAttention(hidden_dim, num_heads, dropout)
            for _ in range(num_layers)
        ])
        
        # 🔥 前馈网络层
        self.feed_forward_layers = nn.ModuleList([
            nn.Sequential(
                nn.Linear(hidden_dim, intermediate_size),
                nn.GELU(),
                nn.Dropout(dropout),
                nn.Linear(intermediate_size, hidden_dim),
                nn.Dropout(dropout)
            )
            for _ in range(num_layers)
        ])
        
        # 🔥 层归一化
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(hidden_dim) for _ in range(num_layers)
        ])
        
        # 🔥 模态融合层
        self.modality_fusion = nn.Sequential(
            nn.Linear(hidden_dim * num_modalities, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        # 🔥 深度特征提取
        self.deep_feature_extractor = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim * 4),
            nn.LayerNorm(hidden_dim * 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 4, hidden_dim * 2),
            nn.LayerNorm(hidden_dim * 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim * 2, hidden_dim)
        )
        
        # 🔥 输出头
        self.output_head = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.LayerNorm(hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, hidden_dim // 4),
            nn.LayerNorm(hidden_dim // 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 4, output_dim)
        )
        
        # 🔥 辅助任务头（增加计算复杂度）
        self.auxiliary_heads = nn.ModuleDict({
            'volatility': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.GELU(),
                nn.Linear(hidden_dim // 2, 1)
            ),
            'trend': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.GELU(),
                nn.Linear(hidden_dim // 2, 3)
            ),
            'momentum': nn.Sequential(
                nn.Linear(hidden_dim, hidden_dim // 2),
                nn.GELU(),
                nn.Linear(hidden_dim // 2, 1)
            )
        })
        
        # 初始化权重
        self.apply(self._init_weights)
        
        # 移动到设备
        self.to(self.device)
    
    def _init_weights(self, module):
        """初始化权重"""
        if isinstance(module, nn.Linear):
            torch.nn.init.normal_(module.weight, mean=0.0, std=0.02)
            if module.bias is not None:
                torch.nn.init.zeros_(module.bias)
        elif isinstance(module, nn.LayerNorm):
            torch.nn.init.zeros_(module.bias)
            torch.nn.init.ones_(module.weight)
    
    def split_modalities(self, features: torch.Tensor) -> List[torch.Tensor]:
        """将特征分割为多个模态"""
        batch_size = features.size(0)
        modality_dim = self.feature_dim // self.num_modalities
        
        modalities = []
        for i in range(self.num_modalities):
            start_idx = i * modality_dim
            end_idx = start_idx + modality_dim
            if end_idx > features.size(1):
                # 如果特征维度不能整除，最后一个模态包含剩余特征
                end_idx = features.size(1)
            modality = features[:, start_idx:end_idx]
            modalities.append(modality)
        
        return modalities
    
    def forward(self, features: torch.Tensor) -> Dict[str, torch.Tensor]:
        """前向传播"""
        batch_size = features.size(0)
        
        # 🔥 分割为多个模态
        modalities = self.split_modalities(features)
        
        # 🔥 编码每个模态
        encoded_modalities = []
        for i, modality in enumerate(modalities):
            if modality.size(1) > 0:  # 确保模态不为空
                encoded = self.modality_encoders[i](modality)
                encoded_modalities.append(encoded)
        
        # 如果没有有效模态，使用零填充
        if not encoded_modalities:
            encoded_modalities = [torch.zeros(batch_size, self.hidden_dim, device=features.device)]
        
        # 🔥 堆叠模态并添加位置编码
        stacked_modalities = torch.stack(encoded_modalities, dim=1)  # [batch, num_modalities, hidden_dim]
        
        # 调整位置编码维度
        pos_encoding = self.positional_encoding[:, :stacked_modalities.size(1), :]
        stacked_modalities = stacked_modalities + pos_encoding
        
        # 🔥 多层交叉注意力处理
        hidden_states = stacked_modalities
        
        for i in range(self.num_layers):
            # 交叉注意力
            attended = self.cross_attention_layers[i](
                hidden_states.view(batch_size, -1, self.hidden_dim),
                hidden_states.view(batch_size, -1, self.hidden_dim),
                hidden_states.view(batch_size, -1, self.hidden_dim)
            )
            
            # 前馈网络
            ff_output = self.feed_forward_layers[i](attended)
            
            # 残差连接和层归一化
            hidden_states = self.layer_norms[i](ff_output + attended)
            hidden_states = hidden_states.view(batch_size, -1, self.hidden_dim)
        
        # 🔥 模态融合
        flattened = hidden_states.view(batch_size, -1)
        fused_features = self.modality_fusion(flattened)
        
        # 🔥 深度特征提取
        deep_features = self.deep_feature_extractor(fused_features)
        
        # 🔥 主要输出
        main_output = self.output_head(deep_features)
        
        # 🔥 辅助输出（增加计算复杂度）
        auxiliary_outputs = {}
        for task_name, head in self.auxiliary_heads.items():
            auxiliary_outputs[task_name] = head(deep_features)
        
        return {
            'signal': main_output,
            'features': deep_features,
            'auxiliary': auxiliary_outputs
        }


def create_multimodal_fusion_model(config: Dict[str, Any]) -> MultiModalFusionModel:
    """创建多模态融合模型"""
    return MultiModalFusionModel(**config)
