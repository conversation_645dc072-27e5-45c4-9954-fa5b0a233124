"""
实时性能监控仪表板
"""

import time
import json
import threading
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
from pathlib import Path
import torch

from src.monitoring.gpu_monitor import get_gpu_monitor, GPUMetrics


@dataclass
class TrainingMetrics:
    """训练指标"""
    timestamp: datetime
    epoch: int
    batch_idx: int
    train_loss: float
    val_loss: Optional[float] = None
    val_accuracy: Optional[float] = None
    learning_rate: float = 0.0
    batch_size: int = 0
    throughput: float = 0.0  # samples/s
    gpu_utilization: float = 0.0
    gpu_memory_percent: float = 0.0
    teacher_calls: int = 0
    cache_hit_rate: float = 0.0


@dataclass
class SystemMetrics:
    """系统指标"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    disk_usage_percent: float
    network_io: Dict[str, float]
    process_count: int


class PerformanceDashboard:
    """性能监控仪表板"""
    
    def __init__(self, 
                 update_interval: float = 5.0,
                 history_size: int = 1000,
                 export_path: Optional[str] = None):
        """
        初始化性能仪表板
        
        Args:
            update_interval: 更新间隔(秒)
            history_size: 历史数据保存数量
            export_path: 数据导出路径
        """
        self.logger = logging.getLogger(__name__)
        self.update_interval = update_interval
        self.history_size = history_size
        self.export_path = Path(export_path) if export_path else Path("data/monitoring")
        
        # 创建导出目录
        self.export_path.mkdir(parents=True, exist_ok=True)
        
        # 数据存储
        self.training_metrics: List[TrainingMetrics] = []
        self.system_metrics: List[SystemMetrics] = []
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 获取GPU监控器
        self.gpu_monitor = get_gpu_monitor()
        
        # 当前训练状态
        self.current_training_state = {
            'is_training': False,
            'current_epoch': 0,
            'total_epochs': 0,
            'current_batch': 0,
            'total_batches': 0,
            'start_time': None,
            'estimated_completion': None
        }
        
        # 性能统计
        self.performance_stats = {
            'avg_throughput': 0.0,
            'peak_throughput': 0.0,
            'avg_gpu_utilization': 0.0,
            'peak_gpu_utilization': 0.0,
            'total_samples_processed': 0,
            'total_training_time': 0.0
        }
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            self.logger.warning("性能监控已在运行")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        # 启动GPU监控
        if not self.gpu_monitor.is_monitoring:
            self.gpu_monitor.start_monitoring()
        
        self.logger.info("🚀 性能监控仪表板已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        # 导出最终数据
        self.export_data()
        
        self.logger.info("🛑 性能监控仪表板已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        self.logger.info("🔍 性能监控线程启动")
        
        while self.is_monitoring:
            try:
                # 收集系统指标
                system_metrics = self._collect_system_metrics()
                if system_metrics:
                    self.system_metrics.append(system_metrics)
                    
                    # 保持历史记录大小
                    if len(self.system_metrics) > self.history_size:
                        self.system_metrics.pop(0)
                
                # 更新性能统计
                self._update_performance_stats()
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                self.logger.error(f"性能监控循环错误: {e}")
                time.sleep(self.update_interval)
        
        self.logger.info("🔍 性能监控线程停止")
    
    def _collect_system_metrics(self) -> Optional[SystemMetrics]:
        """收集系统指标"""
        try:
            import psutil
            
            # CPU和内存
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            
            # 磁盘使用
            disk = psutil.disk_usage('/')
            disk_percent = (disk.used / disk.total) * 100
            
            # 网络IO
            network = psutil.net_io_counters()
            network_io = {
                'bytes_sent': network.bytes_sent,
                'bytes_recv': network.bytes_recv,
                'packets_sent': network.packets_sent,
                'packets_recv': network.packets_recv
            }
            
            # 进程数
            process_count = len(psutil.pids())
            
            return SystemMetrics(
                timestamp=datetime.now(),
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                disk_usage_percent=disk_percent,
                network_io=network_io,
                process_count=process_count
            )
            
        except Exception as e:
            self.logger.error(f"收集系统指标失败: {e}")
            return None
    
    def _update_performance_stats(self):
        """更新性能统计"""
        if not self.training_metrics:
            return
        
        recent_metrics = self.training_metrics[-100:]  # 最近100个数据点
        
        # 计算平均值
        throughputs = [m.throughput for m in recent_metrics if m.throughput > 0]
        gpu_utils = [m.gpu_utilization for m in recent_metrics if m.gpu_utilization > 0]
        
        if throughputs:
            self.performance_stats['avg_throughput'] = sum(throughputs) / len(throughputs)
            self.performance_stats['peak_throughput'] = max(throughputs)
        
        if gpu_utils:
            self.performance_stats['avg_gpu_utilization'] = sum(gpu_utils) / len(gpu_utils)
            self.performance_stats['peak_gpu_utilization'] = max(gpu_utils)
    
    def record_training_metrics(self, 
                              epoch: int,
                              batch_idx: int,
                              train_loss: float,
                              val_loss: Optional[float] = None,
                              val_accuracy: Optional[float] = None,
                              learning_rate: float = 0.0,
                              batch_size: int = 0,
                              throughput: float = 0.0,
                              teacher_calls: int = 0,
                              cache_hit_rate: float = 0.0):
        """记录训练指标"""
        
        # 获取GPU指标
        gpu_metrics = self.gpu_monitor.get_current_metrics()
        gpu_utilization = gpu_metrics.gpu_utilization if gpu_metrics else 0.0
        gpu_memory_percent = gpu_metrics.memory_percent if gpu_metrics else 0.0
        
        # 创建训练指标
        metrics = TrainingMetrics(
            timestamp=datetime.now(),
            epoch=epoch,
            batch_idx=batch_idx,
            train_loss=train_loss,
            val_loss=val_loss,
            val_accuracy=val_accuracy,
            learning_rate=learning_rate,
            batch_size=batch_size,
            throughput=throughput,
            gpu_utilization=gpu_utilization,
            gpu_memory_percent=gpu_memory_percent,
            teacher_calls=teacher_calls,
            cache_hit_rate=cache_hit_rate
        )
        
        self.training_metrics.append(metrics)
        
        # 保持历史记录大小
        if len(self.training_metrics) > self.history_size:
            self.training_metrics.pop(0)
        
        # 更新训练状态
        self.current_training_state['is_training'] = True
        self.current_training_state['current_epoch'] = epoch
        self.current_training_state['current_batch'] = batch_idx
    
    def update_training_state(self, 
                            current_epoch: int = 0,
                            total_epochs: int = 0,
                            current_batch: int = 0,
                            total_batches: int = 0):
        """更新训练状态"""
        self.current_training_state.update({
            'current_epoch': current_epoch,
            'total_epochs': total_epochs,
            'current_batch': current_batch,
            'total_batches': total_batches
        })
        
        # 估算完成时间
        if self.current_training_state['start_time'] and total_epochs > 0:
            elapsed = time.time() - self.current_training_state['start_time']
            progress = (current_epoch + current_batch / max(total_batches, 1)) / total_epochs
            
            if progress > 0:
                estimated_total = elapsed / progress
                remaining = estimated_total - elapsed
                self.current_training_state['estimated_completion'] = datetime.now() + timedelta(seconds=remaining)
    
    def start_training(self, total_epochs: int, total_batches: int):
        """开始训练"""
        self.current_training_state.update({
            'is_training': True,
            'total_epochs': total_epochs,
            'total_batches': total_batches,
            'start_time': time.time(),
            'current_epoch': 0,
            'current_batch': 0
        })
        
        self.logger.info(f"📊 开始训练监控: {total_epochs} epochs, {total_batches} batches/epoch")
    
    def stop_training(self):
        """停止训练"""
        if self.current_training_state['start_time']:
            total_time = time.time() - self.current_training_state['start_time']
            self.performance_stats['total_training_time'] += total_time
        
        self.current_training_state['is_training'] = False
        self.logger.info("📊 训练监控已停止")
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        # 最近的训练指标
        recent_training = self.training_metrics[-50:] if self.training_metrics else []
        recent_system = self.system_metrics[-50:] if self.system_metrics else []
        
        # GPU状态
        gpu_status = self.gpu_monitor.get_current_metrics()
        gpu_summary = self.gpu_monitor.get_metrics_summary(minutes=5)
        
        return {
            'timestamp': datetime.now().isoformat(),
            'training_state': self.current_training_state,
            'performance_stats': self.performance_stats,
            'recent_training_metrics': [asdict(m) for m in recent_training],
            'recent_system_metrics': [asdict(m) for m in recent_system],
            'gpu_status': {
                'current': {
                    'utilization': gpu_status.gpu_utilization if gpu_status else 0,
                    'memory_percent': gpu_status.memory_percent if gpu_status else 0,
                    'temperature': gpu_status.temperature if gpu_status else 0,
                    'power_usage': gpu_status.power_usage if gpu_status else 0
                } if gpu_status else {},
                'summary': gpu_summary
            }
        }
    
    def export_data(self, filename: Optional[str] = None):
        """导出数据"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"performance_data_{timestamp}.json"
        
        export_file = self.export_path / filename
        
        try:
            data = self.get_dashboard_data()
            
            with open(export_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, default=str)
            
            self.logger.info(f"📊 性能数据已导出到: {export_file}")
            
        except Exception as e:
            self.logger.error(f"导出性能数据失败: {e}")
    
    def generate_report(self) -> str:
        """生成性能报告"""
        if not self.training_metrics:
            return "暂无训练数据"
        
        recent_metrics = self.training_metrics[-100:]
        
        # 计算统计信息
        avg_loss = sum(m.train_loss for m in recent_metrics) / len(recent_metrics)
        avg_throughput = sum(m.throughput for m in recent_metrics if m.throughput > 0) / max(1, len([m for m in recent_metrics if m.throughput > 0]))
        avg_gpu_util = sum(m.gpu_utilization for m in recent_metrics if m.gpu_utilization > 0) / max(1, len([m for m in recent_metrics if m.gpu_utilization > 0]))
        
        report = f"""
🚀 性能监控报告
================

📊 训练统计:
- 平均损失: {avg_loss:.4f}
- 平均吞吐量: {avg_throughput:.0f} samples/s
- 峰值吞吐量: {self.performance_stats['peak_throughput']:.0f} samples/s

🔧 GPU性能:
- 平均利用率: {avg_gpu_util:.1f}%
- 峰值利用率: {self.performance_stats['peak_gpu_utilization']:.1f}%

⏱️ 时间统计:
- 总训练时间: {self.performance_stats['total_training_time']:.1f}s
- 数据点数量: {len(self.training_metrics)}

📈 当前状态:
- 训练中: {'是' if self.current_training_state['is_training'] else '否'}
- 当前Epoch: {self.current_training_state['current_epoch']}/{self.current_training_state['total_epochs']}
"""
        
        return report


# 全局仪表板实例
_dashboard = None

def get_performance_dashboard() -> PerformanceDashboard:
    """获取全局性能仪表板实例"""
    global _dashboard
    if _dashboard is None:
        _dashboard = PerformanceDashboard()
    return _dashboard


def start_performance_monitoring():
    """启动性能监控"""
    dashboard = get_performance_dashboard()
    dashboard.start_monitoring()


def stop_performance_monitoring():
    """停止性能监控"""
    dashboard = get_performance_dashboard()
    dashboard.stop_monitoring()


def record_training_metrics(**kwargs):
    """记录训练指标"""
    dashboard = get_performance_dashboard()
    dashboard.record_training_metrics(**kwargs)
