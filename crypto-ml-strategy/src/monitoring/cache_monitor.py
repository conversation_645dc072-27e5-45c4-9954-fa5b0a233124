"""
缓存性能监控工具
实时监控多级缓存的命中率、性能指标和优化建议
"""

import time
import asyncio
import logging
import threading
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
import json
import numpy as np
from collections import defaultdict, deque

from src.storage.redis_cache import RedisCache
from src.api.async_deepseek_client import AsyncDeepSeekClient
from src.utils.prometheus_metrics import get_metrics


@dataclass
class CacheMetrics:
    """缓存指标数据类"""
    timestamp: datetime
    cache_type: str
    level: str
    hits: int
    misses: int
    hit_rate: float
    total_requests: int
    avg_response_time: float
    cache_size: int
    memory_usage: float


@dataclass
class CacheAnalysis:
    """缓存分析结果"""
    overall_hit_rate: float
    level_hit_rates: Dict[str, float]
    performance_score: float
    bottlenecks: List[str]
    recommendations: List[str]
    efficiency_rating: str


class CacheMonitor:
    """缓存性能监控器"""
    
    def __init__(self, monitoring_interval: int = 30):
        """
        初始化缓存监控器
        
        Args:
            monitoring_interval: 监控间隔（秒）
        """
        self.logger = logging.getLogger(__name__)
        self.monitoring_interval = monitoring_interval
        
        # 缓存实例
        self.redis_cache = RedisCache()
        self.deepseek_client = None
        
        # 监控数据
        self.metrics_history = deque(maxlen=1000)  # 保留最近1000个数据点
        self.current_metrics = {}
        
        # 性能基线
        self.baseline_hit_rate = 0.8  # 期望命中率80%
        self.baseline_response_time = 0.1  # 期望响应时间100ms
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        
        # Prometheus指标
        self.prometheus_metrics = get_metrics()
        
        # 统计锁
        self._lock = threading.Lock()
        
        self.logger.info("🔍 缓存监控器初始化完成")
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            self.logger.warning("监控已在运行中")
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info(f"🚀 开始缓存监控，间隔: {self.monitoring_interval}秒")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=5)
        
        self.logger.info("⏹️ 缓存监控已停止")
    
    def _monitoring_loop(self):
        """监控主循环"""
        while self.is_monitoring:
            try:
                # 收集缓存指标
                metrics = self._collect_cache_metrics()
                
                # 存储指标
                with self._lock:
                    self.metrics_history.append(metrics)
                    self.current_metrics = metrics
                
                # 更新Prometheus指标
                self._update_prometheus_metrics(metrics)
                
                # 分析性能
                analysis = self._analyze_performance(metrics)
                
                # 记录分析结果
                self._log_analysis(analysis)
                
                # 等待下一次监控
                time.sleep(self.monitoring_interval)
                
            except Exception as e:
                self.logger.error(f"监控循环错误: {e}")
                time.sleep(5)  # 错误后短暂等待
    
    def _collect_cache_metrics(self) -> Dict[str, CacheMetrics]:
        """收集缓存指标"""
        metrics = {}
        timestamp = datetime.now()
        
        try:
            # Redis缓存指标
            redis_stats = self.redis_cache.get_stats()
            
            for level in ['L1', 'L2', 'L3']:
                hits = redis_stats.get('hits', 0)
                misses = redis_stats.get('misses', 0)
                total = hits + misses
                hit_rate = hits / total if total > 0 else 0.0
                
                metrics[f'redis_{level}'] = CacheMetrics(
                    timestamp=timestamp,
                    cache_type='redis',
                    level=level,
                    hits=hits,
                    misses=misses,
                    hit_rate=hit_rate,
                    total_requests=total,
                    avg_response_time=self._measure_cache_response_time('redis', level),
                    cache_size=self._get_cache_size('redis', level),
                    memory_usage=self._get_memory_usage('redis')
                )
            
            # L3内存缓存指标
            l3_hits = redis_stats.get('l3_hits', 0)
            l3_misses = redis_stats.get('l3_misses', 0)
            l3_total = l3_hits + l3_misses
            l3_hit_rate = l3_hits / l3_total if l3_total > 0 else 0.0
            
            metrics['memory_l3'] = CacheMetrics(
                timestamp=timestamp,
                cache_type='memory',
                level='L3',
                hits=l3_hits,
                misses=l3_misses,
                hit_rate=l3_hit_rate,
                total_requests=l3_total,
                avg_response_time=self._measure_cache_response_time('memory', 'L3'),
                cache_size=len(self.redis_cache.l3_cache),
                memory_usage=self._get_memory_usage('l3')
            )
            
            # DeepSeek API缓存指标
            if self.deepseek_client:
                deepseek_stats = self.deepseek_client.get_stats()
                ds_hits = deepseek_stats.get('cache_hits', 0)
                ds_misses = deepseek_stats.get('cache_misses', 0)
                ds_total = ds_hits + ds_misses
                ds_hit_rate = ds_hits / ds_total if ds_total > 0 else 0.0
                
                metrics['deepseek_api'] = CacheMetrics(
                    timestamp=timestamp,
                    cache_type='deepseek',
                    level='API',
                    hits=ds_hits,
                    misses=ds_misses,
                    hit_rate=ds_hit_rate,
                    total_requests=ds_total,
                    avg_response_time=deepseek_stats.get('avg_response_time', 0.0),
                    cache_size=deepseek_stats.get('cache_size', 0),
                    memory_usage=0.0
                )
            
        except Exception as e:
            self.logger.error(f"收集缓存指标失败: {e}")
        
        return metrics
    
    def _measure_cache_response_time(self, cache_type: str, level: str) -> float:
        """测量缓存响应时间"""
        try:
            test_key = f"test_response_time_{cache_type}_{level}_{int(time.time())}"
            test_value = "test_value"
            
            # 测量设置时间
            start_time = time.time()
            if cache_type == 'redis':
                self.redis_cache.set(test_key, test_value, level=level)
            elif cache_type == 'memory':
                self.redis_cache._set_l3_cache(test_key, test_value)
            set_time = time.time() - start_time
            
            # 测量获取时间
            start_time = time.time()
            if cache_type == 'redis':
                self.redis_cache.get(test_key, level=level)
            elif cache_type == 'memory':
                self.redis_cache._get_l3_cache(test_key)
            get_time = time.time() - start_time
            
            # 清理测试数据
            if cache_type == 'redis':
                self.redis_cache.delete(test_key, level=level)
            
            return (set_time + get_time) / 2
            
        except Exception as e:
            self.logger.debug(f"测量响应时间失败: {e}")
            return 0.0
    
    def _get_cache_size(self, cache_type: str, level: str) -> int:
        """获取缓存大小"""
        try:
            if cache_type == 'redis':
                # 估算Redis缓存大小
                pattern = f"{self.redis_cache.key_prefix}{self.redis_cache.cache_levels[level]['prefix']}*"
                keys = self.redis_cache.redis_client.keys(pattern)
                return len(keys)
            elif cache_type == 'memory':
                return len(self.redis_cache.l3_cache)
        except Exception as e:
            self.logger.debug(f"获取缓存大小失败: {e}")
        
        return 0
    
    def _get_memory_usage(self, cache_type: str) -> float:
        """获取内存使用量（MB）"""
        try:
            if cache_type == 'redis':
                info = self.redis_cache.redis_client.info('memory')
                return info.get('used_memory', 0) / 1024 / 1024  # 转换为MB
            elif cache_type == 'l3':
                # 估算L3缓存内存使用
                import sys
                total_size = sum(sys.getsizeof(k) + sys.getsizeof(v) 
                               for k, v in self.redis_cache.l3_cache.items())
                return total_size / 1024 / 1024  # 转换为MB
        except Exception as e:
            self.logger.debug(f"获取内存使用量失败: {e}")
        
        return 0.0
    
    def _update_prometheus_metrics(self, metrics: Dict[str, CacheMetrics]):
        """更新Prometheus指标"""
        try:
            for cache_key, metric in metrics.items():
                # 记录缓存命中和未命中
                cache_type = f"{metric.cache_type}_{metric.level}"
                
                # 更新命中率
                if hasattr(self.prometheus_metrics, 'cache_hits_total'):
                    self.prometheus_metrics.cache_hits_total.labels(
                        cache_type=cache_type
                    )._value._value = metric.hits
                    
                    self.prometheus_metrics.cache_misses_total.labels(
                        cache_type=cache_type
                    )._value._value = metric.misses
                
        except Exception as e:
            self.logger.debug(f"更新Prometheus指标失败: {e}")
    
    def _analyze_performance(self, metrics: Dict[str, CacheMetrics]) -> CacheAnalysis:
        """分析缓存性能"""
        try:
            # 计算整体命中率
            total_hits = sum(m.hits for m in metrics.values())
            total_requests = sum(m.total_requests for m in metrics.values())
            overall_hit_rate = total_hits / total_requests if total_requests > 0 else 0.0
            
            # 各级别命中率
            level_hit_rates = {
                key: metric.hit_rate for key, metric in metrics.items()
            }
            
            # 性能评分（0-100）
            hit_rate_score = min(overall_hit_rate / self.baseline_hit_rate * 50, 50)
            
            avg_response_time = np.mean([m.avg_response_time for m in metrics.values()])
            response_time_score = max(50 - (avg_response_time / self.baseline_response_time * 25), 0)
            
            performance_score = hit_rate_score + response_time_score
            
            # 识别瓶颈
            bottlenecks = []
            if overall_hit_rate < self.baseline_hit_rate:
                bottlenecks.append(f"整体命中率过低: {overall_hit_rate:.2%}")
            
            if avg_response_time > self.baseline_response_time:
                bottlenecks.append(f"响应时间过慢: {avg_response_time:.3f}s")
            
            for key, metric in metrics.items():
                if metric.hit_rate < 0.5:
                    bottlenecks.append(f"{key}命中率过低: {metric.hit_rate:.2%}")
            
            # 生成建议
            recommendations = self._generate_recommendations(metrics, bottlenecks)
            
            # 效率评级
            if performance_score >= 80:
                efficiency_rating = "优秀"
            elif performance_score >= 60:
                efficiency_rating = "良好"
            elif performance_score >= 40:
                efficiency_rating = "一般"
            else:
                efficiency_rating = "需要优化"
            
            return CacheAnalysis(
                overall_hit_rate=overall_hit_rate,
                level_hit_rates=level_hit_rates,
                performance_score=performance_score,
                bottlenecks=bottlenecks,
                recommendations=recommendations,
                efficiency_rating=efficiency_rating
            )
            
        except Exception as e:
            self.logger.error(f"性能分析失败: {e}")
            return CacheAnalysis(
                overall_hit_rate=0.0,
                level_hit_rates={},
                performance_score=0.0,
                bottlenecks=["分析失败"],
                recommendations=["检查监控系统"],
                efficiency_rating="未知"
            )
    
    def _generate_recommendations(self, metrics: Dict[str, CacheMetrics], bottlenecks: List[str]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于瓶颈生成建议
        for bottleneck in bottlenecks:
            if "命中率过低" in bottleneck:
                recommendations.append("增加缓存TTL时间")
                recommendations.append("优化缓存键生成策略")
                recommendations.append("实施缓存预热机制")
            
            if "响应时间过慢" in bottleneck:
                recommendations.append("优化缓存序列化方式")
                recommendations.append("增加缓存连接池大小")
                recommendations.append("考虑使用更快的缓存存储")
        
        # 基于指标生成建议
        for key, metric in metrics.items():
            if metric.cache_size > 10000:
                recommendations.append(f"考虑清理{key}缓存，当前大小: {metric.cache_size}")
            
            if metric.memory_usage > 100:  # 100MB
                recommendations.append(f"监控{key}内存使用，当前: {metric.memory_usage:.1f}MB")
        
        return list(set(recommendations))  # 去重
    
    def _log_analysis(self, analysis: CacheAnalysis):
        """记录分析结果"""
        self.logger.info(f"📊 缓存性能分析:")
        self.logger.info(f"   整体命中率: {analysis.overall_hit_rate:.2%}")
        self.logger.info(f"   性能评分: {analysis.performance_score:.1f}/100")
        self.logger.info(f"   效率评级: {analysis.efficiency_rating}")
        
        if analysis.bottlenecks:
            self.logger.warning(f"⚠️ 发现瓶颈:")
            for bottleneck in analysis.bottlenecks:
                self.logger.warning(f"   - {bottleneck}")
        
        if analysis.recommendations:
            self.logger.info(f"💡 优化建议:")
            for rec in analysis.recommendations[:3]:  # 只显示前3个建议
                self.logger.info(f"   - {rec}")
    
    def get_current_metrics(self) -> Dict[str, CacheMetrics]:
        """获取当前指标"""
        with self._lock:
            return self.current_metrics.copy()
    
    def get_metrics_history(self, hours: int = 1) -> List[Dict[str, CacheMetrics]]:
        """获取历史指标"""
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        with self._lock:
            return [
                metrics for metrics in self.metrics_history
                if any(m.timestamp >= cutoff_time for m in metrics.values())
            ]
    
    def generate_report(self) -> Dict[str, Any]:
        """生成缓存性能报告"""
        current_metrics = self.get_current_metrics()
        if not current_metrics:
            return {"error": "没有可用的指标数据"}
        
        analysis = self._analyze_performance(current_metrics)
        
        return {
            "timestamp": datetime.now().isoformat(),
            "metrics": {key: asdict(metric) for key, metric in current_metrics.items()},
            "analysis": asdict(analysis),
            "summary": {
                "total_caches": len(current_metrics),
                "overall_hit_rate": analysis.overall_hit_rate,
                "performance_score": analysis.performance_score,
                "efficiency_rating": analysis.efficiency_rating
            }
        }
    
    def set_deepseek_client(self, client: AsyncDeepSeekClient):
        """设置DeepSeek客户端"""
        self.deepseek_client = client
        self.logger.info("✅ DeepSeek客户端已设置到缓存监控器")


# 全局监控器实例
_cache_monitor_instance = None

def get_cache_monitor() -> CacheMonitor:
    """获取全局缓存监控器实例"""
    global _cache_monitor_instance
    if _cache_monitor_instance is None:
        _cache_monitor_instance = CacheMonitor()
    return _cache_monitor_instance
