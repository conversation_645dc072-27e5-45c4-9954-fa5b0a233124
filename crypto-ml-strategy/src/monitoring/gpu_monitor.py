import logging
import sys
import time
import os
try:
    import pynvml
except ImportError:
    print("pynvml library not found. Please install it using: pip install pynvml")
    sys.exit(1)

logger = logging.getLogger(__name__)

class GPUMonitor:
    """A utility to monitor NVIDIA GPU status using pynvml."""

    def __init__(self):
        self.handle = None
        self.is_initialized = False

        if os.environ.get("FORCE_CPU_TESTING") == "true":
            logger.info("FORCE_CPU_TESTING is set. GPUMonitor will be disabled.")
            return
            
        try:
            pynvml.nvmlInit()
            # Assuming we are monitoring the first GPU device (device 0)
            self.handle = pynvml.nvmlDeviceGetHandleByIndex(0)
            self.is_initialized = True
            logger.info("GPUMonitor initialized successfully.")
        except pynvml.NVMLError as e:
            logger.error(f"Failed to initialize GPUMonitor. Is an NVIDIA driver installed? Error: {e}")
            self.is_initialized = False

    def get_status(self) -> dict:
        """
        Retrieves the current GPU status.
        
        Returns:
            A dictionary with GPU utilization and memory stats, or None if not initialized.
        """
        if not self.is_initialized:
            return None
            
        try:
            utilization = pynvml.nvmlDeviceGetUtilizationRates(self.handle)
            memory_info = pynvml.nvmlDeviceGetMemoryInfo(self.handle)
            
            return {
                "timestamp": time.time(),
                "gpu_utilization_percent": utilization.gpu,
                "memory_utilization_percent": utilization.memory,
                "memory_used_mb": memory_info.used / (1024**2),
                "memory_total_mb": memory_info.total / (1024**2),
                "memory_used_percent": (memory_info.used / memory_info.total) * 100 if memory_info.total > 0 else 0
            }
        except pynvml.NVMLError as e:
            logger.error(f"Failed to get GPU status. Error: {e}")
            return {}

    def shutdown(self):
        """Shuts down the NVML library."""
        if self.is_initialized:
            try:
                pynvml.nvmlShutdown()
                logger.info("GPUMonitor shut down successfully.")
            except pynvml.NVMLError as e:
                logger.error(f"Failed to shut down NVML. Error: {e}")

if __name__ == '__main__':
    # Example usage for standalone testing
    logging.basicConfig(level=logging.INFO, stream=sys.stdout)
    monitor = GPUMonitor()
    if monitor.is_initialized:
        for i in range(5):
            status = monitor.get_status()
            if status:
                logger.info(f"Report {i+1}: {status}")
            time.sleep(2)
        monitor.shutdown()
    else:
        logger.error("Could not run GPUMonitor example.")
