"""
Unified Trainer Module
A single, configurable trainer to replace all other trainers.
"""
from __future__ import annotations

import asyncio
import time
import logging
from typing import Any, Callable, Dict, List, Optional, Protocol, Tuple, Union

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
from dataclasses import dataclass, field
from pathlib import Path

# Removed obsolete import
# from src.utils.gpu_utils import get_device, move_to_device
from src.utils.metrics import MetricsTracker, calculate_metrics
from src.visualization.training_visualizer import TrainingVisualizer
from src.api.async_deepseek_client import AsyncDeepSeekClient
from prometheus_client import Gauge, start_http_server, REGISTRY

from src.monitoring.gpu_monitor import GPUMonitor


# region: Prometheus Metrics
# Deregister default metrics to avoid clutter
for coll in list(REGISTRY._collector_to_names.keys()):
    REGISTRY.unregister(coll)

TRAIN_LOSS = Gauge('training_loss', 'Current training loss')
VAL_LOSS = Gauge('validation_loss', 'Current validation loss')
TRAIN_ACCURACY = Gauge('training_accuracy', 'Current training accuracy')
VAL_ACCURACY = Gauge('validation_accuracy', 'Current validation accuracy')
TRAIN_F1 = Gauge('training_f1_score', 'Current training F1 score')
VAL_F1 = Gauge('validation_f1_score', 'Current validation F1 score')
EPOCH_GAUGE = Gauge('training_epoch', 'Current training epoch')
GPU_UTILIZATION = Gauge('gpu_utilization_percent', 'Current GPU utilization')
GPU_MEMORY_USED = Gauge('gpu_memory_used_percent', 'Current GPU memory usage')
# endregion

# region: Helper Functions for Device Handling
def _move_to_device(data: Any, device: torch.device) -> Any:
    """Recursively move tensors in a nested structure to the specified device."""
    if isinstance(data, torch.Tensor):
        return data.to(device)
    if isinstance(data, dict):
        return {k: _move_to_device(v, device) for k, v in data.items()}
    if isinstance(data, list):
        return [_move_to_device(v, device) for v in data]
    if isinstance(data, tuple):
        return tuple(_move_to_device(v, device) for v in data)
    return data
# endregion

# region: Losses
class DistillationLoss(nn.Module):
    """Knowledge Distillation Loss Function."""
    def __init__(self, temperature: float = 2.5, alpha: float = 0.8, beta: float = 0.2):
        super().__init__()
        self.temperature = temperature
        self.alpha = alpha
        self.beta = beta
        self.kl_div = nn.KLDivLoss(reduction='batchmean')
        self.ce_loss = nn.CrossEntropyLoss()

    def forward(self, student_logits, teacher_logits, targets):
        student_soft = F.log_softmax(student_logits / self.temperature, dim=1)
        teacher_soft = F.softmax(teacher_logits / self.temperature, dim=1)
        soft_loss = self.kl_div(student_soft, teacher_soft) * (self.temperature ** 2)
        hard_loss = self.ce_loss(student_logits, targets)
        return self.alpha * soft_loss + self.beta * hard_loss
# endregion

# region: Callbacks
class TrainingCallback(Protocol):
    """Protocol for training callbacks."""
    async def on_train_begin(self, trainer: "UnifiedTrainer"): ...
    async def on_train_end(self, trainer: "UnifiedTrainer"): ...
    async def on_epoch_begin(self, trainer: "UnifiedTrainer"): ...
    async def on_epoch_end(self, trainer: "UnifiedTrainer", metrics: Dict[str, Any]): ...
    async def on_step_begin(self, trainer: "UnifiedTrainer"): ...
    async def on_step_end(self, trainer: "UnifiedTrainer", loss: torch.Tensor): ...

class LoggerCallback(TrainingCallback):
    """Callback for logging training progress."""
    def __init__(self, logger: Optional[logging.Logger] = None, log_interval: int = 1):
        self.logger = logger or logging.getLogger(__name__)
        self.log_interval = log_interval

    async def on_epoch_end(self, trainer: "UnifiedTrainer", metrics: Dict[str, Any]):
        if trainer.state.epoch % self.log_interval == 0:
            self.logger.info(f"Epoch {trainer.state.epoch}/{trainer.config.epochs}: {metrics}")

class MetricsCallback(TrainingCallback):
    """Callback for tracking and calculating metrics."""
    def __init__(self):
        self.metrics_tracker = MetricsTracker()

    async def on_epoch_end(self, trainer: "UnifiedTrainer", metrics: Dict[str, Any]):
        train_metrics = {k: v for k, v in metrics.items() if not k.startswith('val_')}
        val_metrics = {k: v for k, v in metrics.items() if k.startswith('val_')}
        if train_metrics:
            self.metrics_tracker.add_metrics(train_metrics, trainer.state.epoch, 'train')
        if val_metrics:
            self.metrics_tracker.add_metrics(val_metrics, trainer.state.epoch, 'val')

class EarlyStoppingCallback(TrainingCallback):
    """Callback for early stopping."""
    def __init__(self, monitor: str = 'val_loss', patience: int = 10, mode: str = 'min'):
        self.monitor = monitor
        self.patience = patience
        self.mode = mode
        self.counter = 0
        self.best_metric = None

    async def on_epoch_end(self, trainer: "UnifiedTrainer", metrics: Dict[str, Any]):
        metric = metrics.get(self.monitor)
        if metric is None: return

        if self.best_metric is None or \
           (self.mode == 'min' and metric < self.best_metric) or \
           (self.mode == 'max' and metric > self.best_metric):
            self.best_metric = metric
            self.counter = 0
        else:
            self.counter += 1

        if self.counter >= self.patience:
            trainer.state.is_training = False
            logging.info(f"Early stopping triggered at epoch {trainer.state.epoch}")

class VisualizerCallback(TrainingCallback):
    """Callback for visualizing training progress."""
    def __init__(self, save_dir: str = "plots"):
        self.visualizer = TrainingVisualizer(save_dir=save_dir)

    async def on_step_end(self, trainer: "UnifiedTrainer", loss: torch.Tensor):
        if trainer.state.step % 100 == 0:
            self.visualizer.update_batch_metrics(trainer.state.step, loss.item(), 0, 0)

class CheckpointCallback(TrainingCallback):
    """Callback for saving model checkpoints."""
    def __init__(self, save_path: str, monitor: str = 'val_loss', mode: str = 'min'):
        self.save_path = Path(save_path)
        self.monitor = monitor
        self.mode = mode
        self.best_metric = None
        self.save_path.mkdir(parents=True, exist_ok=True)

    async def on_epoch_end(self, trainer: "UnifiedTrainer", metrics: Dict[str, Any]):
        metric = metrics.get(self.monitor)
        if metric is None: return

        if self.best_metric is None or \
           (self.mode == 'min' and metric < self.best_metric) or \
           (self.mode == 'max' and metric > self.best_metric):
            self.best_metric = metric
            checkpoint_path = self.save_path / f"best_model_epoch_{trainer.state.epoch}.pt"
            torch.save(trainer.model.state_dict(), checkpoint_path)
            logging.info(f"Checkpoint saved to {checkpoint_path}")

class PrometheusCallback(TrainingCallback):
    """Callback for exposing metrics to Prometheus."""
    def __init__(self, interval: int = 5):
        self.interval = interval
        self._task = None

    async def _monitor_gpu(self):
        """Periodically polls nvidia-smi and updates GPU metrics."""
        while True:
            try:
                command = "nvidia-smi --query-gpu=utilization.gpu,memory.used,memory.total --format=csv,noheader,nounits"
                result = await asyncio.create_subprocess_shell(
                    command,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                stdout, stderr = await result.communicate()

                if result.returncode == 0:
                    # Assuming a single GPU for simplicity
                    utilization, mem_used, mem_total = stdout.decode().strip().split(', ')
                    utilization = float(utilization)
                    mem_percent = (float(mem_used) / float(mem_total)) * 100
                    
                    GPU_UTILIZATION.set(utilization)
                    GPU_MEMORY_USED.set(mem_percent)
                else:
                    logging.warning(f"nvidia-smi command failed: {stderr.decode()}")

            except FileNotFoundError:
                logging.warning("nvidia-smi not found. GPU monitoring is disabled.")
                break # Exit the loop if nvidia-smi is not available
            except Exception as e:
                logging.error(f"Error in GPU monitoring task: {e}")
            
            await asyncio.sleep(self.interval)

    async def on_train_begin(self, trainer: "UnifiedTrainer"):
        if torch.cuda.is_available():
            self._task = asyncio.create_task(self._monitor_gpu())
            logging.info("🚀 Started background GPU monitoring task.")

    async def on_train_end(self, trainer: "UnifiedTrainer"):
        if self._task and not self._task.done():
            self._task.cancel()
            logging.info("🛑 Stopped background GPU monitoring task.")

    async def on_epoch_end(self, trainer: "UnifiedTrainer", metrics: Dict[str, Any]):
        EPOCH_GAUGE.set(trainer.state.epoch)
        
        # Training metrics
        if 'train_loss' in metrics:
            TRAIN_LOSS.set(metrics['train_loss'])
        if 'accuracy' in metrics:
            TRAIN_ACCURACY.set(metrics['accuracy'])
        if 'f1_score' in metrics:
            TRAIN_F1.set(metrics['f1_score'])
            
        # Validation metrics (prefixed with 'val_')
        if 'val_loss' in metrics:
            VAL_LOSS.set(metrics['val_loss'])
        if 'val_accuracy' in metrics:
            VAL_ACCURACY.set(metrics['val_accuracy'])
        if 'val_f1_score' in metrics:
            VAL_F1.set(metrics['val_f1_score'])
# endregion

# region: Training Strategies
class TrainingStep(Protocol):
    """Protocol for the training step strategy."""
    async def __call__(self, trainer: "UnifiedTrainer", batch: Any) -> torch.Tensor: ...

class StandardTrainingStep(TrainingStep):
    """A standard training step strategy."""
    def __init__(self, criterion: nn.Module):
        self.criterion = criterion

    async def __call__(self, trainer: "UnifiedTrainer", batch: Any) -> torch.Tensor:
        if isinstance(batch, dict):
            features = batch['features']
            labels = batch['labels']
        else:
            features, labels = batch[0], batch[1]
        outputs = trainer.model(features)
        logits = outputs.get('signal', outputs) if isinstance(outputs, dict) else outputs
        return self.criterion(logits, labels)

class DistillationTrainingStep(TrainingStep):
    """Training step for knowledge distillation."""
    def __init__(self, config: "DistillationConfig"):
        self.config = config
        self.loss_fn = DistillationLoss(config.temperature, config.alpha, config.beta)
        self.teacher_calls_this_epoch = 0

    def _should_call_teacher(self, student_logits: torch.Tensor) -> bool:
        if not self.config.deepseek_client or self.teacher_calls_this_epoch >= self.config.max_teacher_calls_per_epoch:
            return False
        confidence = torch.softmax(student_logits, dim=-1).max().item()
        return confidence < self.config.teacher_call_threshold

    async def __call__(self, trainer: "UnifiedTrainer", batch: Any) -> torch.Tensor:
        features = batch['features']
        labels = batch['labels']
        outputs = trainer.model(features)
        student_logits = outputs.get('signal', outputs) if isinstance(outputs, dict) else outputs

        if self._should_call_teacher(student_logits):
            try:
                teacher_preds_np = await self.config.deepseek_client.predict_batch_async(features.cpu().numpy())
                teacher_logits = torch.tensor(teacher_preds_np, dtype=student_logits.dtype, device=student_logits.device)
                self.teacher_calls_this_epoch += 1
                return self.loss_fn(student_logits, teacher_logits, labels)
            except Exception as e:
                logging.warning(f"Teacher call failed: {e}, falling back to standard loss.")
        
        return F.cross_entropy(student_logits, labels)
# endregion

# region: Configurations
@dataclass
class DistillationConfig:
    """Configuration for distillation training."""
    deepseek_client: AsyncDeepSeekClient
    temperature: float = 2.5
    alpha: float = 0.8
    beta: float = 0.2
    teacher_call_threshold: float = 0.4
    max_teacher_calls_per_epoch: int = 500

@dataclass
class TrainerConfig:
    """Configuration for the UnifiedTrainer."""
    epochs: int = 10
    learning_rate: float = 1e-3
    device: Optional[torch.device] = None
    use_amp: bool = True
    use_compile: bool = True
    callbacks: Optional[List[TrainingCallback]] = field(default=None)
    training_step: TrainingStep = field(default_factory=lambda: StandardTrainingStep(nn.CrossEntropyLoss()))
    distillation: Optional[DistillationConfig] = None

@dataclass
class TrainerState:
    """State of the UnifiedTrainer."""
    epoch: int = 0
    step: int = 0
    is_training: bool = False
    metrics: Dict[str, Any] = field(default_factory=dict)
    
    def update_metrics(self, new_metrics: Dict[str, Any]):
        self.metrics.update(new_metrics)
# endregion

class UnifiedTrainer:
    """A unified, configuration-driven trainer that uses composition over inheritance."""
    def __init__(self, model: nn.Module, config: TrainerConfig, device: Optional[Union[str, torch.device]] = None, monitor: Optional[Any] = None):
        self.logger = logging.getLogger(__name__)
        self.model = model
        self.config = config
        # Use provided callbacks or create default if none are given
        self.callbacks = config.callbacks if config.callbacks is not None else [LoggerCallback(), MetricsCallback()]
        
        # Manually inject monitor callback if monitor exists
        if monitor and hasattr(monitor, 'get_callback'):
            self.callbacks.append(monitor.get_callback())
        self.state = TrainerState()
        self.monitor = monitor  # 保存对监控器的直接引用
        self.gpu_monitor = GPUMonitor() if torch.cuda.is_available() else None
        # Final, most robust implementation: explicitly check for allowed types.
        # This prevents MagicMock objects from being treated as valid devices.
        # Priority: 1. constructor arg, 2. config, 3. fallback to cpu.
        if isinstance(device, (str, torch.device)):
            selected_device = device
        else:
            # Fallback to config if constructor arg is invalid (e.g., None or MagicMock)
            config_device = getattr(self.config, 'device', None)
            if isinstance(config_device, (str, torch.device)):
                selected_device = config_device
            else:
                # Fallback to cpu if both are invalid
                selected_device = "cpu"

        # At this point, selected_device is guaranteed to be a str or torch.device.
        # Create the torch.device object if it's a string.
        if isinstance(selected_device, str):
            self.device = torch.device(selected_device)
        else:
            self.device = selected_device
        
        self.model.to(self.device)

        if self.config.use_compile and hasattr(torch, 'compile'):
            self.model = torch.compile(self.model)
        self.optimizer = torch.optim.Adam(self.model.parameters(), lr=self.config.learning_rate)
        # 兼容不同PyTorch版本的GradScaler
        try:
            self.scaler = torch.amp.GradScaler(enabled=self.config.use_amp and self.device.type == 'cuda')
        except AttributeError:
            try:
                self.scaler = torch.cuda.amp.GradScaler(enabled=self.config.use_amp and self.device.type == 'cuda')
            except AttributeError:
                # 创建简单的替代类
                class SimpleScaler:
                    def __init__(self, enabled=True):
                        self._enabled = enabled
                    def scale(self, loss):
                        return loss
                    def step(self, optimizer):
                        optimizer.step()
                    def update(self):
                        pass
                    def is_enabled(self):
                        return self._enabled
                self.scaler = SimpleScaler(enabled=self.config.use_amp and self.device.type == 'cuda')

    async def _execute_callbacks(self, event_name: str, **kwargs):
        await asyncio.gather(*(getattr(cb, event_name)(self, **kwargs) for cb in self.callbacks if hasattr(cb, event_name)))

    async def train(self, train_loader: DataLoader, val_loader: Optional[DataLoader] = None):
        self.state.is_training = True
        await self._execute_callbacks('on_train_begin')
        try:
            for epoch in range(self.config.epochs):
                if not self.state.is_training: break
                self.state.epoch = epoch
                await self._execute_callbacks('on_epoch_begin')
                if self.gpu_monitor:
                    self.logger.info(f"GPU Status at start of epoch {epoch}: {self.gpu_monitor.get_status()}")
                
                self.model.train()
                for batch in train_loader:
                    await self._execute_callbacks('on_step_begin')
                    # Changed: Use the new helper function
                    batch = _move_to_device(batch, self.device)
                    
                    # Conditionally apply autocast only for CUDA AMP
                    if self.device.type == 'cuda' and self.config.use_amp:
                        with torch.autocast(device_type='cuda', enabled=True):
                            loss = await self.config.training_step(self, batch)
                    else:
                        loss = await self.config.training_step(self, batch)

                    self.optimizer.zero_grad()
                    if self.scaler.is_enabled():
                        self.scaler.scale(loss).backward()
                        self.scaler.step(self.optimizer)
                        self.scaler.update()
                    else:
                        loss.backward()
                        self.optimizer.step()
                    
                    self.state.step += 1
                    self.state.metrics['train_loss'] = loss.item()
                    await self._execute_callbacks('on_step_end', loss=loss)

                if self.gpu_monitor and self.state.step % 20 == 0: # Log every 20 steps
                    self.logger.info(f"GPU Status mid-epoch {epoch}, step {self.state.step}: {self.gpu_monitor.get_status()}")

                if val_loader: await self.validate(val_loader)
                await self._execute_callbacks('on_epoch_end', metrics=self.state.metrics)
                if self.gpu_monitor:
                    self.logger.info(f"GPU Status at end of epoch {epoch}: {self.gpu_monitor.get_status()}")
            
            # Save final model
            await self._save_final_model()

            self.state.is_training = False
            await self._execute_callbacks('on_train_end')
        finally:
            if self.gpu_monitor:
                self.gpu_monitor.shutdown()


    async def _save_final_model(self):
        """Saves the final model state."""
        # Find the CheckpointCallback to determine the save path
        checkpoint_cb = next((cb for cb in self.callbacks if isinstance(cb, CheckpointCallback)), None)
        if checkpoint_cb:
            save_path = checkpoint_cb.save_path
            save_path.mkdir(parents=True, exist_ok=True)
            final_model_path = save_path / "final_model.pt"
            torch.save(self.model.state_dict(), final_model_path)
            logging.info(f"✅ Final model saved to {final_model_path}")
        else:
            logging.warning("No CheckpointCallback configured. Final model will not be saved.")

    async def validate(self, data_loader: DataLoader):
        self.model.eval()
        total_loss, all_preds, all_labels = 0, [], []
        with torch.no_grad():
            for batch in data_loader:
                # Changed: Use the new helper function
                batch = _move_to_device(batch, self.device)
                if isinstance(batch, dict):
                    features = batch['features']
                    labels = batch['labels']
                else:
                    features, labels = batch[0], batch[1]
                outputs = self.model(features)
                logits = outputs.get('signal', outputs) if isinstance(outputs, dict) else outputs
                total_loss += F.cross_entropy(logits, labels).item()
                all_preds.append(torch.argmax(logits, dim=1).cpu())
                all_labels.append(labels.cpu())
        
        val_metrics = calculate_metrics(torch.cat(all_labels).numpy(), torch.cat(all_preds).numpy())
        val_metrics['val_loss'] = total_loss / len(data_loader)
        self.state.update_metrics(val_metrics)
