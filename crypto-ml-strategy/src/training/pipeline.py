#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
训练管道 (Training Pipeline)

该模块定义了端到端的训练流程，将数据准备、模型训练、验证和保存等步骤封装成一个独立的、可重用的组件。
"""

import asyncio
import logging
import sys
import time
from typing import Dict, Any, Optional
from pathlib import Path
import torch
from torch.utils.data import DataLoader, TensorDataset
from tqdm import tqdm
import numpy as np
import pandas as pd
import os
from datetime import datetime
import json

# 修复：解决循环导入问题
from typing import TYPE_CHECKING
if TYPE_CHECKING:
    from src.core.application import CryptoMLApplication

from src.data import DataManager


class TrainingPipeline:
    """
    端到端训练管道，负责协调整个训练流程。
    """

    def __init__(self, app: "CryptoMLApplication"):
        """
        初始化训练管道。

        Args:
            app (CryptoMLApplication): 主应用程序实例的引用。
        """
        self.app = app
        self.config = app.config
        self.device = app.device
        self.logger = logging.getLogger(__name__)

    async def run(
        self,
        data_path: Optional[str] = None,
        model_path: Optional[str] = None,
        output_path: Optional[str] = None,
        **kwargs,
    ):
        """运行训练流程"""
        print("DEBUG: Entered TrainingPipeline.run", file=sys.stderr)
        sys.stderr.flush()
        app = self.app
        if not app.is_initialized:
            print("DEBUG: App not initialized, calling app.initialize()", file=sys.stderr)
            sys.stderr.flush()
            await app.initialize()

        if not app.trainer:
            raise RuntimeError("Trainer not initialized")

        app.is_training = True
        try:
            self.logger.info("🚀 Starting enhanced training process...")
            print("DEBUG: Starting enhanced training process...", file=sys.stderr)
            sys.stderr.flush()
            
            # ... (monitor setup)

            # --- 1. Data Preparation ---
            self.logger.info("🚚 Preparing data using the application-level Unified DataManager...")
            print("DEBUG: Preparing data using DataManager...", file=sys.stderr)
            sys.stderr.flush()

            if not self.app.data_manager:
                raise RuntimeError("DataManager not initialized in the main application.")
            
            data_manager = self.app.data_manager

            if data_path:
                # ...
                train_loader, val_loader, test_loader = data_manager.create_dataloaders_from_file(Path(data_path))
            else:
                symbols = kwargs.get('symbols', self.config.get('training', {}).get('symbols', ['BTCUSDT']))
                # ... (other params)
                dl_config = self.config.get('gpu', {}).get('dataloader', {})
                batch_size = kwargs.get('batch_size', self.config.get('training', {}).get('batch_size', 8192))
                
                # Performance Optimization: Enable multi-worker data loading and pin memory
                # This allows data to be pre-fetched in parallel, preventing the GPU
                # from waiting for data from the CPU.
                num_workers = os.cpu_count() or 2 # Use available cores, fallback to 2
                dataloader_kwargs = {
                    'batch_size': batch_size,
                    'num_workers': num_workers,
                    'pin_memory': True,
                    'persistent_workers': True if num_workers > 0 else False,
                }
                self.logger.info(f"DataLoader configured with num_workers={num_workers}, pin_memory=True")

                timeframes = kwargs.get('timeframes', ['1h']) 
                
                print("DEBUG: Calling data_manager.run_full_pipeline...", file=sys.stderr)
                sys.stderr.flush()
                train_loader, val_loader, test_loader = await data_manager.run_full_pipeline(
                    symbols=symbols,
                    timeframes=timeframes,
                    force_refresh=kwargs.get('force_refresh', False),
                    days=kwargs.get('days', 90),
                    dataloader_kwargs=dataloader_kwargs
                )
                print("DEBUG: data_manager.run_full_pipeline completed.", file=sys.stderr)
                sys.stderr.flush()


            if not train_loader or not val_loader:
                raise RuntimeError("❌ DataManager failed to create DataLoaders. Aborting training.")
            
            self.logger.info(f"✅ DataLoaders created successfully.")
            print("DEBUG: DataLoaders created successfully.", file=sys.stderr)
            sys.stderr.flush()

            # --- 2. Model and Training Setup ---
            if hasattr(train_loader.dataset, 'features') and train_loader.dataset.features is not None:
                print("DEBUG: Updating model feature dimension...", file=sys.stderr)
                sys.stderr.flush()
                actual_feature_dim = train_loader.dataset.features.shape[-1]
                app.update_model_feature_dim(actual_feature_dim)
                self.logger.info(f"🧬 Model feature dimension updated to: {actual_feature_dim}")
                print("DEBUG: Model feature dimension updated.", file=sys.stderr)
                sys.stderr.flush()
            else:
                self.logger.warning("Could not determine feature dimension from train_loader.dataset.")

            # ... (trainer setup)
            
            print("DEBUG: Starting training loop...", file=sys.stderr)
            sys.stderr.flush()
            # --- 3. Model Training ---
            self.logger.info(f"🚀 Starting model training for {app.trainer.config.epochs} epochs...")
            print("DEBUG: Calling app.trainer.train...", file=sys.stderr)
            sys.stderr.flush()

            await app.trainer.train(
                train_loader=train_loader,
                val_loader=val_loader
            )

            print("DEBUG: app.trainer.train completed.", file=sys.stderr)
            sys.stderr.flush()

            # --- 4. Save Selected Features ---
            if data_manager.selected_features:
                self.logger.info("💾 Saving selected features list...")
                features_path = Path(self.config.get('storage', {}).get('directories', {}).get('models')) / "selected_features.json"
                features_path.parent.mkdir(parents=True, exist_ok=True)
                with open(features_path, 'w') as f:
                    json.dump(data_manager.selected_features, f, indent=4)
                self.logger.info(f"✅ Selected features saved to {features_path}")
            
            # The results are now in the trainer's state
            training_results = app.trainer.state.metrics

            self.logger.info("Training completed successfully")
            return training_results
            
        except Exception as e:
            print(f"❌ ERROR in TrainingPipeline.run: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc(file=sys.stderr)
            sys.stderr.flush()
            raise
        finally:
            print("DEBUG: Executing TrainingPipeline.run finally block.", file=sys.stderr)
            sys.stderr.flush()
            app.is_training = False
            if app.monitor:
                app.monitor.stop_monitoring()