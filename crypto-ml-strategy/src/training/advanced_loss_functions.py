"""
高级损失函数 - 提升模型准确度和预测能力
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import Optional, Tuple
import logging



class FocalLoss(nn.Module):
    """
    真正的Focal Loss实现，用于解决类别不平衡问题。
    继承自torch.nn.Module，可在PyTorch模型中直接使用。
    """
    def __init__(self, alpha: Optional[torch.Tensor] = None, gamma: float = 2.0, reduction: str = 'mean'):
        """
        Args:
            alpha (torch.Tensor, optional): 控制正负样本权重的alpha参数。默认为None。
            gamma (float, optional): 调制因子，用于关注难分类样本。默认为2.0。
            reduction (str, optional): 指定损失的规约方式 ('none', 'mean', 'sum')。默认为'mean'。
        """
        super(FocalLoss, self).__init__()
        self.alpha = alpha
        self.gamma = gamma
        self.reduction = reduction

    def forward(self, inputs: torch.Tensor, targets: torch.Tensor) -> torch.Tensor:
        """
        前向传播。

        Args:
            inputs (torch.Tensor): 模型预测的logits，形状为(N, C)。
            targets (torch.Tensor): 真实标签，形状为(N,)。

        Returns:
            torch.Tensor: 计算出的Focal Loss。
        """
        # 计算标准的交叉熵损失，但不进行规约
        ce_loss = F.cross_entropy(inputs, targets, reduction='none')
        
        # pt是正确类别的预测概率
        pt = torch.exp(-ce_loss)
        
        # 计算Focal Loss
        focal_loss = ((1 - pt) ** self.gamma * ce_loss)

        # 如果提供了alpha，则应用类别权重
        if self.alpha is not None:
            # 确保alpha张量与设备和数据类型匹配
            if self.alpha.device != inputs.device:
                self.alpha = self.alpha.to(inputs.device)
            if self.alpha.type() != inputs.data.type():
                self.alpha = self.alpha.type_as(inputs.data)
            
            # 根据target获取对应的alpha值
            at = self.alpha.gather(0, targets.data.view(-1))
            focal_loss = at * focal_loss

        # 根据指定的reduction进行规约
        if self.reduction == 'mean':
            return focal_loss.mean()
        elif self.reduction == 'sum':
            return focal_loss.sum()
        else:
            return focal_loss


















def create_advanced_loss_function(
    loss_type: str = 'combined',
    num_classes: int = 3,
    class_weights: Optional[torch.Tensor] = None,
    **kwargs
) -> nn.Module:
    """
    创建高级损失函数
    
    Args:
        loss_type: 损失函数类型
        num_classes: 类别数量
        class_weights: 类别权重
        **kwargs: 其他参数
        
    Returns:
        损失函数实例
    """
    # 🔥 修复：只返回真实有效的、基于PyTorch的损失函数
    if loss_type == 'focal':
        # 确保gamma参数被正确传递
        gamma = kwargs.get('gamma', 2.0)
        return FocalLoss(alpha=class_weights, gamma=gamma)
    else:
        # 默认或未知的损失类型，返回标准的交叉熵损失
        logging.info(f"指定的损失类型 '{loss_type}' 不被支持或已弃用。使用标准的CrossEntropyLoss。")
        return nn.CrossEntropyLoss(weight=class_weights)


def calculate_class_weights(labels: torch.Tensor, num_classes: int) -> torch.Tensor:
    """
    计算类别权重
    
    Args:
        labels: 标签张量
        num_classes: 类别数量
        
    Returns:
        类别权重张量
    """
    class_counts = torch.bincount(labels, minlength=num_classes).float()
    total_samples = class_counts.sum()
    
    # 使用逆频率权重
    class_weights = total_samples / (num_classes * class_counts)
    
    # 归一化权重
    class_weights = class_weights / class_weights.sum() * num_classes
    
    return class_weights
