"""
Trainer Factory Module
Provides a factory function to create a configured UnifiedTrainer.
"""
from typing import Any, Dict, Optional

import torch
import torch.nn as nn

from src.api.async_deepseek_client import AsyncDeepSeekClient
from .unified_trainer import (
    CheckpointCallback,
    DistillationConfig,
    DistillationTrainingStep,
    EarlyStoppingCallback,
    LoggerCallback,
    MetricsCallback,
    PrometheusCallback,
    StandardTrainingStep,
    TrainerConfig,
    UnifiedTrainer,
    VisualizerCallback,
)


def create_trainer(
    model: nn.Module,
    train_config: Dict[str, Any],
    device: Optional[torch.device] = None,
    monitor: Optional[Any] = None,  # 添加monitor参数
) -> UnifiedTrainer:
    """
    Factory function to create and configure the UnifiedTrainer.

    Args:
        model: The model to be trained.
        train_config: A dictionary containing training configuration.
        device: The device to train on.

    Returns:
        An instance of the UnifiedTrainer.
    """
    # Create callbacks based on the configuration
    callbacks = [
        LoggerCallback(),
        MetricsCallback(),
        PrometheusCallback(),
    ]
    if train_config.get("early_stopping_patience"):
        callbacks.append(
            EarlyStoppingCallback(
                patience=train_config["early_stopping_patience"],
                monitor=train_config.get("early_stopping_monitor", "val_loss"),
                mode=train_config.get("early_stopping_mode", "min"),
            )
        )
    if train_config.get("visualization_enabled"):
        callbacks.append(VisualizerCallback())
        
    if train_config.get("checkpoint_path"):
        callbacks.append(
            CheckpointCallback(
                save_path=train_config["checkpoint_path"],
                monitor=train_config.get("checkpoint_monitor", "val_loss"),
                mode=train_config.get("checkpoint_mode", "min"),
            )
        )

    # Dynamically select the training step strategy
    training_step: TrainingStep
    distillation_config: Optional[DistillationConfig] = None
    if "distillation" in train_config and train_config["distillation"].get("enabled"):
        distillation_params = train_config["distillation"]
        # This assumes a mechanism to get the deepseek client
        # For now, we might need to pass it in or have a global way to access it.
        # Let's assume a placeholder or a way to initialize it.
        client = AsyncDeepSeekClient() # Placeholder
        
        distillation_config = DistillationConfig(
            deepseek_client=client,
            temperature=distillation_params.get("temperature", 2.5),
            alpha=distillation_params.get("alpha", 0.8),
            beta=distillation_params.get("beta", 0.2),
            teacher_call_threshold=distillation_params.get("teacher_call_threshold", 0.4),
            max_teacher_calls_per_epoch=distillation_params.get("max_teacher_calls_per_epoch", 500),
        )
        training_step = DistillationTrainingStep(config=distillation_config)
    else:
        training_step = StandardTrainingStep(criterion=nn.CrossEntropyLoss())

    # Create the main trainer configuration object
    trainer_config = TrainerConfig(
        epochs=train_config.get("epochs", 10),
        learning_rate=train_config.get("learning_rate", 1e-3),
        device=device,
        use_amp=train_config.get("use_amp", True),
        use_compile=False,  # 修复：暂时禁用以解决重新编译的性能问题
        callbacks=callbacks,
        training_step=training_step,
        distillation=distillation_config,
    )

    return UnifiedTrainer(model=model, config=trainer_config, device=device, monitor=monitor)
