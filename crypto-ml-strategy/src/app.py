#!/usr/bin/env python3
"""
主应用程序模块
包含核心的 MainApplication 类
"""

import asyncio
import argparse
import logging
import os
import signal
import shutil
import sys
import traceback
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional
import json
import subprocess

import numpy as np
import pandas as pd
import torch

# 兼容不同PyTorch版本的GradScaler导入
try:
    from torch.amp import GradScaler
except ImportError:
    try:
        from torch.cuda.amp import GradScaler
    except ImportError:
        # 如果都导入失败，创建一个简单的替代类
        class GradScaler:
            def __init__(self):
                pass
            def scale(self, loss):
                return loss
            def step(self, optimizer):
                optimizer.step()
            def update(self):
                pass

from src.core.application import CryptoMLApplication
from src.data.unified_pipeline.data_manager import DataManager
from src.models.unified_fusion_model import UnifiedSignalFusionModel
from src.training.unified_trainer import start_http_server
from src.utils.config import get_config_manager
from src.utils.logger import setup_logger
from src.utils.metrics import calculate_metrics


class AppOrchestrator:
    """应用编排器类"""

    def __init__(self, project_root: Optional[Path] = None):
        """初始化应用编排器"""
        # If project_root is not provided, it will be determined dynamically
        # This allows for flexibility and robustness, especially during testing.
        if isinstance(project_root, Path):
            self.project_root = project_root
        else:
            self.project_root = Path(__file__).parent.parent
        
        # 配置管理 (不再需要传递 project_root)
        self.config_manager = get_config_manager(config_dir=self.project_root / 'config')
        self.config = self.config_manager.to_dict()

        # 设置日志 (现在只输出到控制台)
        log_config = self.config.get('logging', {})
        log_level = 'DEBUG' if self.config.get('system', {}).get('debug_mode') else log_config.get('level', 'INFO')
        
        self.logger = setup_logger(
            name="AppOrchestrator",
            level=log_level,
            log_file=None,  # 强制日志到控制台
            json_format=True
        )
        self.logger.info("Logging configured to output to console only.")

        # GPU环境配置
        device_config = self.config.get('model', {}).get('device', 'auto')
        if device_config == 'auto':
            self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
            self.logger.info(f"Device set to 'auto', selected device: {self.device}")
        else:
            self.device = torch.device(device_config)

        # 混合精度训练
        self.use_mixed_precision = self.config.get('gpu', {}).get('mixed_precision', {}).get('enabled', False)
        self.scaler = GradScaler(enabled=self.use_mixed_precision) if self.device.type == 'cuda' else None
        if self.use_mixed_precision:
            self.logger.info("Mixed precision training enabled.")


        # 核心应用
        self.app: Optional[CryptoMLApplication] = None
        self.is_running = False
        self.selected_features: Optional[List[str]] = None
        self.prometheus_server = None

        # 注册信号处理
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

        self.logger.info(f"Main application initialized on {self.device}")
        if self.device.type == 'cuda':
            # GPU信息现在通过PerformanceOptimizer获取
            pass

    async def initialize(self):
        """初始化应用组件"""
        try:
            self.logger.info("Initializing application components...")

            # --- Load Selected Features ---
            features_path = Path(self.config_manager.get('storage.directories.models')) / "selected_features.json"
            if features_path.exists():
                self.logger.info(f"Loading selected features from {features_path}")
                with open(features_path, 'r') as f:
                    self.selected_features = json.load(f)
                self.logger.info(f"Loaded {len(self.selected_features)} feature names.")
            else:
                self.logger.warning("selected_features.json not found. Online mode may fail if feature mismatch occurs.")

            print("DEBUG: Instantiating CryptoMLApplication...")
            self.app = CryptoMLApplication(
                config=self.config,
                device=self.device,
                scaler=self.scaler,
                project_root=self.project_root  # 保持传递 project_root
            )
            print("DEBUG: CryptoMLApplication instantiated. Calling self.app.initialize()...")
            await self.app.initialize()  # Now awaits the async method
            self.logger.info("Application initialization completed")
        except Exception as e:
            self.logger.error(f"Failed to initialize application: {e}")
            self.logger.error(traceback.format_exc())
            raise

    def check_model_exists(self, model_path: Optional[str] = None) -> Dict[str, Any]:
        """检查模型文件是否存在"""
        models_dir_path = self.config_manager.get('storage.directories.models')
        if not models_dir_path:
            self.logger.warning("storage.directories.models not configured, using default 'data/models'")
            models_dir = self.project_root / 'data' / 'models'
            models_dir.mkdir(parents=True, exist_ok=True)
        else:
            models_dir = Path(models_dir_path)
        default_paths = [
            models_dir / "trained_model.pth",
            models_dir / "best_model.pth",
        ]
        
        check_paths = [Path(model_path)] if model_path else default_paths
        result = {'model_exists': False, 'model_path': None, 'available_models': [], 'recommended_action': 'train'}

        for path in check_paths:
            if path.exists():
                result['available_models'].append(str(path))
                if not result['model_exists']:
                    result.update({'model_exists': True, 'model_path': str(path), 'recommended_action': 'online_learning'})

        checkpoints_dir = models_dir / "checkpoints"
        if checkpoints_dir.exists():
            checkpoint_files = sorted(checkpoints_dir.glob("*.pth"), key=lambda p: p.stat().st_mtime, reverse=True)
            if checkpoint_files:
                result['available_models'].extend([str(f) for f in checkpoint_files])
                if not result['model_exists']:
                    result.update({'model_exists': True, 'model_path': str(checkpoint_files), 'recommended_action': 'online_learning'})
        
        self.logger.info(f"🔍 Model check result: Exists={result['model_exists']}, Recommended='{result['recommended_action']}'")
        return result

    async def run_training(self, **kwargs):
        """
        运行训练流程。
        此方法现在直接将所有参数委托给核心应用处理。
        """
        if not self.app: raise RuntimeError("Application not initialized")
        
        # Start Prometheus metrics server if not already running
        if self.prometheus_server is None:
            try:
                # The server runs in a daemon thread, so it won't block
                start_http_server(8000)
                self.prometheus_server = True # Mark as started
                self.logger.info("📈 Prometheus metrics server started on http://localhost:8000")
            except Exception as e:
                self.logger.error(f"Failed to start Prometheus server: {e}")

        self.logger.info("🚀 Delegating training process to CryptoMLApplication...")
        self.is_running = True
        try:
            # 直接将所有相关参数传递给核心应用的run_training
            # TrainingPipeline将负责处理数据准备
            await self.app.run_training(**kwargs)
            self.logger.info("✅ Training process orchestrated successfully.")
        except Exception as e:
            self.logger.error(f"❌ An error occurred during the training process orchestration: {e}", exc_info=True)
        finally:
            self.is_running = False

    async def run_online_learning_with_kafka(self, **kwargs):
        """运行Kafka集成的在线学习"""
        if not self.app: raise RuntimeError("Application not initialized")
        self.logger.info("🚀 Starting Kafka-integrated online learning...")
        from src.kafka.kafka_client import KafkaClient
        kafka_config = self.config.get('kafka') or {}
        kafka_client = KafkaClient(kafka_config)
        self.is_running = True
        try:
            # --- 动态特征维度同步 ---
            if not self.app.feature_dim_synced:
                self.logger.info("🚀 首次运行，同步实时特征维度...")
                sample_features = await self._get_latest_features_for_symbol(kwargs.get('symbol', 'BTCUSDT'))
                if sample_features is not None:
                    real_feature_dim = sample_features.shape[1]
                    self.app.update_model_feature_dim(real_feature_dim)
                    self.app.feature_dim_synced = True
                    self.logger.info(f"✅ 模型特征维度已同步为: {real_feature_dim}")
                else:
                    self.logger.error("❌ 无法获取样本特征以同步维度，信号生成可能失败。")
            # --- 同步完成 ---

            online_learning_task = asyncio.create_task(self.app.run_online_learning(**kwargs))
            signal_task = asyncio.create_task(self._run_signal_generation_service(kafka_client, **kwargs))
            await asyncio.gather(online_learning_task, signal_task)
        finally:
            self.is_running = False
            kafka_client.stop()
            self.logger.info("✅ Kafka online learning process finished.")

    async def run_evaluation(self, checkpoint: str, symbols: Optional[List[str]] = None):
        """运行模型评估流程。"""
        self.logger.info("🚀 Starting model evaluation process...")
        self.is_running = True
        try:
            # 1. 验证检查点
            checkpoint_path = Path(checkpoint)
            if not checkpoint_path.exists():
                self.logger.error(f"检查点文件未找到: {checkpoint}")
                return

            # 2. 加载模型
            model = self._load_evaluation_model(checkpoint_path)

            # 3. 创建数据加载器
            if not symbols:
                symbols = self.config.get('data', {}).get('default_eval_symbols', ['BTCUSDT'])
                self.logger.info(f"未提供交易对，使用默认值: {symbols}")
            
            dataloader_kwargs = {'batch_size': 1024, 'num_workers': 0, 'pin_memory': False}
            dataloader = await self._create_evaluation_dataloader(symbols, dataloader_kwargs)

            # 4. 运行评估循环
            metrics = await self._execute_evaluation_loop(model, dataloader)

            # 5. 打印指标
            self._print_evaluation_metrics(metrics)
            
            self.logger.info("✅ 评估流程成功完成。")

        except Exception as e:
            self.logger.error(f"❌ 评估过程中发生错误: {e}", exc_info=True)
        finally:
            self.is_running = False

    async def run_full_cycle_workflow(self, args: argparse.Namespace):
        """
        运行完整的端到端工作流。
        """
        self.logger.info("🚀 Starting the full end-to-end workflow...")
        self.is_running = True
        try:
            # 1. Pre-flight check: Verify Java service is running
            if not self._check_java_service_running():
                return  # Stop execution if the service is not running
            
            # 2. Data Cleanup
            self._clean_project_directories()

            # 3. Model Check and Conditional Training
            self.logger.info("🕵️ Checking for existing model...")
            model_check = self.check_model_exists()
            if not model_check['model_exists']:
                self.logger.info("❌ No existing model found. Starting training process...")
                self.logger.info(f"Training with symbols: {args.fc_symbols}")
                self.logger.info(f"Training with timeframes: {args.fc_timeframes}")
                await self.run_training(
                    symbols=args.fc_symbols,
                    force_refresh=args.force_refresh,
                    days=args.days,
                    epochs=args.epochs,
                    timeframes=args.fc_timeframes,
                    trainer=args.trainer
                )
                self.logger.info("✅ Training finished.")
            else:
                self.logger.info(f"✅ Existing model found at: {model_check['model_path']}")

            # 4. Start Real-time Prediction and Online Learning
            self.logger.info("🚀 Launching real-time prediction and online learning service...")
            await self.run_online_learning_with_kafka(
                symbol=args.symbol,
                signal_interval=args.signal_interval
            )

            self.logger.info("✅ Full-cycle workflow completed successfully.")
        except Exception as e:
            self.logger.error(f"❌ An error occurred during the full-cycle workflow: {e}", exc_info=True)
        finally:
            self.is_running = False

    def _load_evaluation_model(self, checkpoint_path: Path) -> "UnifiedSignalFusionModel":
        """为评估加载模型。"""
        self.logger.info(f"正在从 {checkpoint_path} 加载模型...")
        model_config = self.config.get('model', {})
        if not model_config:
            raise ValueError("在配置中找不到 'model' 部分。")
            
        model = UnifiedSignalFusionModel(**model_config)
        
        state_dict = torch.load(checkpoint_path, map_location=self.device)
        if next(iter(state_dict)).startswith('_orig_mod.'):
            state_dict = {k.replace('_orig_mod.', ''): v for k, v in state_dict.items()}
            
        model.load_state_dict(state_dict)
        model.to(self.device)
        model.eval()
        self.logger.info("模型加载成功以供评估。")
        return model

    async def _create_evaluation_dataloader(self, symbols: list, dataloader_kwargs: dict):
        """为评估创建数据加载器。"""
        self.logger.info("正在创建评估数据加载器...")
        data_manager = DataManager(config=self.config.get('data',{}), project_root=self.project_root)
        _, _, test_loader = await data_manager.run_full_pipeline(
            symbols=symbols,
            dataloader_kwargs=dataloader_kwargs
        )
        if test_loader is None:
            raise RuntimeError("创建测试数据加载器失败。")
        self.logger.info("评估数据加载器创建成功。")
        return test_loader

    async def _execute_evaluation_loop(self, model: "UnifiedSignalFusionModel", dataloader: torch.utils.data.DataLoader) -> dict:
        """执行评估循环并返回指标。"""
        self.logger.info("正在执行评估循环...")
        all_preds, all_labels, all_probs = [], [], []

        with torch.no_grad():
            for i, batch in enumerate(dataloader):
                features = batch['features'].to(self.device)
                labels = batch['labels'].to(self.device)

                outputs = model(features)
                logits = outputs.get('signal', outputs) if isinstance(outputs, dict) else outputs
                
                probabilities = torch.softmax(logits, dim=1)
                predictions = torch.argmax(probabilities, dim=1)
                
                all_preds.append(predictions.cpu().numpy())
                all_labels.append(labels.cpu().numpy())
                all_probs.append(probabilities.cpu().numpy())
                
                if (i + 1) % 50 == 0:
                    self.logger.info(f"已评估 {i + 1}/{len(dataloader)} 个批次...")

        y_true = np.concatenate(all_labels)
        y_pred = np.concatenate(all_preds)
        y_prob = np.concatenate(all_probs)
        
        self.logger.info("评估循环完成，正在计算指标...")
        metrics = calculate_metrics(y_true=y_true, y_pred=y_pred, y_prob=y_prob, task_type='classification')
        return metrics

    def _print_evaluation_metrics(self, metrics: dict):
        """格式化并记录评估指标。"""
        self.logger.info("\\n" + "="*50)
        self.logger.info("           模型评估结果")
        self.logger.info("="*50)
        self.logger.info(f"➡️ 准确率 (Accuracy): {metrics.get('accuracy', 0):.4f}")
        self.logger.info(f"➡️ F1 分数 (Weighted): {metrics.get('f1_score', 0):.4f}")
        self.logger.info(f"➡️ AUC (Weighted):   {metrics.get('auc', 0):.4f}")
        self.logger.info(f"➡️ 精确率 (Precision): {metrics.get('precision', 0):.4f}")
        self.logger.info(f"➡️ 召回率 (Recall):   {metrics.get('recall', 0):.4f}")
        
        if 'confusion_matrix' in metrics:
            self.logger.info("\n混淆矩阵:")
            cm = np.array(metrics['confusion_matrix'])
            cm_df = pd.DataFrame(cm, index=[f'真实类别 {i}' for i in range(cm.shape[0])], 
                                     columns=[f'预测类别 {i}' for i in range(cm.shape[1])])
            self.logger.info(f"\n{cm_df.to_string()}")
            
        self.logger.info("="*50)

    def _check_java_service_running(self) -> bool:
        """检查核心Java服务是否正在运行。"""
        service_name = "crypto-market-data"
        try:
            # The [c] is a trick to prevent grep from matching its own process
            command = f"ps aux | grep '[c]rypto-market-data'"
            result = subprocess.run(command, shell=True, capture_output=True, text=True)
            if result.returncode == 0 and service_name in result.stdout:
                self.logger.info(f"✅ Pre-flight check PASSED: Java service '{service_name}' is running.")
                return True
            else:
                self.logger.error(f"❌ Pre-flight check FAILED: Java service '{service_name}' is NOT running.")
                self.logger.error("Please start the Java market data service before running the full-cycle workflow.")
                return False
        except Exception as e:
            self.logger.error(f"Error checking Java service status: {e}")
            return False

    def _clean_project_directories(self):
        """清理项目数据和日志目录。"""
        self.logger.info("🧹 Starting directory cleanup...")
        data_dir = self.project_root / 'data'
        logs_dir = self.project_root / 'logs'
        
        for dir_path in [data_dir, logs_dir]:
            try:
                if dir_path.exists():
                    self.logger.warning(f"Removing all contents from: {dir_path}")
                    # Remove the directory and all its contents
                    shutil.rmtree(dir_path)
                    # Recreate the directory so it's clean and available
                    dir_path.mkdir(parents=True, exist_ok=True)
                    self.logger.info(f"✅ Successfully cleaned and recreated directory: {dir_path}")
                else:
                    # If it doesn't exist, create it
                    dir_path.mkdir(parents=True, exist_ok=True)
                    self.logger.info(f"Directory did not exist, created: {dir_path}")
            except Exception as e:
                self.logger.error(f"❌ Failed to clean directory {dir_path}: {e}", exc_info=True)
                # Depending on the desired robustness, you might want to raise here
                raise

    async def _run_signal_generation_service(self, kafka_client: Any, **kwargs):
        """运行信号生成和推送服务"""
        symbol = kwargs.get('symbol', 'BTCUSDT')
        signal_interval = kwargs.get('signal_interval', 1)
        self.logger.info(f"📊 Starting signal generation for {symbol} every {signal_interval}s")
        while self.is_running:
            try:
                signal_data = await self._generate_trading_signal(symbol)
                if signal_data:
                    success = await kafka_client.send_signal(signal_data, symbol)
                    if success:
                        self.logger.info(f"📡 Signal sent for {symbol}: {signal_data.get('action', 'N/A')}")
            except Exception as e:
                self.logger.error(f"❌ Signal generation service error: {e}")
            await asyncio.sleep(signal_interval)

    async def _get_latest_features_for_symbol(self, symbol: str) -> Optional['np.ndarray']:
        """
        使用优化的实时数据获取器生成最新特征
        优先使用InfluxDB，支持智能回退机制
        """
        if not self.app:
            self.logger.warning("Application not initialized, cannot get features.")
            return None

        try:
            # 🚀 使用新的实时数据获取器
            from src.online_learning.realtime_data_fetcher import RealtimeDataFetcher

            # 初始化实时数据获取器（单例模式）
            if not hasattr(self, '_realtime_fetcher'):
                self._realtime_fetcher = RealtimeDataFetcher(self.app.config.get('data', {}))
                await self._realtime_fetcher.initialize()
                self.logger.info("✅ 实时数据获取器初始化完成")

            # 获取配置的时间框架
            timeframes = self.config.get('online', {}).get('timeframes', ['1h'])
            if isinstance(timeframes, str):
                timeframes = [timeframes]

            # 生成实时特征
            features_df = await self._realtime_fetcher.generate_realtime_features(
                symbol=symbol,
                timeframes=timeframes
            )

            if features_df is None or features_df.empty:
                self.logger.warning(f"❌ 无法为{symbol}生成实时特征，尝试回退方法")
                return await self._get_latest_features_fallback(symbol)

            # --- BEGIN FEATURE FILTERING ---
            if self.selected_features:
                self.logger.debug(f"🔍 匹配选定特征: {len(self.selected_features)}个")
                # 确保所有选定特征都存在，缺失的用0填充
                features_df = features_df.reindex(columns=self.selected_features, fill_value=0)
            else:
                self.logger.warning("⚠️ 未加载选定特征，使用所有生成的特征")
            # --- END FEATURE FILTERING ---

            # 获取最新时间戳的特征
            numeric_features_df = features_df.select_dtypes(include=np.number)
            latest_features_np = numeric_features_df.iloc[-1:].values

            # 转换为张量
            latest_features_tensor = torch.from_numpy(latest_features_np).float()

            self.logger.debug(f"✅ 成功生成{symbol}实时特征，形状: {latest_features_tensor.shape}")
            return latest_features_tensor

        except Exception as e:
            self.logger.error(f"❌ 实时特征生成失败 {symbol}: {e}", exc_info=True)

            # 🔄 回退到原始方法（如果新方法失败）
            return await self._get_latest_features_fallback(symbol)

    async def _get_latest_features_fallback(self, symbol: str) -> Optional['np.ndarray']:
        """回退的特征生成方法（使用文件数据）"""
        try:
            self.logger.warning(f"🔄 使用回退方法为{symbol}生成特征")

            from src.data.unified_pipeline.data_manager import DataManager
            data_manager = DataManager(config=self.app.config.get('data', {}), project_root=self.project_root)
            feature_engine = self.app.feature_engine

            timeframe = self.config.get('online', {}).get('timeframe', '1h')
            raw_file_path = data_manager.raw_dir / f"{symbol}_{timeframe}_kline.csv"

            if not raw_file_path.exists():
                self.logger.warning(f"❌ 找不到原始数据文件: {raw_file_path}")
                return None

            # 加载数据
            df = pd.read_csv(raw_file_path, parse_dates=['timestamp']).tail(500)

            # 生成特征
            features_df = feature_engine.create_all_features(df)
            features_df = feature_engine.normalize_features(features_df)

            # 特征过滤
            if self.selected_features:
                features_df = features_df.reindex(columns=self.selected_features, fill_value=0)

            # 获取最新特征
            numeric_features_df = features_df.select_dtypes(include=np.number)
            latest_features_np = numeric_features_df.iloc[-1:].values
            latest_features_tensor = torch.from_numpy(latest_features_np).float()

            self.logger.info(f"✅ 回退方法成功生成特征: {latest_features_tensor.shape}")
            return latest_features_tensor

        except Exception as e:
            self.logger.error(f"❌ 回退特征生成也失败: {e}")
            return None

    async def _generate_trading_signal(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Generates a trading signal using the optimized high-performance inference path."""
        if not self.app or not self.app.model:
            self.logger.warning("Application or model not ready for signal generation.")
            return None
        
        try:
            # 1. Get latest features for prediction
            latest_features = await self._get_latest_features_for_symbol(symbol)
            if latest_features is None:
                self.logger.warning(f"Could not generate features for {symbol}, skipping signal generation.")
                return None

            # 2. Get raw model prediction using the new high-performance method
            raw_prediction = await self.app.run_prediction(features=latest_features)

            if not raw_prediction or 'predictions' not in raw_prediction or not raw_prediction['predictions']:
                self.logger.info(f"No raw prediction generated for {symbol}.")
                return None
            
            pred_probs = raw_prediction['probabilities'][0]
            action_index = int(raw_prediction['predictions'][0])
            action_map = {0: 'SELL', 1: 'HOLD', 2: 'BUY'}
            action = action_map.get(action_index, 'UNKNOWN')
            confidence = float(pred_probs[action_index])

            # 3. Create signal dictionary
            signal_data = {
                'action': action,
                'confidence': confidence,
                'raw_prediction': pred_probs if isinstance(pred_probs, list) else pred_probs.tolist() # Convert numpy array to list for JSON serialization
            }

            signal = {
                'symbol': symbol,
                'signal_data': signal_data,
                'timestamp': datetime.now().isoformat()
            }

            # 4. Filter signal through the unified risk framework
            # The risk_manager is part of the app now
            trade_decision = await self.app.risk_manager.process_trade_signal(signal)
            if not trade_decision.is_approved:
                self.logger.info(f"Signal for {symbol} was rejected by UnifiedRiskFramework: {trade_decision.reason}")
                return None

            # 5. Assemble the final signal using the decision from the framework
            final_signal = {
                **signal['signal_data'],
                'symbol': symbol,
                'position_size': trade_decision.position_size,
                'entry_price': (await self.app._get_latest_market_data(symbol)).get('close', 0),
                'stop_loss': trade_decision.stop_loss_price,
                'take_profit': trade_decision.take_profit_price,
                'timestamp': signal['timestamp'],
                'risk_assessment': {
                    'reason': trade_decision.reason,
                    'alerts': [alert.__dict__ for alert in trade_decision.alerts]
                },
                'source': 'UnifiedSignalFusionModel'
            }
            
            self.logger.info(f"Generated final signal for {symbol}: {final_signal}")
            return final_signal

        except Exception as e:
            self.logger.error(f"❌ Failed to generate trading signal: {e}", exc_info=True)
            return None

    async def cleanup(self):
        """清理资源"""
        self.logger.info("Cleaning up resources...")
        if self.prometheus_server:
            self.logger.info("Prometheus server will shut down with the application.")
        if self.app:
            await self.app.cleanup()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        import gc
        gc.collect()
        self.logger.info("Cleanup completed.")

    def _signal_handler(self, signum, frame):
        """信号处理器 - 优雅退出"""
        self.logger.warning(f"🛑 Signal {signum} received, initiating graceful shutdown...")
        self.is_running = False
        # Give async tasks a moment to notice the flag
        asyncio.create_task(self.cleanup())
        # A second signal should force exit
        signal.signal(signum, lambda s, f: os._exit(1))