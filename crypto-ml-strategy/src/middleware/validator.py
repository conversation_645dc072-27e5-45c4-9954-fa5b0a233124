import logging
import pandas as pd
from prometheus_client import Counter, Gauge

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DataValidationMiddleware:
    """
    A middleware for validating, cleaning, and monitoring data batches.

    This class is designed to be part of a data processing pipeline. It ensures
    that the incoming data (in a pandas DataFrame) adheres to expected formats,
    contains necessary columns, and is clean of common issues like missing values.
    It also uses Prometheus to expose metrics about data quality.
    """

    def __init__(self, config: dict):
        """
        Initializes the DataValidationMiddleware.

        Args:
            config (dict): A configuration dictionary. Expected keys:
                           'required_columns', 'dlq_path'.
        """
        self.config = config
        self.required_columns = self.config.get('required_columns', ['open', 'high', 'low', 'close', 'volume'])
        self.dlq_path = self.config.get('dlq_path', '/tmp/dlq.log')

        # Prometheus metrics
        self.processed_rows_counter = Counter(
            'data_validator_processed_rows_total',
            'Total number of rows processed'
        )
        self.invalid_rows_counter = Counter(
            'data_validator_invalid_rows_total',
            'Total number of rows deemed invalid'
        )
        self.missing_values_gauge = Gauge(
            'data_validator_missing_values',
            'Number of missing values detected in the last batch'
        )
        logger.info("DataValidationMiddleware initialized.")
        logger.info(f"Required columns: {self.required_columns}")
        logger.info(f"Dead Letter Queue (DLQ) path: {self.dlq_path}")

    def process(self, data_batch: pd.DataFrame) -> pd.DataFrame:
        """
        Processes a batch of data, performing validation and cleaning.

        Args:
            data_batch (pd.DataFrame): The input data batch.

        Returns:
            pd.DataFrame: A cleaned and validated data batch. Returns an empty
                          DataFrame if the input is None or empty.
        """
        if data_batch is None or data_batch.empty:
            logger.warning("Received an empty or None data batch. Skipping.")
            return pd.DataFrame()

        self.processed_rows_counter.inc(len(data_batch))
        
        is_valid, invalid_rows = self._validate(data_batch.copy())

        if not is_valid.all():
            # Separate valid and invalid rows
            valid_batch = data_batch[is_valid].copy()
            invalid_batch = data_batch[~is_valid].copy()
            
            logger.warning(f"Found {len(invalid_batch)} invalid rows. Writing to DLQ.")
            self.invalid_rows_counter.inc(len(invalid_batch))
            self._write_to_dlq(invalid_batch)
        else:
            valid_batch = data_batch.copy()

        if valid_batch.empty:
            logger.warning("No valid data remaining after validation. Returning empty DataFrame.")
            return pd.DataFrame()

        cleaned_batch = self._clean(valid_batch)

        return cleaned_batch

    def _validate(self, df: pd.DataFrame) -> tuple[pd.Series, pd.DataFrame]:
        """
        Validates the DataFrame against a set of rules.

        - Checks for the presence of required columns.
        - Checks that numeric columns are indeed numeric.

        Args:
            df (pd.DataFrame): The DataFrame to validate.

        Returns:
            tuple[pd.Series, pd.DataFrame]: A tuple containing a boolean Series 
                                             indicating valid rows, and a DataFrame
                                             of the invalid rows themselves.
        """
        # Check for required columns
        missing_cols = [col for col in self.required_columns if col not in df.columns]
        if missing_cols:
            logger.error(f"Missing required columns: {missing_cols}. Marking all rows as invalid.")
            # If essential columns are missing, all rows are invalid
            return pd.Series([False] * len(df)), df

        # Ensure numeric columns are numeric, coercing errors
        for col in self.required_columns:
            df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # A row is valid if it has no NaNs in the required columns after coercion
        valid_mask = df[self.required_columns].notna().all(axis=1)
        invalid_rows = df[~valid_mask]
        
        return valid_mask, invalid_rows

    def _clean(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Cleans the DataFrame by handling missing values.

        Args:
            df (pd.DataFrame): The DataFrame to clean.

        Returns:
            pd.DataFrame: The cleaned DataFrame.
        """
        missing_before = df.isnull().sum().sum()
        self.missing_values_gauge.set(missing_before)
        
        if missing_before > 0:
            logger.info(f"Found {missing_before} missing values. Applying forward fill.")
            # Using forward fill as a common strategy for time-series data
            df.ffill(inplace=True)
            # Drop any remaining NaNs (e.g., at the beginning of the frame)
            df.dropna(inplace=True)

        return df

    def _write_to_dlq(self, invalid_data: pd.DataFrame):
        """
        Writes invalid data to a Dead Letter Queue (DLQ) file.

        This is a simple implementation that appends to a log file.
        In a production system, this could write to a message queue,
        a database, or a cloud storage bucket.

        Args:
            invalid_data (pd.DataFrame): The DataFrame containing invalid rows.
        """
        try:
            with open(self.dlq_path, 'a') as f:
                invalid_data.to_csv(f, header=f.tell()==0, index=False, lineterminator='\\n')
            logger.info(f"Successfully wrote {len(invalid_data)} rows to DLQ at {self.dlq_path}")
        except IOError as e:
            logger.error(f"Failed to write to DLQ at {self.dlq_path}: {e}")

