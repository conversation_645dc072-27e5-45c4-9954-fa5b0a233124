"""
训练过程可视化监控
实现实时训练监控、性能指标可视化、模型状态展示
"""

import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Any, Tuple
import logging
from dataclasses import dataclass
import time
from collections import deque
import threading
import json
from pathlib import Path

import streamlit as st
import seaborn as sns
from matplotlib.animation import FuncAnimation


@dataclass
class TrainingMetrics:
    """训练指标数据类"""
    epoch: int
    step: int
    timestamp: float
    train_loss: float
    val_loss: Optional[float] = None
    train_accuracy: Optional[float] = None
    val_accuracy: Optional[float] = None
    learning_rate: Optional[float] = None
    gpu_memory: Optional[float] = None
    batch_time: Optional[float] = None
    additional_metrics: Optional[Dict[str, float]] = None


class TrainingMonitor:
    """
    训练监控器
    
    实时监控训练过程，提供可视化界面和性能分析
    """
    
    def __init__(
        self,
        save_dir: Optional[str] = "logs/training_monitor",
        max_history: int = 10000,
        update_interval: int = 50,  # 每50个batch更新一次，提高性能
        enable_live_plot: bool = True
    ):
        # 🔧 修复：如果save_dir为None，则使用默认路径以增强健壮性
        final_save_dir = Path(save_dir) if save_dir is not None else Path("logs/training_monitor")
        self.save_dir = final_save_dir
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        self.max_history = max_history
        self.update_interval = update_interval
        self.enable_live_plot = enable_live_plot
        
        # 训练指标历史
        self.metrics_history = deque(maxlen=max_history)
        self.current_epoch = 0
        self.current_step = 0
        
        # 实时监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        
        # 图表配置
        self.figure_size = (12, 8)
        self.colors = {
            'train_loss': '#1f77b4',
            'val_loss': '#ff7f0e',
            'train_acc': '#2ca02c',
            'val_acc': '#d62728',
            'lr': '#9467bd',
            'gpu_memory': '#8c564b'
        }
        
        self.logger = logging.getLogger(__name__)
        self.logger.info(f"Training monitor initialized, save_dir: {self.save_dir}")
    
    def log_metrics(
        self,
        epoch: int,
        step: int,
        train_loss: float,
        val_loss: Optional[float] = None,
        train_accuracy: Optional[float] = None,
        val_accuracy: Optional[float] = None,
        learning_rate: Optional[float] = None,
        gpu_memory: Optional[float] = None,
        batch_time: Optional[float] = None,
        **kwargs
    ):
        """
        记录训练指标
        
        Args:
            epoch: 当前epoch
            step: 当前step
            train_loss: 训练损失
            val_loss: 验证损失
            train_accuracy: 训练准确率
            val_accuracy: 验证准确率
            learning_rate: 学习率
            gpu_memory: GPU内存使用
            batch_time: 批次处理时间
            **kwargs: 其他指标
        """
        metrics = TrainingMetrics(
            epoch=epoch,
            step=step,
            timestamp=time.time(),
            train_loss=train_loss,
            val_loss=val_loss,
            train_accuracy=train_accuracy,
            val_accuracy=val_accuracy,
            learning_rate=learning_rate,
            gpu_memory=gpu_memory,
            batch_time=batch_time,
            additional_metrics=kwargs
        )
        
        self.metrics_history.append(metrics)
        self.current_epoch = epoch
        self.current_step = step
        
        # 保存到文件
        self._save_metrics(metrics)
        
        # 实时更新图表
        if self.enable_live_plot and step % self.update_interval == 0:
            self._update_live_plots()
    
    def _save_metrics(self, metrics: TrainingMetrics):
        """保存指标到文件"""
        metrics_file = self.save_dir / "training_metrics.jsonl"
        
        metrics_dict = {
            'epoch': metrics.epoch,
            'step': metrics.step,
            'timestamp': metrics.timestamp,
            'train_loss': metrics.train_loss,
            'val_loss': metrics.val_loss,
            'train_accuracy': metrics.train_accuracy,
            'val_accuracy': metrics.val_accuracy,
            'learning_rate': metrics.learning_rate,
            'gpu_memory': metrics.gpu_memory,
            'batch_time': metrics.batch_time
        }
        
        if metrics.additional_metrics:
            metrics_dict.update(metrics.additional_metrics)
        
        with open(metrics_file, 'a') as f:
            f.write(json.dumps(metrics_dict) + '\n')
    
    def create_training_dashboard(self) -> go.Figure:
        """创建训练仪表板"""
        if not self.metrics_history:
            return go.Figure()
        
        # 转换为DataFrame
        df = self._metrics_to_dataframe()
        
        # 创建子图
        fig = make_subplots(
            rows=3, cols=2,
            subplot_titles=[
                'Training & Validation Loss',
                'Training & Validation Accuracy',
                'Learning Rate',
                'GPU Memory Usage',
                'Batch Processing Time',
                'Loss Distribution'
            ],
            specs=[
                [{"secondary_y": False}, {"secondary_y": False}],
                [{"secondary_y": False}, {"secondary_y": False}],
                [{"secondary_y": False}, {"type": "histogram"}]
            ]
        )
        
        # 1. 损失曲线
        if 'train_loss' in df.columns:
            fig.add_trace(
                go.Scatter(
                    x=df['step'],
                    y=df['train_loss'],
                    mode='lines',
                    name='Train Loss',
                    line=dict(color=self.colors['train_loss'])
                ),
                row=1, col=1
            )
        
        if 'val_loss' in df.columns and df['val_loss'].notna().any():
            fig.add_trace(
                go.Scatter(
                    x=df['step'],
                    y=df['val_loss'],
                    mode='lines',
                    name='Val Loss',
                    line=dict(color=self.colors['val_loss'])
                ),
                row=1, col=1
            )
        
        # 2. 准确率曲线
        if 'train_accuracy' in df.columns and df['train_accuracy'].notna().any():
            fig.add_trace(
                go.Scatter(
                    x=df['step'],
                    y=df['train_accuracy'],
                    mode='lines',
                    name='Train Accuracy',
                    line=dict(color=self.colors['train_acc'])
                ),
                row=1, col=2
            )
        
        if 'val_accuracy' in df.columns and df['val_accuracy'].notna().any():
            fig.add_trace(
                go.Scatter(
                    x=df['step'],
                    y=df['val_accuracy'],
                    mode='lines',
                    name='Val Accuracy',
                    line=dict(color=self.colors['val_acc'])
                ),
                row=1, col=2
            )
        
        # 3. 学习率
        if 'learning_rate' in df.columns and df['learning_rate'].notna().any():
            fig.add_trace(
                go.Scatter(
                    x=df['step'],
                    y=df['learning_rate'],
                    mode='lines',
                    name='Learning Rate',
                    line=dict(color=self.colors['lr'])
                ),
                row=2, col=1
            )
        
        # 4. GPU内存使用
        if 'gpu_memory' in df.columns and df['gpu_memory'].notna().any():
            fig.add_trace(
                go.Scatter(
                    x=df['step'],
                    y=df['gpu_memory'],
                    mode='lines',
                    name='GPU Memory (GB)',
                    line=dict(color=self.colors['gpu_memory'])
                ),
                row=2, col=2
            )
        
        # 5. 批次处理时间
        if 'batch_time' in df.columns and df['batch_time'].notna().any():
            fig.add_trace(
                go.Scatter(
                    x=df['step'],
                    y=df['batch_time'],
                    mode='lines',
                    name='Batch Time (s)',
                    line=dict(color='#17becf')
                ),
                row=3, col=1
            )
        
        # 6. 损失分布直方图
        if 'train_loss' in df.columns:
            fig.add_trace(
                go.Histogram(
                    x=df['train_loss'],
                    name='Loss Distribution',
                    nbinsx=30,
                    marker_color=self.colors['train_loss'],
                    opacity=0.7
                ),
                row=3, col=2
            )
        
        # 更新布局
        fig.update_layout(
            height=900,
            title_text="Training Dashboard",
            showlegend=True,
            template="plotly_white"
        )
        
        return fig
    
    def create_performance_analysis(self) -> Dict[str, go.Figure]:
        """创建性能分析图表"""
        if not self.metrics_history:
            return {}
        
        df = self._metrics_to_dataframe()
        figures = {}
        
        # 1. 损失收敛分析
        if 'train_loss' in df.columns:
            fig_convergence = go.Figure()
            
            # 原始损失
            fig_convergence.add_trace(
                go.Scatter(
                    x=df['step'],
                    y=df['train_loss'],
                    mode='lines',
                    name='Train Loss',
                    line=dict(color=self.colors['train_loss'], width=1)
                )
            )
            
            # 移动平均
            window_size = min(50, len(df) // 10)
            if window_size > 1:
                smoothed_loss = df['train_loss'].rolling(window=window_size).mean()
                fig_convergence.add_trace(
                    go.Scatter(
                        x=df['step'],
                        y=smoothed_loss,
                        mode='lines',
                        name=f'Smoothed (window={window_size})',
                        line=dict(color='red', width=2)
                    )
                )
            
            fig_convergence.update_layout(
                title="Loss Convergence Analysis",
                xaxis_title="Step",
                yaxis_title="Loss",
                template="plotly_white"
            )
            
            figures['convergence'] = fig_convergence
        
        # 2. 学习率调度分析
        if 'learning_rate' in df.columns and df['learning_rate'].notna().any():
            fig_lr = go.Figure()
            
            fig_lr.add_trace(
                go.Scatter(
                    x=df['step'],
                    y=df['learning_rate'],
                    mode='lines+markers',
                    name='Learning Rate',
                    line=dict(color=self.colors['lr'])
                )
            )
            
            fig_lr.update_layout(
                title="Learning Rate Schedule",
                xaxis_title="Step",
                yaxis_title="Learning Rate",
                yaxis_type="log",
                template="plotly_white"
            )
            
            figures['learning_rate'] = fig_lr
        
        # 3. 训练效率分析
        if 'batch_time' in df.columns and df['batch_time'].notna().any():
            fig_efficiency = make_subplots(
                rows=2, cols=1,
                subplot_titles=['Batch Processing Time', 'Throughput (samples/sec)']
            )
            
            # 批次时间
            fig_efficiency.add_trace(
                go.Scatter(
                    x=df['step'],
                    y=df['batch_time'],
                    mode='lines',
                    name='Batch Time',
                    line=dict(color='blue')
                ),
                row=1, col=1
            )
            
            # 吞吐量 (假设批次大小为32)
            batch_size = 32  # 可以从配置中获取
            throughput = batch_size / df['batch_time']
            fig_efficiency.add_trace(
                go.Scatter(
                    x=df['step'],
                    y=throughput,
                    mode='lines',
                    name='Throughput',
                    line=dict(color='green')
                ),
                row=2, col=1
            )
            
            fig_efficiency.update_layout(
                title="Training Efficiency Analysis",
                template="plotly_white"
            )
            
            figures['efficiency'] = fig_efficiency
        
        return figures
    
    def _metrics_to_dataframe(self) -> pd.DataFrame:
        """将指标历史转换为DataFrame"""
        data = []
        for metrics in self.metrics_history:
            row = {
                'epoch': metrics.epoch,
                'step': metrics.step,
                'timestamp': metrics.timestamp,
                'train_loss': metrics.train_loss,
                'val_loss': metrics.val_loss,
                'train_accuracy': metrics.train_accuracy,
                'val_accuracy': metrics.val_accuracy,
                'learning_rate': metrics.learning_rate,
                'gpu_memory': metrics.gpu_memory,
                'batch_time': metrics.batch_time
            }
            
            if metrics.additional_metrics:
                row.update(metrics.additional_metrics)
            
            data.append(row)
        
        return pd.DataFrame(data)
    
    def _update_live_plots(self):
        """更新实时图表"""
        if not self.enable_live_plot:
            return

        try:
            # 保存当前图表到logs目录
            fig = self.create_training_dashboard()
            fig.write_html(self.save_dir / "live_dashboard.html")

            # 同时保存到plots目录
            plots_dir = Path("plots")
            plots_dir.mkdir(exist_ok=True)
            fig.write_html(plots_dir / "training_dashboard.html")

            # 保存为静态图片
            try:
                fig.write_image(plots_dir / "training_dashboard.png", width=1200, height=800)
                self.logger.info(f"✅ 训练仪表板PNG已保存: {plots_dir / 'training_dashboard.png'}")
            except Exception as img_e:
                self.logger.warning(f"Failed to save PNG image: {img_e}")
                # 尝试安装kaleido
                try:
                    import subprocess
                    subprocess.run(["pip", "install", "kaleido"], check=True, capture_output=True)
                    self.logger.info("✅ Kaleido已安装，重试PNG保存...")
                    fig.write_image(plots_dir / "training_dashboard.png", width=1200, height=800)
                    self.logger.info(f"✅ 训练仪表板PNG已保存: {plots_dir / 'training_dashboard.png'}")
                except Exception as retry_e:
                    self.logger.error(f"❌ PNG保存失败，即使安装kaleido后: {retry_e}")

            # 保存性能分析图表
            analysis_figures = self.create_performance_analysis()
            for name, fig in analysis_figures.items():
                fig.write_html(self.save_dir / f"analysis_{name}.html")
                fig.write_html(plots_dir / f"analysis_{name}.html")

                # 保存为静态图片
                try:
                    fig.write_image(plots_dir / f"analysis_{name}.png", width=1200, height=600)
                    self.logger.info(f"✅ 分析图表PNG已保存: {plots_dir / f'analysis_{name}.png'}")
                except Exception as img_e:
                    self.logger.warning(f"Failed to save {name} PNG image: {img_e}")

        except Exception as e:
            self.logger.error(f"Failed to update live plots: {e}")
    
    def start_monitoring(self):
        """开始实时监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitoring_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        self.logger.info("Training monitoring started")
    
    def stop_monitoring(self):
        """停止实时监控"""
        self.is_monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join()
        
        self.logger.info("Training monitoring stopped")
    
    def _monitoring_loop(self):
        """监控循环"""
        while self.is_monitoring:
            try:
                self._update_live_plots()
                time.sleep(self.update_interval)
            except Exception as e:
                self.logger.error(f"Monitoring loop error: {e}")
                time.sleep(1)
    
    def export_metrics(self, format: str = "csv") -> str:
        """导出训练指标"""
        df = self._metrics_to_dataframe()
        
        if format == "csv":
            file_path = self.save_dir / "training_metrics.csv"
            df.to_csv(file_path, index=False)
        elif format == "json":
            file_path = self.save_dir / "training_metrics.json"
            df.to_json(file_path, orient='records', indent=2)
        elif format == "excel":
            file_path = self.save_dir / "training_metrics.xlsx"
            df.to_excel(file_path, index=False)
        else:
            raise ValueError(f"Unsupported format: {format}")
        
        self.logger.info(f"Metrics exported to {file_path}")
        return str(file_path)
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取训练摘要统计"""
        if not self.metrics_history:
            return {}
        
        df = self._metrics_to_dataframe()
        
        stats = {
            'total_epochs': self.current_epoch,
            'total_steps': self.current_step,
            'training_duration': df['timestamp'].max() - df['timestamp'].min(),
            'final_train_loss': df['train_loss'].iloc[-1] if 'train_loss' in df.columns else None,
            'best_train_loss': df['train_loss'].min() if 'train_loss' in df.columns else None,
            'final_val_loss': df['val_loss'].iloc[-1] if 'val_loss' in df.columns and df['val_loss'].notna().any() else None,
            'best_val_loss': df['val_loss'].min() if 'val_loss' in df.columns and df['val_loss'].notna().any() else None,
            'final_train_acc': df['train_accuracy'].iloc[-1] if 'train_accuracy' in df.columns and df['train_accuracy'].notna().any() else None,
            'best_train_acc': df['train_accuracy'].max() if 'train_accuracy' in df.columns and df['train_accuracy'].notna().any() else None,
            'final_val_acc': df['val_accuracy'].iloc[-1] if 'val_accuracy' in df.columns and df['val_accuracy'].notna().any() else None,
            'best_val_acc': df['val_accuracy'].max() if 'val_accuracy' in df.columns and df['val_accuracy'].notna().any() else None,
            'avg_batch_time': df['batch_time'].mean() if 'batch_time' in df.columns and df['batch_time'].notna().any() else None,
            'max_gpu_memory': df['gpu_memory'].max() if 'gpu_memory' in df.columns and df['gpu_memory'].notna().any() else None
        }
        
        return stats
    
    def clear_history(self):
        """清空历史记录"""
        self.metrics_history.clear()
        self.current_epoch = 0
        self.current_step = 0
        self.logger.info("Training history cleared")
