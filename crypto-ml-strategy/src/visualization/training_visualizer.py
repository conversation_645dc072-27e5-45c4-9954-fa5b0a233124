"""
Training Process Visualization Module
Provides real-time training monitoring and chart generation functionality
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import seaborn as sns
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import os
from typing import Dict, List, Optional, Tuple
import logging
from pathlib import Path
import json

# Set font and style settings
import matplotlib
# Suppress font warnings
import warnings
warnings.filterwarnings('ignore', category=UserWarning, module='matplotlib')
matplotlib.rcParams['font.family'] = 'sans-serif'
matplotlib.rcParams['font.sans-serif'] = ['Noto Sans CJK SC', 'WenQuanYi Zen Hei', 'SimHei', 'DejaVu Sans', 'Arial Unicode MS']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.sans-serif'] = ['Noto Sans CJK SC', 'WenQuanYi Zen Hei', 'SimHei', 'DejaVu Sans', 'Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

class TrainingVisualizer:
    """Training Process Visualizer"""

    def __init__(self, save_dir: str = "plots", figsize: Tuple[int, int] = (15, 10)):
        """
        Initialize visualizer

        Args:
            save_dir: Chart save directory
            figsize: Chart size
        """
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        self.figsize = figsize
        self.logger = logging.getLogger(__name__)
        
        # Training history data
        self.training_history = {
            'epoch': [],
            'train_loss': [],
            'val_loss': [],
            'train_accuracy': [],
            'val_accuracy': [],
            'learning_rate': [],
            'timestamp': [],
            'gpu_memory': [],
            'batch_time': []
        }
        
        # Real-time monitoring data
        self.real_time_data = {
            'batch_losses': [],
            'batch_accuracies': [],
            'batch_times': [],
            'current_epoch': 0
        }
        
    def update_epoch_metrics(self, epoch: int, metrics: Dict):
        """
        Update epoch-level metrics

        Args:
            epoch: Current epoch
            metrics: Metrics dictionary
        """
        self.training_history['epoch'].append(epoch)
        self.training_history['train_loss'].append(metrics.get('train_loss', 0))
        self.training_history['val_loss'].append(metrics.get('val_loss', 0))
        self.training_history['train_accuracy'].append(metrics.get('train_accuracy', 0))
        self.training_history['val_accuracy'].append(metrics.get('val_accuracy', 0))
        self.training_history['learning_rate'].append(metrics.get('learning_rate', 0))
        self.training_history['timestamp'].append(datetime.now())
        self.training_history['gpu_memory'].append(metrics.get('gpu_memory', 0))
        self.training_history['batch_time'].append(metrics.get('avg_batch_time', 0))

        # Update current epoch for real-time monitoring
        self.real_time_data['current_epoch'] = epoch

        # Generate epoch chart
        self.plot_epoch_summary(epoch)
        
    def update_batch_metrics(self, batch_idx: int, loss: float, accuracy: float, batch_time: float):
        """
        Update batch-level metrics

        Args:
            batch_idx: Batch index
            loss: Loss value
            accuracy: Accuracy
            batch_time: Batch processing time
        """
        self.real_time_data['batch_losses'].append(loss)
        self.real_time_data['batch_accuracies'].append(accuracy)
        self.real_time_data['batch_times'].append(batch_time)
        
        # Generate real-time chart every 500 batches (减少频率以提高性能)
        if batch_idx % 500 == 0:
            self.plot_real_time_metrics()
    
    def plot_epoch_summary(self, epoch: int):
        """
        Plot epoch summary chart

        Args:
            epoch: Current epoch
        """
        if len(self.training_history['epoch']) < 2:
            return
            
        fig, axes = plt.subplots(2, 3, figsize=self.figsize)
        fig.suptitle(f'Training Progress Summary - Epoch {epoch}', fontsize=16, fontweight='bold')
        
        # 1. Loss curves
        axes[0, 0].plot(self.training_history['epoch'], self.training_history['train_loss'], 
                       'b-', label='Train Loss', linewidth=2, marker='o')
        axes[0, 0].plot(self.training_history['epoch'], self.training_history['val_loss'], 
                       'r-', label='Val Loss', linewidth=2, marker='s')
        axes[0, 0].set_title('Loss Curve')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Accuracy curves
        axes[0, 1].plot(self.training_history['epoch'], self.training_history['train_accuracy'], 
                       'g-', label='Train Accuracy', linewidth=2, marker='o')
        axes[0, 1].plot(self.training_history['epoch'], self.training_history['val_accuracy'], 
                       'orange', label='Val Accuracy', linewidth=2, marker='s')
        axes[0, 1].set_title('Accuracy Curve')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Accuracy')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Learning rate changes
        axes[0, 2].plot(self.training_history['epoch'], self.training_history['learning_rate'], 
                       'purple', linewidth=2, marker='d')
        axes[0, 2].set_title('Learning Rate')
        axes[0, 2].set_xlabel('Epoch')
        axes[0, 2].set_ylabel('Learning Rate')
        axes[0, 2].set_yscale('log')
        axes[0, 2].grid(True, alpha=0.3)
        
        # 4. GPU memory usage
        axes[1, 0].plot(self.training_history['epoch'], self.training_history['gpu_memory'], 
                       'red', linewidth=2, marker='x')
        axes[1, 0].set_title('GPU Memory Usage')
        axes[1, 0].set_xlabel('Epoch')
        axes[1, 0].set_ylabel('Memory (MB)')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 5. Batch processing time
        axes[1, 1].plot(self.training_history['epoch'], self.training_history['batch_time'], 
                       'brown', linewidth=2, marker='^')
        axes[1, 1].set_title('Average Batch Time')
        axes[1, 1].set_xlabel('Epoch')
        axes[1, 1].set_ylabel('Time (s)')
        axes[1, 1].grid(True, alpha=0.3)
        
        # 6. Training timeline
        if len(self.training_history['timestamp']) > 1:
            time_diff = [(t - self.training_history['timestamp'][0]).total_seconds() / 3600 
                        for t in self.training_history['timestamp']]
            axes[1, 2].plot(time_diff, self.training_history['train_loss'], 
                           'blue', linewidth=2, marker='o', alpha=0.7)
            axes[1, 2].set_title('Training Timeline')
            axes[1, 2].set_xlabel('Training Time (hours)')
            axes[1, 2].set_ylabel('Train Loss')
            axes[1, 2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save chart
        save_path = self.save_dir / f"epoch_{epoch:03d}_summary.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        self.logger.info(f"📊 Epoch {epoch} chart saved: {save_path}")
    
    def plot_real_time_metrics(self):
        """Plot real-time metrics chart"""
        if len(self.real_time_data['batch_losses']) < 10:
            return
            
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        current_epoch = self.real_time_data.get("current_epoch", 0)
        fig.suptitle(f'Real-time Training Monitor - Epoch {current_epoch}',
                    fontsize=14, fontweight='bold')
        
        # Data from the last 100 batches
        recent_data = {
            'losses': self.real_time_data['batch_losses'][-100:],
            'accuracies': self.real_time_data['batch_accuracies'][-100:],
            'times': self.real_time_data['batch_times'][-100:]
        }
        
        batch_indices = list(range(len(recent_data['losses'])))
        
        # 1. Real-time loss
        axes[0].plot(batch_indices, recent_data['losses'], 'b-', alpha=0.7, linewidth=1)
        axes[0].set_title('Real-time Loss (Last 100 Batches)')
        axes[0].set_xlabel('Batch')
        axes[0].set_ylabel('Loss')
        axes[0].grid(True, alpha=0.3)
        
        # Add moving average line
        if len(recent_data['losses']) >= 10:
            ma_losses = pd.Series(recent_data['losses']).rolling(window=10).mean()
            axes[0].plot(batch_indices, ma_losses, 'r-', linewidth=2, label='10-Batch Moving Average')
            axes[0].legend()
        
        # 2. Real-time accuracy
        axes[1].plot(batch_indices, recent_data['accuracies'], 'g-', alpha=0.7, linewidth=1)
        axes[1].set_title('Real-time Accuracy (Last 100 Batches)')
        axes[1].set_xlabel('Batch')
        axes[1].set_ylabel('Accuracy')
        axes[1].grid(True, alpha=0.3)
        
        # Add moving average line
        if len(recent_data['accuracies']) >= 10:
            ma_acc = pd.Series(recent_data['accuracies']).rolling(window=10).mean()
            axes[1].plot(batch_indices, ma_acc, 'orange', linewidth=2, label='10-Batch Moving Average')
            axes[1].legend()
        
        # 3. Batch processing time
        axes[2].plot(batch_indices, recent_data['times'], 'purple', alpha=0.7, linewidth=1)
        axes[2].set_title('Batch Processing Time (Last 100 Batches)')
        axes[2].set_xlabel('Batch')
        axes[2].set_ylabel('Time (s)')
        axes[2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # Save real-time chart
        save_path = self.save_dir / "real_time_metrics.png"
        plt.savefig(save_path, dpi=150, bbox_inches='tight')
        plt.close()
    
    def plot_final_summary(self):
        """Plot final training summary"""
        if len(self.training_history['epoch']) == 0:
            return
            
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('Training Summary', fontsize=18, fontweight='bold')
        
        # 1. Complete loss and accuracy curves
        ax1 = axes[0, 0]
        ax1_twin = ax1.twinx()
        
        line1 = ax1.plot(self.training_history['epoch'], self.training_history['train_loss'], 
                        'b-', label='Train Loss', linewidth=2)
        line2 = ax1.plot(self.training_history['epoch'], self.training_history['val_loss'], 
                        'r-', label='Val Loss', linewidth=2)
        line3 = ax1_twin.plot(self.training_history['epoch'], self.training_history['train_accuracy'], 
                             'g--', label='Train Accuracy', linewidth=2)
        line4 = ax1_twin.plot(self.training_history['epoch'], self.training_history['val_accuracy'], 
                             'orange', label='Val Accuracy', linewidth=2, linestyle='--')
        
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss', color='blue')
        ax1_twin.set_ylabel('Accuracy', color='green')
        ax1.set_title('Training Process Overview')
        
        # Combine legends
        lines = line1 + line2 + line3 + line4
        labels = [l.get_label() for l in lines]
        ax1.legend(lines, labels, loc='center right')
        
        # 2. Performance metrics distribution
        axes[0, 1].hist(self.real_time_data['batch_losses'], bins=50, alpha=0.7, color='blue')
        axes[0, 1].set_title('Batch Loss Distribution')
        axes[0, 1].set_xlabel('Loss')
        axes[0, 1].set_ylabel('Frequency')
        
        # 3. Training efficiency analysis
        if len(self.training_history['timestamp']) > 1:
            total_time = (self.training_history['timestamp'][-1] - 
                         self.training_history['timestamp'][0]).total_seconds() / 3600
            epochs_per_hour = len(self.training_history['epoch']) / total_time if total_time > 0 else 0
            
            axes[1, 0].bar(['Total Time(h)', 'Epoch/h', 'Avg Batch Time(s)', 'GPU Memory Peak(MB)'],
                          [total_time, epochs_per_hour, 
                           np.mean(self.training_history['batch_time']),
                           max(self.training_history['gpu_memory']) if self.training_history['gpu_memory'] else 0],
                          color=['skyblue', 'lightgreen', 'orange', 'red'])
            axes[1, 0].set_title('Training Efficiency Statistics')
            axes[1, 0].set_ylabel('Value')
        
        # 4. Best performance metrics
        if self.training_history['val_loss']:
            best_epoch = np.argmin(self.training_history['val_loss'])
            best_metrics = {
                'Best Epoch': self.training_history['epoch'][best_epoch],
                'Best Val Loss': self.training_history['val_loss'][best_epoch],
                'Corresponding Train Loss': self.training_history['train_loss'][best_epoch],
                'Val Accuracy': self.training_history['val_accuracy'][best_epoch],
                'Train Accuracy': self.training_history['train_accuracy'][best_epoch]
            }
            
            metrics_text = '\n'.join([f'{k}: {v:.4f}' for k, v in best_metrics.items()])
            axes[1, 1].text(0.1, 0.5, metrics_text, fontsize=12, 
                           verticalalignment='center', transform=axes[1, 1].transAxes,
                           bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
            axes[1, 1].set_title('Best Performance Metrics')
            axes[1, 1].axis('off')
        
        plt.tight_layout()
        
        # Save final summary chart
        save_path = self.save_dir / "training_final_summary.png"
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        self.logger.info(f"📊 Training summary chart saved: {save_path}")
    
    def save_training_data(self):
        """Save training data to JSON file"""
        data = {
            'training_history': self.training_history,
            'real_time_data': self.real_time_data,
            'summary': {
                'total_epochs': len(self.training_history['epoch']),
                'best_val_loss': min(self.training_history['val_loss']) if self.training_history['val_loss'] else None,
                'final_train_loss': self.training_history['train_loss'][-1] if self.training_history['train_loss'] else None,
                'training_completed': datetime.now().isoformat()
            }
        }
        
        # Convert datetime objects to strings
        for i, ts in enumerate(data['training_history']['timestamp']):
            data['training_history']['timestamp'][i] = ts.isoformat()
        
        save_path = self.save_dir / "training_data.json"
        with open(save_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📊 Training data saved: {save_path}")
