"""
增强可视化训练监控器 - 实时训练过程监控和交互式界面
"""

import asyncio
import json
import time
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path
import numpy as np
import pandas as pd
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import plotly.express as px
from plotly.offline import plot
import logging
from dataclasses import dataclass, asdict
import websockets
import uvicorn
from fastapi import FastAPI, WebSocket, WebSocketDisconnect
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse


@dataclass
class TrainingMetrics:
    """训练指标数据类"""
    epoch: int
    step: int
    train_loss: float
    val_loss: Optional[float] = None
    train_accuracy: Optional[float] = None
    val_accuracy: Optional[float] = None
    learning_rate: float = 0.0
    gpu_memory_used: float = 0.0
    gpu_memory_total: float = 0.0
    batch_time: float = 0.0
    data_time: float = 0.0
    timestamp: str = ""
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


class EnhancedTrainingMonitor:
    """增强训练监控器"""
    
    def __init__(self, save_dir: str = "logs/enhanced_monitor", port: int = 8888):
        """
        初始化增强训练监控器
        
        Args:
            save_dir: 保存目录
            port: Web服务端口
        """
        self.save_dir = Path(save_dir)
        self.save_dir.mkdir(parents=True, exist_ok=True)
        
        self.port = port
        self.metrics_history: List[TrainingMetrics] = []
        self.websocket_connections: List[WebSocket] = []
        
        # 实时监控配置
        self.max_history = 10000
        self.update_interval = 1.0  # 秒
        self.auto_save_interval = 60  # 秒
        
        # Web应用
        self.app = FastAPI(title="Enhanced Training Monitor")
        self.setup_web_app()
        
        # 监控状态
        self.is_monitoring = False
        self.monitor_thread = None
        self.web_server_thread = None
        
        self.logger = logging.getLogger(__name__)
        
        # 性能统计
        self.performance_stats = {
            'total_updates': 0,
            'avg_update_time': 0.0,
            'last_update_time': 0.0
        }
    
    def setup_web_app(self):
        """设置Web应用"""
        # 静态文件
        static_dir = self.save_dir / "static"
        static_dir.mkdir(exist_ok=True)
        
        # 创建HTML模板
        self.create_html_template()
        
        @self.app.get("/")
        async def get_dashboard():
            """获取仪表板页面"""
            html_path = self.save_dir / "dashboard.html"
            with open(html_path, 'r', encoding='utf-8') as f:
                return HTMLResponse(content=f.read())
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket端点"""
            await websocket.accept()
            self.websocket_connections.append(websocket)
            
            try:
                # 发送历史数据
                if self.metrics_history:
                    await websocket.send_json({
                        'type': 'history',
                        'data': [asdict(m) for m in self.metrics_history[-100:]]
                    })
                
                # 保持连接
                while True:
                    await websocket.receive_text()
                    
            except WebSocketDisconnect:
                self.websocket_connections.remove(websocket)
        
        @self.app.get("/api/metrics")
        async def get_metrics():
            """获取训练指标API"""
            return {
                'metrics': [asdict(m) for m in self.metrics_history[-100:]],
                'stats': self.performance_stats
            }
        
        @self.app.get("/api/status")
        async def get_status():
            """获取监控状态API"""
            return {
                'is_monitoring': self.is_monitoring,
                'total_metrics': len(self.metrics_history),
                'last_update': self.metrics_history[-1].timestamp if self.metrics_history else None,
                'performance_stats': self.performance_stats
            }
    
    def create_html_template(self):
        """创建HTML仪表板模板"""
        html_content = """
<!DOCTYPE html>
<html>
<head>
    <title>Enhanced Training Monitor</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <style>
        body { font-family: Arial, sans-serif; margin: 0; padding: 20px; background-color: #f5f5f5; }
        .header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        .metrics-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-bottom: 20px; }
        .metric-card { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .metric-value { font-size: 2em; font-weight: bold; color: #667eea; }
        .metric-label { color: #666; margin-top: 5px; }
        .chart-container { background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }
        .status-indicator { display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 8px; }
        .status-active { background-color: #4CAF50; }
        .status-inactive { background-color: #f44336; }
        .controls { margin-bottom: 20px; }
        .btn { background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px; }
        .btn:hover { background: #5a6fd8; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🚀 Enhanced Training Monitor</h1>
        <p>Real-time Deep Learning Training Visualization</p>
        <div>
            <span class="status-indicator" id="status-indicator"></span>
            <span id="status-text">Connecting...</span>
        </div>
    </div>
    
    <div class="controls">
        <button class="btn" onclick="toggleMonitoring()">Toggle Monitoring</button>
        <button class="btn" onclick="clearHistory()">Clear History</button>
        <button class="btn" onclick="exportData()">Export Data</button>
    </div>
    
    <div class="metrics-grid">
        <div class="metric-card">
            <div class="metric-value" id="current-loss">--</div>
            <div class="metric-label">Current Loss</div>
        </div>
        <div class="metric-card">
            <div class="metric-value" id="current-accuracy">--</div>
            <div class="metric-label">Current Accuracy</div>
        </div>
        <div class="metric-card">
            <div class="metric-value" id="learning-rate">--</div>
            <div class="metric-label">Learning Rate</div>
        </div>
        <div class="metric-card">
            <div class="metric-value" id="gpu-memory">--</div>
            <div class="metric-label">GPU Memory</div>
        </div>
    </div>
    
    <div class="chart-container">
        <div id="loss-chart" style="height: 400px;"></div>
    </div>
    
    <div class="chart-container">
        <div id="accuracy-chart" style="height: 400px;"></div>
    </div>
    
    <div class="chart-container">
        <div id="performance-chart" style="height: 400px;"></div>
    </div>
    
    <script>
        let ws;
        let metricsData = [];
        
        function connectWebSocket() {
            ws = new WebSocket(`ws://localhost:${window.location.port}/ws`);
            
            ws.onopen = function(event) {
                updateStatus(true);
            };
            
            ws.onmessage = function(event) {
                const message = JSON.parse(event.data);
                
                if (message.type === 'history') {
                    metricsData = message.data;
                    updateCharts();
                } else if (message.type === 'update') {
                    metricsData.push(message.data);
                    if (metricsData.length > 1000) {
                        metricsData = metricsData.slice(-1000);
                    }
                    updateCharts();
                    updateMetricCards(message.data);
                }
            };
            
            ws.onclose = function(event) {
                updateStatus(false);
                setTimeout(connectWebSocket, 3000);
            };
        }
        
        function updateStatus(connected) {
            const indicator = document.getElementById('status-indicator');
            const text = document.getElementById('status-text');
            
            if (connected) {
                indicator.className = 'status-indicator status-active';
                text.textContent = 'Connected';
            } else {
                indicator.className = 'status-indicator status-inactive';
                text.textContent = 'Disconnected';
            }
        }
        
        function updateMetricCards(metrics) {
            document.getElementById('current-loss').textContent = metrics.train_loss?.toFixed(4) || '--';
            document.getElementById('current-accuracy').textContent = metrics.train_accuracy ? (metrics.train_accuracy * 100).toFixed(2) + '%' : '--';
            document.getElementById('learning-rate').textContent = metrics.learning_rate?.toExponential(2) || '--';
            document.getElementById('gpu-memory').textContent = metrics.gpu_memory_used ? (metrics.gpu_memory_used / 1024).toFixed(1) + 'GB' : '--';
        }
        
        function updateCharts() {
            if (metricsData.length === 0) return;
            
            // Loss Chart
            const lossTrace = {
                x: metricsData.map(m => m.step),
                y: metricsData.map(m => m.train_loss),
                type: 'scatter',
                mode: 'lines',
                name: 'Training Loss',
                line: { color: '#667eea' }
            };
            
            const valLossTrace = {
                x: metricsData.filter(m => m.val_loss).map(m => m.step),
                y: metricsData.filter(m => m.val_loss).map(m => m.val_loss),
                type: 'scatter',
                mode: 'lines',
                name: 'Validation Loss',
                line: { color: '#f093fb' }
            };
            
            Plotly.newPlot('loss-chart', [lossTrace, valLossTrace], {
                title: 'Training & Validation Loss',
                xaxis: { title: 'Step' },
                yaxis: { title: 'Loss' }
            });
            
            // Accuracy Chart
            if (metricsData.some(m => m.train_accuracy)) {
                const accTrace = {
                    x: metricsData.filter(m => m.train_accuracy).map(m => m.step),
                    y: metricsData.filter(m => m.train_accuracy).map(m => m.train_accuracy * 100),
                    type: 'scatter',
                    mode: 'lines',
                    name: 'Training Accuracy',
                    line: { color: '#4CAF50' }
                };
                
                Plotly.newPlot('accuracy-chart', [accTrace], {
                    title: 'Training Accuracy',
                    xaxis: { title: 'Step' },
                    yaxis: { title: 'Accuracy (%)' }
                });
            }
            
            // Performance Chart
            const memoryTrace = {
                x: metricsData.map(m => m.step),
                y: metricsData.map(m => m.gpu_memory_used / 1024),
                type: 'scatter',
                mode: 'lines',
                name: 'GPU Memory (GB)',
                line: { color: '#ff6b6b' }
            };
            
            Plotly.newPlot('performance-chart', [memoryTrace], {
                title: 'Performance Metrics',
                xaxis: { title: 'Step' },
                yaxis: { title: 'Memory (GB)' }
            });
        }
        
        function toggleMonitoring() {
            fetch('/api/toggle-monitoring', { method: 'POST' });
        }
        
        function clearHistory() {
            metricsData = [];
            updateCharts();
        }
        
        function exportData() {
            const dataStr = JSON.stringify(metricsData, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = 'training_metrics.json';
            link.click();
        }
        
        // Initialize
        connectWebSocket();
        
        // Auto-refresh every 5 seconds
        setInterval(() => {
            fetch('/api/metrics')
                .then(response => response.json())
                .then(data => {
                    if (data.metrics.length > metricsData.length) {
                        metricsData = data.metrics;
                        updateCharts();
                    }
                });
        }, 5000);
    </script>
</body>
</html>
        """
        
        html_path = self.save_dir / "dashboard.html"
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
    
    def start_monitoring(self):
        """开始监控"""
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        
        # 启动Web服务器
        self.start_web_server()
        
        self.logger.info(f"🚀 Enhanced Training Monitor started on http://localhost:{self.port}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_monitoring = False
        
        if self.web_server_thread and self.web_server_thread.is_alive():
            self.web_server_thread.join(timeout=5)
        
        self.logger.info("Enhanced Training Monitor stopped")
    
    def start_web_server(self):
        """启动Web服务器"""
        def run_server():
            try:
                import uvicorn
                uvicorn.run(
                    self.app,
                    host="0.0.0.0",
                    port=self.port,
                    log_level="warning",
                    access_log=False
                )
            except Exception as e:
                self.logger.error(f"Web服务器启动失败: {e}")

        self.web_server_thread = threading.Thread(target=run_server, daemon=True)
        self.web_server_thread.start()

        # 等待服务器启动
        import time
        time.sleep(2)  # 给服务器一些启动时间
    
    async def update_metrics(self, metrics: TrainingMetrics):
        """更新训练指标"""
        start_time = time.time()
        
        # 添加到历史记录
        self.metrics_history.append(metrics)
        
        # 限制历史记录长度
        if len(self.metrics_history) > self.max_history:
            self.metrics_history = self.metrics_history[-self.max_history:]
        
        # 广播到WebSocket连接
        message = {
            'type': 'update',
            'data': asdict(metrics)
        }
        
        disconnected = []
        for ws in self.websocket_connections:
            try:
                await ws.send_json(message)
            except:
                disconnected.append(ws)
        
        # 移除断开的连接
        for ws in disconnected:
            self.websocket_connections.remove(ws)
        
        # 更新性能统计
        update_time = time.time() - start_time
        self.performance_stats['total_updates'] += 1
        self.performance_stats['avg_update_time'] = (
            (self.performance_stats['avg_update_time'] * (self.performance_stats['total_updates'] - 1) + update_time) /
            self.performance_stats['total_updates']
        )
        self.performance_stats['last_update_time'] = time.time()
    
    def save_metrics_history(self):
        """保存指标历史"""
        if not self.metrics_history:
            return
        
        # 保存为JSON
        json_path = self.save_dir / f"metrics_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(json_path, 'w') as f:
            json.dump([asdict(m) for m in self.metrics_history], f, indent=2)
        
        # 保存为CSV
        csv_path = self.save_dir / f"metrics_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        df = pd.DataFrame([asdict(m) for m in self.metrics_history])
        df.to_csv(csv_path, index=False)
        
        self.logger.info(f"Metrics history saved: {json_path}, {csv_path}")
    
    def create_summary_report(self) -> Dict[str, Any]:
        """创建训练总结报告"""
        if not self.metrics_history:
            return {}
        
        df = pd.DataFrame([asdict(m) for m in self.metrics_history])
        
        report = {
            'training_summary': {
                'total_epochs': df['epoch'].max() if 'epoch' in df else 0,
                'total_steps': len(df),
                'training_duration': (
                    pd.to_datetime(df['timestamp'].iloc[-1]) - 
                    pd.to_datetime(df['timestamp'].iloc[0])
                ).total_seconds() / 3600,  # hours
                'final_train_loss': df['train_loss'].iloc[-1] if 'train_loss' in df else None,
                'best_train_loss': df['train_loss'].min() if 'train_loss' in df else None,
                'final_accuracy': df['train_accuracy'].iloc[-1] if 'train_accuracy' in df else None,
                'best_accuracy': df['train_accuracy'].max() if 'train_accuracy' in df else None
            },
            'performance_stats': self.performance_stats,
            'convergence_analysis': self._analyze_convergence(df),
            'resource_usage': self._analyze_resource_usage(df)
        }
        
        return report
    
    def _analyze_convergence(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析收敛性"""
        if 'train_loss' not in df or len(df) < 10:
            return {}
        
        # 计算损失变化趋势
        recent_steps = min(100, len(df) // 4)
        recent_loss = df['train_loss'].tail(recent_steps)
        
        # 线性回归分析趋势
        x = np.arange(len(recent_loss))
        slope = np.polyfit(x, recent_loss, 1)[0]
        
        return {
            'is_converging': slope < -1e-6,
            'convergence_rate': abs(slope),
            'loss_stability': recent_loss.std(),
            'improvement_ratio': (df['train_loss'].iloc[0] - df['train_loss'].iloc[-1]) / df['train_loss'].iloc[0]
        }
    
    def _analyze_resource_usage(self, df: pd.DataFrame) -> Dict[str, Any]:
        """分析资源使用"""
        analysis = {}
        
        if 'gpu_memory_used' in df:
            analysis['gpu_memory'] = {
                'avg_usage_gb': df['gpu_memory_used'].mean() / 1024,
                'max_usage_gb': df['gpu_memory_used'].max() / 1024,
                'usage_efficiency': df['gpu_memory_used'].mean() / df['gpu_memory_total'].mean() if 'gpu_memory_total' in df else None
            }
        
        if 'batch_time' in df:
            analysis['timing'] = {
                'avg_batch_time': df['batch_time'].mean(),
                'total_training_time': df['batch_time'].sum(),
                'throughput_samples_per_sec': 1.0 / df['batch_time'].mean() if df['batch_time'].mean() > 0 else 0
            }
        
        return analysis


# 全局实例
_enhanced_monitor = None

def get_enhanced_monitor() -> EnhancedTrainingMonitor:
    """获取全局增强监控器实例"""
    global _enhanced_monitor
    if _enhanced_monitor is None:
        _enhanced_monitor = EnhancedTrainingMonitor()
    return _enhanced_monitor
