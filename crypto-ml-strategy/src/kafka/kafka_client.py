"""
Kafka客户端模块
提供Kafka生产者和消费者功能，用于实时数据流处理
"""

import json
import asyncio
import logging
from typing import Dict, Any, Optional, List, Callable
from datetime import datetime
import traceback

try:
    from kafka import KafkaProducer, KafkaConsumer
    from kafka.errors import KafkaError
    KAFKA_AVAILABLE = True
except ImportError:
    KAFKA_AVAILABLE = False
    KafkaProducer = None
    KafkaConsumer = None
    KafkaError = Exception

from src.utils.config import get_config_manager


class KafkaClient:
    """Kafka客户端，提供生产者和消费者功能"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.logger = logging.getLogger(__name__)
        if not config:
            self.logger.warning("Kafka configuration not provided directly, attempting to load from config manager.")
            config = get_config_manager().get('kafka')
            if not config:
                raise ValueError("Kafka configuration is required but not found in config manager.")
        self.config = config
        
        # Kafka配置
        self.bootstrap_servers = self.config.get('bootstrap_servers', 'localhost:9092')
        self.producer = None
        self.consumer = None
        self.is_running = False
        
        # 主题配置
        self.topics = self.config.get('topics', {
            'market_data': 'crypto-market-data-kline',  # 与Java端保持一致
            'trade_signals': 'trade-signals',
            'order_events': 'order-events'
        })
        
        if not KAFKA_AVAILABLE:
            self.logger.warning("⚠️ Kafka库未安装，将使用模拟模式")

    async def initialize(self):
        """初始化Kafka客户端"""
        try:
            if not KAFKA_AVAILABLE:
                self.logger.warning("⚠️ Kafka库不可用，跳过初始化")
                return False

            # 测试连接
            self.producer = self._create_producer()
            if self.producer:
                self.logger.info("✅ Kafka客户端初始化成功")
                return True
            else:
                self.logger.warning("⚠️ Kafka生产者创建失败")
                return False

        except Exception as e:
            self.logger.error(f"❌ Kafka客户端初始化失败: {e}")
            return False
    
    def _create_producer(self) -> Optional[KafkaProducer]:
        """创建Kafka生产者"""
        if not KAFKA_AVAILABLE:
            return None
            
        try:
            producer_config = {
                'bootstrap_servers': self.bootstrap_servers,
                'value_serializer': lambda v: json.dumps(v).encode('utf-8'),
                'key_serializer': lambda k: k.encode('utf-8') if k else None,
                'acks': 'all',
                'retries': 3,
                'linger_ms': 5,
                'batch_size': 16384,
                'buffer_memory': 33554432,
                'compression_type': 'lz4'
            }
            
            producer = KafkaProducer(**producer_config)
            self.logger.info(f"✅ Kafka生产者创建成功: {self.bootstrap_servers}")
            return producer
            
        except Exception as e:
            self.logger.error(f"❌ Kafka生产者创建失败: {e}")
            return None
    
    def _create_consumer(self, topics: List[str], group_id: str = 'crypto-ml-strategy') -> Optional[KafkaConsumer]:
        """创建Kafka消费者"""
        if not KAFKA_AVAILABLE:
            return None
            
        try:
            consumer_config = {
                'bootstrap_servers': self.bootstrap_servers,
                'group_id': group_id,
                'value_deserializer': lambda m: json.loads(m.decode('utf-8')) if m else None,
                'key_deserializer': lambda m: m.decode('utf-8') if m else None,
                'auto_offset_reset': 'latest',
                'enable_auto_commit': False,
                'max_poll_records': 100,
                'consumer_timeout_ms': 1000
            }
            
            consumer = KafkaConsumer(*topics, **consumer_config)
            self.logger.info(f"✅ Kafka消费者创建成功: topics={topics}, group_id={group_id}")
            return consumer
            
        except Exception as e:
            self.logger.error(f"❌ Kafka消费者创建失败: {e}")
            return None
    
    async def send_signal(self, signal_data: Dict[str, Any], symbol: str) -> bool:
        """发送交易信号到Kafka"""
        try:
            if not self.producer:
                self.producer = self._create_producer()
                
            if not self.producer:
                self.logger.warning("⚠️ Kafka生产者不可用，跳过信号发送")
                return False
            
            # 准备信号数据
            message = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'signal_data': signal_data,
                'source': 'crypto-ml-strategy'
            }
            
            # 发送到trade-signals主题
            topic = self.topics.get('trade_signals', 'trade-signals')
            future = self.producer.send(topic, key=symbol, value=message)
            
            # 等待发送完成
            record_metadata = future.get(timeout=10)
            
            self.logger.info(f"✅ 交易信号已发送: topic={topic}, symbol={symbol}, partition={record_metadata.partition}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 发送交易信号失败: {e}")
            return False
    
    async def consume_market_data(self, callback: Callable[[Dict[str, Any]], None], timeout_seconds: int = 30) -> int:
        """消费市场数据"""
        self.logger.info(f"🔄 开始消费市场数据，超时: {timeout_seconds}秒")

        if not KAFKA_AVAILABLE:
            self.logger.warning("⚠️ Kafka不可用，使用模拟数据")
            return await self._simulate_market_data_consumption(callback, timeout_seconds)
        
        try:
            # 创建消费者
            topics = [self.topics.get('market_data', 'crypto-market-data-kline')]
            self.logger.info(f"📋 准备消费主题: {topics}")
            consumer = self._create_consumer(topics)

            if not consumer:
                self.logger.warning("⚠️ Kafka消费者不可用，使用模拟数据")
                return await self._simulate_market_data_consumption(callback, timeout_seconds)

            self.logger.info("✅ Kafka消费者创建成功，开始消费真实数据")

            # 🔥 修复：检查Kafka连接状态（使用正确的方法）
            try:
                # 使用topics()方法检查连接状态，而不是list_consumer_group_offsets
                available_topics = consumer.topics()
                self.logger.info(f"📊 Kafka连接状态正常，可用主题数: {len(available_topics) if available_topics else 0}")

                # 检查我们需要的主题是否存在
                required_topics = [self.topics.get('market_data', 'crypto-market-data-kline')]
                missing_topics = [topic for topic in required_topics if topic not in available_topics]
                if missing_topics:
                    self.logger.warning(f"⚠️ 缺少必需的主题: {missing_topics}")
                else:
                    self.logger.info(f"✅ 所有必需的主题都存在: {required_topics}")

            except Exception as e:
                self.logger.warning(f"⚠️ 无法获取Kafka主题信息: {e}")
                # 即使检查失败，也继续尝试消费，让实际的消费过程来处理错误
            
            self.logger.info(f"🔄 开始消费市场数据: topics={topics}, timeout={timeout_seconds}s")
            
            processed_count = 0
            start_time = asyncio.get_event_loop().time()
            
            self.is_running = True
            
            while self.is_running and (asyncio.get_event_loop().time() - start_time) < timeout_seconds:
                try:
                    # 轮询消息
                    message_pack = consumer.poll(timeout_ms=1000)

                    if message_pack:
                        self.logger.info(f"📨 接收到Kafka消息批次，分区数: {len(message_pack)}")

                    for topic_partition, messages in message_pack.items():
                        if messages:
                            self.logger.info(f"📋 处理分区 {topic_partition}，消息数: {len(messages)}")
                        for message in messages:
                            if not self.is_running:
                                break

                            try:
                                # 处理消息
                                market_data = message.value
                                if market_data:
                                    self.logger.debug(f"📊 处理Kafka消息: {type(market_data)}")
                                    await asyncio.get_event_loop().run_in_executor(None, callback, market_data)
                                    processed_count += 1

                                    if processed_count % 10 == 0:
                                        self.logger.info(f"📈 已处理 {processed_count} 条Kafka消息")

                                    # 手动提交偏移量
                                    consumer.commit_async()

                            except Exception as e:
                                self.logger.error(f"❌ 处理市场数据失败: {e}")
                                import traceback
                                self.logger.error(f"❌ 错误详情: {traceback.format_exc()}")
                    
                    # 让出控制权
                    await asyncio.sleep(0.1)
                    
                except Exception as e:
                    self.logger.error(f"❌ 轮询消息失败: {e}")
                    await asyncio.sleep(1)
            
            consumer.close()
            self.logger.info(f"✅ 市场数据消费完成: 处理了{processed_count}条消息")
            return processed_count
            
        except Exception as e:
            self.logger.error(f"❌ 消费市场数据失败: {e}")
            return 0
    
    async def _simulate_market_data_consumption(self, callback: Callable[[Dict[str, Any]], None], timeout_seconds: int) -> int:
        """模拟市场数据消费（当Kafka不可用时）"""
        self.logger.info("🔄 使用模拟市场数据进行在线学习...")
        
        processed_count = 0
        start_time = asyncio.get_event_loop().time()
        
        # 模拟市场数据
        symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT']
        
        while (asyncio.get_event_loop().time() - start_time) < timeout_seconds:
            for symbol in symbols:
                # 模拟市场数据
                market_data = {
                    'symbol': symbol,
                    'timestamp': datetime.now().isoformat(),
                    'price': 50000.0 + (processed_count % 1000),
                    'volume': 1000.0,
                    'features': [0.1, 0.2, 0.3, 0.4, 0.5] * 5  # 模拟25个特征
                }
                
                try:
                    await asyncio.get_event_loop().run_in_executor(None, callback, market_data)
                    processed_count += 1
                except Exception as e:
                    self.logger.error(f"❌ 处理模拟数据失败: {e}")
                
                await asyncio.sleep(0.5)  # 每0.5秒一条数据
        
        self.logger.info(f"✅ 模拟数据消费完成: 处理了{processed_count}条消息")
        return processed_count
    
    def stop(self):
        """停止消费"""
        self.is_running = False
        
        if self.producer:
            try:
                self.producer.close()
                self.logger.info("✅ Kafka生产者已关闭")
            except Exception as e:
                self.logger.error(f"❌ 关闭Kafka生产者失败: {e}")
        
        if self.consumer:
            try:
                self.consumer.close()
                self.logger.info("✅ Kafka消费者已关闭")
            except Exception as e:
                self.logger.error(f"❌ 关闭Kafka消费者失败: {e}")
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'is_running') and self.is_running:
            self.stop()
