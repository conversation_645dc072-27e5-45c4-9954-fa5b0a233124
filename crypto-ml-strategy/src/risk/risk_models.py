"""
风险模型实现
基于qlib风险模型架构的各种风险计算模型
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Union, Any
import logging
from abc import ABC, abstractmethod
from scipy import stats
from sklearn.covariance import LedoitWolf
import warnings


class BaseRiskModel(ABC):
    """风险模型基类"""
    
    def __init__(self, name: str = "BaseRiskModel"):
        self.name = name
        self.logger = logging.getLogger(f"{__name__}.{name}")
    
    @abstractmethod
    def calculate(self, *args, **kwargs):
        """计算风险指标 - 子类必须实现"""
        pass


class VaRModel(BaseRiskModel):
    """Value at Risk (VaR) 模型"""

    def __init__(self):
        super().__init__("VaRModel")

    def calculate(self, portfolio_data: Dict[str, Any], **kwargs) -> float:
        """实现基类的抽象方法"""
        return self.calculate_var(portfolio_data, **kwargs)
    
    def calculate_var(
        self,
        portfolio_data: Dict[str, Any],
        confidence_level: float = 0.95,
        time_horizon: int = 1,
        method: str = "historical"
    ) -> float:
        """
        计算投资组合VaR
        
        Args:
            portfolio_data: 投资组合数据
            confidence_level: 置信水平
            time_horizon: 时间范围(天)
            method: 计算方法 ('historical', 'parametric', 'monte_carlo')
            
        Returns:
            var_value: VaR值
        """
        returns = portfolio_data.get('returns', [])
        if not returns:
            return 0.0
        
        returns = np.array(returns)
        
        if method == "historical":
            return self._historical_var(returns, confidence_level, time_horizon)
        elif method == "parametric":
            return self._parametric_var(returns, confidence_level, time_horizon)
        elif method == "monte_carlo":
            return self._monte_carlo_var(returns, confidence_level, time_horizon)
        else:
            raise ValueError(f"Unknown VaR method: {method}")
    
    def _historical_var(
        self,
        returns: np.ndarray,
        confidence_level: float,
        time_horizon: int
    ) -> float:
        """历史模拟法计算VaR"""
        if len(returns) == 0:
            return 0.0
        
        # 调整时间范围
        adjusted_returns = returns * np.sqrt(time_horizon)
        
        # 计算分位数
        percentile = (1 - confidence_level) * 100
        var_value = np.percentile(adjusted_returns, percentile)
        
        return var_value
    
    def _parametric_var(
        self,
        returns: np.ndarray,
        confidence_level: float,
        time_horizon: int
    ) -> float:
        """参数法计算VaR"""
        if len(returns) == 0:
            return 0.0
        
        # 计算均值和标准差
        mean_return = np.mean(returns)
        std_return = np.std(returns, ddof=1)
        
        # 调整时间范围
        adjusted_mean = mean_return * time_horizon
        adjusted_std = std_return * np.sqrt(time_horizon)
        
        # 计算VaR
        z_score = stats.norm.ppf(1 - confidence_level)
        var_value = adjusted_mean + z_score * adjusted_std
        
        return var_value
    
    def _monte_carlo_var(
        self,
        returns: np.ndarray,
        confidence_level: float,
        time_horizon: int,
        num_simulations: int = 10000
    ) -> float:
        """蒙特卡洛模拟法计算VaR"""
        if len(returns) == 0:
            return 0.0
        
        # 拟合分布参数
        mean_return = np.mean(returns)
        std_return = np.std(returns, ddof=1)
        
        # 蒙特卡洛模拟
        simulated_returns = np.random.normal(
            mean_return * time_horizon,
            std_return * np.sqrt(time_horizon),
            num_simulations
        )
        
        # 计算VaR
        percentile = (1 - confidence_level) * 100
        var_value = np.percentile(simulated_returns, percentile)
        
        return var_value


class DrawdownModel(BaseRiskModel):
    """回撤模型"""

    def __init__(self):
        super().__init__("DrawdownModel")

    def calculate(self, returns: Union[pd.Series, np.ndarray], **kwargs) -> Dict[str, float]:
        """实现基类的抽象方法"""
        return self.calculate_drawdown(returns, **kwargs)
    
    def calculate_max_drawdown(self, returns: List[float]) -> float:
        """计算最大回撤"""
        if not returns:
            return 0.0
        
        returns = np.array(returns)
        cumulative_returns = np.cumprod(1 + returns)
        
        # 计算累计最高点
        peak = np.maximum.accumulate(cumulative_returns)
        
        # 计算回撤
        drawdown = (cumulative_returns - peak) / peak
        
        # 返回最大回撤
        max_drawdown = np.min(drawdown)
        
        return max_drawdown
    
    def calculate_current_drawdown(self, returns: List[float]) -> float:
        """计算当前回撤"""
        if not returns:
            return 0.0
        
        returns = np.array(returns)
        cumulative_returns = np.cumprod(1 + returns)
        
        # 找到历史最高点
        peak = np.max(cumulative_returns)
        current_value = cumulative_returns[-1]
        
        # 计算当前回撤
        current_drawdown = (current_value - peak) / peak
        
        return current_drawdown
    
    def calculate_drawdown_duration(self, returns: List[float]) -> Dict[str, int]:
        """计算回撤持续时间"""
        if not returns:
            return {'max_duration': 0, 'current_duration': 0}
        
        returns = np.array(returns)
        cumulative_returns = np.cumprod(1 + returns)
        peak = np.maximum.accumulate(cumulative_returns)
        
        # 标识回撤期间
        in_drawdown = cumulative_returns < peak
        
        # 计算回撤持续时间
        durations = []
        current_duration = 0
        
        for is_dd in in_drawdown:
            if is_dd:
                current_duration += 1
            else:
                if current_duration > 0:
                    durations.append(current_duration)
                current_duration = 0
        
        # 如果当前仍在回撤中
        if current_duration > 0:
            durations.append(current_duration)
        
        max_duration = max(durations) if durations else 0
        current_dd_duration = current_duration if in_drawdown[-1] else 0
        
        return {
            'max_duration': max_duration,
            'current_duration': current_dd_duration
        }


class VolatilityModel(BaseRiskModel):
    """波动率模型"""

    def __init__(self):
        super().__init__("VolatilityModel")

    def calculate(self, returns: Union[pd.Series, np.ndarray], **kwargs) -> float:
        """实现基类的抽象方法"""
        return self.calculate_volatility(returns, **kwargs)
    
    def calculate_portfolio_volatility(
        self,
        portfolio_data: Dict[str, Any],
        market_data: Optional[pd.DataFrame] = None,
        method: str = "historical"
    ) -> float:
        """计算投资组合波动率"""
        returns = portfolio_data.get('returns', [])
        if not returns:
            return 0.0
        
        returns = np.array(returns)
        
        if method == "historical":
            return self._historical_volatility(returns)
        elif method == "ewma":
            return self._ewma_volatility(returns)
        elif method == "garch":
            return self._garch_volatility(returns)
        else:
            raise ValueError(f"Unknown volatility method: {method}")
    
    def _historical_volatility(self, returns: np.ndarray, annualize: bool = True) -> float:
        """历史波动率"""
        if len(returns) < 2:
            return 0.0
        
        volatility = np.std(returns, ddof=1)
        
        if annualize:
            # 假设252个交易日
            volatility *= np.sqrt(252)
        
        return volatility
    
    def _ewma_volatility(
        self,
        returns: np.ndarray,
        lambda_param: float = 0.94,
        annualize: bool = True
    ) -> float:
        """指数加权移动平均波动率"""
        if len(returns) < 2:
            return 0.0
        
        # 计算平方收益率
        squared_returns = returns ** 2
        
        # EWMA权重
        weights = np.array([lambda_param ** i for i in range(len(returns))])
        weights = weights[::-1]  # 反转，最新的权重最大
        weights /= weights.sum()  # 归一化
        
        # 计算EWMA方差
        ewma_variance = np.sum(weights * squared_returns)
        volatility = np.sqrt(ewma_variance)
        
        if annualize:
            volatility *= np.sqrt(252)
        
        return volatility
    
    def _garch_volatility(self, returns: np.ndarray, annualize: bool = True) -> float:
        """GARCH模型波动率 (简化版)"""
        # 这里使用简化的GARCH(1,1)模型
        if len(returns) < 10:
            return self._historical_volatility(returns, annualize)
        
        # GARCH(1,1)参数 (简化估计)
        alpha0 = 0.00001  # 常数项
        alpha1 = 0.1      # ARCH项系数
        beta1 = 0.85      # GARCH项系数
        
        # 初始化
        variance = np.var(returns)
        variances = [variance]
        
        # 递归计算条件方差
        for i in range(1, len(returns)):
            variance = alpha0 + alpha1 * (returns[i-1] ** 2) + beta1 * variance
            variances.append(variance)
        
        # 当前波动率
        current_volatility = np.sqrt(variances[-1])
        
        if annualize:
            current_volatility *= np.sqrt(252)
        
        return current_volatility


class ConcentrationRiskModel(BaseRiskModel):
    """集中度风险模型"""

    def __init__(self):
        super().__init__("ConcentrationRiskModel")

    def calculate(self, portfolio_weights: Union[pd.Series, np.ndarray, Dict], **kwargs) -> Dict[str, float]:
        """实现基类的抽象方法"""
        return self.calculate_concentration_risk(portfolio_weights, **kwargs)
    
    def calculate_concentration(self, positions: Dict[str, float]) -> Dict[str, float]:
        """计算集中度指标"""
        if not positions:
            return {
                'max_weight': 0.0,
                'hhi': 0.0,
                'effective_assets': 0.0,
                'concentration_ratio': 0.0
            }
        
        # 计算权重
        total_value = sum(abs(value) for value in positions.values())
        if total_value == 0:
            return {
                'max_weight': 0.0,
                'hhi': 0.0,
                'effective_assets': 0.0,
                'concentration_ratio': 0.0
            }
        
        weights = {asset: abs(value) / total_value for asset, value in positions.items()}
        weight_values = list(weights.values())
        
        # 最大权重
        max_weight = max(weight_values)
        
        # Herfindahl-Hirschman指数
        hhi = sum(w ** 2 for w in weight_values)
        
        # 有效资产数量
        effective_assets = 1 / hhi if hhi > 0 else 0
        
        # 集中度比率 (前5大资产权重之和)
        sorted_weights = sorted(weight_values, reverse=True)
        top5_weights = sorted_weights[:5]
        concentration_ratio = sum(top5_weights)
        
        return {
            'max_weight': max_weight,
            'hhi': hhi,
            'effective_assets': effective_assets,
            'concentration_ratio': concentration_ratio
        }


class PortfolioRiskModel(BaseRiskModel):
    """投资组合风险模型"""

    def __init__(self):
        super().__init__("PortfolioRiskModel")
        self.var_model = VaRModel()
        self.drawdown_model = DrawdownModel()
        self.volatility_model = VolatilityModel()
        self.concentration_model = ConcentrationRiskModel()

    def calculate(self, portfolio_data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """实现基类的抽象方法"""
        return self.calculate_portfolio_risk(portfolio_data, **kwargs)
    
    def calculate_portfolio_risk(
        self,
        portfolio_data: Dict[str, Any],
        market_data: Optional[pd.DataFrame] = None
    ) -> Dict[str, Any]:
        """计算投资组合综合风险"""
        
        risk_metrics = {}
        
        # VaR指标
        try:
            risk_metrics['var_95'] = self.var_model.calculate_var(
                portfolio_data, confidence_level=0.95
            )
            risk_metrics['var_99'] = self.var_model.calculate_var(
                portfolio_data, confidence_level=0.99
            )
        except Exception as e:
            self.logger.error(f"VaR calculation failed: {e}")
            risk_metrics['var_95'] = 0.0
            risk_metrics['var_99'] = 0.0
        
        # 回撤指标
        try:
            risk_metrics['max_drawdown'] = self.drawdown_model.calculate_max_drawdown(
                portfolio_data.get('returns', [])
            )
            risk_metrics['current_drawdown'] = self.drawdown_model.calculate_current_drawdown(
                portfolio_data.get('returns', [])
            )
        except Exception as e:
            self.logger.error(f"Drawdown calculation failed: {e}")
            risk_metrics['max_drawdown'] = 0.0
            risk_metrics['current_drawdown'] = 0.0
        
        # 波动率指标
        try:
            risk_metrics['volatility'] = self.volatility_model.calculate_portfolio_volatility(
                portfolio_data, market_data
            )
        except Exception as e:
            self.logger.error(f"Volatility calculation failed: {e}")
            risk_metrics['volatility'] = 0.0
        
        # 集中度指标
        try:
            concentration_metrics = self.concentration_model.calculate_concentration(
                portfolio_data.get('positions', {})
            )
            risk_metrics.update(concentration_metrics)
        except Exception as e:
            self.logger.error(f"Concentration calculation failed: {e}")
            risk_metrics.update({
                'max_weight': 0.0,
                'hhi': 0.0,
                'effective_assets': 0.0,
                'concentration_ratio': 0.0
            })
        
        return risk_metrics
