"""
Unified Risk Framework
A single, cohesive framework for all risk management activities, including
portfolio assessment, pre-trade control, and real-time monitoring.
"""
from __future__ import annotations

import asyncio
import logging
import numpy as np
import time
from collections import deque, defaultdict
from dataclasses import dataclass, field
from enum import Enum
import pandas as pd
from typing import Any, Dict, List, Optional, Callable

from .risk_models import (
    VaRModel,
    DrawdownModel,
    VolatilityModel,
    ConcentrationRiskModel
)

# region: Unified Data Models
# ==============================================================================
# Solve the data model inconsistency issue by defining a single source of truth.

class RiskLevel(str, Enum):
    """Unified risk level enum, using strings for clarity."""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"
    CRITICAL = "CRITICAL"

@dataclass
class RiskAlert:
    """Unified risk alert data structure."""
    risk_type: str
    risk_level: RiskLevel
    message: str
    details: Dict[str, Any] = field(default_factory=dict)

@dataclass
class PortfolioRiskAssessment:
    """Data structure for portfolio-level risk assessment results."""
    overall_score: float
    risk_level: RiskLevel
    individual_risks: Dict[str, float] # e.g., {'var': 0.8, 'drawdown': 0.6}
    alerts: List[RiskAlert] = field(default_factory=list)

@dataclass
class TradeDecision:
    """Data structure for trade execution decisions."""
    is_approved: bool
    position_size: float
    stop_loss_price: float
    take_profit_price: float
    reason: str
    alerts: List[RiskAlert] = field(default_factory=list)

@dataclass
class TradeRulesConfig:
    """Dataclasses for trade rule configurations."""
    min_confidence: float = 0.6
    max_daily_trades: int = 50
    min_time_between_trades_sec: int = 60
    volatility_threshold: float = 0.05
    static_stop_loss_pct: float = 0.02
    atr_multiplier_sl: float = 2.0
    max_stop_loss_pct: float = 0.05
    static_take_profit_pct: float = 0.04
    risk_reward_ratio: float = 2.0

@dataclass
class UnifiedRiskConfig:
    """A single configuration object for the entire framework."""
    portfolio_thresholds: Dict[str, Any] = field(default_factory=dict)
    trade_rules: TradeRulesConfig = field(default_factory=TradeRulesConfig)
    monitoring_interval: float = 5.0

# endregion

# region: Internal Framework Components
# ==============================================================================
# Define the internal components responsible for specific risk management tasks.

class PortfolioRiskEngine:
    """
    Component responsible for static, portfolio-level risk assessment.
    This absorbs the core calculation logic from the old RiskManager.
    """
    def __init__(self, config: UnifiedRiskConfig):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 1. Initialize risk models from the old RiskManager
        self.var_model = VaRModel()
        self.drawdown_model = DrawdownModel()
        self.volatility_model = VolatilityModel()
        self.concentration_model = ConcentrationRiskModel()
        
        # 2. Risk thresholds are now accessed directly from the unified config
        self.risk_thresholds = self.config.portfolio_thresholds

    def assess(self, portfolio_data: Dict[str, Any], market_data: Optional[pd.DataFrame] = None) -> PortfolioRiskAssessment:
        """
        Performs a full risk assessment of the portfolio.
        Migrated and adapted from the old RiskManager.
        """
        try:
            # A. Calculate all individual risk components
            var_result = self._assess_var_risk(portfolio_data, market_data)
            drawdown_result = self._assess_drawdown_risk(portfolio_data)
            volatility_result = self._assess_volatility_risk(portfolio_data, market_data)
            concentration_result = self._assess_concentration_risk(portfolio_data)
            leverage_result = self._assess_leverage_risk(portfolio_data)

            # B. Aggregate results
            individual_risks = {
                'var': var_result['risk_score'],
                'drawdown': drawdown_result['risk_score'],
                'volatility': volatility_result['risk_score'],
                'concentration': concentration_result['risk_score'],
                'leverage': leverage_result['risk_score'],
            }
            
            all_alerts = (
                var_result['alerts'] +
                drawdown_result['alerts'] +
                volatility_result['alerts'] +
                concentration_result['alerts'] +
                leverage_result['alerts']
            )

            # C. Calculate overall score and level
            overall_risk_score = self._calculate_overall_risk_score(individual_risks)
            risk_level = self._determine_risk_level(overall_risk_score)

            # D. Return the unified assessment object
            return PortfolioRiskAssessment(
                overall_score=overall_risk_score,
                risk_level=risk_level,
                individual_risks=individual_risks,
                alerts=all_alerts,
            )
        except Exception as e:
            self.logger.error(f"Portfolio risk assessment failed: {e}", exc_info=True)
            return PortfolioRiskAssessment(
                overall_score=1.0, # Return highest risk score on failure
                risk_level=RiskLevel.CRITICAL,
                individual_risks={},
                alerts=[RiskAlert(risk_type="assessment_failure", risk_level=RiskLevel.CRITICAL, message=str(e))]
            )

    def _calculate_overall_risk_score(self, individual_risks: Dict[str, float]) -> float:
        """Calculates the overall weighted risk score."""
        weights = self.risk_thresholds.get('weights', {
            'var': 0.25, 'drawdown': 0.20, 'volatility': 0.20,
            'concentration': 0.20, 'leverage': 0.15
        })
        
        weighted_score = sum(individual_risks.get(k, 0) * w for k, w in weights.items())
        total_weight = sum(w for k, w in weights.items() if k in individual_risks)
        
        return weighted_score / total_weight if total_weight > 0 else 0.0

    def _determine_risk_level(self, risk_score: float) -> RiskLevel:
        """Determines the risk level based on a score."""
        levels = self.risk_thresholds.get('levels', {'critical': 0.8, 'high': 0.6, 'medium': 0.4})
        if risk_score >= levels['critical']:
            return RiskLevel.CRITICAL
        elif risk_score >= levels['high']:
            return RiskLevel.HIGH
        elif risk_score >= levels['medium']:
            return RiskLevel.MEDIUM
        else:
            return RiskLevel.LOW

    def _assess_var_risk(self, portfolio_data: Dict[str, Any], market_data: Optional[pd.DataFrame]) -> Dict[str, Any]:
        """Assesses Value-at-Risk (VaR)."""
        alerts = []
        try:
            var_95 = self.var_model.calculate_var(portfolio_data, 0.95, 1, market_data)
            risk_score = min(abs(var_95) / self.risk_thresholds.get('var', {}).get('high', 0.10), 1.0)
            
            threshold = self.risk_thresholds.get('var', {}).get('high', 0.10)
            if abs(var_95) > threshold:
                alerts.append(RiskAlert(
                    risk_type='var', risk_level=self._determine_risk_level(risk_score),
                    message=f"High VaR detected: {var_95:.2%}",
                    details={'var_95': var_95, 'threshold': threshold}
                ))
            return {'risk_score': risk_score, 'alerts': alerts}
        except Exception as e:
            self.logger.warning(f"VaR assessment failed: {e}")
            return {'risk_score': 0.5, 'alerts': []}

    def _assess_drawdown_risk(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assesses drawdown risk."""
        alerts = []
        try:
            current_drawdown = self.drawdown_model.calculate_current_drawdown(portfolio_data.get('returns', []))
            risk_score = min(abs(current_drawdown) / self.risk_thresholds.get('drawdown', {}).get('high', 0.20), 1.0)
            
            threshold = self.risk_thresholds.get('drawdown', {}).get('high', 0.20)
            if abs(current_drawdown) > threshold:
                alerts.append(RiskAlert(
                    risk_type='drawdown', risk_level=self._determine_risk_level(risk_score),
                    message=f"High drawdown detected: {current_drawdown:.2%}",
                    details={'current_drawdown': current_drawdown, 'threshold': threshold}
                ))
            return {'risk_score': risk_score, 'alerts': alerts}
        except Exception as e:
            self.logger.warning(f"Drawdown assessment failed: {e}")
            return {'risk_score': 0.5, 'alerts': []}

    def _assess_volatility_risk(self, portfolio_data: Dict[str, Any], market_data: Optional[pd.DataFrame]) -> Dict[str, Any]:
        """Assesses volatility risk."""
        alerts = []
        try:
            volatility = self.volatility_model.calculate_portfolio_volatility(portfolio_data, market_data)
            risk_score = min(volatility / self.risk_thresholds.get('volatility', {}).get('high', 0.40), 1.0)
            
            threshold = self.risk_thresholds.get('volatility', {}).get('high', 0.40)
            if volatility > threshold:
                alerts.append(RiskAlert(
                    risk_type='volatility', risk_level=self._determine_risk_level(risk_score),
                    message=f"High volatility detected: {volatility:.2%}",
                    details={'volatility': volatility, 'threshold': threshold}
                ))
            return {'risk_score': risk_score, 'alerts': alerts}
        except Exception as e:
            self.logger.warning(f"Volatility assessment failed: {e}")
            return {'risk_score': 0.5, 'alerts': []}

    def _assess_concentration_risk(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assesses concentration risk."""
        alerts = []
        try:
            positions = portfolio_data.get('positions', {})
            position_values = {asset: info.get('quantity', 0) * info.get('current_price', 0) for asset, info in positions.items()}
            concentration = self.concentration_model.calculate_concentration(position_values)
            max_weight = concentration.get('max_weight', 0)
            risk_score = min(max_weight / self.risk_thresholds.get('concentration', {}).get('high', 0.60), 1.0)
            
            threshold = self.risk_thresholds.get('concentration', {}).get('high', 0.60)
            if max_weight > threshold:
                alerts.append(RiskAlert(
                    risk_type='concentration', risk_level=self._determine_risk_level(risk_score),
                    message=f"High concentration detected: {max_weight:.2%}",
                    details={'max_weight': max_weight, 'threshold': threshold}
                ))
            return {'risk_score': risk_score, 'alerts': alerts}
        except Exception as e:
            self.logger.warning(f"Concentration assessment failed: {e}")
            return {'risk_score': 0.5, 'alerts': []}

    def _assess_leverage_risk(self, portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assesses leverage risk."""
        alerts = []
        try:
            leverage = portfolio_data.get('leverage', 1.0)
            risk_score = min(leverage / self.risk_thresholds.get('leverage', {}).get('high', 10.0), 1.0)
            
            threshold = self.risk_thresholds.get('leverage', {}).get('high', 10.0)
            if leverage > threshold:
                alerts.append(RiskAlert(
                    risk_type='leverage', risk_level=self._determine_risk_level(risk_score),
                    message=f"High leverage detected: {leverage:.1f}x",
                    details={'leverage': leverage, 'threshold': threshold}
                ))
            return {'risk_score': risk_score, 'alerts': alerts}
        except Exception as e:
            self.logger.warning(f"Leverage assessment failed: {e}")
            return {'risk_score': 0.5, 'alerts': []}


class TradeExecutionGovernor:
    """
    Component for pre-trade risk control: signal filtering, position sizing, SL/TP.
    This absorbs the logic from the old AdvancedRiskManager. It is STATELESS.
    """
    def __init__(self, config: UnifiedRiskConfig):
        self.config = config
        self.rules = config.trade_rules # Direct access to the rules dataclass
        self.logger = logging.getLogger(__name__)

    async def decide_on_trade(self, signal: Dict[str, Any], context: Dict[str, Any]) -> TradeDecision:
        """
        Filters a trade signal and determines execution parameters based on provided context.
        """
        alerts = []
        symbol = signal.get('symbol')
        action = signal.get('signal_data', {}).get('action')
        confidence = signal.get('signal_data', {}).get('confidence', 0)
        
        # 1. Confidence Check
        if confidence < self.rules.min_confidence:
            return self._reject_trade(f"Signal confidence {confidence:.2f} is below threshold {self.rules.min_confidence}")

        # 2. Trade Frequency Check
        if not self._check_trade_frequency(symbol, context):
            return self._reject_trade(f"Trade frequency for {symbol} is too high.")

        # 3. Volatility Check
        volatility = await self._calculate_volatility(symbol, context)
        if volatility > self.rules.volatility_threshold:
            alerts.append(RiskAlert(
                risk_type='volatility', risk_level=RiskLevel.HIGH,
                message=f"High volatility {volatility:.2%} for {symbol}",
                details={'volatility': volatility}
            ))
            # High volatility is a warning, not a rejection, but might affect position size.

        # If all checks pass, calculate trade parameters
        try:
            position_size = self._calculate_position_size(signal, volatility, context)
            entry_price = context.get('price_history', {}).get(symbol, [0])[-1]
            
            if entry_price == 0:
                return self._reject_trade(f"Cannot determine entry price for {symbol}.")

            stop_loss = await self._calculate_stop_loss(signal, entry_price, volatility, context)
            take_profit = self._calculate_take_profit(signal, entry_price, stop_loss)

            return TradeDecision(
                is_approved=True,
                position_size=position_size,
                stop_loss_price=stop_loss,
                take_profit_price=take_profit,
                reason="Signal approved by TradeExecutionGovernor.",
                alerts=alerts
            )
        except Exception as e:
            self.logger.error(f"Failed to calculate trade parameters for {symbol}: {e}", exc_info=True)
            return self._reject_trade(f"Calculation error: {e}")

    def _reject_trade(self, reason: str) -> TradeDecision:
        """Helper to create a rejected trade decision."""
        self.logger.debug(f"Trade rejected: {reason}")
        return TradeDecision(
            is_approved=False, position_size=0, stop_loss_price=0,
            take_profit_price=0, reason=reason
        )
        
    def _check_trade_frequency(self, symbol: str, context: Dict[str, Any]) -> bool:
        """Checks trade frequency based on context."""
        current_time = time.time()
        today_str = time.strftime('%Y-%m-%d')
        daily_trades = context.get('daily_trades') or defaultdict(int)
        last_trade_times = context.get('last_trade_times') or defaultdict(float)

        if daily_trades[f"{symbol}_{today_str}"] >= self.rules.max_daily_trades:
            return False
        
        if current_time - last_trade_times.get(symbol, 0) < self.rules.min_time_between_trades_sec:
            return False
            
        return True

    async def _calculate_volatility(self, symbol: str, context: Dict[str, Any]) -> float:
        """Calculates historical volatility from context."""
        prices = list(context.get('price_history', {}).get(symbol, []))
        if len(prices) < 20: return 0.0 # Not enough data
        returns = np.diff(np.log(prices))
        return np.std(returns) * np.sqrt(365) # Annualized volatility

    def _calculate_position_size(self, signal: Dict[str, Any], volatility: float, context: Dict[str, Any]) -> float:
        """Calculates position size based on signal, context, and rules."""
        account_balance = context.get('account_balance', 0)
        confidence = signal.get('signal_data', {}).get('confidence', 0.5)
        
        base_ratio = 0.05 # Base size is 5% of account
        
        # Adjust for confidence
        conf_multiplier = 1.0 + (confidence - 0.5) # Scale from 0.5 to 1.5
        
        # Adjust for volatility (Kelly Criterion inspired)
        vol_multiplier = 0.01 / volatility if volatility > 0.01 else 1.0

        position_ratio = base_ratio * conf_multiplier * vol_multiplier
        position_ratio = min(max(position_ratio, 0.01), 0.2) # Clamp between 1% and 20%
        
        return account_balance * position_ratio

    async def _calculate_stop_loss(self, signal: Dict[str, Any], entry_price: float, volatility: float, context: Dict[str, Any]) -> float:
        """Calculates stop loss price."""
        action = signal.get('signal_data', {}).get('action')
        
        # Dynamic stop loss based on volatility (similar to ATR)
        stop_distance = entry_price * volatility * self.rules.atr_multiplier_sl
        
        # Clamp by max static stop loss
        max_stop_distance = entry_price * self.rules.max_stop_loss_pct
        stop_distance = min(stop_distance, max_stop_distance)

        return entry_price - stop_distance if action == 'BUY' else entry_price + stop_distance

    def _calculate_take_profit(self, signal: Dict[str, Any], entry_price: float, stop_loss: float) -> float:
        """Calculates take profit price based on risk-reward ratio."""
        action = signal.get('signal_data', {}).get('action')
        risk_per_share = abs(entry_price - stop_loss)
        reward_per_share = risk_per_share * self.rules.risk_reward_ratio
        
        return entry_price + reward_per_share if action == 'BUY' else entry_price - reward_per_share

class RealTimeMonitor:
    """
    Component for continuous, asynchronous monitoring and alerting.
    This absorbs the logic from the old RealTimeRiskMonitor, adapted for asyncio.
    """
    def __init__(
        self,
        config: UnifiedRiskConfig,
        risk_engine: PortfolioRiskEngine,
        get_portfolio_data_callback: Callable[[], Dict[str, Any]],
        alert_callback: Optional[Callable[[List[RiskAlert]], None]]
    ):
        """
        Initializes the monitor.

        Args:
            config: The unified risk configuration.
            risk_engine: The portfolio risk assessment engine.
            get_portfolio_data_callback: A callback to fetch the latest portfolio data.
            alert_callback: A callback to send alerts.
        """
        self.config = config
        self.risk_engine = risk_engine
        self.get_portfolio_data = get_portfolio_data_callback
        self.alert_callback = alert_callback
        self.is_running = False
        self._monitor_task: Optional[asyncio.Task] = None
        self.logger = logging.getLogger(__name__)

    async def _monitoring_loop(self):
        """The core asynchronous monitoring loop."""
        self.logger.info("Monitoring loop started.")
        while self.is_running:
            try:
                self.logger.debug("Real-time monitor checking portfolio risk...")
                # 1. Fetch the latest portfolio data using the callback
                portfolio_data = self.get_portfolio_data()

                if not portfolio_data:
                    self.logger.warning("No portfolio data available for monitoring.")
                    await asyncio.sleep(self.config.monitoring_interval)
                    continue

                # 2. Assess the risk using the injected engine
                assessment = self.risk_engine.assess(portfolio_data)
                self.logger.debug(f"Portfolio assessment completed. Risk level: {assessment.risk_level.value}")

                # 3. Trigger alerts if necessary
                if assessment.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL]:
                    if self.alert_callback and assessment.alerts:
                        self.logger.warning(f"High/Critical risk detected. Triggering {len(assessment.alerts)} alert(s).")
                        # Assuming the passed callback is async.
                        await self.alert_callback(assessment.alerts)

                # 4. Wait for the next interval
                await asyncio.sleep(self.config.monitoring_interval)

            except asyncio.CancelledError:
                self.logger.info("Monitoring loop was cancelled.")
                break
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}", exc_info=True)
                # Avoid fast failure loops; wait before retrying
                await asyncio.sleep(self.config.monitoring_interval * 2)

    def start(self):
        """Starts the monitoring loop in the background."""
        if not self.is_running:
            self.is_running = True
            self._monitor_task = asyncio.create_task(self._monitoring_loop())
            self.logger.info("RealTimeMonitor started.")

    async def stop(self):
        """Stops the monitoring loop gracefully."""
        if self.is_running and self._monitor_task:
            self.is_running = False
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                self.logger.info("Monitoring task successfully cancelled.")
            finally:
                self._monitor_task = None
                self.logger.info("RealTimeMonitor stopped.")

# endregion

# region: Main Framework Class
# ==============================================================================
# The main class that acts as a single entry point for all risk operations.

class UnifiedRiskFramework:
    """The single entry point for all risk management operations."""

    def __init__(self, config: UnifiedRiskConfig, alert_callback: Optional[Callable[[List[RiskAlert]], None]] = None):
        self.config = config
        self.logger = logging.getLogger(__name__)

        # 1. Centralized state management
        self.portfolio_state: Dict[str, Any] = {}
        self.trade_history: List[Dict[str, Any]] = []

        # 2. Instantiate internal components
        self.portfolio_engine = PortfolioRiskEngine(config)
        self.trade_governor = TradeExecutionGovernor(config)
        self.monitor = RealTimeMonitor(
            config=config,
            risk_engine=self.portfolio_engine,
            get_portfolio_data_callback=self.get_portfolio_state,
            alert_callback=alert_callback
        )

        self.logger.info("UnifiedRiskFramework initialized.")

    def assess_portfolio(self, portfolio_data: Dict[str, Any]) -> PortfolioRiskAssessment:
        """Public method to assess portfolio risk."""
        return self.portfolio_engine.assess(portfolio_data)

    async def process_trade_signal(self, signal: Dict[str, Any]) -> TradeDecision:
        """Public method to process a new trade signal."""
        # Provides the trade governor with the current portfolio context
        return await self.trade_governor.decide_on_trade(signal, self.portfolio_state)

    def start_monitoring(self):
        """Starts the real-time monitoring service."""
        self.monitor.start()

    async def stop_monitoring(self):
        """Stops the real-time monitoring service."""
        await self.monitor.stop()
        
    def get_portfolio_state(self) -> Dict[str, Any]:
        """Callback for the monitor to get the current portfolio state."""
        return self.portfolio_state

    def update_portfolio_state(self, state: Dict[str, Any]):
        """Updates the central portfolio state."""
        self.portfolio_state.update(state)

# endregion