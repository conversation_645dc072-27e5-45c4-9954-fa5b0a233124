"""
核心应用类
集成所有功能模块的统一应用接口
"""

import asyncio
import logging
import sys
import torch
import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, List
from pathlib import Path
import aiohttp
import aiofiles
from datetime import datetime
# 兼容不同PyTorch版本的GradScaler导入
try:
    from torch.amp import GradScaler
except ImportError:
    try:
        from torch.cuda.amp import GradScaler
    except ImportError:
        # 如果都导入失败，创建一个简单的替代类
        class GradScaler:
            def __init__(self, enabled=True):
                self.enabled = enabled
            def scale(self, loss):
                return loss
            def step(self, optimizer):
                optimizer.step()
            def update(self):
                pass
            def is_enabled(self):
                return self.enabled

from src.models.unified_fusion_model import UnifiedSignalFusionModel
from src.models.ensemble_model import EnsembleModel, AdaptiveEnsemble
from src.training import create_trainer, TrainerConfig, DistillationConfig
from src.api.async_deepseek_client import AsyncDeepSeekClient
from src.risk import UnifiedRiskFramework, UnifiedRiskConfig
from src.online_learning.online_learner import OnlineLearner
from src.visualization.training_monitor import TrainingMonitor
# from visualization.enhanced_training_monitor import get_enhanced_monitor
        # from src.visualization.enhanced_training_monitor import get_enhanced_monitor
from src.optimization.hyperparameter_optimizer import HyperparameterOptimizer

# from cache.multi_level_cache import get_cache, get_dataset_cache, get_model_cache
from src.utils.config import ConfigManager
from src.utils.logger import setup_logger

from src.utils.performance_optimizer import PerformanceOptimizer
from src.storage.mysql_manager import MySQLManager
from src.storage.redis_cache import RedisCache
from src.data.unified_pipeline.data_manager import DataManager
from src.data.unified_pipeline.feature_engine import FeatureEngine



class CryptoMLApplication:
    """
    加密货币机器学习应用核心类
    
    集成所有功能模块，提供统一的异步接口
    """
    
    def __init__(
        self,
        config: Dict[str, Any],
        device: torch.device,
        project_root: Path,
        scaler: Optional[GradScaler] = None,
    ):
        """
        Initializes the CryptoMLApplication.

        Args:
            config: The application's configuration dictionary.
            device: The torch.device to run computations on.
            project_root: The absolute path to the project's root directory.
            scaler: An optional GradScaler for mixed-precision training.
        """
        self.config = config
        self.device = device
        self.scaler = scaler
        self.project_root = project_root
        
        # 组件实例
        self.model = None
        self.trainer = None
        self.online_learner = None
        self.deepseek_client = None
        self.risk_manager = None
        self.monitor = None
        self.enhanced_monitor = None  # 增强监控器
        self.mysql_manager = None
        self.redis_cache = None
        self.performance_optimizer = None
        self.data_manager = None
        self.feature_engine = None
        
        # 状态管理
        self.is_initialized = False
        self.is_training = False
        self.is_monitoring = False
        self.feature_dim_synced = False
        
        self.logger = logging.getLogger(__name__)
        self.logger.error("DEBUG: CryptoMLApplication __init__ ENTERED")
        
        # 🔧 初始化浏览器清理机制
        # # initialize_browser_cleanup()

        self.logger.error("DEBUG: CryptoMLApplication __init__ EXITED")

    async def initialize(self):
        """Asynchronous initialization of application components."""
        if self.is_initialized:
            return

        # 修复：在应用初始化的最开始禁用CUDA同步调试模式
        if torch.cuda.is_available():
            try:
                torch.cuda.set_sync_debug_mode(0)  # 禁用CUDA同步调试模式
                self.logger.debug("✅ CUDA同步调试模式已禁用")
            except Exception as e:
                self.logger.warning(f"⚠️ 无法禁用CUDA同步调试模式: {e}")

        self.logger.info("Initializing CryptoML Application...")

        try:
            self.logger.error("DEBUG: INITIALIZE STEP 1: PERFORMANCE OPTIMIZER...")
            self.performance_optimizer = PerformanceOptimizer(self.config)
            self.logger.error("DEBUG: INITIALIZE STEP 1: COMPLETED")

            # 2. 初始化存储组件
            self.logger.error("DEBUG: INITIALIZE STEP 2: STORAGE...")
            self._initialize_storage()
            self.logger.error("DEBUG: INITIALIZE STEP 2: COMPLETED")

            # 3. 初始化数据处理组件 (Async)
            self.logger.error("DEBUG: INITIALIZE STEP 3: DATA COMPONENTS...")
            await self._initialize_data_components()
            self.logger.error("DEBUG: INITIALIZE STEP 3: COMPLETED")
            
            # 4. 初始化模型
            self.logger.error("DEBUG: INITIALIZE STEP 4: MODEL...")
            self._initialize_model()
            self.logger.error("DEBUG: INITIALIZE STEP 4: COMPLETED")

            # 5. 初始化API客户端
            self.logger.error("DEBUG: INITIALIZE STEP 5: API CLIENT...")
            self._initialize_api_client()
            self.logger.error("DEBUG: INITIALIZE STEP 5: COMPLETED")

            # 6. 🔧 修复：先初始化监控组件，再初始化训练组件
            self.logger.error("DEBUG: INITIALIZE STEP 6: MONITOR...")
            self._initialize_monitor()
            self.logger.error("DEBUG: INITIALIZE STEP 6: COMPLETED")

            # 7. 初始化训练组件（现在可以正确获取监控器）
            self.logger.error("DEBUG: INITIALIZE STEP 7: TRAINING COMPONENTS...")
            self._initialize_training_components()
            self.logger.error("DEBUG: INITIALIZE STEP 7: COMPLETED")

            # 8. 初始化风险管理
            self.logger.error("DEBUG: INITIALIZE STEP 8: RISK MANAGER...")
            self._initialize_risk_manager()
            self.logger.error("DEBUG: INITIALIZE STEP 8: COMPLETED")

            self.is_initialized = True
            self.logger.info("Application initialization completed")

        except Exception as e:
            self.logger.error(f"Failed to initialize application: {e}")
            raise
    
    def _initialize_storage(self):
        """初始化存储组件 (MySQL, Redis)"""
        db_config = self.config.get('database', {})

        # MySQL 管理器
        mysql_config = db_config.get('mysql', {})
        if mysql_config.get('enabled', False):
            try:
                # 只传递MySQLManager支持的参数
                supported_params = {
                    k: v for k, v in mysql_config.items()
                    if k in ['host', 'port', 'database', 'username', 'password', 'pool_size', 'max_overflow', 'pool_timeout', 'pool_recycle']
                }
                self.mysql_manager = MySQLManager(**supported_params)
                self.logger.info("✅ MySQL管理器初始化成功")
            except Exception as e:
                self.logger.warning(f"⚠️ MySQL初始化失败: {e}")
                self.mysql_manager = None
        else:
            self.mysql_manager = None
            self.logger.info("🚫 MySQL已禁用")

        # Redis 缓存
        redis_config = db_config.get('redis', {})
        if redis_config.get('enabled', False):
            try:
                # 只传递RedisCache支持的参数
                supported_params = {
                    k: v for k, v in redis_config.items()
                    if k in ['host', 'port', 'db', 'password', 'max_connections', 'socket_timeout', 'socket_connect_timeout', 'retry_on_timeout', 'health_check_interval']
                }
                self.redis_cache = RedisCache(**supported_params)
                self.logger.info("✅ Redis缓存初始化成功")
            except Exception as e:
                self.logger.warning(f"⚠️ Redis初始化失败: {e}")
                self.redis_cache = None
        else:
            self.redis_cache = None
            self.logger.info("🚫 Redis已禁用")


    def _initialize_model(self, use_ensemble: bool = True):
        """初始化优化的模型"""
        model_config = self.config.get('model', {})
        model_config['device'] = self.device

        # 自动计算特征维度（如果缺失）
        if 'feature_dim' not in model_config:
            self.logger.warning("模型配置中缺少 'feature_dim'，尝试自动计算...")
            try:
                # 尝试从顶层或'data'下的'feature_engineering'配置中获取信息
                feature_eng_config = self.config.get('feature_engineering', self.config.get('data', {}).get('feature_engineering', {}))

                tech_indicators = feature_eng_config.get('technical_indicators', [])
                window_sizes = feature_eng_config.get('window_sizes', [])
                
                if not tech_indicators or not window_sizes:
                    raise ValueError("无法计算特征维度：配置中 'technical_indicators' 或 'window_sizes' 缺失或为空。")

                # 这里的计算逻辑假设每个指标都应用于每个窗口大小
                feature_dim = len(tech_indicators) * len(window_sizes)
                
                model_config['feature_dim'] = feature_dim
                self.logger.info(f"✅ 'feature_dim' 自动计算并设置为: {feature_dim}")

            except Exception as e:
                self.logger.error(f"❌ 自动计算 'feature_dim' 失败: {e}", exc_info=True)
                # 重新引发原始错误，让用户知道需要手动配置
                raise ValueError("Missing required model parameter in configuration: 'model.feature_dim' and auto-calculation failed.") from e

        # 模型参数现在必须在配置文件中明确定义
        required_params = ['feature_dim', 'hidden_dims', 'num_heads', 'num_layers', 'dropout', 'output_dim']
        for param in required_params:
            if param not in model_config:
                raise ValueError(f"Missing required model parameter in configuration: 'model.{param}'")

        # 选择模型类型
        if use_ensemble:
            ensemble_config = {
                'n_models': 5,
                'ensemble_method': 'weighted_voting',
                'diversity_weight': 0.1
            }
            model_config.update(ensemble_config)
            self.model = EnsembleModel(**model_config)
            self.logger.info("🎯 使用集成学习模型 (5个基础模型)")
        else:
            self.model = UnifiedSignalFusionModel(**model_config)
            self.logger.info(f"🎯 使用单一融合模型 (特征维度: {model_config['feature_dim']})")

        self.model.to(self.device)



        # 修复：暂时禁用GPU利用率优化器，以解决启动死锁问题
        # try:
        #     from src.optimization.gpu_utilization_optimizer import create_gpu_utilization_optimizer
        #     self.gpu_utilization_optimizer = create_gpu_utilization_optimizer(target_utilization=0.85)
        #     self.logger.info("✅ GPU利用率优化器初始化完成")
        # except Exception as e:
        #     self.logger.warning(f"⚠️ GPU利用率优化器初始化失败: {e}")
        #     self.gpu_utilization_optimizer = None

        # 多级缓存系统 - 使用Redis缓存
        self.cache = self.redis_cache if self.redis_cache else None
        self.dataset_cache = self.redis_cache if self.redis_cache else None

        # 保存模型配置，用于动态调整
        self.model_config = model_config

    def update_model_feature_dim(self, feature_dim: int):
        """根据实际特征数量更新模型"""
        if self.model and hasattr(self.model, 'update_feature_dim'):
            # 如果模型支持动态更新，则调用它
            self.model.update_feature_dim(feature_dim)
            self.model_config['feature_dim'] = feature_dim # 确保配置同步
        elif feature_dim != self.model_config.get('feature_dim'):
            # 否则，回退到重新创建模型
            self.logger.info(f"🔄 Recreating model to update feature dimension: {self.model_config.get('feature_dim')} -> {feature_dim}")
            self.model_config['feature_dim'] = feature_dim
            self._initialize_model(use_ensemble='n_models' in self.model_config)
            self.model_cache = self.redis_cache if self.redis_cache else None
    
            # 实时风险监控器（暂时禁用）
            # self.real_time_risk_monitor = get_real_time_risk_monitor(self.risk_manager)
    
            # 模型优化
            if self.performance_optimizer:
                self.model = self.performance_optimizer.optimize_model_sync(self.model)
    
    
            self.logger.info(f"Model initialized with {sum(p.numel() for p in self.model.parameters())} parameters")

    def _initialize_api_client(self):
        """初始化API客户端"""
        # 检查DeepSeek是否启用 - 修复配置路径
        deepseek_config = self.config.get('deepseek', {})
        deepseek_enabled = deepseek_config.get('enabled', True)

        self.logger.debug(f"🔧 DeepSeek配置: {deepseek_config}")
        self.logger.debug(f"🔧 DeepSeek启用状态: {deepseek_enabled}")

        # 修改条件：只要DeepSeek启用就初始化客户端
        if deepseek_enabled:
            # 创建配置副本并移除不支持的参数
            client_config = deepseek_config.copy()
            client_config.pop('enabled', None)  # 移除enabled参数
            client_config.pop('max_workers', None)  # 移除不支持的参数
            client_config.pop('model', None)  # 移除model参数（不支持）
            client_config.pop('distillation', None)  # 移除蒸馏配置
            client_config.pop('retry_delay', None)  # 移除retry_delay参数（不支持）
            client_config.pop('backoff_factor', None)  # 移除backoff_factor参数（不支持）
            client_config.pop('max_concurrent_requests', None)  # 移除不支持的参数
            client_config.pop('rate_limit', None)  # 移除复杂的rate_limit配置
            client_config.pop('batch_size', None)  # 移除batch相关参数
            client_config.pop('batch_timeout', None)
            client_config.pop('enable_batching', None)
            client_config.pop('cache', None)  # 移除复杂的cache配置
            client_config.pop('parameters', None)  # 移除parameters配置

            self.deepseek_client = AsyncDeepSeekClient(
                **client_config,
                redis_cache=self.redis_cache
            )
            # 延迟初始化DeepSeek客户端 - 在实际使用时再初始化
            self.logger.info("✅ DeepSeek API客户端已创建，将在首次使用时初始化")
        else:
            self.deepseek_client = None
            self.logger.info("🚫 DeepSeek API已禁用")

    def _initialize_training_components(self):
        self.logger.info("🔧 Initializing unified training components...")

        # create_trainer 工厂封装了所有选择和配置正确训练器的逻辑
        # (基础, 蒸馏, Epoch等)，基于应用的配置
        self.trainer = create_trainer(
            model=self.model,
            train_config=self.config.get('training', {}), # 修复：传递正确的配置
            device=self.device,
            monitor=self.monitor,  # 注入监控器实例
        )

        self.logger.info(f"✅ Unified trainer initialized: {type(self.trainer).__name__}")
        if hasattr(self.trainer, 'optimizer') and self.trainer.optimizer:
            self.logger.info(f"   Optimizer: {type(self.trainer.optimizer).__name__}")
            self.logger.info(f"   Learning Rate: {self.trainer.optimizer.param_groups[0]['lr']}")
        else:
            self.logger.warning("   Trainer does not have an optimizer configured.")

        # 设置主应用引用，用于中断检查
        if hasattr(self.trainer, 'set_main_app'):
            self.trainer.set_main_app(self)

        # 确认监控器连接
        if hasattr(self.trainer, 'monitor') and self.trainer.monitor:
            self.logger.info(f"🔧 Trainer is connected to monitor: {type(self.trainer.monitor).__name__}")
        else:
            self.logger.warning("⚠️ Trainer is NOT connected to a monitor.")

        # 在线学习器
        if self.config.get('online_learning', {}).get('enabled', False):
            online_config = self.config.get('online_learning', {}).copy()
            # 移除OnlineLearner不支持的参数
            online_config.pop('enabled', None)
            self.online_learner = OnlineLearner(
                model=self.model,
                config=online_config
            )
            self.logger.info("✅ OnlineLearner initialized.")

        # 增强监控器
        try:
            from src.visualization.enhanced_training_monitor import get_enhanced_monitor
            self.enhanced_monitor = get_enhanced_monitor()
            self.logger.info("✅ Enhanced Training Monitor initialized.")
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Enhanced Training Monitor: {e}")
            self.enhanced_monitor = None

    def _initialize_risk_manager(self):
        """初始化风险管理"""
        risk_config_dict = self.config.get('risk_management', {})
        if risk_config_dict.get('enabled', False):
            # Create the config dataclass from the dictionary
            from src.risk.unified_framework import TradeRulesConfig
            trade_rules_dict = risk_config_dict.get('trade_rules', {})
            risk_config = UnifiedRiskConfig(
                portfolio_thresholds=risk_config_dict.get('portfolio_thresholds', {}),
                trade_rules=TradeRulesConfig(**trade_rules_dict),
                monitoring_interval=risk_config_dict.get('monitoring_interval', 5.0)
            )
            self.risk_manager = UnifiedRiskFramework(
                config=risk_config,
                # Example of an alert callback
                alert_callback=lambda alerts: self.logger.warning(f"RISK ALERTS: {alerts}")
            )
            self.logger.info("✅ UnifiedRiskFramework initialized.")
            # Auto-start monitoring if configured
            # if risk_config_dict.get('start_monitoring_on_init', True):
            #     self.risk_manager.start_monitoring()
            #     self.logger.info("✅ Real-time risk monitoring started by default.")

    def _initialize_monitor(self):
        """初始化监控组件"""
        monitor_config = self.config.get('monitoring', {}).copy()

        # 只保留TrainingMonitor支持的参数
        supported_params = {
            'save_dir': monitor_config.get('save_dir'),
            'max_history': monitor_config.get('max_history', 10000),
            'update_interval': monitor_config.get('update_interval', 50),
            'enable_live_plot': monitor_config.get('enable_live_plot', True)
        }

        self.monitor = TrainingMonitor(**supported_params)
    
    async def run_training(
        self,
        data_path: Optional[str] = None,
        model_path: Optional[str] = None,
        output_path: Optional[str] = None,
        **kwargs
    ):
        """
        运行端到端训练流程。

        此方法将所有训练逻辑委托给 TrainingPipeline 类，自身只负责协调。
        """
        print("DEBUG: Entered CryptoMLApplication.run_training", file=sys.stderr)
        sys.stderr.flush()
        self.logger.info("DEBUG: Attempting to import TrainingPipeline...")
        print("DEBUG: Attempting to import TrainingPipeline...", file=sys.stderr)
        sys.stderr.flush()
        from src.training.pipeline import TrainingPipeline # 延迟导入以避免循环依赖
        self.logger.info("DEBUG: Successfully imported TrainingPipeline.")
        print("DEBUG: Successfully imported TrainingPipeline.", file=sys.stderr)
        sys.stderr.flush()
        
        self.logger.info("🚀 Delegating training process to TrainingPipeline...")
        print("DEBUG: Delegating training process to TrainingPipeline...", file=sys.stderr)
        sys.stderr.flush()

        try:
            print("DEBUG: Instantiating TrainingPipeline...", file=sys.stderr)
            sys.stderr.flush()
            pipeline = TrainingPipeline(self)
            print("DEBUG: TrainingPipeline instantiated.", file=sys.stderr)
            sys.stderr.flush()

            print("DEBUG: Calling pipeline.run()...", file=sys.stderr)
            sys.stderr.flush()
            training_results = await pipeline.run(
                data_path=data_path,
                model_path=model_path,
                output_path=output_path,
                **kwargs,
            )
            self.logger.info("✅ TrainingPipeline completed successfully.")
            return training_results
        except Exception as e:
            self.logger.error(f"❌ An error occurred during the training pipeline execution: {e}", exc_info=True)
            # Also print to stderr in case logging fails
            import traceback
            print(f"❌ ERROR in run_training: {e}", file=sys.stderr)
            traceback.print_exc(file=sys.stderr)
            sys.stderr.flush()
            raise

    async def _start_online_learning_and_signals(self, model_path: str, symbol: str):
        """启动在线学习和交易信号发送"""
        try:
            self.logger.info("🔧 初始化在线学习和信号发送系统...")

            # 导入必要的模块
            from src.online_learning.online_learner import OnlineLearner
            from src.strategy.signal_sender import TradingSignalSender, SignalConfig

            # 初始化在线学习器
            online_learner = OnlineLearner(
                model_path=model_path,
                update_frequency=300,  # 5分钟更新一次
                drift_detection_window=1000,
                retrain_threshold=0.1
            )

            # 初始化信号发送器
            signal_config = SignalConfig(
                confidence_threshold=0.7,
                signal_interval=60,  # 1分钟间隔
                max_signals_per_hour=10,
                symbols=[symbol, 'ETHUSDT', 'BNBUSDT'],
                enable_kafka=True,
                enable_redis=True,
                enable_file_output=True
            )

            signal_sender = TradingSignalSender(model_path, signal_config)

            # 初始化组件
            await online_learner.initialize()
            await signal_sender.initialize()

            self.logger.info("✅ 在线学习和信号发送系统初始化完成")

            # 启动在线学习循环（后台任务）
            online_learning_task = asyncio.create_task(
                online_learner.start_online_learning()
            )

            # 启动信号发送循环（后台任务）
            signal_sending_task = asyncio.create_task(
                signal_sender.start_signal_loop()
            )

            self.logger.info("🚀 在线学习和信号发送已启动，系统进入持续运行模式...")
            self.logger.info("📊 实时监控: http://localhost:8888")
            self.logger.info("📈 信号输出: signals/ 目录")
            self.logger.info("🔄 在线学习: 每5分钟检查一次")
            self.logger.info("📤 交易信号: 每1分钟生成一次")

            # 等待任务完成（实际上会一直运行）
            try:
                await asyncio.gather(online_learning_task, signal_sending_task)
            except KeyboardInterrupt:
                self.logger.info("⏹️ 收到中断信号，停止在线学习和信号发送...")
                online_learner.stop()
                signal_sender.stop()

                # 等待任务清理
                await asyncio.sleep(2)

                self.logger.info("✅ 在线学习和信号发送已停止")

        except Exception as e:
            self.logger.error(f"❌ 在线学习和信号发送启动失败: {e}")
            import traceback
            traceback.print_exc()

    async def setup_inference_service(self, model_path: Optional[str] = None):
        """
        Prepares the application for high-performance inference ("cold start").
        This method should be called once when the service starts.
        """
        self.logger.info("🚀 Setting up inference service...")
        if not self.is_initialized:
            self.initialize()

        # Load a default or specified model.
        # The logic prioritizes: 1. provided path, 2. config path, 3. existing model.
        effective_model_path = model_path or self.config.get('inference', {}).get('default_model_path')

        if effective_model_path:
            await self._load_model(effective_model_path)
        else:
            if self.model is None:
                self.logger.error("❌ No model is loaded and no default model path is configured.")
                raise RuntimeError("Cannot setup inference service without a model.")
            self.logger.info("✅ Using already loaded model for inference.")

        # Pre-warm the model to avoid latency on the first request.
        try:
            self.logger.info("🔥 Pre-warming model with a dummy batch...")
            dummy_input_dim = self.model_config.get('feature_dim', 60)
            dummy_batch_size = 8
            # The model internally handles adding the sequence dimension if needed.
            dummy_input = torch.randn(dummy_batch_size, dummy_input_dim, device=self.device)
            await self._predict_batch(dummy_input)
            self.logger.info("✅ Model pre-warmed successfully.")
        except Exception as e:
            self.logger.error(f"❌ Failed to pre-warm model: {e}", exc_info=True)

        self.logger.info("✅ Inference service is ready.")
    
    async def run_prediction(
        self,
        features: Any,
        output_path: Optional[str] = None,
    ) -> Dict[str, Any]:
        """
        Runs a high-performance prediction on the given features (the "hot path").
        Assumes the inference service has been set up via `setup_inference_service`.

        This method now ensures that raw model outputs (logits) are converted
        to probabilities using a softmax function before being returned, providing
        meaningful, interpretable confidence scores.

        Args:
            features: The input data for prediction. Can be a pandas DataFrame,
                      NumPy array, or a PyTorch Tensor.
            output_path: Optional path to save the prediction results.

        Returns:
            A dictionary containing the prediction results, including class
            probabilities (`probs`) and the final predicted class (`predictions`).
        """
        if not self.is_initialized or self.model is None:
            raise RuntimeError(
                "Inference service is not ready. Call `setup_inference_service()` before running predictions."
            )

        # The core logic now includes applying softmax to get probabilities
        self.logger.debug("Executing prediction batch...")
        raw_output = await self._predict_batch(features)
        
        # The model might return a dictionary. Extract the actual logits tensor.
        if isinstance(raw_output, dict):
            if 'logits' in raw_output:
                logits = raw_output['logits']
            elif 'signal' in raw_output:
                logits = raw_output['signal']
            else:
                # If no specific key is found, and it's a single-item dict, use that value.
                if len(raw_output) == 1:
                    logits = next(iter(raw_output.values()))
                else:
                    raise ValueError(f"Model returned a dictionary but could not find 'logits' or 'signal' key. Keys: {raw_output.keys()}")
        else:
            logits = raw_output
        
        # Convert logits to probabilities. Ensure it's a tensor.
        if not isinstance(logits, torch.Tensor):
            logits = torch.tensor(logits, device=self.device)

        probs = torch.nn.functional.softmax(logits, dim=-1)
        
        # Get the predicted class (the one with the highest probability)
        predictions = torch.argmax(probs, dim=-1)
        
        # Prepare the results dictionary
        results = {
            'logits': logits.tolist(),
            'probabilities': probs.tolist(),
            'predictions': predictions.tolist()
        }
        self.logger.debug("Prediction batch executed and probabilities calculated.")

        # Save results if path is provided
        if output_path:
            self.logger.info(f"💾 Saving prediction results to: {output_path}")
            await self._save_predictions(output_path, results)
            self.logger.info("✅ Prediction results saved successfully.")

        return results
    
    async def run_risk_assessment(
        self,
        data_path: Optional[str] = None,
        output_path: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """运行风险评估"""
        if not self.is_initialized:
            await self.initialize()
        
        if not self.risk_manager:
            raise RuntimeError("Risk manager not initialized")
        
        self.logger.info("Starting risk assessment...")
        
        # 加载投资组合数据
        portfolio_data = await self._load_portfolio_data(data_path)
        
        # 执行风险评估
        risk_assessment = await self.risk_manager.assess_portfolio_risk_async(
            portfolio_data=portfolio_data,
            **kwargs
        )
        
        # 保存结果
        if output_path:
            await self._save_risk_assessment(output_path, risk_assessment)
        
        self.logger.info("Risk assessment completed successfully")
        return risk_assessment
    
    async def run_online_learning(
        self,
        data_path: Optional[str] = None,
        model_path: Optional[str] = None,
        **kwargs
    ):
        """运行在线学习"""
        if not self.is_initialized:
            await self.initialize()
        
        if not self.online_learner:
            raise RuntimeError("Online learner not initialized")
        
        self.logger.info("Starting online learning...")

        # 加载初始模型
        if model_path:
            await self._load_model(model_path)
            # 更新在线学习器的模型引用
            self.online_learner.model = self.model
            self.logger.info("✅ 在线学习器模型引用已更新")

        # 启动在线学习流程
        self.logger.info("🔄 准备启动在线学习流程...")
        try:
            result = await self.online_learner.start_online_learning_async(
                data_source=data_path,
                monitor=self.monitor,
                **kwargs
            )
            self.logger.info(f"✅ 在线学习流程完成: {result}")
        except Exception as e:
            self.logger.error(f"❌ 在线学习流程失败: {e}")
            import traceback
            self.logger.error(f"❌ 错误详情: {traceback.format_exc()}")
    
    async def start_monitoring(self):
        """启动监控服务"""
        if not self.is_initialized:
            await self.initialize()
        
        if not self.monitor:
            raise RuntimeError("Monitor not initialized")
        
        self.is_monitoring = True
        self.monitor.start_monitoring()
        self.logger.info("Monitoring service started")
    
    
    async def cleanup(self):
        """清理资源"""
        self.logger.info("Cleaning up application resources...")

        # 停止监控
        if self.monitor and self.is_monitoring:
            self.monitor.stop_monitoring()

        # 停止增强监控器
        if hasattr(self, 'enhanced_monitor') and self.enhanced_monitor:
            try:
                self.enhanced_monitor.stop_monitoring()
                self.logger.info("✅ Enhanced monitor stopped")
            except Exception as e:
                self.logger.warning(f"⚠️ Enhanced monitor cleanup failed: {e}")

        # 关闭API客户端
        if self.deepseek_client:
            await self.deepseek_client.close()
        
        # 关闭存储连接
        if self.mysql_manager:
            try:
                if hasattr(self.mysql_manager, 'close'):
                    if asyncio.iscoroutinefunction(self.mysql_manager.close):
                        await self.mysql_manager.close()
                    else:
                        self.mysql_manager.close()
            except Exception as e:
                self.logger.warning(f"MySQL关闭时出现错误: {e}")

        if self.redis_cache:
            try:
                if hasattr(self.redis_cache, 'close'):
                    if asyncio.iscoroutinefunction(self.redis_cache.close):
                        await self.redis_cache.close()
                    else:
                        self.redis_cache.close()
            except Exception as e:
                self.logger.warning(f"Redis关闭时出现错误: {e}")
        
        # 清理性能优化器
        if self.performance_optimizer:
            await self.performance_optimizer.cleanup()
        
        self.logger.info("Application cleanup completed")

    async def _initialize_data_components(self):
        """Initializes data-related components, primarily the DataManager."""
        self.logger.info("🔧 Initializing data components...")
        try:
            # Pass the project root to the DataManager
            self.data_manager = DataManager(
                config=self.config.get('data', {}),
                project_root=self.project_root
            )
            await self.data_manager.initialize_connections()
            self.logger.info("✅ Unified DataManager initialized and connected successfully.")

            # Initialize FeatureEngine after DataManager
            feature_config = self.config.get('feature_engineering', {})
            self.feature_engine = FeatureEngine(feature_config)
            self.logger.info("✅ FeatureEngine initialized successfully.")

        except Exception as e:
            self.logger.error(f"❌ Failed to initialize DataManager: {e}", exc_info=True)
            self.data_manager = None


    async def _load_prediction_data(self, data_path: Optional[str] = None) -> Dict[str, Any]:
        """
        加载预测数据，如果没有提供路径，则自动查找或生成。
        此方法现在使用统一的 DataManager。
        """
        if self.data_manager is None:
            raise RuntimeError("DataManager is not initialized. Cannot load prediction data.")
        
        processed_dir = self.data_manager.processed_dir

        # 2. 如果没有提供路径，则自动查找最新的数据集
        if not data_path:
            self.logger.info("No data path provided, searching for the latest unified dataset...")
            search_pattern = "unified_dataset_*.parquet"
            
            if processed_dir.exists():
                found_files = list(processed_dir.glob(search_pattern))
                if found_files:
                    latest_file = max(found_files, key=lambda p: p.stat().st_mtime)
                    data_path = str(latest_file)
                    self.logger.info(f"🎯 Automatically selected latest dataset for prediction: {data_path}")

        # 3. 如果没有找到数据文件，自动生成数据集
        if not data_path or not Path(data_path).exists():
            self.logger.warning("No usable prediction dataset found, attempting to generate automatically...")
            try:
                gen_config = self.config.get('data', {}).get('auto_generation', {})
                symbols = gen_config.get('symbols', ['BTCUSDT'])
                days = gen_config.get('prediction_days', 30)
                
                self.logger.info(f"🚀 Generating prediction data for symbols {symbols} for the last {days} days...")
                
                # 使用 DataManager 创建数据集
                generated_path = await self.data_manager.create_unified_dataset(
                    symbols=symbols,
                    days=days,
                    force_refresh=False
                )

                if generated_path:
                    data_path = str(generated_path)
                    self.logger.info(f"✅ Dataset successfully generated: {data_path}")
                else:
                    raise RuntimeError("DataManager failed to generate a dataset.")

            except Exception as e:
                self.logger.error(f"❌ Critical error during automatic dataset generation: {e}", exc_info=True)
                raise ValueError("Could not load or generate prediction data. Check data sources or 'data.auto_generation' settings.")

        # 4. 从最终确定的路径加载并处理数据
        self.logger.info(f"⏳ Loading prediction data from: {data_path}")
        try:
            df = pd.read_parquet(data_path)
            
            # 从 DataManager 的内部逻辑中借鉴，以提取特征
            numeric_cols = df.select_dtypes(include=np.number).columns
            feature_columns = numeric_cols.drop('target') if 'target' in numeric_cols else numeric_cols
            
            features = df[feature_columns.tolist()].values
            
            # 返回与旧方法兼容的字典格式
            processed_data = {'features': features, 'dataframe': df}
            
            self.logger.info(f"✅ Data loaded and processed from {data_path}, features shape: {features.shape}")
            return processed_data

        except Exception as e:
            self.logger.error(f"❌ Failed to load and process the final Parquet file {data_path}: {e}", exc_info=True)
            raise


    async def _predict_batch(self, data: Any) -> torch.Tensor:
        """
        Performs a forward pass on the model and returns the raw logits.

        Args:
            data: Input data, which can be a dict, Tensor, or NumPy array.

        Returns:
            A torch.Tensor containing the raw output logits from the model.
        """
        self.model.eval()

        with torch.no_grad():
            features = data.get('features') if isinstance(data, dict) else data

            if not isinstance(features, torch.Tensor):
                features = torch.from_numpy(features).float()

            features = features.to(self.device)
            if features.dim() == 2:
                features = features.unsqueeze(1)

            # Optimized batching logic
            batch_size = features.shape[0]
            max_batch_size = self._get_safe_batch_size()

            if batch_size > max_batch_size:
                all_logits = []
                for batch_features in torch.split(features, max_batch_size):
                    try:
                        output = self.model(batch_features)
                        # Standardize access to logits
                        logits = output.get('logits', output) if isinstance(output, dict) else output
                        all_logits.append(logits)
                    except RuntimeError as e:
                        self.logger.error(f"Error during batched prediction: {e}")
                        # Simple re-raise, could add more sophisticated retry later
                        raise
                return torch.cat(all_logits)
            else:
                output = self.model(features)
                # Standardize access to logits
                logits = output.get('logits', output) if isinstance(output, dict) else output
                return logits

    def _get_safe_batch_size(self):
        """获取安全的批次大小"""
        if not torch.cuda.is_available():
            return 1024  # CPU模式使用较大批次

        # 根据GPU内存动态计算安全批次大小
        try:
            total_memory = torch.cuda.get_device_properties(self.device).total_memory
            allocated_memory = torch.cuda.memory_allocated(self.device)
            free_memory = total_memory - allocated_memory

            # 🔥 优化：使用实际特征维度和更激进的内存使用策略
            # 估算每个样本需要的内存 (特征维度 * 序列长度 * 4字节 * 安全系数)
            feature_dim = getattr(self, 'model_config', {}).get('feature_dim', 60)  # 使用实际特征维度
            seq_len = 1
            bytes_per_sample = feature_dim * seq_len * 4 * 3  # 🔥 减少安全系数到3倍

            # 🔥 优化：使用更多的可用内存，提高GPU利用率
            memory_usage_ratio = 0.75  # 使用75%的可用内存
            max_batch_size = 16384  # 🔥 大幅提高最大批次限制以充分利用RTX 2060

            safe_batch_size = min(
                int(free_memory * memory_usage_ratio / bytes_per_sample),
                max_batch_size
            )

            # 🔥 优化：提高最小批次大小以充分利用GPU
            safe_batch_size = max(safe_batch_size, 128)  # 最小批次大小提高到128

            self.logger.info(f"🎯 GPU优化批次大小: {safe_batch_size} (可用内存: {free_memory/(1024**3):.2f}GB, 特征维度: {feature_dim})")
            return safe_batch_size

        except Exception as e:
            self.logger.warning(f"⚠️ 无法计算安全批次大小: {e}，使用默认值")
            return 256  # 🔥 提高保守的默认值

    async def _save_predictions(self, output_path: str, predictions: Dict[str, Any]):
        """保存预测结果"""
        import json
        from pathlib import Path

        output_file = Path(output_path)
        output_file.parent.mkdir(parents=True, exist_ok=True)

        with open(output_file, 'w') as f:
            json.dump(predictions, f, indent=2)

        self.logger.info(f"Predictions saved to: {output_path}")

    async def _load_portfolio_data(self, data_path: Optional[str] = None):
        """加载投资组合数据"""
        if not data_path:
            data_path = self.config.get('data', {}).get('portfolio_data_path')

        if not data_path:
            raise ValueError("Portfolio data path must be provided either as an argument or in the configuration under data.portfolio_data_path")

        # 简化的投资组合数据加载
        import pandas as pd
        import numpy as np

        if Path(data_path).exists():
            df = pd.read_csv(data_path)
            # 模拟投资组合数据
            portfolio_data = {
                'positions': np.random.rand(10),
                'weights': np.random.rand(10),
                'returns': df.iloc[:, -1].values if len(df.columns) > 0 else np.random.rand(100)
            }
        else:
            # 生成模拟数据
            portfolio_data = {
                'positions': np.random.rand(10),
                'weights': np.random.rand(10),
                'returns': np.random.rand(100)
            }

        return portfolio_data

    async def _load_model(self, model_path: str):
        """
        Loads a model's state_dict from a specified path with robust error handling.

        This method enforces a strict, standardized loading process:
        1. It expects a single, standard checkpoint format: a dictionary containing
           the 'model_state_dict'.
        2. It enforces `strict=True` loading to ensure the model architecture
           and the state dictionary are perfectly aligned.
        3. Any deviation from this standard will result in a clear, immediate exception,
           preventing silent failures from partially loaded or incompatible models.

        Args:
            model_path: The file path to the model checkpoint.

        Raises:
            FileNotFoundError: If the model file does not exist.
            ValueError: If the checkpoint file is corrupted, in an invalid format,
                        or if the state_dict does not match the model architecture.
        """
        model_file = Path(model_path)
        if not model_file.is_file():
            self.logger.error(f"❌ Model file not found at path: {model_path}")
            raise FileNotFoundError(f"Model file not found: {model_path}")

        self.logger.info(f"⏳ Attempting to load model from: {model_path}")

        try:
            # Load the checkpoint from disk. Use map_location to ensure it loads
            # correctly onto the configured device.
            checkpoint = torch.load(model_path, map_location=self.device)

            # --- Validation Step 1: Ensure checkpoint is a dictionary ---
            if not isinstance(checkpoint, dict):
                raise ValueError("Checkpoint is not a dictionary. Supported format: {'model_state_dict': ...}")

            # --- Validation Step 2: Ensure 'model_state_dict' key exists ---
            if 'model_state_dict' not in checkpoint:
                # For backward compatibility, check for 'state_dict' as a fallback
                if 'state_dict' in checkpoint:
                    state_dict = checkpoint['state_dict']
                    self.logger.warning("⚠️ Found 'state_dict' key, proceeding. Please migrate to 'model_state_dict'.")
                else:
                    raise ValueError("Checkpoint dictionary missing required key: 'model_state_dict'")
            else:
                state_dict = checkpoint['model_state_dict']

            # --- Loading Step: Load with strict enforcement ---
            # `strict=True` is the default and safest mode. It ensures that the keys
            # in state_dict match the keys returned by this module’s state_dict() function.
            missing_keys, unexpected_keys = self.model.load_state_dict(state_dict, strict=True)

            # Although strict=True will raise an error, this is for logging just in case behavior changes.
            if missing_keys:
                 self.logger.warning(f"Mismatched keys during loading: {len(missing_keys)} keys missing from state_dict.")
            if unexpected_keys:
                 self.logger.warning(f"Mismatched keys during loading: {len(unexpected_keys)} keys unexpected in state_dict.")


            self.model.to(self.device)
            self.logger.info(f"✅ Model loaded successfully from {model_path} and moved to {self.device}")

            # --- Verification Step: Log memory usage on GPU ---
            if self.device.type == 'cuda':
                allocated = torch.cuda.memory_allocated(self.device) / 1024**2
                reserved = torch.cuda.memory_reserved(self.device) / 1024**2
                self.logger.info(f"GPU Memory after load: {allocated:.2f}MB Allocated / {reserved:.2f}MB Reserved")

        except FileNotFoundError:
            # Re-raise the specific error for clarity
            raise
        except Exception as e:
            self.logger.error(f"❌ A critical error occurred while loading the model from {model_path}: {e}", exc_info=True)
            # Wrap the original exception in a new, more informative error.
            raise ValueError(f"Failed to load a valid model from {model_path}. Please check file integrity and format.") from e

    async def _create_fallback_model(self, original_path: str):
        """创建备用模型（当原模型损坏时）"""
        self.logger.warning("🔧 创建备用模型...")

        try:
            import time
            # 备份损坏的文件
            backup_format = self.config.get('training', {}).get('corrupted_backup_format', '{original_path}.corrupted.{timestamp}')
            backup_path = backup_format.format(original_path=original_path, timestamp=int(time.time()))
            if Path(original_path).exists():
                import shutil
                shutil.move(original_path, backup_path)
                self.logger.info(f"📦 损坏的模型已备份到: {backup_path}")

            # 创建新的模型实例（使用当前配置）
            if not self.model:
                self._initialize_model()

            # 保存新模型到原路径
            await self._save_model(original_path)
            self.logger.info(f"✅ 新模型已保存到: {original_path}")

        except Exception as e:
            self.logger.error(f"❌ 创建备用模型失败: {e}")
            raise






    async def run_monitoring(
        self,
        symbols: Optional[List[str]] = None,
        monitoring_interval: int = 60,
        enable_risk_monitoring: bool = True,
        enable_performance_monitoring: bool = True,
        enable_real_time_prediction: bool = True
    ) -> Dict[str, Any]:
        """
        运行实时监控模式

        Args:
            symbols: 监控的交易对列表
            monitoring_interval: 监控间隔（秒）
            enable_risk_monitoring: 启用风险监控
            enable_performance_monitoring: 启用性能监控
            enable_real_time_prediction: 启用实时预测

        Returns:
            监控结果字典
        """
        try:
            self.logger.info("🔍 启动实时监控模式...")

            # 设置默认监控交易对
            if symbols is None:
                symbols = self.config.get('trading', {}).get('symbols', ['BTCUSDT', 'ETHUSDT'])

            # 初始化监控组件
            monitoring_results = {
                'status': 'running',
                'symbols': symbols,
                'start_time': datetime.now(),
                'monitoring_stats': {},
                'risk_alerts': [],
                'performance_metrics': {},
                'predictions': {}
            }

            # 启动风险监控
            if enable_risk_monitoring and hasattr(self, 'risk_manager'):
                self.logger.info("🛡️ 启动风险监控...")
                try:
                    # 启动实时风险监控
                    if hasattr(self.risk_manager, 'start_real_time_monitoring'):
                        await self.risk_manager.start_real_time_monitoring()
                    monitoring_results['risk_monitoring'] = 'active'
                except Exception as e:
                    self.logger.warning(f"⚠️ 风险监控启动失败: {e}")
                    monitoring_results['risk_monitoring'] = 'failed'

            # 启动性能监控
            if enable_performance_monitoring:
                self.logger.info("📊 启动性能监控...")
                try:
                    # 启动训练监控器
                    if hasattr(self, 'training_monitor'):
                        await self.training_monitor.start_monitoring()
                    monitoring_results['performance_monitoring'] = 'active'
                except Exception as e:
                    self.logger.warning(f"⚠️ 性能监控启动失败: {e}")
                    monitoring_results['performance_monitoring'] = 'failed'

            # 启动实时预测
            if enable_real_time_prediction:
                self.logger.info("🤖 启动实时预测...")
                try:
                    for symbol in symbols:
                        # 获取最新数据并进行预测
                        latest_data = await self._get_latest_market_data(symbol)
                        if latest_data is not None:
                            prediction = await self._make_real_time_prediction(symbol, latest_data)
                            monitoring_results['predictions'][symbol] = prediction

                    monitoring_results['real_time_prediction'] = 'active'
                except Exception as e:
                    self.logger.warning(f"⚠️ 实时预测启动失败: {e}")
                    monitoring_results['real_time_prediction'] = 'failed'

            # 监控循环
            self.logger.info(f"🔄 开始监控循环，间隔: {monitoring_interval}秒")

            monitoring_count = 0

            while True:
                try:
                    monitoring_count += 1
                    self.logger.info(f"📈 监控周期 #{monitoring_count}")

                    # 更新监控统计
                    monitoring_results['monitoring_stats'] = {
                        'cycles_completed': monitoring_count,
                        'uptime_seconds': (datetime.now() - monitoring_results['start_time']).total_seconds(),
                        'last_update': datetime.now()
                    }

                    # 检查系统状态
                    system_status = await self._check_system_health()
                    monitoring_results['system_health'] = system_status

                    # 更新实时预测
                    if enable_real_time_prediction:
                        for symbol in symbols:
                            try:
                                latest_data = await self._get_latest_market_data(symbol)
                                if latest_data is not None:
                                    prediction = await self._make_real_time_prediction(symbol, latest_data)
                                    monitoring_results['predictions'][symbol] = prediction
                                    self.logger.info(f"🎯 {symbol} 预测更新: {prediction}")
                            except Exception as e:
                                self.logger.warning(f"⚠️ {symbol} 预测更新失败: {e}")

                    # 检查风险告警
                    if enable_risk_monitoring and hasattr(self, 'risk_manager'):
                        try:
                            risk_alerts = await self._check_risk_alerts()
                            if risk_alerts:
                                monitoring_results['risk_alerts'].extend(risk_alerts)
                                self.logger.warning(f"🚨 风险告警: {len(risk_alerts)}个新告警")
                        except Exception as e:
                            self.logger.warning(f"⚠️ 风险检查失败: {e}")

                    # 等待下一个监控周期
                    await asyncio.sleep(monitoring_interval)

                except KeyboardInterrupt:
                    self.logger.info("⏹️ 监控被用户中断")
                    break
                except Exception as e:
                    self.logger.error(f"❌ 监控周期失败: {e}")
                    await asyncio.sleep(monitoring_interval)

            # 停止监控
            monitoring_results['status'] = 'stopped'
            monitoring_results['end_time'] = datetime.now()

            self.logger.info("✅ 实时监控已停止")
            return monitoring_results

        except Exception as e:
            self.logger.error(f"❌ 实时监控失败: {e}")
            raise

    async def _get_latest_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """获取最新市场数据"""
        try:
            if hasattr(self, 'data_collector'):
                # 获取最新的1小时数据
                data = await self.data_collector.fetch_latest_data(symbol, '1h', limit=100)
                if data is not None and not data.empty:
                    return {
                        'symbol': symbol,
                        'timestamp': data.index[-1] if hasattr(data, 'index') else datetime.now(),
                        'close': float(data['close'].iloc[-1]) if 'close' in data.columns else None,
                        'volume': float(data['volume'].iloc[-1]) if 'volume' in data.columns else None,
                        'data_points': len(data)
                    }
            return None
        except Exception as e:
            self.logger.warning(f"⚠️ 获取{symbol}最新数据失败: {e}")
            return None

    async def _make_real_time_prediction(self, symbol: str, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """进行实时预测"""
        if self.model is None:
            return {'error': 'Model not loaded'}

        # This now represents the real-time feature vector for the symbol
        feature_vector = market_data.get('features') # Assuming features are pre-calculated

        if feature_vector is None:
            # Fallback for demonstration if features aren't passed
            # In a real system, you'd fetch or compute features here
            feature_vector = torch.randn(1, self.model_config.get('feature_dim', 60))

        try:
            # Use the main prediction logic
            results = await self.run_prediction(features=feature_vector)
            
            # Extract the relevant information
            probabilities = results['probabilities'][0] # We are predicting for a single item
            prediction_idx = results['predictions'][0]
            
            signal_map = {0: 'SELL', 1: 'HOLD', 2: 'BUY'} # Example mapping
            
            prediction_result = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'signal': signal_map.get(prediction_idx, 'UNKNOWN'),
                'confidence': max(probabilities),
                'price_at_prediction': market_data.get('close', 0),
                'full_probabilities': probabilities,
            }
            return prediction_result

        except Exception as e:
            self.logger.warning(f"⚠️ {symbol} real-time prediction failed: {e}", exc_info=True)
            return {'error': str(e)}

    async def _check_system_health(self) -> Dict[str, Any]:
        """检查系统健康状态"""
        try:
            import psutil
            import torch

            health_status = {
                'timestamp': datetime.now(),
                'cpu_usage': psutil.cpu_percent(),
                'memory_usage': psutil.virtual_memory().percent,
                'disk_usage': psutil.disk_usage('/').percent,
                'gpu_available': torch.cuda.is_available(),
                'status': 'healthy'
            }

            # GPU信息
            if torch.cuda.is_available():
                health_status['gpu_memory_used'] = torch.cuda.memory_allocated() / 1024**3  # GB
                health_status['gpu_memory_total'] = torch.cuda.get_device_properties(0).total_memory / 1024**3  # GB

            # 判断健康状态
            if health_status['cpu_usage'] > 90 or health_status['memory_usage'] > 90:
                health_status['status'] = 'warning'

            if health_status['disk_usage'] > 95:
                health_status['status'] = 'critical'

            return health_status

        except Exception as e:
            return {
                'timestamp': datetime.now(),
                'status': 'error',
                'error': str(e)
            }

    async def _check_risk_alerts(self) -> List[Dict[str, Any]]:
        """检查风险告警"""
        try:
            alerts = []

            if hasattr(self, 'risk_manager'):
                # 获取最新风险指标
                risk_metrics = await self.risk_manager.get_current_risk_metrics()

                # 检查风险阈值
                for metric_name, value in risk_metrics.items():
                    if metric_name == 'var' and value > 0.1:  # VaR > 10%
                        alerts.append({
                            'type': 'risk',
                            'metric': metric_name,
                            'value': value,
                            'threshold': 0.1,
                            'severity': 'high',
                            'timestamp': datetime.now()
                        })
                    elif metric_name == 'volatility' and value > 0.5:  # 波动率 > 50%
                        alerts.append({
                            'type': 'risk',
                            'metric': metric_name,
                            'value': value,
                            'threshold': 0.5,
                            'severity': 'medium',
                            'timestamp': datetime.now()
                        })

            return alerts

        except Exception as e:
            self.logger.warning(f"⚠️ 风险告警检查失败: {e}")
            return []
