"""
优化的DeepSeek API客户端
解决性能瓶颈：缓存命中率低、API调用频繁、内存泄漏等问题
"""

import asyncio
import hashlib
import logging
import time
import numpy as np
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
import aiohttp
import json
from concurrent.futures import ThreadPoolExecutor
import threading
import weakref

from .async_deepseek_client import AsyncDeepSeekClient, APIResponse
from src.storage.redis_cache import RedisCache
from src.utils.config import get_config_manager


@dataclass
class BatchRequest:
    """批量请求数据"""
    features: np.ndarray
    request_id: str
    timestamp: float
    context: Optional[str] = None


class OptimizedDeepSeekClient:
    """优化的DeepSeek客户端"""
    
    def __init__(self, base_client: AsyncDeepSeekClient = None):
        """
        初始化优化客户端
        
        Args:
            base_client: 基础DeepSeek客户端
        """
        self.logger = logging.getLogger(__name__)
        self.base_client = base_client or AsyncDeepSeekClient()
        
        # 配置
        config = get_config_manager().get('deepseek', {})
        deepseek_config = config.get('deepseek', {})
        
        # 批量处理配置
        self.batch_size = deepseek_config.get('batch_size', 128)
        self.batch_timeout = deepseek_config.get('batch_timeout', 30)
        self.max_concurrent = deepseek_config.get('max_concurrent_requests', 50)
        
        # 缓存优化
        self.redis_cache = RedisCache()
        self.cache_ttl = deepseek_config.get('cache', {}).get('ttl', 7200)  # 2小时
        
        # 批量处理队列
        self.batch_queue = asyncio.Queue()
        self.pending_requests = {}
        self.batch_processor_task = None
        
        # 性能统计
        self.stats = {
            'total_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'batch_requests': 0,
            'api_calls': 0,
            'avg_batch_size': 0,
            'total_latency': 0.0
        }
        
        # 资源管理
        self._cleanup_tasks = set()
        self._session_pool = None
        self._executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="deepseek-opt")
        
        # 启动批量处理器
        self._start_batch_processor()
        
    def _start_batch_processor(self):
        """启动批量处理器"""
        if self.batch_processor_task is None or self.batch_processor_task.done():
            self.batch_processor_task = asyncio.create_task(self._batch_processor())
            self._cleanup_tasks.add(self.batch_processor_task)
            
    async def _batch_processor(self):
        """批量处理器主循环"""
        while True:
            try:
                batch_requests = []
                
                # 收集批量请求
                try:
                    # 等待第一个请求
                    first_request = await asyncio.wait_for(
                        self.batch_queue.get(), timeout=self.batch_timeout
                    )
                    batch_requests.append(first_request)
                    
                    # 收集更多请求直到达到批量大小或超时
                    start_time = time.time()
                    while (len(batch_requests) < self.batch_size and 
                           time.time() - start_time < self.batch_timeout):
                        try:
                            request = await asyncio.wait_for(
                                self.batch_queue.get(), timeout=0.1
                            )
                            batch_requests.append(request)
                        except asyncio.TimeoutError:
                            break
                            
                except asyncio.TimeoutError:
                    # 没有请求，继续等待
                    continue
                    
                if batch_requests:
                    await self._process_batch(batch_requests)
                    
            except Exception as e:
                self.logger.error(f"批量处理器错误: {e}")
                await asyncio.sleep(1)
                
    async def _process_batch(self, batch_requests: List[BatchRequest]):
        """处理批量请求"""
        try:
            self.stats['batch_requests'] += 1
            self.stats['avg_batch_size'] = (
                (self.stats['avg_batch_size'] * (self.stats['batch_requests'] - 1) + 
                 len(batch_requests)) / self.stats['batch_requests']
            )
            
            # 检查缓存
            cached_results = {}
            uncached_requests = []
            
            for request in batch_requests:
                cache_key = self._generate_cache_key(request.features, request.context)
                cached_result = await self._get_from_cache(cache_key)
                
                if cached_result is not None:
                    cached_results[request.request_id] = cached_result
                    self.stats['cache_hits'] += 1
                else:
                    uncached_requests.append(request)
                    self.stats['cache_misses'] += 1
            
            # 处理未缓存的请求
            if uncached_requests:
                api_results = await self._call_api_batch(uncached_requests)
                
                # 缓存结果
                for request, result in zip(uncached_requests, api_results):
                    if result is not None:
                        cache_key = self._generate_cache_key(request.features, request.context)
                        await self._set_to_cache(cache_key, result)
                        cached_results[request.request_id] = result
            
            # 返回结果给等待的协程
            for request in batch_requests:
                if request.request_id in self.pending_requests:
                    future = self.pending_requests.pop(request.request_id)
                    if not future.done():
                        result = cached_results.get(request.request_id)
                        future.set_result(result)
                        
        except Exception as e:
            self.logger.error(f"批量处理错误: {e}")
            # 设置错误结果
            for request in batch_requests:
                if request.request_id in self.pending_requests:
                    future = self.pending_requests.pop(request.request_id)
                    if not future.done():
                        future.set_exception(e)
                        
    async def _call_api_batch(self, requests: List[BatchRequest]) -> List[Optional[np.ndarray]]:
        """调用API处理批量请求"""
        try:
            self.stats['api_calls'] += 1
            
            # 准备批量特征
            features_batch = np.array([req.features for req in requests])
            contexts = [req.context for req in requests]
            
            # 调用基础客户端的批量预测
            start_time = time.time()
            results = await self.base_client.predict_batch_async(
                features_batch, contexts, batch_size=len(requests)
            )
            latency = time.time() - start_time
            
            self.stats['total_latency'] += latency
            
            # 转换结果格式
            if results is not None and len(results) == len(requests):
                return [results[i] for i in range(len(requests))]
            else:
                # 返回默认预测
                return [np.array([0.33, 0.33, 0.34]) for _ in requests]
                
        except Exception as e:
            self.logger.error(f"API批量调用错误: {e}")
            return [None for _ in requests]
            
    def _generate_cache_key(self, features: np.ndarray, context: Optional[str] = None) -> str:
        """生成缓存键"""
        # 使用特征的哈希和上下文生成键
        features_hash = hashlib.md5(features.tobytes()).hexdigest()
        if context:
            context_hash = hashlib.md5(context.encode()).hexdigest()
            return f"deepseek_opt_{features_hash}_{context_hash}"
        return f"deepseek_opt_{features_hash}"
        
    async def _get_from_cache(self, cache_key: str) -> Optional[np.ndarray]:
        """从缓存获取结果"""
        try:
            result = await self.redis_cache.get_async(cache_key, level='L2')
            return result
        except Exception as e:
            self.logger.debug(f"缓存获取错误: {e}")
            return None
            
    async def _set_to_cache(self, cache_key: str, result: np.ndarray):
        """设置缓存"""
        try:
            await self.redis_cache.set_async(cache_key, result, ttl=self.cache_ttl, level='L2')
        except Exception as e:
            self.logger.debug(f"缓存设置错误: {e}")
            
    async def predict_async(
        self, 
        features: Union[np.ndarray, List[float]], 
        context: Optional[str] = None
    ) -> Optional[np.ndarray]:
        """异步预测（优化版本）"""
        if isinstance(features, list):
            features = np.array(features)
            
        self.stats['total_requests'] += 1
        
        # 生成请求ID
        request_id = f"{time.time()}_{id(features)}"
        
        # 创建批量请求
        batch_request = BatchRequest(
            features=features,
            request_id=request_id,
            timestamp=time.time(),
            context=context
        )
        
        # 创建Future等待结果
        future = asyncio.Future()
        self.pending_requests[request_id] = future
        
        # 添加到批量队列
        await self.batch_queue.put(batch_request)
        
        try:
            # 等待结果
            result = await asyncio.wait_for(future, timeout=60)
            return result
        except asyncio.TimeoutError:
            self.logger.warning(f"预测请求超时: {request_id}")
            self.pending_requests.pop(request_id, None)
            return np.array([0.33, 0.33, 0.34])  # 返回默认预测
            
    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计"""
        stats = self.stats.copy()
        if stats['total_requests'] > 0:
            stats['cache_hit_rate'] = stats['cache_hits'] / stats['total_requests']
            stats['avg_latency'] = stats['total_latency'] / max(stats['api_calls'], 1)
        else:
            stats['cache_hit_rate'] = 0.0
            stats['avg_latency'] = 0.0
        return stats
        
    async def cleanup(self):
        """清理资源"""
        try:
            # 停止批量处理器
            if self.batch_processor_task and not self.batch_processor_task.done():
                self.batch_processor_task.cancel()
                try:
                    await self.batch_processor_task
                except asyncio.CancelledError:
                    pass
                    
            # 清理待处理请求
            for future in self.pending_requests.values():
                if not future.done():
                    future.cancel()
            self.pending_requests.clear()
            
            # 清理其他任务
            for task in self._cleanup_tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            self._cleanup_tasks.clear()
            
            # 关闭线程池
            if self._executor:
                self._executor.shutdown(wait=False)
                
            self.logger.info("OptimizedDeepSeekClient资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理错误: {e}")
            
    def __del__(self):
        """析构函数"""
        try:
            if hasattr(self, '_executor') and self._executor:
                self._executor.shutdown(wait=False)
        except:
            pass
