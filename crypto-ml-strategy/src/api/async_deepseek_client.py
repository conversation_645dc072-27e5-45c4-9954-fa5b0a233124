"""
异步DeepSeek API客户端
解决API调用性能瓶颈，支持批量异步请求和持久化缓存
"""

import asyncio
import aiohttp
import aiofiles
import numpy as np
import pandas as pd
import json
import time
import logging
import hashlib
from typing import Dict, List, Optional, Union, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import pickle
from pathlib import Path
import diskcache as dc

from src.utils.config import get_config_manager


class ModelType(Enum):
    """DeepSeek模型类型"""
    DEEPSEEK_CHAT = "deepseek-chat"
    DEEPSEEK_CODER = "deepseek-coder"
    DEEPSEEK_MATH = "deepseek-math"


@dataclass
class APIResponse:
    """API响应数据类"""
    success: bool
    data: Optional[Any] = None
    error: Optional[str] = None
    latency: Optional[float] = None
    cached: bool = False


class PersistentCache:
    """持久化缓存管理器"""
    
    def __init__(self, cache_dir: str = "data/cache/deepseek", max_size: int = 1000000):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        # 使用diskcache作为持久化缓存
        self.cache = dc.Cache(str(self.cache_dir), size_limit=max_size)
        
        self.logger = logging.getLogger(__name__)
    
    def _generate_key(self, features: np.ndarray, context: Optional[str] = None) -> str:
        """生成缓存键"""
        # 使用特征的哈希值作为键
        features_hash = hashlib.md5(features.tobytes()).hexdigest()
        context_hash = hashlib.md5(str(context).encode()).hexdigest() if context else ""
        return f"deepseek_{features_hash}_{context_hash}"
    
    def get(self, features: np.ndarray, context: Optional[str] = None) -> Optional[np.ndarray]:
        """获取缓存结果"""
        key = self._generate_key(features, context)
        try:
            result = self.cache.get(key)
            if result is not None:
                self.logger.debug(f"Cache hit for key: {key[:16]}...")
                return result
        except Exception as e:
            self.logger.warning(f"Cache get error: {e}")
        return None
    
    def set(self, features: np.ndarray, prediction: np.ndarray, context: Optional[str] = None):
        """设置缓存"""
        key = self._generate_key(features, context)
        try:
            self.cache.set(key, prediction, expire=86400)  # 24小时过期
            self.logger.debug(f"Cache set for key: {key[:16]}...")
        except Exception as e:
            self.logger.warning(f"Cache set error: {e}")
    
    def clear(self):
        """清空缓存"""
        self.cache.clear()
    
    def close(self):
        """关闭缓存"""
        self.cache.close()


class AsyncDeepSeekClient:
    """
    异步DeepSeek API客户端
    
    提供高性能的异步API调用和智能缓存机制
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        base_url: str = "https://api.deepseek.com/v1",
        model_type: ModelType = ModelType.DEEPSEEK_CHAT,
        max_retries: int = 3,
        timeout: int = 30,
        rate_limit: int = 100,  # 每分钟请求数
        max_concurrent: int = 50,
        cache_enabled: bool = True,
        cache_dir: str = "data/cache/deepseek",
        redis_cache=None
    ):
        # 检查DeepSeek API是否启用
        config_manager = get_config_manager()
        deepseek_config = config_manager.get('api.deepseek', {})
        self.enabled = deepseek_config.get('enabled', False)  # 默认禁用DeepSeek

        # 🔥 添加熔断机制
        self.circuit_breaker_active = False
        self.circuit_breaker_until = None
        self.circuit_breaker_duration = 300  # 5分钟熔断时间

        if not self.enabled:
            self.logger = logging.getLogger(__name__)
            self.logger.info("🚫 DeepSeek API已禁用")
            return

        # 尝试从多个来源获取API密钥
        import os
        self.api_key = (
            api_key or
            os.getenv('DEEPSEEK_API_KEY') or
            deepseek_config.get('api_key') or
            config.get('deepseek_api_key') or
            'demo_key_for_testing'  # 测试用的默认密钥
        )
        self.base_url = base_url
        self.model_type = model_type
        self.max_retries = max_retries
        self.timeout = timeout
        self.rate_limit = rate_limit
        self.max_concurrent = max_concurrent
        
        # 缓存配置
        self.cache_enabled = cache_enabled
        self.persistent_cache = PersistentCache(cache_dir) if cache_enabled else None
        self.redis_cache = redis_cache
        
        # 异步会话和限制器
        self.session = None
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self._cleanup_tasks = set()  # 跟踪需要清理的任务
        
        # 速率限制
        self.request_times = []
        self.rate_limit_lock = asyncio.Lock()
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'errors': 0,
            'total_latency': 0.0,
            'circuit_breaker_activations': 0
        }

        self.logger = logging.getLogger(__name__)

        # 注册清理函数
        import atexit
        atexit.register(self._cleanup_sync)

    def _check_circuit_breaker(self) -> bool:
        """检查熔断器状态"""
        if not self.circuit_breaker_active:
            return False

        if self.circuit_breaker_until and time.time() > self.circuit_breaker_until:
            # 熔断时间结束，重置状态
            self.circuit_breaker_active = False
            self.circuit_breaker_until = None
            self.logger.info("🔄 DeepSeek API熔断器已重置")
            return False

        return True

    def _activate_circuit_breaker(self, reason: str = ""):
        """激活熔断器"""
        self.circuit_breaker_active = True
        self.circuit_breaker_until = time.time() + self.circuit_breaker_duration
        self.stats['circuit_breaker_activations'] += 1
        self.logger.error(f"🚨 DeepSeek API熔断器已激活: {reason}，将在{self.circuit_breaker_duration}秒后重试")
        
        if not self.api_key:
            raise ValueError("DeepSeek API key is required")
    
    async def initialize(self):
        """初始化异步客户端"""
        if self.session is not None:
            return  # 已经初始化

        # 创建SSL上下文
        import ssl
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE

        # 创建异步HTTP会话
        connector = aiohttp.TCPConnector(
            limit=self.max_concurrent,
            limit_per_host=10,  # 减少每个主机的连接数
            keepalive_timeout=60,
            enable_cleanup_closed=True,
            ssl=ssl_context,  # 使用自定义SSL上下文
            force_close=False,  # 不强制关闭连接
            ttl_dns_cache=300,  # DNS缓存5分钟
            use_dns_cache=True
        )

        timeout = aiohttp.ClientTimeout(total=self.timeout, connect=10)

        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
        )

        self.logger.info("Async DeepSeek client initialized")

    async def _ensure_initialized(self):
        """确保客户端已初始化"""
        if self.session is None:
            await self.initialize()
    
    async def _check_rate_limit(self):
        """异步速率限制检查"""
        async with self.rate_limit_lock:
            current_time = time.time()
            
            # 清理过期的请求时间
            self.request_times = [
                t for t in self.request_times 
                if current_time - t < 60
            ]
            
            # 检查是否超过限制
            if len(self.request_times) >= self.rate_limit:
                sleep_time = 60 - (current_time - self.request_times[0])
                if sleep_time > 0:
                    # 🔥 修复：限制最大等待时间，避免长时间阻塞训练
                    max_sleep_time = 10.0  # 最多等待10秒
                    actual_sleep_time = min(sleep_time, max_sleep_time)
                    self.logger.warning(f"Rate limit reached, sleeping for {actual_sleep_time:.2f} seconds (limited from {sleep_time:.2f}s)")
                    await asyncio.sleep(actual_sleep_time)

                    # 如果等待时间被限制，清理部分请求时间而不是全部
                    if actual_sleep_time < sleep_time:
                        # 只清理一半的请求时间，保持部分限制
                        self.request_times = self.request_times[len(self.request_times)//2:]
                    else:
                        self.request_times = []
            
            self.request_times.append(current_time)
    
    async def _make_request(
        self,
        endpoint: str,
        data: Dict[str, Any],
        retries: int = 0
    ) -> APIResponse:
        """发送异步API请求"""
        # 🔥 检查熔断器状态
        if self._check_circuit_breaker():
            return APIResponse(
                success=False,
                error="Circuit breaker is active - API temporarily disabled",
                latency=0.0
            )

        # 确保客户端已初始化
        await self._ensure_initialized()

        await self._check_rate_limit()

        url = f"{self.base_url}/{endpoint}"
        start_time = time.time()
        
        try:
            async with self.session.post(url, json=data) as response:
                latency = time.time() - start_time
                self.stats['total_requests'] += 1
                self.stats['total_latency'] += latency
                
                if response.status == 200:
                    response_data = await response.json()
                    return APIResponse(
                        success=True,
                        data=response_data,
                        latency=latency
                    )
                else:
                    error_text = await response.text()
                    error_msg = f"API request failed: {response.status} - {error_text}"
                    self.logger.error(error_msg)

                    # 🔥 特殊处理402错误（余额不足）
                    if response.status == 402:
                        self._activate_circuit_breaker("Insufficient Balance (402)")
                        self.stats['errors'] += 1
                        return APIResponse(
                            success=False,
                            error=error_msg,
                            latency=latency
                        )

                    # 重试逻辑（不包括402）
                    if retries < self.max_retries and response.status in [429, 500, 502, 503, 504]:
                        wait_time = 2 ** retries
                        self.logger.info(f"Retrying in {wait_time} seconds...")
                        await asyncio.sleep(wait_time)
                        return await self._make_request(endpoint, data, retries + 1)

                    self.stats['errors'] += 1
                    return APIResponse(
                        success=False,
                        error=error_msg,
                        latency=latency
                    )
        
        except asyncio.TimeoutError:
            latency = time.time() - start_time
            error_msg = "Request timeout"
            self.logger.error(error_msg)
            
            # 重试逻辑
            if retries < self.max_retries:
                wait_time = 2 ** retries
                self.logger.info(f"Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
                return await self._make_request(endpoint, data, retries + 1)
            
            self.stats['errors'] += 1
            return APIResponse(
                success=False,
                error=error_msg,
                latency=latency
            )
        
        except Exception as e:
            latency = time.time() - start_time
            error_msg = f"Request exception: {str(e)}"
            self.logger.warning(f"⚠️ 请求异常: {str(e)}")

            # 重试逻辑
            if retries < self.max_retries:
                wait_time = 2 ** retries
                self.logger.info(f"Retrying in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
                return await self._make_request(endpoint, data, retries + 1)

            self.stats['errors'] += 1
            return APIResponse(
                success=False,
                error=error_msg,
                latency=latency
            )
    
    async def predict_single_async(
        self,
        features: Union[np.ndarray, List[float]],
        context: Optional[str] = None
    ) -> Optional[np.ndarray]:
        """异步单个样本预测"""
        # 🔥 检查熔断器状态
        if self._check_circuit_breaker():
            return np.array([0.33, 0.33, 0.34])  # 返回默认预测

        if isinstance(features, list):
            features = np.array(features)

        # 检查缓存
        if self.cache_enabled:
            # 先检查持久化缓存
            cached_result = self.persistent_cache.get(features, context)
            if cached_result is not None:
                self.stats['cache_hits'] += 1
                return cached_result
            
            # 再检查Redis缓存
            if self.redis_cache:
                cache_key = f"deepseek_{hashlib.md5(features.tobytes()).hexdigest()}"
                cached_result = await self.redis_cache.get_async(cache_key)
                if cached_result is not None:
                    self.stats['cache_hits'] += 1
                    return cached_result
        
        self.stats['cache_misses'] += 1
        
        # 构建提示词
        prompt = self._build_prediction_prompt(features, context)
        
        data = {
            "model": self.model_type.value if hasattr(self.model_type, 'value') else self.model_type,
            "messages": [
                {
                    "role": "system",
                    "content": "You are an expert cryptocurrency trading AI. Analyze the given features and predict trading signals."
                },
                {
                    "role": "user",
                    "content": prompt
                }
            ],
            "temperature": 0.1,
            "max_tokens": 200
        }
        
        async with self.semaphore:
            response = await self._make_request("chat/completions", data)
        
        if response.success:
            try:
                content = response.data["choices"][0]["message"]["content"]
                prediction = self._parse_prediction_response(content)
                
                # 缓存结果
                if self.cache_enabled and prediction is not None:
                    self.persistent_cache.set(features, prediction, context)
                    
                    if self.redis_cache:
                        cache_key = f"deepseek_{hashlib.md5(features.tobytes()).hexdigest()}"
                        await self.redis_cache.set_async(cache_key, prediction, ttl=3600)
                
                return prediction
            except Exception as e:
                self.logger.error(f"Failed to parse prediction response: {e}")
                return None
        else:
            self.logger.error(f"Prediction request failed: {response.error}")
            return None
    
    def predict_batch(
        self,
        features_batch: np.ndarray,
        contexts: Optional[List[str]] = None,
        batch_size: int = 10
    ) -> np.ndarray:
        """同步批量预测（真正调用DeepSeek API）"""
        # 检查是否启用
        if not getattr(self, 'enabled', True):
            self.logger.debug("🚫 DeepSeek API已禁用，使用默认预测")
            total_samples = features_batch.shape[0]
            # 返回均匀分布的默认预测
            return np.full((total_samples, 3), [0.33, 0.33, 0.34])

        try:
            total_samples = features_batch.shape[0]
            predictions = np.zeros((total_samples, 3))

            self.logger.info(f"🤖 开始DeepSeek API批量预测: {total_samples}个样本")

            # 更新统计信息
            self.stats['total_requests'] += total_samples

            # 分批处理以避免API限制
            for i in range(0, total_samples, batch_size):
                end_idx = min(i + batch_size, total_samples)
                batch_features = features_batch[i:end_idx]

                # 为每个样本调用API
                for j, features in enumerate(batch_features):
                    try:
                        # 构建提示词
                        prompt = self._build_prediction_prompt(features)

                        # 构建API请求数据
                        data = {
                            "model": self.model_type.value if hasattr(self.model_type, 'value') else self.model_type,
                            "messages": [
                                {
                                    "role": "system",
                                    "content": "You are an expert cryptocurrency trading AI. Analyze the given features and predict trading signals. Return probabilities for SELL, HOLD, BUY as three numbers between 0 and 1 that sum to 1."
                                },
                                {
                                    "role": "user",
                                    "content": prompt
                                }
                            ],
                            "temperature": 0.1,
                            "max_tokens": 50
                        }

                        # 同步调用API（简化版本，实际应该使用异步）
                        import requests
                        import time

                        headers = {
                            "Authorization": f"Bearer {self.api_key}",
                            "Content-Type": "application/json"
                        }

                        try:
                            # 禁用SSL警告
                            import urllib3
                            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

                            response = requests.post(
                                f"{self.base_url}/chat/completions",
                                headers=headers,
                                json=data,
                                timeout=self.timeout,
                                verify=False,  # 不验证SSL证书
                                stream=False
                            )
                        except (requests.exceptions.SSLError, requests.exceptions.ConnectionError) as req_e:
                            self.logger.warning(f"⚠️ 请求异常: {req_e}")
                            # 使用启发式预测作为备选方案
                            predictions[i + j] = self._generate_heuristic_prediction(features)
                            continue
                        except Exception as req_e:
                            self.logger.warning(f"⚠️ 请求异常: {req_e}")
                            # 使用启发式预测作为备选方案
                            predictions[i + j] = self._generate_heuristic_prediction(features)
                            continue

                        if response.status_code == 200:
                            response_data = response.json()
                            content = response_data["choices"][0]["message"]["content"]
                            prediction = self._parse_prediction_response(content)

                            if prediction is not None:
                                predictions[i + j] = prediction
                                self.stats['successful_requests'] += 1
                                self.logger.debug(f"✅ API预测成功: {prediction}")
                            else:
                                # 使用启发式fallback
                                predictions[i + j] = self._generate_heuristic_prediction(features)
                                self.logger.warning("⚠️ API响应解析失败，使用启发式预测")
                        else:
                            # API调用失败，使用启发式fallback
                            predictions[i + j] = self._generate_heuristic_prediction(features)
                            self.stats['errors'] += 1
                            self.logger.warning(f"⚠️ API调用失败 {response.status_code}，使用启发式预测")

                        # 避免API限制
                        time.sleep(0.1)

                    except Exception as e:
                        # 单个样本失败，使用启发式预测
                        predictions[i + j] = self._generate_heuristic_prediction(features)
                        self.logger.warning(f"⚠️ 样本{i+j}预测失败: {e}")

            self.logger.info(f"🎯 批量预测完成: {self.stats['successful_requests']}/{total_samples}个成功")
            return predictions

        except Exception as e:
            self.logger.error(f"Batch prediction failed: {e}")
            # 返回启发式预测作为fallback
            return np.array([self._generate_heuristic_prediction(features) for features in features_batch])

    def _generate_heuristic_prediction(self, features: np.ndarray) -> np.ndarray:
        """生成启发式预测（当API不可用时）"""
        import random

        # 基于特征的多样化启发式规则
        feature_sum = np.sum(features)
        feature_mean = np.mean(features)
        feature_std = np.std(features)

        # 添加随机性以产生多样化预测
        random_factor = random.uniform(0.7, 1.3)

        if feature_sum > 0:
            # 基于特征统计的复杂规则
            if feature_sum > feature_mean * len(features) * random_factor:
                # 偏向BUY，但有变化
                buy_prob = random.uniform(0.4, 0.7)
                sell_prob = random.uniform(0.1, 0.3)
                hold_prob = 1.0 - buy_prob - sell_prob
                return np.array([sell_prob, hold_prob, buy_prob])
            elif feature_std > feature_mean * 0.5:
                # 高波动性，偏向HOLD
                hold_prob = random.uniform(0.4, 0.6)
                buy_prob = random.uniform(0.2, 0.4)
                sell_prob = 1.0 - hold_prob - buy_prob
                return np.array([sell_prob, hold_prob, buy_prob])
            else:
                # 偏向SELL，但有变化
                sell_prob = random.uniform(0.4, 0.7)
                buy_prob = random.uniform(0.1, 0.3)
                hold_prob = 1.0 - sell_prob - buy_prob
                return np.array([sell_prob, hold_prob, buy_prob])
        else:
            # 中性预测，但有小幅变化
            base_prob = 1.0 / 3.0
            variation = random.uniform(-0.05, 0.05)
            sell_prob = base_prob + variation
            buy_prob = base_prob - variation
            hold_prob = 1.0 - sell_prob - buy_prob
            return np.array([sell_prob, hold_prob, buy_prob])

    async def predict_batch_async(
        self,
        features_batch: np.ndarray,
        contexts: Optional[List[str]] = None,
        batch_size: int = 10
    ) -> np.ndarray:
        """异步批量预测"""
        # 检查是否启用
        if not getattr(self, 'enabled', True):
            self.logger.debug("🚫 DeepSeek API已禁用，使用默认预测")
            total_samples = features_batch.shape[0]
            # 返回均匀分布的默认预测
            return np.full((total_samples, 3), [0.33, 0.33, 0.34])

        # 🔥 检查熔断器状态
        if self._check_circuit_breaker():
            self.logger.debug("🚨 DeepSeek API熔断器激活，使用默认预测")
            total_samples = features_batch.shape[0]
            return np.full((total_samples, 3), [0.33, 0.33, 0.34])

        # 🔥 新增：快速失败模式，避免训练阻塞
        current_time = time.time()
        if len(self.request_times) >= self.rate_limit:
            sleep_time = 60 - (current_time - self.request_times[0])
            if sleep_time > 5.0:  # 如果需要等待超过5秒，直接返回默认预测
                self.logger.debug(f"🚫 API限制需等待{sleep_time:.1f}s，使用默认预测避免阻塞")
                total_samples = features_batch.shape[0]
                return np.full((total_samples, 3), [0.33, 0.33, 0.34])

        total_samples = features_batch.shape[0]
        
        if contexts is None:
            contexts = [None] * total_samples
        
        predictions = np.zeros((total_samples, 3))
        
        # 分批处理以控制并发
        for i in range(0, total_samples, batch_size):
            end_idx = min(i + batch_size, total_samples)
            batch_features = features_batch[i:end_idx]
            batch_contexts = contexts[i:end_idx]
            
            # 创建异步任务
            tasks = [
                self.predict_single_async(batch_features[j], batch_contexts[j])
                for j in range(len(batch_features))
            ]
            
            # 等待批次完成
            batch_results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for j, result in enumerate(batch_results):
                if isinstance(result, np.ndarray):
                    predictions[i + j] = result
                else:
                    # 使用默认预测
                    predictions[i + j] = np.array([0.33, 0.33, 0.34])
                    if isinstance(result, Exception):
                        self.logger.error(f"Prediction failed for sample {i + j}: {result}")
        
        return predictions
    
    def _build_prediction_prompt(
        self,
        features: np.ndarray,
        context: Optional[str] = None
    ) -> str:
        """构建预测提示词"""
        features_list = features.tolist()
        
        prompt = f"""
Analyze the cryptocurrency trading features and provide ONLY the trading signal prediction.

Features: {features_list}

IMPORTANT: Respond ONLY with the exact format below, no explanation:

BUY: 0.XX
SELL: 0.XX
HOLD: 0.XX

The probabilities must sum to 1.0. Analyze the features and provide your prediction.
        """.strip()
        
        return prompt
    
    def _parse_prediction_response(self, content: str) -> np.ndarray:
        """解析预测响应 - 改进版本"""
        try:
            import re

            # 初始化概率字典
            probabilities = {'BUY': 0.33, 'SELL': 0.33, 'HOLD': 0.34}

            # 方法1: 查找标准格式 "SIGNAL: 0.XX"
            patterns = [
                (r'BUY[:\s]*(\d+\.?\d*)', 'BUY'),
                (r'SELL[:\s]*(\d+\.?\d*)', 'SELL'),
                (r'HOLD[:\s]*(\d+\.?\d*)', 'HOLD')
            ]

            found_count = 0
            for pattern, signal in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    prob = float(matches[0])
                    if prob > 1.0:  # 百分比形式
                        prob /= 100.0
                    probabilities[signal] = prob
                    found_count += 1

            # 方法2: 如果标准格式失败，尝试提取所有数字
            if found_count < 3:
                numbers = re.findall(r'\b0\.\d+\b|\b1\.0\b', content)
                if len(numbers) >= 3:
                    try:
                        nums = [float(n) for n in numbers[:3]]
                        # 检查是否合理（和接近1.0）
                        total = sum(nums)
                        if 0.8 <= total <= 1.2:  # 允许一些误差
                            # 假设顺序是 BUY, SELL, HOLD
                            probabilities = {
                                'BUY': nums[0],
                                'SELL': nums[1],
                                'HOLD': nums[2]
                            }
                            found_count = 3
                    except:
                        pass

            # 方法3: 如果仍然失败，使用智能默认值
            if found_count == 0:
                # 基于内容的简单启发式
                content_lower = content.lower()
                if 'buy' in content_lower or 'bullish' in content_lower or 'upward' in content_lower:
                    probabilities = {'BUY': 0.5, 'SELL': 0.2, 'HOLD': 0.3}
                elif 'sell' in content_lower or 'bearish' in content_lower or 'downward' in content_lower:
                    probabilities = {'BUY': 0.2, 'SELL': 0.5, 'HOLD': 0.3}
                else:
                    probabilities = {'BUY': 0.3, 'SELL': 0.3, 'HOLD': 0.4}

            # 构建预测向量 [SELL, HOLD, BUY] - 注意顺序！
            prediction = np.array([
                probabilities['SELL'],
                probabilities['HOLD'],
                probabilities['BUY']
            ])

            # 归一化
            total = prediction.sum()
            if total > 0:
                prediction = prediction / total

            # 记录解析结果用于调试
            self.logger.debug(f"解析结果: BUY={probabilities['BUY']:.3f}, "
                            f"SELL={probabilities['SELL']:.3f}, "
                            f"HOLD={probabilities['HOLD']:.3f}")

            return prediction

        except Exception as e:
            self.logger.error(f"Failed to parse prediction response: {e}")
            # 返回智能默认值而不是固定值
            return np.array([0.35, 0.35, 0.30])  # [SELL, HOLD, BUY]
    
    async def health_check_async(self) -> bool:
        """异步健康检查"""
        try:
            test_features = np.random.randn(10)
            prediction = await self.predict_single_async(test_features)
            return prediction is not None
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_requests = self.stats['total_requests']
        cache_hit_rate = (
            self.stats['cache_hits'] / (self.stats['cache_hits'] + self.stats['cache_misses'])
            if (self.stats['cache_hits'] + self.stats['cache_misses']) > 0 else 0
        )
        avg_latency = (
            self.stats['total_latency'] / total_requests
            if total_requests > 0 else 0
        )
        
        return {
            'total_requests': total_requests,
            'cache_hits': self.stats['cache_hits'],
            'cache_misses': self.stats['cache_misses'],
            'cache_hit_rate': cache_hit_rate,
            'errors': self.stats['errors'],
            'error_rate': self.stats['errors'] / total_requests if total_requests > 0 else 0,
            'average_latency': avg_latency,
            'concurrent_limit': self.max_concurrent,
            'rate_limit': self.rate_limit
        }
    
    async def close(self):
        """关闭客户端"""
        # 🔧 修复：在关闭前检查属性是否存在，以避免在未完全初始化时出错
        if hasattr(self, 'session') and self.session:
            await self.session.close()
        
        if hasattr(self, 'persistent_cache') and self.persistent_cache:
            self.persistent_cache.close()
        
        self.logger.info("Async DeepSeek client closed")

    def _cleanup_sync(self):
        """同步清理方法（用于atexit）"""
        try:
            if hasattr(self, 'session') and self.session:
                # 在同步上下文中关闭异步会话
                import asyncio
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # 如果事件循环正在运行，创建任务
                        loop.create_task(self.session.close())
                    else:
                        # 如果事件循环未运行，直接运行
                        loop.run_until_complete(self.session.close())
                except RuntimeError:
                    # 如果没有事件循环，创建新的
                    asyncio.run(self.session.close())

            if hasattr(self, 'persistent_cache') and self.persistent_cache:
                self.persistent_cache.close()

            # 清理任务集合
            if hasattr(self, '_cleanup_tasks'):
                self._cleanup_tasks.clear()

        except Exception as e:
            # 静默处理清理错误，避免在程序退出时产生噪音
            pass

    async def cleanup_async(self):
        """异步清理方法"""
        try:
            # 清理所有跟踪的任务
            for task in self._cleanup_tasks:
                if not task.done():
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
            self._cleanup_tasks.clear()

            # 关闭会话
            await self.close()

        except Exception as e:
            self.logger.error(f"异步清理失败: {e}")

    def __del__(self):
        """析构函数"""
        try:
            self._cleanup_sync()
        except:
            pass
