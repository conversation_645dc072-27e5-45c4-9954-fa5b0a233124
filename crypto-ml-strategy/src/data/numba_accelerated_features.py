"""
Numba加速的特征工程模块
大幅提升技术指标计算性能
"""

import numpy as np
import pandas as pd
import logging
from typing import Tuple, Optional, Dict
import os

# 尝试导入Numba
try:
    from numba import jit, prange
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    # 创建装饰器的替代品
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        return decorator

    def prange(x):
        return range(x)

# 尝试导入TA-Lib替代方案
try:
    import pandas_ta as ta
    PANDAS_TA_AVAILABLE = True
except ImportError:
    PANDAS_TA_AVAILABLE = False

try:
    import talib
    TALIB_AVAILABLE = True
except ImportError:
    TALIB_AVAILABLE = False


# Numba加速的技术指标计算函数

@jit(nopython=True, cache=True)
def numba_sma(prices: np.ndarray, window: int) -> np.ndarray:
    """Numba加速的简单移动平均"""
    n = len(prices)
    result = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        result[i] = np.mean(prices[i - window + 1:i + 1])
    
    return result


@jit(nopython=True, cache=True)
def numba_ema(prices: np.ndarray, window: int) -> np.ndarray:
    """Numba加速的指数移动平均"""
    n = len(prices)
    result = np.full(n, np.nan)
    
    if n == 0:
        return result
    
    alpha = 2.0 / (window + 1)
    result[0] = prices[0]
    
    for i in range(1, n):
        result[i] = alpha * prices[i] + (1 - alpha) * result[i - 1]
    
    return result


@jit(nopython=True, cache=True)
def numba_rsi(prices: np.ndarray, window: int = 14) -> np.ndarray:
    """Numba加速的RSI计算"""
    n = len(prices)
    result = np.full(n, np.nan)
    
    if n < window + 1:
        return result
    
    # 计算价格变化
    deltas = np.diff(prices)
    
    # 分离涨跌
    gains = np.where(deltas > 0, deltas, 0.0)
    losses = np.where(deltas < 0, -deltas, 0.0)
    
    # 计算初始平均涨跌
    avg_gain = np.mean(gains[:window])
    avg_loss = np.mean(losses[:window])
    
    # 计算RSI
    for i in range(window, n - 1):
        avg_gain = (avg_gain * (window - 1) + gains[i]) / window
        avg_loss = (avg_loss * (window - 1) + losses[i]) / window
        
        if avg_loss == 0:
            result[i + 1] = 100.0
        else:
            rs = avg_gain / avg_loss
            result[i + 1] = 100.0 - (100.0 / (1.0 + rs))
    
    return result


@jit(nopython=True, cache=True)
def numba_bollinger_bands(prices: np.ndarray, window: int = 20, num_std: float = 2.0) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """Numba加速的布林带计算"""
    n = len(prices)
    middle = np.full(n, np.nan)
    upper = np.full(n, np.nan)
    lower = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        window_data = prices[i - window + 1:i + 1]
        mean_val = np.mean(window_data)
        std_val = np.std(window_data)
        
        middle[i] = mean_val
        upper[i] = mean_val + num_std * std_val
        lower[i] = mean_val - num_std * std_val
    
    return upper, middle, lower


@jit(nopython=True, cache=True)
def numba_macd(prices: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """Numba加速的MACD计算"""
    ema_fast = numba_ema(prices, fast)
    ema_slow = numba_ema(prices, slow)
    
    macd_line = ema_fast - ema_slow
    signal_line = numba_ema(macd_line, signal)
    histogram = macd_line - signal_line
    
    return macd_line, signal_line, histogram


@jit(nopython=True, cache=True)
def numba_stochastic(high: np.ndarray, low: np.ndarray, close: np.ndarray, window: int = 14) -> Tuple[np.ndarray, np.ndarray]:
    """Numba加速的随机指标计算"""
    n = len(close)
    k_percent = np.full(n, np.nan)
    d_percent = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        highest_high = np.max(high[i - window + 1:i + 1])
        lowest_low = np.min(low[i - window + 1:i + 1])
        
        if highest_high != lowest_low:
            k_percent[i] = 100.0 * (close[i] - lowest_low) / (highest_high - lowest_low)
        else:
            k_percent[i] = 50.0
    
    # 计算%D (3期%K的移动平均)
    for i in range(window + 1, n):
        d_percent[i] = np.mean(k_percent[i - 2:i + 1])
    
    return k_percent, d_percent


@jit(nopython=True, cache=True)
def numba_atr(high: np.ndarray, low: np.ndarray, close: np.ndarray, window: int = 14) -> np.ndarray:
    """Numba加速的ATR计算"""
    n = len(close)
    atr = np.full(n, np.nan)
    
    if n < 2:
        return atr
    
    # 计算真实范围
    tr = np.full(n, np.nan)
    for i in range(1, n):
        tr1 = high[i] - low[i]
        tr2 = abs(high[i] - close[i - 1])
        tr3 = abs(low[i] - close[i - 1])
        tr[i] = max(tr1, tr2, tr3)
    
    # 计算ATR
    for i in range(window, n):
        atr[i] = np.mean(tr[i - window + 1:i + 1])
    
    return atr


@jit(nopython=True, cache=True)
def numba_williams_r(high: np.ndarray, low: np.ndarray, close: np.ndarray, window: int = 14) -> np.ndarray:
    """Numba加速的威廉指标计算"""
    n = len(close)
    williams_r = np.full(n, np.nan)
    
    for i in range(window - 1, n):
        highest_high = np.max(high[i - window + 1:i + 1])
        lowest_low = np.min(low[i - window + 1:i + 1])
        
        if highest_high != lowest_low:
            williams_r[i] = -100.0 * (highest_high - close[i]) / (highest_high - lowest_low)
        else:
            williams_r[i] = -50.0
    
    return williams_r


@jit(nopython=True, cache=True)
def numba_momentum(prices: np.ndarray, window: int = 10) -> np.ndarray:
    """Numba加速的动量指标计算"""
    n = len(prices)
    momentum = np.full(n, np.nan)
    
    for i in range(window, n):
        momentum[i] = prices[i] - prices[i - window]
    
    return momentum


@jit(nopython=True, cache=True)
def numba_roc(prices: np.ndarray, window: int = 10) -> np.ndarray:
    """Numba加速的变化率计算"""
    n = len(prices)
    roc = np.full(n, np.nan)
    
    for i in range(window, n):
        if prices[i - window] != 0:
            roc[i] = 100.0 * (prices[i] - prices[i - window]) / prices[i - window]
    
    return roc


class NumbaAcceleratedFeatures:
    """Numba加速的特征工程类"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.numba_enabled = NUMBA_AVAILABLE and os.environ.get('USE_NUMBA_ACCELERATION', 'true').lower() == 'true'

        # 确定最佳的技术指标库
        if PANDAS_TA_AVAILABLE:
            self.ta_library = 'pandas_ta'
            self.logger.info("✅ 使用pandas-ta进行技术指标计算")
        elif TALIB_AVAILABLE:
            self.ta_library = 'talib'
            self.logger.info("✅ 使用TA-Lib进行技术指标计算")
        else:
            self.ta_library = 'pandas'
            self.logger.info("⚠️ 使用pandas进行技术指标计算（性能较低）")

        if self.numba_enabled:
            self.logger.info("✅ Numba加速已启用")
        else:
            self.logger.info("⚠️ Numba加速已禁用或不可用")
    
    def create_technical_indicators_np(self, kline_data: Dict[str, np.ndarray], index: pd.Index) -> pd.DataFrame:
        """
        Creates technical indicators from NumPy arrays for maximum performance and stability.
        
        Args:
            kline_data (Dict[str, np.ndarray]): A dictionary of numpy arrays for o,h,l,c,v.
            index (pd.Index): The original pandas DatetimeIndex to attach to the result.
            
        Returns:
            A new DataFrame containing only the calculated indicators.
        """
        close = kline_data['close']
        high = kline_data['high']
        low = kline_data['low']
        volume = kline_data['volume']
        n_rows = len(close)
        indicators = {}

        self.logger.info(f"🔧 开始计算技术指标，数据长度: {len(close)}")
        
        # --- All calculation logic remains the same, but results are stored in the dictionary ---

        # 移动平均线
        for window in [5, 10, 20, 50]:
            if len(close) >= window:
                if self.ta_library == 'pandas_ta':
                    close_series = pd.Series(close, index=index)
                    indicators[f'sma_{window}'] = ta.sma(close_series, length=window).values
                    indicators[f'ema_{window}'] = ta.ema(close_series, length=window).values
                elif self.numba_enabled:
                    indicators[f'sma_{window}'] = numba_sma(close, window)
                    indicators[f'ema_{window}'] = numba_ema(close, window)
                elif self.ta_library == 'talib':
                    indicators[f'sma_{window}'] = talib.SMA(close, timeperiod=window)
                    indicators[f'ema_{window}'] = talib.EMA(close, timeperiod=window)
                else:
                    indicators[f'sma_{window}'] = pd.Series(close).rolling(window).mean().values
                    indicators[f'ema_{window}'] = pd.Series(close).ewm(span=window).mean().values
        
        # RSI
        if len(close) >= 15:
            if self.ta_library == 'pandas_ta':
                close_series = pd.Series(close, index=index)
                indicators['rsi'] = ta.rsi(close_series, length=14).values
            elif self.numba_enabled:
                indicators['rsi'] = numba_rsi(close, 14)
            elif self.ta_library == 'talib':
                indicators['rsi'] = talib.RSI(close, timeperiod=14)
            else:
                delta = pd.Series(close).diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                indicators['rsi'] = (100 - (100 / (1 + rs))).values
        
        # 布林带
        if len(close) >= 20:
            if self.ta_library == 'pandas_ta':
                close_series = pd.Series(close, index=index)
                bbands = ta.bbands(close_series, length=20, std=2.0)
                if bbands is not None and not bbands.empty:
                    indicators['bb_lower'] = bbands.iloc[:, 0].values
                    indicators['bb_middle'] = bbands.iloc[:, 1].values
                    indicators['bb_upper'] = bbands.iloc[:, 2].values
                    indicators['bb_width'] = bbands.iloc[:, 3].values
                    indicators['bb_position'] = bbands.iloc[:, 4].values
            elif self.numba_enabled:
                bb_upper, bb_middle, bb_lower = numba_bollinger_bands(close, 20, 2.0)
                indicators['bb_upper'] = bb_upper
                indicators['bb_middle'] = bb_middle
                indicators['bb_lower'] = bb_lower
                indicators['bb_width'] = (bb_upper - bb_lower) / np.where(bb_middle==0, np.nan, bb_middle)
                indicators['bb_position'] = (close - bb_lower) / np.where((bb_upper - bb_lower)==0, np.nan, (bb_upper - bb_lower))
            elif self.ta_library == 'talib':
                bb_upper, bb_middle, bb_lower = talib.BBANDS(close, timeperiod=20, nbdevup=2, nbdevdn=2, matype=0)
                indicators['bb_upper'] = bb_upper
                indicators['bb_middle'] = bb_middle
                indicators['bb_lower'] = bb_lower
                indicators['bb_width'] = (bb_upper - bb_lower) / np.where(bb_middle==0, np.nan, bb_middle)
                indicators['bb_position'] = (close - bb_lower) / np.where((bb_upper - bb_lower)==0, np.nan, (bb_upper - bb_lower))
            else:
                bb_middle = pd.Series(close).rolling(20).mean()
                bb_std = pd.Series(close).rolling(20).std()
                bb_upper = bb_middle + 2 * bb_std
                bb_lower = bb_middle - 2 * bb_std
                indicators['bb_upper'] = bb_upper.values
                indicators['bb_middle'] = bb_middle.values
                indicators['bb_lower'] = bb_lower.values
                indicators['bb_width'] = ((bb_upper - bb_lower) / bb_middle).values
                indicators['bb_position'] = ((pd.Series(close) - bb_lower) / (bb_upper - bb_lower)).values
        
        # MACD
        if len(close) >= 35:
            if self.numba_enabled:
                macd_line, signal_line, histogram = numba_macd(close, 12, 26, 9)
                indicators['macd'] = macd_line
                indicators['macd_signal'] = signal_line
                indicators['macd_histogram'] = histogram
            else:
                ema12 = pd.Series(close).ewm(span=12).mean()
                ema26 = pd.Series(close).ewm(span=26).mean()
                macd = ema12 - ema26
                macd_signal = macd.ewm(span=9).mean()
                indicators['macd'] = macd.values
                indicators['macd_signal'] = macd_signal.values
                indicators['macd_histogram'] = (macd - macd_signal).values
        
        # 随机指标
        if len(close) >= 17:
            if self.numba_enabled:
                stoch_k, stoch_d = numba_stochastic(high, low, close, 14)
                indicators['stoch_k'] = stoch_k
                indicators['stoch_d'] = stoch_d
            else:
                lowest_low = pd.Series(low).rolling(14).min()
                highest_high = pd.Series(high).rolling(14).max()
                stoch_k = 100 * (pd.Series(close) - lowest_low) / (highest_high - lowest_low)
                indicators['stoch_k'] = stoch_k.values
                indicators['stoch_d'] = stoch_k.rolling(3).mean().values
        
        # ATR
        if len(close) >= 15:
            if self.numba_enabled:
                indicators['atr'] = numba_atr(high, low, close, 14)
            else:
                tr1 = pd.Series(high) - pd.Series(low)
                tr2 = abs(pd.Series(high) - pd.Series(close).shift(1))
                tr3 = abs(pd.Series(low) - pd.Series(close).shift(1))
                tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
                indicators['atr'] = tr.rolling(14).mean().values
        
        # 威廉指标
        if len(close) >= 15:
            if self.numba_enabled:
                indicators['williams_r'] = numba_williams_r(high, low, close, 14)
            else:
                highest_high = pd.Series(high).rolling(14).max()
                lowest_low = pd.Series(low).rolling(14).min()
                indicators['williams_r'] = (-100 * (highest_high - pd.Series(close)) / (highest_high - lowest_low)).values
        
        # 动量指标
        for window in [5, 10, 20]:
            if len(close) >= window + 1:
                if self.numba_enabled:
                    indicators[f'momentum_{window}'] = numba_momentum(close, window)
                    indicators[f'roc_{window}'] = numba_roc(close, window)
                else:
                    indicators[f'momentum_{window}'] = (pd.Series(close) - pd.Series(close).shift(window)).values
                    indicators[f'roc_{window}'] = (100 * (pd.Series(close) - pd.Series(close).shift(window)) / pd.Series(close).shift(window)).values
        
        # 成交量指标
        if volume is not None:
            for window in [5, 10, 20]:
                if len(volume) >= window:
                    if self.numba_enabled:
                        indicators[f'volume_sma_{window}'] = numba_sma(volume, window)
                    else:
                        indicators[f'volume_sma_{window}'] = pd.Series(volume).rolling(window).mean().values
            
            if f'volume_sma_20' in indicators:
                 # Ensure we use the calculated indicator, not a potentially non-existent df column
                 vol_sma_20 = pd.Series(indicators[f'volume_sma_20'], index=index)
                 indicators['volume_ratio'] = (pd.Series(volume, index=index) / vol_sma_20).values
        
        # Create a new DataFrame from the indicators dictionary with the original index
        indicators_df = pd.DataFrame(indicators, index=index)
        self.logger.info(f"✅ 技术指标计算完成，新增特征: {len(indicators_df.columns)} 个")
        return indicators_df
    
    def benchmark_performance(self, df: pd.DataFrame, iterations: int = 10) -> Dict:
        """性能基准测试"""
        import time
        
        close = df['close'].values
        
        results = {}
        
        # 测试SMA
        start_time = time.time()
        for _ in range(iterations):
            if self.numba_enabled:
                _ = numba_sma(close, 20)
            else:
                _ = df['close'].rolling(20).mean().values
        sma_time = (time.time() - start_time) / iterations
        
        # 测试RSI
        start_time = time.time()
        for _ in range(iterations):
            if self.numba_enabled:
                _ = numba_rsi(close, 14)
            else:
                delta = df['close'].diff()
                gain = (delta.where(delta > 0, 0)).rolling(window=14).mean()
                loss = (-delta.where(delta < 0, 0)).rolling(window=14).mean()
                rs = gain / loss
                _ = (100 - (100 / (1 + rs))).values
        rsi_time = (time.time() - start_time) / iterations
        
        results = {
            'numba_enabled': self.numba_enabled,
            'data_length': len(close),
            'sma_time_ms': sma_time * 1000,
            'rsi_time_ms': rsi_time * 1000,
            'total_time_ms': (sma_time + rsi_time) * 1000
        }
        
        self.logger.info(f"📊 性能基准测试结果: {results}")
        return results
