"""
多数据源集成器
集成深度数据（订单簿、交易量分布）和交易数据（大单追踪、资金流向）
"""

import asyncio
import time
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging
import aiohttp
import websockets
import json
from collections import deque, defaultdict

@dataclass
class OrderBookData:
    """订单簿数据"""
    symbol: str
    timestamp: float
    bids: List[Tuple[float, float]]  # [(price, quantity), ...]
    asks: List[Tuple[float, float]]
    bid_depth: float
    ask_depth: float
    spread: float
    imbalance: float

@dataclass
class TradeData:
    """交易数据"""
    symbol: str
    timestamp: float
    price: float
    quantity: float
    side: str  # 'buy' or 'sell'
    is_large_order: bool
    trade_id: str

@dataclass
class VolumeProfile:
    """成交量分布"""
    symbol: str
    timestamp: float
    price_levels: List[float]
    volumes: List[float]
    poc: float  # Point of Control
    value_area_high: float
    value_area_low: float

class MultiSourceDataIntegrator:
    """多数据源集成器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 数据缓存
        self.orderbook_cache = defaultdict(lambda: deque(maxlen=100))
        self.trade_cache = defaultdict(lambda: deque(maxlen=1000))
        self.volume_profile_cache = defaultdict(lambda: deque(maxlen=50))
        
        # 大单阈值配置
        self.large_order_thresholds = config.get('large_order_thresholds', {
            'BTCUSDT': 10.0,  # BTC
            'ETHUSDT': 100.0,  # ETH
            'BNBUSDT': 1000.0,  # BNB
            'SOLUSDT': 5000.0,  # SOL
            'DOGEUSDT': 100000.0  # DOGE
        })
        
        # WebSocket连接
        self.ws_connections = {}
        self.is_running = False
        
        # 数据质量监控
        self.data_quality_metrics = {
            'orderbook_updates': 0,
            'trade_updates': 0,
            'missing_data_count': 0,
            'last_update_time': {}
        }
    
    async def start(self):
        """启动多数据源集成"""
        self.is_running = True
        self.logger.info("🚀 启动多数据源集成器...")
        
        # 启动各种数据源
        tasks = [
            self._start_orderbook_stream(),
            self._start_trade_stream(),
            self._start_volume_analysis(),
            self._monitor_data_quality()
        ]
        
        await asyncio.gather(*tasks, return_exceptions=True)
    
    async def stop(self):
        """停止数据集成"""
        self.is_running = False
        
        # 关闭WebSocket连接
        for ws in self.ws_connections.values():
            if ws and not ws.closed:
                await ws.close()
        
        self.logger.info("⏹️ 多数据源集成器已停止")
    
    async def _start_orderbook_stream(self):
        """启动订单簿数据流"""
        symbols = self.config.get('symbols', ['BTCUSDT', 'ETHUSDT'])
        
        for symbol in symbols:
            asyncio.create_task(self._subscribe_orderbook(symbol))
    
    async def _subscribe_orderbook(self, symbol: str):
        """订阅订单簿数据"""
        url = f"wss://stream.binance.com:9443/ws/{symbol.lower()}@depth20@100ms"
        
        while self.is_running:
            try:
                async with websockets.connect(url) as websocket:
                    self.ws_connections[f"orderbook_{symbol}"] = websocket
                    self.logger.info(f"📊 订阅订单簿数据: {symbol}")
                    
                    async for message in websocket:
                        if not self.is_running:
                            break
                        
                        data = json.loads(message)
                        await self._process_orderbook_data(symbol, data)
                        
            except Exception as e:
                self.logger.error(f"❌ 订单簿连接错误 {symbol}: {e}")
                await asyncio.sleep(5)
    
    async def _process_orderbook_data(self, symbol: str, data: Dict[str, Any]):
        """处理订单簿数据"""
        try:
            bids = [(float(bid[0]), float(bid[1])) for bid in data['bids']]
            asks = [(float(ask[0]), float(ask[1])) for ask in data['asks']]
            
            # 计算深度指标
            bid_depth = sum(price * qty for price, qty in bids[:10])
            ask_depth = sum(price * qty for price, qty in asks[:10])
            
            # 计算价差
            best_bid = bids[0][0] if bids else 0
            best_ask = asks[0][0] if asks else 0
            spread = (best_ask - best_bid) / best_bid if best_bid > 0 else 0
            
            # 计算订单簿不平衡
            imbalance = (bid_depth - ask_depth) / (bid_depth + ask_depth) if (bid_depth + ask_depth) > 0 else 0
            
            orderbook = OrderBookData(
                symbol=symbol,
                timestamp=time.time(),
                bids=bids,
                asks=asks,
                bid_depth=bid_depth,
                ask_depth=ask_depth,
                spread=spread,
                imbalance=imbalance
            )
            
            self.orderbook_cache[symbol].append(orderbook)
            self.data_quality_metrics['orderbook_updates'] += 1
            self.data_quality_metrics['last_update_time'][f"orderbook_{symbol}"] = time.time()
            
        except Exception as e:
            self.logger.error(f"❌ 处理订单簿数据失败 {symbol}: {e}")
    
    async def _start_trade_stream(self):
        """启动交易数据流"""
        symbols = self.config.get('symbols', ['BTCUSDT', 'ETHUSDT'])
        
        for symbol in symbols:
            asyncio.create_task(self._subscribe_trades(symbol))
    
    async def _subscribe_trades(self, symbol: str):
        """订阅交易数据"""
        url = f"wss://stream.binance.com:9443/ws/{symbol.lower()}@trade"
        
        while self.is_running:
            try:
                async with websockets.connect(url) as websocket:
                    self.ws_connections[f"trade_{symbol}"] = websocket
                    self.logger.info(f"💰 订阅交易数据: {symbol}")
                    
                    async for message in websocket:
                        if not self.is_running:
                            break
                        
                        data = json.loads(message)
                        await self._process_trade_data(symbol, data)
                        
            except Exception as e:
                self.logger.error(f"❌ 交易数据连接错误 {symbol}: {e}")
                await asyncio.sleep(5)
    
    async def _process_trade_data(self, symbol: str, data: Dict[str, Any]):
        """处理交易数据"""
        try:
            price = float(data['p'])
            quantity = float(data['q'])
            side = 'buy' if data['m'] else 'sell'  # m=true表示买方是maker
            
            # 判断是否为大单
            threshold = self.large_order_thresholds.get(symbol, 10.0)
            is_large_order = (price * quantity) >= threshold
            
            trade = TradeData(
                symbol=symbol,
                timestamp=float(data['T']) / 1000,  # 转换为秒
                price=price,
                quantity=quantity,
                side=side,
                is_large_order=is_large_order,
                trade_id=data['t']
            )
            
            self.trade_cache[symbol].append(trade)
            self.data_quality_metrics['trade_updates'] += 1
            self.data_quality_metrics['last_update_time'][f"trade_{symbol}"] = time.time()
            
            # 大单预警
            if is_large_order:
                self.logger.info(f"🚨 大单检测: {symbol} {side.upper()} {quantity:.4f} @ {price:.4f} (价值: ${price*quantity:.2f})")
            
        except Exception as e:
            self.logger.error(f"❌ 处理交易数据失败 {symbol}: {e}")
    
    async def _start_volume_analysis(self):
        """启动成交量分析"""
        while self.is_running:
            try:
                symbols = self.config.get('symbols', ['BTCUSDT', 'ETHUSDT'])
                
                for symbol in symbols:
                    await self._analyze_volume_profile(symbol)
                
                await asyncio.sleep(60)  # 每分钟分析一次
                
            except Exception as e:
                self.logger.error(f"❌ 成交量分析错误: {e}")
                await asyncio.sleep(30)
    
    async def _analyze_volume_profile(self, symbol: str):
        """分析成交量分布"""
        try:
            trades = list(self.trade_cache[symbol])
            if len(trades) < 10:
                return
            
            # 获取最近5分钟的交易
            current_time = time.time()
            recent_trades = [t for t in trades if current_time - t.timestamp <= 300]
            
            if not recent_trades:
                return
            
            # 计算价格区间和成交量
            prices = [t.price for t in recent_trades]
            volumes = [t.quantity for t in recent_trades]
            
            min_price = min(prices)
            max_price = max(prices)
            
            # 创建价格区间
            num_levels = 20
            price_step = (max_price - min_price) / num_levels
            price_levels = [min_price + i * price_step for i in range(num_levels + 1)]
            
            # 计算每个价格区间的成交量
            level_volumes = [0.0] * num_levels
            for trade in recent_trades:
                level_idx = min(int((trade.price - min_price) / price_step), num_levels - 1)
                level_volumes[level_idx] += trade.quantity
            
            # 找到POC (Point of Control)
            max_volume_idx = level_volumes.index(max(level_volumes))
            poc = price_levels[max_volume_idx]
            
            # 计算价值区域 (70%成交量区域)
            total_volume = sum(level_volumes)
            target_volume = total_volume * 0.7
            
            # 从POC向两边扩展
            current_volume = level_volumes[max_volume_idx]
            low_idx = high_idx = max_volume_idx
            
            while current_volume < target_volume and (low_idx > 0 or high_idx < num_levels - 1):
                if low_idx > 0 and high_idx < num_levels - 1:
                    if level_volumes[low_idx - 1] > level_volumes[high_idx + 1]:
                        low_idx -= 1
                        current_volume += level_volumes[low_idx]
                    else:
                        high_idx += 1
                        current_volume += level_volumes[high_idx]
                elif low_idx > 0:
                    low_idx -= 1
                    current_volume += level_volumes[low_idx]
                elif high_idx < num_levels - 1:
                    high_idx += 1
                    current_volume += level_volumes[high_idx]
                else:
                    break
            
            value_area_low = price_levels[low_idx]
            value_area_high = price_levels[high_idx]
            
            volume_profile = VolumeProfile(
                symbol=symbol,
                timestamp=current_time,
                price_levels=price_levels,
                volumes=level_volumes,
                poc=poc,
                value_area_high=value_area_high,
                value_area_low=value_area_low
            )
            
            self.volume_profile_cache[symbol].append(volume_profile)
            
        except Exception as e:
            self.logger.error(f"❌ 成交量分析失败 {symbol}: {e}")
    
    async def _monitor_data_quality(self):
        """监控数据质量"""
        while self.is_running:
            try:
                current_time = time.time()
                
                # 检查数据更新时间
                for source, last_time in self.data_quality_metrics['last_update_time'].items():
                    if current_time - last_time > 30:  # 30秒无更新
                        self.logger.warning(f"⚠️ 数据源超时: {source}")
                        self.data_quality_metrics['missing_data_count'] += 1
                
                # 每5分钟报告一次统计
                if int(current_time) % 300 == 0:
                    self.logger.info(f"📊 数据质量报告:")
                    self.logger.info(f"  订单簿更新: {self.data_quality_metrics['orderbook_updates']}")
                    self.logger.info(f"  交易更新: {self.data_quality_metrics['trade_updates']}")
                    self.logger.info(f"  数据缺失: {self.data_quality_metrics['missing_data_count']}")
                
                await asyncio.sleep(10)
                
            except Exception as e:
                self.logger.error(f"❌ 数据质量监控错误: {e}")
                await asyncio.sleep(30)
    
    def get_integrated_features(self, symbol: str, lookback_seconds: int = 300) -> Dict[str, Any]:
        """获取集成特征"""
        try:
            current_time = time.time()
            features = {}
            
            # 订单簿特征
            orderbooks = [ob for ob in self.orderbook_cache[symbol] 
                         if current_time - ob.timestamp <= lookback_seconds]
            
            if orderbooks:
                features.update({
                    'avg_spread': np.mean([ob.spread for ob in orderbooks]),
                    'avg_imbalance': np.mean([ob.imbalance for ob in orderbooks]),
                    'bid_depth_trend': np.polyfit(range(len(orderbooks)), 
                                                 [ob.bid_depth for ob in orderbooks], 1)[0] if len(orderbooks) > 1 else 0,
                    'ask_depth_trend': np.polyfit(range(len(orderbooks)), 
                                                 [ob.ask_depth for ob in orderbooks], 1)[0] if len(orderbooks) > 1 else 0
                })
            
            # 交易特征
            trades = [t for t in self.trade_cache[symbol] 
                     if current_time - t.timestamp <= lookback_seconds]
            
            if trades:
                buy_trades = [t for t in trades if t.side == 'buy']
                sell_trades = [t for t in trades if t.side == 'sell']
                large_orders = [t for t in trades if t.is_large_order]
                
                features.update({
                    'trade_count': len(trades),
                    'buy_ratio': len(buy_trades) / len(trades) if trades else 0.5,
                    'large_order_count': len(large_orders),
                    'large_order_ratio': len(large_orders) / len(trades) if trades else 0,
                    'avg_trade_size': np.mean([t.quantity for t in trades]),
                    'volume_weighted_price': np.average([t.price for t in trades], 
                                                       weights=[t.quantity for t in trades]) if trades else 0
                })
            
            # 成交量分布特征
            volume_profiles = list(self.volume_profile_cache[symbol])
            if volume_profiles:
                latest_profile = volume_profiles[-1]
                features.update({
                    'poc_price': latest_profile.poc,
                    'value_area_width': latest_profile.value_area_high - latest_profile.value_area_low,
                    'volume_concentration': max(latest_profile.volumes) / sum(latest_profile.volumes) if latest_profile.volumes else 0
                })
            
            return features
            
        except Exception as e:
            self.logger.error(f"❌ 获取集成特征失败 {symbol}: {e}")
            return {}
    
    def get_data_quality_report(self) -> Dict[str, Any]:
        """获取数据质量报告"""
        return {
            'metrics': self.data_quality_metrics.copy(),
            'cache_sizes': {
                'orderbook': {symbol: len(cache) for symbol, cache in self.orderbook_cache.items()},
                'trade': {symbol: len(cache) for symbol, cache in self.trade_cache.items()},
                'volume_profile': {symbol: len(cache) for symbol, cache in self.volume_profile_cache.items()}
            },
            'connection_status': {name: not ws.closed if ws else False 
                                for name, ws in self.ws_connections.items()}
        }
