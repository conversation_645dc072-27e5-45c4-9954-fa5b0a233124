"""
Data Processing Module

This package contains the unified data pipeline for all data loading, 
processing, and feature engineering tasks.
"""

from .unified_pipeline.data_manager import DataManager
from .unified_pipeline.feature_engine import FeatureEngine
from .unified_pipeline.dataset import UnifiedCryptoDataset
from .unified_pipeline.factory import DataLoaderFactory

__all__ = [
    'DataManager',
    'FeatureEngine',
    'UnifiedCryptoDataset',
    'DataLoaderFactory'
]
