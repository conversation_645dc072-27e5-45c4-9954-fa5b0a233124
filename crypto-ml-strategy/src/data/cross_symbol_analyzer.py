"""
币种间特征交互分析器
实现币种间相关性分析、领先滞后关系分析、市场情绪传导分析

@author: Crypto Trading System
@since: 1.0.0
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from scipy import stats
from scipy.stats import pearsonr, spearmanr
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans
import networkx as nx
import logging
from dataclasses import dataclass
from enum import Enum
import warnings
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)


class CorrelationType(Enum):
    """相关性类型"""
    PEARSON = "pearson"
    SPEARMAN = "spearman"
    KENDALL = "kendall"
    MUTUAL_INFO = "mutual_info"


class LeadLagMethod(Enum):
    """领先滞后分析方法"""
    CROSS_CORRELATION = "cross_correlation"
    GRANGER_CAUSALITY = "granger_causality"
    TRANSFER_ENTROPY = "transfer_entropy"


@dataclass
class CorrelationResult:
    """相关性分析结果"""
    symbol_pair: Tuple[str, str]
    correlation: float
    p_value: float
    method: CorrelationType
    significance_level: float = 0.05
    
    @property
    def is_significant(self) -> bool:
        return self.p_value < self.significance_level


@dataclass
class LeadLagResult:
    """领先滞后关系结果"""
    leader: str
    follower: str
    lag_periods: int
    strength: float
    confidence: float
    method: LeadLagMethod


@dataclass
class MarketRegimeResult:
    """市场状态分析结果"""
    regime_id: int
    symbols: List[str]
    characteristics: Dict[str, float]
    duration: int
    stability: float


class CrossSymbolAnalyzer:
    """币种间特征交互分析器"""
    
    def __init__(self, symbols: List[str]):
        self.symbols = symbols
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # 分析结果缓存
        self.correlation_matrix: Optional[pd.DataFrame] = None
        self.lead_lag_relationships: List[LeadLagResult] = []
        self.market_regimes: List[MarketRegimeResult] = []
        
        # 网络图
        self.correlation_network: Optional[nx.Graph] = None
        self.influence_network: Optional[nx.DiGraph] = None
        
    def analyze_cross_correlations(self, 
                                 symbol_data: Dict[str, pd.DataFrame],
                                 feature_columns: List[str] = None,
                                 method: CorrelationType = CorrelationType.PEARSON,
                                 window_size: int = 100) -> pd.DataFrame:
        """
        分析币种间相关性
        
        Args:
            symbol_data: 各币种的数据
            feature_columns: 要分析的特征列
            method: 相关性计算方法
            window_size: 滚动窗口大小
            
        Returns:
            相关性矩阵
        """
        self.logger.info(f"开始币种间相关性分析: {len(self.symbols)}个币种")
        
        if feature_columns is None:
            feature_columns = ['close', 'volume', 'rsi', 'macd']
        
        # 准备数据
        aligned_data = self._align_symbol_data(symbol_data, feature_columns)
        
        # 计算相关性矩阵
        correlation_results = {}
        
        for feature in feature_columns:
            feature_data = {}
            for symbol in self.symbols:
                if symbol in aligned_data and feature in aligned_data[symbol].columns:
                    feature_data[symbol] = aligned_data[symbol][feature]
            
            if len(feature_data) >= 2:
                corr_matrix = self._calculate_correlation_matrix(
                    feature_data, method, window_size
                )
                correlation_results[feature] = corr_matrix
        
        # 合并结果
        self.correlation_matrix = self._aggregate_correlation_matrices(correlation_results)
        
        self.logger.info(f"相关性分析完成: {self.correlation_matrix.shape}")
        return self.correlation_matrix
    
    def analyze_lead_lag_relationships(self,
                                     symbol_data: Dict[str, pd.DataFrame],
                                     max_lag: int = 20,
                                     method: LeadLagMethod = LeadLagMethod.CROSS_CORRELATION) -> List[LeadLagResult]:
        """
        分析领先滞后关系
        
        Args:
            symbol_data: 各币种的数据
            max_lag: 最大滞后期数
            method: 分析方法
            
        Returns:
            领先滞后关系列表
        """
        self.logger.info(f"开始领先滞后关系分析: max_lag={max_lag}")
        
        # 准备价格变化数据
        price_changes = {}
        for symbol, data in symbol_data.items():
            if 'close' in data.columns:
                price_changes[symbol] = data['close'].pct_change().dropna()
        
        # 分析所有币种对
        lead_lag_results = []
        
        for i, symbol1 in enumerate(self.symbols):
            for j, symbol2 in enumerate(self.symbols):
                if i != j and symbol1 in price_changes and symbol2 in price_changes:
                    result = self._analyze_pair_lead_lag(
                        price_changes[symbol1], 
                        price_changes[symbol2],
                        symbol1, symbol2, max_lag, method
                    )
                    if result:
                        lead_lag_results.append(result)
        
        self.lead_lag_relationships = lead_lag_results
        self.logger.info(f"发现{len(lead_lag_results)}个显著的领先滞后关系")
        
        return lead_lag_results
    
    def analyze_market_sentiment_contagion(self,
                                         symbol_data: Dict[str, pd.DataFrame],
                                         sentiment_features: List[str] = None) -> Dict[str, Any]:
        """
        分析市场情绪传导
        
        Args:
            symbol_data: 各币种的数据
            sentiment_features: 情绪相关特征
            
        Returns:
            情绪传导分析结果
        """
        self.logger.info("开始市场情绪传导分析")
        
        if sentiment_features is None:
            sentiment_features = ['rsi', 'williams_r', 'stoch_k', 'cci']
        
        # 构建情绪指数
        sentiment_indices = self._build_sentiment_indices(symbol_data, sentiment_features)
        
        # 分析情绪传导网络
        contagion_network = self._analyze_sentiment_contagion_network(sentiment_indices)
        
        # 识别情绪传导中心
        contagion_centers = self._identify_contagion_centers(contagion_network)
        
        # 计算传导强度
        contagion_strength = self._calculate_contagion_strength(sentiment_indices)
        
        results = {
            'sentiment_indices': sentiment_indices,
            'contagion_network': contagion_network,
            'contagion_centers': contagion_centers,
            'contagion_strength': contagion_strength,
            'network_metrics': self._calculate_network_metrics(contagion_network)
        }
        
        self.logger.info("市场情绪传导分析完成")
        return results
    
    def detect_market_regimes(self,
                            symbol_data: Dict[str, pd.DataFrame],
                            n_regimes: int = 3,
                            features: List[str] = None) -> List[MarketRegimeResult]:
        """
        检测市场状态
        
        Args:
            symbol_data: 各币种的数据
            n_regimes: 状态数量
            features: 用于聚类的特征
            
        Returns:
            市场状态列表
        """
        self.logger.info(f"开始市场状态检测: {n_regimes}个状态")
        
        if features is None:
            features = ['volatility', 'volume_trend', 'momentum', 'correlation_strength']
        
        # 构建市场特征矩阵
        market_features = self._build_market_feature_matrix(symbol_data, features)
        
        # 使用聚类算法检测状态
        regimes = self._cluster_market_regimes(market_features, n_regimes)
        
        # 分析每个状态的特征
        regime_results = []
        for regime_id in range(n_regimes):
            regime_data = market_features[regimes == regime_id]
            if len(regime_data) > 0:
                result = self._analyze_regime_characteristics(
                    regime_id, regime_data, market_features.index[regimes == regime_id]
                )
                regime_results.append(result)
        
        self.market_regimes = regime_results
        self.logger.info(f"检测到{len(regime_results)}个市场状态")
        
        return regime_results
    
    def build_influence_network(self) -> nx.DiGraph:
        """构建影响力网络"""
        if not self.lead_lag_relationships:
            self.logger.warning("需要先进行领先滞后关系分析")
            return nx.DiGraph()
        
        # 创建有向图
        G = nx.DiGraph()
        
        # 添加节点
        for symbol in self.symbols:
            G.add_node(symbol)
        
        # 添加边（基于领先滞后关系）
        for relationship in self.lead_lag_relationships:
            if relationship.confidence > 0.7:  # 只包含高置信度的关系
                G.add_edge(
                    relationship.leader,
                    relationship.follower,
                    weight=relationship.strength,
                    lag=relationship.lag_periods,
                    confidence=relationship.confidence
                )
        
        self.influence_network = G
        return G
    
    def get_market_leaders(self, top_n: int = 5) -> List[Tuple[str, float]]:
        """获取市场领导者"""
        if self.influence_network is None:
            self.build_influence_network()
        
        # 计算出度中心性（影响力）
        out_centrality = nx.out_degree_centrality(self.influence_network)
        
        # 计算PageRank（综合影响力）
        pagerank = nx.pagerank(self.influence_network)
        
        # 综合评分
        leadership_scores = {}
        for symbol in self.symbols:
            out_score = out_centrality.get(symbol, 0)
            pr_score = pagerank.get(symbol, 0)
            leadership_scores[symbol] = (out_score + pr_score) / 2
        
        # 排序并返回top_n
        sorted_leaders = sorted(leadership_scores.items(), key=lambda x: x[1], reverse=True)
        return sorted_leaders[:top_n]
    
    def get_analysis_summary(self) -> Dict[str, Any]:
        """获取分析摘要"""
        summary = {
            'symbols_analyzed': len(self.symbols),
            'correlation_matrix_shape': self.correlation_matrix.shape if self.correlation_matrix is not None else None,
            'lead_lag_relationships_count': len(self.lead_lag_relationships),
            'market_regimes_count': len(self.market_regimes),
            'network_nodes': self.influence_network.number_of_nodes() if self.influence_network else 0,
            'network_edges': self.influence_network.number_of_edges() if self.influence_network else 0
        }
        
        # 添加相关性统计
        if self.correlation_matrix is not None:
            corr_values = self.correlation_matrix.values
            corr_values = corr_values[~np.isnan(corr_values)]
            summary.update({
                'avg_correlation': np.mean(corr_values),
                'max_correlation': np.max(corr_values),
                'min_correlation': np.min(corr_values),
                'correlation_std': np.std(corr_values)
            })
        
        # 添加领导者信息
        if self.influence_network:
            leaders = self.get_market_leaders(3)
            summary['top_market_leaders'] = leaders
        
        return summary
    
    def _align_symbol_data(self, symbol_data: Dict[str, pd.DataFrame],
                          features: List[str]) -> Dict[str, pd.DataFrame]:
        """对齐不同币种的数据"""
        aligned_data = {}

        # 找到共同的时间范围
        common_index = None
        for symbol, data in symbol_data.items():
            if 'timestamp' in data.columns:
                data = data.set_index('timestamp')

            if common_index is None:
                common_index = data.index
            else:
                common_index = common_index.intersection(data.index)

        # 对齐数据
        for symbol, data in symbol_data.items():
            if 'timestamp' in data.columns:
                data = data.set_index('timestamp')

            # 选择共同时间范围和特征
            available_features = [f for f in features if f in data.columns]
            if available_features and len(common_index) > 0:
                aligned_data[symbol] = data.loc[common_index, available_features]

        return aligned_data

    def _calculate_correlation_matrix(self, feature_data: Dict[str, pd.Series],
                                    method: CorrelationType, window_size: int) -> pd.DataFrame:
        """计算相关性矩阵"""
        symbols = list(feature_data.keys())
        n_symbols = len(symbols)

        # 创建数据矩阵
        data_matrix = pd.DataFrame(feature_data)

        # 计算相关性
        if method == CorrelationType.PEARSON:
            corr_matrix = data_matrix.corr(method='pearson')
        elif method == CorrelationType.SPEARMAN:
            corr_matrix = data_matrix.corr(method='spearman')
        elif method == CorrelationType.KENDALL:
            corr_matrix = data_matrix.corr(method='kendall')
        else:
            # 默认使用Pearson
            corr_matrix = data_matrix.corr(method='pearson')

        return corr_matrix

    def _aggregate_correlation_matrices(self, correlation_results: Dict[str, pd.DataFrame]) -> pd.DataFrame:
        """聚合多个特征的相关性矩阵"""
        if not correlation_results:
            return pd.DataFrame()

        # 计算平均相关性
        matrices = list(correlation_results.values())
        avg_matrix = sum(matrices) / len(matrices)

        return avg_matrix

    def _analyze_pair_lead_lag(self, series1: pd.Series, series2: pd.Series,
                              symbol1: str, symbol2: str, max_lag: int,
                              method: LeadLagMethod) -> Optional[LeadLagResult]:
        """分析两个序列的领先滞后关系"""
        try:
            if method == LeadLagMethod.CROSS_CORRELATION:
                return self._cross_correlation_analysis(series1, series2, symbol1, symbol2, max_lag)
            else:
                # 默认使用交叉相关
                return self._cross_correlation_analysis(series1, series2, symbol1, symbol2, max_lag)
        except Exception as e:
            self.logger.warning(f"领先滞后分析失败 {symbol1}-{symbol2}: {e}")
            return None

    def _cross_correlation_analysis(self, series1: pd.Series, series2: pd.Series,
                                   symbol1: str, symbol2: str, max_lag: int) -> Optional[LeadLagResult]:
        """交叉相关分析"""
        # 对齐数据
        aligned_data = pd.concat([series1, series2], axis=1, join='inner').dropna()
        if len(aligned_data) < max_lag * 2:
            return None

        s1, s2 = aligned_data.iloc[:, 0], aligned_data.iloc[:, 1]

        # 计算不同滞后期的相关性
        correlations = []
        for lag in range(-max_lag, max_lag + 1):
            if lag == 0:
                corr = s1.corr(s2)
            elif lag > 0:
                # s1 领先 s2
                corr = s1[:-lag].corr(s2[lag:]) if lag < len(s1) else np.nan
            else:
                # s2 领先 s1
                corr = s1[-lag:].corr(s2[:lag]) if -lag < len(s2) else np.nan

            correlations.append((lag, corr))

        # 找到最大相关性
        valid_correlations = [(lag, corr) for lag, corr in correlations if not np.isnan(corr)]
        if not valid_correlations:
            return None

        best_lag, best_corr = max(valid_correlations, key=lambda x: abs(x[1]))

        # 判断显著性
        if abs(best_corr) < 0.3:  # 相关性阈值
            return None

        # 确定领先者和滞后者
        if best_lag > 0:
            leader, follower = symbol1, symbol2
            lag_periods = best_lag
        elif best_lag < 0:
            leader, follower = symbol2, symbol1
            lag_periods = -best_lag
        else:
            # 同步关系，选择相关性更强的方向
            leader, follower = symbol1, symbol2
            lag_periods = 0

        # 计算置信度（基于相关性强度）
        confidence = min(abs(best_corr), 1.0)

        return LeadLagResult(
            leader=leader,
            follower=follower,
            lag_periods=lag_periods,
            strength=abs(best_corr),
            confidence=confidence,
            method=LeadLagMethod.CROSS_CORRELATION
        )

    def _build_sentiment_indices(self, symbol_data: Dict[str, pd.DataFrame],
                               sentiment_features: List[str]) -> Dict[str, pd.Series]:
        """构建情绪指数"""
        sentiment_indices = {}

        for symbol, data in symbol_data.items():
            # 选择可用的情绪特征
            available_features = [f for f in sentiment_features if f in data.columns]

            if available_features:
                # 标准化特征
                scaler = StandardScaler()
                scaled_features = scaler.fit_transform(data[available_features].fillna(0))

                # 使用PCA降维到一个情绪指数
                pca = PCA(n_components=1)
                sentiment_index = pca.fit_transform(scaled_features).flatten()

                # 创建时间序列
                if 'timestamp' in data.columns:
                    index = pd.to_datetime(data['timestamp'])
                else:
                    index = data.index

                sentiment_indices[symbol] = pd.Series(sentiment_index, index=index)

        return sentiment_indices

    def _analyze_sentiment_contagion_network(self, sentiment_indices: Dict[str, pd.Series]) -> nx.Graph:
        """分析情绪传导网络"""
        G = nx.Graph()

        # 添加节点
        for symbol in sentiment_indices.keys():
            G.add_node(symbol)

        # 计算情绪传导强度
        symbols = list(sentiment_indices.keys())
        for i, symbol1 in enumerate(symbols):
            for j, symbol2 in enumerate(symbols[i+1:], i+1):
                # 对齐数据
                aligned_data = pd.concat([
                    sentiment_indices[symbol1],
                    sentiment_indices[symbol2]
                ], axis=1, join='inner').dropna()

                if len(aligned_data) > 20:  # 最小数据量要求
                    # 计算相关性
                    corr = aligned_data.iloc[:, 0].corr(aligned_data.iloc[:, 1])

                    # 如果相关性显著，添加边
                    if abs(corr) > 0.3:
                        G.add_edge(symbol1, symbol2, weight=abs(corr), correlation=corr)

        return G

    def _identify_contagion_centers(self, network: nx.Graph) -> List[Tuple[str, float]]:
        """识别情绪传导中心"""
        if network.number_of_nodes() == 0:
            return []

        # 计算中心性指标
        degree_centrality = nx.degree_centrality(network)
        betweenness_centrality = nx.betweenness_centrality(network)
        eigenvector_centrality = nx.eigenvector_centrality(network, max_iter=1000)

        # 综合评分
        centrality_scores = {}
        for node in network.nodes():
            score = (
                degree_centrality.get(node, 0) * 0.4 +
                betweenness_centrality.get(node, 0) * 0.3 +
                eigenvector_centrality.get(node, 0) * 0.3
            )
            centrality_scores[node] = score

        # 排序返回
        sorted_centers = sorted(centrality_scores.items(), key=lambda x: x[1], reverse=True)
        return sorted_centers

    def _calculate_contagion_strength(self, sentiment_indices: Dict[str, pd.Series]) -> float:
        """计算整体传导强度"""
        if len(sentiment_indices) < 2:
            return 0.0

        # 计算所有币种对的平均相关性
        correlations = []
        symbols = list(sentiment_indices.keys())

        for i, symbol1 in enumerate(symbols):
            for j, symbol2 in enumerate(symbols[i+1:], i+1):
                aligned_data = pd.concat([
                    sentiment_indices[symbol1],
                    sentiment_indices[symbol2]
                ], axis=1, join='inner').dropna()

                if len(aligned_data) > 10:
                    corr = aligned_data.iloc[:, 0].corr(aligned_data.iloc[:, 1])
                    if not np.isnan(corr):
                        correlations.append(abs(corr))

        return np.mean(correlations) if correlations else 0.0

    def _calculate_network_metrics(self, network: nx.Graph) -> Dict[str, float]:
        """计算网络指标"""
        if network.number_of_nodes() == 0:
            return {}

        metrics = {
            'nodes': network.number_of_nodes(),
            'edges': network.number_of_edges(),
            'density': nx.density(network),
            'average_clustering': nx.average_clustering(network),
        }

        # 连通性指标
        if nx.is_connected(network):
            metrics['average_path_length'] = nx.average_shortest_path_length(network)
            metrics['diameter'] = nx.diameter(network)
        else:
            # 对于非连通图，计算最大连通分量的指标
            largest_cc = max(nx.connected_components(network), key=len)
            subgraph = network.subgraph(largest_cc)
            if len(largest_cc) > 1:
                metrics['largest_component_size'] = len(largest_cc)
                metrics['largest_component_path_length'] = nx.average_shortest_path_length(subgraph)

        return metrics

    def _build_market_feature_matrix(self, symbol_data: Dict[str, pd.DataFrame],
                                   features: List[str]) -> pd.DataFrame:
        """构建市场特征矩阵"""
        feature_matrix = []
        timestamps = []

        # 获取共同的时间戳
        common_timestamps = None
        for symbol, data in symbol_data.items():
            if 'timestamp' in data.columns:
                ts = pd.to_datetime(data['timestamp'])
            else:
                ts = data.index

            if common_timestamps is None:
                common_timestamps = set(ts)
            else:
                common_timestamps = common_timestamps.intersection(set(ts))

        common_timestamps = sorted(list(common_timestamps))

        # 为每个时间点构建特征向量
        for timestamp in common_timestamps:
            feature_vector = []

            for symbol in self.symbols:
                if symbol in symbol_data:
                    data = symbol_data[symbol]
                    if 'timestamp' in data.columns:
                        data = data.set_index('timestamp')

                    # 获取该时间点的数据
                    try:
                        row_data = data.loc[timestamp]
                        for feature in features:
                            if feature in row_data:
                                feature_vector.append(row_data[feature])
                            else:
                                feature_vector.append(0.0)
                    except KeyError:
                        # 如果没有该时间点的数据，用0填充
                        feature_vector.extend([0.0] * len(features))

            if feature_vector:
                feature_matrix.append(feature_vector)
                timestamps.append(timestamp)

        # 创建DataFrame
        column_names = []
        for symbol in self.symbols:
            for feature in features:
                column_names.append(f"{symbol}_{feature}")

        return pd.DataFrame(feature_matrix, index=timestamps, columns=column_names)

    def _cluster_market_regimes(self, feature_matrix: pd.DataFrame, n_regimes: int) -> np.ndarray:
        """使用聚类算法检测市场状态"""
        # 标准化特征
        scaler = StandardScaler()
        scaled_features = scaler.fit_transform(feature_matrix.fillna(0))

        # K-means聚类
        kmeans = KMeans(n_clusters=n_regimes, random_state=42, n_init=10)
        regimes = kmeans.fit_predict(scaled_features)

        return regimes

    def _analyze_regime_characteristics(self, regime_id: int, regime_data: pd.DataFrame,
                                      regime_timestamps: pd.Index) -> MarketRegimeResult:
        """分析市场状态特征"""
        # 计算状态特征
        characteristics = {}
        for column in regime_data.columns:
            characteristics[column] = {
                'mean': regime_data[column].mean(),
                'std': regime_data[column].std(),
                'min': regime_data[column].min(),
                'max': regime_data[column].max()
            }

        # 计算持续时间
        duration = len(regime_data)

        # 计算稳定性（方差的倒数）
        total_variance = regime_data.var().sum()
        stability = 1.0 / (total_variance + 1e-6)

        # 提取涉及的币种
        involved_symbols = list(set([col.split('_')[0] for col in regime_data.columns]))

        return MarketRegimeResult(
            regime_id=regime_id,
            symbols=involved_symbols,
            characteristics=characteristics,
            duration=duration,
            stability=stability
        )


def create_cross_symbol_analyzer(symbols: List[str]) -> CrossSymbolAnalyzer:
    """创建币种间分析器的便捷函数"""
    return CrossSymbolAnalyzer(symbols)
