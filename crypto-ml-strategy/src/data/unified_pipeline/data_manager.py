"""
Unified Data Manager
This module provides a high-level manager to orchestrate the entire data pipeline,
from loading raw data to creating optimized PyTorch DataLoaders.
"""

import asyncio
import logging
import time
import numpy as np
import pandas as pd
import pyarrow as pa
import pyarrow.parquet as pq
from pathlib import Path
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Tuple
from torch.utils.data import DataLoader

from .feature_engine import FeatureEngine
from .dataset import UnifiedCryptoDataset
from .factory import DataLoaderFactory
from src.utils.config import get_config_manager
from src.data.influxdb_client import InfluxDBDataClient
from src.data.java_data_service import JavaDataService
from src.middleware.validator import DataValidationMiddleware

logger = logging.getLogger(__name__)


import aiohttp


class DataManager:
    """
    Orchestrates the entire data processing pipeline, including feature engineering,
    dataset creation, and DataLoader generation.
    """

    def __init__(self, config: Dict[str, Any], project_root: Path):
        """
        Initializes the DataManager.

        Args:
            config: A configuration dictionary for data settings.
            project_root: The absolute path to the project root.
        """
        self.project_root = project_root
        self.logger = logger
        self.config_manager = get_config_manager(config_dir=project_root / 'config')  # 移除多余参数
        # 优先使用传入的配置，如果为空则从config_manager获取
        if config and any(config.values()):  # 检查config是否有实际内容
            self.config = config
        else:
            self.config = self.config_manager.get('data', {})

        self.logger.info(f"🔍 DataManager配置来源: {'传入参数' if config and any(config.values()) else 'config_manager'}")
        self.logger.info(f"🔍 DataManager配置内容: {self.config}")
        
        self.feature_engine = FeatureEngine(self.config.get('feature_engineering', {}))
        
        pipeline_config = self.config.get('pipeline', {})
        self.on_symbol_failure = pipeline_config.get('on_symbol_failure', 'skip')

        storage_config = self.config_manager.get('storage.directories', {})
        
        # Build absolute paths from the project root
        self.raw_dir = self.project_root / storage_config.get('raw', 'data/raw')
        self.processed_dir = self.project_root / storage_config.get('processed', 'data/processed')
        
        self.raw_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)
        
        # InfluxDB Client for historical data
        # 修复：优先使用传入的config字典，然后回退到文件
        influx_config = self.config.get('influxdb')
        if not isinstance(influx_config, dict) or not influx_config:
             self.logger.warning("InfluxDB configuration not found in provided config dict. Checking file-based ConfigManager.")
             influx_config = self.config_manager.get('storage.influxdb', self.config_manager.get('influxdb', {}))
        
        self.influx_client = InfluxDBDataClient(influx_config)
        
        # Java Data Service Client
        java_service_config = self.config_manager.get('java_service', {})
        self.java_service = JavaDataService(java_service_config)

        # Data Validation Middleware
        validator_config = self.config_manager.get('validator', {})
        self.validator = DataValidationMiddleware(validator_config) if validator_config.get('enabled', False) else None
        if self.validator:
            logger.info("DataValidationMiddleware is enabled.")
        else:
            logger.warning("DataValidationMiddleware is disabled.")
        
        self.logger = logger
        self.selected_features: Optional[List[str]] = None
        
        logger.info("DataManager initialized with error handling strategy: '%s'", self.on_symbol_failure)

    async def _fetch_and_validate_data(self, symbol, timeframe, start_time, end_time):
        """
        重构的数据获取方法 - 新的数据源优先级：
        1. InfluxDB (主要数据源)
        2. Java Service (备用数据源)
        3. 本地文件 (最后回退)
        """
        kline_data, depth_data, trade_data = None, None, None

        try:
            # 🥇 优先: InfluxDB (主要数据源)
            logger.info(f"🔍 尝试从InfluxDB获取数据: {symbol} {timeframe}")
            try:
                kline_data = await self.influx_client.fetch_historical_data(symbol, timeframe, start_time, end_time)
                if kline_data is not None and not kline_data.empty:
                    logger.info(f"✅ InfluxDB数据获取成功: {symbol} {timeframe} ({len(kline_data)} 条记录)")
                else:
                    logger.warning(f"⚠️ InfluxDB中未找到数据: {symbol} {timeframe}")
            except Exception as e:
                logger.warning(f"❌ InfluxDB数据获取失败: {symbol} {timeframe} - {e}")
                kline_data = None

            # 🥈 回退: Java Service (备用数据源)
            if kline_data is None or kline_data.empty:
                logger.info(f"🔄 InfluxDB数据不可用，尝试Java Service: {symbol} {timeframe}")
                if await self.java_service.is_available():
                    try:
                        kline_task = self.java_service.fetch_historical_data(symbol, timeframe, start_time, end_time)
                        kline_data = await kline_task
                        if kline_data is not None and not kline_data.empty:
                            logger.info(f"✅ Java Service数据获取成功: {symbol} {timeframe} ({len(kline_data)} 条记录)")
                        else:
                            logger.warning(f"⚠️ Java Service返回空数据: {symbol} {timeframe}")
                    except Exception as e:
                        logger.warning(f"❌ Java Service数据获取失败: {symbol} {timeframe} - {e}")
                        kline_data = None
                else:
                    logger.warning(f"⚠️ Java Service不可用: {symbol} {timeframe}")

            # 🥉 回退: 本地文件 (最后回退)
            if kline_data is None or kline_data.empty:
                logger.info(f"🔄 远程数据源不可用，尝试本地文件: {symbol}")
                local_file_path = self.raw_dir / f"{symbol}.csv"
                if local_file_path.exists():
                    try:
                        kline_data = pd.read_csv(local_file_path, parse_dates=['timestamp'])
                        kline_data.set_index('timestamp', inplace=True)
                        logger.info(f"✅ 本地文件数据加载成功: {symbol} ({len(kline_data)} 条记录)")
                    except Exception as e:
                        logger.error(f"❌ 本地文件加载失败 {local_file_path}: {e}")
                        kline_data = None
                else:
                    logger.warning(f"⚠️ 本地文件不存在: {local_file_path}")


            # Ensure dataframe has a DatetimeIndex before validation
            if kline_data is not None and not kline_data.empty:
                if not isinstance(kline_data.index, pd.DatetimeIndex) and 'timestamp' in kline_data.columns:
                    logger.info("Ensuring DatetimeIndex from 'timestamp' column before validation.")
                    kline_data['timestamp'] = pd.to_datetime(kline_data['timestamp'])
                    kline_data.set_index('timestamp', inplace=True)

            # 验证
            if kline_data is not None and not kline_data.empty and self.validator:
                logger.info(f"Validating kline data for {symbol}...")
                kline_data = self.validator.process(kline_data)
                if kline_data.empty:
                    logger.warning(f"Kline data for {symbol} was rejected by the validator.")
                    return symbol, None, None, None

            if kline_data is not None and not kline_data.empty:
                logger.info(f"Successfully fetched and validated data for {symbol}.")
            else:
                kline_data = None

        except Exception as e:
            logger.warning(f"Data fetch/validation failed for {symbol}: {e}. Returning empty data.", exc_info=True)
            kline_data, depth_data, trade_data = None, None, None
            
        # 修复: 确保始终返回元组
        return symbol, kline_data, depth_data, trade_data


    async def initialize_connections(self):
        """Initializes async connections, e.g., to InfluxDB and Java Service."""
        self.logger.error("DEBUG: DataManager.initialize_connections ENTERED")
        if self.influx_client:
            self.logger.error("DEBUG: Connecting to InfluxDB...")
            try:
                await self.influx_client.connect()
                self.logger.error("DEBUG: InfluxDB connection completed.")
            except Exception as e:
                self.logger.warning(f"Could not connect to InfluxDB, proceeding without it. Reason: {e}")
        if self.java_service:
            self.logger.error("DEBUG: Testing connection to Java service...")
            await self.java_service.test_connection()
            self.logger.error("DEBUG: Java service connection test completed.")
        self.logger.error("DEBUG: DataManager.initialize_connections EXITED")


    async def process_symbol_data(
        self,
        symbol: str,
        raw_data: pd.DataFrame,
        depth_data: Optional[pd.DataFrame] = None,
        trade_data: Optional[pd.DataFrame] = None,
        timeframe: str = "1h"  # 添加timeframe参数
    ) -> Optional[pd.DataFrame]:
        """
        Processes data for a single symbol, from loading to feature engineering.

        Args:
            symbol: The crypto symbol (e.g., 'BTCUSDT').
            raw_data: The raw kline dataframe.
            depth_data: Optional dataframe with raw depth data.
            trade_data: Optional dataframe with raw trade data.

        Returns:
            A pandas DataFrame with engineered features, or None if processing fails.
        """
        # 从配置中获取最小数据点要求，默认为50（降低要求）
        MIN_DATA_POINTS = self.config.get('min_data_points', 50)

        try:
            if len(raw_data) < MIN_DATA_POINTS:
                self.logger.warning(
                    f"Skipping symbol {symbol} due to insufficient data. "
                    f"Found {len(raw_data)} records, but require at least {MIN_DATA_POINTS} "
                    "for reliable feature engineering."
                )

                # 如果启用了生成数据回退，尝试加载生成的测试数据
                use_generated = self.config.get('use_generated_data', False)
                self.logger.info(f"🔍 检查生成数据配置: use_generated_data = {use_generated}")
                self.logger.info(f"🔍 完整配置: {self.config}")

                if use_generated:
                    self.logger.info(f"🔄 尝试加载生成的测试数据: {symbol} {timeframe}")
                    generated_data = await self._load_generated_data(symbol, timeframe)
                    if generated_data is not None and len(generated_data) >= MIN_DATA_POINTS:
                        self.logger.info(f"✅ 使用生成的测试数据: {symbol} {timeframe} ({len(generated_data)} 条记录)")
                        raw_data = generated_data
                    else:
                        self.logger.warning(f"❌ 生成的测试数据不足或不存在: {symbol} {timeframe}")
                        return None
                else:
                    self.logger.warning("⚠️ 生成数据回退未启用，跳过符号")
                    return None

            # 0. Pre-emptive Data Cleaning
            # This is a critical defensive step. It ensures that any NaNs introduced
            # by the data loading process (e.g., from 'coerce' errors) are handled
            # before they can poison the feature engineering pipeline.
            raw_data.ffill(inplace=True)
            raw_data.bfill(inplace=True)

            # 1. Preserve original index
            original_index = raw_data.index

            # 2. Clean Data & Engineer Features
            features_df = self.feature_engine.create_all_features(
                raw_data, 
                depth_data=depth_data, 
                trade_data=trade_data
            )
            
            # CRITICAL FIX: Ensure index is preserved after feature engineering
            features_df.index = original_index

            # 4. Create Labels (simple example, can be customized)
            features_df['target'] = (features_df['close'].shift(-1) > features_df['close']).astype(int)
            
            features_df.dropna(subset=['target'], inplace=True)

            # Final check to ensure the dataframe is not empty after processing
            if features_df.empty:
                self.logger.warning(f"No data remaining for {symbol} after feature engineering and cleaning. Skipping symbol.")
                return None

            logger.info(f"Successfully processed data for {symbol}, shape: {features_df.shape}")
            
            return features_df

        except Exception:
            # Use a more robust way to capture and display the error
            import traceback
            tb_str = traceback.format_exc()
            error_msg = f"--- CRITICAL FAILURE in process_symbol_data for {symbol} ---\n{tb_str}"
            print(error_msg) # Use print as a fallback for logging
            self.logger.error(error_msg)
            if self.on_symbol_failure == 'abort':
                raise
            return None

    async def create_unified_dataset(
        self, symbols: List[str], timeframes: List[str] = ['1h'], force_refresh: bool = False, days: Optional[int] = None,
    ) -> Optional[str]:
        """
        Generates and merges datasets for multiple symbols and timeframes into a partitioned Parquet dataset.

        Args:
            symbols: A list of crypto symbols.
            timeframes: A list of timeframes (e.g., ['1h', '4h', '1d']).
            force_refresh: If True, forces re-processing for all symbols.
            days: The number of days of recent data to load for each symbol.

        Returns:
            The path to the root directory of the partitioned Parquet dataset, or None on failure.
        """
        total_start_time = time.time()
        logger.info(f"START: Unified dataset creation for symbols: {symbols} and timeframes: {timeframes}")

        all_timeframe_dfs = []

        for timeframe in timeframes:
            logger.info(f"--- Processing timeframe: {timeframe} ---")
            
            start_time = datetime.now(timezone.utc) - pd.to_timedelta(days, unit='d') if days else None
            end_time = datetime.now(timezone.utc)

            # This entire block is now replaced by the new helper method
            all_symbol_data_results = await asyncio.gather(
                *[self._fetch_and_validate_data(s, timeframe, start_time, end_time) for s in symbols]
            )

            valid_symbol_data = [res for res in all_symbol_data_results if res[1] is not None and not res[1].empty]
            
            if not valid_symbol_data:
                logger.warning(f"Failed to fetch kline data for ANY symbol for timeframe {timeframe}. Skipping timeframe.")
                continue

            process_tasks = [self.process_symbol_data(symbol, kline_df, depth_df, trade_df, timeframe) for symbol, kline_df, depth_df, trade_df in valid_symbol_data]
            processed_dfs_results = await asyncio.gather(*process_tasks)

            timeframe_features_list = []
            for (symbol, _, _, _), df in zip(valid_symbol_data, processed_dfs_results):
                if df is not None and not df.empty:
                    target_col = df['target']
                    df = df.drop(columns=['target'])
                    renamed_df = df.add_suffix(f"_{timeframe}")
                    renamed_df['target'] = target_col
                    renamed_df['symbol'] = symbol 
                    timeframe_features_list.append(renamed_df)
            
            if timeframe_features_list:
                timeframe_full_df = pd.concat(timeframe_features_list)
                all_timeframe_dfs.append(timeframe_full_df)
            else:
                logger.warning(f"No data was processed for any symbol for timeframe {timeframe}.")

        if not all_timeframe_dfs:
            logger.error("No data could be processed for any timeframe. Aborting dataset creation.")
            return None

        logger.info(f"STEP 3: Merging data from {len(all_timeframe_dfs)} timeframes...")
        base_df = all_timeframe_dfs[0]
        merged_df = base_df
        
        if len(all_timeframe_dfs) > 1:
            final_merged_list = []
            all_symbols = merged_df['symbol'].unique()

            for symbol in all_symbols:
                symbol_base_df = merged_df[merged_df['symbol'] == symbol].sort_index()

                for i in range(1, len(all_timeframe_dfs)):
                    other_df = all_timeframe_dfs[i]
                    symbol_other_df = other_df[other_df['symbol'] == symbol].sort_index()
                    cols_to_drop = ['target', 'symbol']
                    symbol_other_df = symbol_other_df.drop(columns=[c for c in cols_to_drop if c in symbol_other_df.columns])
                    
                    symbol_base_df = pd.merge_asof(
                        symbol_base_df, 
                        symbol_other_df, 
                        left_index=True, 
                        right_index=True, 
                        direction='backward'
                    )
                final_merged_list.append(symbol_base_df)
            
            merged_df = pd.concat(final_merged_list)

        # 在进行任何进一步操作之前，确保索引是 DatetimeIndex
        if not isinstance(merged_df.index, pd.DatetimeIndex):
            # 尝试从 'timestamp' 列（如果存在）恢复索引
            if 'timestamp' in merged_df.columns:
                merged_df['timestamp'] = pd.to_datetime(merged_df['timestamp'])
                merged_df = merged_df.set_index('timestamp')
                logger.warning("Merged DataFrame index was not DatetimeIndex, but was restored from 'timestamp' column.")
            else:
                logger.error("CRITICAL: Merged DataFrame index is lost and cannot be restored. Aborting.")
                return None


        logger.info(f"STEP 5: Converting final merged DataFrame to Arrow Table...")
        
        if merged_df.empty:
            logger.error("Merged DataFrame is empty. Aborting dataset creation.")
            return None

        merged_df['year'] = merged_df.index.year
        merged_df['month'] = merged_df.index.month
        merged_df = merged_df.loc[:, ~merged_df.columns.duplicated()]

        try:
            # --- FEATURE SELECTION on the final merged dataframe ---
            logger.info("STEP 5.5: Performing feature selection on the unified dataset...")
            df = merged_df
            if 'target' not in df.columns or df['target'].isnull().all():
                logger.error("Target column is missing or all NaN. Cannot perform feature selection.")
                feature_columns = df.select_dtypes(include=np.number).columns.drop(['year', 'month'], errors='ignore').tolist()
                self.selected_features = [col for col in feature_columns if col not in ['symbol']]
                final_df = df
            else:
                df.dropna(subset=['target'], inplace=True)
                labels = df['target']
                non_feature_cols = ['target', 'year', 'month', 'symbol']
                feature_columns = [col for col in df.columns if col not in non_feature_cols and df[col].dtype in [np.int64, np.float64]]
                features_df = df[feature_columns]
                selection_config = self.config.get('feature_selection', {})
                k = selection_config.get('k', 50)
                logger.info(f"Running feature selection to select top {k} features...")
                selected_features_df, selected_feature_names = self.feature_engine.select_features(features=features_df, labels=labels, k=k)
                self.selected_features = selected_feature_names
                logger.info(f"Feature selection complete. Selected {len(self.selected_features)} features.")
                metadata_cols = ['symbol', 'year', 'month', 'target']
                selected_features_df_indexed = pd.DataFrame(selected_features_df, index=features_df.index, columns=selected_feature_names)
                final_df = pd.concat([df[metadata_cols], selected_features_df_indexed], axis=1)
                final_df.dropna(subset=['target'], inplace=True)
            
            combined_table = pa.Table.from_pandas(final_df, preserve_index=True)
            # --- END FEATURE SELECTION ---

            dataset_root_path = self.processed_dir
            pq.write_to_dataset(
                combined_table,
                root_path=dataset_root_path,
                partition_cols=['symbol', 'year', 'month'],
                existing_data_behavior='overwrite_or_ignore'
            )
            self.config_manager.set('data.unified_dataset_path', str(dataset_root_path))
            logger.info(f"Unified dataset created successfully at: {dataset_root_path}")
            total_duration = time.time() - total_start_time
            logger.info(f"SUCCESS: Unified dataset creation finished in {total_duration:.4f} seconds.")
            return str(dataset_root_path)
        except Exception as e:
            logger.error(f"Failed to merge and save the partitioned dataset: {e}", exc_info=True)
            return None

    def create_dataloaders_from_file(
        self,
        dataset_path: Path,
        data_split: Tuple[float, float, float] = (0.8, 0.1, 0.1),
        **kwargs,
    ) -> Tuple[Optional[DataLoader], Optional[DataLoader], Optional[DataLoader]]:
        """
        Creates train, validation, and test DataLoaders from a processed Parquet file.

        Args:
            dataset_path: Path to the processed Parquet file.
            data_split: A tuple indicating the (train, validation, test) split ratios.
            **kwargs: Additional arguments for the DataLoaderFactory.

        Returns:
            A tuple containing the (train_loader, val_loader, test_loader).
        """
        if not dataset_path.exists():
            logger.error(f"Dataset file not found: {dataset_path}")
            return None, None, None

        try:
            df = pd.read_parquet(dataset_path)
            
            # Robust label column handling
            if df.empty:
                logger.error("DataFrame is empty! Cannot create dataloaders.")
                return None, None, None
                
            if 'target' in df.columns:
                labels = df['target'].values
                feature_columns = df.select_dtypes(include=np.number).columns.drop('target', errors='ignore').tolist()
            else:
                logger.warning("'target' column not found. Using last column as labels.")
                if len(df.columns) > 0:
                    labels = df.iloc[:, -1].values
                    feature_columns = df.columns[:-1].tolist()
                else:
                    logger.error("DataFrame has no columns! Cannot create dataloaders.")
                    return None, None, None
            
            if len(feature_columns) == 0:
                logger.error("No feature columns found! Cannot create dataloaders.")
                return None, None, None
                
            features = df[feature_columns].values

            # Split data
            n_samples = len(features)
            train_end = int(n_samples * data_split[0])
            val_end = train_end + int(n_samples * data_split[1])

            train_features, val_features, test_features = np.split(features, [train_end, val_end])
            train_labels, val_labels, test_labels = np.split(labels, [train_end, val_end])

            # Create Datasets
            train_dataset = UnifiedCryptoDataset(features=train_features, labels=train_labels, data_type='vector')
            val_dataset = UnifiedCryptoDataset(features=val_features, labels=val_labels, data_type='vector')
            test_dataset = UnifiedCryptoDataset(features=test_features, labels=test_labels, data_type='vector')

            # Create DataLoaders using the factory
            train_loader = DataLoaderFactory.create(train_dataset, shuffle=True, **kwargs)
            val_loader = DataLoaderFactory.create(val_dataset, shuffle=False, **kwargs)
            test_loader = DataLoaderFactory.create(test_dataset, shuffle=False, **kwargs)

            logger.info("  DataLoaders created successfully.")
            return train_loader, val_loader, test_loader

        except Exception as e:
            logger.error(f"Failed to create DataLoaders: {e}", exc_info=True)
            return None, None, None

    async def run_full_pipeline(
        self,
        symbols: List[str],
        timeframes: List[str] = ['1h'],
        force_refresh: bool = False,
        days: Optional[int] = None,
        dataloader_kwargs: Optional[Dict[str, Any]] = None,
    ) -> Tuple[Optional[DataLoader], Optional[DataLoader], Optional[DataLoader]]:
        """
        Runs the full pipeline: process symbols, create a unified dataset, and generate DataLoaders.

        Args:
            symbols: List of crypto symbols to process.
            timeframes: List of timeframes to process.
            force_refresh: If True, forces re-processing of all data.
            days: The number of days of recent data to load.
            dataloader_kwargs: Dictionary of arguments for the DataLoaderFactory.

        Returns:
            A tuple of (train_loader, val_loader, test_loader).
        """
        dataset_path = await self.create_unified_dataset(symbols, timeframes, force_refresh, days)
        
        if dataset_path:
            # Note: create_dataloaders_from_file expects a Path object
            return self.create_dataloaders_from_file(Path(dataset_path), **(dataloader_kwargs or {}))
        
        return None, None, None

    async def _load_generated_data(self, symbol: str, timeframe: str) -> Optional[pd.DataFrame]:
        """加载生成的测试数据"""
        try:
            import os
            from pathlib import Path

            # 构建文件路径 - 使用绝对路径
            project_root = Path(__file__).parent.parent.parent.parent
            data_dir = project_root / 'data' / 'generated'
            filename = f"{symbol}_{timeframe}_test_data.csv"
            filepath = data_dir / filename

            self.logger.info(f"🔍 查找生成数据文件: {filepath}")

            if not filepath.exists():
                self.logger.warning(f"生成的测试数据文件不存在: {filepath}")
                return None

            # 加载CSV数据
            df = pd.read_csv(filepath)
            self.logger.info(f"📊 原始数据形状: {df.shape}")

            # 确保时间戳列为datetime类型
            if 'timestamp' in df.columns:
                df['timestamp'] = pd.to_datetime(df['timestamp'])
                df.set_index('timestamp', inplace=True)
            elif 'open_time' in df.columns:
                df['open_time'] = pd.to_datetime(df['open_time'])
                df.set_index('open_time', inplace=True)
                df.index.name = 'timestamp'

            # 重命名列以匹配预期格式
            column_mapping = {
                'open_price': 'open',
                'high_price': 'high',
                'low_price': 'low',
                'close_price': 'close',
                'quote_volume': 'quote_asset_volume',
                'taker_buy_volume': 'taker_buy_base_asset_volume',
                'taker_buy_quote_volume': 'taker_buy_quote_asset_volume',
                'trade_count': 'count'
            }

            for old_col, new_col in column_mapping.items():
                if old_col in df.columns:
                    df[new_col] = df[old_col]

            # 确保必需的列存在
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in required_columns:
                if col not in df.columns:
                    self.logger.error(f"缺少必需列: {col}")
                    return None

            self.logger.info(f"✅ 成功加载生成的测试数据: {filepath} ({len(df)} 条记录)")
            self.logger.info(f"📋 数据列: {list(df.columns)}")
            return df

        except Exception as e:
            self.logger.error(f"加载生成数据失败 {symbol} {timeframe}: {e}")
            import traceback
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            return None
