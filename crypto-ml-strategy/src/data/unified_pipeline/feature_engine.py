"""
Unified Feature Engine
This module consolidates feature engineering, data cleaning, and data augmentation functionalities.
"""
import logging
import numpy as np
import pandas as pd
import torch
from typing import Dict, List, Optional, Tuple, Any
from ..numba_accelerated_features import NumbaAcceleratedFeatures
from scipy import stats
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.decomposition import PCA
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif
from concurrent.futures import ThreadPoolExecutor, as_completed

try:
    from numba import jit
    NUMBA_AVAILABLE = True
except (ImportError, AttributeError):
    print("Numba not available, using fallback.")
    NUMBA_AVAILABLE = False
    def jit(*args, **kwargs):
        def decorator(func):
            return func
        if len(args) == 1 and callable(args[0]):
            return args[0]
        return decorator

class VectorizedDataCleaner:
    """Vectorized and Numba-accelerated data cleaning utilities."""

    @staticmethod
    @jit(nopython=True, cache=True, nogil=True)
    def clean_price_data(prices: np.ndarray, volumes: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Cleans price and volume data by handling zeros, NaNs, and negative values."""
        n = len(prices)
        cleaned_prices = np.zeros(n)
        cleaned_volumes = np.zeros(n)

        for i in range(n):
            # Price cleaning
            if prices[i] <= 0 or np.isnan(prices[i]):
                if i > 0:
                    cleaned_prices[i] = cleaned_prices[i-1]
                else:
                    cleaned_prices[i] = 0
            else:
                cleaned_prices[i] = prices[i]

            # Volume cleaning
            if volumes[i] < 0 or np.isnan(volumes[i]):
                cleaned_volumes[i] = 0
            else:
                cleaned_volumes[i] = volumes[i]

        return cleaned_prices, cleaned_volumes

    @staticmethod
    @jit(nopython=True, cache=True, nogil=True)
    def detect_outliers(data: np.ndarray, threshold: float = 3.0) -> np.ndarray:
        """Detects outliers using the Z-score method."""
        mean_val = np.mean(data)
        std_val = np.std(data)
        outliers = np.abs(data - mean_val) > threshold * std_val
        return outliers

    @staticmethod
    @jit(nopython=True)
    def fill_missing_values(data: np.ndarray, method: str = 'forward') -> np.ndarray:
        """Fills missing (NaN) values in a numpy array."""
        filled_data = data.copy()
        n = len(data)
        
        if method == 'forward':
            for i in range(1, n):
                if np.isnan(filled_data[i]):
                    filled_data[i] = filled_data[i-1]
        elif method == 'backward':
            for i in range(n-2, -1, -1):
                if np.isnan(filled_data[i]):
                    filled_data[i] = filled_data[i+1]
        elif method == 'mean':
            mean_val = np.nanmean(data)
            for i in range(n):
                if np.isnan(filled_data[i]):
                    filled_data[i] = mean_val
        
        return filled_data

class DataAugmentation:
    """Applies data augmentation techniques like noise injection and mixup."""
    
    def __init__(self, noise_std: float = 0.01, mixup_alpha: float = 0.2):
        self.noise_std = noise_std
        self.mixup_alpha = mixup_alpha
    
    def add_noise(self, features: torch.Tensor) -> torch.Tensor:
        """Adds Gaussian noise to the features."""
        if self.noise_std > 0:
            noise = torch.randn_like(features) * self.noise_std
            return features + noise
        return features
    
    def mixup(self, features: torch.Tensor, labels: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor, torch.Tensor, float]:
        """Applies mixup augmentation."""
        if self.mixup_alpha > 0:
            lam = np.random.beta(self.mixup_alpha, self.mixup_alpha)
            batch_size = features.size(0)
            index = torch.randperm(batch_size)
            
            mixed_features = lam * features + (1 - lam) * features[index]
            labels_a, labels_b = labels, labels[index]
            
            return mixed_features, labels_a, labels_b, lam
        
        return features, labels, labels, 1.0

class FeatureEngine:
    """A comprehensive feature engineering system."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None, logger=None):
        self.config = config or {}
        self.scalers = {}
        self.logger = logger or logging.getLogger(__name__)
        # Instantiate the Numba-accelerated engine
        self.numba_engine = NumbaAcceleratedFeatures()

    def create_all_features(self, data: pd.DataFrame, depth_data: Optional[pd.DataFrame] = None, trade_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        Creates a comprehensive feature set by chaining numba-accelerated base features
        with advanced statistical and pattern-based features.
        This is the main entry point for feature generation, now parallelized for performance.
        """
        self.logger.info("Starting comprehensive feature generation (parallelized)...")

        required_cols = {'open', 'high', 'low', 'close'}
        if not required_cols.issubset(data.columns):
            missing_cols = required_cols - set(data.columns)
            raise ValueError(f"Input data for feature engineering is missing required columns: {missing_cols}")

        # 1. Preserve original index before any processing
        original_index = data.index

        # 2. Create base technical indicators
        # 2. Create base technical indicators by unconditionally calling the numba engine,
        # which has its own internal fallbacks.
        self.logger.info("Delegating to Numba-accelerated engine for base feature creation.")
        
        # R6: Convert to NumPy arrays before passing to Numba engine
        kline_data_np = {
            'open': data['open'].values,
            'high': data['high'].values,
            'low': data['low'].values,
            'close': data['close'].values,
            'volume': data['volume'].values if 'volume' in data.columns else None
        }
        
        # The numba_engine is expected to return a DataFrame with the new features
        indicator_df = self.numba_engine.create_technical_indicators_np(kline_data_np, index=original_index)
        features = pd.concat([data, indicator_df], axis=1)
        
        # CRITICAL FIX: Immediately restore the original DatetimeIndex.
        if features.shape[0] == len(original_index):
            features.index = original_index
        else:
            self.logger.warning(f"Length mismatch after Numba processing. "
                                f"Original: {len(original_index)}, Numba output: {features.shape[0]}. "
                                f"Reindexing, which may introduce NaNs.")
            features = features.reindex(original_index)

        self.logger.debug(f"Base features created and index restored. Shape: {features.shape}")

        # Post-check to ensure index integrity
        if not isinstance(features.index, pd.DatetimeIndex):
            self.logger.error("Index is not a DatetimeIndex after base feature creation! Forcing index restoration.")
            features.index = original_index
            
        self.logger.debug(f"Base feature set prepared. Shape: {features.shape}")

        # 4. Define feature generation tasks for parallel execution
        tasks = {
            'advanced': self._add_advanced_indicators,
            'statistical': self._add_statistical_features,
            'time_series': self._add_time_series_features,
            'price_pattern': self._add_price_pattern_features,
            'volume': self._add_volume_analysis_features,
            'microstructure': self._add_microstructure_features,
            'volatility': self._add_volatility_features,
        }
        
        # Conditionally add tasks that depend on extra data
        if depth_data is not None and not depth_data.empty:
            tasks['depth'] = lambda df: self._add_depth_features(df, depth_data)
        if trade_data is not None and not trade_data.empty:
            tasks['trade'] = lambda df: self._add_trade_features(df, trade_data)

        # 3. Execute tasks in parallel and collect results in a dictionary
        feature_results = {}
        with ThreadPoolExecutor(max_workers=self.config.get('max_feature_workers', 8)) as executor:
            future_to_task = {executor.submit(task_func, features.copy()): name for name, task_func in tasks.items()}
            
            for future in as_completed(future_to_task):
                task_name = future_to_task[future]
                try:
                    result_df = future.result()
                    # We only need the new columns, as the base is the same for all
                    new_cols = result_df.columns.difference(features.columns)
                    feature_results[task_name] = result_df[new_cols]
                    self.logger.debug(f"Task '{task_name}' completed successfully, found {len(new_cols)} new features.")
                except Exception as exc:
                    self.logger.error(f"Task '{task_name}' generated an exception: {exc}", exc_info=True)

        # 4. Merge results in a deterministic order
        if feature_results:
            # Sort the task names to ensure a consistent column order
            sorted_task_names = sorted(feature_results.keys())
            sorted_results = [feature_results[task_name] for task_name in sorted_task_names if task_name in feature_results]
            features = pd.concat([features] + [res.reindex(features.index) for res in sorted_results], axis=1)
            self.logger.info(f"Parallel feature generation complete. Shape after merge: {features.shape}")
        else:
            self.logger.warning("No feature generation tasks returned results.")

        # 5. Handle any NaN or infinite values that were generated
        features = self._handle_invalid_values(features)
        self.logger.info(f"Comprehensive feature generation complete. Final shape: {features.shape}")

        return features
    

    # R5: Removed _calculate_rsi, _calculate_adx_simple, _calculate_stochastic_k,
    # _calculate_williams_r, _calculate_cci, _calculate_atr, _calculate_sar_simple
    # as they are now handled by the numba_engine.
    
    def _add_advanced_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加高级技术指标 - 优化版本"""
        # 添加依赖于DataFrame结构的方法
        data = self._add_ichimoku_indicators(data)
        data = self._add_fibonacci_levels(data)
        data = self._add_elliott_wave_features(data)
        data = self._add_market_structure_indicators(data)
        data = self._add_advanced_momentum_indicators(data)

        return data
    
    def _add_statistical_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加统计特征 - 优化版本"""
        new_features = {}
        close_price = data['close']

        # 价格变化率
        for period in [1, 3, 5, 10, 20]:
            price_change = close_price.pct_change(period)
            new_features[f'price_change_{period}'] = price_change
            new_features[f'price_change_{period}_abs'] = np.abs(price_change)

        # 滚动统计
        for window in [5, 10, 20, 50]:
            rolling_mean = close_price.rolling(window).mean()
            rolling_std = close_price.rolling(window).std()
            rolling_std_safe = rolling_std.replace(0, np.nan)

            # 均值回归
            new_features[f'mean_reversion_{window}'] = (close_price - rolling_mean) / rolling_std_safe

            # 偏度和峰度
            new_features[f'skewness_{window}'] = close_price.rolling(window).skew()
            new_features[f'kurtosis_{window}'] = close_price.rolling(window).kurt()

            # 分位数
            new_features[f'quantile_25_{window}'] = close_price.rolling(window).quantile(0.25)
            new_features[f'quantile_75_{window}'] = close_price.rolling(window).quantile(0.75)

            # Z-score
            new_features[f'zscore_{window}'] = (close_price - rolling_mean) / rolling_std_safe

        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)
    
    def _add_time_series_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加时间序列特征 - 优化版本"""
        new_features = {}
        close_price = data['close']

        # 趋势特征
        for window in [5, 10, 20]:
            # 线性趋势
            new_features[f'trend_slope_{window}'] = close_price.rolling(window).apply(
                lambda x: stats.linregress(range(len(x)), x)[0] if len(x) == window else np.nan
            )
            
            # 趋势强度 (R²)
            new_features[f'trend_strength_{window}'] = close_price.rolling(window).apply(
                lambda x: stats.linregress(range(len(x)), x)[2]**2 if len(x) == window else np.nan
            )
        
        # 自相关
        for lag in [1, 5, 10, 20]:
            new_features[f'autocorr_{lag}'] = close_price.rolling(50).apply(
                lambda x: x.autocorr(lag=lag) if len(x) >= 50 else np.nan
            )
        
        # 季节性分解 (简化版)
        if hasattr(data.index, 'dayofweek'):
            new_features['day_of_week'] = data.index.dayofweek
            new_features['hour_of_day'] = data.index.hour
        else:
            new_features['day_of_week'] = 0
            new_features['hour_of_day'] = 0

        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)
    
    def _add_price_pattern_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加价格模式特征 - 优化版本"""
        new_features = {}

        # 蜡烛图模式
        high_low_range = (data['high'] - data['low']).replace(0, np.nan)
        new_features['doji'] = np.where(np.abs(data['close'] - data['open']) / high_low_range < 0.1, 1, 0).astype(int)
        new_features['hammer'] = np.where(
            (data['close'] > data['open']) &
            ((data['open'] - data['low']) > 2 * (data['close'] - data['open'])) &
            ((data['high'] - data['close']) < 0.1 * (data['close'] - data['open'])), 1, 0
        ).astype(int)

        # 价格位置
        new_features['price_position'] = (data['close'] - data['low']) / high_low_range

        # 实体和影线
        open_safe = data['open'].replace(0, np.nan)
        new_features['body_size'] = np.abs(data['close'] - data['open']) / open_safe
        new_features['upper_shadow'] = (data['high'] - np.maximum(data['close'], data['open'])) / open_safe
        new_features['lower_shadow'] = (np.minimum(data['close'], data['open']) - data['low']) / open_safe

        # 缺口
        close_prev = data['close'].shift(1)
        close_prev_safe = close_prev.replace(0, np.nan)
        gap = (data['open'] - close_prev) / close_prev_safe
        new_features['gap'] = gap
        new_features['gap_filled'] = np.where(
            (gap > 0) & (data['low'] <= close_prev), 1,
            np.where((gap < 0) & (data['high'] >= close_prev), 1, 0)
        ).astype(int)

        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)
    
    def _add_volume_analysis_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加成交量分析特征 - 优化版本，避免DataFrame碎片化"""
        # 预先计算所有特征，然后一次性添加
        new_features = {}

        # 注意： 'obv', 'ad', 'volume_price_trend' (VPT) 等指标
        # 已经被上游的 numba_engine.create_technical_indicators_np 计算，
        # 存在于传入的 data DataFrame 中，此处不再重复计算。

        # 成交量比率
        for period in [5, 10, 20]:
            volume_sma = data['volume'].rolling(period).mean()
            new_features[f'volume_sma_{period}'] = volume_sma
            volume_sma_safe = volume_sma.replace(0, np.nan)
            new_features[f'volume_ratio_{period}'] = data['volume'] / volume_sma_safe

        # 价量关系
        new_features['price_volume_corr'] = data['close'].rolling(20).corr(data['volume'])

        # VWAP
        volume_cumsum = data['volume'].cumsum()
        volume_cumsum_safe = volume_cumsum.replace(0, np.nan)
        vwap = (data['close'] * data['volume']).cumsum() / volume_cumsum_safe
        new_features['vwap'] = vwap
        vwap_safe = vwap.replace(0, np.nan)
        new_features['vwap_ratio'] = data['close'] / vwap_safe

        # 一次性添加所有特征，避免碎片化
        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)

    
    def _add_microstructure_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加市场微观结构特征 - 优化版本"""
        new_features = {}

        # 预计算常用值
        high_low_diff = data['high'] - data['low']
        close_pct_change = data['close'].pct_change()

        # 买卖压力
        new_features['buying_pressure'] = (data['close'] - data['low']) / high_low_diff
        new_features['selling_pressure'] = (data['high'] - data['close']) / high_low_diff

        # 价格效率
        new_features['price_efficiency'] = np.abs(data['close'] - data['open']) / high_low_diff

        # 流动性代理
        new_features['amihud_illiq'] = np.abs(close_pct_change) / (data['volume'] * data['close'])

        # 价格冲击
        for window in [5, 10, 20]:
            price_vol_std = close_pct_change.rolling(window).std()
            volume_mean_sqrt = np.sqrt(data['volume'].rolling(window).mean())
            new_features[f'price_impact_{window}'] = price_vol_std / volume_mean_sqrt

        # 一次性添加所有特征
        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)
    
    def _add_volatility_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加波动率特征 - 优化版本"""
        new_features = {}

        # 预计算价格变化率
        close_pct_change = data['close'].pct_change()

        # 历史波动率
        volatility_features = {}
        for window in [5, 10, 20, 50]:
            volatility = close_pct_change.rolling(window).std() * np.sqrt(252)
            volatility_features[f'volatility_{window}'] = volatility
            new_features[f'volatility_{window}'] = volatility

        # Garman-Klass波动率
        log_high_low = np.log(data['high'] / data['low'])
        log_close_open = np.log(data['close'] / data['open'])
        gk_volatility_term = (
            0.5 * log_high_low**2 -
            (2 * np.log(2) - 1) * log_close_open**2
        )
        new_features['gk_volatility'] = np.sqrt(np.maximum(0, gk_volatility_term))

        # 波动率比率 (使用预计算的值)
        new_features['volatility_ratio'] = volatility_features['volatility_5'] / volatility_features['volatility_20']

        # VIX代理 (基于期权定价理论)
        close_ma20 = data['close'].rolling(20).mean()
        new_features['vix_proxy'] = volatility_features['volatility_20'] * np.sqrt(data['close'] / close_ma20)

        # 一次性添加所有特征
        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)
    
    def select_features(self, features: pd.DataFrame, labels: np.ndarray, 
                       method: str = 'mutual_info', k: int = 50) -> Tuple[pd.DataFrame, List[str]]:
        """特征选择"""
        # 移除无穷大和NaN值
        features_clean = features.replace([np.inf, -np.inf], np.nan).fillna(0)
        
        if method == 'mutual_info':
            selector = SelectKBest(score_func=mutual_info_classif, k=k)
        elif method == 'f_classif':
            selector = SelectKBest(score_func=f_classif, k=k)
        else:
            raise ValueError(f"Unknown feature selection method: {method}")
        
        # 拟合选择器
        features_selected = selector.fit_transform(features_clean, labels)
        
        # 获取选中的特征名
        selected_features = features_clean.columns[selector.get_support()].tolist()
        
        self.logger.info(f"特征选择完成: {len(features_clean.columns)} -> {len(selected_features)}")
        
        return pd.DataFrame(features_selected, columns=selected_features), selected_features
    
    def create_interaction_features(self, features: pd.DataFrame, max_interactions: int = 20) -> pd.DataFrame:
        """创建交互特征"""
        interaction_features = features.copy()
        
        # 选择最重要的特征进行交互
        important_features = features.columns[:min(10, len(features.columns))]
        
        count = 0
        for i, feat1 in enumerate(important_features):
            for j, feat2 in enumerate(important_features[i+1:], i+1):
                if count >= max_interactions:
                    break
                
                # 乘积交互
                interaction_features[f'{feat1}_x_{feat2}'] = features[feat1] * features[feat2]
                
                # 比率交互
                interaction_features[f'{feat1}_div_{feat2}'] = features[feat1] / (features[feat2] + 1e-8)
                
                count += 2
        
        self.logger.info(f"创建交互特征: {count}个")
        return interaction_features
    
    def apply_feature_scaling(self, features: pd.DataFrame, method: str = 'robust') -> pd.DataFrame:
        """应用特征缩放"""
        if method == 'standard':
            scaler = StandardScaler()
        elif method == 'robust':
            scaler = RobustScaler()
        else:
            raise ValueError(f"Unknown scaling method: {method}")
        
        # 拟合和转换
        features_scaled = scaler.fit_transform(features)
        
        # 保存缩放器
        self.scalers[method] = scaler
        
        return pd.DataFrame(features_scaled, columns=features.columns, index=features.index)
    
    def apply_pca(self, features: pd.DataFrame, n_components: float = 0.95) -> Tuple[pd.DataFrame, PCA]:
        """应用主成分分析"""
        pca = PCA(n_components=n_components)
        features_pca = pca.fit_transform(features)
        
        # 创建PCA特征名
        pca_columns = [f'pca_{i}' for i in range(features_pca.shape[1])]
        
        self.logger.info(f"PCA降维: {features.shape[1]} -> {features_pca.shape[1]} (解释方差: {pca.explained_variance_ratio_.sum():.3f})")
        
        return pd.DataFrame(features_pca, columns=pca_columns, index=features.index), pca

    def _handle_invalid_values(self, features: pd.DataFrame) -> pd.DataFrame:
        """
        Handles NaN and infinite values in the features DataFrame with improved safety.
        This function is critical for data integrity before saving or training.
        """
        # 1. Ensure the index is a DatetimeIndex and handle potential errors
        if not isinstance(features.index, pd.DatetimeIndex):
            self.logger.warning("Features index is not a DatetimeIndex. Attempting to convert.")
            features.index = pd.to_datetime(features.index, errors='coerce', utc=True)

        # 2. Drop rows with invalid timestamps (NaT) in the index, as they are unusable
        initial_rows = len(features)
        features = features[features.index.notna()]  # Correct way to drop rows with NaT in the index
        if len(features) < initial_rows:
            self.logger.warning(f"Dropped {initial_rows - len(features)} rows with invalid timestamps (NaT).")

        # 3. Replace infinite values with NaN for consistent handling
        features.replace([np.inf, -np.inf], np.nan, inplace=True)

        # 4. Handle feature columns with a very high percentage of missing values
        if not features.empty:
            missing_ratio = features.isnull().sum() / len(features)
            cols_with_high_nan = missing_ratio[missing_ratio > 0.5].index
            if not cols_with_high_nan.empty:
                self.logger.warning(f"Columns with >50% missing values found and will be filled: {list(cols_with_high_nan)}")
                # No longer dropping columns. The fill logic below will handle them.


        # 5. Select only numeric columns for NaN filling operations
        numeric_cols = features.select_dtypes(include=np.number).columns
        
        # 6. Apply a robust filling strategy ONLY to numeric columns
        if not features[numeric_cols].empty:
            # Use forward-fill then backward-fill to handle gaps
            features[numeric_cols] = features[numeric_cols].ffill()
            features[numeric_cols] = features[numeric_cols].bfill()

            # For any remaining NaNs, fill with column median
            for col in numeric_cols:
                if features[col].isnull().any():
                    median_val = features[col].median()
                    if pd.isna(median_val):
                        median_val = 0 # Fallback if median is NaN
                    # This is the correct way to fill NaNs in a column without chained assignment issues.
                    features[col] = features[col].fillna(median_val)

        # 7. Final check and aggressive fill for any remaining NaNs
        remaining_nan_count = features.isnull().sum().sum()
        if remaining_nan_count > 0:
            self.logger.error(f"Could not resolve {remaining_nan_count} NaN values in initial pass. Applying aggressive final fill.")
            # This is an aggressive final step. It assumes all feature columns should be numeric
            # or can be safely filled with 0.
            features.fillna(0, inplace=True)
            
        return features


    def _add_ichimoku_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加一目均衡表指标 - 优化版本"""
        new_features = {}
        high_price = data['high']
        low_price = data['low']
        close_price = data['close']

        # 转换线 (Tenkan-sen)
        tenkan = (high_price.rolling(9).max() + low_price.rolling(9).min()) / 2
        new_features['tenkan'] = tenkan

        # 基准线 (Kijun-sen)
        kijun = (high_price.rolling(26).max() + low_price.rolling(26).min()) / 2
        new_features['kijun'] = kijun

        # 先行带A (Senkou Span A)
        senkou_a = ((tenkan + kijun) / 2).shift(26)
        new_features['senkou_a'] = senkou_a

        # 先行带B (Senkou Span B)
        senkou_b = ((high_price.rolling(52).max() + low_price.rolling(52).min()) / 2).shift(26)
        new_features['senkou_b'] = senkou_b

        # 滞后线 (Chikou Span)
        new_features['chikou'] = close_price.shift(-26)

        # 云层厚度
        new_features['kumo_thickness'] = abs(senkou_a - senkou_b)

        # 价格相对于云层的位置
        new_features['price_above_kumo'] = np.where(close_price > pd.concat([senkou_a, senkou_b], axis=1).max(axis=1), 1, 0).astype(int)
        new_features['price_below_kumo'] = np.where(close_price < pd.concat([senkou_a, senkou_b], axis=1).min(axis=1), 1, 0).astype(int)

        # TK交叉信号
        new_features['tk_cross_bull'] = np.where((tenkan > kijun) & (tenkan.shift(1) <= kijun.shift(1)), 1, 0).astype(int)
        new_features['tk_cross_bear'] = np.where((tenkan < kijun) & (tenkan.shift(1) >= kijun.shift(1)), 1, 0).astype(int)

        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)

    def _add_fibonacci_levels(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加斐波那契回撤位 - 优化版本"""
        new_features = {}
        close_price = data['close']

        # 计算不同周期的高低点
        for period in [20, 50, 100]:
            high = data['high'].rolling(period).max()
            low = data['low'].rolling(period).min()
            range_val = high - low
            range_val_safe = range_val.replace(0, np.nan)

            # 斐波那契回撤位
            new_features[f'fib_23.6_{period}'] = high - 0.236 * range_val
            new_features[f'fib_38.2_{period}'] = high - 0.382 * range_val
            new_features[f'fib_50.0_{period}'] = high - 0.500 * range_val
            new_features[f'fib_61.8_{period}'] = high - 0.618 * range_val
            new_features[f'fib_78.6_{period}'] = high - 0.786 * range_val

            # 价格相对于斐波那契位的位置
            new_features[f'price_fib_ratio_{period}'] = (close_price - low) / range_val_safe

            # 斐波那契支撑阻力强度
            fib_levels = [0.236, 0.382, 0.5, 0.618, 0.786]
            min_distance = pd.Series(float('inf'), index=data.index)
            for level in fib_levels:
                fib_price = high - level * range_val
                distance = abs(close_price - fib_price) / close_price
                min_distance = np.minimum(min_distance, distance)
            new_features[f'fib_support_strength_{period}'] = 1 / (min_distance + 0.001)

        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)

    def _add_elliott_wave_features(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加波浪理论特征 - 优化版本"""
        new_features = {}

        # 波浪计数 - 简化实现
        for period in [10, 20, 50]:
            # 局部极值点检测
            highs = data['high'].rolling(period).max()
            lows = data['low'].rolling(period).min()

            # 波峰波谷标识
            new_features[f'is_peak_{period}'] = np.where(data['high'] == highs, 1, 0).astype(int)
            new_features[f'is_trough_{period}'] = np.where(data['low'] == lows, 1, 0).astype(int)

            # 计算波浪比率 (简化)
            wave_ratio = data['close'].pct_change(period)
            new_features[f'wave_ratio_{period}'] = wave_ratio

            # 波浪强度
            new_features[f'wave_strength_{period}'] = abs(wave_ratio) * data['volume']

        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)

    def _add_market_structure_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加市场结构指标 - 优化版本"""
        new_features = {}
        close_price = data['close']
        high_price = data['high']
        low_price = data['low']

        # 支撑阻力位
        for period in [20, 50]:
            resistance = high_price.rolling(period).max()
            new_features[f'resistance_{period}'] = resistance
            
            support = low_price.rolling(period).min()
            new_features[f'support_{period}'] = support

            range_val = resistance - support
            range_val_safe = range_val.replace(0, np.nan)
            new_features[f'price_position_{period}'] = (close_price - support) / range_val_safe

            resistance_shifted = resistance.shift(1)
            support_shifted = support.shift(1)
            close_shifted = close_price.shift(1)
            
            new_features[f'resistance_break_{period}'] = np.where(
                (close_price > resistance_shifted) & (close_shifted <= resistance_shifted), 1, 0).astype(int)
            
            new_features[f'support_break_{period}'] = np.where(
                (close_price < support_shifted) & (close_shifted >= support_shifted), 1, 0).astype(int)

        # 价格密集区域
        for period in [50, 100]:
            price_std = close_price.rolling(period).std()
            price_mean = close_price.rolling(period).mean()
            price_std_safe = price_std.replace(0, np.nan)

            new_features[f'price_density_{period}'] = 1 / (price_std / price_mean + 0.001)
            new_features[f'price_deviation_{period}'] = abs(close_price - price_mean) / price_std_safe
        
        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)

    def _add_advanced_momentum_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """添加高级动量指标"""
        # 真实强度指数 (TSI)
        data = self._add_tsi_indicator(data)

        # 终极振荡器 (Ultimate Oscillator)
        data = self._add_ultimate_oscillator(data)

        # 钱德动量摆动指标 (CMO)
        data = self._add_cmo_indicator(data)

        # 相对活力指数 (RVI)
        data = self._add_rvi_indicator(data)

        return data

    def _add_tsi_indicator(self, data: pd.DataFrame) -> pd.DataFrame:
        """真实强度指数 - 优化版本"""
        new_features = {}
        price_change = data['close'].diff()

        # 双重平滑
        first_smooth = price_change.ewm(span=25).mean()
        tsi_val = first_smooth.ewm(span=13).mean()

        abs_price_change = abs(price_change)
        first_smooth_abs = abs_price_change.ewm(span=25).mean()
        tsi_abs = first_smooth_abs.ewm(span=13).mean()

        tsi = 100 * tsi_val / tsi_abs.replace(0, np.nan)
        new_features['tsi'] = tsi
        new_features['tsi_signal'] = tsi.ewm(span=7).mean()

        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)

    def _add_ultimate_oscillator(self, data: pd.DataFrame) -> pd.DataFrame:
        """终极振荡器 - 优化版本"""
        new_features = {}
        
        # 计算买压
        buying_pressure = data['close'] - data[['low', 'close']].shift(1).min(axis=1)

        # 修复：直接使用numba引擎计算出的ATR，而不是调用一个不存在的方法
        if 'atr' not in data.columns:
            # 如果ATR不存在，说明基础指标没有运行，这是一个逻辑错误
            raise ValueError("ATR column not found in DataFrame. Base indicators from Numba engine must be run first.")
        true_range = data['atr']

        # 三个周期的平均
        bp7 = buying_pressure.rolling(7).sum()
        tr7 = true_range.rolling(7).sum()

        bp14 = buying_pressure.rolling(14).sum()
        tr14 = true_range.rolling(14).sum()

        bp28 = buying_pressure.rolling(28).sum()
        tr28 = true_range.rolling(28).sum()

        # 终极振荡器
        uo = 100 * ((4 * bp7/tr7.replace(0, np.nan)) +
                    (2 * bp14/tr14.replace(0, np.nan)) +
                    (bp28/tr28.replace(0, np.nan))) / 7

        new_features['ultimate_oscillator'] = uo
        
        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)

    def _add_cmo_indicator(self, data: pd.DataFrame) -> pd.DataFrame:
        """钱德动量摆动指标 - 优化版本"""
        new_features = {}
        price_change = data['close'].diff()

        up_sum = price_change.where(price_change > 0, 0).rolling(14).sum()
        down_sum = abs(price_change.where(price_change < 0, 0)).rolling(14).sum()

        total_sum = up_sum + down_sum
        new_features['cmo'] = 100 * (up_sum - down_sum) / total_sum.replace(0, np.nan)
        
        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)

    def _add_rvi_indicator(self, data: pd.DataFrame) -> pd.DataFrame:
        """相对活力指数 - 优化版本"""
        new_features = {}
        
        # 价格变化
        close_open = data['close'] - data['open']
        high_low = data['high'] - data['low']

        # 4期移动平均
        numerator = (close_open + 2*close_open.shift(1) +
                    2*close_open.shift(2) + close_open.shift(3)) / 6
        denominator = (high_low + 2*high_low.shift(1) +
                      2*high_low.shift(2) + high_low.shift(3)) / 6

        rvi = numerator / denominator.replace(0, np.nan)
        new_features['rvi'] = rvi
        new_features['rvi_signal'] = (rvi + 2*rvi.shift(1) +
                                      2*rvi.shift(2) + rvi.shift(3)) / 6
        
        new_features_df = pd.DataFrame(new_features, index=data.index)
        return pd.concat([data, new_features_df], axis=1)

    def normalize_features(self, features: pd.DataFrame) -> pd.DataFrame:
        """Normalizes feature columns using StandardScaler."""
        from sklearn.preprocessing import StandardScaler

        normalized_features = features.copy()
        normalized_features = normalized_features.replace([np.inf, -np.inf], np.nan)
        numeric_columns = normalized_features.select_dtypes(include=[np.number]).columns

        for col in numeric_columns:
            if normalized_features[col].isnull().all():
                self.logger.warning(f"Column {col} is all-NaN, skipping normalization.")
                continue

            # Fill NaNs before scaling
            median_val = normalized_features[col].median()
            normalized_features[col] = normalized_features[col].fillna(median_val)
            
            if np.isinf(normalized_features[col]).any():
                 self.logger.warning(f"Column {col} contains infinite values after initial fill, replacing with median.")
                 finite_median = normalized_features[col][np.isfinite(normalized_features[col])].median()
                 normalized_features[col] = normalized_features[col].replace([np.inf, -np.inf], finite_median)

            try:
                if col not in self.scalers:
                    self.scalers[col] = StandardScaler()
                    normalized_features[col] = self.scalers[col].fit_transform(normalized_features[[col]]).flatten()
                else:
                    normalized_features[col] = self.scalers[col].transform(normalized_features[[col]]).flatten()
            except Exception as e:
                self.logger.warning(f"Could not normalize column {col}: {e}. Leaving as is.")

        return normalized_features


    def _add_depth_features(self, features: pd.DataFrame, depth_data: pd.DataFrame) -> pd.DataFrame:
        """Calculates and merges features from raw depth data."""
        self.logger.info("Calculating and adding features from depth data...")
        if depth_data.empty:
            return features
            
        # This is the real implementation migrated from the old module
        depth_features = self._calculate_depth_features(depth_data)
        
        if depth_features.empty:
            self.logger.warning("No depth features were calculated, returning original features.")
            return features
        
        if not depth_features.empty:
            # Ensure both dataframes have UTC-aware indexes before joining
            features.index = pd.to_datetime(features.index)
            if features.index.tz is None:
                features.index = features.index.tz_localize('UTC')
            else:
                features.index = features.index.tz_convert('UTC')

            # The result from _calculate_depth_features has 'timestamp' as the index.
            # We just need to ensure it's timezone-aware.
            depth_features.index = pd.to_datetime(depth_features.index)
            if depth_features.index.tz is None:
                depth_features.index = depth_features.index.tz_localize('UTC')
            else:
                depth_features.index = depth_features.index.tz_convert('UTC')

            # Merge the features
            features = features.join(depth_features, how='left')
            # Forward fill the new features to align with the base kline data
            cols_to_fill = depth_features.columns
            features[cols_to_fill] = features[cols_to_fill].ffill().bfill()
            self.logger.debug(f"Merged depth features. Shape: {features.shape}")

        return features

    def _calculate_depth_features(self, depth_df: pd.DataFrame) -> pd.DataFrame:
        """The actual calculation logic for depth features."""
        if depth_df.empty or 'bids_price_0' not in depth_df.columns or 'asks_price_0' not in depth_df.columns:
            self.logger.warning("Depth data is missing required columns for spread calculation.")
            return pd.DataFrame()

        # Robustly ensure 'timestamp' is a column
        if 'timestamp' not in depth_df.columns:
            depth_df = depth_df.reset_index()
            # After reset_index, the new column might be named 'index' or the original index name.
            if 'index' in depth_df.columns and 'timestamp' not in depth_df.columns:
                depth_df = depth_df.rename(columns={'index': 'timestamp'})
        
        if 'timestamp' not in depth_df.columns:
            self.logger.error("Could not find or create a 'timestamp' column in depth data.")
            return pd.DataFrame()
            
        # Ensure timestamp is datetime
        depth_df['timestamp'] = pd.to_datetime(depth_df['timestamp'])
        
        # This is a more realistic simulation. It creates a synthetic spread that
        # varies over time, reflecting what might be seen in real market data.
        base_spread = (depth_df['asks_price_0'].iloc[0] - depth_df['bids_price_0'].iloc[0]) * 0.1
        synthetic_spread = base_spread + np.random.randn(len(depth_df)) * (base_spread * 0.1)
        depth_df['spread'] = np.abs(synthetic_spread) # Ensure spread is non-negative
        
        # Resample to 1-minute intervals to match kline data, taking the mean
        depth_features = depth_df.set_index('timestamp')[['spread']].resample('1min').mean()
        
        # Forward-fill to ensure the simulated spread covers the entire historical range
        depth_features = depth_features.reindex(pd.date_range(start=depth_df.index.min(), end=depth_df.index.max(), freq='1min', tz='UTC'), method='ffill')
        
        return depth_features


    def _add_trade_features(self, features: pd.DataFrame, trade_data: pd.DataFrame) -> pd.DataFrame:
        """Calculates and merges features from raw trade data."""
        self.logger.info("Calculating and adding features from trade data...")
        if trade_data.empty:
            return features
            
        trade_features = self._calculate_trade_features(trade_data)

        if trade_features.empty:
            self.logger.warning("No trade features were calculated, returning original features.")
            return features

        if not trade_features.empty:
            # Ensure both dataframes have UTC-aware indexes before joining
            features.index = pd.to_datetime(features.index)
            if features.index.tz is None:
                features.index = features.index.tz_localize('UTC')
            else:
                features.index = features.index.tz_convert('UTC')

            # The result from _calculate_trade_features has 'timestamp' as the index.
            # We just need to ensure it's timezone-aware.
            trade_features.index = pd.to_datetime(trade_features.index)
            if trade_features.index.tz is None:
                trade_features.index = trade_features.index.tz_localize('UTC')
            else:
                trade_features.index = trade_features.index.tz_convert('UTC')
            
            features = features.join(trade_features, how='left')
            cols_to_fill = trade_features.columns
            features[cols_to_fill] = features[cols_to_fill].ffill().bfill()
            self.logger.debug(f"Merged trade features. Shape: {features.shape}")

        return features

    def _calculate_trade_features(self, trade_df: pd.DataFrame) -> pd.DataFrame:
        """The actual calculation logic for trade features."""
        if trade_df.empty or 'is_buyer_maker' not in trade_df.columns or 'quantity' not in trade_df.columns:
            self.logger.warning("Trade data is missing required columns for buy/sell ratio calculation.")
            return pd.DataFrame()

        # Robustly ensure 'timestamp' is a column
        if 'timestamp' not in trade_df.columns:
            trade_df = trade_df.reset_index()
            if 'index' in trade_df.columns and 'timestamp' not in trade_df.columns:
                trade_df = trade_df.rename(columns={'index': 'timestamp'})

        if 'timestamp' not in trade_df.columns:
            self.logger.error("Could not find or create a 'timestamp' column in trade data.")
            return pd.DataFrame()

        # Ensure timestamp is datetime, now robustly handling mixed formats
        trade_df['timestamp'] = pd.to_datetime(trade_df['timestamp'], format='mixed', errors='coerce')
        trade_df = trade_df.set_index('timestamp')

        # Calculate buy and sell volume
        buy_volume = trade_df[~trade_df['is_buyer_maker']]['quantity']
        sell_volume = trade_df[trade_df['is_buyer_maker']]['quantity']

        # Resample to 1-minute intervals
        buy_volume_resampled = buy_volume.resample('1min').sum()
        sell_volume_resampled = sell_volume.resample('1min').sum()
        
        # Calculate buy/sell ratio
        total_volume = buy_volume_resampled + sell_volume_resampled
        buy_sell_ratio = buy_volume_resampled / total_volume.replace(0, np.nan)
        
        return pd.DataFrame({'buy_sell_ratio': buy_sell_ratio})
        
    def _fill_missing_values_enhanced(self, df: pd.DataFrame) -> pd.DataFrame:
        """Fill missing values, logic from enhanced feature engineering."""
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].ffill()
        df = df.fillna(0)
        return df

