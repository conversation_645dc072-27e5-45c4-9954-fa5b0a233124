"""
DataLoader Factory
This module provides a factory class for creating optimized PyTorch DataLoaders.
"""

import torch
from torch.utils.data import DataLoader
from typing import Optional, Dict, Any
import psutil
import os

from .dataset import UnifiedCryptoDataset

class DataLoaderFactory:
    """A factory for creating high-performance, standardized DataLoaders."""

    @staticmethod
    def create(
        dataset: UnifiedCryptoDataset,
        batch_size: int = 32,
        shuffle: bool = True,
        num_workers: Optional[int] = None,
        pin_memory: bool = True,
        prefetch_factor: int = 4,
        persistent_workers: bool = True,
        drop_last: bool = False
    ) -> DataLoader:
        """
        Creates a PyTorch DataLoader with optimized settings.

        Args:
            dataset: An instance of UnifiedCryptoDataset.
            batch_size: The number of samples per batch.
            shuffle: Whether to shuffle the data at every epoch.
            num_workers: The number of subprocesses to use for data loading. 
                         If None, it's automatically determined based on CPU cores.
            pin_memory: If True, copies tensors into CUDA pinned memory before returning them.
            prefetch_factor: Number of batches loaded in advance by each worker.
            persistent_workers: If True, the data loader will not shut down the worker processes after a dataset has been consumed once.
            drop_last: If True, drops the last incomplete batch.

        Returns:
            An optimized PyTorch DataLoader instance.
        """
        
        # Automatically determine the optimal number of workers if not specified
        if num_workers is None:
            # A common heuristic: 75% of CPU cores, with a floor of 2 and a ceiling of 16
            cpu_cores = psutil.cpu_count(logical=True) or 1
            num_workers = max(2, min(int(cpu_cores * 0.75), 16))

        # Ensure pin_memory is only used when CUDA is available and not forced to CPU
        force_cpu = os.environ.get('FORCE_CPU_TESTING', 'false').lower() == 'true'
        use_pin_memory = pin_memory and not force_cpu and torch.cuda.is_available()
        
        # Persistent workers require num_workers > 0
        use_persistent_workers = persistent_workers and num_workers > 0

        dataloader_kwargs: Dict[str, Any] = {
            'dataset': dataset,
            'batch_size': batch_size,
            'shuffle': shuffle,
            'num_workers': num_workers,
            'pin_memory': use_pin_memory,
            'drop_last': drop_last,
        }

        # prefetch_factor and persistent_workers are only available for multi-process data loading
        if num_workers > 0:
            dataloader_kwargs['prefetch_factor'] = prefetch_factor
            dataloader_kwargs['persistent_workers'] = use_persistent_workers

        return DataLoader(**dataloader_kwargs)
