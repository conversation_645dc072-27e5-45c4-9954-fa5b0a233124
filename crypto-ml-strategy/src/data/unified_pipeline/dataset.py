"""
Unified Crypto Dataset
This module provides a unified and high-performance Dataset class for handling cryptographic data,
supporting both vector and sequence data types with advanced memory management.
"""

import os
import logging
import numpy as np
import pandas as pd
import torch
from torch.utils.data import Dataset
from typing import Optional, Union, Dict, Any, List

logger = logging.getLogger(__name__)

class UnifiedCryptoDataset(Dataset):
    """
    A unified dataset for handling both vector-based and sequence-based crypto data.
    It integrates high-performance features like in-memory caching and memory-mapping (mmap).
    """

    def __init__(
        self,
        features: Union[np.ndarray, pd.DataFrame],
        labels: np.ndarray,
        data_type: str = 'vector',
        sequence_length: int = 20,
        feature_columns: Optional[List[str]] = None,
        target_column: str = 'target',
        transform: Optional[callable] = None,
        cache_in_memory: bool = True,
        use_mmap: bool = False,
        timestamps: Optional[np.ndarray] = None
    ):
        """
        Initializes the UnifiedCryptoDataset.

        Args:
            features: The feature data, either a numpy array (for vectors) or a pandas DataFrame (for sequences).
            labels: The label data as a numpy array.
            data_type: The type of data, 'vector' or 'sequence'.
            sequence_length: The length of sequences for 'sequence' data_type.
            feature_columns: List of columns to be used as features for 'sequence' data.
            target_column: The name of the target column for 'sequence' data.
            transform: An optional transformation to be applied to the features.
            cache_in_memory: If True, caches the entire dataset in RAM.
            use_mmap: If True and cache_in_memory is False, uses memory-mapping for large datasets.
            timestamps: Optional array of timestamps corresponding to the data.
        """
        if data_type not in ['vector', 'sequence']:
            raise ValueError("data_type must be either 'vector' or 'sequence'")

        self.data_type = data_type
        self.transform = transform
        self.cache_in_memory = cache_in_memory
        self.use_mmap = use_mmap and not cache_in_memory
        self.timestamps = timestamps

        if self.data_type == 'sequence':
            if not isinstance(features, pd.DataFrame):
                raise TypeError("For 'sequence' data_type, features must be a pandas DataFrame.")
            self._prepare_sequences(features, sequence_length, feature_columns, target_column)
        else: # vector
            if not isinstance(features, np.ndarray) or not isinstance(labels, np.ndarray):
                raise TypeError("For 'vector' data_type, features and labels must be numpy arrays.")
            self._prepare_vectors(features, labels)
        
        self.length = len(self.features)

    def _prepare_sequences(self, data: pd.DataFrame, sequence_length: int, feature_columns: Optional[List[str]], target_column: str):
        """Prepares sliding window sequences from a DataFrame."""
        self.sequence_length = sequence_length
        self.feature_columns = feature_columns or [col for col in data.columns if col != target_column]
        self.target_column = target_column

        feature_data = data[self.feature_columns].values.astype(np.float32)
        target_data = data[self.target_column].values.astype(np.int64)

        sequences, sequence_labels = [], []
        for i in range(len(feature_data) - self.sequence_length + 1):
            seq = feature_data[i:i + self.sequence_length]
            label = target_data[i + self.sequence_length - 1]
            sequences.append(seq)
            sequence_labels.append(label)

        self.features = np.array(sequences, dtype=np.float32)
        self.labels = np.array(sequence_labels, dtype=np.int64)
        
        if self.timestamps is not None:
             # Align timestamps with the end of each sequence
            self.timestamps = self.timestamps[self.sequence_length - 1:]


    def _prepare_vectors(self, features: np.ndarray, labels: np.ndarray):
        """Prepares vector data with caching or memory-mapping."""
        if self.cache_in_memory:
            self.features = np.array(features, dtype=np.float32)
            self.labels = np.array(labels, dtype=np.int64)
        elif self.use_mmap:
            self._setup_mmap(features, labels)
        else:
            self.features = features
            self.labels = labels

    def _setup_mmap(self, features: np.ndarray, labels: np.ndarray):
        """Sets up memory-mapped files for features and labels."""
        tmp_dir = "/tmp"
        os.makedirs(tmp_dir, exist_ok=True)
        self.features_file = os.path.join(tmp_dir, f"features_{id(self)}.npy")
        self.labels_file = os.path.join(tmp_dir, f"labels_{id(self)}.npy")

        np.save(self.features_file, features.astype(np.float32))
        np.save(self.labels_file, labels.astype(np.int64))

        self.features = np.load(self.features_file, mmap_mode='r')
        self.labels = np.load(self.labels_file, mmap_mode='r')
        logger.info(f"Using memory-mapped files: {self.features_file}, {self.labels_file}")

    def __len__(self) -> int:
        return self.length

    def __getitem__(self, idx: int) -> Dict[str, Any]:
        """Retrieves an item from the dataset."""
        # Note: mmap access is already handled by numpy transparently
        feature_item = self.features[idx]
        label_item = self.labels[idx]

        if self.transform:
            feature_item = self.transform(feature_item)
        
        # Ensure correct types before returning
        item = {
            'features': torch.from_numpy(np.array(feature_item, dtype=np.float32)),
            'labels': torch.from_numpy(np.array([label_item], dtype=np.int64)).squeeze()
        }

        if self.timestamps is not None and idx < len(self.timestamps):
            item['timestamp'] = self.timestamps[idx]
            
        return item

    def __del__(self):
        """Cleans up temporary memory-mapped files upon object deletion."""
        if self.use_mmap:
            try:
                if hasattr(self, 'features_file') and os.path.exists(self.features_file):
                    os.remove(self.features_file)
                if hasattr(self, 'labels_file') and os.path.exists(self.labels_file):
                    os.remove(self.labels_file)
            except Exception as e:
                logger.warning(f"Failed to clean up mmap files: {e}")
