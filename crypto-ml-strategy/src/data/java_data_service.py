"""
Java数据服务接口 - 通过HTTP API访问crypto-market-data Java服务
按照用户要求，从Binance的交互在java端处理，使用API形式访问
"""

import asyncio
import logging
import pandas as pd
import numpy as np
import aiohttp
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any


class JavaDataService:
    """Java数据服务接口 - 通过HTTP API访问crypto-market-data服务"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化Java数据服务

        Args:
            config: Java服务配置
        """
        self.logger = logging.getLogger(__name__)

        # 默认配置
        if config is None:
            config = {}

        self.config = config
        self.base_url = config.get('base_url', 'http://127.0.0.1:19527')
        self.api_prefix = config.get('api_prefix', '/api')
        self.timeout = config.get('timeout', 60)  # Increased timeout to 60s
        self.max_retries = config.get('max_retries', 3)

        # API端点配置
        self.endpoints = {
            'kline': f"{self.api_prefix}/market/kline",
            'symbols': f"{self.api_prefix}/market/symbols",
            'latest': f"{self.api_prefix}/market-data/latest",
            'depth': f"{self.api_prefix}/market-data/depth",
            'trade': f"{self.api_prefix}/market-data/trade",
            'health': "/actuator/health"
        }

        # 检查服务可用性
        self.service_available = False
        self._check_service_availability()

    def _check_service_availability(self):
        """检查Java服务是否可用"""
        try:
            # 这里只是初始化，实际检查在异步方法中进行
            self.logger.info("Java数据服务初始化完成，将在首次调用时检查可用性")
        except Exception as e:
            self.logger.error(f"Java数据服务初始化失败: {e}")

    async def _check_service_health(self) -> bool:
        """异步检查Java服务健康状态"""
        try:
            url = f"{self.base_url}{self.endpoints['health']}"

            # 使用统一的API调用方法
            response_data = await self._call_api('GET', url)

            if response_data and response_data.get('status') == 'UP':
                self.service_available = True
                self.logger.info("✅ Java数据服务健康检查通过")
                return True

            self.service_available = False
            self.logger.warning(f"⚠️ Java数据服务健康检查失败。详细信息: {json.dumps(response_data, indent=2)}")
            return False

        except Exception as e:
            self.service_available = False
            self.logger.error(f"Java数据服务健康检查异常: {e}")
            return False
    
    async def fetch_historical_data(
        self,
        symbol: str,
        timeframe: str = '1h',
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 2500
    ) -> pd.DataFrame:
        """
        通过Java API获取历史OHLCV数据

        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            start_time: 开始时间
            end_time: 结束时间
            limit: 数据条数限制

        Returns:
            包含OHLCV数据的DataFrame
        """
        # 首先检查服务健康状态
        if not await self._check_service_health():
            raise RuntimeError("Java数据服务不可用，请确保crypto-market-data服务正在运行")

        try:
            # 构建API URL
            url = f"{self.base_url}{self.endpoints['kline']}/{symbol.upper()}/{timeframe}"

            # 准备查询参数
            params = {
                'limit': limit
            }

            self.logger.info(f"通过Java API获取 {symbol} {timeframe} 真实数据...")
            self.logger.debug(f"API URL: {url}, params: {params}")

            # 调用Java API
            response_data = await self._call_api('GET', url, params=params)

            if not response_data or not response_data.get('success'):
                error_msg = response_data.get('error', 'Unknown error') if response_data else 'No response'
                self.logger.warning(f"Java API返回错误: {error_msg}")
                return pd.DataFrame()

            # 获取数据
            kline_data = response_data.get('data', [])
            if not kline_data:
                self.logger.warning(f"Java API返回空数据: {symbol}")
                return pd.DataFrame()

            # 转换为DataFrame
            df = self._convert_kline_to_dataframe(kline_data, symbol)

            self.logger.info(f"✅ 成功获取 {len(df)} 条真实数据从Java API")
            return df

        except Exception as e:
            self.logger.error(f"通过Java API获取数据失败: {e}")
            return pd.DataFrame()

    async def fetch_depth_data(self, symbol: str, limit: int = 1000) -> Optional[pd.DataFrame]:
        if not await self._check_service_health():
            self.logger.error("Java data service is not available.")
            return None
        
        url = f"{self.base_url}{self.endpoints['depth']}/{symbol.upper()}"
        params = {'limit': limit}
        
        response = await self._call_api('GET', url, params=params)
        
        if response and response.get('success'):
            data = response.get('data', {})
            if not data:
                self.logger.warning(f"Depth data fetch for {symbol} returned success but data field is empty.")
                return None

            bids_str = data.get('bids')
            asks_str = data.get('asks')

            if not bids_str or not asks_str:
                self.logger.warning(f"Bids or asks string is missing in depth data for {symbol}")
                return None
            
            try:
                bids = json.loads(bids_str)
                asks = json.loads(asks_str)
            except json.JSONDecodeError:
                self.logger.error(f"Failed to decode JSON for depth data: {symbol}. Bids: {bids_str[:100]}, Asks: {asks_str[:100]}")
                return None

            if not bids or not asks:
                self.logger.warning(f"Bids or asks list is empty after JSON parsing for {symbol}")
                return None

            # The timestamp from Java is an ISO 8601 string.
            ts = pd.to_datetime(data.get('timestamp'), errors='coerce', utc=True)
            if pd.isna(ts):
                 self.logger.error(f"Invalid timestamp format in depth data for {symbol}: {data.get('timestamp')}")
                 return None

            df = pd.DataFrame({
                'timestamp': [ts],
                'bids_price_0': [bids[0]['price']],
                'bids_qty_0': [bids[0]['quantity']],
                'asks_price_0': [asks[0]['price']],
                'asks_qty_0': [asks[0]['quantity']]
            })
            df.set_index('timestamp', inplace=True)
            return df
        return None

    async def fetch_trade_data(self, symbol: str, limit: int = 1000) -> Optional[pd.DataFrame]:
        if not await self._check_service_health():
            self.logger.error("Java data service is not available.")
            return None

        url = f"{self.base_url}{self.endpoints['trade']}/{symbol.upper()}"
        params = {'limit': limit}

        response = await self._call_api('GET', url, params=params)

        if not response or not response.get('success'):
            self.logger.warning(f"Failed to fetch trade data for {symbol}: {response.get('error') if response else 'No response'}")
            return None

        trades = response.get('data', [])
        if not trades:
            self.logger.warning(f"No trade data returned for {symbol}")
            return None
        
        try:
            df = pd.DataFrame(trades)
            # The 'timestamp' from Java is an ISO 8601 string. Pandas can infer it.
            # Using errors='coerce' to handle any problematic timestamps gracefully.
            df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce')
            df.dropna(subset=['timestamp'], inplace=True)

            if df.empty:
                self.logger.warning(f"DataFrame empty after handling timestamps for {symbol}")
                return None

            df.set_index('timestamp', inplace=True)
            
            # Create the 'isBuyerMaker' column based on the 'side' column.
            # 'side' is from the taker's perspective. If side is SELL, it means the seller was the taker,
            # so the buyer must have been the maker.
            if 'side' in df.columns:
                df['isBuyerMaker'] = (df['side'] == 'SELL')
            else:
                self.logger.warning("Column 'side' not found in trade data, cannot create 'isBuyerMaker'.")
                # Handle case where side is missing, maybe return None or an empty df with expected columns
                return None
            
            # Ensure the required columns exist before returning.
            required_cols = ['price', 'quantity', 'isBuyerMaker']
            if not all(col in df.columns for col in required_cols):
                self.logger.error(f"DataFrame for {symbol} is missing required columns. Available: {df.columns.tolist()}")
                return None

            return df[required_cols]
        except KeyError as e:
            self.logger.error(f"KeyError processing trade data for {symbol}: {e}. Columns are: {df.columns.tolist() if 'df' in locals() else 'N/A'}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error processing trade data for {symbol}: {e}", exc_info=True)
            return None
    
    async def _call_api(
        self,
        method: str,
        url: str,
        params: Optional[Dict[str, Any]] = None,
        data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        调用Java API，增加了重试和更精细的超时控制。

        Args:
            method: HTTP方法 (GET, POST, etc.)
            url: API URL
            params: 查询参数
            data: 请求体数据

        Returns:
            API响应数据
        """
        # 设置更精细的超时
        # 总超时时间为 self.timeout, 连接超时为10秒
        connect_timeout = 10
        # sock_read 超时为总超时减去连接超时
        read_timeout = max(self.timeout - connect_timeout, 1)
        timeout = aiohttp.ClientTimeout(
            total=self.timeout,
            connect=connect_timeout,
            sock_read=read_timeout
        )

        last_exception = None

        for attempt in range(self.max_retries):
            try:
                self.logger.info(f"发起API调用 (尝试 {attempt + 1}/{self.max_retries}): method={method}, url={url}, timeout={self.timeout}s")
                async with aiohttp.ClientSession(trust_env=False) as session:
                    async with session.request(
                        method=method,
                        url=url,
                        params=params,
                        json=data,
                        timeout=timeout
                    ) as response:

                        if response.status >= 200 and response.status < 300:
                            json_data = await response.json(content_type=None)
                            self.logger.debug(f"成功获取来自 {url} 的JSON响应")
                            return json_data
                        
                        # 对于客户端错误(4xx)，通常不应重试
                        if 400 <= response.status < 500:
                            error_text = await response.text()
                            self.logger.error(f"客户端错误，不进行重试: {response.status}, {error_text}")
                            return {'success': False, 'error': f'HTTP {response.status}: {error_text}'}

                        # 对于服务器错误(5xx)，进行重试
                        error_text = await response.text()
                        self.logger.error(f"DEBUG: Status check: code={response.status}, type={type(response.status)}")

                        if response.status in [503]: # Service Unavailable
                            self.logger.error(f"服务器错误，不进行重试 (服务不可用): {response.status}, {error_text}")
                            return {'success': False, 'error': f'HTTP {response.status}: {error_text}'}
                        
                        self.logger.warning(f"API调用失败，将在 {2**attempt} 秒后重试: {response.status}, {error_text}")
                        last_exception = aiohttp.ClientResponseError(response.request_info, response.history, status=response.status, message=error_text)

            except (asyncio.TimeoutError, aiohttp.ClientConnectorError) as e:
                last_exception = e
                self.logger.warning(f"API调用出现可重试错误 (尝试 {attempt + 1}/{self.max_retries}): {e}。将在 {2**attempt} 秒后重试...")
            
            except Exception as e:
                # 对于其他未知异常，记录日志并立即失败
                self.logger.error(f"API调用出现未知异常: {e}", exc_info=True)
                return {'success': False, 'error': str(e)}

            # 如果不是最后一次尝试，则进行指数退避等待
            if attempt < self.max_retries - 1:
                await asyncio.sleep(2**attempt)

        self.logger.error(f"API调用在 {self.max_retries} 次尝试后最终失败: {last_exception}")
        return {'success': False, 'error': f'Request failed after {self.max_retries} retries: {last_exception}'}

    def _convert_kline_to_dataframe(self, kline_data: List[List], symbol: str) -> pd.DataFrame:
        """
        将从Java服务接收的K线数据 (List[List]) 转换为DataFrame。

        Args:
            kline_data: K线数据列表 (每个内部列表代表一个时间点的K线)。
            symbol: 交易对符号。

        Returns:
            DataFrame格式的K线数据，包含标准化的列名。
        """
        try:
            if not kline_data:
                return pd.DataFrame()

            # 根据Java服务返回的List<List<Object>>结构定义列名
            # 参考Binance API文档顺序
            columns = [
                'openTime', 'open', 'high', 'low', 'close', 'volume',
                'closeTime', 'quoteAssetVolume', 'numberOfTrades',
                'takerBuyBaseAssetVolume', 'takerBuyQuoteAssetVolume', 'ignore'
            ]
            
            # 确保DataFrame的列数不超过实际数据长度
            df = pd.DataFrame(kline_data, columns=columns[:len(kline_data[0])])

            # 检查必要的列
            required_columns = ['openTime', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                self.logger.error(f"K线数据在转换为DataFrame后缺少必要列: {missing_columns}")
                return pd.DataFrame()

            # 重命名和转换列
            df = df.rename(columns={
                'openTime': 'timestamp'
            })

            # The timestamp from Java can be an ISO 8601 string. Let pandas infer the format.
            df['timestamp'] = pd.to_datetime(df['timestamp'], errors='coerce', utc=True)
            df.dropna(subset=['timestamp'], inplace=True)

            numeric_columns = ['open', 'high', 'low', 'close', 'volume']
            for col in numeric_columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

            # 添加symbol列
            df['symbol'] = symbol

            # 选择和排序列
            final_columns = ['timestamp', 'symbol', 'open', 'high', 'low', 'close', 'volume']
            df = df[final_columns]

            # 按时间排序
            # df = df.sort_values('timestamp').reset_index(drop=True)

            return df

        except Exception as e:
            self.logger.error(f"K线数据转换失败: {e}", exc_info=True)
            return pd.DataFrame()
    
    async def fetch_multiple_symbols(
        self,
        symbols: List[str],
        timeframe: str = '1h',
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        max_concurrent: int = 5
    ) -> Dict[str, pd.DataFrame]:
        """
        并发获取多个交易对的数据
        
        Args:
            symbols: 交易对列表
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            max_concurrent: 最大并发数
            
        Returns:
            Dict[str, pd.DataFrame]: 交易对数据字典
        """
        self.logger.info(f"🔄 通过Java端并发获取{len(symbols)}个交易对数据")
        
        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(max_concurrent)
        
        async def fetch_single_symbol(symbol: str) -> Tuple[str, Optional[pd.DataFrame]]:
            """获取单个交易对数据"""
            async with semaphore:
                try:
                    df = await self.fetch_historical_data(
                        symbol=symbol,
                        timeframe=timeframe,
                        start_time=start_time,
                        end_time=end_time
                    )
                    self.logger.info(f"✅ {symbol}: {len(df)} records")
                    return symbol, df
                except Exception as e:
                    self.logger.error(f"❌ {symbol}: {e}")
                    return symbol, None
        
        # 并发执行
        tasks = [fetch_single_symbol(symbol) for symbol in symbols]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 整理结果
        data_dict = {}
        for result in results:
            if isinstance(result, Exception):
                self.logger.error(f"获取数据时发生异常: {result}")
                continue
            
            symbol, df = result
            if df is not None and not df.empty:
                data_dict[symbol] = df
        
        self.logger.info(f"✅ 成功获取 {len(data_dict)}/{len(symbols)} 个交易对数据")
        return data_dict
    
    async def is_available(self) -> bool:
        """Asynchronously checks if the Java data service is available and healthy."""
        return await self._check_service_health()

    async def test_connection(self) -> bool:
        """测试Java API连接"""
        try:
            # 首先测试健康检查端点
            if not await self._check_service_health():
                return False

            # 然后测试获取支持的交易对
            url = f"{self.base_url}{self.endpoints['symbols']}"
            response_data = await self._call_api('GET', url)

            if response_data and response_data.get('success'):
                symbols = response_data.get('symbols', [])
                self.logger.info(f"✅ Java API连接测试成功，支持 {len(symbols)} 个交易对")
                return True

            return False

        except Exception as e:
            self.logger.error(f"Java API连接测试失败: {e}")
            return False

    async def get_supported_symbols(self) -> List[str]:
        """获取支持的交易对列表"""
        try:
            url = f"{self.base_url}{self.endpoints['symbols']}"
            response_data = await self._call_api('GET', url)

            if response_data and response_data.get('success'):
                return response_data.get('symbols', [])

            return []

        except Exception as e:
            self.logger.error(f"获取支持的交易对失败: {e}")
            return []
