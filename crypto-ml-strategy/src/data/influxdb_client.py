"""
InfluxDB客户端 - 从InfluxDB获取真实历史数据
"""

import asyncio
import functools
import logging
import pandas as pd
import numpy as np
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
# 按照用户要求，不使用模拟数据，必须使用真实数据源
# 如果InfluxDB不可用，应该明确报错而不是降级到模拟数据
try:
    from influxdb_client import InfluxDBClient, Point
    from influxdb_client.client.write_api import SYNCHRONOUS
    INFLUXDB_AVAILABLE = True
except ImportError as e:
    print(f"❌ InfluxDB客户端导入失败: {e}")
    print("⚠️ 按照用户要求，不使用模拟数据。请安装正确的influxdb_client包或使用其他真实数据源。")
    INFLUXDB_AVAILABLE = False

    # 不创建模拟类，而是在使用时抛出明确错误
    InfluxDBClient = None
    Point = None
    SYNCHRONOUS = None


class InfluxDBDataClient:
    """InfluxDB数据客户端"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化InfluxDB客户端

        Args:
            config: InfluxDB配置
        """
        if not INFLUXDB_AVAILABLE:
            raise ImportError("InfluxDB client is not available. Please install 'influxdb-client'.")
            
        if not config:
            raise ValueError("InfluxDB configuration is required.")

        self.available = INFLUXDB_AVAILABLE
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # InfluxDB连接配置
        self.url = self.config.get('url')
        self.token = self.config.get('token')
        self.org = self.config.get('org')
        self.bucket = self.config.get('bucket')
        
        # 初始化客户端
        self.client = None
        self.query_api = None
        self.write_api = None
        
        # 数据配置（使用真实的币安符号格式）
        self.symbols = config.get('symbols', ['BTCUSDT', 'ETHUSDT', 'BNBUSDT'])
        self.timeframes = config.get('timeframes', ['1h', '4h', '1d'])

        # 直接使用InfluxDB，移除Java服务依赖

    async def connect(self):
        """连接到InfluxDB"""
        try:
            from influxdb_client import InfluxDBClient as InfluxClient
            self.client = InfluxClient(
                url=self.url,
                token=self.token,
                org=self.org
            )
            self.query_api = self.client.query_api()
            self.write_api = self.client.write_api(write_options=SYNCHRONOUS)

            # 测试连接 - 添加超时控制
            await asyncio.wait_for(self._test_connection(), timeout=10.0)
            self.logger.info("Successfully connected to InfluxDB")
            return True

        except asyncio.TimeoutError:
            self.logger.error("InfluxDB connection timeout (10 seconds)")
            self.client = None
            return False
        except Exception as e:
            self.logger.error(f"Failed to connect to InfluxDB: {e}")
            self.client = None
            return False
    
    async def _test_connection(self):
        """测试InfluxDB连接"""
        try:
            # 简单查询测试连接
            query = f'''
            from(bucket: "{self.bucket}")
            |> range(start: -1h)
            |> limit(n: 1)
            '''
            result = self.query_api.query(query)
            self.logger.info("InfluxDB connection test successful")
        except Exception as e:
            self.logger.warning(f"InfluxDB connection test failed: {e}")
            raise
    
    async def _setup_mock_data(self):
        """设置模拟数据（当InfluxDB不可用时）"""
        self.logger.warning("InfluxDB not available, using mock data")
        self.client = None
    
    async def fetch_historical_data(
        self,
        symbol: str = 'BTC/USDT',
        timeframe: str = '1h',
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        limit: int = 10000
    ) -> pd.DataFrame:
        """
        获取历史OHLCV数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            start_time: 开始时间
            end_time: 结束时间
            limit: 数据条数限制
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        if self.client is None:
            self.logger.error("InfluxDB客户端未初始化，无法获取数据")
            raise RuntimeError("InfluxDB连接不可用，请检查InfluxDB服务状态")
        
        try:
            # 设置默认时间范围
            if end_time is None:
                end_time = datetime.now()
            if start_time is None:
                start_time = end_time - timedelta(days=365)  # 默认一年数据

            # 修复：确保时间格式正确，并使用传入的时间参数
            start_str = start_time.strftime('%Y-%m-%dT%H:%M:%SZ')
            end_str = end_time.strftime('%Y-%m-%dT%H:%M:%SZ')

            # 构建InfluxDB查询（使用实际的数据结构）
            # 修复：使用正确的measurement名称和字段名称
            query = f'''
            from(bucket: "{self.bucket}")
            |> range(start: {start_str}, stop: {end_str})
            |> filter(fn: (r) => r["_measurement"] == "kline_data")
            |> filter(fn: (r) => r["symbol"] == "{symbol}")
            |> filter(fn: (r) => r["interval"] == "{timeframe}")
            |> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")
            |> sort(columns: ["_time"], desc: false)
            |> limit(n: {limit})
            '''

            # 🔥 添加：记录查询详情
            self.logger.info(f"🔍 InfluxDB查询: bucket={self.bucket}, symbol={symbol}, timeframe={timeframe}, limit={limit}")
            
            # 执行查询
            result = self.query_api.query_data_frame(query)

            # 处理查询结果
            if isinstance(result, list):
                if not result:
                    self.logger.warning(f"InfluxDB中未找到数据: {symbol} {timeframe}")
                    return pd.DataFrame()
                # 如果是list，合并所有DataFrame
                result = pd.concat(result, ignore_index=True) if len(result) > 1 else result[0]

            if result.empty:
                self.logger.warning(f"InfluxDB中未找到数据: {symbol} {timeframe}")
                return pd.DataFrame()

            # 🔥 添加：记录原始查询结果
            self.logger.info(f"📊 InfluxDB原始结果: {len(result)}行, 列: {list(result.columns)}")
            if len(result) > 0:
                self.logger.info(f"📊 样本数据: {result.head(1).to_dict('records')}")

            # 处理数据
            df = self._process_influx_data(result)
            self.logger.info(f"✅ 获取到 {len(df)} 条记录: {symbol} {timeframe}")

            return df
            
        except Exception as e:
            self.logger.error(f"从InfluxDB获取数据失败: {e}")
            return pd.DataFrame()
    
    def _process_influx_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理从InfluxDB获取的数据"""
        # 🔥 修复：使用实际的InfluxDB字段名（基于查询结果）
        # InfluxDB中的字段名是 'open_price', 'high_price' 等
        column_mapping = {
            '_time': 'timestamp',
            'open_price': 'open',
            'high_price': 'high',
            'low_price': 'low',
            'close_price': 'close',
            'volume': 'volume',
            'quote_volume': 'quote_asset_volume',
            'trade_count': 'count',
            'taker_buy_volume': 'taker_buy_base_asset_volume',
            'taker_buy_quote_volume': 'taker_buy_quote_asset_volume',
            'close_time': 'close_time'
        }

        # 选择需要的列并重命名
        available_cols = [col for col in column_mapping.keys() if col in df.columns]
        if available_cols:
            df_processed = df[available_cols].rename(columns=column_mapping)
        else:
            self.logger.warning(f"⚠️ 未找到预期的列，可用列: {list(df.columns)}")
            return pd.DataFrame()

        # 确保数据类型正确
        numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'quote_asset_volume', 'count',
                       'taker_buy_base_asset_volume', 'taker_buy_quote_asset_volume']
        for col in numeric_cols:
            if col in df_processed.columns:
                df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce')

        # 处理时间戳
        if 'timestamp' in df_processed.columns:
            df_processed['timestamp'] = pd.to_datetime(df_processed['timestamp'])
            df_processed.set_index('timestamp', inplace=True)
            df_processed.sort_index(inplace=True)

        # 移除缺失值
        df_processed = df_processed.dropna()

        # 🔥 添加：记录成功处理的数据
        self.logger.info(f"✅ 成功处理InfluxDB数据: {len(df_processed)}行, 列: {list(df_processed.columns)}")

        return df_processed

    async def fetch_raw_data(
        self,
        symbol: str,
        measurement: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        fields: Optional[List[str]] = None,
        limit: int = 100000
    ) -> Optional[pd.DataFrame]:
        """
        Fetches raw data for a specific measurement from InfluxDB.

        Args:
            symbol: The trading pair symbol.
            measurement: The InfluxDB measurement name (e.g., 'depth_data', 'trade_data').
            start_time: The start time for the data range.
            end_time: The end time for the data range.
            fields: The list of fields to pivot. If None, no pivot is performed.
            limit: The maximum number of records to return.

        Returns:
            A pandas DataFrame with the raw data, or None if an error occurs.
        """
        if self.client is None:
            self.logger.error(f"InfluxDB client not initialized, cannot fetch {measurement}.")
            raise RuntimeError("InfluxDB connection is not available.")

        try:
            if end_time is None:
                end_time = datetime.now()
            if start_time is None:
                start_time = end_time - timedelta(days=1)  # Default to 1 day for raw data

            start_str = start_time.strftime('%Y-%m-%dT%H:%M:%SZ')
            end_str = end_time.strftime('%Y-%m-%dT%H:%M:%SZ')
            
            # Use pivot to transform data into a wide format, which is generally more useful.
            pivot_str = '|> pivot(rowKey:["_time"], columnKey: ["_field"], valueColumn: "_value")'

            filter_str = f'|> filter(fn: (r) => r["_measurement"] == "{measurement}")'
            if measurement == 'trade_data':
                self.logger.info("Adjusting query for 'trade_data' to search in 'market_data' measurement with tag.")
                filter_str = f'''
                |> filter(fn: (r) => r["_measurement"] == "market_data")
                |> filter(fn: (r) => r["data_type"] == "trade")
                '''

            query = f'''
            from(bucket: "{self.bucket}")
            |> range(start: {start_str}, stop: {end_str})
            {filter_str}
            |> filter(fn: (r) => r["symbol"] == "{symbol}")
            {pivot_str}
            |> sort(columns: ["_time"], desc: true)
            |> limit(n: {limit})
            '''
            self.logger.info(f"🔍 Fetching raw data: measurement={measurement}, symbol={symbol}, limit={limit}")

            result_df_or_list = self.query_api.query_data_frame(query)

            # The query_data_frame method can return a single DataFrame or a list of them.
            if isinstance(result_df_or_list, list):
                if not result_df_or_list:
                    self.logger.warning(f"No raw data found in InfluxDB for: {measurement} on {symbol}")
                    return pd.DataFrame()
                result_df = pd.concat(result_df_or_list, ignore_index=True)
            else:
                result_df = result_df_or_list

            if result_df.empty:
                self.logger.warning(f"No raw data found in InfluxDB for: {measurement} on {symbol}")
                return pd.DataFrame()

            if '_time' in result_df.columns:
                result_df = result_df.rename(columns={'_time': 'timestamp'})
                result_df['timestamp'] = pd.to_datetime(result_df['timestamp'])

            self.logger.info(f"✅ Fetched {len(result_df)} records for {measurement} on {symbol}")
            return result_df

        except Exception as e:
            self.logger.error(f"Failed to fetch raw data for {measurement} on {symbol}: {e}", exc_info=True)
            return pd.DataFrame()
    
    # 模拟数据生成方法已移除 - 按照用户要求，不使用模拟数据，只使用真实数据
    
    async def fetch_multiple_symbols(
        self,
        symbols: Optional[List[str]] = None,
        timeframe: str = '1h',
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        max_concurrent: int = 5
    ) -> Dict[str, pd.DataFrame]:
        """
        并发获取多个交易对的数据

        Args:
            symbols: 交易对列表
            timeframe: 时间周期
            start_time: 开始时间
            end_time: 结束时间
            max_concurrent: 最大并发数

        Returns:
            Dict[str, pd.DataFrame]: 交易对数据字典
        """
        if symbols is None:
            symbols = self.symbols

        self.logger.info(f"🔄 并发获取{len(symbols)}个交易对数据，最大并发数: {max_concurrent}")

        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(max_concurrent)

        async def fetch_single_symbol(symbol: str) -> Tuple[str, Optional[pd.DataFrame]]:
            """获取单个交易对数据"""
            async with semaphore:
                try:
                    df = await self.fetch_historical_data(
                        symbol=symbol,
                        timeframe=timeframe,
                        start_time=start_time,
                        end_time=end_time
                    )
                    self.logger.info(f"✅ {symbol}: {len(df)} records")
                    return symbol, df
                except Exception as e:
                    self.logger.error(f"❌ {symbol}: {e}")
                    return symbol, None

        # 并发执行所有查询
        start_time_total = time.time()
        tasks = [fetch_single_symbol(symbol) for symbol in symbols]
        results_list = await asyncio.gather(*tasks, return_exceptions=True)
        total_time = time.time() - start_time_total

        # 处理结果
        results = {}
        successful_count = 0
        for result in results_list:
            if isinstance(result, Exception):
                self.logger.error(f"并发查询异常: {result}")
                continue

            symbol, df = result
            if df is not None:
                results[symbol] = df
                successful_count += 1

        self.logger.info(f"🎯 并发查询完成: {successful_count}/{len(symbols)} 成功, 耗时: {total_time:.2f}s")
        return results

    async def write_dataframe(self, df: pd.DataFrame, measurement: str, tags: Dict[str, str]):
        """
        Asynchronously writes a pandas DataFrame to InfluxDB.

        Args:
            df: The DataFrame to write. Must have a DatetimeIndex.
            measurement: The measurement name.
            tags: A dictionary of tags to apply to all points.
        """
        if self.write_api is None:
            self.logger.error("Write API not initialized. Cannot write to InfluxDB.")
            return

        try:
            # 🔥 FIX: Work on a copy to avoid modifying the original DataFrame from the caller
            df_copy = df.copy()

            # Add tags to the dataframe as columns, as required by the write_api
            for key, value in tags.items():
                df_copy[key] = value

            tag_columns = list(tags.keys())

            self.logger.info(f"Writing {len(df_copy)} rows to InfluxDB measurement '{measurement}' with tags {tags}")

            # The write API is synchronous in the library, but we run it in an executor
            # to make it non-blocking in our async application.
            loop = asyncio.get_running_loop()

            # 🔥 FIX: Use functools.partial to correctly pass keyword arguments to the executor
            write_func = functools.partial(
                self.write_api.write,
                bucket=self.bucket,
                org=self.org,
                record=df_copy,
                data_frame_measurement_name=measurement,
                data_frame_tag_columns=tag_columns
            )

            await loop.run_in_executor(None, write_func)

            self.logger.info(f"Successfully wrote {len(df_copy)} rows to {measurement}.")
            return True

        except Exception as e:
            self.logger.error(f"Failed to write DataFrame to InfluxDB: {e}", exc_info=True)
            return False

    async def write_batch(self, points: List[Dict[str, Any]]) -> bool:
        """
        批量写入数据点到InfluxDB

        Args:
            points: 数据点列表，每个点包含measurement, tags, time, fields

        Returns:
            是否写入成功
        """
        if not self.available:
            self.logger.error("InfluxDB客户端不可用")
            return False

        if not points:
            self.logger.warning("没有数据点需要写入")
            return True

        try:
            # 转换为InfluxDB Point对象
            influx_points = []
            for point_data in points:
                point = Point(point_data['measurement'])

                # 添加标签
                for tag_key, tag_value in point_data.get('tags', {}).items():
                    point = point.tag(tag_key, tag_value)

                # 添加字段
                for field_key, field_value in point_data.get('fields', {}).items():
                    point = point.field(field_key, field_value)

                # 设置时间
                if 'time' in point_data:
                    point = point.time(point_data['time'])

                influx_points.append(point)

            self.logger.info(f"批量写入 {len(influx_points)} 个数据点到InfluxDB")

            # 异步写入
            loop = asyncio.get_running_loop()
            write_func = functools.partial(
                self.write_api.write,
                bucket=self.bucket,
                org=self.org,
                record=influx_points
            )

            await loop.run_in_executor(None, write_func)

            self.logger.info(f"✅ 成功批量写入 {len(influx_points)} 个数据点")
            return True

        except Exception as e:
            self.logger.error(f"❌ 批量写入InfluxDB失败: {e}", exc_info=True)
            return False

    async def close(self):
        """关闭连接"""
        if self.client:
            self.client.close()
            self.logger.info("InfluxDB connection closed")
    
    def __del__(self):
        """析构函数"""
        if hasattr(self, 'client') and self.client:
            try:
                self.client.close()
            except:
                pass
