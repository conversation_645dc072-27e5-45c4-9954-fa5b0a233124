"""
增量学习器 - 在线学习和概念漂移检测
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import logging
from collections import deque
import asyncio

from src.models.unified_fusion_model import UnifiedSignalFusionModel
from src.utils.metrics import calculate_metrics


class ConceptDriftDetector:
    """概念漂移检测器"""
    
    def __init__(
        self,
        window_size: int = 100,
        drift_threshold: float = 0.1,
        min_samples: int = 30
    ):
        """
        初始化概念漂移检测器
        
        Args:
            window_size: 滑动窗口大小
            drift_threshold: 漂移阈值
            min_samples: 最小样本数
        """
        self.window_size = window_size
        self.drift_threshold = drift_threshold
        self.min_samples = min_samples
        
        # 性能历史
        self.performance_history = deque(maxlen=window_size)
        self.baseline_performance = None
        
        self.logger = logging.getLogger(__name__)
    
    def update_performance(self, accuracy: float) -> bool:
        """
        更新性能并检测漂移
        
        Args:
            accuracy: 当前准确率
            
        Returns:
            是否检测到概念漂移
        """
        self.performance_history.append(accuracy)
        
        # 如果样本不足，不进行漂移检测
        if len(self.performance_history) < self.min_samples:
            return False
        
        # 设置基线性能
        if self.baseline_performance is None:
            self.baseline_performance = np.mean(list(self.performance_history)[:self.min_samples])
            return False
        
        # 计算当前窗口的平均性能
        current_performance = np.mean(list(self.performance_history)[-self.min_samples:])
        
        # 检测性能下降
        performance_drop = self.baseline_performance - current_performance
        
        if performance_drop > self.drift_threshold:
            self.logger.warning(f"🚨 检测到概念漂移: 性能下降 {performance_drop:.3f}")
            # 更新基线
            self.baseline_performance = current_performance
            return True
        
        return False
    
    def get_drift_statistics(self) -> Dict[str, Any]:
        """获取漂移统计信息"""
        if len(self.performance_history) == 0:
            return {'status': 'no_data'}
        
        recent_performance = list(self.performance_history)[-self.min_samples:] if len(self.performance_history) >= self.min_samples else list(self.performance_history)
        
        return {
            'baseline_performance': self.baseline_performance,
            'current_performance': np.mean(recent_performance) if recent_performance else 0,
            'performance_std': np.std(recent_performance) if recent_performance else 0,
            'total_samples': len(self.performance_history),
            'recent_samples': len(recent_performance)
        }


class IncrementalLearner:
    """增量学习器"""
    
    def __init__(
        self,
        model: Optional[nn.Module] = None,
        learning_rate: float = 0.001,
        batch_size: int = 32,
        buffer_size: int = 1000,
        update_frequency: int = 10,
        device: Optional[torch.device] = None
    ):
        """
        初始化增量学习器
        
        Args:
            model: 深度学习模型
            learning_rate: 学习率
            batch_size: 批次大小
            buffer_size: 经验回放缓冲区大小
            update_frequency: 更新频率
            device: 计算设备
        """
        self.device = device or torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 模型
        if model is None:
            self.model = UnifiedSignalFusionModel(
                feature_dim=25,
                hidden_dims=[128, 64, 32],
                num_heads=4,
                num_layers=2,
                dropout=0.1,
                sequence_length=20,
                output_dim=3
            )
        else:
            self.model = model
        
        self.model = self.model.to(self.device)
        
        # 优化器
        self.optimizer = optim.AdamW(self.model.parameters(), lr=learning_rate)
        self.criterion = nn.CrossEntropyLoss()
        
        # 学习参数
        self.batch_size = batch_size
        self.update_frequency = update_frequency
        
        # 经验回放缓冲区
        self.experience_buffer = deque(maxlen=buffer_size)
        
        # 概念漂移检测器
        self.drift_detector = ConceptDriftDetector()
        
        # 统计信息
        self.update_count = 0
        self.total_samples = 0
        self.performance_history = []
        
        self.logger = logging.getLogger(__name__)
    
    def add_sample(
        self,
        features: torch.Tensor,
        label: torch.Tensor,
        update_immediately: bool = False
    ) -> Dict[str, Any]:
        """
        添加新样本
        
        Args:
            features: 特征张量
            label: 标签张量
            update_immediately: 是否立即更新
            
        Returns:
            更新结果
        """
        # 添加到经验缓冲区
        self.experience_buffer.append((features.cpu(), label.cpu()))
        self.total_samples += 1
        
        result = {
            'sample_added': True,
            'buffer_size': len(self.experience_buffer),
            'total_samples': self.total_samples,
            'updated': False
        }
        
        # 检查是否需要更新
        should_update = (
            update_immediately or 
            len(self.experience_buffer) >= self.batch_size and 
            self.total_samples % self.update_frequency == 0
        )
        
        if should_update:
            update_result = self._update_model()
            result.update(update_result)
        
        return result
    
    def _update_model(self) -> Dict[str, Any]:
        """更新模型"""
        if len(self.experience_buffer) < self.batch_size:
            return {'updated': False, 'reason': 'insufficient_samples'}
        
        try:
            # 从缓冲区采样
            batch_indices = np.random.choice(
                len(self.experience_buffer), 
                size=min(self.batch_size, len(self.experience_buffer)), 
                replace=False
            )
            
            batch_features = []
            batch_labels = []
            
            for idx in batch_indices:
                features, label = self.experience_buffer[idx]
                batch_features.append(features)
                batch_labels.append(label)
            
            # 转换为张量
            batch_features = torch.stack(batch_features).to(self.device)
            batch_labels = torch.stack(batch_labels).to(self.device)
            
            # 前向传播
            self.model.train()
            outputs = self.model(batch_features)
            
            if isinstance(outputs, dict):
                logits = outputs.get('signal', outputs.get('logits', outputs.get('output')))
            else:
                logits = outputs
            
            loss = self.criterion(logits, batch_labels)
            
            # 反向传播
            # self.# optimizer.zero_grad() not needed with sklearn
            # loss.backward() not needed with sklearn
            # self.# optimizer.step() not needed with sklearn
            
            # 计算准确率
            with torch.no_grad():
                predictions = torch.argmax(logits, dim=-1)
                accuracy = (predictions == batch_labels).float().mean().item()
            
            # 更新统计信息
            self.update_count += 1
            self.performance_history.append(accuracy)
            
            # 概念漂移检测
            drift_detected = self.drift_detector.update_performance(accuracy)
            
            result = {
                'updated': True,
                'update_count': self.update_count,
                'loss': loss.item(),
                'accuracy': accuracy,
                'drift_detected': drift_detected,
                'batch_size': len(batch_features)
            }
            
            if drift_detected:
                result['drift_action'] = self._handle_concept_drift()
            
            return result
            
        except Exception as e:
            self.logger.error(f"❌ 模型更新失败: {e}")
            return {
                'updated': False,
                'error': str(e)
            }
    
    def _handle_concept_drift(self) -> Dict[str, Any]:
        """处理概念漂移"""
        self.logger.info("🔄 处理概念漂移...")
        
        # 增加学习率以快速适应
        for param_group in self.optimizer.param_groups:
            param_group['lr'] *= 1.5
        
        # 清空部分经验缓冲区，保留最新的样本
        keep_size = len(self.experience_buffer) // 2
        new_buffer = deque(
            list(self.experience_buffer)[-keep_size:], 
            maxlen=self.experience_buffer.maxlen
        )
        self.experience_buffer = new_buffer
        
        return {
            'action': 'learning_rate_increased',
            'new_lr': self.optimizer.param_groups[0]['lr'],
            'buffer_cleared': True,
            'remaining_samples': len(self.experience_buffer)
        }
    
    async def batch_update(
        self,
        features_batch: List[torch.Tensor],
        labels_batch: List[torch.Tensor]
    ) -> Dict[str, Any]:
        """批量更新"""
        results = []
        
        for features, label in zip(features_batch, labels_batch):
            result = self.add_sample(features, label, update_immediately=False)
            results.append(result)
        
        # 执行一次模型更新
        update_result = self._update_model()
        
        return {
            'batch_size': len(features_batch),
            'samples_added': len(results),
            'update_result': update_result,
            'buffer_size': len(self.experience_buffer)
        }
    
    def evaluate_performance(
        self,
        test_features: torch.Tensor,
        test_labels: torch.Tensor
    ) -> Dict[str, Any]:
        """评估性能"""
        try:
            self.model.eval()
            
            with torch.no_grad():
                test_features = test_features.to(self.device)
                test_labels = test_labels.to(self.device)
                
                outputs = self.model(test_features)
                
                if isinstance(outputs, dict):
                    logits = outputs.get('signal', outputs.get('logits', outputs.get('output')))
                else:
                    logits = outputs
                
                predictions = torch.argmax(logits, dim=-1)
                probabilities = torch.softmax(logits, dim=-1)
                
                # 计算指标
                metrics = calculate_metrics(
                    test_labels.cpu().numpy(),
                    predictions.cpu().numpy(),
                    probabilities.cpu().numpy(),
                    task_type='classification'
                )
                
                return {
                    'evaluation_successful': True,
                    'metrics': metrics,
                    'test_samples': len(test_features)
                }
                
        except Exception as e:
            self.logger.error(f"❌ 性能评估失败: {e}")
            return {
                'evaluation_successful': False,
                'error': str(e)
            }
    
    def get_learning_statistics(self) -> Dict[str, Any]:
        """获取学习统计信息"""
        drift_stats = self.drift_detector.get_drift_statistics()
        
        return {
            'total_samples': self.total_samples,
            'update_count': self.update_count,
            'buffer_size': len(self.experience_buffer),
            'buffer_capacity': self.experience_buffer.maxlen,
            'recent_performance': np.mean(self.performance_history[-10:]) if self.performance_history else 0,
            'performance_std': np.std(self.performance_history[-10:]) if len(self.performance_history) > 1 else 0,
            'drift_detection': drift_stats,
            'current_lr': self.optimizer.param_groups[0]['lr'] if self.optimizer.param_groups else 0
        }
    
    def save_model(self, path: str) -> bool:
        """保存模型"""
        try:
            torch.save({
                'model_state_dict': self.model.state_dict(),
                'optimizer_state_dict': self.optimizer.state_dict(),
                'update_count': self.update_count,
                'total_samples': self.total_samples,
                'performance_history': self.performance_history
            }, path)
            
            self.logger.info(f"✅ 增量学习模型已保存: {path}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 保存模型失败: {e}")
            return False
    
    def load_model(self, path: str) -> bool:
        """加载模型"""
        try:
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.update_count = checkpoint.get('update_count', 0)
            self.total_samples = checkpoint.get('total_samples', 0)
            self.performance_history = checkpoint.get('performance_history', [])
            
            self.logger.info(f"✅ 增量学习模型已加载: {path}")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 加载模型失败: {e}")
            return False


def create_incremental_learner(
    model: Optional[nn.Module] = None,
    config: Optional[Dict[str, Any]] = None
) -> IncrementalLearner:
    """
    创建增量学习器的便捷函数
    
    Args:
        model: 深度学习模型
        config: 配置字典
        
    Returns:
        增量学习器实例
    """
    if config is None:
        config = {}
    
    return IncrementalLearner(
        model=model,
        learning_rate=config.get('learning_rate', 0.001),
        batch_size=config.get('batch_size', 32),
        buffer_size=config.get('buffer_size', 1000),
        update_frequency=config.get('update_frequency', 10)
    )
