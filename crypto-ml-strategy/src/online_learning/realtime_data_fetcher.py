"""
实时数据获取器 - 专门为在线学习优化的数据获取模块
使用InfluxDB作为主要数据源，支持实时特征生成
"""

import asyncio
import logging
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
import pandas as pd
import numpy as np

from src.data.influxdb_client import InfluxDBDataClient
from src.data.java_data_service import JavaDataService
from src.data.numba_accelerated_features import NumbaAcceleratedFeatures
from src.utils.config import get_config_manager


class RealtimeDataFetcher:
    """
    实时数据获取器 - 为在线学习优化
    
    特点：
    1. 优先使用InfluxDB获取最新数据
    2. 支持多时间框架数据融合
    3. 实时特征计算和缓存
    4. 智能回退机制
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.logger = logging.getLogger(__name__)
        self.config_manager = get_config_manager()
        
        # 配置管理
        self.data_config = config or self.config_manager.get('data', {})
        self.influx_config = self.config_manager.get('storage.influxdb', {})
        
        # 数据源客户端
        self.influx_client = None
        self.java_service = None
        self.feature_engine = NumbaAcceleratedFeatures()
        
        # 缓存管理
        self.data_cache = {}
        self.feature_cache = {}
        self.cache_ttl = 300  # 5分钟缓存
        
        # 配置参数
        self.min_data_points = self.data_config.get('min_data_points', 20)  # 降低要求
        self.default_timeframes = ['15m', '5m']  # 使用InfluxDB中有足够数据的时间框架
        
        self.logger.info("✅ RealtimeDataFetcher初始化完成")
    
    async def initialize(self):
        """初始化数据源连接"""
        try:
            # 初始化InfluxDB客户端
            if self.influx_config:
                self.influx_client = InfluxDBDataClient(self.influx_config)
                await self.influx_client.connect()
                self.logger.info("✅ InfluxDB客户端连接成功")
            
            # 初始化Java服务客户端
            java_config = self.config_manager.get('data_sources.java_service', {})
            if java_config.get('enabled', True):
                self.java_service = JavaDataService(java_config)
                self.logger.info("✅ Java服务客户端初始化完成")
            
        except Exception as e:
            self.logger.error(f"❌ 数据源初始化失败: {e}")
            raise
    
    async def get_latest_data(
        self, 
        symbol: str, 
        timeframe: str = '1h',
        limit: int = 200
    ) -> Optional[pd.DataFrame]:
        """
        获取最新的市场数据
        
        Args:
            symbol: 交易对符号
            timeframe: 时间框架
            limit: 数据条数限制
            
        Returns:
            包含OHLCV数据的DataFrame
        """
        cache_key = f"{symbol}_{timeframe}_{limit}"
        
        # 检查缓存
        if self._is_cache_valid(cache_key):
            self.logger.debug(f"📋 使用缓存数据: {cache_key}")
            return self.data_cache[cache_key]['data']
        
        # 尝试从InfluxDB获取数据
        data = await self._fetch_from_influxdb(symbol, timeframe, limit)
        
        # 如果InfluxDB数据不足，尝试Java服务
        if data is None or len(data) < self.min_data_points:
            self.logger.warning(f"⚠️ InfluxDB数据不足({len(data) if data is not None else 0}条)，尝试Java服务")
            data = await self._fetch_from_java_service(symbol, timeframe, limit)
        
        # 缓存数据
        if data is not None and not data.empty:
            self.data_cache[cache_key] = {
                'data': data,
                'timestamp': datetime.now()
            }
            self.logger.info(f"✅ 获取最新数据成功: {symbol} {timeframe} ({len(data)}条)")
        else:
            self.logger.error(f"❌ 无法获取数据: {symbol} {timeframe}")
        
        return data
    
    async def _fetch_from_influxdb(
        self, 
        symbol: str, 
        timeframe: str, 
        limit: int
    ) -> Optional[pd.DataFrame]:
        """从InfluxDB获取数据"""
        if not self.influx_client:
            return None
        
        try:
            self.logger.debug(f"🔍 从InfluxDB获取数据: {symbol} {timeframe}")
            data = await self.influx_client.fetch_historical_data(
                symbol=symbol,
                timeframe=timeframe,
                limit=limit
            )
            
            if data is not None and not data.empty:
                self.logger.info(f"✅ InfluxDB数据获取成功: {len(data)}条")
                return data
            else:
                self.logger.warning(f"⚠️ InfluxDB未返回数据: {symbol} {timeframe}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ InfluxDB数据获取失败: {e}")
            return None
    
    async def _fetch_from_java_service(
        self, 
        symbol: str, 
        timeframe: str, 
        limit: int
    ) -> Optional[pd.DataFrame]:
        """从Java服务获取数据"""
        if not self.java_service:
            return None
        
        try:
            self.logger.debug(f"🔍 从Java服务获取数据: {symbol} {timeframe}")
            
            # 计算时间范围
            end_time = datetime.now()
            if timeframe == '1h':
                start_time = end_time - timedelta(hours=limit)
            elif timeframe == '4h':
                start_time = end_time - timedelta(hours=limit * 4)
            elif timeframe == '1d':
                start_time = end_time - timedelta(days=limit)
            else:
                start_time = end_time - timedelta(hours=limit)
            
            data = await self.java_service.fetch_historical_data(
                symbol=symbol,
                timeframe=timeframe,
                limit=limit
            )
            
            if data is not None and not data.empty:
                self.logger.info(f"✅ Java服务数据获取成功: {len(data)}条")
                return data
            else:
                self.logger.warning(f"⚠️ Java服务未返回数据: {symbol} {timeframe}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Java服务数据获取失败: {e}")
            return None
    
    async def generate_realtime_features(
        self,
        symbol: str,
        timeframes: Optional[List[str]] = None,
        adaptive_requirements: bool = True
    ) -> Optional[pd.DataFrame]:
        """
        生成实时特征（支持多时间框架混合训练）

        Args:
            symbol: 交易对符号
            timeframes: 时间框架列表
            adaptive_requirements: 是否启用自适应数据要求

        Returns:
            包含特征的DataFrame
        """
        if timeframes is None:
            # 使用全时间框架进行混合训练
            timeframes = ["1m", "3m", "5m", "15m", "1h", "4h", "1d"]
        
        cache_key = f"features_{symbol}_{'_'.join(timeframes)}"
        
        # 检查特征缓存
        if self._is_cache_valid(cache_key, cache_type='feature'):
            self.logger.debug(f"📋 使用缓存特征: {cache_key}")
            return self.feature_cache[cache_key]['data']
        
        try:
            all_features = []
            successful_timeframes = []

            # 定义时间框架优先级（基于数据可用性）
            timeframe_priority = {
                "1m": 1, "3m": 2, "5m": 3, "15m": 4,
                "1h": 5, "4h": 6, "1d": 7
            }

            # 按优先级排序时间框架
            sorted_timeframes = sorted(timeframes, key=lambda x: timeframe_priority.get(x, 999))

            for timeframe in sorted_timeframes:
                # 根据时间框架调整数据获取量
                limit = self._get_adaptive_limit(timeframe)

                # 获取原始数据
                raw_data = await self.get_latest_data(symbol, timeframe, limit=limit)

                if raw_data is None or raw_data.empty:
                    if adaptive_requirements:
                        self.logger.debug(f"⚠️ 跳过 {symbol} {timeframe} (无数据)")
                        continue
                    else:
                        self.logger.warning(f"⚠️ 无法获取{symbol} {timeframe}数据，跳过特征生成")
                        continue

                # 检查数据量是否足够
                min_required = 20 if adaptive_requirements else 50
                if len(raw_data) < min_required:
                    if adaptive_requirements:
                        self.logger.debug(f"⚠️ {symbol} {timeframe} 数据量不足 ({len(raw_data)}<{min_required})，但继续处理")
                    else:
                        self.logger.warning(f"⚠️ {symbol} {timeframe} 数据量不足，跳过")
                        continue
                
                # 生成技术指标特征
                # 准备NumPy数组格式的数据
                kline_data = {
                    'close': raw_data['close'].values,
                    'high': raw_data['high'].values,
                    'low': raw_data['low'].values,
                    'volume': raw_data['volume'].values
                }

                features = self.feature_engine.create_technical_indicators_np(kline_data, raw_data.index)
                
                if features is not None and not features.empty:
                    # 添加时间框架标识
                    feature_columns = {}
                    for col in features.columns:
                        if col not in ['open', 'high', 'low', 'close', 'volume']:
                            feature_columns[col] = f"{col}_{timeframe}"

                    features = features.rename(columns=feature_columns)
                    all_features.append(features.tail(1))  # 只取最新的一行
                    successful_timeframes.append(timeframe)

                    self.logger.info(f"✅ {symbol} {timeframe}特征生成成功: {len(features.columns)}个特征")
            
            if not all_features:
                self.logger.error(f"❌ 无法为{symbol}生成任何特征")
                return None
            
            # 合并所有时间框架的特征
            combined_features = pd.concat(all_features, axis=1)
            combined_features = combined_features.fillna(method='ffill').fillna(0)
            
            # 缓存特征
            self.feature_cache[cache_key] = {
                'data': combined_features,
                'timestamp': datetime.now()
            }
            
            self.logger.info(f"✅ {symbol}实时特征生成完成: {combined_features.shape}")
            return combined_features
            
        except Exception as e:
            self.logger.error(f"❌ 实时特征生成失败: {e}")
            return None

    def _get_adaptive_limit(self, timeframe: str) -> int:
        """
        根据时间框架获取自适应数据限制

        Args:
            timeframe: 时间框架

        Returns:
            数据限制数量
        """
        # 根据时间框架的数据密度调整获取量
        timeframe_limits = {
            "1m": 100,   # 1分钟数据密度高，获取较少
            "3m": 120,   # 3分钟数据
            "5m": 150,   # 5分钟数据
            "15m": 200,  # 15分钟数据
            "1h": 250,   # 1小时数据
            "4h": 300,   # 4小时数据
            "1d": 350    # 1天数据密度低，获取较多
        }

        return timeframe_limits.get(timeframe, 200)
    
    def _is_cache_valid(self, cache_key: str, cache_type: str = 'data') -> bool:
        """检查缓存是否有效"""
        cache = self.data_cache if cache_type == 'data' else self.feature_cache
        
        if cache_key not in cache:
            return False
        
        cache_time = cache[cache_key]['timestamp']
        return (datetime.now() - cache_time).seconds < self.cache_ttl
    
    def clear_cache(self):
        """清空缓存"""
        self.data_cache.clear()
        self.feature_cache.clear()
        self.logger.info("🧹 缓存已清空")
    
    async def close(self):
        """关闭连接"""
        if self.influx_client:
            await self.influx_client.close()
        
        self.clear_cache()
        self.logger.info("✅ RealtimeDataFetcher已关闭")
