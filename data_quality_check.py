
import json
from datetime import datetime, <PERSON><PERSON><PERSON>

def generate_report_script():
    """
    Generates a shell script to query the database and create a JSON report.
    This approach avoids the need for Python database drivers.
    """
    symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'SOLUSDT', 'DOGEUSDT']
    report_file = "/tmp/data_quality_report.json"
    temp_result_file = "/tmp/db_results.tmp"
    
    ninety_days_ago = datetime.utcnow() - timedelta(days=90)
    ninety_days_ago_ts = int(ninety_days_ago.timestamp() * 1000)
    expected_kline_count = 129600

    # Shell script header
    script_content = f"""#!/bin/bash
DB_USER="root"
DB_PASS="root"
DB_HOST="localhost"
DB_PORT="13306"
DB_NAME="crypto_market_data"

MYSQL_CMD="mysql -u$DB_USER -p$DB_PASS -h$DB_HOST -P$DB_PORT $DB_NAME -N -s"

# Initialize JSON report
echo '{{' > {report_file}
echo '  "report_generated_at": "{datetime.utcnow().isoformat()}Z",' >> {report_file}
echo '  "time_range_days": 90,' >> {report_file}
echo '  "kline_interval": "1m",' >> {report_file}
echo '  "expected_kline_count": {expected_kline_count},' >> {report_file}
echo '  "trading_pairs": {{' >> {report_file}
"""

    # Loop through symbols to generate queries
    for i, symbol in enumerate(symbols):
        kline_table = "kline_1m"
        
        # Query for K-line count
        query_kline_count = f"SELECT COUNT(*) FROM {kline_table} WHERE symbol = '{symbol}' AND open_time >= {ninety_days_ago_ts};"
        # Query for K-line time range
        query_kline_time = f"SELECT MIN(open_time), MAX(open_time) FROM {kline_table} WHERE symbol = '{symbol}';"
        # Query for market_data time range
        query_market_data_time = f"SELECT MIN(trade_time), MAX(trade_time) FROM market_data WHERE symbol = '{symbol}' AND trade_time >= {ninety_days_ago_ts};"

        script_content += f"""
# --- Processing {symbol} ---
KLINE_COUNT=$($MYSQL_CMD -e "{query_kline_count}")
KLINE_TIME_RANGE=$($MYSQL_CMD -e "{query_kline_time}")
MARKET_DATA_TIME_RANGE=$($MYSQL_CMD -e "{query_market_data_time}")

KLINE_MIN_TS=$(echo $KLINE_TIME_RANGE | cut -d' ' -f1)
KLINE_MAX_TS=$(echo $KLINE_TIME_RANGE | cut -d' ' -f2)
MARKET_MIN_TS=$(echo $MARKET_DATA_TIME_RANGE | cut -d' ' -f1)
MARKET_MAX_TS=$(echo $MARKET_DATA_TIME_RANGE | cut -d' ' -f2)

# Calculate completeness
COMPLETENESS=$(echo "scale=4; $KLINE_COUNT / {expected_kline_count} * 100" | bc)
QUALITY_SCORE=$COMPLETENESS

# Convert timestamps to ISO format if not NULL
KLINE_MIN_ISO=$( [ "$KLINE_MIN_TS" != "NULL" ] && date -d @$(($KLINE_MIN_TS/1000)) --iso-8601=seconds || echo "null" )
KLINE_MAX_ISO=$( [ "$KLINE_MAX_TS" != "NULL" ] && date -d @$(($KLINE_MAX_TS/1000)) --iso-8601=seconds || echo "null" )
MARKET_MIN_ISO=$( [ "$MARKET_MIN_TS" != "NULL" ] && date -d @$(($MARKET_MIN_TS/1000)) --iso-8601=seconds || echo "null" )
MARKET_MAX_ISO=$( [ "$MARKET_MAX_TS" != "NULL" ] && date -d @$(($MARKET_MAX_TS/1000)) --iso-8601=seconds || echo "null" )

# Append to JSON report
echo '    "{symbol}": {{' >> {report_file}
echo '      "kline_actual_count": '$KLINE_COUNT',' >> {report_file}
echo '      "kline_completeness_percentage": '$COMPLETENESS',' >> {report_file}
echo '      "kline_min_timestamp": "'$KLINE_MIN_ISO'",' >> {report_file}
echo '      "kline_max_timestamp": "'$KLINE_MAX_ISO'",' >> {report_file}
echo '      "market_data_min_timestamp": "'$MARKET_MIN_ISO'",' >> {report_file}
echo '      "market_data_max_timestamp": "'$MARKET_MAX_ISO'",' >> {report_file}
echo '      "quality_score": '$QUALITY_SCORE >> {report_file}
echo '    }}'{',' if i < len(symbols) - 1 else ''} >> {report_file}
"""

    # Shell script footer
    script_content += f"""
# --- Finalize JSON ---
echo '  }}' >> {report_file}
echo '}}' >> {report_file}

echo "Data quality report generated successfully at {report_file}"
"""
    
    # Save the generation script
    with open("/home/<USER>/code/generate_report_script.sh", "w") as f:
        f.write(script_content)

if __name__ == "__main__":
    generate_report_script()
    print("Shell script 'generate_report_script.sh' created successfully.")

