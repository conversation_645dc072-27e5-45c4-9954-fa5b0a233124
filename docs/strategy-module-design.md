# 统一机器学习策略模块设计大纲

## 1. 项目概述

### 1.1 设计目标
本文档描述了一个简化的统一机器学习策略模块设计，将所有传统策略（技术指标、经典交易、反转形态等）的信号作为特征输入，使用单一的机器学习模型进行信号融合和决策，输出统一的交易信号。

### 1.2 核心特性
- **单一ML模型**：将所有策略信号整合为特征，使用统一模型进行决策
- **特征工程**：LPPL泡沫检测、Hematread、BMSB、SuperTrend等策略信号转换为ML特征
- **信号融合**：智能融合多种策略信号，输出统一交易决策
- **知识蒸馏**：使用DeepSeek API进行模型优化和知识传递
- **异步处理**：基于Kafka的高效信号传输
- **Java API集成**：通过HistoricalDataController.java统一数据访问

## 2. 统一ML模型架构设计

### 2.1 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    统一ML策略模块架构                         │
├─────────────────────────────────────────────────────────────┤
│                      特征工程层                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ 技术指标特征提取 │  │ 经典策略特征提取 │  │ 形态识别特征提取 │ │
│  │                │  │                │  │                │ │
│  │ • LPPL泡沫概率  │  │ • SuperTrend信号│  │ • 支撑阻力强度  │ │
│  │ • Hematread动量 │  │ • 海龟突破信号  │  │ • 反转形态概率  │ │
│  │ • BMSB支撑强度  │  │ • 趋势跟踪信号  │  │ • 突破确认度    │ │
│  │ • SuperTrend方向│  │ • 动量指标     │  │ • 形态完整度    │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    统一ML模型层                              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 信号融合ML模型                           │ │
│  │                                                        │ │
│  │  特征输入 → 特征标准化 → 深度神经网络 → 信号融合 → 交易决策  │ │
│  │                                                        │ │
│  │  • 多层感知机 (MLP)     • 注意力机制                    │ │
│  │  • 长短期记忆网络 (LSTM) • DeepSeek知识蒸馏             │ │
│  │  • 集成学习算法         • 在线学习更新                  │ │
│  └─────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                      数据与通信层                            │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐ │
│  │ Java API客户端  │  │ Kafka信号发布   │  │ DeepSeek集成    │ │
│  │                │  │                │  │                │ │
│  │ • 历史数据获取  │  │ • 异步信号传输  │  │ • 教师模型API   │ │
│  │ • 实时数据流    │  │ • 消息队列管理  │  │ • 知识蒸馏训练  │ │
│  │ • 数据预处理    │  │ • 事件驱动架构  │  │ • 模型优化     │ │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 简化模块组织结构
```
crypto_ml_strategy/
├── src/
│   ├── ml/                  # 机器学习核心模块
│   │   ├── features/        # 统一特征工程
│   │   │   ├── unified_feature_extractor.py    # 统一特征提取器
│   │   │   ├── strategy_signal_features.py     # 策略信号特征
│   │   │   ├── technical_indicators.py         # 技术指标计算
│   │   │   └── feature_config.py               # 特征配置
│   │   ├── models/          # ML模型定义
│   │   │   ├── unified_signal_fusion_model.py  # 统一信号融合模型
│   │   │   ├── ensemble_models.py              # 集成学习模型
│   │   │   └── model_config.py                 # 模型配置
│   │   ├── training/        # 训练模块
│   │   │   ├── distillation_trainer.py         # 知识蒸馏训练器
│   │   │   ├── online_learner.py               # 在线学习
│   │   │   └── training_pipeline.py            # 训练流程
│   │   └── inference/       # 推理模块
│   │       ├── signal_predictor.py             # 信号预测器
│   │       └── real_time_inference.py          # 实时推理
│   ├── data/                # 数据处理
│   │   ├── java_api_client.py                  # Java API客户端
│   │   ├── data_preprocessor.py                # 数据预处理
│   │   └── data_validator.py                   # 数据验证
│   ├── messaging/           # 消息处理
│   │   ├── kafka_signal_publisher.py           # Kafka信号发布
│   │   └── async_data_processor.py             # 异步数据处理
│   ├── ai/                  # AI集成
│   │   ├── deepseek_client.py                  # DeepSeek API客户端
│   │   └── knowledge_distillation.py           # 知识蒸馏
│   └── utils/               # 工具类
│       ├── config_manager.py                   # 配置管理
│       └── logging_utils.py                    # 日志工具
├── tests/                   # 测试模块
│   ├── ml/                  # ML模块测试
│   ├── integration/         # 集成测试
│   └── validation/          # 验证测试
├── config/                  # 配置文件
│   ├── model_config.yaml    # 模型配置
│   ├── feature_config.yaml  # 特征配置
│   └── api_config.yaml      # API配置
└── docs/                    # 文档
```

## 3. 统一特征工程设计

### 3.1 策略信号特征化

#### 3.1.1 技术指标特征组
将传统技术指标策略转换为ML特征：

**LPPL泡沫检测特征**：
- `lppl_bubble_probability`: 泡沫形成概率 [0-1]
- `lppl_critical_time`: 临界时间距离 (标准化)
- `lppl_oscillation_freq`: 振荡频率特征
- `lppl_price_deviation`: 价格偏离度

**Hematread动量特征**：
- `hematread_momentum_strength`: 动量强度 [-1, 1]
- `hematread_trend_direction`: 趋势方向 {-1, 0, 1}
- `hematread_volume_momentum`: 成交量动量
- `hematread_acceleration`: 动量加速度

**BMSB支撑特征**：
- `bmsb_support_strength`: 支撑强度 [0-1]
- `bmsb_price_position`: 价格相对位置 [0-1]
- `bmsb_breakout_probability`: 突破概率 [0-1]
- `bmsb_support_distance`: 距离支撑位距离

**SuperTrend特征**：
- `supertrend_direction`: 趋势方向 {-1, 1}
- `supertrend_strength`: 趋势强度 [0-1]
- `supertrend_distance`: 价格距离趋势线
- `supertrend_volatility`: ATR标准化波动率

#### 3.1.2 经典策略特征组
**海龟交易特征**：
- `turtle_breakout_signal`: 突破信号强度 [0-1]
- `turtle_channel_position`: 通道内位置 [0-1]
- `turtle_volatility_ratio`: 波动率比率
- `turtle_trend_duration`: 趋势持续时间

**形态识别特征**：
- `pattern_reversal_probability`: 反转形态概率 [0-1]
- `support_resistance_strength`: 支撑阻力强度 [0-1]
- `breakout_confirmation`: 突破确认度 [0-1]
- `volume_confirmation`: 成交量确认度 [0-1]

### 3.2 特征工程流程
```python
class UnifiedFeatureExtractor:
    """统一特征提取器"""

    def __init__(self, config):
        self.technical_extractor = TechnicalIndicatorExtractor()
        self.classical_extractor = ClassicalStrategyExtractor()
        self.pattern_extractor = PatternRecognitionExtractor()

    def extract_features(self, market_data):
        """提取所有策略特征"""
        features = {}

        # 技术指标特征
        features.update(self.technical_extractor.extract_lppl_features(market_data))
        features.update(self.technical_extractor.extract_hematread_features(market_data))
        features.update(self.technical_extractor.extract_bmsb_features(market_data))
        features.update(self.technical_extractor.extract_supertrend_features(market_data))

        # 经典策略特征
        features.update(self.classical_extractor.extract_turtle_features(market_data))

        # 形态识别特征
        features.update(self.pattern_extractor.extract_pattern_features(market_data))

        return self.normalize_features(features)
```

## 4. 统一ML模型架构

### 4.1 信号融合模型设计
```python
class UnifiedSignalFusionModel:
    """统一信号融合ML模型"""

    def __init__(self, config):
        self.feature_dim = config.feature_dim  # 所有策略特征维度
        self.hidden_dims = config.hidden_dims  # 隐藏层维度
        self.output_dim = 3  # BUY, SELL, HOLD

        # 模型组件
        self.feature_encoder = self._build_feature_encoder()
        self.attention_layer = self._build_attention_mechanism()
        self.fusion_network = self._build_fusion_network()
        self.output_layer = self._build_output_layer()

    def _build_feature_encoder(self):
        """特征编码器 - 处理不同类型的策略特征"""
        return nn.Sequential(
            nn.Linear(self.feature_dim, self.hidden_dims[0]),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(self.hidden_dims[0], self.hidden_dims[1]),
            nn.ReLU()
        )

    def _build_attention_mechanism(self):
        """注意力机制 - 动态权重分配不同策略信号"""
        return MultiHeadAttention(
            embed_dim=self.hidden_dims[1],
            num_heads=8,
            dropout=0.1
        )

    def _build_fusion_network(self):
        """信号融合网络 - LSTM处理时序信息"""
        return nn.LSTM(
            input_size=self.hidden_dims[1],
            hidden_size=self.hidden_dims[2],
            num_layers=2,
            dropout=0.2,
            batch_first=True
        )

    def forward(self, features, sequence_length=20):
        """前向传播 - 特征 → 编码 → 注意力 → 融合 → 决策"""
        # 特征编码
        encoded_features = self.feature_encoder(features)

        # 注意力权重计算
        attended_features, attention_weights = self.attention_layer(
            encoded_features, encoded_features, encoded_features
        )

        # 时序信息融合
        lstm_out, (hidden, cell) = self.fusion_network(attended_features)

        # 最终决策输出
        output = self.output_layer(lstm_out[:, -1, :])  # 取最后时刻输出

        return {
            'signal': output,
            'attention_weights': attention_weights,
            'confidence': torch.softmax(output, dim=-1).max(dim=-1)[0]
        }
```

### 4.2 DeepSeek知识蒸馏
```python
class DeepSeekDistillationTrainer:
    """DeepSeek知识蒸馏训练器"""

    def __init__(self, student_model, deepseek_client):
        self.student_model = student_model
        self.deepseek_client = deepseek_client
        self.temperature = 4.0  # 蒸馏温度

    def distillation_loss(self, student_logits, teacher_logits, true_labels):
        """知识蒸馏损失函数"""
        # 软标签损失 (知识蒸馏)
        soft_loss = F.kl_div(
            F.log_softmax(student_logits / self.temperature, dim=-1),
            F.softmax(teacher_logits / self.temperature, dim=-1),
            reduction='batchmean'
        ) * (self.temperature ** 2)

        # 硬标签损失 (真实标签)
        hard_loss = F.cross_entropy(student_logits, true_labels)

        # 组合损失
        return 0.7 * soft_loss + 0.3 * hard_loss

    def train_with_distillation(self, train_data):
        """使用知识蒸馏训练学生模型"""
        for batch in train_data:
            # 获取DeepSeek教师模型预测
            teacher_predictions = self.deepseek_client.predict_batch(batch)

            # 学生模型预测
            student_output = self.student_model(batch['features'])

            # 计算蒸馏损失
            loss = self.distillation_loss(
                student_output['signal'],
                teacher_predictions,
                batch['labels']
            )

            # 反向传播更新
            loss.backward()
            self.optimizer.step()
```

### 4.3 在线学习与模型更新
- **增量学习**：新数据到达时持续更新模型参数
- **概念漂移检测**：监控市场环境变化，触发模型重训练
- **模型版本管理**：维护多个模型版本，支持回滚
- **性能监控**：实时监控F1分数，确保在0.5-0.7范围内

## 5. 简化数据流转设计

### 5.1 统一数据流程
```
Java API数据获取 → 特征工程 → ML模型推理 → 信号融合 → Kafka发布 → Java交易执行
```

### 5.2 核心数据流组件
```python
class UnifiedDataPipeline:
    """统一数据处理流水线"""

    def __init__(self):
        self.java_api_client = JavaAPIClient()
        self.feature_extractor = UnifiedFeatureExtractor()
        self.ml_model = UnifiedSignalFusionModel()
        self.kafka_publisher = KafkaSignalPublisher()

    async def process_market_data(self, symbol, timeframe):
        """处理市场数据并生成交易信号"""
        # 1. 从Java API获取历史数据
        market_data = await self.java_api_client.get_market_data(
            symbol=symbol,
            timeframe=timeframe,
            lookback=100  # 获取100个周期的数据
        )

        # 2. 特征工程 - 将所有策略信号转换为特征
        features = self.feature_extractor.extract_features(market_data)

        # 3. ML模型推理 - 信号融合决策
        prediction = self.ml_model.predict(features)

        # 4. 生成统一交易信号
        trading_signal = self._generate_trading_signal(prediction)

        # 5. 通过Kafka发布信号
        await self.kafka_publisher.publish_signal(trading_signal)

        return trading_signal
```

### 5.3 Java API集成
- **历史数据获取**：通过HistoricalDataController.java REST API
- **数据格式标准化**：统一的JSON响应格式处理
- **缓存机制**：本地缓存减少API调用频率
- **错误处理**：API超时和重试机制

## 6. 性能与质量保证

### 6.1 关键性能指标
- **F1分数控制**：严格维持在0.5-0.7范围，避免数据泄漏
- **信号延迟**：从数据获取到信号生成<100ms
- **预测准确率**：平衡精确率和召回率
- **模型稳定性**：避免过拟合，确保泛化能力

### 6.2 数据泄漏防护
```python
class DataLeakageValidator:
    """数据泄漏检测器"""

    def validate_f1_score(self, f1_score, threshold_min=0.5, threshold_max=0.7):
        """验证F1分数是否在合理范围"""
        if f1_score > threshold_max:
            raise DataLeakageError(f"F1分数{f1_score:.3f}过高，可能存在数据泄漏")
        elif f1_score < threshold_min:
            raise ModelPerformanceError(f"F1分数{f1_score:.3f}过低，模型性能不足")
        return True

    def check_temporal_split(self, train_data, test_data):
        """检查时间序列分割是否正确"""
        if train_data['timestamp'].max() >= test_data['timestamp'].min():
            raise DataLeakageError("训练数据时间范围与测试数据重叠")
        return True
```

### 6.3 测试框架
- **单元测试**：特征提取、模型推理、信号生成各模块测试
- **集成测试**：端到端数据流测试
- **性能测试**：延迟和吞吐量基准测试
- **回测验证**：历史数据严格时间分割回测

## 7. 部署与监控

### 7.1 简化部署架构
```yaml
# Docker Compose 部署配置
services:
  ml-strategy-service:
    image: crypto-ml-strategy:latest
    environment:
      - JAVA_API_URL=http://crypto-bootstrap:8080/api/v1
      - KAFKA_BROKERS=kafka:9092
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
    depends_on:
      - kafka
      - crypto-bootstrap

  kafka:
    image: confluentinc/cp-kafka:latest

  crypto-bootstrap:
    image: crypto-trading:java-latest
```

### 7.2 监控指标
- **模型性能**：实时F1分数、准确率监控
- **系统性能**：CPU、内存、GPU使用率
- **业务指标**：信号生成频率、延迟分布
- **错误监控**：API调用失败、模型推理异常

## 8. 扩展性设计

### 8.1 特征扩展
- **新策略特征**：易于添加新的策略信号特征
- **外部数据**：支持集成宏观经济、新闻情感等外部特征
- **多时间框架**：支持不同时间周期的特征组合

### 8.2 模型扩展
- **模型架构**：支持更换不同的深度学习架构
- **集成方法**：支持多模型集成和投票机制
- **在线学习**：支持实时模型参数更新
