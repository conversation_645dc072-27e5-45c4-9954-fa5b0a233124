# API接口设计文档 - 统一ML策略模块

## 1. 接口概述

### 1.1 设计原则
- **简化性**: 专注于ML模型服务，减少接口复杂度
- **统一性**: 统一的特征输入和信号输出格式
- **高效性**: 优化的数据传输和模型推理性能
- **可靠性**: 完善的错误处理和重试机制
- **可监控性**: 全面的性能指标和日志记录

### 1.2 接口分类
- **数据获取接口**: Java历史数据API集成
- **ML模型服务接口**: 统一信号预测和模型管理
- **Kafka消息接口**: 异步信号传输
- **DeepSeek集成接口**: 知识蒸馏和模型优化
- **监控验证接口**: F1分数验证和性能监控

## 2. Java数据访问API

### 2.1 历史数据控制器API

#### 2.1.1 K线数据接口
```http
GET /api/v1/historical-data/kline
```

**请求参数**:
```json
{
  "symbol": "BTCUSDT",           // 交易对
  "interval": "1h",              // 时间间隔 (1m,5m,15m,30m,1h,4h,1d)
  "startTime": 1609459200000,    // 开始时间戳(毫秒)
  "endTime": 1609545600000,      // 结束时间戳(毫秒)
  "limit": 1000                  // 限制条数(可选,默认500,最大1000)
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "openTime": 1609459200000,
      "open": "29000.00",
      "high": "29500.00", 
      "low": "28800.00",
      "close": "29200.00",
      "volume": "1234.56",
      "closeTime": 1609462799999,
      "quoteAssetVolume": "35987654.32",
      "numberOfTrades": 5678,
      "takerBuyBaseAssetVolume": "567.89",
      "takerBuyQuoteAssetVolume": "16543210.98"
    }
  ],
  "timestamp": 1609459260000
}
```

#### 2.1.2 深度数据接口
```http
GET /api/v1/historical-data/depth
```

**请求参数**:
```json
{
  "symbol": "BTCUSDT",
  "startTime": 1609459200000,
  "endTime": 1609545600000,
  "limit": 100
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success", 
  "data": [
    {
      "timestamp": 1609459200000,
      "bids": [
        ["29000.00", "1.234"],
        ["28999.00", "2.345"]
      ],
      "asks": [
        ["29001.00", "1.567"],
        ["29002.00", "2.678"]
      ],
      "lastUpdateId": 123456789
    }
  ],
  "timestamp": 1609459260000
}
```

#### 2.1.3 交易数据接口
```http
GET /api/v1/historical-data/trades
```

**请求参数**:
```json
{
  "symbol": "BTCUSDT",
  "startTime": 1609459200000,
  "endTime": 1609545600000,
  "limit": 1000
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 123456789,
      "price": "29000.00",
      "qty": "1.234",
      "quoteQty": "35806.00",
      "time": 1609459200000,
      "isBuyerMaker": false,
      "isBestMatch": true
    }
  ],
  "timestamp": 1609459260000
}
```

### 2.2 批量数据接口

#### 2.2.1 批量数据下载
```http
POST /api/v1/historical-data/batch
```

**请求体**:
```json
{
  "requests": [
    {
      "type": "kline",
      "symbol": "BTCUSDT", 
      "interval": "1h",
      "startTime": 1609459200000,
      "endTime": 1609545600000
    },
    {
      "type": "depth",
      "symbol": "BTCUSDT",
      "startTime": 1609459200000,
      "endTime": 1609545600000
    }
  ]
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "taskId": "batch-task-uuid-123",
    "status": "PROCESSING",
    "results": [
      {
        "type": "kline",
        "status": "COMPLETED",
        "data": [...],
        "count": 24
      },
      {
        "type": "depth", 
        "status": "PROCESSING",
        "progress": 0.5
      }
    ]
  },
  "timestamp": 1609459260000
}
```

## 3. 统一ML模型服务API

### 3.1 信号预测接口

#### 3.1.1 实时信号预测
```http
POST /api/v1/ml/predict
```

**请求体**:
```json
{
  "symbol": "BTCUSDT",
  "timeframe": "1h",
  "market_data": {
    "kline_data": [...],  // 最近100个K线数据
    "depth_data": [...],  // 深度数据
    "trade_data": [...]   // 交易数据
  },
  "model_version": "v1.0"
}
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "signal_id": "signal-uuid-123",
    "symbol": "BTCUSDT",
    "prediction": {
      "signal": "BUY",           // BUY, SELL, HOLD
      "confidence": 0.85,        // 置信度 [0-1]
      "probability": {
        "buy": 0.65,
        "sell": 0.15,
        "hold": 0.20
      }
    },
    "features": {
      "lppl_bubble_probability": 0.25,
      "hematread_momentum": 0.30,
      "bmsb_support_strength": 0.15,
      "supertrend_direction": 1,
      "turtle_breakout_signal": 0.40,
      "pattern_reversal_probability": 0.10
    },
    "attention_weights": {
      "technical_indicators": 0.45,
      "classical_strategies": 0.35,
      "pattern_recognition": 0.20
    },
    "model_info": {
      "version": "v1.0",
      "training_date": "2025-06-20",
      "f1_score": 0.65,
      "inference_time_ms": 45
    }
  },
  "timestamp": 1609459260000
}
```

#### 3.1.2 批量信号预测
```http
POST /api/v1/ml/predict/batch
```

**请求体**:
```json
{
  "requests": [
    {
      "symbol": "BTCUSDT",
      "timeframe": "1h",
      "market_data": {...}
    },
    {
      "symbol": "ETHUSDT",
      "timeframe": "1h",
      "market_data": {...}
    }
  ]
}
```

### 3.2 模型管理接口

#### 3.2.1 模型训练状态
```http
GET /api/v1/models/{modelId}/training-status
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "modelId": "unified-ml-model-v1.0",
    "status": "TRAINING",
    "progress": 0.75,
    "metrics": {
      "currentEpoch": 75,
      "totalEpochs": 100,
      "trainLoss": 0.0234,
      "valLoss": 0.0267,
      "f1Score": 0.65,
      "accuracy": 0.72
    },
    "estimatedCompletion": 1609459800000
  },
  "timestamp": 1609459260000
}
```

#### 3.2.2 模型性能评估
```http
GET /api/v1/models/{modelId}/performance
```

**响应格式**:
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "modelId": "unified-ml-model-v1.0",
    "evaluationPeriod": {
      "startTime": 1609459200000,
      "endTime": 1609545600000
    },
    "metrics": {
      "f1Score": 0.65,
      "precision": 0.68,
      "recall": 0.62,
      "accuracy": 0.72,
      "sharpeRatio": 1.45,
      "maxDrawdown": 0.08,
      "winRate": 0.58
    },
    "featureImportance": {
      "lppl_features": 0.25,
      "hematread_features": 0.30,
      "bmsb_features": 0.15,
      "supertrend_features": 0.30
    }
  },
  "timestamp": 1609459260000
}
```

## 4. Kafka消息接口

### 4.1 消息格式标准

#### 4.1.1 市场数据消息
**主题**: `market.data.kline`
```json
{
  "messageId": "msg-uuid-123",
  "messageType": "MARKET_DATA_KLINE",
  "timestamp": 1609459200000,
  "source": "binance-websocket",
  "data": {
    "symbol": "BTCUSDT",
    "interval": "1h",
    "openTime": 1609459200000,
    "closeTime": 1609462799999,
    "open": "29000.00",
    "high": "29500.00",
    "low": "28800.00", 
    "close": "29200.00",
    "volume": "1234.56"
  }
}
```

#### 4.1.2 策略信号消息
**主题**: `strategy.signals`
```json
{
  "messageId": "signal-uuid-456",
  "messageType": "STRATEGY_SIGNAL",
  "timestamp": 1609459260000,
  "source": "unified-ml-strategy",
  "data": {
    "strategyId": "unified-ml-strategy-001",
    "symbol": "BTCUSDT",
    "signalType": "BUY",
    "signalStrength": 0.85,
    "confidence": 0.92,
    "riskScore": 0.35,
    "expiryTime": 1609459560000,
    "metadata": {
      "modelVersion": "v1.0",
      "featureVersion": "v2.1",
      "processingTime": 45
    }
  }
}
```

#### 4.1.3 风险告警消息
**主题**: `risk.alerts`
```json
{
  "messageId": "alert-uuid-789",
  "messageType": "RISK_ALERT",
  "timestamp": 1609459300000,
  "source": "risk-manager",
  "data": {
    "alertType": "HIGH_RISK_SIGNAL",
    "severity": "HIGH",
    "symbol": "BTCUSDT",
    "riskScore": 0.85,
    "description": "策略信号风险评分超过阈值",
    "recommendations": [
      "降低仓位大小",
      "增加止损幅度",
      "暂停自动交易"
    ]
  }
}
```

## 5. DeepSeek AI接口

### 5.1 知识蒸馏接口

#### 5.1.1 教师模型预测
```python
# Python客户端调用示例
class DeepSeekClient:
    def predict_market_trend(self, market_data):
        """使用DeepSeek预测市场趋势"""
        payload = {
            "model": "deepseek-chat",
            "messages": [
                {
                    "role": "system",
                    "content": "你是一个专业的量化交易分析师..."
                },
                {
                    "role": "user", 
                    "content": f"分析以下市场数据: {market_data}"
                }
            ],
            "temperature": 0.1,
            "max_tokens": 1000
        }
        
        response = requests.post(
            "https://api.deepseek.com/v1/chat/completions",
            headers={"Authorization": f"Bearer {self.api_key}"},
            json=payload
        )
        
        return response.json()
```

### 5.2 在线学习接口

#### 5.2.1 模型更新通知
```json
{
  "messageType": "MODEL_UPDATE",
  "data": {
    "modelId": "unified-ml-model-v1.1",
    "updateType": "ONLINE_LEARNING",
    "performance": {
      "f1Score": 0.67,
      "improvement": 0.02
    },
    "updateTime": 1609459400000
  }
}
```

## 6. 错误处理与状态码

### 6.1 HTTP状态码
- `200 OK`: 请求成功
- `400 Bad Request`: 请求参数错误
- `401 Unauthorized`: 认证失败
- `403 Forbidden`: 权限不足
- `404 Not Found`: 资源不存在
- `429 Too Many Requests`: 请求频率超限
- `500 Internal Server Error`: 服务器内部错误
- `503 Service Unavailable`: 服务不可用

### 6.2 业务错误码
```json
{
  "code": 40001,
  "message": "策略参数验证失败",
  "details": {
    "field": "riskThreshold",
    "error": "值必须在0-1之间"
  },
  "timestamp": 1609459260000
}
```

### 6.3 错误码定义
- `40001-40099`: 参数验证错误
- `40101-40199`: 认证授权错误  
- `40201-40299`: 业务逻辑错误
- `40301-40399`: 数据访问错误
- `50001-50099`: 系统内部错误
- `50101-50199`: 外部服务错误

## 7. 接口安全

### 7.1 认证机制
- **API Key**: 用于服务间认证
- **JWT Token**: 用于用户会话认证
- **签名验证**: 关键接口使用HMAC签名

### 7.2 限流策略
```yaml
rate_limits:
  - endpoint: "/api/v1/historical-data/*"
    limit: "100/minute"
    burst: 20
  
  - endpoint: "/api/v1/strategies/*"
    limit: "50/minute"
    burst: 10
```

### 7.3 数据加密
- **传输加密**: 所有API使用HTTPS
- **敏感数据**: API密钥等敏感信息加密存储
- **数据脱敏**: 日志中敏感信息脱敏处理

## 8. 接口版本控制

### 8.1 版本策略
- **URL版本**: `/api/v1/`, `/api/v2/`
- **向后兼容**: 保持旧版本API可用
- **废弃通知**: 提前通知API废弃计划

### 8.2 版本迁移
```json
{
  "version": "v1",
  "deprecated": true,
  "deprecationDate": "2025-12-31",
  "migrationGuide": "https://docs.example.com/api/v1-to-v2-migration"
}
```
