# 数据流转图

由于当前环境无法创建图片文件，以下提供数据流转的文本描述，可以根据这个描述使用绘图工具（如 PlantUML、Draw.io 等）创建实际的数据流转图。

## 市场数据流转路径

```
╔════════════════════╗     WebSocket     ╔════════════════════╗
║                    ║    连接/订阅      ║                    ║
║    币安交易所      ╠═════════════════>║   WebSocket采集层  ║
║    WebSocket API   ║                   ║                    ║
╚════════════════════╝                   ╚═════════╦══════════╝
                                                   ║
                                                   ║ 原始数据
                                                   ▼
╔════════════════════╗                   ╔═════════╩══════════╗
║                    ║     数据发布      ║                    ║
║    Kafka主题:      ║<══════════════════╣   市场数据模块    ║
║    - kline.data    ║                   ║   (market-data)    ║
║    - depth.data    ║                   ║                    ║
║    - trade.data    ║                   ╚═════════╦══════════╝
╚══════════╦═════════╝                             ║
           ║                                       ║ 格式化数据
           ║                                       ║ (异步写入)
           ║                                       ▼
           ║                            ╔═════════════════════╗
           ║                            ║                     ║
           ║                            ║     InfluxDB        ║
           ║                            ║  (时序数据存储)     ║
           ║                            ║  (真实市场数据)     ║
           ║                            ╚═════════════════════╝
           ║
           ║ 数据消费
           ▼
╔════════════════════╗                  ╔══════════════════════╗
║                    ║                  ║                      ║
║   Python机器学习   ║                  ║   统一机器学习策略   ║
║   策略模块         ╠═════════════════>║   (UnifiedMLStrategy)║
║   (crypto-ml-strategy)║  分发数据     ║   - 整合LPPL策略     ║
║                    ║                  ║   - 整合hematread    ║
║                    ║                  ║   - 整合bull market  ║
║                    ║                  ║   - 内置风险评估     ║
╚══════════╦═════════╝                  ╚══════════╦═══════════╝
           ║                                       ║
           ║                                       ║ 生成风险评估后信号
           ║                                       ▼
╔══════════╩═════════╗                  
║                    ║     发布信号     
║   Kafka主题:       ║<═════════════════
║   strategy.signal  ║                  
║                    ║                  
╚══════════╦═════════╝
           ║
           ║ 信号消费
           ▼
╔════════════════════╗                  ╔══════════════════════╗
║                    ║    执行交易      ║                      ║
║   交易模块         ╠═════════════════>║   交易执行器         ║
║   (trade)          ║                  ║   (TradeExecutor)    ║
║                    ║                  ║                      ║
╚══════════╦═════════╝                  ╚══════════╦═══════════╝
           ║                                       ║
           ║                                       ║ 下单请求
           ║                                       ▼
╔══════════╩═════════╗                  ╔══════════════════════╗
║                    ║     API调用      ║                      ║
║   币安期货API      ║<═════════════════╣   订单管理服务       ║
║   (Binance         ║                  ║   (OrderService)     ║
║    Futures API)    ║                  ║                      ║
╚══════════╦═════════╝                  ╚══════════════════════╝
           ║
           ║ 订单响应
           ▼
╔════════════════════╗                  ╔══════════════════════╗
║                    ║    订单更新      ║                      ║
║   订单追踪器       ╠═════════════════>║   MySQL数据库        ║
║   (OrderTracker)   ║                  ║   - t_order          ║
║                    ║                  ║   - t_order_update   ║
╚══════════╦═════════╝                  ╚══════════════════════╝
           ║
           ║ 发布订单状态
           ▼
╔════════════════════╗                  ╔══════════════════════╗
║                    ║     状态更新     ║                      ║
║   Kafka主题:       ╠═════════════════>║   账户模块           ║
║   account.update   ║                  ║   (account)          ║
║                    ║                  ║                      ║
╚════════════════════╝                  ╚══════════╦═══════════╝
                                                   ║
                                                   ║ 更新账户信息
                                                   ▼
                                        ╔══════════════════════╗
                                        ║                      ║
                                        ║   MySQL数据库        ║
                                        ║   - t_position       ║
                                        ║   - t_account_snapshot║
                                        ║                      ║
                                        ╚══════════════════════╝
```

## 订单追踪流程

```
╔════════════════════╗     下单请求     ╔════════════════════╗
║                    ║    (client_id)   ║                    ║
║  Python机器学习    ╠═════════════════>║    交易模块        ║
║  策略模块          ║                  ║    (trade)         ║
║  (crypto-ml-strategy)║               ║                    ║
╚════════════════════╝                  ╚══════════╦═════════╝
                                                   ║
                                                   ║ 创建订单记录
                                                   ▼
╔════════════════════╗                   ╔═════════╩══════════╗
║                    ║     API调用       ║                    ║
║    币安期货API     ║<══════════════════╣    订单管理服务   ║
║                    ║                   ║    (OrderService)  ║
╚══════════╦═════════╝                   ╚════════════════════╝
           ║
           ║ 订单响应
           ▼
╔════════════════════╗                   ╔════════════════════╗
║                    ║    更新订单状态   ║                    ║
║    订单追踪器      ╠═════════════════> ║    MySQL数据库     ║
║   (OrderTracker)   ║                   ║    - t_order       ║
║                    ║                   ║                    ║
╚════════════════════╝                   ╚════════════════════╝
           ▲
           ║ 用户数据流推送               ╔════════════════════╗
           ║                             ║                    ║
           ╚═════════════════════════════╣    币安WebSocket   ║
                                         ║    用户数据流      ║
                                         ╚════════════════════╝
           ┌─────────────────────────────┐
           │                             │
           │  定期主动查询订单状态       │
           │  - 网络异常处理             │
           │  - 确保订单状态一致         │
           │                             │
           └─────────────────────────────┘
```

## Python机器学习策略模块内部流程

```
╔═════════════════════╗        ╔═════════════════════╗
║                     ║        ║                     ║
║    Kafka消费者      ╠═══════>║    数据预处理       ║
║    (消费市场数据)   ║        ║    (Data Processor) ║
║                     ║        ║   - 真实数据处理   ║
╚═════════════════════╝        ╚══════════╦══════════╝
                                          ║
                                          ║ 处理后的数据
                                          ▼
╔═════════════════════╗        ╔══════════════════════╗
║                     ║        ║                      ║
║    特征工程         ║════════║    技术指标计算      ║
║    (Feature Engine) ║        ║    (Indicators)      ║
║    - 高质量特征     ║        ║    - LPPL指标特征    ║
║    - 时序特征处理   ║        ║    - hematread指标特征║
║    - 实时特征更新   ║        ║    - bull market支持特征║
╚══════════╦══════════╝        ╚══════════════════════╝
           ║
           ║ 特征数据
           ▼
╔═════════════════════════════════════════════╗
║                                             ║
║             统一机器学习策略                ║
║          (Unified ML Strategy)              ║
║    - 模型融合 (LPPL + hematread + bull market)║
║    - 集成学习与深度学习模型                 ║
║    - 内置风险评估器                         ║
║      * 动态仓位控制                         ║
║      * 自适应止损策略                       ║
║      * 多维风险评分                         ║
╚══════════════════════╦══════════════════════╝
                       ║                    ▲
                       ║ 策略输出           ║
                       ▼                    ║
╔═════════════════════╗        ╔═══════════╩═══════════╗
║                     ║        ║                       ║
║    信号生成器       ║        ║    模型训练与优化     ║
║    (Signal Generator)        ║    (Model Training)   ║
║    - 信号可靠性评估 ║        ║    - 离线批量训练     ║
║    - 信号优先级排序 ║        ║    - 在线增量学习     ║
╚══════════╦══════════╝        ║    - DeepSeek知识蒸馏  ║
           ║                   ║    - 真实数据训练     ║
           ║                   ╚═══════════╦═══════════╝
           ▼                               ║
╔═════════════════════╗                   ║
║                     ║                   ▼
║    Kafka生产者      ║        ╔═══════════════════════╗
║    (发布策略信号)   ║        ║    模型部署与监控     ║
║                     ║        ║    (Model Deployment) ║
╚═════════════════════╝        ║    - A/B测试          ║
                               ║    - 性能监控         ║
                               ║    - 模型版本管理     ║
                               ╚═══════════════════════╝
```

## 全系统数据流转概览

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│  市场数据模块   ├───>│  Python机器学习 ├───>│    交易模块     │
│  (market-data)  │    │  策略模块       │    │    (trade)      │
│                 │    │  (crypto-ml-strategy)│                 │
└────────┬────────┘    └────────┬────────┘    └────────┬────────┘
         │                      │                      │
         │                      │                      │
         ▼                      ▼                      ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│                 │    │                 │    │                 │
│    InfluxDB     │<───┤     Kafka       ├───>│     MySQL       │
│  (时序数据)     │    │  (消息队列)     │    │  (关系数据)     │
│  (真实行情数据) │    │                 │    │  (模型和状态)   │
└───────┬─────────┘    └─────────────────┘    └────────┬────────┘
        │                                              │
        │                                              │
        ▼                                              ▼
┌─────────────────┐                          ┌─────────────────┐
│                 │                          │                 │
│  模型训练系统   │                          │    账户模块     │
│ (Model Training)│                          │   (account)     │
│  - 真实数据训练 │                          │                 │
│  - 在线增量学习 │                          │                 │
│  - DeepSeek蒸馏 │                          │                 │
└───────┬─────────┘                          └────────┬────────┘
        │                                             │
        │                                             │
        ▼                                             ▼
┌─────────────────┐                          ┌─────────────────┐
│                 │                          │                 │
│   模型评估系统  │                          │  风险模块(已整合)│
│ (Model Evaluation)                         │  (已整合至机器  │
│  - 性能指标     │                          │   学习策略中)   │
│  - A/B测试      │                          │                 │
└─────────────────┘                          └─────────────────┘
```