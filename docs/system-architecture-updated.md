# 加密货币交易系统架构设计 - 统一ML策略模块版

## 1. 系统概述

### 1.1 架构演进
本文档描述了集成统一机器学习策略模块后的加密货币交易系统架构。新架构将所有传统策略信号转换为ML特征，使用单一的机器学习模型进行信号融合和决策。

### 1.2 核心改进
- **单一ML模型**: 将LPPL、Hematread、BMSB、SuperTrend等策略信号作为特征输入
- **信号融合**: 智能融合多种策略信号，输出统一交易决策
- **知识蒸馏**: DeepSeek API集成，支持模型优化和知识传递
- **简化架构**: 专注于ML服务，减少系统复杂度
- **API统一**: 通过HistoricalDataController.java统一数据访问

## 2. 整体系统架构

### 2.1 系统分层架构
```
┌─────────────────────────────────────────────────────────────────┐
│                        用户接口层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   Web控制台     │  │   API接口       │  │   监控面板      │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                        业务逻辑层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   策略模块      │  │   交易模块      │  │   风险模块      │  │
│  │  (Python)       │  │   (Java)        │  │   (Java)        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                        数据服务层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   市场数据      │  │   历史数据API   │  │   账户数据      │  │
│  │   (Java)        │  │   (Java)        │  │   (Java)        │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                        消息中间件                                │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    Kafka消息队列                            │ │
│  │  market.data | strategy.signals | order.events | risk.alerts │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                        数据存储层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │     MySQL       │  │    InfluxDB     │  │     Redis       │  │
│  │   (业务数据)     │  │   (时序数据)     │  │    (缓存)       │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
├─────────────────────────────────────────────────────────────────┤
│                        外部服务层                                │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │   币安API       │  │   DeepSeek API  │  │   其他数据源    │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 统一ML策略模块架构
```
┌─────────────────────────────────────────────────────────────────┐
│                    统一ML策略模块架构                            │
├─────────────────────────────────────────────────────────────────┤
│                      特征工程层                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                 策略信号特征化                               │ │
│  │                                                            │ │
│  │  LPPL特征 → Hematread特征 → BMSB特征 → SuperTrend特征       │ │
│  │      ↓            ↓            ↓            ↓              │ │
│  │  泡沫概率     动量强度      支撑强度      趋势方向            │ │
│  │  临界时间     趋势方向      价格位置      趋势强度            │ │
│  │  振荡频率     成交量动量    突破概率      波动率              │ │
│  │                                                            │ │
│  │  海龟特征 → 形态特征 → 市场结构特征 → 统一特征向量           │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                    统一ML模型层                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                 信号融合神经网络                             │ │
│  │                                                            │ │
│  │  特征输入 → 特征编码器 → 注意力机制 → LSTM融合 → 决策输出     │ │
│  │     ↓           ↓           ↓          ↓         ↓        │ │
│  │  标准化      深度编码    动态权重    时序建模   BUY/SELL/HOLD │ │
│  │                                                            │ │
│  │  DeepSeek知识蒸馏 ← 教师模型API ← 在线学习更新               │ │
│  └─────────────────────────────────────────────────────────────┘ │
├─────────────────────────────────────────────────────────────────┤
│                      数据与通信层                               │
│  ┌─────────────────┐  ┌─────────────────┐  ┌─────────────────┐  │
│  │ Java API客户端  │  │ Kafka信号发布   │  │ DeepSeek集成    │  │
│  │                │  │                │  │                │  │
│  │ • 历史数据获取  │  │ • 异步信号传输  │  │ • 教师模型API   │  │
│  │ • 数据预处理    │  │ • 消息序列化    │  │ • 知识蒸馏     │  │
│  │ • 缓存管理     │  │ • 错误重试     │  │ • 模型优化     │  │
│  └─────────────────┘  └─────────────────┘  └─────────────────┘  │
└─────────────────────────────────────────────────────────────────┘
```

## 3. 核心模块设计

### 3.1 策略模块 (Python)

#### 3.1.1 统一策略接口
```python
class StrategyBase:
    """策略基类"""
    def __init__(self, config):
        self.config = config
        self.risk_manager = RiskManager(config)
    
    def generate_signal(self, data):
        """生成交易信号"""
        pass
    
    def assess_risk(self, signal, market_data):
        """风险评估"""
        return self.risk_manager.assess(signal, market_data)
```

#### 3.1.2 技术指标策略
- **LPPL泡沫检测**: 基于Log-Periodic Power Law模型
- **Hematread策略**: 动量和趋势分析
- **BMSB策略**: 牛市支撑带分析
- **SuperTrend策略**: ATR基础趋势跟踪

#### 3.1.3 机器学习集成
```python
class UnifiedMLStrategy:
    """统一机器学习策略"""
    def __init__(self):
        self.feature_extractor = FeatureExtractor()
        self.model = EnsembleModel()
        self.deepseek_client = DeepSeekClient()
    
    def train_with_distillation(self, data):
        """使用知识蒸馏训练"""
        teacher_predictions = self.deepseek_client.predict(data)
        self.model.distill_train(data, teacher_predictions)
```

### 3.2 数据服务层 (Java)

#### 3.2.1 历史数据控制器
```java
@RestController
@RequestMapping("/api/v1/historical-data")
public class HistoricalDataController {
    
    @GetMapping("/kline")
    public ResponseEntity<List<KlineData>> getKlineData(
        @RequestParam String symbol,
        @RequestParam String interval,
        @RequestParam Long startTime,
        @RequestParam Long endTime) {
        // 返回K线数据
    }
    
    @GetMapping("/depth")
    public ResponseEntity<List<DepthData>> getDepthData(
        @RequestParam String symbol,
        @RequestParam Long startTime,
        @RequestParam Long endTime) {
        // 返回深度数据
    }
    
    @GetMapping("/trades")
    public ResponseEntity<List<TradeData>> getTradeData(
        @RequestParam String symbol,
        @RequestParam Long startTime,
        @RequestParam Long endTime) {
        // 返回交易数据
    }
}
```

#### 3.2.2 数据统一访问接口
- **标准化数据格式**: 统一的JSON响应格式
- **多数据源支持**: K线、深度、交易数据
- **缓存机制**: Redis缓存热点数据
- **限流控制**: API访问频率限制

### 3.3 消息队列设计

#### 3.3.1 Kafka主题规划
```yaml
topics:
  # 市场数据主题
  market.data.kline: 
    partitions: 12
    replication: 3
    retention: 7d
  
  market.data.depth:
    partitions: 12
    replication: 3
    retention: 1d
  
  market.data.trades:
    partitions: 12
    replication: 3
    retention: 3d
  
  # 策略信号主题
  strategy.signals:
    partitions: 6
    replication: 3
    retention: 30d
  
  # 风险告警主题
  risk.alerts:
    partitions: 3
    replication: 3
    retention: 90d
```

#### 3.3.2 消息格式标准
```json
{
  "messageId": "uuid",
  "messageType": "STRATEGY_SIGNAL",
  "timestamp": 1609459200000,
  "source": "unified-ml-strategy",
  "data": {
    "symbol": "BTCUSDT",
    "signal": "BUY",
    "confidence": 0.85,
    "riskScore": 0.35,
    "features": {
      "lppl_probability": 0.25,
      "hematread_momentum": 0.30,
      "bmsb_support": 0.15,
      "supertrend_signal": 0.30
    }
  }
}
```

## 4. 数据流转设计

### 4.1 实时数据流
```
币安WebSocket → Java市场数据模块 → Kafka → Python策略模块 → 信号生成 → Kafka → Java交易模块
```

### 4.2 历史数据流
```
Python策略模块 → HistoricalDataController API → Java数据服务 → InfluxDB/MySQL → 数据返回 → 特征工程 → 模型训练
```

### 4.3 AI增强流程
```
历史数据 → DeepSeek API → 教师模型预测 → 知识蒸馏 → 学生模型训练 → 在线学习更新
```

## 5. 部署架构

### 5.1 容器化部署
```yaml
services:
  # Java服务
  crypto-bootstrap:
    image: crypto-trading:java-latest
    ports: ["8080:8080"]
    environment:
      - SPRING_PROFILES_ACTIVE=prod
  
  # Python策略服务
  strategy-module:
    image: crypto-trading:python-latest
    environment:
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - KAFKA_BROKERS=${KAFKA_BROKERS}
  
  # 基础设施
  kafka:
    image: confluentinc/cp-kafka:latest
  
  mysql:
    image: mysql:8.0
  
  influxdb:
    image: influxdb:2.0
  
  redis:
    image: redis:7-alpine
```

### 5.2 高可用性设计
- **负载均衡**: Nginx反向代理
- **服务发现**: Consul服务注册
- **健康检查**: 定期健康状态检查
- **故障转移**: 自动故障转移机制

## 6. 监控与告警

### 6.1 性能监控
- **延迟监控**: 端到端延迟跟踪
- **吞吐量监控**: 消息处理速率
- **资源监控**: CPU、内存、网络使用率
- **错误率监控**: 异常和错误统计

### 6.2 业务监控
- **策略性能**: F1分数、夏普比率监控
- **风险指标**: 实时风险评估监控
- **交易执行**: 订单执行成功率
- **数据质量**: 数据完整性和准确性

### 6.3 告警机制
```yaml
alerts:
  - name: "策略F1分数异常"
    condition: "f1_score > 0.8 or f1_score < 0.4"
    severity: "HIGH"
    
  - name: "API延迟过高"
    condition: "api_latency > 100ms"
    severity: "MEDIUM"
    
  - name: "Kafka消息积压"
    condition: "kafka_lag > 1000"
    severity: "HIGH"
```

## 7. 安全设计

### 7.1 API安全
- **认证授权**: JWT token认证
- **API限流**: 基于用户的访问限制
- **数据加密**: HTTPS传输加密
- **输入验证**: 严格的参数验证

### 7.2 数据安全
- **敏感数据加密**: API密钥等敏感信息加密存储
- **访问控制**: 基于角色的访问控制
- **审计日志**: 完整的操作审计记录
- **数据备份**: 定期数据备份和恢复测试

## 8. 扩展性设计

### 8.1 水平扩展
- **微服务架构**: 独立服务可单独扩展
- **消息队列**: Kafka支持分区扩展
- **数据库分片**: 支持数据库水平分片
- **缓存集群**: Redis集群支持

### 8.2 功能扩展
- **策略插件**: 支持新策略动态加载
- **多交易所**: 易于扩展到其他交易所
- **多资产**: 支持不同类型交易资产
- **多语言**: 支持其他编程语言集成
