# Crypto Common Module

## 项目概述

crypto-common 是加密货币量化交易系统的公共模块，提供了系统中所有模块共享的基础功能和组件。该模块采用 Java 21 + Spring Boot 3.x 技术栈，支持虚拟线程和现代化的开发模式。

## 技术栈

- **Java**: 21 (LTS)
- **Spring Boot**: 3.2.x
- **Maven**: 3.9.x
- **Jackson**: JSON序列化/反序列化
- **Lombok**: 减少样板代码
- **JUnit 5**: 单元测试框架
- **Logback**: 日志框架

## 模块结构

```
crypto-common/
├── src/main/java/com/trading/common/
│   ├── config/          # 配置类
│   ├── constant/        # 常量定义
│   ├── dto/            # 数据传输对象
│   ├── enums/          # 枚举类
│   ├── exception/      # 异常处理
│   └── utils/          # 工具类
├── src/main/resources/
│   ├── application-common.yml    # 公共配置
│   ├── logback-common.xml       # 日志配置
│   ├── messages.properties      # 中文消息
│   └── messages_en_US.properties # 英文消息
├── src/test/           # 单元测试
└── pom.xml            # Maven配置
```

## 核心功能

### 1. 异常处理体系

提供统一的异常处理机制：

- `BaseException`: 基础异常类
- `BusinessException`: 业务异常
- `SystemException`: 系统异常
- `TradeException`: 交易异常
- `ApiException`: API异常

### 2. 配置管理

支持多环境配置和配置验证：

- `AppConfig`: 应用配置
- `BinanceConfig`: Binance API配置
- `DatabaseConfig`: 数据库配置
- `KafkaConfig`: Kafka配置

### 3. 统一响应封装

标准化的API响应格式：

- `ResponseResult<T>`: 统一响应结果
- `PageResult<T>`: 分页响应结果

### 4. 服务治理与监控

提供强大的服务治理和监控能力，确保系统稳定性和弹性：

- **熔断器 (Circuit Breaker)**: 基于 Resilience4j，防止故障级联。
- **限流器 (Rate Limiter)**: 基于 Resilience4j，控制API和资源访问速率。
- **指标框架 (Metrics)**: 集成 Micrometer，暴露应用性能指标至 Prometheus。
- **对象池 (Object Pool)**: 基于 Apache Commons Pool2，管理和复用高成本对象，提升性能。

### 5. 工具类库

常用的工具类：

- `DateTimeUtils`: 日期时间处理
- `NumberUtils`: 数字计算和精度处理
- `JsonUtils`: JSON序列化/反序列化
- `HttpUtils`: HTTP客户端工具
- `ValidationUtils`: 数据验证
- `EncryptUtils`: 加密解密

### 6. 交易相关枚举

定义交易系统中的状态和类型：

- `OrderStatus`: 订单状态
- `OrderSide`: 订单方向
- `OrderType`: 订单类型
- `PositionSide`: 持仓方向
- `TimeInForce`: 订单有效期

### 7. 市场数据DTO

支持各种市场数据的传输：

- `KlineData`: K线数据
- `DepthData`: 深度数据
- `TradeData`: 交易数据
- `TickerData`: 24小时统计数据

## 使用方式

### 1. 添加依赖

在其他模块的 `pom.xml` 中添加依赖：

```xml
<dependency>
    <groupId>com.trading</groupId>
    <artifactId>crypto-common</artifactId>
    <version>1.0.0</version>
</dependency>
```

### 2. 导入配置

在 Spring Boot 应用中导入公共配置：

```yaml
spring:
  config:
    import:
      - classpath:application-common.yml
```

### 3. 使用示例

#### 异常处理
```java
// 抛出业务异常
throw new BusinessException(ErrorCodes.INVALID_PARAMETER, "参数无效");

// 抛出交易异常
throw new TradeException(ErrorCodes.INSUFFICIENT_BALANCE, "余额不足");
```

#### 响应封装
```java
// 成功响应
return ResponseResult.success(data);

// 失败响应
return ResponseResult.fail(ErrorCodes.BUSINESS_ERROR, "操作失败");
```

#### 工具类使用
```java
// 日期时间处理
LocalDateTime utcTime = DateTimeUtils.getCurrentUtcTime();
String formatted = DateTimeUtils.formatDateTime(utcTime);

// 数字处理
BigDecimal price = NumberUtils.formatPrice(new BigDecimal("123.456789"));
BigDecimal quantity = NumberUtils.formatQuantity(new BigDecimal("10.123456"));

// JSON处理
String json = JsonUtils.toJsonString(object);
MyObject obj = JsonUtils.fromJsonString(json, MyObject.class);
```

## 配置说明

### 应用配置

主要配置项包括：

- **性能配置**: 虚拟线程、超时设置、精度配置
- **监控配置**: 指标收集、健康检查
- **安全配置**: 加密算法、签名算法、限流配置

### Binance API配置

支持现货和期货交易：

- **现货交易**: API密钥、基础URL
- **期货交易**: API密钥、基础URL
- **WebSocket**: 连接配置、重连机制

### 数据库配置

支持多种数据库：

- **MySQL**: 主要业务数据存储
- **InfluxDB**: 时序数据存储
- **Redis**: 缓存和会话存储

## 测试

### 运行单元测试

```bash
mvn test
```

### 运行测试套件

```bash
mvn test -Dtest=CryptoCommonTestSuite
```

### 测试覆盖率

项目包含完整的单元测试，覆盖所有核心功能：

- 工具类测试
- 异常处理测试
- DTO测试
- 配置测试

## 日志配置

### 日志级别

- **开发环境**: DEBUG级别，输出详细信息
- **测试环境**: INFO级别，输出关键信息
- **生产环境**: WARN级别，只输出警告和错误

### 日志分类

- **应用日志**: 业务逻辑相关
- **错误日志**: 错误和异常信息
- **交易日志**: 交易相关操作
- **市场数据日志**: 市场数据处理
- **性能日志**: 性能监控信息

## 国际化支持

支持中英文双语：

- `messages.properties`: 中文消息
- `messages_en_US.properties`: 英文消息

## 版本历史

### v1.0.0 (当前版本)

- 初始版本发布
- 完整的基础功能实现
- 全面的单元测试覆盖
- 完善的文档和配置

## 贡献指南

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证。

## 联系方式

如有问题或建议，请联系开发团队。
