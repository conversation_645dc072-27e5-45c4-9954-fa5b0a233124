syntax = "proto3";

package com.trading.common.data;

option java_multiple_files = true;
option java_package = "com.trading.common.data";
option java_outer_classname = "DataServiceProto";

// Unified data service definition
service DataService {
  // Get OHLCV data
  rpc GetOHLCV(OHLCVRequest) returns (OHLCVResponse) {}
  
  // Get order book snapshot
  rpc GetOrderBook(OrderBookRequest) returns (OrderBookResponse) {}
  
  // Get real-time market data stream
  rpc MarketDataStream(DataStreamRequest) returns (stream MarketData) {}
}

// Request for OHLCV data
message OHLCVRequest {
  string symbol = 1;
  string timeframe = 2;  // e.g., "1m", "5m", "1h"
  int64 start_time = 3;  // Unix timestamp in milliseconds
  int64 end_time = 4;    // Unix timestamp in milliseconds
  int32 limit = 5;       // Max number of records
}

// OHLCV data point
message OHLCV {
  int64 timestamp = 1;  // Unix timestamp in milliseconds
  double open = 2;
  double high = 3;
  double low = 4;
  double close = 5;
  double volume = 6;
}

// Response with OHLCV data
message OHLCVResponse {
  repeated OHLCV data = 1;
}

// Request for order book data
message OrderBookRequest {
  string symbol = 1;
  int32 depth = 2;  // Number of price levels
}

// Order book price level
message PriceLevel {
  double price = 1;
  double quantity = 2;
}

// Order book snapshot
message OrderBookResponse {
  repeated PriceLevel bids = 1;  // Descending price order
  repeated PriceLevel asks = 2;  // Ascending price order
  int64 timestamp = 3;           // Unix timestamp in milliseconds
}

// Request for market data stream
message DataStreamRequest {
  repeated string symbols = 1;   // List of symbols to subscribe
  repeated string data_types = 2; // e.g., "trade", "orderbook", "kline"
}

// Unified market data message
message MarketData {
  string symbol = 1;
  string data_type = 2;  // "trade", "orderbook", "kline"
  int64 timestamp = 3;   // Unix timestamp in milliseconds
  
  oneof data {
    Trade trade = 4;
    OrderBookSnapshot orderbook = 5;
    OHLCV kline = 6;
  }
}

// Trade data
message Trade {
  double price = 1;
  double quantity = 2;
  bool is_buyer_maker = 3; // True: seller is maker, False: buyer is maker
}

// Order book snapshot
message OrderBookSnapshot {
  repeated PriceLevel bids = 1;
  repeated PriceLevel asks = 2;
}