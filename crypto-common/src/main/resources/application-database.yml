---
# 统一数据库配置
spring:
  datasource:
    url: jdbc:mysql://${MYSQL_HOST:localhost}:${MYSQL_PORT:13306}/${MYSQL_DATABASE:crypto_trading}?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
    username: ${MYSQL_USERNAME:root}
    password: ${MYSQL_PASSWORD:root}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: ${DB_MAX_POOL:50}
      minimum-idle: ${DB_MIN_IDLE:10}
      connection-timeout: ${DB_CONN_TIMEOUT:30000}
      idle-timeout: ${DB_IDLE_TIMEOUT:600000}
      max-lifetime: ${DB_MAX_LIFE:1800000}
      leak-detection-threshold: ${DB_LEAK_THRESHOLD:60000}

# MyBatis-Plus配置
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: true
    default-statement-timeout: 30
  global-config:
    db-config:
      id-type: auto
      logic-delete-field: deleted