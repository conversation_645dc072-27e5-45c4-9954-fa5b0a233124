# 错误消息配置文件 - 中文
# 系统错误消息 1000-1999
error.1000=系统未知错误
error.1001=系统超时
error.1002=网络连接错误
error.1003=系统维护中
error.1004=系统资源不足
error.1005=系统配置错误
error.1006=系统初始化失败
error.1007=系统服务不可用
error.1008=系统内部错误
error.1009=系统版本不兼容

# 参数验证错误 2000-2999
error.2000=参数无效
error.2001=参数不能为空
error.2002=参数格式错误
error.2003=参数长度超限
error.2004=参数值超出范围
error.2005=参数类型错误
error.2006=必填参数缺失
error.2007=参数组合无效
error.2008=参数重复
error.2009=参数依赖关系错误

# 业务逻辑错误 3000-3999
error.3000=业务操作失败
error.3001=数据不存在
error.3002=数据已存在
error.3003=数据状态错误
error.3004=业务规则违反
error.3005=操作权限不足
error.3006=操作频率过高
error.3007=业务流程错误
error.3008=数据完整性错误
error.3009=业务配置错误

# 交易相关错误 4000-4999
error.4000=交易失败
error.4001=余额不足
error.4002=交易对无效
error.4003=订单数量无效
error.4004=订单价格无效
error.4005=订单类型不支持
error.4006=交易暂停
error.4007=订单不存在
error.4008=订单状态错误
error.4009=交易限制
error.4010=持仓不足
error.4011=风险控制拒绝
error.4012=交易时间限制
error.4013=最小交易量限制
error.4014=最大交易量限制
error.4015=价格偏离过大

# API相关错误 5000-5999
error.5000=API调用失败
error.5001=API密钥无效
error.5002=API签名错误
error.5003=API权限不足
error.5004=API调用频率超限
error.5005=API服务不可用
error.5006=API版本不支持
error.5007=API参数错误
error.5008=API响应超时
error.5009=API响应格式错误

# 数据库相关错误 6000-6999
error.6000=数据库操作失败
error.6001=数据库连接失败
error.6002=数据库查询超时
error.6003=数据库事务失败
error.6004=数据库约束违反
error.6005=数据库死锁
error.6006=数据库连接池耗尽
error.6007=数据库版本不兼容
error.6008=数据库权限不足
error.6009=数据库存储空间不足

# 缓存相关错误 7000-7999
error.7000=缓存操作失败
error.7001=缓存连接失败
error.7002=缓存键不存在
error.7003=缓存过期
error.7004=缓存序列化失败
error.7005=缓存反序列化失败
error.7006=缓存空间不足
error.7007=缓存配置错误
error.7008=缓存集群故障
error.7009=缓存同步失败

# 消息队列相关错误 8000-8999
error.8000=消息队列操作失败
error.8001=消息发送失败
error.8002=消息消费失败
error.8003=消息序列化失败
error.8004=消息反序列化失败
error.8005=消息队列连接失败
error.8006=消息队列配置错误
error.8007=消息重复消费
error.8008=消息丢失
error.8009=消息队列满

# 成功消息
success.1000=操作成功
success.1001=创建成功
success.1002=更新成功
success.1003=删除成功
success.1004=查询成功
success.1005=保存成功
success.1006=提交成功
success.1007=取消成功
success.1008=确认成功
success.1009=处理成功

# 交易成功消息
success.trade.1000=订单创建成功
success.trade.1001=订单取消成功
success.trade.1002=订单修改成功
success.trade.1003=订单成交成功
success.trade.1004=持仓开仓成功
success.trade.1005=持仓平仓成功
success.trade.1006=资金转账成功
success.trade.1007=风控检查通过
success.trade.1008=策略执行成功
success.trade.1009=数据同步成功

# 验证消息
validation.required=字段{0}不能为空
validation.min=字段{0}的值不能小于{1}
validation.max=字段{0}的值不能大于{1}
validation.size=字段{0}的长度必须在{1}到{2}之间
validation.pattern=字段{0}的格式不正确
validation.email=邮箱格式不正确
validation.phone=手机号格式不正确
validation.positive=字段{0}必须为正数
validation.decimal=字段{0}必须为有效的小数
validation.future=字段{0}必须为未来时间
validation.past=字段{0}必须为过去时间

# 业务消息
business.symbol.invalid=交易对{0}无效
business.symbol.not.supported=交易对{0}暂不支持
business.symbol.suspended=交易对{0}已暂停交易
business.order.min.quantity=订单数量不能小于{0}
business.order.max.quantity=订单数量不能大于{0}
business.order.min.price=订单价格不能小于{0}
business.order.max.price=订单价格不能大于{0}
business.order.price.precision=价格精度不能超过{0}位小数
business.order.quantity.precision=数量精度不能超过{0}位小数
business.balance.insufficient=余额不足，当前余额{0}，需要{1}
business.position.insufficient=持仓不足，当前持仓{0}，需要{1}

# 时间相关消息
time.market.closed=市场已关闭
time.market.opening=市场即将开盘
time.market.closing=市场即将收盘
time.trading.hours=交易时间：{0} - {1}
time.maintenance=系统维护时间：{0} - {1}

# 状态消息
status.order.new=新建订单
status.order.pending=待成交
status.order.partially.filled=部分成交
status.order.filled=完全成交
status.order.canceled=已取消
status.order.rejected=已拒绝
status.order.expired=已过期

status.trading.normal=正常交易
status.trading.halt=暂停交易
status.trading.maintenance=维护中
status.trading.pre.open=预开市
status.trading.post.close=收盘后

# 操作消息
operation.create=创建
operation.update=更新
operation.delete=删除
operation.query=查询
operation.submit=提交
operation.cancel=取消
operation.confirm=确认
operation.reject=拒绝
operation.approve=批准
operation.process=处理

# 单位消息
unit.price=价格
unit.quantity=数量
unit.amount=金额
unit.volume=成交量
unit.percent=百分比
unit.time=时间
unit.count=数量
unit.rate=费率
unit.ratio=比例
unit.level=等级
