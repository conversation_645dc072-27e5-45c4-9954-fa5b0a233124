<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 定义变量 -->
    <property name="LOG_HOME" value="${LOG_HOME:-logs}" />
    <property name="APP_NAME" value="${spring.application.name:-crypto-common}" />
    <property name="LOG_LEVEL" value="${LOG_LEVEL:-INFO}" />
    
    <!-- 控制台输出配置 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %highlight(%-5level) [%cyan(%logger{36})] [%magenta(%X{traceId})] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <!-- 只输出INFO及以上级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>
    
    <!-- 应用日志文件配置 -->
    <appender name="APP_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_NAME}.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] [%X{traceId}] [%X{userId}] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 错误日志文件配置 -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_NAME}-error.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] [%X{traceId}] [%X{userId}] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}-error.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>50MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>500MB</totalSizeCap>
        </rollingPolicy>
        <!-- 只记录ERROR级别的日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    
    <!-- 交易日志文件配置 -->
    <appender name="TRADING_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_NAME}-trading.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] [%X{traceId}] [%X{orderId}] [%X{symbol}] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}-trading.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>200MB</maxFileSize>
            <maxHistory>90</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 市场数据日志文件配置 -->
    <appender name="MARKET_DATA_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_NAME}-market-data.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] [%X{symbol}] [%X{dataType}] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}-market-data.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>500MB</maxFileSize>
            <maxHistory>7</maxHistory>
            <totalSizeCap>2GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 性能监控日志文件配置 -->
    <appender name="PERFORMANCE_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_HOME}/${APP_NAME}-performance.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] [%X{method}] [%X{duration}] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/${APP_NAME}-performance.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 异步日志配置 -->
    <appender name="ASYNC_APP_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <includeCallerData>true</includeCallerData>
        <maxFlushTime>1000</maxFlushTime>
        <shutdownTimeout>2000</shutdownTimeout>
        <appender-ref ref="APP_FILE"/>
    </appender>

    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>256</queueSize>
        <includeCallerData>true</includeCallerData>
        <maxFlushTime>1000</maxFlushTime>
        <shutdownTimeout>2000</shutdownTimeout>
        <appender-ref ref="ERROR_FILE"/>
    </appender>

    <appender name="ASYNC_TRADING_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>2048</queueSize>
        <includeCallerData>true</includeCallerData>
        <maxFlushTime>1000</maxFlushTime>
        <shutdownTimeout>2000</shutdownTimeout>
        <appender-ref ref="TRADING_FILE"/>
    </appender>

    <appender name="ASYNC_MARKET_DATA_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>20</discardingThreshold>
        <queueSize>4096</queueSize>
        <includeCallerData>false</includeCallerData>
        <maxFlushTime>500</maxFlushTime>
        <shutdownTimeout>1000</shutdownTimeout>
        <appender-ref ref="MARKET_DATA_FILE"/>
    </appender>
    
    <!-- 特定包的日志级别配置 -->
    
    <!-- 交易相关日志 -->
    <logger name="com.trading.common.trading" level="DEBUG" additivity="false">
        <appender-ref ref="ASYNC_TRADING_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- 市场数据相关日志 -->
    <logger name="com.trading.common.market" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_MARKET_DATA_FILE"/>
    </logger>
    
    <!-- 性能监控日志 -->
    <logger name="com.trading.common.performance" level="DEBUG" additivity="false">
        <appender-ref ref="PERFORMANCE_FILE"/>
    </logger>
    
    <!-- Binance API日志 -->
    <logger name="com.binance" level="DEBUG" additivity="false">
        <appender-ref ref="ASYNC_APP_FILE"/>
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- Spring框架日志 -->
    <logger name="org.springframework" level="WARN" additivity="false">
        <appender-ref ref="ASYNC_APP_FILE"/>
    </logger>

    <!-- 连接池日志 -->
    <logger name="com.zaxxer.hikari" level="WARN" additivity="false">
        <appender-ref ref="ASYNC_APP_FILE"/>
    </logger>
    
    <!-- Redis日志 -->
    <logger name="org.springframework.data.redis" level="WARN" additivity="false">
        <appender-ref ref="ASYNC_APP_FILE"/>
    </logger>
    
    <!-- Kafka日志 -->
    <logger name="org.apache.kafka" level="WARN" additivity="false">
        <appender-ref ref="ASYNC_APP_FILE"/>
    </logger>
    
    <!-- HTTP客户端日志 -->
    <logger name="okhttp3" level="INFO" additivity="false">
        <appender-ref ref="ASYNC_APP_FILE"/>
    </logger>
    
    <!-- 根日志配置 -->
    <root level="${LOG_LEVEL}">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="ASYNC_APP_FILE"/>
        <appender-ref ref="ASYNC_ERROR_FILE"/>
    </root>
    
    <!-- 开发环境配置 -->
    <springProfile name="dev">
        <logger name="com.trading" level="DEBUG"/>
        <logger name="org.springframework.web" level="DEBUG"/>
        <logger name="org.springframework.security" level="DEBUG"/>
    </springProfile>
    
    <!-- 测试环境配置 -->
    <springProfile name="test">
        <logger name="com.trading" level="INFO"/>
        <logger name="org.springframework" level="WARN"/>
    </springProfile>
    
    <!-- 生产环境配置 -->
    <springProfile name="prod">
        <logger name="com.trading" level="INFO"/>
        <logger name="org.springframework" level="WARN"/>
        <logger name="com.zaxxer.hikari" level="ERROR"/>
        
        <!-- 生产环境关闭控制台输出 -->
        <root level="INFO">
            <appender-ref ref="ASYNC_APP_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
        </root>
    </springProfile>
    
</configuration>
