# 统一缓存配置文件
# 整合所有缓存系统配置

cache:
  # 统一缓存设置
  unified:
    enabled: true
    ttl-seconds: 1800
    max-size: 100000
    
  # 多级缓存层级配置
  levels:
    L1: # 内存缓存
      enabled: true
      type: "Caffeine"
      max-size: 32000
      ttl-seconds: 600
      
    L2: # Redis缓存
      enabled: true
      type: "Redis"
      ttl-seconds: 1800
      batch-size: 100
      
    L3: # MySQL缓存
      enabled: true
      type: "MySQL"
      ttl-seconds: 7200
      persist-data: true

  # 缓存预热配置
  warmup:
    enabled: true
    batch-size: 50
    thread-count: 4
    timeout-seconds: 300
    symbols: ["BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "XRPUSDT"]
    intervals: ["1m", "5m", "15m", "1h", "4h", "1d"]
    levels: [5, 10, 20]

  # 缓存优化设置
  optimization:
    eviction-policy: "LFU"
    compression-enabled: true
    async-write: true
    batch-write: true
    
  # Redis配置
  redis:
    host: "localhost"
    port: 116379
    database: 0
    password: ""
    max-connections: 50
    timeout-ms: 5000
    
  # MySQL配置
  mysql:
    host: "localhost"
    port: 3306
    database: "crypto_trading"
    username: "crypto_user"
    password: "crypto_password"
    max-connections: 20
    pool-size: 10

  # 性能监控
  monitoring:
    enabled: true
    log-stats-interval: 300
    track-hit-rates: true
    log-slow-queries: true
    slow-query-threshold-ms: 100

# 缓存键前缀配置
cache.prefixes:
  market_data: "market:"
  kline_data: "kline:"
  depth_data: "depth:"
  price_data: "price:"
  stats_data: "stats:"
  global_rate_limit: "rate_limit:"

# 缓存键空间管理
keyspaces:
  trading: 0
  market_data: 1
  statistics: 2
  metadata: 3