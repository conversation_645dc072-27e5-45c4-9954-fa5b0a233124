---
# 统一消息队列配置
kafka:
  bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:229092}
  
  producer:
    key-serializer: org.apache.kafka.common.serialization.StringSerializer
    value-serializer: org.apache.kafka.common.serialization.StringSerializer
    batch-size: ${KAFKA_PRODUCER_BATCH_SIZE:16384}
    linger-ms: ${KAFKA_PRODUCER_LINGER:5}
    buffer-memory: ${KAFKA_PRODUCER_BUFFER:33554432}
    acknowledgments: all
    compression-type: ${KAFKA_COMPRESSION:snappy}
    retries: ${KAFKA_RETRIES:3}
    
  consumer:
    key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    group-id: ${KAFKA_CONSUMER_GROUP:crypto-trading-group}
    auto-offset-reset: latest
    enable-auto-commit: false
    max-poll-records: ${KAFKA_MAX_POLL:500}
    
  topics:
    market-data: ${KAFKA_TOPIC_MARKET_DATA:market-data}
    trade-signals: ${KAFKA_TOPIC_TRADE_SIGNALS:trade-signals}
    order-events: ${KAFKA_TOPIC_ORDER_EVENTS:order-events}
    risk-alerts: ${KAFKA_TOPIC_RISK_ALERTS:risk-alerts}