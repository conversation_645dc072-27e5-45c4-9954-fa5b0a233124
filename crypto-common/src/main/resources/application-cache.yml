---
# 统一缓存配置
spring:
  data:
    redis:
      host: ${REDIS_HOST:localhost}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:root}
      database: ${REDIS_DATABASE:0}
      timeout: ${REDIS_TIMEOUT:5000}
      lettuce:
        pool:
          max-active: ${REDIS_MAX_POOL:50}
          max-idle: ${REDIS_MAX_IDLE:20}
          min-idle: ${REDIS_MIN_IDLE:5}
          max-wait: ${REDIS_MAX_WAIT:5000}

# 多级缓存配置
multi-level-cache:
  enabled: true
  levels:
    l1:
      enabled: true
      type: caffeine
      max-size: 10000
    l2:
      enabled: true
      type: redis
      ttl: ${CACHE_TTL:300}