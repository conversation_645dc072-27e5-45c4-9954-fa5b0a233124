# Error Messages Configuration - English
# System Errors 1000-1999
error.1000=Unknown system error
error.1001=System timeout
error.1002=Network connection error
error.1003=System under maintenance
error.1004=Insufficient system resources
error.1005=System configuration error
error.1006=System initialization failed
error.1007=System service unavailable
error.1008=Internal system error
error.1009=System version incompatible

# Parameter Validation Errors 2000-2999
error.2000=Invalid parameter
error.2001=Parameter cannot be null
error.2002=Parameter format error
error.2003=Parameter length exceeded
error.2004=Parameter value out of range
error.2005=Parameter type error
error.2006=Required parameter missing
error.2007=Invalid parameter combination
error.2008=Duplicate parameter
error.2009=Parameter dependency error

# Business Logic Errors 3000-3999
error.3000=Business operation failed
error.3001=Data not found
error.3002=Data already exists
error.3003=Invalid data status
error.3004=Business rule violation
error.3005=Insufficient operation permission
error.3006=Operation frequency too high
error.3007=Business process error
error.3008=Data integrity error
error.3009=Business configuration error

# Trading Related Errors 4000-4999
error.4000=Trading failed
error.4001=Insufficient balance
error.4002=Invalid trading pair
error.4003=Invalid order quantity
error.4004=Invalid order price
error.4005=Order type not supported
error.4006=Trading suspended
error.4007=Order not found
error.4008=Invalid order status
error.4009=Trading restriction
error.4010=Insufficient position
error.4011=Risk control rejection
error.4012=Trading time restriction
error.4013=Minimum trading volume restriction
error.4014=Maximum trading volume restriction
error.4015=Price deviation too large

# API Related Errors 5000-5999
error.5000=API call failed
error.5001=Invalid API key
error.5002=API signature error
error.5003=Insufficient API permission
error.5004=API call frequency exceeded
error.5005=API service unavailable
error.5006=API version not supported
error.5007=API parameter error
error.5008=API response timeout
error.5009=API response format error

# Database Related Errors 6000-6999
error.6000=Database operation failed
error.6001=Database connection failed
error.6002=Database query timeout
error.6003=Database transaction failed
error.6004=Database constraint violation
error.6005=Database deadlock
error.6006=Database connection pool exhausted
error.6007=Database version incompatible
error.6008=Insufficient database permission
error.6009=Insufficient database storage space

# Cache Related Errors 7000-7999
error.7000=Cache operation failed
error.7001=Cache connection failed
error.7002=Cache key not found
error.7003=Cache expired
error.7004=Cache serialization failed
error.7005=Cache deserialization failed
error.7006=Insufficient cache space
error.7007=Cache configuration error
error.7008=Cache cluster failure
error.7009=Cache synchronization failed

# Message Queue Related Errors 8000-8999
error.8000=Message queue operation failed
error.8001=Message send failed
error.8002=Message consume failed
error.8003=Message serialization failed
error.8004=Message deserialization failed
error.8005=Message queue connection failed
error.8006=Message queue configuration error
error.8007=Duplicate message consumption
error.8008=Message lost
error.8009=Message queue full

# Success Messages
success.1000=Operation successful
success.1001=Created successfully
success.1002=Updated successfully
success.1003=Deleted successfully
success.1004=Queried successfully
success.1005=Saved successfully
success.1006=Submitted successfully
success.1007=Canceled successfully
success.1008=Confirmed successfully
success.1009=Processed successfully

# Trading Success Messages
success.trade.1000=Order created successfully
success.trade.1001=Order canceled successfully
success.trade.1002=Order modified successfully
success.trade.1003=Order filled successfully
success.trade.1004=Position opened successfully
success.trade.1005=Position closed successfully
success.trade.1006=Fund transfer successful
success.trade.1007=Risk control check passed
success.trade.1008=Strategy executed successfully
success.trade.1009=Data synchronized successfully

# Validation Messages
validation.required=Field {0} cannot be empty
validation.min=Field {0} value cannot be less than {1}
validation.max=Field {0} value cannot be greater than {1}
validation.size=Field {0} length must be between {1} and {2}
validation.pattern=Field {0} format is incorrect
validation.email=Email format is incorrect
validation.phone=Phone number format is incorrect
validation.positive=Field {0} must be positive
validation.decimal=Field {0} must be a valid decimal
validation.future=Field {0} must be a future time
validation.past=Field {0} must be a past time

# Business Messages
business.symbol.invalid=Trading pair {0} is invalid
business.symbol.not.supported=Trading pair {0} is not supported
business.symbol.suspended=Trading pair {0} is suspended
business.order.min.quantity=Order quantity cannot be less than {0}
business.order.max.quantity=Order quantity cannot be greater than {0}
business.order.min.price=Order price cannot be less than {0}
business.order.max.price=Order price cannot be greater than {0}
business.order.price.precision=Price precision cannot exceed {0} decimal places
business.order.quantity.precision=Quantity precision cannot exceed {0} decimal places
business.balance.insufficient=Insufficient balance, current balance {0}, required {1}
business.position.insufficient=Insufficient position, current position {0}, required {1}

# Time Related Messages
time.market.closed=Market is closed
time.market.opening=Market is about to open
time.market.closing=Market is about to close
time.trading.hours=Trading hours: {0} - {1}
time.maintenance=System maintenance time: {0} - {1}

# Status Messages
status.order.new=New order
status.order.pending=Pending
status.order.partially.filled=Partially filled
status.order.filled=Filled
status.order.canceled=Canceled
status.order.rejected=Rejected
status.order.expired=Expired

status.trading.normal=Normal trading
status.trading.halt=Trading halted
status.trading.maintenance=Under maintenance
status.trading.pre.open=Pre-market
status.trading.post.close=Post-market

# Operation Messages
operation.create=Create
operation.update=Update
operation.delete=Delete
operation.query=Query
operation.submit=Submit
operation.cancel=Cancel
operation.confirm=Confirm
operation.reject=Reject
operation.approve=Approve
operation.process=Process

# Unit Messages
unit.price=Price
unit.quantity=Quantity
unit.amount=Amount
unit.volume=Volume
unit.percent=Percent
unit.time=Time
unit.count=Count
unit.rate=Rate
unit.ratio=Ratio
unit.level=Level
