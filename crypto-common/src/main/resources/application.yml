---
# 主配置文件 - 统一导入所有模块配置
spring:
  profiles:
    include: common,database,cache,message

# 主应用配置
app:
  name: crypto-trading-system-v2
  version: 2.0.0
  description: 优化后的加密货币量化交易系统（无重复配置）
  
---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  datasource:
    url: ******************************************************************************************************************
    username: root
    password: root123
  data:
    redis:
      host: localhost
      port: 16379
  kafka:
    bootstrap-servers: localhost:29092
  
# 开发环境开关
dev:
  mock-data: true
  debug-mode: true

---
# 测试环境配置
spring:
  config:
    activate:
      on-profile: test
  datasource:
    url: **********************************************************************************************************************
    username: test
    password: test123
  data:
    redis:
      host: localhost
      port: 6380
  kafka:
    bootstrap-servers: localhost:29093