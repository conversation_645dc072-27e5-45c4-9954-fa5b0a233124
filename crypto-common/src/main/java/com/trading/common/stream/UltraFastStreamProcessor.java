package com.trading.common.stream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * 超高性能数据流处理器
 * 使用响应式流和背压控制实现高吞吐量数据处理
 *
 * @param <T> 输入数据类型
 * @param <R> 输出数据类型
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class UltraFastStreamProcessor<T, R> {
    
    private static final Logger log = LoggerFactory.getLogger(UltraFastStreamProcessor.class);
    
    // 默认配置
    private static final int DEFAULT_BUFFER_SIZE = 1024;
    private static final int DEFAULT_PARALLELISM = Runtime.getRuntime().availableProcessors();
    private static final long DEFAULT_BACKPRESSURE_THRESHOLD = 10000;
    
    // 配置参数
    private final int bufferSize;
    private final int parallelism;
    private final long backpressureThreshold;
    
    // 数据流管道
    private final List<Function<Object, Object>> transformers = new ArrayList<>();
    private final List<Predicate<Object>> filters = new ArrayList<>();
    private final List<Consumer<Object>> consumers = new ArrayList<>();
    
    // 环形缓冲区 - 无锁高性能
    private final Object[] ringBuffer;
    private final AtomicLong writeIndex = new AtomicLong(0);
    private final AtomicLong readIndex = new AtomicLong(0);
    
    // 工作线程池
    private final ForkJoinPool processingPool;
    private final ScheduledExecutorService scheduledExecutor;
    
    // 状态控制
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final AtomicBoolean backpressureActive = new AtomicBoolean(false);
    
    // 性能统计
    private final LongAdder itemsProcessed = new LongAdder();
    private final LongAdder itemsDropped = new LongAdder();
    private final LongAdder backpressureEvents = new LongAdder();
    private final LongAdder processingErrors = new LongAdder();
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    private final AtomicLong maxProcessingTime = new AtomicLong(0);
    private final AtomicLong minProcessingTime = new AtomicLong(Long.MAX_VALUE);
    
    /**
     * 创建默认配置的流处理器
     */
    public UltraFastStreamProcessor() {
        this(DEFAULT_BUFFER_SIZE, DEFAULT_PARALLELISM, DEFAULT_BACKPRESSURE_THRESHOLD);
    }
    
    /**
     * 创建自定义配置的流处理器
     */
    public UltraFastStreamProcessor(int bufferSize, int parallelism, long backpressureThreshold) {
        this.bufferSize = bufferSize;
        this.parallelism = parallelism;
        this.backpressureThreshold = backpressureThreshold;
        
        // 初始化环形缓冲区
        this.ringBuffer = new Object[bufferSize];
        
        // 创建工作线程池
        this.processingPool = new ForkJoinPool(parallelism, 
            pool -> {
                ForkJoinWorkerThread worker = ForkJoinPool.defaultForkJoinWorkerThreadFactory.newThread(pool);
                worker.setName("ultra-fast-stream-" + worker.getPoolIndex());
                return worker;
            }, null, true);
        
        this.scheduledExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "stream-monitor");
            t.setDaemon(true);
            return t;
        });
    }
    
    @PostConstruct
    public void start() {
        if (running.compareAndSet(false, true)) {
            // 启动处理循环
            for (int i = 0; i < parallelism; i++) {
                processingPool.submit(this::processingLoop);
            }
            
            // 启动背压监控
            scheduledExecutor.scheduleAtFixedRate(this::monitorBackpressure, 
                100, 100, TimeUnit.MILLISECONDS);
            
            log.info("超高性能流处理器已启动: bufferSize={}, parallelism={}, backpressureThreshold={}", 
                bufferSize, parallelism, backpressureThreshold);
        }
    }
    
    /**
     * 添加数据到流中
     */
    public boolean offer(T item) {
        if (item == null || !running.get()) {
            return false;
        }
        
        // 检查背压
        if (backpressureActive.get()) {
            itemsDropped.increment();
            return false;
        }
        
        // 尝试写入环形缓冲区
        long currentWrite = writeIndex.get();
        long nextWrite = currentWrite + 1;
        
        // 检查缓冲区是否已满
        if (nextWrite - readIndex.get() >= bufferSize) {
            // 触发背压
            backpressureActive.set(true);
            backpressureEvents.increment();
            itemsDropped.increment();
            return false;
        }
        
        // 写入数据
        ringBuffer[(int) (currentWrite % bufferSize)] = item;
        writeIndex.set(nextWrite);
        
        return true;
    }
    
    /**
     * 添加转换器
     */
    @SuppressWarnings("unchecked")
    public <U> UltraFastStreamProcessor<T, U> map(Function<R, U> mapper) {
        transformers.add((Function<Object, Object>) mapper);
        return (UltraFastStreamProcessor<T, U>) this;
    }
    
    /**
     * 添加过滤器
     */
    public UltraFastStreamProcessor<T, R> filter(Predicate<R> predicate) {
        filters.add((Predicate<Object>) predicate);
        return this;
    }
    
    /**
     * 添加消费者
     */
    public UltraFastStreamProcessor<T, R> forEach(Consumer<R> consumer) {
        consumers.add((Consumer<Object>) consumer);
        return this;
    }
    
    /**
     * 处理循环
     */
    private void processingLoop() {
        while (running.get()) {
            try {
                // 从环形缓冲区读取数据
                long currentRead = readIndex.get();
                long currentWrite = writeIndex.get();
                
                if (currentRead >= currentWrite) {
                    // 没有数据，短暂休眠
                    Thread.sleep(1);
                    continue;
                }
                
                // 读取数据
                Object item = ringBuffer[(int) (currentRead % bufferSize)];
                if (item != null) {
                    // 处理数据
                    processItem(item);
                    
                    // 更新读取索引
                    readIndex.set(currentRead + 1);
                    
                    // 清理缓冲区位置
                    ringBuffer[(int) (currentRead % bufferSize)] = null;
                }
                
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("流处理循环异常", e);
                processingErrors.increment();
            }
        }
    }
    
    /**
     * 处理单个数据项
     */
    private void processItem(Object item) {
        long startTime = System.nanoTime();
        
        try {
            Object current = item;
            
            // 应用转换器
            for (Function<Object, Object> transformer : transformers) {
                current = transformer.apply(current);
                if (current == null) {
                    return; // 转换结果为null，跳过
                }
            }
            
            // 应用过滤器
            for (Predicate<Object> filter : filters) {
                if (!filter.test(current)) {
                    return; // 过滤掉
                }
            }
            
            // 应用消费者
            for (Consumer<Object> consumer : consumers) {
                consumer.accept(current);
            }
            
            itemsProcessed.increment();
            
        } catch (Exception e) {
            log.error("处理数据项失败: {}", item, e);
            processingErrors.increment();
        } finally {
            long endTime = System.nanoTime();
            long processingTime = endTime - startTime;
            
            // 更新统计信息
            totalProcessingTime.addAndGet(processingTime);
            
            long currentMax = maxProcessingTime.get();
            while (processingTime > currentMax && 
                   !maxProcessingTime.compareAndSet(currentMax, processingTime)) {
                currentMax = maxProcessingTime.get();
            }
            
            long currentMin = minProcessingTime.get();
            while (processingTime < currentMin && 
                   !minProcessingTime.compareAndSet(currentMin, processingTime)) {
                currentMin = minProcessingTime.get();
            }
        }
    }
    
    /**
     * 监控背压状态
     */
    private void monitorBackpressure() {
        long currentWrite = writeIndex.get();
        long currentRead = readIndex.get();
        long queueSize = currentWrite - currentRead;
        
        if (queueSize < backpressureThreshold * 0.8) {
            // 队列大小降到阈值的80%以下，解除背压
            if (backpressureActive.compareAndSet(true, false)) {
                log.debug("背压已解除: queueSize={}", queueSize);
            }
        }
        
        if (log.isTraceEnabled()) {
            log.trace("流处理器状态: queueSize={}, backpressure={}, processed={}, dropped={}", 
                queueSize, backpressureActive.get(), itemsProcessed.sum(), itemsDropped.sum());
        }
    }
    
    /**
     * 获取性能统计信息
     */
    public StreamProcessorStats getStats() {
        long processed = itemsProcessed.sum();
        long dropped = itemsDropped.sum();
        long errors = processingErrors.sum();
        long backpressureCount = backpressureEvents.sum();
        long totalTime = totalProcessingTime.get();
        long maxTime = maxProcessingTime.get();
        long minTime = minProcessingTime.get() == Long.MAX_VALUE ? 0 : minProcessingTime.get();
        
        double avgProcessingTime = processed > 0 ? (double) totalTime / processed / 1_000_000 : 0; // ms
        double throughput = processed > 0 ? processed * 1000.0 / (totalTime / 1_000_000) : 0; // items/sec
        double dropRate = (processed + dropped) > 0 ? (double) dropped / (processed + dropped) * 100 : 0;
        
        long queueSize = writeIndex.get() - readIndex.get();
        
        return new StreamProcessorStats(
            processed, dropped, errors, backpressureCount,
            queueSize, bufferSize, avgProcessingTime, 
            maxTime / 1_000_000.0, minTime / 1_000_000.0,
            throughput, dropRate, backpressureActive.get()
        );
    }
    
    /**
     * 流处理器统计信息
     */
    public static class StreamProcessorStats {
        public final long itemsProcessed;
        public final long itemsDropped;
        public final long processingErrors;
        public final long backpressureEvents;
        public final long currentQueueSize;
        public final int bufferSize;
        public final double avgProcessingTimeMs;
        public final double maxProcessingTimeMs;
        public final double minProcessingTimeMs;
        public final double throughputPerSec;
        public final double dropRatePercent;
        public final boolean backpressureActive;
        
        public StreamProcessorStats(long itemsProcessed, long itemsDropped, long processingErrors,
                                  long backpressureEvents, long currentQueueSize, int bufferSize,
                                  double avgProcessingTimeMs, double maxProcessingTimeMs, 
                                  double minProcessingTimeMs, double throughputPerSec, 
                                  double dropRatePercent, boolean backpressureActive) {
            this.itemsProcessed = itemsProcessed;
            this.itemsDropped = itemsDropped;
            this.processingErrors = processingErrors;
            this.backpressureEvents = backpressureEvents;
            this.currentQueueSize = currentQueueSize;
            this.bufferSize = bufferSize;
            this.avgProcessingTimeMs = avgProcessingTimeMs;
            this.maxProcessingTimeMs = maxProcessingTimeMs;
            this.minProcessingTimeMs = minProcessingTimeMs;
            this.throughputPerSec = throughputPerSec;
            this.dropRatePercent = dropRatePercent;
            this.backpressureActive = backpressureActive;
        }
        
        @Override
        public String toString() {
            return String.format(
                "StreamStats{processed=%d, dropped=%d(%.1f%%), errors=%d, " +
                "backpressure=%d, queue=%d/%d, throughput=%.1f/s, " +
                "avgTime=%.3fms, maxTime=%.3fms, minTime=%.3fms, active=%s}",
                itemsProcessed, itemsDropped, dropRatePercent, processingErrors,
                backpressureEvents, currentQueueSize, bufferSize, throughputPerSec,
                avgProcessingTimeMs, maxProcessingTimeMs, minProcessingTimeMs, backpressureActive
            );
        }
    }
    
    @PreDestroy
    public void shutdown() {
        if (running.compareAndSet(true, false)) {
            // 关闭线程池
            if (processingPool != null) {
                processingPool.shutdown();
                try {
                    if (!processingPool.awaitTermination(10, TimeUnit.SECONDS)) {
                        processingPool.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    processingPool.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
            
            if (scheduledExecutor != null) {
                scheduledExecutor.shutdown();
                try {
                    if (!scheduledExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        scheduledExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    scheduledExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
            
            log.info("超高性能流处理器已关闭");
        }
    }
}
