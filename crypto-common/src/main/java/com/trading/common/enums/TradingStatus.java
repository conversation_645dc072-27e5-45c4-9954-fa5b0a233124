package com.trading.common.enums;

/**
 * 交易状态枚举
 * 定义交易系统和交易对的各种状态
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum TradingStatus {
    
    /**
     * 正常交易 - 可以正常进行买卖交易
     */
    TRADING("TRADING", "正常交易", "可以正常进行买卖交易", true, true),
    
    /**
     * 暂停交易 - 暂时停止交易
     */
    HALT("HALT", "暂停交易", "暂时停止交易", false, false),
    
    /**
     * 只能买入 - 只允许买入操作
     */
    BUY_ONLY("BUY_ONLY", "只能买入", "只允许买入操作", true, false),
    
    /**
     * 只能卖出 - 只允许卖出操作
     */
    SELL_ONLY("SELL_ONLY", "只能卖出", "只允许卖出操作", false, true),
    
    /**
     * 预开市 - 开市前准备阶段
     */
    PRE_TRADING("PRE_TRADING", "预开市", "开市前准备阶段", false, false),
    
    /**
     * 收盘后 - 收盘后阶段
     */
    POST_TRADING("POST_TRADING", "收盘后", "收盘后阶段", false, false),
    
    /**
     * 维护中 - 系统维护中
     */
    MAINTENANCE("MAINTENANCE", "维护中", "系统维护中", false, false),
    
    /**
     * 熔断 - 触发熔断机制
     */
    CIRCUIT_BREAKER("CIRCUIT_BREAKER", "熔断", "触发熔断机制", false, false),
    
    /**
     * 退市 - 交易对已退市
     */
    DELISTED("DELISTED", "退市", "交易对已退市", false, false),
    
    /**
     * 即将退市 - 交易对即将退市
     */
    PENDING_DELISTING("PENDING_DELISTING", "即将退市", "交易对即将退市", true, true),
    
    /**
     * 新上市 - 新上市的交易对
     */
    NEWLY_LISTED("NEWLY_LISTED", "新上市", "新上市的交易对", true, true),
    
    /**
     * 限制交易 - 受到交易限制
     */
    RESTRICTED("RESTRICTED", "限制交易", "受到交易限制", false, false),
    
    /**
     * 异常状态 - 交易异常
     */
    ABNORMAL("ABNORMAL", "异常状态", "交易异常", false, false),
    
    /**
     * 未知状态 - 状态未知
     */
    UNKNOWN("UNKNOWN", "未知状态", "状态未知", false, false);
    
    /**
     * 状态码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 是否允许买入
     */
    private final boolean buyAllowed;
    
    /**
     * 是否允许卖出
     */
    private final boolean sellAllowed;
    
    /**
     * 构造函数
     * 
     * @param code 状态码
     * @param name 状态名称
     * @param description 状态描述
     * @param buyAllowed 是否允许买入
     * @param sellAllowed 是否允许卖出
     */
    TradingStatus(String code, String name, String description, boolean buyAllowed, boolean sellAllowed) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.buyAllowed = buyAllowed;
        this.sellAllowed = sellAllowed;
    }
    
    /**
     * 获取状态码
     * 
     * @return 状态码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取状态名称
     * 
     * @return 状态名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取状态描述
     * 
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 是否允许买入
     * 
     * @return true表示允许买入，false表示不允许
     */
    public boolean isBuyAllowed() {
        return buyAllowed;
    }
    
    /**
     * 是否允许卖出
     * 
     * @return true表示允许卖出，false表示不允许
     */
    public boolean isSellAllowed() {
        return sellAllowed;
    }
    
    /**
     * 根据状态码获取枚举值
     * 
     * @param code 状态码
     * @return 对应的枚举值，如果找不到则返回UNKNOWN
     */
    public static TradingStatus fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return UNKNOWN;
        }
        
        for (TradingStatus status : values()) {
            if (status.code.equalsIgnoreCase(code.trim())) {
                return status;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 检查是否为正常交易状态
     * 
     * @return true表示正常交易，false表示非正常交易
     */
    public boolean isNormalTrading() {
        return this == TRADING || this == NEWLY_LISTED || this == PENDING_DELISTING;
    }
    
    /**
     * 检查是否为暂停状态
     * 
     * @return true表示暂停，false表示非暂停
     */
    public boolean isHalted() {
        return this == HALT || this == MAINTENANCE || this == CIRCUIT_BREAKER;
    }
    
    /**
     * 检查是否为限制状态
     * 
     * @return true表示限制，false表示非限制
     */
    public boolean isRestricted() {
        return this == BUY_ONLY || this == SELL_ONLY || this == RESTRICTED;
    }
    
    /**
     * 检查是否为时间相关状态
     * 
     * @return true表示时间相关，false表示非时间相关
     */
    public boolean isTimeRelated() {
        return this == PRE_TRADING || this == POST_TRADING;
    }
    
    /**
     * 检查是否为终止状态
     * 
     * @return true表示终止，false表示非终止
     */
    public boolean isTerminated() {
        return this == DELISTED;
    }
    
    /**
     * 检查是否为异常状态
     * 
     * @return true表示异常，false表示正常
     */
    public boolean isAbnormal() {
        return this == ABNORMAL || this == UNKNOWN;
    }
    
    /**
     * 检查是否允许指定方向的交易
     * 
     * @param orderSide 订单方向
     * @return true表示允许，false表示不允许
     */
    public boolean isOrderAllowed(OrderSide orderSide) {
        if (orderSide == null) {
            return false;
        }
        
        switch (orderSide) {
            case BUY:
                return buyAllowed;
            case SELL:
                return sellAllowed;
            default:
                return false;
        }
    }
    
    /**
     * 获取状态的严重程度
     * 
     * @return 严重程度（1-5，数值越大越严重）
     */
    public int getSeverityLevel() {
        switch (this) {
            case TRADING:
            case NEWLY_LISTED:
                return 1; // 正常
            case BUY_ONLY:
            case SELL_ONLY:
            case PENDING_DELISTING:
                return 2; // 轻微限制
            case PRE_TRADING:
            case POST_TRADING:
                return 3; // 时间限制
            case HALT:
            case MAINTENANCE:
            case RESTRICTED:
                return 4; // 严重限制
            case CIRCUIT_BREAKER:
            case DELISTED:
            case ABNORMAL:
            case UNKNOWN:
                return 5; // 最严重
            default:
                return 3;
        }
    }
    
    /**
     * 获取状态对应的颜色（用于UI显示）
     * 
     * @return 颜色代码
     */
    public String getColor() {
        switch (this) {
            case TRADING:
            case NEWLY_LISTED:
                return "#52c41a"; // 绿色 - 正常
            case BUY_ONLY:
            case SELL_ONLY:
            case PENDING_DELISTING:
                return "#faad14"; // 橙色 - 警告
            case PRE_TRADING:
            case POST_TRADING:
                return "#1890ff"; // 蓝色 - 信息
            case HALT:
            case MAINTENANCE:
            case RESTRICTED:
                return "#722ed1"; // 紫色 - 限制
            case CIRCUIT_BREAKER:
            case DELISTED:
            case ABNORMAL:
            case UNKNOWN:
                return "#ff4d4f"; // 红色 - 错误
            default:
                return "#8c8c8c"; // 灰色
        }
    }
    
    /**
     * 获取状态对应的图标（用于UI显示）
     * 
     * @return 图标名称
     */
    public String getIcon() {
        switch (this) {
            case TRADING:
                return "check-circle";
            case HALT:
                return "pause-circle";
            case BUY_ONLY:
                return "arrow-up";
            case SELL_ONLY:
                return "arrow-down";
            case PRE_TRADING:
                return "clock-circle";
            case POST_TRADING:
                return "clock-circle";
            case MAINTENANCE:
                return "tool";
            case CIRCUIT_BREAKER:
                return "stop";
            case DELISTED:
                return "close-circle";
            case PENDING_DELISTING:
                return "warning";
            case NEWLY_LISTED:
                return "star";
            case RESTRICTED:
                return "lock";
            case ABNORMAL:
                return "exclamation-circle";
            case UNKNOWN:
                return "question-circle";
            default:
                return "minus-circle";
        }
    }
    
    @Override
    public String toString() {
        return String.format("TradingStatus{code='%s', name='%s', description='%s', buyAllowed=%s, sellAllowed=%s}", 
                code, name, description, buyAllowed, sellAllowed);
    }
}
