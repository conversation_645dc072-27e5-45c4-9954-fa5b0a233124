package com.trading.common.enums;

/**
 * 交易对符号枚举
 * 定义币安支持的主要交易对
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum Symbol {
    
    // 主流币对USDT交易对
    BTCUSDT("BTCUSDT", "BTC", "USDT", "比特币/USDT"),
    ETHUSDT("ETHUSDT", "ETH", "USDT", "以太坊/USDT"),
    BNBUSDT("BNBUSDT", "BNB", "USDT", "币安币/USDT"),
    ADAUSDT("ADAUSDT", "ADA", "USDT", "艾达币/USDT"),
    XRPUSDT("XRPUSDT", "XRP", "USDT", "瑞波币/USDT"),
    SOLUSDT("SOLUSDT", "SOL", "USDT", "Solana/USDT"),
    DOTUSDT("DOTUSDT", "DOT", "USDT", "波卡/USDT"),
    DOGEUSDT("DOGEUSDT", "DOGE", "USDT", "狗狗币/USDT"),
    AVAXUSDT("AVAXUSDT", "AVAX", "USDT", "雪崩/USDT"),
    MATICUSDT("MATICUSDT", "MATIC", "USDT", "Polygon/USDT"),
    LINKUSDT("LINKUSDT", "LINK", "USDT", "Chainlink/USDT"),
    LTCUSDT("LTCUSDT", "LTC", "USDT", "莱特币/USDT"),
    UNIUSDT("UNIUSDT", "UNI", "USDT", "Uniswap/USDT"),
    ATOMUSDT("ATOMUSDT", "ATOM", "USDT", "Cosmos/USDT"),
    VETUSDT("VETUSDT", "VET", "USDT", "唯链/USDT"),
    FILUSDT("FILUSDT", "FIL", "USDT", "Filecoin/USDT"),
    TRXUSDT("TRXUSDT", "TRX", "USDT", "波场/USDT"),
    ETCUSDT("ETCUSDT", "ETC", "USDT", "以太经典/USDT"),
    XLMUSDT("XLMUSDT", "XLM", "USDT", "恒星币/USDT"),
    THETAUSDT("THETAUSDT", "THETA", "USDT", "Theta/USDT"),
    
    // BTC交易对
    ETHBTC("ETHBTC", "ETH", "BTC", "以太坊/比特币"),
    BNBBTC("BNBBTC", "BNB", "BTC", "币安币/比特币"),
    ADABTC("ADABTC", "ADA", "BTC", "艾达币/比特币"),
    XRPBTC("XRPBTC", "XRP", "BTC", "瑞波币/比特币"),
    DOTBTC("DOTBTC", "DOT", "BTC", "波卡/比特币"),
    LINKBTC("LINKBTC", "LINK", "BTC", "Chainlink/比特币"),
    LTCBTC("LTCBTC", "LTC", "BTC", "莱特币/比特币"),
    
    // ETH交易对
    BNBETH("BNBETH", "BNB", "ETH", "币安币/以太坊"),
    ADAETH("ADAETH", "ADA", "ETH", "艾达币/以太坊"),
    XRPETH("XRPETH", "XRP", "ETH", "瑞波币/以太坊"),
    LINKETH("LINKETH", "LINK", "ETH", "Chainlink/以太坊"),
    
    // BUSD交易对
    BTCBUSD("BTCBUSD", "BTC", "BUSD", "比特币/BUSD"),
    ETHBUSD("ETHBUSD", "ETH", "BUSD", "以太坊/BUSD"),
    BNBBUSD("BNBBUSD", "BNB", "BUSD", "币安币/BUSD"),
    ADABUSD("ADABUSD", "ADA", "BUSD", "艾达币/BUSD"),
    
    // 新兴币种
    SHIBUSDT("SHIBUSDT", "SHIB", "USDT", "柴犬币/USDT"),
    PEPEUSDT("PEPEUSDT", "PEPE", "USDT", "佩佩币/USDT"),
    FLOKIUSDT("FLOKIUSDT", "FLOKI", "USDT", "Floki/USDT"),
    APTUSDT("APTUSDT", "APT", "USDT", "Aptos/USDT"),
    OPUSDT("OPUSDT", "OP", "USDT", "Optimism/USDT"),
    ARBUSDT("ARBUSDT", "ARB", "USDT", "Arbitrum/USDT"),
    SUIUSDT("SUIUSDT", "SUI", "USDT", "Sui/USDT"),
    
    // DeFi代币
    AAVEUSDT("AAVEUSDT", "AAVE", "USDT", "Aave/USDT"),
    COMPUSDT("COMPUSDT", "COMP", "USDT", "Compound/USDT"),
    MKRUSDT("MKRUSDT", "MKR", "USDT", "Maker/USDT"),
    SUSHIUSDT("SUSHIUSDT", "SUSHI", "USDT", "SushiSwap/USDT"),
    CRVUSDT("CRVUSDT", "CRV", "USDT", "Curve/USDT"),
    YFIUSDT("YFIUSDT", "YFI", "USDT", "yearn.finance/USDT"),
    
    // Layer 2代币
    LRCUSDT("LRCUSDT", "LRC", "USDT", "Loopring/USDT"),
    IMXUSDT("IMXUSDT", "IMX", "USDT", "Immutable X/USDT"),
    
    // 稳定币交易对
    USDCUSDT("USDCUSDT", "USDC", "USDT", "USDC/USDT"),
    DAIUSDT("DAIUSDT", "DAI", "USDT", "DAI/USDT"),
    TUSDUSDT("TUSDUSDT", "TUSD", "USDT", "TrueUSD/USDT"),
    
    // 期货专用交易对
    BTCUSDT_PERP("BTCUSDT", "BTC", "USDT", "比特币永续合约"),
    ETHUSDT_PERP("ETHUSDT", "ETH", "USDT", "以太坊永续合约"),
    BNBUSDT_PERP("BNBUSDT", "BNB", "USDT", "币安币永续合约");
    
    private final String symbol;
    private final String baseAsset;
    private final String quoteAsset;
    private final String description;
    
    Symbol(String symbol, String baseAsset, String quoteAsset, String description) {
        this.symbol = symbol;
        this.baseAsset = baseAsset;
        this.quoteAsset = quoteAsset;
        this.description = description;
    }
    
    /**
     * 获取交易对符号
     * 
     * @return 交易对符号
     */
    public String getSymbol() {
        return symbol;
    }
    
    /**
     * 获取基础资产
     * 
     * @return 基础资产
     */
    public String getBaseAsset() {
        return baseAsset;
    }
    
    /**
     * 获取报价资产
     * 
     * @return 报价资产
     */
    public String getQuoteAsset() {
        return quoteAsset;
    }
    
    /**
     * 获取描述
     * 
     * @return 描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据字符串获取Symbol枚举
     * 
     * @param symbol 交易对字符串
     * @return Symbol枚举，如果未找到返回null
     */
    public static Symbol fromString(String symbol) {
        if (symbol == null || symbol.trim().isEmpty()) {
            return null;
        }
        
        String upperSymbol = symbol.toUpperCase().trim();
        for (Symbol s : values()) {
            if (s.symbol.equals(upperSymbol)) {
                return s;
            }
        }
        return null;
    }
    
    /**
     * 检查是否为USDT交易对
     * 
     * @return 是否为USDT交易对
     */
    public boolean isUsdtPair() {
        return "USDT".equals(quoteAsset);
    }
    
    /**
     * 检查是否为BTC交易对
     * 
     * @return 是否为BTC交易对
     */
    public boolean isBtcPair() {
        return "BTC".equals(quoteAsset);
    }
    
    /**
     * 检查是否为ETH交易对
     * 
     * @return 是否为ETH交易对
     */
    public boolean isEthPair() {
        return "ETH".equals(quoteAsset);
    }
    
    /**
     * 检查是否为稳定币交易对
     * 
     * @return 是否为稳定币交易对
     */
    public boolean isStableCoinPair() {
        return "USDT".equals(quoteAsset) || "USDC".equals(quoteAsset) || 
               "BUSD".equals(quoteAsset) || "DAI".equals(quoteAsset) || 
               "TUSD".equals(quoteAsset);
    }
    
    /**
     * 检查是否为期货交易对
     * 
     * @return 是否为期货交易对
     */
    public boolean isFuturesPair() {
        return this == BTCUSDT_PERP || this == ETHUSDT_PERP || this == BNBUSDT_PERP;
    }
    
    /**
     * 获取交易对的显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return baseAsset + "/" + quoteAsset;
    }
    
    @Override
    public String toString() {
        return symbol;
    }
}
