package com.trading.common.enums;

/**
 * 订单类型枚举
 * 定义各种订单类型
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum OrderType {
    
    /**
     * 市价单 - 以当前市场价格立即成交
     */
    MARKET("MARKET", "市价单", "以当前市场价格立即成交", true, false),
    
    /**
     * 限价单 - 以指定价格或更好价格成交
     */
    LIMIT("LIMIT", "限价单", "以指定价格或更好价格成交", false, true),
    
    /**
     * 止损单 - 当价格达到止损价时转为市价单
     */
    STOP_LOSS("STOP_LOSS", "止损单", "当价格达到止损价时转为市价单", true, false),
    
    /**
     * 止损限价单 - 当价格达到止损价时转为限价单
     */
    STOP_LOSS_LIMIT("STOP_LOSS_LIMIT", "止损限价单", "当价格达到止损价时转为限价单", false, true),
    
    /**
     * 止盈单 - 当价格达到止盈价时转为市价单
     */
    TAKE_PROFIT("TAKE_PROFIT", "止盈单", "当价格达到止盈价时转为市价单", true, false),
    
    /**
     * 止盈限价单 - 当价格达到止盈价时转为限价单
     */
    TAKE_PROFIT_LIMIT("TAKE_PROFIT_LIMIT", "止盈限价单", "当价格达到止盈价时转为限价单", false, true),
    
    /**
     * 限价做市单 - 只能作为挂单，不能立即成交
     */
    LIMIT_MAKER("LIMIT_MAKER", "限价做市单", "只能作为挂单，不能立即成交", false, true),
    
    /**
     * 立即成交或取消 - 立即成交，无法成交的部分立即取消
     */
    IOC("IOC", "立即成交或取消", "立即成交，无法成交的部分立即取消", true, false),
    
    /**
     * 全部成交或取消 - 必须全部成交，否则立即取消
     */
    FOK("FOK", "全部成交或取消", "必须全部成交，否则立即取消", true, false),
    
    /**
     * 冰山订单 - 大订单分批执行，每次只显示小部分
     */
    ICEBERG("ICEBERG", "冰山订单", "大订单分批执行，每次只显示小部分", false, true),
    
    /**
     * 时间加权平均价格订单 - 在指定时间内以TWAP策略执行
     */
    TWAP("TWAP", "时间加权平均价格", "在指定时间内以TWAP策略执行", false, true),
    
    /**
     * 成交量加权平均价格订单 - 以VWAP策略执行
     */
    VWAP("VWAP", "成交量加权平均价格", "以VWAP策略执行", false, true);
    
    /**
     * 类型码
     */
    private final String code;
    
    /**
     * 类型名称
     */
    private final String name;
    
    /**
     * 类型描述
     */
    private final String description;
    
    /**
     * 是否为市价类型（立即成交）
     */
    private final boolean isMarketType;
    
    /**
     * 是否需要指定价格
     */
    private final boolean requiresPrice;
    
    /**
     * 构造函数
     * 
     * @param code 类型码
     * @param name 类型名称
     * @param description 类型描述
     * @param isMarketType 是否为市价类型
     * @param requiresPrice 是否需要指定价格
     */
    OrderType(String code, String name, String description, boolean isMarketType, boolean requiresPrice) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.isMarketType = isMarketType;
        this.requiresPrice = requiresPrice;
    }
    
    /**
     * 获取类型码
     * 
     * @return 类型码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取类型名称
     * 
     * @return 类型名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取类型描述
     * 
     * @return 类型描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 是否为市价类型
     * 
     * @return true表示市价类型，false表示限价类型
     */
    public boolean isMarketType() {
        return isMarketType;
    }
    
    /**
     * 是否需要指定价格
     * 
     * @return true表示需要指定价格，false表示不需要
     */
    public boolean requiresPrice() {
        return requiresPrice;
    }
    
    /**
     * 根据类型码获取枚举值
     * 
     * @param code 类型码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static OrderType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (OrderType type : values()) {
            if (type.code.equalsIgnoreCase(code.trim())) {
                return type;
            }
        }
        
        return null;
    }
    
    /**
     * 检查是否为基础订单类型（市价单或限价单）
     * 
     * @return true表示基础类型，false表示高级类型
     */
    public boolean isBasicType() {
        return this == MARKET || this == LIMIT;
    }
    
    /**
     * 检查是否为条件订单类型（需要触发条件）
     * 
     * @return true表示条件订单，false表示普通订单
     */
    public boolean isConditionalType() {
        return this == STOP_LOSS || this == STOP_LOSS_LIMIT || 
               this == TAKE_PROFIT || this == TAKE_PROFIT_LIMIT;
    }
    
    /**
     * 检查是否为算法订单类型
     * 
     * @return true表示算法订单，false表示普通订单
     */
    public boolean isAlgorithmicType() {
        return this == ICEBERG || this == TWAP || this == VWAP;
    }
    
    /**
     * 检查是否为时间相关订单类型
     * 
     * @return true表示时间相关，false表示非时间相关
     */
    public boolean isTimeRelatedType() {
        return this == IOC || this == FOK;
    }
    
    /**
     * 获取订单类型的优先级（用于排序）
     * 数值越小优先级越高
     * 
     * @return 优先级数值
     */
    public int getPriority() {
        switch (this) {
            case MARKET:
                return 1;
            case LIMIT:
                return 2;
            case IOC:
                return 3;
            case FOK:
                return 4;
            case LIMIT_MAKER:
                return 5;
            case STOP_LOSS:
                return 6;
            case STOP_LOSS_LIMIT:
                return 7;
            case TAKE_PROFIT:
                return 8;
            case TAKE_PROFIT_LIMIT:
                return 9;
            case ICEBERG:
                return 10;
            case TWAP:
                return 11;
            case VWAP:
                return 12;
            default:
                return 99;
        }
    }
    
    /**
     * 获取类型对应的颜色（用于UI显示）
     * 
     * @return 颜色代码
     */
    public String getColor() {
        if (isMarketType()) {
            return "#ff4d4f"; // 红色 - 市价类型
        } else if (isConditionalType()) {
            return "#faad14"; // 橙色 - 条件订单
        } else if (isAlgorithmicType()) {
            return "#722ed1"; // 紫色 - 算法订单
        } else {
            return "#1890ff"; // 蓝色 - 限价类型
        }
    }
    
    /**
     * 获取类型对应的图标（用于UI显示）
     * 
     * @return 图标名称
     */
    public String getIcon() {
        switch (this) {
            case MARKET:
                return "flash";
            case LIMIT:
                return "aim";
            case STOP_LOSS:
            case TAKE_PROFIT:
                return "shield";
            case STOP_LOSS_LIMIT:
            case TAKE_PROFIT_LIMIT:
                return "safety";
            case LIMIT_MAKER:
                return "plus";
            case IOC:
            case FOK:
                return "clock-circle";
            case ICEBERG:
                return "eye-invisible";
            case TWAP:
            case VWAP:
                return "line-chart";
            default:
                return "question";
        }
    }
    
    @Override
    public String toString() {
        return String.format("OrderType{code='%s', name='%s', description='%s', isMarketType=%s, requiresPrice=%s}", 
                code, name, description, isMarketType, requiresPrice);
    }
}
