package com.trading.common.enums;

/**
 * 订单有效期枚举
 * 定义订单的时间有效性规则
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum TimeInForce {
    
    /**
     * 成交为止 - 订单一直有效直到完全成交或被取消
     */
    GTC("GTC", "成交为止", "订单一直有效直到完全成交或被取消"),
    
    /**
     * 立即成交或取消 - 立即成交，无法成交的部分立即取消
     */
    IOC("IOC", "立即成交或取消", "立即成交，无法成交的部分立即取消"),
    
    /**
     * 全部成交或取消 - 必须全部成交，否则立即取消
     */
    FOK("FOK", "全部成交或取消", "必须全部成交，否则立即取消"),
    
    /**
     * 当日有效 - 订单在当日交易时间内有效
     */
    DAY("DAY", "当日有效", "订单在当日交易时间内有效"),
    
    /**
     * 指定时间有效 - 订单在指定时间前有效
     */
    GTD("GTD", "指定时间有效", "订单在指定时间前有效");
    
    /**
     * 有效期码
     */
    private final String code;
    
    /**
     * 有效期名称
     */
    private final String name;
    
    /**
     * 有效期描述
     */
    private final String description;
    
    /**
     * 构造函数
     * 
     * @param code 有效期码
     * @param name 有效期名称
     * @param description 有效期描述
     */
    TimeInForce(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 获取有效期码
     * 
     * @return 有效期码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取有效期名称
     * 
     * @return 有效期名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取有效期描述
     * 
     * @return 有效期描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据有效期码获取枚举值
     * 
     * @param code 有效期码
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果找不到对应的枚举值
     */
    public static TimeInForce fromCode(String code) {
        for (TimeInForce timeInForce : values()) {
            if (timeInForce.getCode().equals(code)) {
                return timeInForce;
            }
        }
        throw new IllegalArgumentException("未知的订单有效期: " + code);
    }
    
    /**
     * 检查是否为立即执行类型
     * 
     * @return true表示立即执行，false表示非立即执行
     */
    public boolean isImmediateType() {
        return this == IOC || this == FOK;
    }
    
    /**
     * 检查是否为时间限制类型
     * 
     * @return true表示有时间限制，false表示无时间限制
     */
    public boolean isTimeRestrictedType() {
        return this == DAY || this == GTD;
    }
    
    /**
     * 检查是否允许部分成交
     * 
     * @return true表示允许部分成交，false表示不允许部分成交
     */
    public boolean allowsPartialFill() {
        return this != FOK;
    }
}
