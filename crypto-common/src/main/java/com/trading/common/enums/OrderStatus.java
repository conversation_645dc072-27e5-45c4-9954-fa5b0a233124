package com.trading.common.enums;

/**
 * 订单状态枚举
 * 定义订单在交易生命周期中的各种状态
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum OrderStatus {
    
    /**
     * 新建订单 - 订单已创建但尚未提交到交易所
     */
    NEW("NEW", "新建订单", "订单已创建但尚未提交"),
    
    /**
     * 待成交 - 订单已提交到交易所，等待成交
     */
    PENDING("PENDING", "待成交", "订单已提交，等待成交"),
    
    /**
     * 部分成交 - 订单已部分成交，剩余数量继续等待成交
     */
    PARTIALLY_FILLED("PARTIALLY_FILLED", "部分成交", "订单已部分成交"),
    
    /**
     * 完全成交 - 订单已完全成交
     */
    FILLED("FILLED", "完全成交", "订单已完全成交"),
    
    /**
     * 已取消 - 订单已被取消
     */
    CANCELED("CANCELED", "已取消", "订单已被取消"),
    
    /**
     * 被拒绝 - 订单被交易所拒绝
     */
    REJECTED("REJECTED", "被拒绝", "订单被交易所拒绝"),
    
    /**
     * 已过期 - 订单已过期
     */
    EXPIRED("EXPIRED", "已过期", "订单已过期"),
    
    /**
     * 待取消 - 订单正在取消过程中
     */
    PENDING_CANCEL("PENDING_CANCEL", "待取消", "订单正在取消过程中"),
    
    /**
     * 取消失败 - 订单取消失败
     */
    CANCEL_FAILED("CANCEL_FAILED", "取消失败", "订单取消失败"),
    
    /**
     * 系统错误 - 订单因系统错误而失败
     */
    SYSTEM_ERROR("SYSTEM_ERROR", "系统错误", "订单因系统错误而失败"),
    
    /**
     * 未知状态 - 订单状态未知
     */
    UNKNOWN("UNKNOWN", "未知状态", "订单状态未知");
    
    /**
     * 状态码
     */
    private final String code;
    
    /**
     * 状态名称
     */
    private final String name;
    
    /**
     * 状态描述
     */
    private final String description;
    
    /**
     * 构造函数
     * 
     * @param code 状态码
     * @param name 状态名称
     * @param description 状态描述
     */
    OrderStatus(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 获取状态码
     * 
     * @return 状态码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取状态名称
     * 
     * @return 状态名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取状态描述
     * 
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据状态码获取枚举值
     * 
     * @param code 状态码
     * @return 对应的枚举值，如果找不到则返回UNKNOWN
     */
    public static OrderStatus fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return UNKNOWN;
        }
        
        for (OrderStatus status : values()) {
            if (status.code.equalsIgnoreCase(code.trim())) {
                return status;
            }
        }
        
        return UNKNOWN;
    }
    
    /**
     * 检查订单是否为活跃状态（可以被取消或修改）
     * 
     * @return true表示活跃状态，false表示非活跃状态
     */
    public boolean isActive() {
        return this == NEW || this == PENDING || this == PARTIALLY_FILLED || this == PENDING_CANCEL;
    }
    
    /**
     * 检查订单是否为最终状态（不会再发生变化）
     * 
     * @return true表示最终状态，false表示非最终状态
     */
    public boolean isFinal() {
        return this == FILLED || this == CANCELED || this == REJECTED || 
               this == EXPIRED || this == CANCEL_FAILED || this == SYSTEM_ERROR;
    }
    
    /**
     * 检查订单是否已成交（完全或部分）
     * 
     * @return true表示已成交，false表示未成交
     */
    public boolean isFilled() {
        return this == FILLED || this == PARTIALLY_FILLED;
    }
    
    /**
     * 检查订单是否可以取消
     * 
     * @return true表示可以取消，false表示不可以取消
     */
    public boolean isCancelable() {
        return this == NEW || this == PENDING || this == PARTIALLY_FILLED;
    }
    
    /**
     * 检查订单是否为成功状态
     * 
     * @return true表示成功状态，false表示失败状态
     */
    public boolean isSuccessful() {
        return this == FILLED || this == PARTIALLY_FILLED || this == CANCELED;
    }
    
    /**
     * 检查订单是否为失败状态
     * 
     * @return true表示失败状态，false表示非失败状态
     */
    public boolean isFailed() {
        return this == REJECTED || this == EXPIRED || this == CANCEL_FAILED || this == SYSTEM_ERROR;
    }
    
    /**
     * 获取状态的优先级（用于排序）
     * 数值越小优先级越高
     * 
     * @return 优先级数值
     */
    public int getPriority() {
        switch (this) {
            case SYSTEM_ERROR:
                return 1;
            case REJECTED:
                return 2;
            case CANCEL_FAILED:
                return 3;
            case EXPIRED:
                return 4;
            case PENDING_CANCEL:
                return 5;
            case NEW:
                return 6;
            case PENDING:
                return 7;
            case PARTIALLY_FILLED:
                return 8;
            case FILLED:
                return 9;
            case CANCELED:
                return 10;
            case UNKNOWN:
            default:
                return 99;
        }
    }
    
    /**
     * 获取状态对应的颜色（用于UI显示）
     * 
     * @return 颜色代码
     */
    public String getColor() {
        switch (this) {
            case NEW:
            case PENDING:
                return "#1890ff"; // 蓝色
            case PARTIALLY_FILLED:
                return "#faad14"; // 橙色
            case FILLED:
                return "#52c41a"; // 绿色
            case CANCELED:
                return "#d9d9d9"; // 灰色
            case REJECTED:
            case EXPIRED:
            case CANCEL_FAILED:
            case SYSTEM_ERROR:
                return "#ff4d4f"; // 红色
            case PENDING_CANCEL:
                return "#722ed1"; // 紫色
            case UNKNOWN:
            default:
                return "#8c8c8c"; // 深灰色
        }
    }
    
    @Override
    public String toString() {
        return String.format("OrderStatus{code='%s', name='%s', description='%s'}", 
                code, name, description);
    }
}
