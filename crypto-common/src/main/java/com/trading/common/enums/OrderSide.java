package com.trading.common.enums;

/**
 * 订单方向枚举
 * 定义买入和卖出方向
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum OrderSide {
    
    /**
     * 买入
     */
    BUY("BUY", "买入", "买入订单", 1),
    
    /**
     * 卖出
     */
    SELL("SELL", "卖出", "卖出订单", -1);
    
    /**
     * 方向码
     */
    private final String code;
    
    /**
     * 方向名称
     */
    private final String name;
    
    /**
     * 方向描述
     */
    private final String description;
    
    /**
     * 方向系数（买入为1，卖出为-1，用于计算）
     */
    private final int coefficient;
    
    /**
     * 构造函数
     * 
     * @param code 方向码
     * @param name 方向名称
     * @param description 方向描述
     * @param coefficient 方向系数
     */
    OrderSide(String code, String name, String description, int coefficient) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.coefficient = coefficient;
    }
    
    /**
     * 获取方向码
     * 
     * @return 方向码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取方向名称
     * 
     * @return 方向名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取方向描述
     * 
     * @return 方向描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取方向系数
     * 
     * @return 方向系数
     */
    public int getCoefficient() {
        return coefficient;
    }
    
    /**
     * 根据方向码获取枚举值
     * 
     * @param code 方向码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static OrderSide fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (OrderSide side : values()) {
            if (side.code.equalsIgnoreCase(code.trim())) {
                return side;
            }
        }
        
        return null;
    }
    
    /**
     * 根据方向系数获取枚举值
     * 
     * @param coefficient 方向系数
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static OrderSide fromCoefficient(int coefficient) {
        for (OrderSide side : values()) {
            if (side.coefficient == coefficient) {
                return side;
            }
        }
        return null;
    }
    
    /**
     * 检查是否为买入方向
     * 
     * @return true表示买入，false表示卖出
     */
    public boolean isBuy() {
        return this == BUY;
    }
    
    /**
     * 检查是否为卖出方向
     * 
     * @return true表示卖出，false表示买入
     */
    public boolean isSell() {
        return this == SELL;
    }
    
    /**
     * 获取相反方向
     * 
     * @return 相反方向的枚举值
     */
    public OrderSide getOpposite() {
        return this == BUY ? SELL : BUY;
    }
    
    /**
     * 获取方向对应的颜色（用于UI显示）
     * 
     * @return 颜色代码
     */
    public String getColor() {
        switch (this) {
            case BUY:
                return "#52c41a"; // 绿色
            case SELL:
                return "#ff4d4f"; // 红色
            default:
                return "#8c8c8c"; // 灰色
        }
    }
    
    /**
     * 获取方向对应的图标（用于UI显示）
     * 
     * @return 图标名称
     */
    public String getIcon() {
        switch (this) {
            case BUY:
                return "arrow-up";
            case SELL:
                return "arrow-down";
            default:
                return "minus";
        }
    }
    
    @Override
    public String toString() {
        return String.format("OrderSide{code='%s', name='%s', description='%s', coefficient=%d}", 
                code, name, description, coefficient);
    }
}
