package com.trading.common.enums;

/**
 * 默认URL枚举
 * 定义币安API和WebSocket的默认URL地址
 * 基于binance-futures-connector-java-main SDK的DefaultUrls类
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum DefaultUrls {
    
    /**
     * USD-M期货生产环境API URL
     */
    USDM_PROD_URL("https://fapi.binance.com", "USD-M期货生产环境API"),
    
    /**
     * COIN-M期货生产环境API URL
     */
    COINM_PROD_URL("https://dapi.binance.com", "COIN-M期货生产环境API"),
    
    /**
     * 现货生产环境API URL
     */
    SPOT_PROD_URL("https://api.binance.com", "现货生产环境API"),
    
    /**
     * USD-M期货测试网API URL
     */
    USDM_TESTNET_URL("https://testnet.binancefuture.com", "USD-M期货测试网API"),

    /**
     * USD-M期货测试网API URL（别名）
     */
    USDM_TEST_URL("https://testnet.binancefuture.com", "USD-M期货测试网API"),

    /**
     * COIN-M期货测试网API URL
     */
    COINM_TESTNET_URL("https://testnet.binancefuture.com", "COIN-M期货测试网API"),

    /**
     * COIN-M期货测试网API URL（别名）
     */
    COINM_TEST_URL("https://testnet.binancefuture.com", "COIN-M期货测试网API"),
    
    /**
     * 现货测试网API URL
     */
    SPOT_TESTNET_URL("https://testnet.binance.vision", "现货测试网API"),
    
    /**
     * USD-M期货WebSocket URL
     */
    USDM_WS_URL("wss://fstream.binance.com", "USD-M期货WebSocket"),
    
    /**
     * COIN-M期货WebSocket URL
     */
    COINM_WS_URL("wss://dstream.binance.com", "COIN-M期货WebSocket"),
    
    /**
     * 现货WebSocket URL
     */
    SPOT_WS_URL("wss://stream.binance.com:9443", "现货WebSocket"),
    
    /**
     * USD-M期货测试网WebSocket URL
     */
    USDM_TESTNET_WS_URL("wss://stream.binancefuture.com", "USD-M期货测试网WebSocket"),
    
    /**
     * COIN-M期货测试网WebSocket URL
     */
    COINM_TESTNET_WS_URL("wss://dstream.binancefuture.com", "COIN-M期货测试网WebSocket"),
    
    /**
     * 现货测试网WebSocket URL
     */
    SPOT_TESTNET_WS_URL("wss://testnet.binance.vision", "现货测试网WebSocket");
    
    private final String url;
    private final String description;
    
    /**
     * 构造函数
     * 
     * @param url URL地址
     * @param description 描述
     */
    DefaultUrls(String url, String description) {
        this.url = url;
        this.description = description;
    }
    
    /**
     * 获取URL地址
     * 
     * @return URL地址
     */
    public String getUrl() {
        return url;
    }
    
    /**
     * 获取描述
     * 
     * @return 描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据URL查找枚举
     * 
     * @param url URL地址
     * @return 对应的枚举，如果未找到返回null
     */
    public static DefaultUrls fromUrl(String url) {
        if (url == null || url.trim().isEmpty()) {
            return null;
        }
        
        for (DefaultUrls defaultUrl : values()) {
            if (defaultUrl.url.equals(url.trim())) {
                return defaultUrl;
            }
        }
        return null;
    }
    
    /**
     * 检查是否为测试网URL
     * 
     * @return 如果是测试网URL返回true，否则返回false
     */
    public boolean isTestnet() {
        return this.name().contains("TESTNET");
    }
    
    /**
     * 检查是否为WebSocket URL
     * 
     * @return 如果是WebSocket URL返回true，否则返回false
     */
    public boolean isWebSocket() {
        return this.url.startsWith("wss://");
    }
    
    /**
     * 检查是否为期货URL
     * 
     * @return 如果是期货URL返回true，否则返回false
     */
    public boolean isFutures() {
        return this.name().contains("USDM") || this.name().contains("COINM");
    }
    
    /**
     * 检查是否为现货URL
     * 
     * @return 如果是现货URL返回true，否则返回false
     */
    public boolean isSpot() {
        return this.name().contains("SPOT");
    }
    
    @Override
    public String toString() {
        return String.format("%s: %s (%s)", name(), url, description);
    }
}
