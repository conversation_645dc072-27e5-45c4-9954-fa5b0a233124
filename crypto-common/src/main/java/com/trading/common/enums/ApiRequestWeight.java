package com.trading.common.enums;

/**
 * API请求权重枚举
 * 定义不同API请求的权重级别，用于限流控制
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum ApiRequestWeight {
    
    /**
     * 权重1 - 轻量级请求
     * 每分钟限制1200次
     */
    WEIGHT_1(1, 1200, "轻量级请求，如ping、time等"),
    
    /**
     * 权重5 - 中等权重请求  
     * 每分钟限制240次
     */
    WEIGHT_5(5, 240, "中等权重请求，如exchangeInfo、depth等"),
    
    /**
     * 权重10 - 较重权重请求
     * 每分钟限制120次
     */
    WEIGHT_10(10, 120, "较重权重请求，如复杂查询等"),
    
    /**
     * 权重20 - 重权重请求
     * 每分钟限制60次
     */
    WEIGHT_20(20, 60, "重权重请求，如批量操作等"),
    
    /**
     * 权重50 - 最重权重请求
     * 每分钟限制24次
     */
    WEIGHT_50(50, 24, "最重权重请求，如大量数据查询等");
    
    /**
     * 权重值
     */
    private final int weight;
    
    /**
     * 每分钟限制次数
     */
    private final int limitPerMinute;
    
    /**
     * 描述信息
     */
    private final String description;
    
    /**
     * 构造函数
     * 
     * @param weight 权重值
     * @param limitPerMinute 每分钟限制次数
     * @param description 描述信息
     */
    ApiRequestWeight(int weight, int limitPerMinute, String description) {
        this.weight = weight;
        this.limitPerMinute = limitPerMinute;
        this.description = description;
    }
    
    /**
     * 获取权重值
     * 
     * @return 权重值
     */
    public int getWeight() {
        return weight;
    }
    
    /**
     * 获取每分钟限制次数
     * 
     * @return 每分钟限制次数
     */
    public int getLimitPerMinute() {
        return limitPerMinute;
    }
    
    /**
     * 获取描述信息
     * 
     * @return 描述信息
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据权重值获取对应的枚举
     * 
     * @param weight 权重值
     * @return 对应的枚举，如果没有找到则返回WEIGHT_1
     */
    public static ApiRequestWeight fromWeight(int weight) {
        for (ApiRequestWeight requestWeight : values()) {
            if (requestWeight.weight == weight) {
                return requestWeight;
            }
        }
        return WEIGHT_1; // 默认返回最小权重
    }
    
    /**
     * 判断是否为高权重请求
     * 
     * @return 如果权重大于等于20则为高权重请求
     */
    public boolean isHighWeight() {
        return weight >= 20;
    }
    
    @Override
    public String toString() {
        return String.format("ApiRequestWeight{weight=%d, limitPerMinute=%d, description='%s'}", 
                           weight, limitPerMinute, description);
    }
}
