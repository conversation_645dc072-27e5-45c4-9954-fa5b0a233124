package com.trading.common.enums;

/**
 * 数据类型枚举
 * 定义系统中处理的各种数据类型
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum DataType {
    
    // ==================== 市场数据类型 ====================
    
    /**
     * K线数据
     */
    KLINE("KLINE", "K线数据", "包含开高低收量的K线数据", "market_data"),
    
    /**
     * 深度数据
     */
    DEPTH("DEPTH", "深度数据", "买卖盘深度数据", "market_data"),
    
    /**
     * 交易数据
     */
    TRADE("TRADE", "交易数据", "实时成交数据", "market_data"),
    
    /**
     * 聚合交易数据
     */
    AGG_TRADE("AGG_TRADE", "聚合交易数据", "聚合的成交数据", "market_data"),
    
    /**
     * 24小时统计数据
     */
    TICKER_24HR("TICKER_24HR", "24小时统计", "24小时价格变动统计", "market_data"),
    
    /**
     * 最优挂单数据
     */
    BOOK_TICKER("BOOK_TICKER", "最优挂单", "最优买卖挂单价格", "market_data"),
    
    /**
     * 标记价格数据
     */
    MARK_PRICE("MARK_PRICE", "标记价格", "期货合约标记价格", "market_data"),
    
    /**
     * 资金费率数据
     */
    FUNDING_RATE("FUNDING_RATE", "资金费率", "期货合约资金费率", "market_data"),
    
    /**
     * 强制平仓订单数据
     */
    FORCE_ORDER("FORCE_ORDER", "强制平仓订单", "强制平仓订单信息", "market_data"),
    
    // ==================== 交易数据类型 ====================
    
    /**
     * 订单数据
     */
    ORDER("ORDER", "订单数据", "交易订单信息", "trading_data"),
    
    /**
     * 成交数据
     */
    FILL("FILL", "成交数据", "订单成交信息", "trading_data"),
    
    /**
     * 持仓数据
     */
    POSITION("POSITION", "持仓数据", "账户持仓信息", "trading_data"),
    
    /**
     * 账户数据
     */
    ACCOUNT("ACCOUNT", "账户数据", "账户余额和信息", "trading_data"),
    
    /**
     * 收入数据
     */
    INCOME("INCOME", "收入数据", "账户收入历史", "trading_data"),
    
    // ==================== 用户数据类型 ====================
    
    /**
     * 用户数据流
     */
    USER_DATA("USER_DATA", "用户数据流", "用户相关的实时数据", "user_data"),
    
    /**
     * 账户更新
     */
    ACCOUNT_UPDATE("ACCOUNT_UPDATE", "账户更新", "账户信息更新", "user_data"),
    
    /**
     * 订单更新
     */
    ORDER_UPDATE("ORDER_UPDATE", "订单更新", "订单状态更新", "user_data"),
    
    /**
     * 持仓更新
     */
    POSITION_UPDATE("POSITION_UPDATE", "持仓更新", "持仓信息更新", "user_data"),
    
    // ==================== 特征数据类型 ====================
    
    /**
     * 技术指标
     */
    TECHNICAL_INDICATOR("TECHNICAL_INDICATOR", "技术指标", "技术分析指标数据", "feature_data"),
    
    /**
     * 基础特征
     */
    BASIC_FEATURE("BASIC_FEATURE", "基础特征", "基础的价格和成交量特征", "feature_data"),
    
    /**
     * 衍生特征
     */
    DERIVED_FEATURE("DERIVED_FEATURE", "衍生特征", "从基础数据衍生的特征", "feature_data"),
    
    /**
     * 标签数据
     */
    LABEL("LABEL", "标签数据", "机器学习的标签数据", "feature_data"),
    
    // ==================== 系统数据类型 ====================
    
    /**
     * 配置数据
     */
    CONFIG("CONFIG", "配置数据", "系统配置信息", "system_data"),
    
    /**
     * 日志数据
     */
    LOG("LOG", "日志数据", "系统日志信息", "system_data"),
    
    /**
     * 监控数据
     */
    METRICS("METRICS", "监控数据", "系统监控指标", "system_data"),
    
    /**
     * 错误数据
     */
    ERROR("ERROR", "错误数据", "系统错误信息", "system_data"),
    
    // ==================== 分析数据类型 ====================
    
    /**
     * 统计数据
     */
    STATISTICS("STATISTICS", "统计数据", "数据统计分析结果", "analysis_data"),
    
    /**
     * 预测数据
     */
    PREDICTION("PREDICTION", "预测数据", "模型预测结果", "analysis_data"),
    
    /**
     * 回测数据
     */
    BACKTEST("BACKTEST", "回测数据", "策略回测结果", "analysis_data"),
    
    /**
     * 风险数据
     */
    RISK("RISK", "风险数据", "风险评估数据", "analysis_data");
    
    private final String code;
    private final String name;
    private final String description;
    private final String category;
    
    /**
     * 构造函数
     * 
     * @param code 数据类型代码
     * @param name 数据类型名称
     * @param description 数据类型描述
     * @param category 数据类型类别
     */
    DataType(String code, String name, String description, String category) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.category = category;
    }
    
    /**
     * 获取数据类型代码
     * 
     * @return 数据类型代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取数据类型名称
     * 
     * @return 数据类型名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取数据类型描述
     * 
     * @return 数据类型描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取数据类型类别
     * 
     * @return 数据类型类别
     */
    public String getCategory() {
        return category;
    }
    
    /**
     * 根据代码查找枚举
     * 
     * @param code 数据类型代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static DataType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (DataType dataType : values()) {
            if (dataType.code.equals(code.trim())) {
                return dataType;
            }
        }
        return null;
    }
    
    /**
     * 根据类别获取数据类型列表
     * 
     * @param category 数据类型类别
     * @return 该类别下的所有数据类型
     */
    public static DataType[] getByCategory(String category) {
        if (category == null || category.trim().isEmpty()) {
            return new DataType[0];
        }
        
        return java.util.Arrays.stream(values())
                .filter(type -> type.category.equals(category.trim()))
                .toArray(DataType[]::new);
    }
    
    /**
     * 检查是否为市场数据
     * 
     * @return 如果是市场数据返回true，否则返回false
     */
    public boolean isMarketData() {
        return "market_data".equals(category);
    }
    
    /**
     * 检查是否为交易数据
     * 
     * @return 如果是交易数据返回true，否则返回false
     */
    public boolean isTradingData() {
        return "trading_data".equals(category);
    }
    
    /**
     * 检查是否为用户数据
     * 
     * @return 如果是用户数据返回true，否则返回false
     */
    public boolean isUserData() {
        return "user_data".equals(category);
    }
    
    /**
     * 检查是否为特征数据
     * 
     * @return 如果是特征数据返回true，否则返回false
     */
    public boolean isFeatureData() {
        return "feature_data".equals(category);
    }
    
    /**
     * 检查是否为系统数据
     * 
     * @return 如果是系统数据返回true，否则返回false
     */
    public boolean isSystemData() {
        return "system_data".equals(category);
    }
    
    /**
     * 检查是否为分析数据
     * 
     * @return 如果是分析数据返回true，否则返回false
     */
    public boolean isAnalysisData() {
        return "analysis_data".equals(category);
    }
    
    /**
     * 检查是否为实时数据
     * 
     * @return 如果是实时数据返回true，否则返回false
     */
    public boolean isRealTimeData() {
        return this == TRADE || this == AGG_TRADE || this == DEPTH || 
               this == BOOK_TICKER || this == USER_DATA || 
               this == ACCOUNT_UPDATE || this == ORDER_UPDATE || this == POSITION_UPDATE;
    }
    
    /**
     * 检查是否为历史数据
     * 
     * @return 如果是历史数据返回true，否则返回false
     */
    public boolean isHistoricalData() {
        return this == KLINE || this == INCOME || this == BACKTEST || 
               this == STATISTICS || this == LOG;
    }
    
    @Override
    public String toString() {
        return String.format("%s: %s (%s)", code, name, description);
    }
}
