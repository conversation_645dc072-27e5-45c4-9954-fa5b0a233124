package com.trading.common.enums;

/**
 * 数据处理器类型枚举
 * 定义数据处理和特征工程中使用的处理器类型
 * 基于qlib框架的处理器定义
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum ProcessorType {
    
    // ==================== 数据清洗处理器 ====================
    
    /**
     * 填充缺失值处理器
     */
    FILLNA("Fillna", "填充缺失值", "使用指定方法填充数据中的缺失值", "data_cleaning"),
    
    /**
     * 删除缺失标签处理器
     */
    DROPNA_LABEL("DropnaLabel", "删除缺失标签", "删除标签为空的数据行", "data_cleaning"),
    
    /**
     * 删除缺失特征处理器
     */
    DROPNA_FEATURE("DropnaFeature", "删除缺失特征", "删除特征为空的数据行", "data_cleaning"),
    
    /**
     * 过滤异常值处理器
     */
    FILTER_OUTLIER("FilterOutlier", "过滤异常值", "过滤掉统计意义上的异常值", "data_cleaning"),
    
    // ==================== 数据标准化处理器 ====================
    
    /**
     * Z-Score标准化处理器
     */
    ZSCORE_NORM("ZScoreNorm", "Z-Score标准化", "使用均值和标准差进行标准化", "normalization"),
    
    /**
     * 鲁棒Z-Score标准化处理器
     */
    ROBUST_ZSCORE_NORM("RobustZScoreNorm", "鲁棒Z-Score标准化", "使用中位数和MAD进行鲁棒标准化", "normalization"),
    
    /**
     * 最小-最大标准化处理器
     */
    MINMAX_NORM("MinMaxNorm", "最小-最大标准化", "将数据缩放到指定范围", "normalization"),
    
    /**
     * 截面排序标准化处理器
     */
    CSRANK_NORM("CSRankNorm", "截面排序标准化", "在截面上进行排序标准化", "normalization"),
    
    /**
     * 截面Z-Score标准化处理器
     */
    CSZSCORE_NORM("CSZScoreNorm", "截面Z-Score标准化", "在截面上进行Z-Score标准化", "normalization"),
    
    // ==================== 特征工程处理器 ====================
    
    /**
     * 技术指标计算处理器
     */
    TECHNICAL_INDICATOR("TechnicalIndicator", "技术指标计算", "计算各种技术分析指标", "feature_engineering"),
    
    /**
     * 滚动窗口统计处理器
     */
    ROLLING_STATS("RollingStats", "滚动窗口统计", "计算滚动窗口的统计指标", "feature_engineering"),
    
    /**
     * 价格变化处理器
     */
    PRICE_CHANGE("PriceChange", "价格变化", "计算价格变化和收益率", "feature_engineering"),
    
    /**
     * 成交量指标处理器
     */
    VOLUME_INDICATOR("VolumeIndicator", "成交量指标", "计算成交量相关指标", "feature_engineering"),
    
    /**
     * 波动率指标处理器
     */
    VOLATILITY_INDICATOR("VolatilityIndicator", "波动率指标", "计算各种波动率指标", "feature_engineering"),
    
    // ==================== 数据转换处理器 ====================
    
    /**
     * 对数变换处理器
     */
    LOG_TRANSFORM("LogTransform", "对数变换", "对数据进行对数变换", "transformation"),
    
    /**
     * 差分变换处理器
     */
    DIFF_TRANSFORM("DiffTransform", "差分变换", "对数据进行差分变换", "transformation"),
    
    /**
     * 百分比变换处理器
     */
    PCT_TRANSFORM("PctTransform", "百分比变换", "计算百分比变化", "transformation"),
    
    /**
     * 标准化变换处理器
     */
    STANDARDIZE_TRANSFORM("StandardizeTransform", "标准化变换", "标准化数据分布", "transformation"),
    
    // ==================== 时间序列处理器 ====================
    
    /**
     * 时间窗口处理器
     */
    TIME_WINDOW("TimeWindow", "时间窗口", "创建时间窗口特征", "time_series"),
    
    /**
     * 滞后特征处理器
     */
    LAG_FEATURE("LagFeature", "滞后特征", "创建滞后特征", "time_series"),
    
    /**
     * 移动平均处理器
     */
    MOVING_AVERAGE("MovingAverage", "移动平均", "计算移动平均值", "time_series"),
    
    /**
     * 指数移动平均处理器
     */
    EMA("ExponentialMovingAverage", "指数移动平均", "计算指数移动平均值", "time_series"),
    
    // ==================== 数据验证处理器 ====================
    
    /**
     * 数据质量检查处理器
     */
    DATA_QUALITY_CHECK("DataQualityCheck", "数据质量检查", "检查数据质量和完整性", "validation"),
    
    /**
     * 数据一致性检查处理器
     */
    CONSISTENCY_CHECK("ConsistencyCheck", "数据一致性检查", "检查数据的一致性", "validation"),
    
    /**
     * 数据范围检查处理器
     */
    RANGE_CHECK("RangeCheck", "数据范围检查", "检查数据是否在合理范围内", "validation");
    
    private final String code;
    private final String name;
    private final String description;
    private final String category;
    
    /**
     * 构造函数
     * 
     * @param code 处理器代码
     * @param name 处理器名称
     * @param description 处理器描述
     * @param category 处理器类别
     */
    ProcessorType(String code, String name, String description, String category) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.category = category;
    }
    
    /**
     * 获取处理器代码
     * 
     * @return 处理器代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取处理器名称
     * 
     * @return 处理器名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取处理器描述
     * 
     * @return 处理器描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取处理器类别
     * 
     * @return 处理器类别
     */
    public String getCategory() {
        return category;
    }
    
    /**
     * 根据代码查找枚举
     * 
     * @param code 处理器代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static ProcessorType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (ProcessorType processorType : values()) {
            if (processorType.code.equals(code.trim())) {
                return processorType;
            }
        }
        return null;
    }
    
    /**
     * 根据类别获取处理器列表
     * 
     * @param category 处理器类别
     * @return 该类别下的所有处理器
     */
    public static ProcessorType[] getByCategory(String category) {
        if (category == null || category.trim().isEmpty()) {
            return new ProcessorType[0];
        }
        
        return java.util.Arrays.stream(values())
                .filter(type -> type.category.equals(category.trim()))
                .toArray(ProcessorType[]::new);
    }
    
    /**
     * 检查是否为数据清洗处理器
     * 
     * @return 如果是数据清洗处理器返回true，否则返回false
     */
    public boolean isDataCleaning() {
        return "data_cleaning".equals(category);
    }
    
    /**
     * 检查是否为标准化处理器
     * 
     * @return 如果是标准化处理器返回true，否则返回false
     */
    public boolean isNormalization() {
        return "normalization".equals(category);
    }
    
    /**
     * 检查是否为特征工程处理器
     * 
     * @return 如果是特征工程处理器返回true，否则返回false
     */
    public boolean isFeatureEngineering() {
        return "feature_engineering".equals(category);
    }
    
    /**
     * 检查是否为数据转换处理器
     * 
     * @return 如果是数据转换处理器返回true，否则返回false
     */
    public boolean isTransformation() {
        return "transformation".equals(category);
    }
    
    /**
     * 检查是否为时间序列处理器
     * 
     * @return 如果是时间序列处理器返回true，否则返回false
     */
    public boolean isTimeSeries() {
        return "time_series".equals(category);
    }
    
    /**
     * 检查是否为数据验证处理器
     * 
     * @return 如果是数据验证处理器返回true，否则返回false
     */
    public boolean isValidation() {
        return "validation".equals(category);
    }
    
    @Override
    public String toString() {
        return String.format("%s: %s (%s)", code, name, description);
    }
}
