package com.trading.common.enums;

/**
 * 持仓方向枚举
 * 定义期货交易中的持仓方向
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum PositionSide {
    
    /**
     * 多头持仓 - 看涨，买入开仓
     */
    LONG("LONG", "多头", "看涨持仓，买入开仓", 1),
    
    /**
     * 空头持仓 - 看跌，卖出开仓
     */
    SHORT("SHORT", "空头", "看跌持仓，卖出开仓", -1),
    
    /**
     * 双向持仓 - 同时持有多头和空头
     */
    BOTH("BOTH", "双向", "同时持有多头和空头持仓", 0);
    
    /**
     * 方向码
     */
    private final String code;
    
    /**
     * 方向名称
     */
    private final String name;
    
    /**
     * 方向描述
     */
    private final String description;
    
    /**
     * 方向系数（多头为1，空头为-1，双向为0）
     */
    private final int coefficient;
    
    /**
     * 构造函数
     * 
     * @param code 方向码
     * @param name 方向名称
     * @param description 方向描述
     * @param coefficient 方向系数
     */
    PositionSide(String code, String name, String description, int coefficient) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.coefficient = coefficient;
    }
    
    /**
     * 获取方向码
     * 
     * @return 方向码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取方向名称
     * 
     * @return 方向名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取方向描述
     * 
     * @return 方向描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取方向系数
     * 
     * @return 方向系数
     */
    public int getCoefficient() {
        return coefficient;
    }
    
    /**
     * 根据方向码获取枚举值
     * 
     * @param code 方向码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static PositionSide fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (PositionSide side : values()) {
            if (side.code.equalsIgnoreCase(code.trim())) {
                return side;
            }
        }
        
        return null;
    }
    
    /**
     * 根据方向系数获取枚举值
     * 
     * @param coefficient 方向系数
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static PositionSide fromCoefficient(int coefficient) {
        for (PositionSide side : values()) {
            if (side.coefficient == coefficient) {
                return side;
            }
        }
        return null;
    }
    
    /**
     * 检查是否为多头持仓
     * 
     * @return true表示多头，false表示非多头
     */
    public boolean isLong() {
        return this == LONG;
    }
    
    /**
     * 检查是否为空头持仓
     * 
     * @return true表示空头，false表示非空头
     */
    public boolean isShort() {
        return this == SHORT;
    }
    
    /**
     * 检查是否为双向持仓
     * 
     * @return true表示双向，false表示单向
     */
    public boolean isBoth() {
        return this == BOTH;
    }
    
    /**
     * 检查是否为单向持仓
     * 
     * @return true表示单向，false表示双向
     */
    public boolean isSingleSide() {
        return this == LONG || this == SHORT;
    }
    
    /**
     * 获取相反方向
     * 
     * @return 相反方向的枚举值，双向持仓返回自身
     */
    public PositionSide getOpposite() {
        switch (this) {
            case LONG:
                return SHORT;
            case SHORT:
                return LONG;
            case BOTH:
            default:
                return BOTH;
        }
    }
    
    /**
     * 根据订单方向获取对应的持仓方向
     * 
     * @param orderSide 订单方向
     * @return 对应的持仓方向
     */
    public static PositionSide fromOrderSide(OrderSide orderSide) {
        if (orderSide == null) {
            return null;
        }
        
        switch (orderSide) {
            case BUY:
                return LONG;
            case SELL:
                return SHORT;
            default:
                return null;
        }
    }
    
    /**
     * 获取开仓对应的订单方向
     * 
     * @return 开仓订单方向
     */
    public OrderSide getOpenOrderSide() {
        switch (this) {
            case LONG:
                return OrderSide.BUY;
            case SHORT:
                return OrderSide.SELL;
            case BOTH:
            default:
                return null;
        }
    }
    
    /**
     * 获取平仓对应的订单方向
     * 
     * @return 平仓订单方向
     */
    public OrderSide getCloseOrderSide() {
        switch (this) {
            case LONG:
                return OrderSide.SELL;
            case SHORT:
                return OrderSide.BUY;
            case BOTH:
            default:
                return null;
        }
    }
    
    /**
     * 获取方向对应的颜色（用于UI显示）
     * 
     * @return 颜色代码
     */
    public String getColor() {
        switch (this) {
            case LONG:
                return "#52c41a"; // 绿色
            case SHORT:
                return "#ff4d4f"; // 红色
            case BOTH:
                return "#faad14"; // 橙色
            default:
                return "#8c8c8c"; // 灰色
        }
    }
    
    /**
     * 获取方向对应的图标（用于UI显示）
     * 
     * @return 图标名称
     */
    public String getIcon() {
        switch (this) {
            case LONG:
                return "arrow-up";
            case SHORT:
                return "arrow-down";
            case BOTH:
                return "swap";
            default:
                return "minus";
        }
    }
    
    /**
     * 获取方向的风险等级
     * 
     * @return 风险等级（1-5，数值越大风险越高）
     */
    public int getRiskLevel() {
        switch (this) {
            case LONG:
                return 3; // 中等风险
            case SHORT:
                return 4; // 较高风险（无限亏损可能）
            case BOTH:
                return 5; // 最高风险（复杂管理）
            default:
                return 1;
        }
    }
    
    /**
     * 获取方向的保证金系数
     * 
     * @return 保证金系数
     */
    public double getMarginCoefficient() {
        switch (this) {
            case LONG:
            case SHORT:
                return 1.0; // 单向持仓标准保证金
            case BOTH:
                return 1.5; // 双向持仓需要更多保证金
            default:
                return 1.0;
        }
    }
    
    @Override
    public String toString() {
        return String.format("PositionSide{code='%s', name='%s', description='%s', coefficient=%d}", 
                code, name, description, coefficient);
    }
}
