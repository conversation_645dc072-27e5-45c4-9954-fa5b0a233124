package com.trading.common.enums;

/**
 * 错误代码枚举
 * 定义系统中的各种错误代码和错误信息
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum ErrorCode {
    
    // ==================== 系统错误 (1000-1999) ====================
    
    /**
     * 系统内部错误
     */
    SYSTEM_ERROR(1000, "系统内部错误", "系统发生未知错误"),
    
    /**
     * 配置错误
     */
    CONFIG_ERROR(1001, "配置错误", "系统配置不正确"),
    
    /**
     * 初始化错误
     */
    INITIALIZATION_ERROR(1002, "初始化错误", "系统初始化失败"),
    
    /**
     * 服务不可用
     */
    SERVICE_UNAVAILABLE(1003, "服务不可用", "服务暂时不可用"),
    
    /**
     * 超时错误
     */
    TIMEOUT_ERROR(1004, "超时错误", "操作超时"),
    
    /**
     * 资源不足
     */
    RESOURCE_EXHAUSTED(1005, "资源不足", "系统资源不足"),
    
    // ==================== 网络错误 (2000-2999) ====================
    
    /**
     * 网络连接错误
     */
    NETWORK_ERROR(2000, "网络连接错误", "网络连接失败"),
    
    /**
     * 连接超时
     */
    CONNECTION_TIMEOUT(2001, "连接超时", "网络连接超时"),
    
    /**
     * 读取超时
     */
    READ_TIMEOUT(2002, "读取超时", "网络读取超时"),
    
    /**
     * 写入超时
     */
    WRITE_TIMEOUT(2003, "写入超时", "网络写入超时"),
    
    /**
     * SSL错误
     */
    SSL_ERROR(2004, "SSL错误", "SSL连接错误"),
    
    /**
     * DNS解析错误
     */
    DNS_ERROR(2005, "DNS解析错误", "域名解析失败"),
    
    // ==================== API错误 (3000-3999) ====================
    
    /**
     * API调用失败
     */
    API_ERROR(3000, "API调用失败", "API接口调用失败"),
    
    /**
     * API限流
     */
    API_RATE_LIMIT(3001, "API限流", "API调用频率超限"),
    
    /**
     * API权限不足
     */
    API_PERMISSION_DENIED(3002, "API权限不足", "API权限验证失败"),
    
    /**
     * API密钥无效
     */
    API_KEY_INVALID(3003, "API密钥无效", "API密钥验证失败"),
    
    /**
     * API签名错误
     */
    API_SIGNATURE_ERROR(3004, "API签名错误", "API签名验证失败"),
    
    /**
     * API参数错误
     */
    API_PARAMETER_ERROR(3005, "API参数错误", "API参数不正确"),
    
    /**
     * API响应格式错误
     */
    API_RESPONSE_FORMAT_ERROR(3006, "API响应格式错误", "API响应数据格式不正确"),

    /**
     * SDK客户端创建错误
     */
    SDK_CLIENT_CREATE_ERROR(3007, "SDK客户端创建错误", "SDK客户端创建失败"),
    
    // ==================== 数据错误 (4000-4999) ====================
    
    /**
     * 数据不存在
     */
    DATA_NOT_FOUND(4000, "数据不存在", "请求的数据不存在"),
    
    /**
     * 数据格式错误
     */
    DATA_FORMAT_ERROR(4001, "数据格式错误", "数据格式不正确"),
    
    /**
     * 数据验证失败
     */
    DATA_VALIDATION_ERROR(4002, "数据验证失败", "数据验证不通过"),
    
    /**
     * 数据重复
     */
    DATA_DUPLICATE(4003, "数据重复", "数据已存在"),
    
    /**
     * 数据过期
     */
    DATA_EXPIRED(4004, "数据过期", "数据已过期"),
    
    /**
     * 数据损坏
     */
    DATA_CORRUPTED(4005, "数据损坏", "数据完整性检查失败"),
    
    // ==================== 数据库错误 (5000-5999) ====================
    
    /**
     * 数据库连接错误
     */
    DATABASE_CONNECTION_ERROR(5000, "数据库连接错误", "数据库连接失败"),
    
    /**
     * 数据库查询错误
     */
    DATABASE_QUERY_ERROR(5001, "数据库查询错误", "数据库查询失败"),
    
    /**
     * 数据库更新错误
     */
    DATABASE_UPDATE_ERROR(5002, "数据库更新错误", "数据库更新失败"),
    
    /**
     * 数据库事务错误
     */
    DATABASE_TRANSACTION_ERROR(5003, "数据库事务错误", "数据库事务处理失败"),
    
    /**
     * 数据库锁定错误
     */
    DATABASE_LOCK_ERROR(5004, "数据库锁定错误", "数据库锁定失败"),
    
    /**
     * 数据库约束违反
     */
    DATABASE_CONSTRAINT_VIOLATION(5005, "数据库约束违反", "违反数据库约束条件"),
    
    // ==================== 交易错误 (6000-6999) ====================
    
    /**
     * 订单创建失败
     */
    ORDER_CREATE_FAILED(6000, "订单创建失败", "创建订单失败"),
    
    /**
     * 订单取消失败
     */
    ORDER_CANCEL_FAILED(6001, "订单取消失败", "取消订单失败"),
    
    /**
     * 订单不存在
     */
    ORDER_NOT_FOUND(6002, "订单不存在", "订单不存在"),
    
    /**
     * 订单状态错误
     */
    ORDER_STATUS_ERROR(6003, "订单状态错误", "订单状态不正确"),
    
    /**
     * 余额不足
     */
    INSUFFICIENT_BALANCE(6004, "余额不足", "账户余额不足"),
    
    /**
     * 持仓不足
     */
    INSUFFICIENT_POSITION(6005, "持仓不足", "持仓数量不足"),
    
    /**
     * 价格超出限制
     */
    PRICE_OUT_OF_RANGE(6006, "价格超出限制", "订单价格超出允许范围"),
    
    /**
     * 数量超出限制
     */
    QUANTITY_OUT_OF_RANGE(6007, "数量超出限制", "订单数量超出允许范围"),
    
    /**
     * 交易对不支持
     */
    SYMBOL_NOT_SUPPORTED(6008, "交易对不支持", "不支持的交易对"),
    
    /**
     * 市场关闭
     */
    MARKET_CLOSED(6009, "市场关闭", "市场暂时关闭"),
    
    // ==================== 认证错误 (7000-7999) ====================
    
    /**
     * 认证失败
     */
    AUTHENTICATION_FAILED(7000, "认证失败", "身份认证失败"),
    
    /**
     * 授权失败
     */
    AUTHORIZATION_FAILED(7001, "授权失败", "权限验证失败"),
    
    /**
     * 令牌无效
     */
    TOKEN_INVALID(7002, "令牌无效", "访问令牌无效"),
    
    /**
     * 令牌过期
     */
    TOKEN_EXPIRED(7003, "令牌过期", "访问令牌已过期"),
    
    /**
     * 会话过期
     */
    SESSION_EXPIRED(7004, "会话过期", "用户会话已过期"),
    
    // ==================== 业务逻辑错误 (8000-8999) ====================
    
    /**
     * 业务规则违反
     */
    BUSINESS_RULE_VIOLATION(8000, "业务规则违反", "违反业务规则"),
    
    /**
     * 操作不允许
     */
    OPERATION_NOT_ALLOWED(8001, "操作不允许", "当前状态下不允许此操作"),
    
    /**
     * 参数无效
     */
    INVALID_PARAMETER(8002, "参数无效", "输入参数无效"),
    
    /**
     * 状态冲突
     */
    STATE_CONFLICT(8003, "状态冲突", "状态冲突，无法执行操作"),
    
    /**
     * 依赖缺失
     */
    DEPENDENCY_MISSING(8004, "依赖缺失", "缺少必要的依赖条件");
    
    private final int code;
    private final String message;
    private final String description;
    
    /**
     * 构造函数
     * 
     * @param code 错误代码
     * @param message 错误消息
     * @param description 错误描述
     */
    ErrorCode(int code, String message, String description) {
        this.code = code;
        this.message = message;
        this.description = description;
    }
    
    /**
     * 获取错误代码
     * 
     * @return 错误代码
     */
    public int getCode() {
        return code;
    }
    
    /**
     * 获取错误消息
     * 
     * @return 错误消息
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * 获取错误描述
     * 
     * @return 错误描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据错误代码查找枚举
     * 
     * @param code 错误代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static ErrorCode fromCode(int code) {
        for (ErrorCode errorCode : values()) {
            if (errorCode.code == code) {
                return errorCode;
            }
        }
        return null;
    }
    
    /**
     * 检查是否为系统错误
     * 
     * @return 如果是系统错误返回true，否则返回false
     */
    public boolean isSystemError() {
        return code >= 1000 && code < 2000;
    }
    
    /**
     * 检查是否为网络错误
     * 
     * @return 如果是网络错误返回true，否则返回false
     */
    public boolean isNetworkError() {
        return code >= 2000 && code < 3000;
    }
    
    /**
     * 检查是否为API错误
     * 
     * @return 如果是API错误返回true，否则返回false
     */
    public boolean isApiError() {
        return code >= 3000 && code < 4000;
    }
    
    /**
     * 检查是否为数据错误
     * 
     * @return 如果是数据错误返回true，否则返回false
     */
    public boolean isDataError() {
        return code >= 4000 && code < 5000;
    }
    
    /**
     * 检查是否为数据库错误
     * 
     * @return 如果是数据库错误返回true，否则返回false
     */
    public boolean isDatabaseError() {
        return code >= 5000 && code < 6000;
    }
    
    /**
     * 检查是否为交易错误
     * 
     * @return 如果是交易错误返回true，否则返回false
     */
    public boolean isTradingError() {
        return code >= 6000 && code < 7000;
    }
    
    /**
     * 检查是否为认证错误
     * 
     * @return 如果是认证错误返回true，否则返回false
     */
    public boolean isAuthError() {
        return code >= 7000 && code < 8000;
    }
    
    /**
     * 检查是否为业务逻辑错误
     * 
     * @return 如果是业务逻辑错误返回true，否则返回false
     */
    public boolean isBusinessError() {
        return code >= 8000 && code < 9000;
    }
    
    @Override
    public String toString() {
        return String.format("[%d] %s: %s", code, message, description);
    }
}
