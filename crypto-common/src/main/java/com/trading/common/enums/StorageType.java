package com.trading.common.enums;

/**
 * 存储类型枚举
 * 定义系统中使用的各种存储类型
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum StorageType {
    
    // ==================== 关系型数据库 ====================
    
    /**
     * MySQL数据库
     */
    MYSQL("MYSQL", "MySQL数据库", "关系型数据库，用于存储业务数据", "relational_db"),
    
    /**
     * PostgreSQL数据库
     */
    POSTGRESQL("POSTGRESQL", "PostgreSQL数据库", "关系型数据库，支持复杂查询", "relational_db"),
    
    /**
     * Oracle数据库
     */
    ORACLE("ORACLE", "Oracle数据库", "企业级关系型数据库", "relational_db"),
    
    /**
     * SQL Server数据库
     */
    SQL_SERVER("SQL_SERVER", "SQL Server数据库", "微软关系型数据库", "relational_db"),
    
    // ==================== 时序数据库 ====================
    
    /**
     * InfluxDB时序数据库
     */
    INFLUXDB("INFLUXDB", "InfluxDB", "时序数据库，用于存储市场数据", "time_series_db"),
    
    /**
     * TimescaleDB时序数据库
     */
    TIMESCALEDB("TIMESCALEDB", "TimescaleDB", "基于PostgreSQL的时序数据库", "time_series_db"),
    
    /**
     * OpenTSDB时序数据库
     */
    OPENTSDB("OPENTSDB", "OpenTSDB", "分布式时序数据库", "time_series_db"),
    
    /**
     * Prometheus时序数据库
     */
    PROMETHEUS("PROMETHEUS", "Prometheus", "监控指标时序数据库", "time_series_db"),
    
    // ==================== NoSQL数据库 ====================
    
    /**
     * MongoDB文档数据库
     */
    MONGODB("MONGODB", "MongoDB", "文档型NoSQL数据库", "nosql_db"),
    
    /**
     * Cassandra列族数据库
     */
    CASSANDRA("CASSANDRA", "Cassandra", "列族型NoSQL数据库", "nosql_db"),
    
    /**
     * HBase列族数据库
     */
    HBASE("HBASE", "HBase", "基于Hadoop的列族数据库", "nosql_db"),
    
    /**
     * DynamoDB键值数据库
     */
    DYNAMODB("DYNAMODB", "DynamoDB", "AWS托管的NoSQL数据库", "nosql_db"),
    
    // ==================== 缓存存储 ====================
    
    /**
     * Redis内存数据库
     */
    REDIS("REDIS", "Redis", "内存键值数据库，用于缓存", "cache"),
    
    /**
     * Memcached缓存
     */
    MEMCACHED("MEMCACHED", "Memcached", "分布式内存缓存系统", "cache"),
    
    /**
     * Hazelcast内存网格
     */
    HAZELCAST("HAZELCAST", "Hazelcast", "分布式内存数据网格", "cache"),
    
    /**
     * Ehcache本地缓存
     */
    EHCACHE("EHCACHE", "Ehcache", "Java本地缓存框架", "cache"),
    
    // ==================== 消息队列 ====================
    
    /**
     * Kafka消息队列
     */
    KAFKA("KAFKA", "Kafka", "分布式流处理平台", "message_queue"),
    
    /**
     * RabbitMQ消息队列
     */
    RABBITMQ("RABBITMQ", "RabbitMQ", "高可靠消息队列", "message_queue"),
    
    /**
     * ActiveMQ消息队列
     */
    ACTIVEMQ("ACTIVEMQ", "ActiveMQ", "Java消息服务实现", "message_queue"),
    
    /**
     * RocketMQ消息队列
     */
    ROCKETMQ("ROCKETMQ", "RocketMQ", "阿里云分布式消息队列", "message_queue"),
    
    // ==================== 文件存储 ====================
    
    /**
     * 本地文件系统
     */
    LOCAL_FILE("LOCAL_FILE", "本地文件", "本地文件系统存储", "file_storage"),
    
    /**
     * HDFS分布式文件系统
     */
    HDFS("HDFS", "HDFS", "Hadoop分布式文件系统", "file_storage"),
    
    /**
     * Amazon S3对象存储
     */
    S3("S3", "Amazon S3", "AWS对象存储服务", "file_storage"),
    
    /**
     * 阿里云OSS对象存储
     */
    OSS("OSS", "阿里云OSS", "阿里云对象存储服务", "file_storage"),
    
    /**
     * MinIO对象存储
     */
    MINIO("MINIO", "MinIO", "开源对象存储服务", "file_storage"),
    
    // ==================== 搜索引擎 ====================
    
    /**
     * Elasticsearch搜索引擎
     */
    ELASTICSEARCH("ELASTICSEARCH", "Elasticsearch", "分布式搜索和分析引擎", "search_engine"),
    
    /**
     * Solr搜索引擎
     */
    SOLR("SOLR", "Solr", "Apache搜索平台", "search_engine"),
    
    // ==================== 内存存储 ====================
    
    /**
     * 内存存储
     */
    MEMORY("MEMORY", "内存存储", "直接存储在内存中", "memory"),
    
    /**
     * 堆外内存
     */
    OFF_HEAP("OFF_HEAP", "堆外内存", "Java堆外内存存储", "memory");
    
    private final String code;
    private final String name;
    private final String description;
    private final String category;
    
    /**
     * 构造函数
     * 
     * @param code 存储类型代码
     * @param name 存储类型名称
     * @param description 存储类型描述
     * @param category 存储类型类别
     */
    StorageType(String code, String name, String description, String category) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.category = category;
    }
    
    /**
     * 获取存储类型代码
     * 
     * @return 存储类型代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取存储类型名称
     * 
     * @return 存储类型名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取存储类型描述
     * 
     * @return 存储类型描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取存储类型类别
     * 
     * @return 存储类型类别
     */
    public String getCategory() {
        return category;
    }
    
    /**
     * 根据代码查找枚举
     * 
     * @param code 存储类型代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static StorageType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (StorageType storageType : values()) {
            if (storageType.code.equals(code.trim())) {
                return storageType;
            }
        }
        return null;
    }
    
    /**
     * 根据类别获取存储类型列表
     * 
     * @param category 存储类型类别
     * @return 该类别下的所有存储类型
     */
    public static StorageType[] getByCategory(String category) {
        if (category == null || category.trim().isEmpty()) {
            return new StorageType[0];
        }
        
        return java.util.Arrays.stream(values())
                .filter(type -> type.category.equals(category.trim()))
                .toArray(StorageType[]::new);
    }
    
    /**
     * 检查是否为关系型数据库
     * 
     * @return 如果是关系型数据库返回true，否则返回false
     */
    public boolean isRelationalDB() {
        return "relational_db".equals(category);
    }
    
    /**
     * 检查是否为时序数据库
     * 
     * @return 如果是时序数据库返回true，否则返回false
     */
    public boolean isTimeSeriesDB() {
        return "time_series_db".equals(category);
    }
    
    /**
     * 检查是否为NoSQL数据库
     * 
     * @return 如果是NoSQL数据库返回true，否则返回false
     */
    public boolean isNoSQLDB() {
        return "nosql_db".equals(category);
    }
    
    /**
     * 检查是否为缓存存储
     * 
     * @return 如果是缓存存储返回true，否则返回false
     */
    public boolean isCache() {
        return "cache".equals(category);
    }
    
    /**
     * 检查是否为消息队列
     * 
     * @return 如果是消息队列返回true，否则返回false
     */
    public boolean isMessageQueue() {
        return "message_queue".equals(category);
    }
    
    /**
     * 检查是否为文件存储
     * 
     * @return 如果是文件存储返回true，否则返回false
     */
    public boolean isFileStorage() {
        return "file_storage".equals(category);
    }
    
    /**
     * 检查是否为搜索引擎
     * 
     * @return 如果是搜索引擎返回true，否则返回false
     */
    public boolean isSearchEngine() {
        return "search_engine".equals(category);
    }
    
    /**
     * 检查是否为内存存储
     * 
     * @return 如果是内存存储返回true，否则返回false
     */
    public boolean isMemory() {
        return "memory".equals(category);
    }
    
    /**
     * 检查是否支持事务
     * 
     * @return 如果支持事务返回true，否则返回false
     */
    public boolean supportsTransaction() {
        return isRelationalDB() || this == MONGODB;
    }
    
    /**
     * 检查是否为分布式存储
     * 
     * @return 如果是分布式存储返回true，否则返回false
     */
    public boolean isDistributed() {
        return this == CASSANDRA || this == HBASE || this == HDFS || 
               this == KAFKA || this == ELASTICSEARCH || this == OPENTSDB;
    }
    
    @Override
    public String toString() {
        return String.format("%s: %s (%s)", code, name, description);
    }
}
