package com.trading.common.enums;

import java.time.Duration;

/**
 * 时间间隔枚举
 * 定义K线图和数据分析的时间间隔
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public enum TimeInterval {
    
    /**
     * 1秒
     */
    SECOND_1("1s", "1秒", Duration.ofSeconds(1), 1),
    
    /**
     * 1分钟
     */
    MINUTE_1("1m", "1分钟", Duration.ofMinutes(1), 60),
    
    /**
     * 3分钟
     */
    MINUTE_3("3m", "3分钟", Duration.ofMinutes(3), 180),
    
    /**
     * 5分钟
     */
    MINUTE_5("5m", "5分钟", Duration.ofMinutes(5), 300),
    
    /**
     * 15分钟
     */
    MINUTE_15("15m", "15分钟", Duration.ofMinutes(15), 900),
    
    /**
     * 30分钟
     */
    MINUTE_30("30m", "30分钟", Duration.ofMinutes(30), 1800),
    
    /**
     * 1小时
     */
    HOUR_1("1h", "1小时", Duration.ofHours(1), 3600),
    
    /**
     * 2小时
     */
    HOUR_2("2h", "2小时", Duration.ofHours(2), 7200),
    
    /**
     * 4小时
     */
    HOUR_4("4h", "4小时", Duration.ofHours(4), 14400),
    
    /**
     * 6小时
     */
    HOUR_6("6h", "6小时", Duration.ofHours(6), 21600),
    
    /**
     * 8小时
     */
    HOUR_8("8h", "8小时", Duration.ofHours(8), 28800),
    
    /**
     * 12小时
     */
    HOUR_12("12h", "12小时", Duration.ofHours(12), 43200),
    
    /**
     * 1天
     */
    DAY_1("1d", "1天", Duration.ofDays(1), 86400),
    
    /**
     * 3天
     */
    DAY_3("3d", "3天", Duration.ofDays(3), 259200),
    
    /**
     * 1周
     */
    WEEK_1("1w", "1周", Duration.ofDays(7), 604800),
    
    /**
     * 1月
     */
    MONTH_1("1M", "1月", Duration.ofDays(30), 2592000);
    
    /**
     * 间隔码
     */
    private final String code;
    
    /**
     * 间隔名称
     */
    private final String name;
    
    /**
     * 时间长度
     */
    private final Duration duration;
    
    /**
     * 秒数
     */
    private final long seconds;
    
    /**
     * 构造函数
     * 
     * @param code 间隔码
     * @param name 间隔名称
     * @param duration 时间长度
     * @param seconds 秒数
     */
    TimeInterval(String code, String name, Duration duration, long seconds) {
        this.code = code;
        this.name = name;
        this.duration = duration;
        this.seconds = seconds;
    }
    
    /**
     * 获取间隔码
     * 
     * @return 间隔码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取间隔名称
     * 
     * @return 间隔名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取时间长度
     * 
     * @return 时间长度
     */
    public Duration getDuration() {
        return duration;
    }
    
    /**
     * 获取秒数
     * 
     * @return 秒数
     */
    public long getSeconds() {
        return seconds;
    }
    
    /**
     * 获取毫秒数
     * 
     * @return 毫秒数
     */
    public long getMilliseconds() {
        return seconds * 1000;
    }
    
    /**
     * 根据间隔码获取枚举值
     * 
     * @param code 间隔码
     * @return 对应的枚举值，如果找不到则返回null
     */
    public static TimeInterval fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (TimeInterval interval : values()) {
            if (interval.code.equalsIgnoreCase(code.trim())) {
                return interval;
            }
        }
        
        return null;
    }
    
    /**
     * 根据秒数获取最接近的时间间隔
     * 
     * @param seconds 秒数
     * @return 最接近的时间间隔
     */
    public static TimeInterval fromSeconds(long seconds) {
        TimeInterval closest = MINUTE_1;
        long minDiff = Math.abs(seconds - closest.seconds);
        
        for (TimeInterval interval : values()) {
            long diff = Math.abs(seconds - interval.seconds);
            if (diff < minDiff) {
                minDiff = diff;
                closest = interval;
            }
        }
        
        return closest;
    }
    
    /**
     * 检查是否为秒级间隔
     * 
     * @return true表示秒级，false表示非秒级
     */
    public boolean isSecondLevel() {
        return this == SECOND_1;
    }
    
    /**
     * 检查是否为分钟级间隔
     * 
     * @return true表示分钟级，false表示非分钟级
     */
    public boolean isMinuteLevel() {
        return this == MINUTE_1 || this == MINUTE_3 || this == MINUTE_5 || 
               this == MINUTE_15 || this == MINUTE_30;
    }
    
    /**
     * 检查是否为小时级间隔
     * 
     * @return true表示小时级，false表示非小时级
     */
    public boolean isHourLevel() {
        return this == HOUR_1 || this == HOUR_2 || this == HOUR_4 || 
               this == HOUR_6 || this == HOUR_8 || this == HOUR_12;
    }
    
    /**
     * 检查是否为天级间隔
     * 
     * @return true表示天级，false表示非天级
     */
    public boolean isDayLevel() {
        return this == DAY_1 || this == DAY_3;
    }
    
    /**
     * 检查是否为周级或月级间隔
     * 
     * @return true表示周级或月级，false表示其他
     */
    public boolean isWeekOrMonthLevel() {
        return this == WEEK_1 || this == MONTH_1;
    }
    
    /**
     * 检查是否为高频间隔（1小时以内）
     * 
     * @return true表示高频，false表示低频
     */
    public boolean isHighFrequency() {
        return seconds <= HOUR_1.seconds;
    }
    
    /**
     * 检查是否为低频间隔（1天以上）
     * 
     * @return true表示低频，false表示高频
     */
    public boolean isLowFrequency() {
        return seconds >= DAY_1.seconds;
    }
    
    /**
     * 获取下一个更大的时间间隔
     * 
     * @return 下一个更大的时间间隔，如果已是最大则返回自身
     */
    public TimeInterval getNextLarger() {
        TimeInterval[] values = values();
        for (int i = 0; i < values.length - 1; i++) {
            if (values[i] == this) {
                return values[i + 1];
            }
        }
        return this; // 已是最大间隔
    }
    
    /**
     * 获取上一个更小的时间间隔
     * 
     * @return 上一个更小的时间间隔，如果已是最小则返回自身
     */
    public TimeInterval getNextSmaller() {
        TimeInterval[] values = values();
        for (int i = 1; i < values.length; i++) {
            if (values[i] == this) {
                return values[i - 1];
            }
        }
        return this; // 已是最小间隔
    }
    
    /**
     * 计算在指定时间范围内有多少个此间隔
     * 
     * @param startTime 开始时间（毫秒）
     * @param endTime 结束时间（毫秒）
     * @return 间隔数量
     */
    public long countIntervalsInRange(long startTime, long endTime) {
        if (endTime <= startTime) {
            return 0;
        }
        return (endTime - startTime) / getMilliseconds();
    }
    
    /**
     * 获取间隔的优先级（用于排序）
     * 数值越小优先级越高
     * 
     * @return 优先级数值
     */
    public int getPriority() {
        return ordinal();
    }
    
    /**
     * 获取适合的数据点数量（用于图表显示）
     * 
     * @return 建议的数据点数量
     */
    public int getRecommendedDataPoints() {
        if (isSecondLevel()) {
            return 300; // 5分钟数据
        } else if (isMinuteLevel()) {
            return 200; // 根据间隔调整
        } else if (isHourLevel()) {
            return 168; // 一周数据
        } else if (isDayLevel()) {
            return 90; // 三个月数据
        } else {
            return 52; // 一年数据
        }
    }
    
    @Override
    public String toString() {
        return String.format("TimeInterval{code='%s', name='%s', seconds=%d}", 
                code, name, seconds);
    }
}
