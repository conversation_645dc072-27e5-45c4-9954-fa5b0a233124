package com.trading.common.enums;

/**
 * WebSocket事件类型枚举
 * 定义WebSocket连接过程中的各种事件类型
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum WebSocketEventType {
    
    /**
     * 连接打开事件
     */
    OPEN("OPEN", "连接打开", "WebSocket连接成功建立"),

    /**
     * 连接打开事件（别名）
     */
    CONNECTION_OPENED("CONNECTION_OPENED", "连接打开", "WebSocket连接成功建立"),

    /**
     * 连接关闭事件
     */
    CLOSE("CLOSE", "连接关闭", "WebSocket连接已关闭"),

    /**
     * 连接关闭事件（别名）
     */
    CONNECTION_CLOSED("CONNECTION_CLOSED", "连接关闭", "WebSocket连接已关闭"),

    /**
     * 连接失败事件
     */
    CONNECTION_FAILED("CONNECTION_FAILED", "连接失败", "WebSocket连接失败"),

    /**
     * 接收消息事件
     */
    MESSAGE("MESSAGE", "接收消息", "接收到WebSocket消息"),

    /**
     * 接收消息事件（别名）
     */
    MESSAGE_RECEIVED("MESSAGE_RECEIVED", "接收消息", "接收到WebSocket消息"),

    /**
     * 错误事件
     */
    ERROR("ERROR", "连接错误", "WebSocket连接发生错误"),

    /**
     * 错误发生事件（别名）
     */
    ERROR_OCCURRED("ERROR_OCCURRED", "错误发生", "WebSocket发生错误"),
    
    /**
     * Ping事件
     */
    PING("PING", "Ping消息", "发送Ping心跳消息"),

    /**
     * 心跳发送事件（别名）
     */
    HEARTBEAT_SENT("HEARTBEAT_SENT", "心跳发送", "发送心跳消息"),

    /**
     * Pong事件
     */
    PONG("PONG", "Pong消息", "接收到Pong响应消息"),

    /**
     * 心跳接收事件（别名）
     */
    HEARTBEAT_RECEIVED("HEARTBEAT_RECEIVED", "心跳接收", "接收到心跳响应"),
    
    /**
     * 重连事件
     */
    RECONNECT("RECONNECT", "重新连接", "WebSocket重新连接"),
    
    /**
     * 连接中断事件
     */
    DISCONNECT("DISCONNECT", "连接中断", "WebSocket连接中断"),
    
    /**
     * 订阅事件
     */
    SUBSCRIBE("SUBSCRIBE", "订阅数据流", "订阅WebSocket数据流"),
    
    /**
     * 取消订阅事件
     */
    UNSUBSCRIBE("UNSUBSCRIBE", "取消订阅", "取消订阅WebSocket数据流"),
    
    /**
     * 认证事件
     */
    AUTH("AUTH", "身份认证", "WebSocket身份认证"),
    
    /**
     * 认证成功事件
     */
    AUTH_SUCCESS("AUTH_SUCCESS", "认证成功", "WebSocket身份认证成功"),
    
    /**
     * 认证失败事件
     */
    AUTH_FAILED("AUTH_FAILED", "认证失败", "WebSocket身份认证失败"),
    
    /**
     * 数据流开始事件
     */
    STREAM_START("STREAM_START", "数据流开始", "WebSocket数据流开始"),
    
    /**
     * 数据流停止事件
     */
    STREAM_STOP("STREAM_STOP", "数据流停止", "WebSocket数据流停止"),
    
    /**
     * 超时事件
     */
    TIMEOUT("TIMEOUT", "连接超时", "WebSocket连接超时");
    
    private final String code;
    private final String name;
    private final String description;
    
    /**
     * 构造函数
     * 
     * @param code 事件代码
     * @param name 事件名称
     * @param description 事件描述
     */
    WebSocketEventType(String code, String name, String description) {
        this.code = code;
        this.name = name;
        this.description = description;
    }
    
    /**
     * 获取事件代码
     * 
     * @return 事件代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取事件名称
     * 
     * @return 事件名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取事件描述
     * 
     * @return 事件描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码查找枚举
     * 
     * @param code 事件代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static WebSocketEventType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        String upperCode = code.trim().toUpperCase();
        for (WebSocketEventType eventType : values()) {
            if (eventType.code.equals(upperCode)) {
                return eventType;
            }
        }
        return null;
    }
    
    /**
     * 检查是否为连接相关事件
     *
     * @return 如果是连接相关事件返回true，否则返回false
     */
    public boolean isConnectionEvent() {
        return this == OPEN || this == CONNECTION_OPENED ||
               this == CLOSE || this == CONNECTION_CLOSED || this == CONNECTION_FAILED ||
               this == RECONNECT || this == DISCONNECT;
    }

    /**
     * 检查是否为错误相关事件
     *
     * @return 如果是错误相关事件返回true，否则返回false
     */
    public boolean isErrorEvent() {
        return this == ERROR || this == ERROR_OCCURRED || this == CONNECTION_FAILED ||
               this == AUTH_FAILED || this == TIMEOUT;
    }

    /**
     * 检查是否为心跳相关事件
     *
     * @return 如果是心跳相关事件返回true，否则返回false
     */
    public boolean isHeartbeatEvent() {
        return this == PING || this == HEARTBEAT_SENT ||
               this == PONG || this == HEARTBEAT_RECEIVED;
    }
    
    /**
     * 检查是否为认证相关事件
     * 
     * @return 如果是认证相关事件返回true，否则返回false
     */
    public boolean isAuthEvent() {
        return this == AUTH || this == AUTH_SUCCESS || this == AUTH_FAILED;
    }
    
    /**
     * 检查是否为数据流相关事件
     * 
     * @return 如果是数据流相关事件返回true，否则返回false
     */
    public boolean isStreamEvent() {
        return this == SUBSCRIBE || this == UNSUBSCRIBE || this == STREAM_START || this == STREAM_STOP;
    }
    
    @Override
    public String toString() {
        return String.format("%s: %s (%s)", code, name, description);
    }
}
