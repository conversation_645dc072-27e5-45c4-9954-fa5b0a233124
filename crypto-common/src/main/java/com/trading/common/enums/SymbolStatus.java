package com.trading.common.enums;

/**
 * 交易对状态枚举
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum SymbolStatus {
    
    /**
     * 交易中
     */
    TRADING("TRADING", "交易中"),
    
    /**
     * 暂停交易
     */
    HALT("HALT", "暂停交易"),
    
    /**
     * 拍卖中
     */
    AUCTION_MATCH("AUCTION_MATCH", "拍卖中"),
    
    /**
     * 断路器
     */
    BREAK("BREAK", "断路器"),
    
    /**
     * 预交易
     */
    PRE_TRADING("PRE_TRADING", "预交易"),
    
    /**
     * 交易后
     */
    POST_TRADING("POST_TRADING", "交易后"),
    
    /**
     * 结束交易
     */
    END_OF_DAY("END_OF_DAY", "结束交易"),
    
    /**
     * 未知状态
     */
    UNKNOWN("UNKNOWN", "未知状态");
    
    private final String code;
    private final String description;
    
    SymbolStatus(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举值
     */
    public static SymbolStatus fromCode(String code) {
        for (SymbolStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return UNKNOWN;
    }
    
    /**
     * 检查是否可以交易
     */
    public boolean isTradable() {
        return this == TRADING || this == PRE_TRADING;
    }
    
    /**
     * 检查是否为暂停状态
     */
    public boolean isHalted() {
        return this == HALT || this == BREAK;
    }
}
