package com.trading.common.enums;

/**
 * 异常类型枚举
 * 定义系统中的各种异常类型
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum ExceptionType {
    
    // ==================== 系统异常 ====================
    
    /**
     * 系统异常
     */
    SYSTEM_EXCEPTION("SYSTEM_EXCEPTION", "系统异常", "系统内部异常", "system"),
    
    /**
     * 配置异常
     */
    CONFIG_EXCEPTION("CONFIG_EXCEPTION", "配置异常", "系统配置异常", "system"),
    
    /**
     * 初始化异常
     */
    INITIALIZATION_EXCEPTION("INITIALIZATION_EXCEPTION", "初始化异常", "系统初始化异常", "system"),
    
    /**
     * 资源异常
     */
    RESOURCE_EXCEPTION("RESOURCE_EXCEPTION", "资源异常", "系统资源异常", "system"),
    
    // ==================== 网络异常 ====================
    
    /**
     * 网络异常
     */
    NETWORK_EXCEPTION("NETWORK_EXCEPTION", "网络异常", "网络连接异常", "network"),
    
    /**
     * 连接异常
     */
    CONNECTION_EXCEPTION("CONNECTION_EXCEPTION", "连接异常", "网络连接异常", "network"),
    
    /**
     * 超时异常
     */
    TIMEOUT_EXCEPTION("TIMEOUT_EXCEPTION", "超时异常", "网络超时异常", "network"),
    
    /**
     * SSL异常
     */
    SSL_EXCEPTION("SSL_EXCEPTION", "SSL异常", "SSL连接异常", "network"),
    
    // ==================== API异常 ====================
    
    /**
     * API异常
     */
    API_EXCEPTION("API_EXCEPTION", "API异常", "API调用异常", "api"),
    
    /**
     * API限流异常
     */
    API_RATE_LIMIT_EXCEPTION("API_RATE_LIMIT_EXCEPTION", "API限流异常", "API调用频率超限异常", "api"),
    
    /**
     * API认证异常
     */
    API_AUTH_EXCEPTION("API_AUTH_EXCEPTION", "API认证异常", "API认证失败异常", "api"),
    
    /**
     * API参数异常
     */
    API_PARAMETER_EXCEPTION("API_PARAMETER_EXCEPTION", "API参数异常", "API参数错误异常", "api"),
    
    /**
     * API响应异常
     */
    API_RESPONSE_EXCEPTION("API_RESPONSE_EXCEPTION", "API响应异常", "API响应异常", "api"),
    
    // ==================== 数据异常 ====================
    
    /**
     * 数据异常
     */
    DATA_EXCEPTION("DATA_EXCEPTION", "数据异常", "数据处理异常", "data"),
    
    /**
     * 数据格式异常
     */
    DATA_FORMAT_EXCEPTION("DATA_FORMAT_EXCEPTION", "数据格式异常", "数据格式错误异常", "data"),
    
    /**
     * 数据验证异常
     */
    DATA_VALIDATION_EXCEPTION("DATA_VALIDATION_EXCEPTION", "数据验证异常", "数据验证失败异常", "data"),
    
    /**
     * 数据不存在异常
     */
    DATA_NOT_FOUND_EXCEPTION("DATA_NOT_FOUND_EXCEPTION", "数据不存在异常", "请求的数据不存在异常", "data"),
    
    /**
     * 数据重复异常
     */
    DATA_DUPLICATE_EXCEPTION("DATA_DUPLICATE_EXCEPTION", "数据重复异常", "数据重复异常", "data"),
    
    // ==================== 数据库异常 ====================
    
    /**
     * 数据库异常
     */
    DATABASE_EXCEPTION("DATABASE_EXCEPTION", "数据库异常", "数据库操作异常", "database"),
    
    /**
     * 数据库连接异常
     */
    DATABASE_CONNECTION_EXCEPTION("DATABASE_CONNECTION_EXCEPTION", "数据库连接异常", "数据库连接失败异常", "database"),
    
    /**
     * 数据库查询异常
     */
    DATABASE_QUERY_EXCEPTION("DATABASE_QUERY_EXCEPTION", "数据库查询异常", "数据库查询失败异常", "database"),
    
    /**
     * 数据库更新异常
     */
    DATABASE_UPDATE_EXCEPTION("DATABASE_UPDATE_EXCEPTION", "数据库更新异常", "数据库更新失败异常", "database"),
    
    /**
     * 数据库事务异常
     */
    DATABASE_TRANSACTION_EXCEPTION("DATABASE_TRANSACTION_EXCEPTION", "数据库事务异常", "数据库事务处理异常", "database"),
    
    // ==================== 交易异常 ====================
    
    /**
     * 交易异常
     */
    TRADING_EXCEPTION("TRADING_EXCEPTION", "交易异常", "交易操作异常", "trading"),
    
    /**
     * 订单异常
     */
    ORDER_EXCEPTION("ORDER_EXCEPTION", "订单异常", "订单操作异常", "trading"),
    
    /**
     * 余额不足异常
     */
    INSUFFICIENT_BALANCE_EXCEPTION("INSUFFICIENT_BALANCE_EXCEPTION", "余额不足异常", "账户余额不足异常", "trading"),
    
    /**
     * 持仓不足异常
     */
    INSUFFICIENT_POSITION_EXCEPTION("INSUFFICIENT_POSITION_EXCEPTION", "持仓不足异常", "持仓数量不足异常", "trading"),
    
    /**
     * 价格异常
     */
    PRICE_EXCEPTION("PRICE_EXCEPTION", "价格异常", "价格超出范围异常", "trading"),
    
    /**
     * 数量异常
     */
    QUANTITY_EXCEPTION("QUANTITY_EXCEPTION", "数量异常", "数量超出范围异常", "trading"),
    
    /**
     * 市场关闭异常
     */
    MARKET_CLOSED_EXCEPTION("MARKET_CLOSED_EXCEPTION", "市场关闭异常", "市场暂时关闭异常", "trading"),
    
    // ==================== 认证异常 ====================
    
    /**
     * 认证异常
     */
    AUTHENTICATION_EXCEPTION("AUTHENTICATION_EXCEPTION", "认证异常", "身份认证异常", "auth"),
    
    /**
     * 授权异常
     */
    AUTHORIZATION_EXCEPTION("AUTHORIZATION_EXCEPTION", "授权异常", "权限验证异常", "auth"),
    
    /**
     * 令牌异常
     */
    TOKEN_EXCEPTION("TOKEN_EXCEPTION", "令牌异常", "访问令牌异常", "auth"),
    
    /**
     * 会话异常
     */
    SESSION_EXCEPTION("SESSION_EXCEPTION", "会话异常", "用户会话异常", "auth"),
    
    // ==================== 业务异常 ====================
    
    /**
     * 业务异常
     */
    BUSINESS_EXCEPTION("BUSINESS_EXCEPTION", "业务异常", "业务逻辑异常", "business"),
    
    /**
     * 参数异常
     */
    PARAMETER_EXCEPTION("PARAMETER_EXCEPTION", "参数异常", "输入参数异常", "business"),
    
    /**
     * 状态异常
     */
    STATE_EXCEPTION("STATE_EXCEPTION", "状态异常", "状态冲突异常", "business"),
    
    /**
     * 操作异常
     */
    OPERATION_EXCEPTION("OPERATION_EXCEPTION", "操作异常", "操作不允许异常", "business"),
    
    // ==================== 并发异常 ====================
    
    /**
     * 并发异常
     */
    CONCURRENCY_EXCEPTION("CONCURRENCY_EXCEPTION", "并发异常", "并发操作异常", "concurrency"),
    
    /**
     * 锁异常
     */
    LOCK_EXCEPTION("LOCK_EXCEPTION", "锁异常", "锁获取失败异常", "concurrency"),
    
    /**
     * 死锁异常
     */
    DEADLOCK_EXCEPTION("DEADLOCK_EXCEPTION", "死锁异常", "死锁检测异常", "concurrency"),
    
    // ==================== 序列化异常 ====================
    
    /**
     * 序列化异常
     */
    SERIALIZATION_EXCEPTION("SERIALIZATION_EXCEPTION", "序列化异常", "数据序列化异常", "serialization"),
    
    /**
     * 反序列化异常
     */
    DESERIALIZATION_EXCEPTION("DESERIALIZATION_EXCEPTION", "反序列化异常", "数据反序列化异常", "serialization"),
    
    // ==================== 缓存异常 ====================
    
    /**
     * 缓存异常
     */
    CACHE_EXCEPTION("CACHE_EXCEPTION", "缓存异常", "缓存操作异常", "cache"),
    
    /**
     * 缓存连接异常
     */
    CACHE_CONNECTION_EXCEPTION("CACHE_CONNECTION_EXCEPTION", "缓存连接异常", "缓存连接失败异常", "cache"),
    
    // ==================== 消息队列异常 ====================
    
    /**
     * 消息队列异常
     */
    MESSAGE_QUEUE_EXCEPTION("MESSAGE_QUEUE_EXCEPTION", "消息队列异常", "消息队列操作异常", "mq"),
    
    /**
     * 消息发送异常
     */
    MESSAGE_SEND_EXCEPTION("MESSAGE_SEND_EXCEPTION", "消息发送异常", "消息发送失败异常", "mq"),
    
    /**
     * 消息接收异常
     */
    MESSAGE_RECEIVE_EXCEPTION("MESSAGE_RECEIVE_EXCEPTION", "消息接收异常", "消息接收失败异常", "mq");
    
    private final String code;
    private final String name;
    private final String description;
    private final String category;
    
    /**
     * 构造函数
     * 
     * @param code 异常类型代码
     * @param name 异常类型名称
     * @param description 异常类型描述
     * @param category 异常类型类别
     */
    ExceptionType(String code, String name, String description, String category) {
        this.code = code;
        this.name = name;
        this.description = description;
        this.category = category;
    }
    
    /**
     * 获取异常类型代码
     * 
     * @return 异常类型代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 获取异常类型名称
     * 
     * @return 异常类型名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 获取异常类型描述
     * 
     * @return 异常类型描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取异常类型类别
     * 
     * @return 异常类型类别
     */
    public String getCategory() {
        return category;
    }
    
    /**
     * 根据代码查找枚举
     * 
     * @param code 异常类型代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static ExceptionType fromCode(String code) {
        if (code == null || code.trim().isEmpty()) {
            return null;
        }
        
        for (ExceptionType exceptionType : values()) {
            if (exceptionType.code.equals(code.trim())) {
                return exceptionType;
            }
        }
        return null;
    }
    
    /**
     * 根据类别获取异常类型列表
     * 
     * @param category 异常类型类别
     * @return 该类别下的所有异常类型
     */
    public static ExceptionType[] getByCategory(String category) {
        if (category == null || category.trim().isEmpty()) {
            return new ExceptionType[0];
        }
        
        return java.util.Arrays.stream(values())
                .filter(type -> type.category.equals(category.trim()))
                .toArray(ExceptionType[]::new);
    }
    
    /**
     * 检查是否为系统异常
     * 
     * @return 如果是系统异常返回true，否则返回false
     */
    public boolean isSystemException() {
        return "system".equals(category);
    }
    
    /**
     * 检查是否为网络异常
     * 
     * @return 如果是网络异常返回true，否则返回false
     */
    public boolean isNetworkException() {
        return "network".equals(category);
    }
    
    /**
     * 检查是否为API异常
     * 
     * @return 如果是API异常返回true，否则返回false
     */
    public boolean isApiException() {
        return "api".equals(category);
    }
    
    /**
     * 检查是否为数据异常
     * 
     * @return 如果是数据异常返回true，否则返回false
     */
    public boolean isDataException() {
        return "data".equals(category);
    }
    
    /**
     * 检查是否为数据库异常
     * 
     * @return 如果是数据库异常返回true，否则返回false
     */
    public boolean isDatabaseException() {
        return "database".equals(category);
    }
    
    /**
     * 检查是否为交易异常
     * 
     * @return 如果是交易异常返回true，否则返回false
     */
    public boolean isTradingException() {
        return "trading".equals(category);
    }
    
    /**
     * 检查是否为认证异常
     * 
     * @return 如果是认证异常返回true，否则返回false
     */
    public boolean isAuthException() {
        return "auth".equals(category);
    }
    
    /**
     * 检查是否为业务异常
     * 
     * @return 如果是业务异常返回true，否则返回false
     */
    public boolean isBusinessException() {
        return "business".equals(category);
    }
    
    /**
     * 检查是否为并发异常
     * 
     * @return 如果是并发异常返回true，否则返回false
     */
    public boolean isConcurrencyException() {
        return "concurrency".equals(category);
    }
    
    @Override
    public String toString() {
        return String.format("%s: %s (%s)", code, name, description);
    }
}
