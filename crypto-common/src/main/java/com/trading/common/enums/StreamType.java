package com.trading.common.enums;

/**
 * WebSocket数据流类型枚举
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum StreamType {
    
    /**
     * USD-M期货
     */
    UM_FUTURES("UM_FUTURES", "USD-M期货"),
    
    /**
     * COIN-M期货
     */
    CM_FUTURES("CM_FUTURES", "COIN-M期货"),
    
    /**
     * K线数据
     */
    KLINE("KLINE", "K线数据"),
    
    /**
     * 深度数据
     */
    DEPTH("DEPTH", "深度数据"),
    
    /**
     * 交易数据
     */
    TRADE("TRADE", "交易数据"),
    
    /**
     * 聚合交易数据
     */
    AGG_TRADE("AGG_TRADE", "聚合交易数据"),
    
    /**
     * 24小时价格变动统计
     */
    TICKER("TICKER", "24小时价格变动统计"),
    
    /**
     * 最优挂单信息
     */
    BOOK_TICKER("BOOK_TICKER", "最优挂单信息"),
    
    /**
     * 用户数据流
     */
    USER_DATA("USER_DATA", "用户数据流"),
    
    /**
     * 强制平仓订单
     */
    FORCE_ORDER("FORCE_ORDER", "强制平仓订单"),
    
    /**
     * 合约信息
     */
    CONTRACT_INFO("CONTRACT_INFO", "合约信息"),

    /**
     * 标记价格
     */
    MARK_PRICE("MARK_PRICE", "标记价格"),

    /**
     * 资金费率
     */
    FUNDING_RATE("FUNDING_RATE", "资金费率"),

    /**
     * 综合指数
     */
    COMPOSITE_INDEX("COMPOSITE_INDEX", "综合指数"),

    /**
     * 全市场迷你统计
     */
    ALL_MINI_TICKER("ALL_MINI_TICKER", "全市场迷你统计"),

    /**
     * 全市场统计
     */
    ALL_TICKER("ALL_TICKER", "全市场统计"),

    /**
     * 连续合约K线
     */
    CONTINUOUS_KLINE("CONTINUOUS_KLINE", "连续合约K线"),

    /**
     * 指数K线
     */
    INDEX_KLINE("INDEX_KLINE", "指数K线"),

    /**
     * 标记价格K线
     */
    MARK_PRICE_KLINE("MARK_PRICE_KLINE", "标记价格K线");
    
    private final String code;
    private final String description;
    
    StreamType(String code, String description) {
        this.code = code;
        this.description = description;
    }
    
    public String getCode() {
        return code;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * 根据代码获取枚举值
     */
    public static StreamType fromCode(String code) {
        for (StreamType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        throw new IllegalArgumentException("未知的数据流类型: " + code);
    }
    
    /**
     * 检查是否为期货类型
     */
    public boolean isFuturesType() {
        return this == UM_FUTURES || this == CM_FUTURES;
    }
    
    /**
     * 检查是否为数据类型
     */
    public boolean isDataType() {
        return !isFuturesType();
    }
}
