package com.trading.common.enums;

/**
 * 数据频率枚举
 * 定义K线数据和市场数据的时间间隔
 * 基于币安API支持的时间间隔和qlib框架的频率定义
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public enum Frequency {
    
    /**
     * 1分钟
     */
    MIN_1("1m", "1分钟", 60, "1min"),
    
    /**
     * 3分钟
     */
    MIN_3("3m", "3分钟", 180, "3min"),
    
    /**
     * 5分钟
     */
    MIN_5("5m", "5分钟", 300, "5min"),
    
    /**
     * 15分钟
     */
    MIN_15("15m", "15分钟", 900, "15min"),
    
    /**
     * 30分钟
     */
    MIN_30("30m", "30分钟", 1800, "30min"),
    
    /**
     * 1小时
     */
    HOUR_1("1h", "1小时", 3600, "1h"),
    
    /**
     * 2小时
     */
    HOUR_2("2h", "2小时", 7200, "2h"),
    
    /**
     * 4小时
     */
    HOUR_4("4h", "4小时", 14400, "4h"),
    
    /**
     * 6小时
     */
    HOUR_6("6h", "6小时", 21600, "6h"),
    
    /**
     * 8小时
     */
    HOUR_8("8h", "8小时", 28800, "8h"),
    
    /**
     * 12小时
     */
    HOUR_12("12h", "12小时", 43200, "12h"),
    
    /**
     * 1天
     */
    DAY_1("1d", "1天", 86400, "day"),
    
    /**
     * 3天
     */
    DAY_3("3d", "3天", 259200, "3day"),
    
    /**
     * 1周
     */
    WEEK_1("1w", "1周", 604800, "week"),
    
    /**
     * 1月
     */
    MONTH_1("1M", "1月", 2592000, "month");
    
    private final String binanceCode;
    private final String description;
    private final long seconds;
    private final String qlibCode;
    
    /**
     * 构造函数
     * 
     * @param binanceCode 币安API代码
     * @param description 描述
     * @param seconds 秒数
     * @param qlibCode qlib框架代码
     */
    Frequency(String binanceCode, String description, long seconds, String qlibCode) {
        this.binanceCode = binanceCode;
        this.description = description;
        this.seconds = seconds;
        this.qlibCode = qlibCode;
    }
    
    /**
     * 获取币安API代码
     * 
     * @return 币安API代码
     */
    public String getBinanceCode() {
        return binanceCode;
    }
    
    /**
     * 获取描述
     * 
     * @return 描述
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * 获取秒数
     * 
     * @return 秒数
     */
    public long getSeconds() {
        return seconds;
    }
    
    /**
     * 获取毫秒数
     * 
     * @return 毫秒数
     */
    public long getMilliseconds() {
        return seconds * 1000;
    }
    
    /**
     * 获取qlib框架代码
     * 
     * @return qlib框架代码
     */
    public String getQlibCode() {
        return qlibCode;
    }
    
    /**
     * 根据币安代码查找枚举
     * 
     * @param binanceCode 币安API代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static Frequency fromBinanceCode(String binanceCode) {
        if (binanceCode == null || binanceCode.trim().isEmpty()) {
            return null;
        }
        
        for (Frequency frequency : values()) {
            if (frequency.binanceCode.equals(binanceCode.trim())) {
                return frequency;
            }
        }
        return null;
    }
    
    /**
     * 根据qlib代码查找枚举
     * 
     * @param qlibCode qlib框架代码
     * @return 对应的枚举，如果未找到返回null
     */
    public static Frequency fromQlibCode(String qlibCode) {
        if (qlibCode == null || qlibCode.trim().isEmpty()) {
            return null;
        }
        
        for (Frequency frequency : values()) {
            if (frequency.qlibCode.equals(qlibCode.trim())) {
                return frequency;
            }
        }
        return null;
    }
    
    /**
     * 检查是否为分钟级别频率
     * 
     * @return 如果是分钟级别频率返回true，否则返回false
     */
    public boolean isMinuteLevel() {
        return this.name().startsWith("MIN_");
    }
    
    /**
     * 检查是否为小时级别频率
     * 
     * @return 如果是小时级别频率返回true，否则返回false
     */
    public boolean isHourLevel() {
        return this.name().startsWith("HOUR_");
    }
    
    /**
     * 检查是否为天级别频率
     * 
     * @return 如果是天级别频率返回true，否则返回false
     */
    public boolean isDayLevel() {
        return this.name().startsWith("DAY_");
    }
    
    /**
     * 检查是否为周级别频率
     * 
     * @return 如果是周级别频率返回true，否则返回false
     */
    public boolean isWeekLevel() {
        return this == WEEK_1;
    }
    
    /**
     * 检查是否为月级别频率
     * 
     * @return 如果是月级别频率返回true，否则返回false
     */
    public boolean isMonthLevel() {
        return this == MONTH_1;
    }
    
    /**
     * 检查是否为高频数据（小于等于1小时）
     * 
     * @return 如果是高频数据返回true，否则返回false
     */
    public boolean isHighFrequency() {
        return seconds <= 3600;
    }
    
    /**
     * 检查是否为低频数据（大于1小时）
     * 
     * @return 如果是低频数据返回true，否则返回false
     */
    public boolean isLowFrequency() {
        return seconds > 3600;
    }
    
    @Override
    public String toString() {
        return String.format("%s (%s)", binanceCode, description);
    }
}
