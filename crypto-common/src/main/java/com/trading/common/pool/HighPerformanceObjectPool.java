package com.trading.common.pool;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Supplier;
import java.util.function.Consumer;

/**
 * 高性能对象池
 * 使用无锁队列实现的线程安全对象池，支持对象复用和性能监控
 * 
 * @param <T> 池化对象类型
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class HighPerformanceObjectPool<T> {

    private static final Logger log = LoggerFactory.getLogger(HighPerformanceObjectPool.class);

    private final String poolName;
    private final int maxPoolSize;
    private final Supplier<T> objectFactory;
    private final Consumer<T> resetFunction;
    private final Consumer<T> validateFunction;

    // 使用无锁队列存储对象
    private final ConcurrentLinkedQueue<T> pool = new ConcurrentLinkedQueue<>();

    // 性能统计 - 使用LongAdder避免竞争
    private final LongAdder borrowCount = new LongAdder();
    private final LongAdder returnCount = new LongAdder();
    private final LongAdder createCount = new LongAdder();
    private final LongAdder discardCount = new LongAdder();
    private final LongAdder validationFailures = new LongAdder();

    // 池状态
    private volatile boolean active = true;

    /**
     * 构造函数
     * 
     * @param poolName 池名称
     * @param maxPoolSize 最大池大小
     * @param objectFactory 对象工厂
     * @param resetFunction 对象重置函数
     * @param validateFunction 对象验证函数
     */
    public HighPerformanceObjectPool(String poolName, 
                                   int maxPoolSize,
                                   Supplier<T> objectFactory,
                                   Consumer<T> resetFunction,
                                   Consumer<T> validateFunction) {
        this.poolName = poolName;
        this.maxPoolSize = maxPoolSize;
        this.objectFactory = objectFactory;
        this.resetFunction = resetFunction;
        this.validateFunction = validateFunction;
        
        log.info("对象池已创建: name={}, maxSize={}", poolName, maxPoolSize);
    }

    /**
     * 从池中借用对象
     */
    public T borrow() {
        if (!active) {
            throw new IllegalStateException("对象池已关闭: " + poolName);
        }
        borrowCount.increment();

        T obj = pool.poll();

        if (obj != null) {
            try {
                if (validateFunction != null) {
                    validateFunction.accept(obj);
                }
                // 如果验证成功，直接返回对象
                return obj;
            } catch (Exception e) {
                validationFailures.increment();
                log.warn("对象验证失败，将创建新对象: pool={}", poolName, e);
                // 验证失败，不返回该对象，继续执行下面的新对象创建逻辑
            }
        }

        // 如果池为空或对象验证失败，则创建新对象
        try {
            T newObj = objectFactory.get();
            createCount.increment();
            return newObj;
        } catch (Exception e) {
            log.error("创建新对象失败: pool={}", poolName, e);
            throw new RuntimeException("创建新对象失败", e);
        }
    }

    /**
     * 归还对象到池中
     */
    public void returnObject(T obj) {
        if (!active || obj == null) {
            return;
        }

        returnCount.increment();

        try {
            // 重置对象状态
            if (resetFunction != null) {
                resetFunction.accept(obj);
            }

            // 检查池大小限制
            if (pool.size() < maxPoolSize) {
                pool.offer(obj);
            } else {
                // 池已满，丢弃对象
                discardCount.increment();
            }
        } catch (Exception e) {
            log.warn("归还对象失败: pool={}", poolName, e);
            discardCount.increment();
        }
    }

    /**
     * 预热池 - 预先创建指定数量的对象
     */
    public void warmUp(int count) {
        log.info("开始预热对象池: pool={}, count={}", poolName, count);
        
        int actualCount = Math.min(count, maxPoolSize);
        for (int i = 0; i < actualCount; i++) {
            try {
                T obj = objectFactory.get();
                if (resetFunction != null) {
                    resetFunction.accept(obj);
                }
                pool.offer(obj);
                createCount.increment();
            } catch (Exception e) {
                log.error("预热对象创建失败: pool={}, index={}", poolName, i, e);
                break;
            }
        }
        
        log.info("对象池预热完成: pool={}, created={}, poolSize={}", 
                poolName, actualCount, pool.size());
    }

    /**
     * 清空池
     */
    public void clear() {
        log.info("清空对象池: pool={}, currentSize={}", poolName, pool.size());
        
        int cleared = 0;
        while (!pool.isEmpty()) {
            T obj = pool.poll();
            if (obj != null) {
                cleared++;
            }
        }
        
        log.info("对象池已清空: pool={}, cleared={}", poolName, cleared);
    }

    /**
     * 关闭池
     */
    public void shutdown() {
        log.info("关闭对象池: pool={}", poolName);
        active = false;
        clear();
    }

    /**
     * 获取池统计信息
     */
    public PoolStatistics getStatistics() {
        return new PoolStatistics(
                poolName,
                pool.size(),
                maxPoolSize,
                borrowCount.sum(),
                returnCount.sum(),
                createCount.sum(),
                discardCount.sum(),
                validationFailures.sum(),
                active
        );
    }

    /**
     * 获取池使用率
     */
    public double getUtilizationRate() {
        long totalBorrows = borrowCount.sum();
        long totalCreates = createCount.sum();
        
        if (totalBorrows == 0) {
            return 0.0;
        }
        
        // 池命中率 = (借用次数 - 创建次数) / 借用次数
        return totalCreates == 0 ? 1.0 : (double) (totalBorrows - totalCreates) / totalBorrows;
    }

    /**
     * 检查池健康状态
     */
    public boolean isHealthy() {
        // 检查基本状态
        if (!active) {
            return false;
        }

        // 检查验证失败率
        long totalValidations = borrowCount.sum();
        long failures = validationFailures.sum();
        
        if (totalValidations > 100 && failures > totalValidations * 0.1) {
            log.warn("对象池验证失败率过高: pool={}, failureRate={:.2f}%", 
                    poolName, (double) failures / totalValidations * 100);
            return false;
        }

        return true;
    }

    /**
     * 池统计信息
     */
    public static class PoolStatistics {
        private final String poolName;
        private final int currentSize;
        private final int maxSize;
        private final long borrowCount;
        private final long returnCount;
        private final long createCount;
        private final long discardCount;
        private final long validationFailures;
        private final boolean active;

        public PoolStatistics(String poolName, int currentSize, int maxSize,
                            long borrowCount, long returnCount, long createCount,
                            long discardCount, long validationFailures, boolean active) {
            this.poolName = poolName;
            this.currentSize = currentSize;
            this.maxSize = maxSize;
            this.borrowCount = borrowCount;
            this.returnCount = returnCount;
            this.createCount = createCount;
            this.discardCount = discardCount;
            this.validationFailures = validationFailures;
            this.active = active;
        }

        // Getters
        public String getPoolName() { return poolName; }
        public int getCurrentSize() { return currentSize; }
        public int getMaxSize() { return maxSize; }
        public long getBorrowCount() { return borrowCount; }
        public long getReturnCount() { return returnCount; }
        public long getCreateCount() { return createCount; }
        public long getDiscardCount() { return discardCount; }
        public long getValidationFailures() { return validationFailures; }
        public boolean isActive() { return active; }

        public double getHitRate() {
            return borrowCount == 0 ? 0.0 : (double) (borrowCount - createCount) / borrowCount;
        }

        @Override
        public String toString() {
            return String.format("Pool[%s]: size=%d/%d, borrows=%d, creates=%d, hitRate=%.2f%%, active=%s",
                    poolName, currentSize, maxSize, borrowCount, createCount, getHitRate() * 100, active);
        }
    }
}
