package com.trading.common.pool;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.module.afterburner.AfterburnerModule;
import com.trading.common.dto.DepthData;
import com.trading.common.dto.KlineData;
import com.trading.common.pool.HighPerformanceObjectPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.math.BigDecimal;
import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 对象池管理器
 * 统一管理所有对象池，提供对象借用/归还接口和性能监控
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class ObjectPoolManager {

    private static final Logger log = LoggerFactory.getLogger(ObjectPoolManager.class);

    // 对象池配置
    private static final int OBJECT_MAPPER_POOL_SIZE = 20;
    private static final int STRING_BUILDER_POOL_SIZE = 50;
    private static final int JSON_NODE_POOL_SIZE = 30;
    private static final int DATA_MODEL_POOL_SIZE = 100;
    private static final int COLLECTION_POOL_SIZE = 200;

    // 对象池实例
    private HighPerformanceObjectPool<ObjectMapper> objectMapperPool;
    private HighPerformanceObjectPool<StringBuilder> stringBuilderPool;
    private HighPerformanceObjectPool<KlineData> klineDataPool;
    private HighPerformanceObjectPool<DepthData> depthDataPool;
    private HighPerformanceObjectPool<ArrayList<Object>> arrayListPool;
    private HighPerformanceObjectPool<HashMap<String, Object>> hashMapPool;

    // 池注册表
    private final Map<String, HighPerformanceObjectPool<?>> poolRegistry = new ConcurrentHashMap<>();

    // 监控执行器
    private ScheduledExecutorService monitorExecutor;

    @PostConstruct
    public void initialize() {
        log.info("初始化对象池管理器...");

        initializeObjectMapperPool();
        initializeStringBuilderPool();
        initializeDataModelPools();
        initializeCollectionPools();
        
        // 预热所有池
        warmUpAllPools();
        
        // 启动监控
        startMonitoring();

        log.info("对象池管理器初始化完成，共注册{}个对象池", poolRegistry.size());
    }

    /**
     * 初始化ObjectMapper对象池
     */
    private void initializeObjectMapperPool() {
        objectMapperPool = new HighPerformanceObjectPool<>(
                "ObjectMapper",
                OBJECT_MAPPER_POOL_SIZE,
                this::createOptimizedObjectMapper,
                mapper -> {}, // ObjectMapper无需重置
                mapper -> {
                    if (mapper == null) {
                        throw new IllegalStateException("ObjectMapper不能为null");
                    }
                }
        );
        poolRegistry.put("ObjectMapper", objectMapperPool);
    }

    /**
     * 初始化StringBuilder对象池
     */
    private void initializeStringBuilderPool() {
        stringBuilderPool = new HighPerformanceObjectPool<>(
                "StringBuilder",
                STRING_BUILDER_POOL_SIZE,
                () -> new StringBuilder(1024), // 预分配1KB容量
                sb -> sb.setLength(0), // 重置长度
                sb -> {
                    if (sb == null || sb.capacity() > 8192) { // 限制最大容量8KB
                        throw new IllegalStateException("StringBuilder无效或容量过大");
                    }
                }
        );
        poolRegistry.put("StringBuilder", stringBuilderPool);
    }

    /**
     * 初始化数据模型对象池
     */
    private void initializeDataModelPools() {
        // KlineData对象池
        klineDataPool = new HighPerformanceObjectPool<>(
                "KlineData",
                DATA_MODEL_POOL_SIZE,
                KlineData::new,
                this::resetKlineData,
                data -> {
                    if (data == null) {
                        throw new IllegalStateException("KlineData不能为null");
                    }
                }
        );
        poolRegistry.put("KlineData", klineDataPool);

        // DepthData对象池
        depthDataPool = new HighPerformanceObjectPool<>(
                "DepthData",
                DATA_MODEL_POOL_SIZE,
                DepthData::new,
                this::resetDepthData,
                data -> {
                    if (data == null) {
                        throw new IllegalStateException("DepthData不能为null");
                    }
                }
        );
        poolRegistry.put("DepthData", depthDataPool);

    }

    /**
     * 初始化集合对象池
     */
    @SuppressWarnings("unchecked")
    private void initializeCollectionPools() {
        // ArrayList对象池
        arrayListPool = new HighPerformanceObjectPool<>(
                "ArrayList",
                COLLECTION_POOL_SIZE,
                () -> new ArrayList<>(16), // 预分配16个元素容量
                list -> list.clear(),
                list -> {
                    if (list == null || list.size() > 1000) { // 限制最大元素数
                        throw new IllegalStateException("ArrayList无效或元素过多");
                    }
                }
        );
        poolRegistry.put("ArrayList", arrayListPool);

        // HashMap对象池
        hashMapPool = new HighPerformanceObjectPool<>(
                "HashMap",
                COLLECTION_POOL_SIZE,
                () -> new HashMap<>(16), // 预分配16个元素容量
                map -> map.clear(),
                map -> {
                    if (map == null || map.size() > 1000) { // 限制最大元素数
                        throw new IllegalStateException("HashMap无效或元素过多");
                    }
                }
        );
        poolRegistry.put("HashMap", hashMapPool);
    }

    /**
     * 创建优化的ObjectMapper
     */
    private ObjectMapper createOptimizedObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 注册Afterburner模块以提升性能
        mapper.registerModule(new AfterburnerModule());

        // 注册JSR310时间模块支持
        mapper.registerModule(new com.fasterxml.jackson.datatype.jsr310.JavaTimeModule());

        // 性能优化配置
        mapper.getFactory().disable(com.fasterxml.jackson.core.JsonGenerator.Feature.AUTO_CLOSE_TARGET);
        mapper.getFactory().disable(com.fasterxml.jackson.core.JsonParser.Feature.AUTO_CLOSE_SOURCE);

        return mapper;
    }

    /**
     * 重置KlineData对象
     */
    private void resetKlineData(KlineData data) {
        data.setSymbol(null);
        data.setInterval(null);
        data.setOpenTime(null);
        data.setCloseTime(null);
        data.setOpenPrice(null);
        data.setHighPrice(null);
        data.setLowPrice(null);
        data.setClosePrice(null);
        data.setVolume(null);
        data.setQuoteVolume(null);
        data.setTradeCount(null);
        data.setTakerBuyVolume(null);
        data.setTakerBuyQuoteVolume(null);
        // data.setCreatedAt(null); // Removed
        // data.setUpdatedAt(null); // Removed
    }

    /**
     * 重置DepthData对象
     */
    private void resetDepthData(DepthData data) {
        data.setSymbol(null);
        data.setBids(null);
        data.setAsks(null);
        data.setLastUpdateId(null);
        data.setTimestamp(null);
        // 保持必需字段的默认值，避免数据库约束错误
        data.setSource("binance");
        data.setLevels(0);
    }


    /**
     * 预热所有对象池 - 优化为更小的预热数量以减少内存压力
     */
    private void warmUpAllPools() {
        log.info("开始预热所有对象池...");

        // 减少预热数量以降低内存使用和启动时间
        objectMapperPool.warmUp(Math.max(2, OBJECT_MAPPER_POOL_SIZE / 4));  // 最少2个，最多5个
        stringBuilderPool.warmUp(Math.max(5, STRING_BUILDER_POOL_SIZE / 10)); // 最少5个，最多5个
        klineDataPool.warmUp(Math.max(3, DATA_MODEL_POOL_SIZE / 20));        // 最少3个，最多5个
        depthDataPool.warmUp(Math.max(3, DATA_MODEL_POOL_SIZE / 20));        // 最少3个，最多5个
        arrayListPool.warmUp(Math.max(5, COLLECTION_POOL_SIZE / 40));        // 最少5个，最多5个
        hashMapPool.warmUp(Math.max(5, COLLECTION_POOL_SIZE / 40));          // 最少5个，最多5个

        log.info("所有对象池预热完成");
    }

    /**
     * 启动监控
     */
    private void startMonitoring() {
        monitorExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "object-pool-monitor");
            thread.setDaemon(true);
            return thread;
        });

        // 每10分钟输出一次统计信息
        monitorExecutor.scheduleAtFixedRate(this::logPoolStatistics, 10, 10, TimeUnit.MINUTES);
    }

    /**
     * 输出池统计信息
     */
    private void logPoolStatistics() {
        log.info("=== 对象池统计信息 ===");
        poolRegistry.forEach((name, pool) -> {
            HighPerformanceObjectPool.PoolStatistics stats = pool.getStatistics();
            log.info("{}", stats);
        });
        log.info("=== 统计信息结束 ===");
    }

    // 公共接口方法
    public ObjectMapper borrowObjectMapper() {
        return objectMapperPool.borrow();
    }

    public void returnObjectMapper(ObjectMapper mapper) {
        objectMapperPool.returnObject(mapper);
    }

    public StringBuilder borrowStringBuilder() {
        return stringBuilderPool.borrow();
    }

    public void returnStringBuilder(StringBuilder sb) {
        stringBuilderPool.returnObject(sb);
    }

    public KlineData borrowKlineData() {
        return klineDataPool.borrow();
    }

    public void returnKlineData(KlineData data) {
        klineDataPool.returnObject(data);
    }

    public DepthData borrowDepthData() {
        return depthDataPool.borrow();
    }

    public void returnDepthData(DepthData data) {
        depthDataPool.returnObject(data);
    }



    @SuppressWarnings("unchecked")
    public <T> ArrayList<T> borrowArrayList() {
        return (ArrayList<T>) arrayListPool.borrow();
    }

    public void returnArrayList(ArrayList<?> list) {
        arrayListPool.returnObject((ArrayList<Object>) list);
    }

    @SuppressWarnings("unchecked")
    public <K, V> HashMap<K, V> borrowHashMap() {
        return (HashMap<K, V>) hashMapPool.borrow();
    }

    public void returnHashMap(HashMap<?, ?> map) {
        hashMapPool.returnObject((HashMap<String, Object>) map);
    }

    /**
     * 获取所有池的统计信息
     */
    public Map<String, HighPerformanceObjectPool.PoolStatistics> getAllStatistics() {
        Map<String, HighPerformanceObjectPool.PoolStatistics> stats = new HashMap<>();
        poolRegistry.forEach((name, pool) -> stats.put(name, pool.getStatistics()));
        return stats;
    }

    @PreDestroy
    public void shutdown() {
        log.info("关闭对象池管理器...");
        
        if (monitorExecutor != null) {
            monitorExecutor.shutdown();
        }
        
        poolRegistry.values().forEach(HighPerformanceObjectPool::shutdown);
        poolRegistry.clear();
        
        log.info("对象池管理器已关闭");
    }
}
