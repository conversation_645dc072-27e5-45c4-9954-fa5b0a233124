package com.trading.common.api;

import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

import com.trading.common.dto.RateLimitInfo;


import java.time.LocalDateTime;
import java.util.Map;

/**
 * API响应基础类
 * 封装所有REST API响应的通用属性和方法
 * 
 * @param <T> 响应数据类型
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
public class ApiResponse<T> {
    
    /**
     * 响应是否成功
     */
    @Builder.Default
    private boolean success = true;
    
    /**
     * HTTP状态码
     */
    private int httpStatusCode;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 错误码（当success为false时）
     */
    private String errorCode;
    
    /**
     * 错误消息（当success为false时）
     */
    private String errorMessage;
    
    /**
     * 原始响应字符串
     */
    private String rawResponse;
    
    /**
     * 响应头信息
     */
    private Map<String, String> headers;
    
    /**
     * 请求时间戳
     */
    @Builder.Default
    private LocalDateTime requestTime = LocalDateTime.now();
    
    /**
     * 响应时间戳
     */
    @Builder.Default
    private LocalDateTime responseTime = LocalDateTime.now();
    
    /**
     * 请求耗时（毫秒）
     */
    private long duration;
    
    /**
     * 限流信息
     */
    private RateLimitInfo rateLimitInfo;
    
    /**
     * 创建成功响应
     * 
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return ApiResponse.<T>builder()
                .success(true)
                .data(data)
                .httpStatusCode(200)
                .build();
    }
    
    /**
     * 创建成功响应（带原始响应）
     * 
     * @param data 响应数据
     * @param rawResponse 原始响应字符串
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data, String rawResponse) {
        return ApiResponse.<T>builder()
                .success(true)
                .data(data)
                .rawResponse(rawResponse)
                .httpStatusCode(200)
                .build();
    }
    
    /**
     * 创建失败响应
     *
     * @param errorMessage 错误消息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResponse<T> error(String errorMessage) {
        return ApiResponse.<T>builder()
                .success(false)
                .errorMessage(errorMessage)
                .httpStatusCode(500)
                .build();
    }

    /**
     * 创建失败响应
     *
     * @param errorCode 错误码
     * @param errorMessage 错误消息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResponse<T> error(String errorCode, String errorMessage) {
        return ApiResponse.<T>builder()
                .success(false)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .httpStatusCode(500)
                .build();
    }
    
    /**
     * 创建失败响应（带HTTP状态码）
     * 
     * @param httpStatusCode HTTP状态码
     * @param errorCode 错误码
     * @param errorMessage 错误消息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ApiResponse<T> error(int httpStatusCode, String errorCode, String errorMessage) {
        return ApiResponse.<T>builder()
                .success(false)
                .httpStatusCode(httpStatusCode)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .build();
    }
    
    /**
     * 检查响应是否成功
     * 
     * @return 是否成功
     */
    public boolean isSuccess() {
        return success && data != null;
    }
    
    /**
     * 检查响应是否失败
     * 
     * @return 是否失败
     */
    public boolean isError() {
        return !success;
    }
    
    /**
     * 获取响应耗时
     * 
     * @return 耗时（毫秒）
     */
    public long getDuration() {
        if (duration > 0) {
            return duration;
        }
        if (requestTime != null && responseTime != null) {
            return java.time.Duration.between(requestTime, responseTime).toMillis();
        }
        return 0;
    }
}
