package com.trading.common.api;

import com.trading.common.enums.HttpMethod;
import com.trading.common.enums.RequestType;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * API请求基础类
 * 封装所有REST API请求的通用属性和方法
 * 
 * @param <T> 响应数据类型
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
public class ApiRequest<T> {
    
    /**
     * 请求路径（不包含baseUrl）
     */
    @NonNull
    private String path;
    
    /**
     * HTTP方法
     */
    @NonNull
    @Builder.Default
    private HttpMethod method = HttpMethod.GET;
    
    /**
     * 请求类型（公开、需要API Key、需要签名）
     */
    @NonNull
    @Builder.Default
    private RequestType requestType = RequestType.PUBLIC;
    
    /**
     * 请求参数
     */
    @Builder.Default
    private Map<String, Object> parameters = new LinkedHashMap<>();
    
    /**
     * 响应数据类型
     */
    @NonNull
    private Class<T> responseType;
    
    /**
     * 请求超时时间（毫秒）
     */
    @Builder.Default
    private long timeoutMs = 30000;
    
    /**
     * 是否显示限流使用情况
     */
    @Builder.Default
    private boolean showLimitUsage = false;
    
    /**
     * 请求优先级（用于限流队列）
     */
    @Builder.Default
    private int priority = 5;
    
    /**
     * 是否启用重试
     */
    @Builder.Default
    private boolean retryEnabled = true;
    
    /**
     * 最大重试次数
     */
    @Builder.Default
    private int maxRetries = 3;
    
    /**
     * 添加请求参数
     * 
     * @param key 参数名
     * @param value 参数值
     * @return 当前请求对象
     */
    public ApiRequest<T> addParameter(String key, Object value) {
        if (value != null) {
            this.parameters.put(key, value);
        }
        return this;
    }
    
    /**
     * 批量添加请求参数
     * 
     * @param params 参数Map
     * @return 当前请求对象
     */
    public ApiRequest<T> addParameters(Map<String, Object> params) {
        if (params != null) {
            this.parameters.putAll(params);
        }
        return this;
    }
    
    /**
     * 设置高优先级（用于交易相关请求）
     * 
     * @return 当前请求对象
     */
    public ApiRequest<T> highPriority() {
        this.priority = 1;
        return this;
    }
    
    /**
     * 设置低优先级（用于查询相关请求）
     * 
     * @return 当前请求对象
     */
    public ApiRequest<T> lowPriority() {
        this.priority = 10;
        return this;
    }
    
    /**
     * 禁用重试
     * 
     * @return 当前请求对象
     */
    public ApiRequest<T> noRetry() {
        this.retryEnabled = false;
        return this;
    }
    
    /**
     * 设置自定义超时时间
     * 
     * @param timeoutMs 超时时间（毫秒）
     * @return 当前请求对象
     */
    public ApiRequest<T> timeout(long timeoutMs) {
        this.timeoutMs = timeoutMs;
        return this;
    }
}
