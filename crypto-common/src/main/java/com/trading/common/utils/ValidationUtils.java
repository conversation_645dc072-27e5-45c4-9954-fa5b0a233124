package com.trading.common.utils;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 验证工具类
 * 提供常用的数据验证方法
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public final class ValidationUtils {
    
    /**
     * 邮箱正则表达式
     */
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
    );
    
    /**
     * 手机号正则表达式（中国）
     */
    private static final Pattern PHONE_PATTERN = Pattern.compile(
            "^1[3-9]\\d{9}$"
    );
    
    /**
     * 身份证号正则表达式（中国）
     */
    private static final Pattern ID_CARD_PATTERN = Pattern.compile(
            "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$"
    );
    
    /**
     * IP地址正则表达式
     */
    private static final Pattern IP_PATTERN = Pattern.compile(
            "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$"
    );
    
    /**
     * URL正则表达式
     */
    private static final Pattern URL_PATTERN = Pattern.compile(
            "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$"
    );
    
    /**
     * 数字正则表达式
     */
    private static final Pattern NUMBER_PATTERN = Pattern.compile(
            "^-?\\d+(\\.\\d+)?$"
    );
    
    /**
     * 正整数正则表达式
     */
    private static final Pattern POSITIVE_INTEGER_PATTERN = Pattern.compile(
            "^[1-9]\\d*$"
    );
    
    /**
     * 非负整数正则表达式
     */
    private static final Pattern NON_NEGATIVE_INTEGER_PATTERN = Pattern.compile(
            "^\\d+$"
    );
    
    private ValidationUtils() {
        // 防止实例化
    }
    
    // ==================== 空值检查 ====================
    
    /**
     * 检查对象是否为null
     * 
     * @param obj 对象
     * @return true表示为null，false表示不为null
     */
    public static boolean isNull(Object obj) {
        return obj == null;
    }
    
    /**
     * 检查对象是否不为null
     * 
     * @param obj 对象
     * @return true表示不为null，false表示为null
     */
    public static boolean isNotNull(Object obj) {
        return obj != null;
    }
    
    /**
     * 检查字符串是否为空（null或空字符串）
     * 
     * @param str 字符串
     * @return true表示为空，false表示不为空
     */
    public static boolean isEmpty(String str) {
        return str == null || str.isEmpty();
    }
    
    /**
     * 检查字符串是否不为空
     * 
     * @param str 字符串
     * @return true表示不为空，false表示为空
     */
    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }
    
    /**
     * 检查字符串是否为空白（null、空字符串或只包含空白字符）
     * 
     * @param str 字符串
     * @return true表示为空白，false表示不为空白
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * 检查字符串是否不为空白
     * 
     * @param str 字符串
     * @return true表示不为空白，false表示为空白
     */
    public static boolean isNotBlank(String str) {
        return !isBlank(str);
    }
    
    /**
     * 检查集合是否为空
     * 
     * @param collection 集合
     * @return true表示为空，false表示不为空
     */
    public static boolean isEmpty(Collection<?> collection) {
        return collection == null || collection.isEmpty();
    }
    
    /**
     * 检查集合是否不为空
     * 
     * @param collection 集合
     * @return true表示不为空，false表示为空
     */
    public static boolean isNotEmpty(Collection<?> collection) {
        return !isEmpty(collection);
    }
    
    /**
     * 检查Map是否为空
     * 
     * @param map Map对象
     * @return true表示为空，false表示不为空
     */
    public static boolean isEmpty(Map<?, ?> map) {
        return map == null || map.isEmpty();
    }
    
    /**
     * 检查Map是否不为空
     * 
     * @param map Map对象
     * @return true表示不为空，false表示为空
     */
    public static boolean isNotEmpty(Map<?, ?> map) {
        return !isEmpty(map);
    }
    
    /**
     * 检查数组是否为空
     * 
     * @param array 数组
     * @return true表示为空，false表示不为空
     */
    public static boolean isEmpty(Object[] array) {
        return array == null || array.length == 0;
    }
    
    /**
     * 检查数组是否不为空
     * 
     * @param array 数组
     * @return true表示不为空，false表示为空
     */
    public static boolean isNotEmpty(Object[] array) {
        return !isEmpty(array);
    }
    
    // ==================== 格式验证 ====================
    
    /**
     * 验证邮箱格式
     * 
     * @param email 邮箱地址
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidEmail(String email) {
        return isNotBlank(email) && EMAIL_PATTERN.matcher(email).matches();
    }
    
    /**
     * 验证手机号格式（中国）
     * 
     * @param phone 手机号
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidPhone(String phone) {
        return isNotBlank(phone) && PHONE_PATTERN.matcher(phone).matches();
    }
    
    /**
     * 验证身份证号格式（中国）
     * 
     * @param idCard 身份证号
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidIdCard(String idCard) {
        return isNotBlank(idCard) && ID_CARD_PATTERN.matcher(idCard).matches();
    }
    
    /**
     * 验证IP地址格式
     * 
     * @param ip IP地址
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidIp(String ip) {
        return isNotBlank(ip) && IP_PATTERN.matcher(ip).matches();
    }
    
    /**
     * 验证URL格式
     * 
     * @param url URL地址
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidUrl(String url) {
        return isNotBlank(url) && URL_PATTERN.matcher(url).matches();
    }
    
    /**
     * 验证数字格式
     * 
     * @param number 数字字符串
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidNumber(String number) {
        return isNotBlank(number) && NUMBER_PATTERN.matcher(number).matches();
    }
    
    /**
     * 验证正整数格式
     * 
     * @param number 数字字符串
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidPositiveInteger(String number) {
        return isNotBlank(number) && POSITIVE_INTEGER_PATTERN.matcher(number).matches();
    }
    
    /**
     * 验证非负整数格式
     * 
     * @param number 数字字符串
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidNonNegativeInteger(String number) {
        return isNotBlank(number) && NON_NEGATIVE_INTEGER_PATTERN.matcher(number).matches();
    }
    
    // ==================== 长度验证 ====================
    
    /**
     * 验证字符串长度是否在指定范围内
     * 
     * @param str 字符串
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @return true表示在范围内，false表示不在范围内
     */
    public static boolean isLengthInRange(String str, int minLength, int maxLength) {
        if (str == null) {
            return minLength <= 0;
        }
        
        int length = str.length();
        return length >= minLength && length <= maxLength;
    }
    
    /**
     * 验证字符串最小长度
     * 
     * @param str 字符串
     * @param minLength 最小长度
     * @return true表示满足最小长度，false表示不满足
     */
    public static boolean isMinLength(String str, int minLength) {
        return str != null && str.length() >= minLength;
    }
    
    /**
     * 验证字符串最大长度
     * 
     * @param str 字符串
     * @param maxLength 最大长度
     * @return true表示不超过最大长度，false表示超过
     */
    public static boolean isMaxLength(String str, int maxLength) {
        return str == null || str.length() <= maxLength;
    }
    
    // ==================== 数值验证 ====================
    
    /**
     * 验证数值是否在指定范围内
     * 
     * @param value 数值
     * @param min 最小值
     * @param max 最大值
     * @return true表示在范围内，false表示不在范围内
     */
    public static boolean isInRange(BigDecimal value, BigDecimal min, BigDecimal max) {
        if (value == null) {
            return false;
        }
        
        boolean aboveMin = min == null || value.compareTo(min) >= 0;
        boolean belowMax = max == null || value.compareTo(max) <= 0;
        
        return aboveMin && belowMax;
    }
    
    /**
     * 验证整数是否在指定范围内
     * 
     * @param value 整数值
     * @param min 最小值
     * @param max 最大值
     * @return true表示在范围内，false表示不在范围内
     */
    public static boolean isInRange(Integer value, Integer min, Integer max) {
        if (value == null) {
            return false;
        }
        
        boolean aboveMin = min == null || value >= min;
        boolean belowMax = max == null || value <= max;
        
        return aboveMin && belowMax;
    }
    
    /**
     * 验证长整数是否在指定范围内
     *
     * @param value 长整数值
     * @param min 最小值
     * @param max 最大值
     * @return true表示在范围内，false表示不在范围内
     */
    public static boolean isInRange(Long value, Long min, Long max) {
        if (value == null) {
            return false;
        }

        boolean aboveMin = min == null || value >= min;
        boolean belowMax = max == null || value <= max;

        return aboveMin && belowMax;
    }

    // ==================== 业务验证 ====================

    /**
     * 验证交易对格式（如BTCUSDT）
     *
     * @param symbol 交易对
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidTradingSymbol(String symbol) {
        if (isBlank(symbol)) {
            return false;
        }

        // 交易对应该是大写字母，长度在6-12之间
        return symbol.matches("^[A-Z]{6,12}$");
    }

    /**
     * 验证订单ID格式
     *
     * @param orderId 订单ID
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidOrderId(String orderId) {
        if (isBlank(orderId)) {
            return false;
        }

        // 订单ID应该是数字或字母数字组合，长度在8-32之间
        return orderId.matches("^[a-zA-Z0-9]{8,32}$");
    }

    /**
     * 验证API密钥格式
     *
     * @param apiKey API密钥
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidApiKey(String apiKey) {
        if (isBlank(apiKey)) {
            return false;
        }

        // API密钥应该是字母数字组合，长度在32-128之间
        return apiKey.matches("^[a-zA-Z0-9]{32,128}$");
    }

    /**
     * 验证价格格式
     *
     * @param price 价格
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidPrice(BigDecimal price) {
        return price != null && price.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 验证数量格式
     *
     * @param quantity 数量
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidQuantity(BigDecimal quantity) {
        return quantity != null && quantity.compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * 验证时间戳格式
     *
     * @param timestamp 时间戳（毫秒）
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidTimestamp(Long timestamp) {
        if (timestamp == null) {
            return false;
        }

        // 时间戳应该在合理范围内（2020年到2050年）
        long min = 1577836800000L; // 2020-01-01 00:00:00 UTC
        long max = 2524608000000L; // 2050-01-01 00:00:00 UTC

        return timestamp >= min && timestamp <= max;
    }

    // ==================== 断言方法 ====================

    /**
     * 断言对象不为null
     *
     * @param obj 对象
     * @param message 错误消息
     * @throws IllegalArgumentException 对象为null时抛出
     */
    public static void requireNonNull(Object obj, String message) {
        if (obj == null) {
            throw new IllegalArgumentException(message != null ? message : "Object cannot be null");
        }
    }

    /**
     * 断言字符串不为空白
     *
     * @param str 字符串
     * @param message 错误消息
     * @throws IllegalArgumentException 字符串为空白时抛出
     */
    public static void requireNonBlank(String str, String message) {
        if (isBlank(str)) {
            throw new IllegalArgumentException(message != null ? message : "String cannot be blank");
        }
    }

    /**
     * 断言集合不为空
     *
     * @param collection 集合
     * @param message 错误消息
     * @throws IllegalArgumentException 集合为空时抛出
     */
    public static void requireNonEmpty(Collection<?> collection, String message) {
        if (isEmpty(collection)) {
            throw new IllegalArgumentException(message != null ? message : "Collection cannot be empty");
        }
    }

    /**
     * 断言条件为真
     *
     * @param condition 条件
     * @param message 错误消息
     * @throws IllegalArgumentException 条件为假时抛出
     */
    public static void requireTrue(boolean condition, String message) {
        if (!condition) {
            throw new IllegalArgumentException(message != null ? message : "Condition must be true");
        }
    }

    /**
     * 断言数值在指定范围内
     *
     * @param value 数值
     * @param min 最小值
     * @param max 最大值
     * @param message 错误消息
     * @throws IllegalArgumentException 数值不在范围内时抛出
     */
    public static void requireInRange(BigDecimal value, BigDecimal min, BigDecimal max, String message) {
        if (!isInRange(value, min, max)) {
            throw new IllegalArgumentException(message != null ? message :
                    String.format("Value must be between %s and %s", min, max));
        }
    }

    /**
     * 断言字符串长度在指定范围内
     *
     * @param str 字符串
     * @param minLength 最小长度
     * @param maxLength 最大长度
     * @param message 错误消息
     * @throws IllegalArgumentException 长度不在范围内时抛出
     */
    public static void requireLengthInRange(String str, int minLength, int maxLength, String message) {
        if (!isLengthInRange(str, minLength, maxLength)) {
            throw new IllegalArgumentException(message != null ? message :
                    String.format("String length must be between %d and %d", minLength, maxLength));
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 获取安全的字符串（null转为空字符串）
     *
     * @param str 字符串
     * @return 安全的字符串
     */
    public static String safeString(String str) {
        return str != null ? str : "";
    }

    /**
     * 获取安全的字符串（null或空白转为默认值）
     *
     * @param str 字符串
     * @param defaultValue 默认值
     * @return 安全的字符串
     */
    public static String safeString(String str, String defaultValue) {
        return isNotBlank(str) ? str : defaultValue;
    }

    /**
     * 获取安全的整数（null转为默认值）
     *
     * @param value 整数值
     * @param defaultValue 默认值
     * @return 安全的整数
     */
    public static Integer safeInteger(Integer value, Integer defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * 获取安全的长整数（null转为默认值）
     *
     * @param value 长整数值
     * @param defaultValue 默认值
     * @return 安全的长整数
     */
    public static Long safeLong(Long value, Long defaultValue) {
        return value != null ? value : defaultValue;
    }

    /**
     * 获取安全的BigDecimal（null转为默认值）
     *
     * @param value BigDecimal值
     * @param defaultValue 默认值
     * @return 安全的BigDecimal
     */
    public static BigDecimal safeBigDecimal(BigDecimal value, BigDecimal defaultValue) {
        return value != null ? value : defaultValue;
    }
}
