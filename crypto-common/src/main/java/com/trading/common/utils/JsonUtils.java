package com.trading.common.utils;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.datatype.jsr310.deser.LocalDateTimeDeserializer;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import com.trading.common.exception.JsonOperationException;

import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * JSON工具类
 * 提供JSON序列化和反序列化的常用操作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public final class JsonUtils {

    /**
     * ObjectMapper实例
     */
    private static final ObjectMapper OBJECT_MAPPER;

    private static final String STANDARD_DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    static {
        OBJECT_MAPPER = new ObjectMapper();

        // 创建并配置JavaTimeModule
        JavaTimeModule javaTimeModule = new JavaTimeModule();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(STANDARD_DATETIME_FORMAT);
        javaTimeModule.addSerializer(LocalDateTime.class, new LocalDateTimeSerializer(dateTimeFormatter));
        javaTimeModule.addDeserializer(LocalDateTime.class, new LocalDateTimeDeserializer(dateTimeFormatter));
        OBJECT_MAPPER.registerModule(javaTimeModule);

        // 配置序列化选项
        OBJECT_MAPPER.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        OBJECT_MAPPER.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        OBJECT_MAPPER.configure(SerializationFeature.INDENT_OUTPUT, false);

        // 忽略null字段
        OBJECT_MAPPER.setSerializationInclusion(JsonInclude.Include.NON_NULL);

        // 配置反序列化选项
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        OBJECT_MAPPER.configure(DeserializationFeature.FAIL_ON_NULL_FOR_PRIMITIVES, false);
        OBJECT_MAPPER.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
    }
    
    private JsonUtils() {
        // 防止实例化
    }
    
    /**
     * 获取ObjectMapper实例
     * 
     * @return ObjectMapper实例
     */
    public static ObjectMapper getObjectMapper() {
        return OBJECT_MAPPER;
    }
    
    // ==================== 序列化 ====================
    
    /**
     * 对象转JSON字符串
     * 
     * @param obj 对象
     * @return JSON字符串
     */
    public static String toJson(Object obj) {
        if (obj == null) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new JsonOperationException("Failed to serialize object to JSON", e);
        }
    }
    
    /**
     * 对象转格式化的JSON字符串
     * 
     * @param obj 对象
     * @return 格式化的JSON字符串
     */
    public static String toPrettyJson(Object obj) {
        if (obj == null) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.writerWithDefaultPrettyPrinter().writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new JsonOperationException("Failed to serialize object to pretty JSON", e);
        }
    }
    
    /**
     * 对象转JSON字节数组
     * 
     * @param obj 对象
     * @return JSON字节数组
     */
    public static byte[] toJsonBytes(Object obj) {
        if (obj == null) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.writeValueAsBytes(obj);
        } catch (JsonProcessingException e) {
            throw new JsonOperationException("Failed to serialize object to JSON bytes", e);
        }
    }
    
    // ==================== 反序列化 ====================
    
    /**
     * JSON字符串转对象
     * 
     * @param json JSON字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 对象实例
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        if (json == null || json.trim().isEmpty()) {
            throw new JsonOperationException("JSON string cannot be null or empty");
        }

        try {
            return OBJECT_MAPPER.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new JsonOperationException("Failed to deserialize JSON to object", e);
        }
    }
    
    /**
     * JSON字符串转对象（使用TypeReference）
     * 
     * @param json JSON字符串
     * @param typeReference 类型引用
     * @param <T> 泛型类型
     * @return 对象实例
     */
    public static <T> T fromJson(String json, TypeReference<T> typeReference) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.readValue(json, typeReference);
        } catch (JsonProcessingException e) {
            throw new JsonOperationException("Failed to deserialize JSON to object", e);
        }
    }
    
    /**
     * JSON字节数组转对象
     * 
     * @param jsonBytes JSON字节数组
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 对象实例
     */
    public static <T> T fromJsonBytes(byte[] jsonBytes, Class<T> clazz) {
        if (jsonBytes == null || jsonBytes.length == 0) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.readValue(jsonBytes, clazz);
        } catch (IOException e) {
            throw new JsonOperationException("Failed to deserialize JSON bytes to object", e);
        }
    }
    
    /**
     * 输入流转对象
     * 
     * @param inputStream 输入流
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 对象实例
     */
    public static <T> T fromInputStream(InputStream inputStream, Class<T> clazz) {
        if (inputStream == null) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.readValue(inputStream, clazz);
        } catch (IOException e) {
            throw new JsonOperationException("Failed to deserialize InputStream to object", e);
        }
    }
    
    // ==================== 便捷方法 ====================
    
    /**
     * JSON字符串转Map
     * 
     * @param json JSON字符串
     * @return Map对象
     */
    public static Map<String, Object> toMap(String json) {
        return fromJson(json, new TypeReference<Map<String, Object>>() {});
    }
    
    /**
     * JSON字符串转List
     * 
     * @param json JSON字符串
     * @param elementClass 元素类型
     * @param <T> 泛型类型
     * @return List对象
     */
    public static <T> List<T> toList(String json, Class<T> elementClass) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.readValue(json, 
                    OBJECT_MAPPER.getTypeFactory().constructCollectionType(List.class, elementClass));
        } catch (JsonProcessingException e) {
            throw new JsonOperationException("Failed to deserialize JSON to List", e);
        }
    }
    
    /**
     * 对象转Map
     * 
     * @param obj 对象
     * @return Map对象
     */
    public static Map<String, Object> objectToMap(Object obj) {
        if (obj == null) {
            return null;
        }
        
        return OBJECT_MAPPER.convertValue(obj, new TypeReference<Map<String, Object>>() {});
    }
    
    /**
     * Map转对象
     * 
     * @param map Map对象
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 对象实例
     */
    public static <T> T mapToObject(Map<String, Object> map, Class<T> clazz) {
        if (map == null) {
            return null;
        }
        
        return OBJECT_MAPPER.convertValue(map, clazz);
    }
    
    // ==================== JsonNode操作 ====================
    
    /**
     * 字符串转JsonNode
     * 
     * @param json JSON字符串
     * @return JsonNode对象
     */
    public static JsonNode parseTree(String json) {
        if (json == null || json.trim().isEmpty()) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.readTree(json);
        } catch (JsonProcessingException e) {
            throw new JsonOperationException("Failed to parse JSON to JsonNode", e);
        }
    }
    
    /**
     * 对象转JsonNode
     * 
     * @param obj 对象
     * @return JsonNode对象
     */
    public static JsonNode valueToTree(Object obj) {
        if (obj == null) {
            return null;
        }
        
        return OBJECT_MAPPER.valueToTree(obj);
    }
    
    /**
     * JsonNode转对象
     * 
     * @param node JsonNode对象
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 对象实例
     */
    public static <T> T treeToValue(JsonNode node, Class<T> clazz) {
        if (node == null) {
            return null;
        }
        
        try {
            return OBJECT_MAPPER.treeToValue(node, clazz);
        } catch (JsonProcessingException e) {
            throw new JsonOperationException("Failed to convert JsonNode to object", e);
        }
    }
    
    // ==================== 安全方法 ====================
    
    /**
     * 安全的JSON序列化，失败时返回默认值
     * 
     * @param obj 对象
     * @param defaultValue 默认值
     * @return JSON字符串或默认值
     */
    public static String toJsonSafe(Object obj, String defaultValue) {
        try {
            return toJson(obj);
        } catch (Exception e) {
            return defaultValue;
        }
    }
    
    /**
     * 安全的JSON反序列化，失败时返回默认值
     * 
     * @param json JSON字符串
     * @param clazz 目标类型
     * @param defaultValue 默认值
     * @param <T> 泛型类型
     * @return 对象实例或默认值
     */
    public static <T> T fromJsonSafe(String json, Class<T> clazz, T defaultValue) {
        try {
            return fromJson(json, clazz);
        } catch (Exception e) {
            return defaultValue;
        }
    }
    
    // ==================== 验证方法 ====================
    
    /**
     * 验证是否为有效的JSON字符串
     * 
     * @param json JSON字符串
     * @return true表示有效，false表示无效
     */
    public static boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }
        
        try {
            OBJECT_MAPPER.readTree(json);
            return true;
        } catch (JsonProcessingException e) {
            return false;
        }
    }
    
    /**
     * 验证是否为有效的JSON对象字符串
     * 
     * @param json JSON字符串
     * @return true表示有效的JSON对象，false表示无效
     */
    public static boolean isValidJsonObject(String json) {
        if (!isValidJson(json)) {
            return false;
        }
        
        try {
            JsonNode node = OBJECT_MAPPER.readTree(json);
            return node.isObject();
        } catch (JsonProcessingException e) {
            return false;
        }
    }
    
    /**
     * 验证是否为有效的JSON数组字符串
     *
     * @param json JSON字符串
     * @return true表示有效的JSON数组，false表示无效
     */
    public static boolean isValidJsonArray(String json) {
        if (!isValidJson(json)) {
            return false;
        }

        try {
            JsonNode node = OBJECT_MAPPER.readTree(json);
            return node.isArray();
        } catch (JsonProcessingException e) {
            return false;
        }
    }

    // ==================== 别名方法 ====================

    /**
     * 对象转JSON字符串（别名方法）
     *
     * @param obj 对象
     * @return JSON字符串
     */
    public static String toJsonString(Object obj) {
        return toJson(obj);
    }

    /**
     * JSON字符串转对象（别名方法）
     *
     * @param json JSON字符串
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 对象
     */
    public static <T> T fromJsonString(String json, Class<T> clazz) {
        return fromJson(json, clazz);
    }

    /**
     * JSON字符串转对象（别名方法，支持TypeReference）
     *
     * @param json JSON字符串
     * @param typeReference 类型引用
     * @param <T> 泛型类型
     * @return 对象
     */
    public static <T> T fromJsonString(String json, TypeReference<T> typeReference) {
        return fromJson(json, typeReference);
    }

    /**
     * 对象转格式化JSON字符串（别名方法）
     *
     * @param obj 对象
     * @return 格式化的JSON字符串
     */
    public static String toPrettyJsonString(Object obj) {
        return toPrettyJson(obj);
    }
}
