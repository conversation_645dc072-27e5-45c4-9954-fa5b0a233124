package com.trading.common.utils;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 加密工具类
 * 提供常用的加密、解密和签名方法
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public final class EncryptUtils {
    
    /**
     * MD5算法名称
     */
    public static final String MD5 = "MD5";
    
    /**
     * SHA-1算法名称
     */
    public static final String SHA1 = "SHA-1";
    
    /**
     * SHA-256算法名称
     */
    public static final String SHA256 = "SHA-256";
    
    /**
     * SHA-512算法名称
     */
    public static final String SHA512 = "SHA-512";
    
    /**
     * HMAC-SHA256算法名称
     */
    public static final String HMAC_SHA256 = "HmacSHA256";
    
    /**
     * HMAC-SHA512算法名称
     */
    public static final String HMAC_SHA512 = "HmacSHA512";
    
    /**
     * 十六进制字符
     */
    private static final char[] HEX_CHARS = "0123456789abcdef".toCharArray();
    
    /**
     * 安全随机数生成器
     */
    private static final SecureRandom SECURE_RANDOM = new SecureRandom();
    
    private EncryptUtils() {
        // 防止实例化
    }
    
    // ==================== 哈希算法 ====================
    
    /**
     * MD5哈希
     * 
     * @param data 原始数据
     * @return MD5哈希值（十六进制字符串）
     */
    public static String md5(String data) {
        return hash(data, MD5);
    }
    
    /**
     * MD5哈希
     * 
     * @param data 原始数据
     * @return MD5哈希值（十六进制字符串）
     */
    public static String md5(byte[] data) {
        return hash(data, MD5);
    }
    
    /**
     * SHA-1哈希
     * 
     * @param data 原始数据
     * @return SHA-1哈希值（十六进制字符串）
     */
    public static String sha1(String data) {
        return hash(data, SHA1);
    }
    
    /**
     * SHA-1哈希
     * 
     * @param data 原始数据
     * @return SHA-1哈希值（十六进制字符串）
     */
    public static String sha1(byte[] data) {
        return hash(data, SHA1);
    }
    
    /**
     * SHA-256哈希
     * 
     * @param data 原始数据
     * @return SHA-256哈希值（十六进制字符串）
     */
    public static String sha256(String data) {
        return hash(data, SHA256);
    }
    
    /**
     * SHA-256哈希
     * 
     * @param data 原始数据
     * @return SHA-256哈希值（十六进制字符串）
     */
    public static String sha256(byte[] data) {
        return hash(data, SHA256);
    }
    
    /**
     * SHA-512哈希
     * 
     * @param data 原始数据
     * @return SHA-512哈希值（十六进制字符串）
     */
    public static String sha512(String data) {
        return hash(data, SHA512);
    }
    
    /**
     * SHA-512哈希
     * 
     * @param data 原始数据
     * @return SHA-512哈希值（十六进制字符串）
     */
    public static String sha512(byte[] data) {
        return hash(data, SHA512);
    }
    
    /**
     * 通用哈希方法
     * 
     * @param data 原始数据
     * @param algorithm 算法名称
     * @return 哈希值（十六进制字符串）
     */
    public static String hash(String data, String algorithm) {
        if (data == null) {
            return null;
        }
        return hash(data.getBytes(StandardCharsets.UTF_8), algorithm);
    }
    
    /**
     * 通用哈希方法
     * 
     * @param data 原始数据
     * @param algorithm 算法名称
     * @return 哈希值（十六进制字符串）
     */
    public static String hash(byte[] data, String algorithm) {
        if (data == null) {
            return null;
        }
        
        try {
            MessageDigest digest = MessageDigest.getInstance(algorithm);
            byte[] hashBytes = digest.digest(data);
            return bytesToHex(hashBytes);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Hash algorithm not available: " + algorithm, e);
        }
    }
    
    // ==================== HMAC签名 ====================
    
    /**
     * HMAC-SHA256签名
     * 
     * @param data 原始数据
     * @param key 密钥
     * @return HMAC-SHA256签名（十六进制字符串）
     */
    public static String hmacSha256(String data, String key) {
        return hmac(data, key, HMAC_SHA256);
    }
    
    /**
     * HMAC-SHA256签名
     * 
     * @param data 原始数据
     * @param key 密钥
     * @return HMAC-SHA256签名（十六进制字符串）
     */
    public static String hmacSha256(byte[] data, byte[] key) {
        return hmac(data, key, HMAC_SHA256);
    }
    
    /**
     * HMAC-SHA512签名
     * 
     * @param data 原始数据
     * @param key 密钥
     * @return HMAC-SHA512签名（十六进制字符串）
     */
    public static String hmacSha512(String data, String key) {
        return hmac(data, key, HMAC_SHA512);
    }
    
    /**
     * HMAC-SHA512签名
     * 
     * @param data 原始数据
     * @param key 密钥
     * @return HMAC-SHA512签名（十六进制字符串）
     */
    public static String hmacSha512(byte[] data, byte[] key) {
        return hmac(data, key, HMAC_SHA512);
    }
    
    /**
     * 通用HMAC签名方法
     * 
     * @param data 原始数据
     * @param key 密钥
     * @param algorithm 算法名称
     * @return HMAC签名（十六进制字符串）
     */
    public static String hmac(String data, String key, String algorithm) {
        if (data == null || key == null) {
            return null;
        }
        return hmac(data.getBytes(StandardCharsets.UTF_8), key.getBytes(StandardCharsets.UTF_8), algorithm);
    }
    
    /**
     * 通用HMAC签名方法
     * 
     * @param data 原始数据
     * @param key 密钥
     * @param algorithm 算法名称
     * @return HMAC签名（十六进制字符串）
     */
    public static String hmac(byte[] data, byte[] key, String algorithm) {
        if (data == null || key == null) {
            return null;
        }
        
        try {
            SecretKeySpec secretKeySpec = new SecretKeySpec(key, algorithm);
            Mac mac = Mac.getInstance(algorithm);
            mac.init(secretKeySpec);
            byte[] signatureBytes = mac.doFinal(data);
            return bytesToHex(signatureBytes);
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            throw new RuntimeException("HMAC signature failed", e);
        }
    }
    
    // ==================== Base64编码 ====================
    
    /**
     * Base64编码
     * 
     * @param data 原始数据
     * @return Base64编码字符串
     */
    public static String base64Encode(String data) {
        if (data == null) {
            return null;
        }
        return base64Encode(data.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * Base64编码
     * 
     * @param data 原始数据
     * @return Base64编码字符串
     */
    public static String base64Encode(byte[] data) {
        if (data == null) {
            return null;
        }
        return Base64.getEncoder().encodeToString(data);
    }
    
    /**
     * Base64解码
     * 
     * @param encodedData Base64编码字符串
     * @return 原始数据
     */
    public static String base64Decode(String encodedData) {
        if (encodedData == null) {
            return null;
        }
        byte[] decodedBytes = Base64.getDecoder().decode(encodedData);
        return new String(decodedBytes, StandardCharsets.UTF_8);
    }
    
    /**
     * Base64解码为字节数组
     * 
     * @param encodedData Base64编码字符串
     * @return 原始数据字节数组
     */
    public static byte[] base64DecodeToBytes(String encodedData) {
        if (encodedData == null) {
            return null;
        }
        return Base64.getDecoder().decode(encodedData);
    }
    
    /**
     * URL安全的Base64编码
     * 
     * @param data 原始数据
     * @return URL安全的Base64编码字符串
     */
    public static String base64UrlEncode(String data) {
        if (data == null) {
            return null;
        }
        return base64UrlEncode(data.getBytes(StandardCharsets.UTF_8));
    }
    
    /**
     * URL安全的Base64编码
     * 
     * @param data 原始数据
     * @return URL安全的Base64编码字符串
     */
    public static String base64UrlEncode(byte[] data) {
        if (data == null) {
            return null;
        }
        return Base64.getUrlEncoder().withoutPadding().encodeToString(data);
    }
    
    /**
     * URL安全的Base64解码
     * 
     * @param encodedData URL安全的Base64编码字符串
     * @return 原始数据
     */
    public static String base64UrlDecode(String encodedData) {
        if (encodedData == null) {
            return null;
        }
        byte[] decodedBytes = Base64.getUrlDecoder().decode(encodedData);
        return new String(decodedBytes, StandardCharsets.UTF_8);
    }
    
    // ==================== 工具方法 ====================
    
    /**
     * 字节数组转十六进制字符串
     * 
     * @param bytes 字节数组
     * @return 十六进制字符串
     */
    public static String bytesToHex(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        
        char[] hexChars = new char[bytes.length * 2];
        for (int i = 0; i < bytes.length; i++) {
            int v = bytes[i] & 0xFF;
            hexChars[i * 2] = HEX_CHARS[v >>> 4];
            hexChars[i * 2 + 1] = HEX_CHARS[v & 0x0F];
        }
        return new String(hexChars);
    }
    
    /**
     * 十六进制字符串转字节数组
     * 
     * @param hex 十六进制字符串
     * @return 字节数组
     */
    public static byte[] hexToBytes(String hex) {
        if (hex == null || hex.length() % 2 != 0) {
            throw new IllegalArgumentException("Invalid hex string");
        }
        
        byte[] bytes = new byte[hex.length() / 2];
        for (int i = 0; i < bytes.length; i++) {
            int index = i * 2;
            int value = Integer.parseInt(hex.substring(index, index + 2), 16);
            bytes[i] = (byte) value;
        }
        return bytes;
    }
    
    /**
     * 生成随机字节数组
     * 
     * @param length 长度
     * @return 随机字节数组
     */
    public static byte[] generateRandomBytes(int length) {
        byte[] bytes = new byte[length];
        SECURE_RANDOM.nextBytes(bytes);
        return bytes;
    }
    
    /**
     * 生成随机十六进制字符串
     * 
     * @param length 字节长度
     * @return 随机十六进制字符串
     */
    public static String generateRandomHex(int length) {
        return bytesToHex(generateRandomBytes(length));
    }
    
    /**
     * 生成随机Base64字符串
     * 
     * @param length 字节长度
     * @return 随机Base64字符串
     */
    public static String generateRandomBase64(int length) {
        return base64Encode(generateRandomBytes(length));
    }
}
