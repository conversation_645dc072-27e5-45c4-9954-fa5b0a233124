package com.trading.common.utils;

import com.trading.common.constant.SystemConstants;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.util.Locale;

/**
 * 数字工具类
 * 提供数字相关的常用操作，特别针对金融交易场景
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public final class NumberUtils {
    
    /**
     * 默认精度
     */
    public static final int DEFAULT_SCALE = SystemConstants.DEFAULT_PRECISION_SCALE;
    
    /**
     * 价格精度
     */
    public static final int PRICE_SCALE = SystemConstants.PRICE_PRECISION_SCALE;
    
    /**
     * 数量精度
     */
    public static final int QUANTITY_SCALE = SystemConstants.QUANTITY_PRECISION_SCALE;
    
    /**
     * 百分比精度
     */
    public static final int PERCENTAGE_SCALE = 4;
    
    /**
     * 零值
     */
    public static final BigDecimal ZERO = BigDecimal.ZERO;
    
    /**
     * 一值
     */
    public static final BigDecimal ONE = BigDecimal.ONE;
    
    /**
     * 一百值
     */
    public static final BigDecimal HUNDRED = new BigDecimal("100");
    
    /**
     * 默认舍入模式
     */
    public static final RoundingMode DEFAULT_ROUNDING_MODE = RoundingMode.HALF_UP;
    
    private NumberUtils() {
        // 防止实例化
    }
    
    // ==================== BigDecimal创建 ====================
    
    /**
     * 安全创建BigDecimal（字符串版本）
     *
     * @param value 字符串数值
     * @return BigDecimal对象，null输入返回null
     */
    public static BigDecimal toBigDecimal(String value) {
        if (value == null) {
            return null;
        }

        String str = value.trim();
        if (str.isEmpty()) {
            return null;
        }

        try {
            return new BigDecimal(str);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid number format: " + str, e);
        }
    }

    /**
     * 安全创建BigDecimal
     *
     * @param value 数值
     * @return BigDecimal对象
     */
    public static BigDecimal toBigDecimal(Object value) {
        if (value == null) {
            return ZERO;
        }

        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }

        if (value instanceof String) {
            return toBigDecimal((String) value);
        }

        if (value instanceof Number) {
            return new BigDecimal(value.toString());
        }

        throw new IllegalArgumentException("Cannot convert to BigDecimal: " + value.getClass().getSimpleName());
    }
    
    /**
     * 安全创建BigDecimal，失败时返回默认值
     * 
     * @param value 数值
     * @param defaultValue 默认值
     * @return BigDecimal对象
     */
    public static BigDecimal toBigDecimalOrDefault(Object value, BigDecimal defaultValue) {
        try {
            return toBigDecimal(value);
        } catch (Exception e) {
            return defaultValue != null ? defaultValue : ZERO;
        }
    }
    
    // ==================== 数值比较 ====================
    
    /**
     * 检查是否为零
     * 
     * @param value 数值
     * @return true表示为零，false表示不为零
     */
    public static boolean isZero(BigDecimal value) {
        return value != null && value.compareTo(ZERO) == 0;
    }
    
    /**
     * 检查是否为正数
     * 
     * @param value 数值
     * @return true表示为正数，false表示不为正数
     */
    public static boolean isPositive(BigDecimal value) {
        return value != null && value.compareTo(ZERO) > 0;
    }
    
    /**
     * 检查是否为负数
     * 
     * @param value 数值
     * @return true表示为负数，false表示不为负数
     */
    public static boolean isNegative(BigDecimal value) {
        return value != null && value.compareTo(ZERO) < 0;
    }
    
    /**
     * 检查是否大于等于零
     * 
     * @param value 数值
     * @return true表示大于等于零，false表示小于零
     */
    public static boolean isNonNegative(BigDecimal value) {
        return value != null && value.compareTo(ZERO) >= 0;
    }
    
    /**
     * 检查是否小于等于零
     * 
     * @param value 数值
     * @return true表示小于等于零，false表示大于零
     */
    public static boolean isNonPositive(BigDecimal value) {
        return value != null && value.compareTo(ZERO) <= 0;
    }
    
    /**
     * 比较两个数值是否相等
     * 
     * @param value1 数值1
     * @param value2 数值2
     * @return true表示相等，false表示不相等
     */
    public static boolean equals(BigDecimal value1, BigDecimal value2) {
        if (value1 == null && value2 == null) {
            return true;
        }
        if (value1 == null || value2 == null) {
            return false;
        }
        return value1.compareTo(value2) == 0;
    }
    
    /**
     * 获取较大值
     * 
     * @param value1 数值1
     * @param value2 数值2
     * @return 较大值
     */
    public static BigDecimal max(BigDecimal value1, BigDecimal value2) {
        if (value1 == null) return value2;
        if (value2 == null) return value1;
        return value1.compareTo(value2) >= 0 ? value1 : value2;
    }
    
    /**
     * 获取较小值
     *
     * @param value1 数值1
     * @param value2 数值2
     * @return 较小值
     */
    public static BigDecimal min(BigDecimal value1, BigDecimal value2) {
        if (value1 == null) return value2;
        if (value2 == null) return value1;
        return value1.compareTo(value2) <= 0 ? value1 : value2;
    }

    // ==================== 比较操作 ====================

    /**
     * 比较两个BigDecimal是否相等（考虑精度）
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return true表示相等，false表示不相等
     */
    public static boolean isEqual(BigDecimal a, BigDecimal b) {
        if (a == null && b == null) {
            return true;
        }
        if (a == null || b == null) {
            return false;
        }
        return a.compareTo(b) == 0;
    }

    /**
     * 比较第一个数是否大于第二个数
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return true表示a大于b，false表示a小于等于b
     */
    public static boolean isGreaterThan(BigDecimal a, BigDecimal b) {
        if (a == null || b == null) {
            return false;
        }
        return a.compareTo(b) > 0;
    }

    /**
     * 比较第一个数是否小于第二个数
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return true表示a小于b，false表示a大于等于b
     */
    public static boolean isLessThan(BigDecimal a, BigDecimal b) {
        if (a == null || b == null) {
            return false;
        }
        return a.compareTo(b) < 0;
    }

    /**
     * 比较第一个数是否大于等于第二个数
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return true表示a大于等于b，false表示a小于b
     */
    public static boolean isGreaterThanOrEqual(BigDecimal a, BigDecimal b) {
        if (a == null || b == null) {
            return false;
        }
        return a.compareTo(b) >= 0;
    }

    /**
     * 比较第一个数是否小于等于第二个数
     *
     * @param a 第一个数
     * @param b 第二个数
     * @return true表示a小于等于b，false表示a大于b
     */
    public static boolean isLessThanOrEqual(BigDecimal a, BigDecimal b) {
        if (a == null || b == null) {
            return false;
        }
        return a.compareTo(b) <= 0;
    }

    // ==================== 数值运算 ====================
    
    /**
     * 安全加法
     * 
     * @param value1 数值1
     * @param value2 数值2
     * @return 相加结果
     */
    public static BigDecimal add(BigDecimal value1, BigDecimal value2) {
        BigDecimal v1 = value1 != null ? value1 : ZERO;
        BigDecimal v2 = value2 != null ? value2 : ZERO;
        return v1.add(v2);
    }
    
    /**
     * 安全减法
     * 
     * @param value1 被减数
     * @param value2 减数
     * @return 相减结果
     */
    public static BigDecimal subtract(BigDecimal value1, BigDecimal value2) {
        BigDecimal v1 = value1 != null ? value1 : ZERO;
        BigDecimal v2 = value2 != null ? value2 : ZERO;
        return v1.subtract(v2);
    }
    
    /**
     * 安全乘法
     * 
     * @param value1 数值1
     * @param value2 数值2
     * @return 相乘结果
     */
    public static BigDecimal multiply(BigDecimal value1, BigDecimal value2) {
        if (value1 == null || value2 == null) {
            return ZERO;
        }
        return value1.multiply(value2);
    }
    
    /**
     * 安全除法
     * 
     * @param dividend 被除数
     * @param divisor 除数
     * @param scale 精度
     * @param roundingMode 舍入模式
     * @return 相除结果
     */
    public static BigDecimal divide(BigDecimal dividend, BigDecimal divisor, int scale, RoundingMode roundingMode) {
        if (dividend == null) {
            return ZERO;
        }
        if (divisor == null || isZero(divisor)) {
            throw new ArithmeticException("Division by zero");
        }
        return dividend.divide(divisor, scale, roundingMode);
    }
    
    /**
     * 安全除法（使用默认精度和舍入模式）
     * 
     * @param dividend 被除数
     * @param divisor 除数
     * @return 相除结果
     */
    public static BigDecimal divide(BigDecimal dividend, BigDecimal divisor) {
        return divide(dividend, divisor, DEFAULT_SCALE, DEFAULT_ROUNDING_MODE);
    }
    
    // ==================== 精度处理 ====================
    
    /**
     * 设置精度
     * 
     * @param value 数值
     * @param scale 精度
     * @param roundingMode 舍入模式
     * @return 设置精度后的数值
     */
    public static BigDecimal setScale(BigDecimal value, int scale, RoundingMode roundingMode) {
        return value != null ? value.setScale(scale, roundingMode) : ZERO;
    }
    
    /**
     * 设置精度（使用默认舍入模式）
     * 
     * @param value 数值
     * @param scale 精度
     * @return 设置精度后的数值
     */
    public static BigDecimal setScale(BigDecimal value, int scale) {
        return setScale(value, scale, DEFAULT_ROUNDING_MODE);
    }
    
    /**
     * 设置价格精度
     * 
     * @param price 价格
     * @return 设置精度后的价格
     */
    public static BigDecimal setPriceScale(BigDecimal price) {
        return setScale(price, PRICE_SCALE);
    }
    
    /**
     * 设置数量精度
     * 
     * @param quantity 数量
     * @return 设置精度后的数量
     */
    public static BigDecimal setQuantityScale(BigDecimal quantity) {
        return setScale(quantity, QUANTITY_SCALE);
    }
    
    /**
     * 设置百分比精度
     *
     * @param percentage 百分比
     * @return 设置精度后的百分比
     */
    public static BigDecimal setPercentageScale(BigDecimal percentage) {
        return setScale(percentage, PERCENTAGE_SCALE);
    }

    // ==================== 格式化 ====================

    /**
     * 格式化数值为字符串
     *
     * @param value 数值
     * @param scale 小数位数
     * @return 格式化后的字符串
     */
    public static String format(BigDecimal value, int scale) {
        if (value == null) {
            return "0";
        }

        StringBuilder pattern = new StringBuilder("0");
        if (scale > 0) {
            pattern.append(".");
            for (int i = 0; i < scale; i++) {
                pattern.append("0");
            }
        }

        DecimalFormat formatter = new DecimalFormat(pattern.toString());
        return formatter.format(value);
    }

    /**
     * 格式化价格
     *
     * @param price 价格
     * @return 格式化后的价格字符串
     */
    public static String formatPrice(BigDecimal price) {
        return format(price, PRICE_SCALE);
    }

    /**
     * 格式化数量
     *
     * @param quantity 数量
     * @return 格式化后的数量字符串
     */
    public static String formatQuantity(BigDecimal quantity) {
        return format(quantity, QUANTITY_SCALE);
    }

    /**
     * 格式化百分比
     *
     * @param percentage 百分比（小数形式，如0.1234表示12.34%）
     * @return 格式化后的百分比字符串
     */
    public static String formatPercentage(BigDecimal percentage) {
        if (percentage == null) {
            return "0.00%";
        }

        BigDecimal percent = multiply(percentage, HUNDRED);
        return format(percent, 2) + "%";
    }

    /**
     * 格式化货币
     *
     * @param amount 金额
     * @param currencyCode 货币代码
     * @return 格式化后的货币字符串
     */
    public static String formatCurrency(BigDecimal amount, String currencyCode) {
        if (amount == null) {
            return "0.00";
        }

        NumberFormat formatter = NumberFormat.getCurrencyInstance(Locale.US);
        if (currencyCode != null && !currencyCode.trim().isEmpty()) {
            try {
                java.util.Currency currency = java.util.Currency.getInstance(currencyCode);
                formatter.setCurrency(currency);
            } catch (Exception e) {
                // 使用默认货币格式
            }
        }

        return formatter.format(amount);
    }

    /**
     * 格式化带千分位分隔符的数值
     *
     * @param value 数值
     * @param scale 小数位数
     * @return 格式化后的字符串
     */
    public static String formatWithComma(BigDecimal value, int scale) {
        if (value == null) {
            return "0";
        }

        StringBuilder pattern = new StringBuilder("#,##0");
        if (scale > 0) {
            pattern.append(".");
            for (int i = 0; i < scale; i++) {
                pattern.append("0");
            }
        }

        DecimalFormat formatter = new DecimalFormat(pattern.toString());
        return formatter.format(value);
    }

    // ==================== 百分比计算 ====================

    /**
     * 计算百分比变化
     *
     * @param oldValue 原值
     * @param newValue 新值
     * @return 百分比变化（小数形式）
     */
    public static BigDecimal calculatePercentageChange(BigDecimal oldValue, BigDecimal newValue) {
        if (oldValue == null || newValue == null || isZero(oldValue)) {
            return ZERO;
        }

        BigDecimal change = subtract(newValue, oldValue);
        return divide(change, oldValue);
    }

    /**
     * 计算百分比
     *
     * @param part 部分值
     * @param total 总值
     * @return 百分比（小数形式）
     */
    public static BigDecimal calculatePercentage(BigDecimal part, BigDecimal total) {
        if (part == null || total == null) {
            return ZERO;
        }

        if (isZero(total)) {
            throw new ArithmeticException("Division by zero in percentage calculation");
        }

        return divide(part, total);
    }

    /**
     * 根据百分比计算值
     *
     * @param total 总值
     * @param percentage 百分比（小数形式）
     * @return 计算结果
     */
    public static BigDecimal calculateByPercentage(BigDecimal total, BigDecimal percentage) {
        return multiply(total, percentage);
    }

    // ==================== 范围检查 ====================

    /**
     * 检查数值是否在指定范围内
     *
     * @param value 数值
     * @param min 最小值（包含）
     * @param max 最大值（包含）
     * @return true表示在范围内，false表示不在范围内
     */
    public static boolean isInRange(BigDecimal value, BigDecimal min, BigDecimal max) {
        if (value == null) {
            return false;
        }

        boolean aboveMin = min == null || value.compareTo(min) >= 0;
        boolean belowMax = max == null || value.compareTo(max) <= 0;

        return aboveMin && belowMax;
    }

    /**
     * 将数值限制在指定范围内
     *
     * @param value 数值
     * @param min 最小值
     * @param max 最大值
     * @return 限制后的数值
     */
    public static BigDecimal clamp(BigDecimal value, BigDecimal min, BigDecimal max) {
        if (value == null) {
            return min != null ? min : ZERO;
        }

        BigDecimal result = value;
        if (min != null && result.compareTo(min) < 0) {
            result = min;
        }
        if (max != null && result.compareTo(max) > 0) {
            result = max;
        }

        return result;
    }

    // ==================== 验证方法 ====================

    /**
     * 验证是否为有效数字
     *
     * @param str 字符串
     * @return true表示是有效数字，false表示不是有效数字
     */
    public static boolean isValidNumber(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }

        try {
            new BigDecimal(str.trim());
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证是否为有效价格
     *
     * @param price 价格
     * @return true表示是有效价格，false表示不是有效价格
     */
    public static boolean isValidPrice(BigDecimal price) {
        return price != null && isPositive(price);
    }

    /**
     * 验证是否为有效数量
     *
     * @param quantity 数量
     * @return true表示是有效数量，false表示不是有效数量
     */
    public static boolean isValidQuantity(BigDecimal quantity) {
        return quantity != null && isPositive(quantity);
    }

    /**
     * 获取绝对值
     *
     * @param value 数值
     * @return 绝对值
     */
    public static BigDecimal abs(BigDecimal value) {
        return value != null ? value.abs() : ZERO;
    }

    /**
     * 获取相反数
     *
     * @param value 数值
     * @return 相反数
     */
    public static BigDecimal negate(BigDecimal value) {
        return value != null ? value.negate() : ZERO;
    }

    /**
     * 计算变化百分比
     *
     * @param oldValue 旧值
     * @param newValue 新值
     * @return 变化百分比
     */
    public static BigDecimal calculateChangePercentage(BigDecimal oldValue, BigDecimal newValue) {
        if (oldValue == null || newValue == null || isZero(oldValue)) {
            return ZERO;
        }
        BigDecimal change = subtract(newValue, oldValue);
        return divide(change, oldValue).multiply(HUNDRED);
    }

    /**
     * 格式化数值为指定小数位数的字符串
     *
     * @param value 数值
     * @param scale 小数位数
     * @return 格式化后的字符串
     */
    public static String toFormattedString(BigDecimal value, int scale) {
        return format(value, scale);
    }

    /**
     * 转换为普通字符串（不使用科学计数法）
     *
     * @param value 数值
     * @return 普通字符串
     */
    public static String toPlainString(BigDecimal value) {
        return value != null ? value.toPlainString() : "0";
    }

    /**
     * 获取多个数值中的最大值
     *
     * @param values 数值数组
     * @return 最大值
     */
    public static BigDecimal max(BigDecimal... values) {
        if (values == null || values.length == 0) {
            return ZERO;
        }

        BigDecimal result = values[0];
        for (int i = 1; i < values.length; i++) {
            if (values[i] != null && (result == null || values[i].compareTo(result) > 0)) {
                result = values[i];
            }
        }
        return result != null ? result : ZERO;
    }

    /**
     * 获取多个数值中的最小值
     *
     * @param values 数值数组
     * @return 最小值
     */
    public static BigDecimal min(BigDecimal... values) {
        if (values == null || values.length == 0) {
            return ZERO;
        }

        BigDecimal result = values[0];
        for (int i = 1; i < values.length; i++) {
            if (values[i] != null && (result == null || values[i].compareTo(result) < 0)) {
                result = values[i];
            }
        }
        return result != null ? result : ZERO;
    }
}
