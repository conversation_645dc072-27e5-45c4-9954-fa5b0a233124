package com.trading.common.utils;

import okhttp3.*;
import okhttp3.logging.HttpLoggingInterceptor;

import java.io.IOException;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * HTTP工具类
 * 基于OkHttp提供HTTP请求的常用操作
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public final class HttpUtils {
    
    /**
     * 默认连接超时时间（秒）
     */
    public static final int DEFAULT_CONNECT_TIMEOUT = 10;
    
    /**
     * 默认读取超时时间（秒）
     */
    public static final int DEFAULT_READ_TIMEOUT = 30;
    
    /**
     * 默认写入超时时间（秒）
     */
    public static final int DEFAULT_WRITE_TIMEOUT = 30;
    
    /**
     * JSON媒体类型
     */
    public static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");
    
    /**
     * 表单媒体类型
     */
    public static final MediaType FORM_MEDIA_TYPE = MediaType.get("application/x-www-form-urlencoded");
    
    /**
     * 默认OkHttpClient实例
     */
    private static final OkHttpClient DEFAULT_CLIENT;
    
    static {
        DEFAULT_CLIENT = createDefaultClient();
    }
    
    private HttpUtils() {
        // 防止实例化
    }
    
    /**
     * 创建默认的OkHttpClient
     * 
     * @return OkHttpClient实例
     */
    private static OkHttpClient createDefaultClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(DEFAULT_CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(DEFAULT_READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(DEFAULT_WRITE_TIMEOUT, TimeUnit.SECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * 创建带日志的OkHttpClient
     * 
     * @param logLevel 日志级别
     * @return OkHttpClient实例
     */
    public static OkHttpClient createClientWithLogging(HttpLoggingInterceptor.Level logLevel) {
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
        loggingInterceptor.setLevel(logLevel);
        
        return new OkHttpClient.Builder()
                .connectTimeout(DEFAULT_CONNECT_TIMEOUT, TimeUnit.SECONDS)
                .readTimeout(DEFAULT_READ_TIMEOUT, TimeUnit.SECONDS)
                .writeTimeout(DEFAULT_WRITE_TIMEOUT, TimeUnit.SECONDS)
                .addInterceptor(loggingInterceptor)
                .retryOnConnectionFailure(true)
                .build();
    }
    
    /**
     * 创建自定义超时的OkHttpClient
     * 
     * @param connectTimeout 连接超时时间
     * @param readTimeout 读取超时时间
     * @param writeTimeout 写入超时时间
     * @return OkHttpClient实例
     */
    public static OkHttpClient createClientWithTimeout(Duration connectTimeout, Duration readTimeout, Duration writeTimeout) {
        return new OkHttpClient.Builder()
                .connectTimeout(connectTimeout)
                .readTimeout(readTimeout)
                .writeTimeout(writeTimeout)
                .retryOnConnectionFailure(true)
                .build();
    }
    
    // ==================== GET请求 ====================
    
    /**
     * 发送GET请求
     * 
     * @param url 请求URL
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String get(String url) throws IOException {
        return get(url, null);
    }
    
    /**
     * 发送GET请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String get(String url, Map<String, String> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder().url(url);
        
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }
        
        Request request = requestBuilder.build();
        
        try (Response response = DEFAULT_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response.code());
            }
            
            ResponseBody body = response.body();
            return body != null ? body.string() : null;
        }
    }
    
    /**
     * 发送GET请求（使用自定义客户端）
     * 
     * @param client OkHttpClient实例
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String get(OkHttpClient client, String url, Map<String, String> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder().url(url);
        
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }
        
        Request request = requestBuilder.build();
        
        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response.code());
            }
            
            ResponseBody body = response.body();
            return body != null ? body.string() : null;
        }
    }
    
    // ==================== POST请求 ====================
    
    /**
     * 发送POST请求（JSON数据）
     * 
     * @param url 请求URL
     * @param json JSON数据
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String postJson(String url, String json) throws IOException {
        return postJson(url, json, null);
    }
    
    /**
     * 发送POST请求（JSON数据）
     * 
     * @param url 请求URL
     * @param json JSON数据
     * @param headers 请求头
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String postJson(String url, String json, Map<String, String> headers) throws IOException {
        RequestBody body = RequestBody.create(json != null ? json : "", JSON_MEDIA_TYPE);
        return post(url, body, headers);
    }
    
    /**
     * 发送POST请求（表单数据）
     * 
     * @param url 请求URL
     * @param formData 表单数据
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String postForm(String url, Map<String, String> formData) throws IOException {
        return postForm(url, formData, null);
    }
    
    /**
     * 发送POST请求（表单数据）
     * 
     * @param url 请求URL
     * @param formData 表单数据
     * @param headers 请求头
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String postForm(String url, Map<String, String> formData, Map<String, String> headers) throws IOException {
        FormBody.Builder formBuilder = new FormBody.Builder();
        
        if (formData != null) {
            formData.forEach(formBuilder::add);
        }
        
        RequestBody body = formBuilder.build();
        return post(url, body, headers);
    }
    
    /**
     * 发送POST请求
     * 
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String post(String url, RequestBody body, Map<String, String> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);
        
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }
        
        Request request = requestBuilder.build();
        
        try (Response response = DEFAULT_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response.code());
            }
            
            ResponseBody responseBody = response.body();
            return responseBody != null ? responseBody.string() : null;
        }
    }
    
    // ==================== PUT请求 ====================
    
    /**
     * 发送PUT请求（JSON数据）
     * 
     * @param url 请求URL
     * @param json JSON数据
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String putJson(String url, String json) throws IOException {
        return putJson(url, json, null);
    }
    
    /**
     * 发送PUT请求（JSON数据）
     * 
     * @param url 请求URL
     * @param json JSON数据
     * @param headers 请求头
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String putJson(String url, String json, Map<String, String> headers) throws IOException {
        RequestBody body = RequestBody.create(json != null ? json : "", JSON_MEDIA_TYPE);
        return put(url, body, headers);
    }
    
    /**
     * 发送PUT请求
     * 
     * @param url 请求URL
     * @param body 请求体
     * @param headers 请求头
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String put(String url, RequestBody body, Map<String, String> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .put(body);
        
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }
        
        Request request = requestBuilder.build();
        
        try (Response response = DEFAULT_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response.code());
            }
            
            ResponseBody responseBody = response.body();
            return responseBody != null ? responseBody.string() : null;
        }
    }
    
    // ==================== DELETE请求 ====================
    
    /**
     * 发送DELETE请求
     * 
     * @param url 请求URL
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String delete(String url) throws IOException {
        return delete(url, null);
    }
    
    /**
     * 发送DELETE请求
     * 
     * @param url 请求URL
     * @param headers 请求头
     * @return 响应字符串
     * @throws IOException IO异常
     */
    public static String delete(String url, Map<String, String> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .delete();
        
        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }
        
        Request request = requestBuilder.build();
        
        try (Response response = DEFAULT_CLIENT.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Unexpected response code: " + response.code());
            }
            
            ResponseBody responseBody = response.body();
            return responseBody != null ? responseBody.string() : null;
        }
    }

    // ==================== 响应处理 ====================

    /**
     * HTTP响应结果类
     */
    public static class HttpResponse {
        private final int code;
        private final String message;
        private final String body;
        private final Headers headers;
        private final boolean successful;

        public HttpResponse(int code, String message, String body, Headers headers, boolean successful) {
            this.code = code;
            this.message = message;
            this.body = body;
            this.headers = headers;
            this.successful = successful;
        }

        public int getCode() { return code; }
        public String getMessage() { return message; }
        public String getBody() { return body; }
        public Headers getHeaders() { return headers; }
        public boolean isSuccessful() { return successful; }

        public String getHeader(String name) {
            return headers != null ? headers.get(name) : null;
        }
    }

    /**
     * 发送GET请求并返回完整响应
     *
     * @param url 请求URL
     * @param headers 请求头
     * @return HTTP响应
     * @throws IOException IO异常
     */
    public static HttpResponse getWithResponse(String url, Map<String, String> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder().url(url);

        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.build();

        try (Response response = DEFAULT_CLIENT.newCall(request).execute()) {
            ResponseBody body = response.body();
            String bodyString = body != null ? body.string() : null;

            return new HttpResponse(
                    response.code(),
                    response.message(),
                    bodyString,
                    response.headers(),
                    response.isSuccessful()
            );
        }
    }

    /**
     * 发送POST请求并返回完整响应
     *
     * @param url 请求URL
     * @param requestBody 请求体
     * @param headers 请求头
     * @return HTTP响应
     * @throws IOException IO异常
     */
    public static HttpResponse postWithResponse(String url, RequestBody requestBody, Map<String, String> headers) throws IOException {
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(requestBody);

        if (headers != null) {
            headers.forEach(requestBuilder::addHeader);
        }

        Request request = requestBuilder.build();

        try (Response response = DEFAULT_CLIENT.newCall(request).execute()) {
            ResponseBody body = response.body();
            String bodyString = body != null ? body.string() : null;

            return new HttpResponse(
                    response.code(),
                    response.message(),
                    bodyString,
                    response.headers(),
                    response.isSuccessful()
            );
        }
    }

    // ==================== 工具方法 ====================

    /**
     * 构建URL查询参数
     *
     * @param baseUrl 基础URL
     * @param params 参数Map
     * @return 完整URL
     */
    public static String buildUrlWithParams(String baseUrl, Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return baseUrl;
        }

        HttpUrl.Builder urlBuilder = HttpUrl.parse(baseUrl).newBuilder();
        params.forEach(urlBuilder::addQueryParameter);

        return urlBuilder.build().toString();
    }

    /**
     * 创建基本认证头
     *
     * @param username 用户名
     * @param password 密码
     * @return 认证头值
     */
    public static String createBasicAuthHeader(String username, String password) {
        String credentials = username + ":" + password;
        return "Basic " + java.util.Base64.getEncoder().encodeToString(credentials.getBytes());
    }

    /**
     * 创建Bearer Token认证头
     *
     * @param token 令牌
     * @return 认证头值
     */
    public static String createBearerAuthHeader(String token) {
        return "Bearer " + token;
    }

    /**
     * 检查HTTP状态码是否表示成功
     *
     * @param code HTTP状态码
     * @return true表示成功，false表示失败
     */
    public static boolean isSuccessful(int code) {
        return code >= 200 && code < 300;
    }

    /**
     * 检查HTTP状态码是否表示客户端错误
     *
     * @param code HTTP状态码
     * @return true表示客户端错误，false表示其他
     */
    public static boolean isClientError(int code) {
        return code >= 400 && code < 500;
    }

    /**
     * 检查HTTP状态码是否表示服务器错误
     *
     * @param code HTTP状态码
     * @return true表示服务器错误，false表示其他
     */
    public static boolean isServerError(int code) {
        return code >= 500 && code < 600;
    }

    /**
     * 安全执行HTTP请求
     *
     * @param request 请求对象
     * @param defaultValue 默认返回值
     * @return 响应字符串或默认值
     */
    public static String executeSafe(Request request, String defaultValue) {
        try (Response response = DEFAULT_CLIENT.newCall(request).execute()) {
            if (response.isSuccessful()) {
                ResponseBody body = response.body();
                return body != null ? body.string() : defaultValue;
            }
        } catch (IOException e) {
            // 记录日志但不抛出异常
        }
        return defaultValue;
    }

    /**
     * 获取默认的OkHttpClient实例
     *
     * @return OkHttpClient实例
     */
    public static OkHttpClient getDefaultClient() {
        return DEFAULT_CLIENT;
    }
}
