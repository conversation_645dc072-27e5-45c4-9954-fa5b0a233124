package com.trading.common.utils;

import com.trading.common.constant.SystemConstants;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.Date;

/**
 * 日期时间工具类
 * 提供日期时间相关的常用操作
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public final class DateTimeUtils {
    
    /**
     * 标准日期时间格式化器
     */
    public static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern(SystemConstants.DATETIME_FORMAT);
    
    /**
     * ISO日期时间格式化器
     */
    public static final DateTimeFormatter ISO_DATETIME_FORMATTER = DateTimeFormatter.ofPattern(SystemConstants.ISO_DATETIME_FORMAT);
    
    /**
     * 日期格式化器
     */
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern(SystemConstants.DATE_FORMAT);
    
    /**
     * 时间格式化器
     */
    public static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern(SystemConstants.TIME_FORMAT);
    
    /**
     * 时间戳格式化器
     */
    public static final DateTimeFormatter TIMESTAMP_FORMATTER = DateTimeFormatter.ofPattern(SystemConstants.TIMESTAMP_FORMAT);
    
    /**
     * UTC时区
     */
    public static final ZoneId UTC_ZONE = ZoneId.of(SystemConstants.DEFAULT_TIMEZONE);
    
    /**
     * 中国时区
     */
    public static final ZoneId CHINA_ZONE = ZoneId.of(SystemConstants.CHINA_TIMEZONE);
    
    private DateTimeUtils() {
        // 防止实例化
    }
    
    // ==================== 获取当前时间 ====================
    
    /**
     * 获取当前UTC时间
     *
     * @return 当前UTC时间
     */
    public static LocalDateTime nowUtc() {
        return LocalDateTime.now(UTC_ZONE);
    }

    /**
     * 获取当前UTC时间（别名方法）
     *
     * @return 当前UTC时间
     */
    public static LocalDateTime getCurrentUtcTime() {
        return nowUtc();
    }

    /**
     * 获取当前中国时间
     *
     * @return 当前中国时间
     */
    public static LocalDateTime nowChina() {
        return LocalDateTime.now(CHINA_ZONE);
    }

    /**
     * 获取当前中国时间（别名方法）
     *
     * @return 当前中国时间
     */
    public static LocalDateTime getCurrentChinaTime() {
        return nowChina();
    }
    
    /**
     * 获取当前时间戳（毫秒）
     * 
     * @return 当前时间戳
     */
    public static long currentTimeMillis() {
        return System.currentTimeMillis();
    }
    
    /**
     * 获取当前时间戳（秒）
     * 
     * @return 当前时间戳
     */
    public static long currentTimeSeconds() {
        return System.currentTimeMillis() / 1000;
    }
    
    // ==================== 格式化 ====================
    
    /**
     * 格式化日期时间为标准格式
     *
     * @param dateTime 日期时间
     * @return 格式化后的字符串
     */
    public static String format(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(DATETIME_FORMATTER) : null;
    }

    /**
     * 格式化日期时间（别名方法）
     *
     * @param dateTime 日期时间
     * @return 格式化后的字符串
     */
    public static String formatDateTime(LocalDateTime dateTime) {
        return format(dateTime);
    }

    /**
     * 格式化日期时间（自定义格式）
     *
     * @param dateTime 日期时间
     * @param pattern 格式模式
     * @return 格式化后的字符串
     */
    public static String formatDateTime(LocalDateTime dateTime, String pattern) {
        if (dateTime == null || pattern == null) {
            return null;
        }
        return dateTime.format(DateTimeFormatter.ofPattern(pattern));
    }
    
    /**
     * 格式化日期时间为ISO格式
     * 
     * @param dateTime 日期时间
     * @return 格式化后的字符串
     */
    public static String formatIso(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.atZone(UTC_ZONE).format(ISO_DATETIME_FORMATTER) : null;
    }
    
    /**
     * 格式化日期
     * 
     * @param date 日期
     * @return 格式化后的字符串
     */
    public static String formatDate(LocalDate date) {
        return date != null ? date.format(DATE_FORMATTER) : null;
    }
    
    /**
     * 格式化时间
     * 
     * @param time 时间
     * @return 格式化后的字符串
     */
    public static String formatTime(LocalTime time) {
        return time != null ? time.format(TIME_FORMATTER) : null;
    }
    
    /**
     * 格式化为时间戳字符串
     * 
     * @param dateTime 日期时间
     * @return 时间戳字符串
     */
    public static String formatTimestamp(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.format(TIMESTAMP_FORMATTER) : null;
    }
    
    /**
     * 使用自定义格式格式化日期时间
     * 
     * @param dateTime 日期时间
     * @param pattern 格式模式
     * @return 格式化后的字符串
     */
    public static String format(LocalDateTime dateTime, String pattern) {
        if (dateTime == null || pattern == null) {
            return null;
        }
        return dateTime.format(DateTimeFormatter.ofPattern(pattern));
    }
    
    // ==================== 解析 ====================
    
    /**
     * 解析标准格式的日期时间字符串
     * 
     * @param dateTimeStr 日期时间字符串
     * @return 日期时间对象
     */
    public static LocalDateTime parse(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeStr.trim(), DATETIME_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid datetime format: " + dateTimeStr, e);
        }
    }
    
    /**
     * 解析ISO格式的日期时间字符串
     * 
     * @param isoDateTimeStr ISO日期时间字符串
     * @return 日期时间对象
     */
    public static LocalDateTime parseIso(String isoDateTimeStr) {
        if (isoDateTimeStr == null || isoDateTimeStr.trim().isEmpty()) {
            return null;
        }
        try {
            return ZonedDateTime.parse(isoDateTimeStr.trim(), ISO_DATETIME_FORMATTER).toLocalDateTime();
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid ISO datetime format: " + isoDateTimeStr, e);
        }
    }
    
    /**
     * 解析日期字符串
     * 
     * @param dateStr 日期字符串
     * @return 日期对象
     */
    public static LocalDate parseDate(String dateStr) {
        if (dateStr == null || dateStr.trim().isEmpty()) {
            return null;
        }
        try {
            return LocalDate.parse(dateStr.trim(), DATE_FORMATTER);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format: " + dateStr, e);
        }
    }
    
    /**
     * 使用自定义格式解析日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串
     * @param pattern 格式模式
     * @return 日期时间对象
     */
    public static LocalDateTime parse(String dateTimeStr, String pattern) {
        if (dateTimeStr == null || pattern == null) {
            return null;
        }
        try {
            return LocalDateTime.parse(dateTimeStr.trim(), DateTimeFormatter.ofPattern(pattern));
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid datetime format: " + dateTimeStr + " with pattern: " + pattern, e);
        }
    }

    /**
     * 解析日期时间字符串（别名方法）
     *
     * @param dateTimeStr 日期时间字符串
     * @return 日期时间对象
     */
    public static LocalDateTime parseDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.trim().isEmpty()) {
            return null;
        }

        // 尝试常见格式
        try {
            // 尝试 "yyyy-MM-dd HH:mm:ss" 格式
            if (dateTimeStr.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                return LocalDateTime.parse(dateTimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            }
            // 尝试 ISO 格式
            return parseIso(dateTimeStr);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid datetime format: " + dateTimeStr, e);
        }
    }

    /**
     * 解析日期时间字符串（自定义格式）
     *
     * @param dateTimeStr 日期时间字符串
     * @param pattern 格式模式
     * @return 日期时间对象
     */
    public static LocalDateTime parseDateTime(String dateTimeStr, String pattern) {
        return parse(dateTimeStr, pattern);
    }
    
    // ==================== 时间戳转换 ====================
    
    /**
     * 时间戳（毫秒）转LocalDateTime
     * 
     * @param timestamp 时间戳（毫秒）
     * @return LocalDateTime对象
     */
    public static LocalDateTime fromTimestamp(long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), UTC_ZONE);
    }
    
    /**
     * 时间戳（秒）转LocalDateTime
     * 
     * @param timestampSeconds 时间戳（秒）
     * @return LocalDateTime对象
     */
    public static LocalDateTime fromTimestampSeconds(long timestampSeconds) {
        return LocalDateTime.ofInstant(Instant.ofEpochSecond(timestampSeconds), UTC_ZONE);
    }
    
    /**
     * LocalDateTime转时间戳（毫秒）
     *
     * @param dateTime 日期时间
     * @return 时间戳（毫秒）
     */
    public static long toTimestamp(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.atZone(UTC_ZONE).toInstant().toEpochMilli() : 0;
    }

    /**
     * LocalDateTime转时间戳（毫秒）（别名方法）
     *
     * @param dateTime 日期时间
     * @return 时间戳（毫秒）
     */
    public static long toMillisTimestamp(LocalDateTime dateTime) {
        return toTimestamp(dateTime);
    }

    /**
     * 时间戳（毫秒）转LocalDateTime（别名方法）
     *
     * @param timestamp 时间戳（毫秒）
     * @return LocalDateTime对象
     */
    public static LocalDateTime fromMillisTimestamp(long timestamp) {
        return fromTimestamp(timestamp);
    }

    /**
     * LocalDateTime转时间戳（秒）
     *
     * @param dateTime 日期时间
     * @return 时间戳（秒）
     */
    public static long toTimestampSeconds(LocalDateTime dateTime) {
        return dateTime != null ? dateTime.atZone(UTC_ZONE).toInstant().getEpochSecond() : 0;
    }
    
    // ==================== Date转换 ====================
    
    /**
     * Date转LocalDateTime
     * 
     * @param date Date对象
     * @return LocalDateTime对象
     */
    public static LocalDateTime fromDate(Date date) {
        return date != null ? LocalDateTime.ofInstant(date.toInstant(), UTC_ZONE) : null;
    }
    
    /**
     * LocalDateTime转Date
     * 
     * @param dateTime LocalDateTime对象
     * @return Date对象
     */
    public static Date toDate(LocalDateTime dateTime) {
        return dateTime != null ? Date.from(dateTime.atZone(UTC_ZONE).toInstant()) : null;
    }
    
    // ==================== 时间计算 ====================
    
    /**
     * 计算两个时间之间的差值（毫秒）
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 时间差（毫秒）
     */
    public static long betweenMillis(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.MILLIS.between(start, end);
    }
    
    /**
     * 计算两个时间之间的差值（秒）
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 时间差（秒）
     */
    public static long betweenSeconds(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.SECONDS.between(start, end);
    }
    
    /**
     * 计算两个时间之间的差值（分钟）
     * 
     * @param start 开始时间
     * @param end 结束时间
     * @return 时间差（分钟）
     */
    public static long betweenMinutes(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.MINUTES.between(start, end);
    }
    
    /**
     * 计算两个时间之间的差值（小时）
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 时间差（小时）
     */
    public static long betweenHours(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.HOURS.between(start, end);
    }

    /**
     * 计算两个时间之间的差值（小时）（别名方法）
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 时间差（小时）
     */
    public static long getHoursBetween(LocalDateTime start, LocalDateTime end) {
        return betweenHours(start, end);
    }

    /**
     * 计算两个时间之间的差值（分钟）（别名方法）
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 时间差（分钟）
     */
    public static long getMinutesBetween(LocalDateTime start, LocalDateTime end) {
        return betweenMinutes(start, end);
    }

    /**
     * 计算两个时间之间的差值（秒）（别名方法）
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 时间差（秒）
     */
    public static long getSecondsBetween(LocalDateTime start, LocalDateTime end) {
        return betweenSeconds(start, end);
    }
    
    /**
     * 计算两个时间之间的差值（天）
     *
     * @param start 开始时间
     * @param end 结束时间
     * @return 时间差（天）
     */
    public static long betweenDays(LocalDateTime start, LocalDateTime end) {
        if (start == null || end == null) {
            return 0;
        }
        return ChronoUnit.DAYS.between(start, end);
    }

    // ==================== 时间操作 ====================

    /**
     * 添加指定毫秒数
     *
     * @param dateTime 原始时间
     * @param millis 毫秒数
     * @return 新的时间
     */
    public static LocalDateTime plusMillis(LocalDateTime dateTime, long millis) {
        return dateTime != null ? dateTime.plus(millis, ChronoUnit.MILLIS) : null;
    }

    /**
     * 添加指定秒数
     *
     * @param dateTime 原始时间
     * @param seconds 秒数
     * @return 新的时间
     */
    public static LocalDateTime plusSeconds(LocalDateTime dateTime, long seconds) {
        return dateTime != null ? dateTime.plusSeconds(seconds) : null;
    }

    /**
     * 添加指定分钟数
     *
     * @param dateTime 原始时间
     * @param minutes 分钟数
     * @return 新的时间
     */
    public static LocalDateTime plusMinutes(LocalDateTime dateTime, long minutes) {
        return dateTime != null ? dateTime.plusMinutes(minutes) : null;
    }

    /**
     * 添加指定小时数
     *
     * @param dateTime 原始时间
     * @param hours 小时数
     * @return 新的时间
     */
    public static LocalDateTime plusHours(LocalDateTime dateTime, long hours) {
        return dateTime != null ? dateTime.plusHours(hours) : null;
    }

    /**
     * 添加指定天数
     *
     * @param dateTime 原始时间
     * @param days 天数
     * @return 新的时间
     */
    public static LocalDateTime plusDays(LocalDateTime dateTime, long days) {
        return dateTime != null ? dateTime.plusDays(days) : null;
    }

    /**
     * 减去指定小时数
     *
     * @param dateTime 原始时间
     * @param hours 小时数
     * @return 新的时间
     */
    public static LocalDateTime minusHours(LocalDateTime dateTime, int hours) {
        return dateTime != null ? dateTime.minusHours(hours) : null;
    }

    /**
     * 减去指定分钟数
     *
     * @param dateTime 原始时间
     * @param minutes 分钟数
     * @return 新的时间
     */
    public static LocalDateTime minusMinutes(LocalDateTime dateTime, int minutes) {
        return dateTime != null ? dateTime.minusMinutes(minutes) : null;
    }

    /**
     * 减去指定秒数
     *
     * @param dateTime 原始时间
     * @param seconds 秒数
     * @return 新的时间
     */
    public static LocalDateTime minusSeconds(LocalDateTime dateTime, int seconds) {
        return dateTime != null ? dateTime.minusSeconds(seconds) : null;
    }

    // ==================== 时间比较 ====================

    /**
     * 检查时间是否在指定范围内
     *
     * @param dateTime 要检查的时间
     * @param start 开始时间（包含）
     * @param end 结束时间（包含）
     * @return true表示在范围内，false表示不在范围内
     */
    public static boolean isBetween(LocalDateTime dateTime, LocalDateTime start, LocalDateTime end) {
        if (dateTime == null || start == null || end == null) {
            return false;
        }
        return !dateTime.isBefore(start) && !dateTime.isAfter(end);
    }

    /**
     * 检查是否为今天
     *
     * @param dateTime 要检查的时间
     * @return true表示是今天，false表示不是今天
     */
    public static boolean isToday(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        LocalDate today = LocalDate.now(UTC_ZONE);
        return dateTime.toLocalDate().equals(today);
    }

    /**
     * 检查是否为过去时间
     *
     * @param dateTime 要检查的时间
     * @return true表示是过去时间，false表示不是过去时间
     */
    public static boolean isPast(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        return dateTime.isBefore(nowUtc());
    }

    /**
     * 检查是否为未来时间
     *
     * @param dateTime 要检查的时间
     * @return true表示是未来时间，false表示不是未来时间
     */
    public static boolean isFuture(LocalDateTime dateTime) {
        if (dateTime == null) {
            return false;
        }
        return dateTime.isAfter(nowUtc());
    }

    /**
     * 比较两个时间是否相等
     *
     * @param dateTime1 第一个时间
     * @param dateTime2 第二个时间
     * @return true表示相等，false表示不相等
     */
    public static boolean isEqual(LocalDateTime dateTime1, LocalDateTime dateTime2) {
        if (dateTime1 == null && dateTime2 == null) {
            return true;
        }
        if (dateTime1 == null || dateTime2 == null) {
            return false;
        }
        return dateTime1.equals(dateTime2);
    }

    /**
     * 比较第一个时间是否在第二个时间之前
     *
     * @param dateTime1 第一个时间
     * @param dateTime2 第二个时间
     * @return true表示第一个时间在第二个时间之前，false表示不是
     */
    public static boolean isBefore(LocalDateTime dateTime1, LocalDateTime dateTime2) {
        if (dateTime1 == null || dateTime2 == null) {
            return false;
        }
        return dateTime1.isBefore(dateTime2);
    }

    /**
     * 比较第一个时间是否在第二个时间之后
     *
     * @param dateTime1 第一个时间
     * @param dateTime2 第二个时间
     * @return true表示第一个时间在第二个时间之后，false表示不是
     */
    public static boolean isAfter(LocalDateTime dateTime1, LocalDateTime dateTime2) {
        if (dateTime1 == null || dateTime2 == null) {
            return false;
        }
        return dateTime1.isAfter(dateTime2);
    }

    // ==================== 时区转换 ====================

    /**
     * 将UTC时间转换为指定时区时间
     *
     * @param utcDateTime UTC时间
     * @param targetZone 目标时区
     * @return 目标时区时间
     */
    public static LocalDateTime convertFromUtc(LocalDateTime utcDateTime, ZoneId targetZone) {
        if (utcDateTime == null || targetZone == null) {
            return utcDateTime;
        }
        return utcDateTime.atZone(UTC_ZONE).withZoneSameInstant(targetZone).toLocalDateTime();
    }

    /**
     * 将指定时区时间转换为UTC时间
     *
     * @param dateTime 原始时间
     * @param sourceZone 原始时区
     * @return UTC时间
     */
    public static LocalDateTime convertToUtc(LocalDateTime dateTime, ZoneId sourceZone) {
        if (dateTime == null || sourceZone == null) {
            return dateTime;
        }
        return dateTime.atZone(sourceZone).withZoneSameInstant(UTC_ZONE).toLocalDateTime();
    }

    /**
     * 时区转换（通用方法）
     *
     * @param dateTime 原始时间
     * @param fromZone 源时区
     * @param toZone 目标时区
     * @return 转换后的时间
     */
    public static LocalDateTime convertTimezone(LocalDateTime dateTime, String fromZone, String toZone) {
        if (dateTime == null || fromZone == null || toZone == null) {
            return dateTime;
        }
        try {
            ZoneId sourceZone = ZoneId.of(fromZone);
            ZoneId targetZone = ZoneId.of(toZone);
            return dateTime.atZone(sourceZone).withZoneSameInstant(targetZone).toLocalDateTime();
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid timezone: " + fromZone + " or " + toZone, e);
        }
    }

    /**
     * 将UTC时间转换为中国时间
     *
     * @param utcDateTime UTC时间
     * @return 中国时间
     */
    public static LocalDateTime convertToChinaTime(LocalDateTime utcDateTime) {
        return convertFromUtc(utcDateTime, CHINA_ZONE);
    }

    /**
     * 将中国时间转换为UTC时间
     *
     * @param chinaDateTime 中国时间
     * @return UTC时间
     */
    public static LocalDateTime convertFromChinaTime(LocalDateTime chinaDateTime) {
        return convertToUtc(chinaDateTime, CHINA_ZONE);
    }

    // ==================== 工具方法 ====================

    /**
     * 获取一天的开始时间（00:00:00）
     *
     * @param date 日期
     * @return 一天的开始时间
     */
    public static LocalDateTime startOfDay(LocalDate date) {
        return date != null ? date.atStartOfDay() : null;
    }

    /**
     * 获取一天的结束时间（23:59:59.999999999）
     *
     * @param date 日期
     * @return 一天的结束时间
     */
    public static LocalDateTime endOfDay(LocalDate date) {
        return date != null ? date.atTime(LocalTime.MAX) : null;
    }

    /**
     * 获取当前月份的第一天
     *
     * @return 当前月份的第一天
     */
    public static LocalDate firstDayOfCurrentMonth() {
        return LocalDate.now(UTC_ZONE).withDayOfMonth(1);
    }

    /**
     * 获取当前月份的最后一天
     *
     * @return 当前月份的最后一天
     */
    public static LocalDate lastDayOfCurrentMonth() {
        LocalDate now = LocalDate.now(UTC_ZONE);
        return now.withDayOfMonth(now.lengthOfMonth());
    }

    /**
     * 安全解析日期时间字符串
     *
     * @param dateTimeStr 日期时间字符串
     * @param defaultValue 默认值
     * @return 解析结果或默认值
     */
    public static LocalDateTime parseOrDefault(String dateTimeStr, LocalDateTime defaultValue) {
        try {
            return parse(dateTimeStr);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 验证日期时间字符串格式
     *
     * @param dateTimeStr 日期时间字符串
     * @return true表示格式正确，false表示格式错误
     */
    public static boolean isValidDateTime(String dateTimeStr) {
        try {
            parse(dateTimeStr);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
