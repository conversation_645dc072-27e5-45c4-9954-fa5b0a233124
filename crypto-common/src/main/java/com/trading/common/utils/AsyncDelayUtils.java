package com.trading.common.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 异步延迟工具类
 * 提供虚拟线程友好的延迟操作，替换Thread.sleep
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class AsyncDelayUtils {

    private static final Logger log = LoggerFactory.getLogger(AsyncDelayUtils.class);
    
    private static final ScheduledExecutorService scheduler = 
            Executors.newScheduledThreadPool(2, Thread.ofVirtual().factory());
    
    /**
     * 异步延迟指定毫秒数
     * 
     * @param delayMs 延迟毫秒数
     * @return CompletableFuture<Void>
     */
    public static CompletableFuture<Void> delay(long delayMs) {
        if (delayMs <= 0) {
            return CompletableFuture.completedFuture(null);
        }
        
        CompletableFuture<Void> future = new CompletableFuture<>();
        scheduler.schedule(() -> future.complete(null), delayMs, TimeUnit.MILLISECONDS);
        return future;
    }
    
    /**
     * 异步延迟指定时间
     * 
     * @param duration 延迟时间
     * @return CompletableFuture<Void>
     */
    public static CompletableFuture<Void> delay(Duration duration) {
        return delay(duration.toMillis());
    }
    
    /**
     * 带指数退避的异步延迟
     * 
     * @param baseDelayMs 基础延迟毫秒数
     * @param attempt 尝试次数（从0开始）
     * @param maxDelayMs 最大延迟毫秒数
     * @return CompletableFuture<Void>
     */
    public static CompletableFuture<Void> exponentialBackoffDelay(long baseDelayMs, int attempt, long maxDelayMs) {
        long delayMs = Math.min(baseDelayMs * (1L << attempt), maxDelayMs);
        log.debug("指数退避延迟: baseDelay={}ms, attempt={}, actualDelay={}ms", baseDelayMs, attempt, delayMs);
        return delay(delayMs);
    }
    
    /**
     * 带抖动的异步延迟（避免惊群效应）
     * 
     * @param baseDelayMs 基础延迟毫秒数
     * @param jitterPercent 抖动百分比（0-100）
     * @return CompletableFuture<Void>
     */
    public static CompletableFuture<Void> delayWithJitter(long baseDelayMs, int jitterPercent) {
        if (jitterPercent < 0 || jitterPercent > 100) {
            throw new IllegalArgumentException("抖动百分比必须在0-100之间");
        }
        
        double jitterFactor = 1.0 + (Math.random() * jitterPercent / 100.0);
        long actualDelay = (long) (baseDelayMs * jitterFactor);
        
        log.debug("带抖动延迟: baseDelay={}ms, jitter={}%, actualDelay={}ms", 
                baseDelayMs, jitterPercent, actualDelay);
        return delay(actualDelay);
    }
    
    /**
     * 可中断的异步延迟
     * 
     * @param delayMs 延迟毫秒数
     * @return 可取消的CompletableFuture
     */
    public static CompletableFuture<Void> interruptibleDelay(long delayMs) {
        CompletableFuture<Void> future = new CompletableFuture<>();
        
        var scheduledFuture = scheduler.schedule(() -> {
            if (!future.isCancelled()) {
                future.complete(null);
            }
        }, delayMs, TimeUnit.MILLISECONDS);
        
        // 如果CompletableFuture被取消，也取消调度任务
        future.whenComplete((result, throwable) -> {
            if (future.isCancelled()) {
                scheduledFuture.cancel(false);
            }
        });
        
        return future;
    }
    
    /**
     * 安全的睡眠方法，支持中断
     * 用于需要保持同步语义的场景
     *
     * @param delayMs 延迟毫秒数
     * @return 是否成功完成延迟（false表示被中断）
     * @deprecated 建议使用 delay() 方法，此方法仅用于向后兼容
     */
    @Deprecated
    public static boolean safeSleep(long delayMs) {
        try {
            Thread.sleep(delayMs);
            return true;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.debug("延迟被中断: delayMs={}", delayMs);
            return false;
        }
    }
    
    /**
     * 关闭调度器（应用关闭时调用）
     */
    public static void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
