package com.trading.common.dto;

import com.trading.common.constant.SystemConstants;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 分页请求封装类
 * 提供标准的分页查询参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class PageRequest implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 页码（从1开始）
     */
    @Min(value = 1, message = "Page number must be greater than 0")
    private int pageNum = SystemConstants.DEFAULT_PAGE_NUM;
    
    /**
     * 每页大小
     */
    @Min(value = 1, message = "Page size must be greater than 0")
    @Max(value = SystemConstants.MAX_PAGE_SIZE, message = "Page size cannot exceed " + SystemConstants.MAX_PAGE_SIZE)
    private int pageSize = SystemConstants.DEFAULT_PAGE_SIZE;
    
    /**
     * 排序字段列表
     */
    private List<SortField> sorts = new ArrayList<>();
    
    /**
     * 搜索关键字
     */
    private String keyword;
    
    /**
     * 是否需要统计总数
     */
    private boolean needTotal = true;
    
    /**
     * 默认构造函数
     */
    public PageRequest() {
    }
    
    /**
     * 构造函数
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     */
    public PageRequest(int pageNum, int pageSize) {
        this.pageNum = Math.max(pageNum, 1);
        this.pageSize = Math.min(Math.max(pageSize, 1), SystemConstants.MAX_PAGE_SIZE);
    }
    
    /**
     * 构造函数
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param keyword 搜索关键字
     */
    public PageRequest(int pageNum, int pageSize, String keyword) {
        this(pageNum, pageSize);
        this.keyword = keyword;
    }
    
    /**
     * 创建分页请求
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @return 分页请求
     */
    public static PageRequest of(int pageNum, int pageSize) {
        return new PageRequest(pageNum, pageSize);
    }
    
    /**
     * 创建分页请求
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param keyword 搜索关键字
     * @return 分页请求
     */
    public static PageRequest of(int pageNum, int pageSize, String keyword) {
        return new PageRequest(pageNum, pageSize, keyword);
    }
    
    /**
     * 创建默认分页请求
     * 
     * @return 默认分页请求
     */
    public static PageRequest defaultRequest() {
        return new PageRequest();
    }
    
    /**
     * 添加排序字段
     * 
     * @param field 字段名
     * @param direction 排序方向
     * @return 当前对象
     */
    public PageRequest addSort(String field, SortDirection direction) {
        if (field != null && !field.trim().isEmpty()) {
            this.sorts.add(new SortField(field.trim(), direction));
        }
        return this;
    }
    
    /**
     * 添加升序排序字段
     * 
     * @param field 字段名
     * @return 当前对象
     */
    public PageRequest addSortAsc(String field) {
        return addSort(field, SortDirection.ASC);
    }
    
    /**
     * 添加降序排序字段
     * 
     * @param field 字段名
     * @return 当前对象
     */
    public PageRequest addSortDesc(String field) {
        return addSort(field, SortDirection.DESC);
    }
    
    /**
     * 清空排序字段
     * 
     * @return 当前对象
     */
    public PageRequest clearSorts() {
        this.sorts.clear();
        return this;
    }
    
    /**
     * 设置搜索关键字
     * 
     * @param keyword 搜索关键字
     * @return 当前对象
     */
    public PageRequest withKeyword(String keyword) {
        this.keyword = keyword;
        return this;
    }
    
    /**
     * 设置是否需要统计总数
     * 
     * @param needTotal 是否需要统计总数
     * @return 当前对象
     */
    public PageRequest withNeedTotal(boolean needTotal) {
        this.needTotal = needTotal;
        return this;
    }
    
    /**
     * 获取偏移量（从0开始）
     * 
     * @return 偏移量
     */
    public long getOffset() {
        return (long) (pageNum - 1) * pageSize;
    }
    
    /**
     * 检查是否有排序字段
     * 
     * @return true表示有排序字段，false表示无排序字段
     */
    public boolean hasSorts() {
        return sorts != null && !sorts.isEmpty();
    }
    
    /**
     * 检查是否有搜索关键字
     * 
     * @return true表示有搜索关键字，false表示无搜索关键字
     */
    public boolean hasKeyword() {
        return keyword != null && !keyword.trim().isEmpty();
    }
    
    /**
     * 获取清理后的搜索关键字
     * 
     * @return 清理后的搜索关键字
     */
    public String getCleanKeyword() {
        return hasKeyword() ? keyword.trim() : null;
    }
    
    /**
     * 验证分页参数
     * 
     * @throws IllegalArgumentException 参数无效时抛出
     */
    public void validate() {
        PageResult.validatePageParams(pageNum, pageSize);
    }
    
    /**
     * 排序字段内部类
     */
    @Data
    public static class SortField implements Serializable {
        
        private static final long serialVersionUID = 1L;
        
        /**
         * 字段名
         */
        private String field;
        
        /**
         * 排序方向
         */
        private SortDirection direction;
        
        /**
         * 构造函数
         * 
         * @param field 字段名
         * @param direction 排序方向
         */
        public SortField(String field, SortDirection direction) {
            this.field = field;
            this.direction = direction != null ? direction : SortDirection.ASC;
        }
        
        @Override
        public String toString() {
            return field + " " + direction.name();
        }
    }
    
    /**
     * 排序方向枚举
     */
    public enum SortDirection {
        /**
         * 升序
         */
        ASC,
        
        /**
         * 降序
         */
        DESC
    }
    
    @Override
    public String toString() {
        return String.format("PageRequest{pageNum=%d, pageSize=%d, keyword='%s', sorts=%d, needTotal=%s}", 
                pageNum, pageSize, keyword, sorts.size(), needTotal);
    }
}
