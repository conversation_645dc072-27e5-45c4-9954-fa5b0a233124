package com.trading.common.dto;

import com.trading.common.constant.SystemConstants;
import lombok.Data;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * 分页结果封装类
 * 提供标准的分页数据格式
 * 
 * @param <T> 数据类型
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class PageResult<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 当前页码（从1开始）
     */
    private int pageNum;
    
    /**
     * 每页大小
     */
    private int pageSize;
    
    /**
     * 总记录数
     */
    private long total;
    
    /**
     * 总页数
     */
    private int totalPages;
    
    /**
     * 当前页数据
     */
    private List<T> records;
    
    /**
     * 是否有上一页
     */
    private boolean hasPrevious;
    
    /**
     * 是否有下一页
     */
    private boolean hasNext;
    
    /**
     * 是否为第一页
     */
    private boolean isFirst;
    
    /**
     * 是否为最后一页
     */
    private boolean isLast;
    
    /**
     * 私有构造函数
     */
    private PageResult() {
    }
    
    /**
     * 构造函数
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param total 总记录数
     * @param records 数据列表
     */
    private PageResult(int pageNum, int pageSize, long total, List<T> records) {
        this.pageNum = Math.max(pageNum, 1);
        this.pageSize = Math.max(pageSize, 1);
        this.total = Math.max(total, 0);
        this.records = records != null ? records : Collections.emptyList();
        
        // 计算总页数
        this.totalPages = (int) Math.ceil((double) this.total / this.pageSize);
        this.totalPages = Math.max(this.totalPages, 0);
        
        // 计算分页状态
        this.hasPrevious = this.pageNum > 1;
        this.hasNext = this.pageNum < this.totalPages;
        this.isFirst = this.pageNum == 1;
        this.isLast = this.pageNum == this.totalPages || this.totalPages == 0;
    }
    
    /**
     * 创建分页结果
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param total 总记录数
     * @param records 数据列表
     * @param <T> 数据类型
     * @return 分页结果
     */
    public static <T> PageResult<T> of(int pageNum, int pageSize, long total, List<T> records) {
        return new PageResult<>(pageNum, pageSize, total, records);
    }
    
    /**
     * 创建空的分页结果
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @param <T> 数据类型
     * @return 空分页结果
     */
    public static <T> PageResult<T> empty(int pageNum, int pageSize) {
        return new PageResult<>(pageNum, pageSize, 0, Collections.emptyList());
    }
    
    /**
     * 创建默认空分页结果
     * 
     * @param <T> 数据类型
     * @return 默认空分页结果
     */
    public static <T> PageResult<T> empty() {
        return empty(SystemConstants.DEFAULT_PAGE_NUM, SystemConstants.DEFAULT_PAGE_SIZE);
    }
    
    /**
     * 创建单页结果
     * 
     * @param records 数据列表
     * @param <T> 数据类型
     * @return 单页结果
     */
    public static <T> PageResult<T> single(List<T> records) {
        List<T> data = records != null ? records : Collections.emptyList();
        return new PageResult<>(1, data.size(), data.size(), data);
    }
    
    /**
     * 获取当前页的记录数
     * 
     * @return 当前页记录数
     */
    public int getCurrentPageSize() {
        return records != null ? records.size() : 0;
    }
    
    /**
     * 检查是否有数据
     * 
     * @return true表示有数据，false表示无数据
     */
    public boolean hasData() {
        return records != null && !records.isEmpty();
    }
    
    /**
     * 检查是否为空结果
     * 
     * @return true表示空结果，false表示有数据
     */
    public boolean isEmpty() {
        return !hasData();
    }
    
    /**
     * 获取开始记录索引（从0开始）
     * 
     * @return 开始记录索引
     */
    public long getStartIndex() {
        return (long) (pageNum - 1) * pageSize;
    }
    
    /**
     * 获取结束记录索引（从0开始）
     * 
     * @return 结束记录索引
     */
    public long getEndIndex() {
        long start = getStartIndex();
        long end = start + getCurrentPageSize() - 1;
        return Math.min(end, total - 1);
    }
    
    /**
     * 获取上一页页码
     * 
     * @return 上一页页码，如果没有上一页则返回当前页码
     */
    public int getPreviousPageNum() {
        return hasPrevious ? pageNum - 1 : pageNum;
    }
    
    /**
     * 获取下一页页码
     * 
     * @return 下一页页码，如果没有下一页则返回当前页码
     */
    public int getNextPageNum() {
        return hasNext ? pageNum + 1 : pageNum;
    }
    
    /**
     * 转换数据类型
     * 
     * @param mapper 转换函数
     * @param <R> 目标数据类型
     * @return 转换后的分页结果
     */
    public <R> PageResult<R> map(java.util.function.Function<T, R> mapper) {
        if (mapper == null) {
            throw new IllegalArgumentException("Mapper function cannot be null");
        }
        
        List<R> mappedRecords = records.stream()
                .map(mapper)
                .collect(java.util.stream.Collectors.toList());
        
        return PageResult.of(pageNum, pageSize, total, mappedRecords);
    }
    
    /**
     * 获取分页信息摘要
     * 
     * @return 分页信息摘要
     */
    public String getSummary() {
        if (total == 0) {
            return "No records found";
        }
        
        long start = getStartIndex() + 1;
        long end = getEndIndex() + 1;
        
        return String.format("Showing %d-%d of %d records (Page %d of %d)", 
                start, end, total, pageNum, totalPages);
    }
    
    /**
     * 验证分页参数
     * 
     * @param pageNum 页码
     * @param pageSize 页大小
     * @throws IllegalArgumentException 参数无效时抛出
     */
    public static void validatePageParams(int pageNum, int pageSize) {
        if (pageNum < 1) {
            throw new IllegalArgumentException("Page number must be greater than 0");
        }
        if (pageSize < 1) {
            throw new IllegalArgumentException("Page size must be greater than 0");
        }
        if (pageSize > SystemConstants.MAX_PAGE_SIZE) {
            throw new IllegalArgumentException("Page size cannot exceed " + SystemConstants.MAX_PAGE_SIZE);
        }
    }
    
    @Override
    public String toString() {
        return String.format("PageResult{pageNum=%d, pageSize=%d, total=%d, totalPages=%d, currentSize=%d}", 
                pageNum, pageSize, total, totalPages, getCurrentPageSize());
    }
}
