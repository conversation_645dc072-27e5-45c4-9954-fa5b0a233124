package com.trading.common.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.trading.common.enums.TimeInterval;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * K线数据DTO
 * 用于传输K线（蜡烛图）数据
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class KlineData {
    
    @TableId(type = com.baomidou.mybatisplus.annotation.IdType.AUTO)
    private Long id;
    
    /**
     * 交易对符号
     */
    @JsonProperty("symbol")
    private String symbol;

    /**
     * 时间间隔
     */
    @JsonProperty("interval")
    private String interval;

    /**
     * 开盘时间
     */
    @JsonProperty("openTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime openTime;

    /**
     * 收盘时间
     */
    @JsonProperty("closeTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime closeTime;

    /**
     * 开盘价
     */
    @JsonProperty("openPrice")
    private BigDecimal openPrice;

    /**
     * 最高价
     */
    @JsonProperty("highPrice")
    private BigDecimal highPrice;

    /**
     * 最低价
     */
    @JsonProperty("lowPrice")
    private BigDecimal lowPrice;

    /**
     * 收盘价
     */
    @JsonProperty("closePrice")
    private BigDecimal closePrice;

    /**
     * 成交量
     */
    @JsonProperty("volume")
    private BigDecimal volume;

    /**
     * 成交额
     */
    @JsonProperty("quoteVolume")
    private BigDecimal quoteVolume;

    /**
     * 成交笔数
     */
    @JsonProperty("tradeCount")
    private Long tradeCount;
    
    /**
     * 主动买入成交量
     */
    @JsonProperty("takerBuyVolume")
    private BigDecimal takerBuyVolume;
    
    /**
     * 主动买入成交额
     */
    @JsonProperty("takerBuyQuoteVolume")
    private BigDecimal takerBuyQuoteVolume;
    
    
    @JsonProperty("latency")
    private Long latency;

    /**
     * 原始数据
     */
    @JsonProperty("rawData")
    private String rawData;

    /**
     * 数据创建时间
     */
    @JsonProperty("createdAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 数据更新时间
     */
    @JsonProperty("updatedAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    /**
     * 是否已完成（K线是否已结束）
     */
    @JsonProperty("isClosed")
    private Boolean isClosed;

    /**
     * 数据来源
     */
    @JsonProperty("source")
    private String source;

    /**
     * 数据类型
     */
    @JsonProperty("dataType")
    private String dataType;
    
    /**
     * 构造函数
     * 
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @param openTime 开盘时间
     * @param closeTime 收盘时间
     * @param openPrice 开盘价
     * @param highPrice 最高价
     * @param lowPrice 最低价
     * @param closePrice 收盘价
     * @param volume 成交量
     * @param quoteVolume 成交额
     * @param tradeCount 成交笔数
     */
    public KlineData(String symbol, String interval, LocalDateTime openTime, LocalDateTime closeTime,
                     BigDecimal openPrice, BigDecimal highPrice, BigDecimal lowPrice, BigDecimal closePrice,
                     BigDecimal volume, BigDecimal quoteVolume, Long tradeCount) {
        this();
        this.symbol = symbol;
        this.interval = interval;
        this.openTime = openTime;
        this.closeTime = closeTime;
        this.openPrice = openPrice;
        this.highPrice = highPrice;
        this.lowPrice = lowPrice;
        this.closePrice = closePrice;
        this.volume = volume;
        this.quoteVolume = quoteVolume;
        this.tradeCount = tradeCount;
    }
    
    /**
     * 计算价格变化
     * 
     * @return 价格变化（收盘价 - 开盘价）
     */
    public BigDecimal getPriceChange() {
        if (openPrice == null || closePrice == null) {
            return BigDecimal.ZERO;
        }
        return closePrice.subtract(openPrice);
    }
    
    /**
     * 计算价格变化百分比
     * 
     * @return 价格变化百分比
     */
    public BigDecimal getPriceChangePercent() {
        if (openPrice == null || closePrice == null || openPrice.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return getPriceChange().divide(openPrice, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
    }
    
    /**
     * 计算振幅
     * 
     * @return 振幅百分比
     */
    public BigDecimal getAmplitude() {
        if (openPrice == null || highPrice == null || lowPrice == null || openPrice.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal range = highPrice.subtract(lowPrice);
        return range.divide(openPrice, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
    }
    
    /**
     * 计算平均价格
     * 
     * @return 平均价格（OHLC的平均值）
     */
    public BigDecimal getAveragePrice() {
        if (openPrice == null || highPrice == null || lowPrice == null || closePrice == null) {
            return BigDecimal.ZERO;
        }
        return openPrice.add(highPrice).add(lowPrice).add(closePrice)
                .divide(new BigDecimal("4"), 8, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 计算典型价格
     * 
     * @return 典型价格（HLC的平均值）
     */
    public BigDecimal getTypicalPrice() {
        if (highPrice == null || lowPrice == null || closePrice == null) {
            return BigDecimal.ZERO;
        }
        return highPrice.add(lowPrice).add(closePrice)
                .divide(new BigDecimal("3"), 8, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 计算加权收盘价
     * 
     * @return 加权收盘价（HLCC的平均值）
     */
    public BigDecimal getWeightedClosePrice() {
        if (highPrice == null || lowPrice == null || closePrice == null) {
            return BigDecimal.ZERO;
        }
        return highPrice.add(lowPrice).add(closePrice.multiply(new BigDecimal("2")))
                .divide(new BigDecimal("4"), 8, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 计算成交均价
     * 
     * @return 成交均价
     */
    public BigDecimal getVwap() {
        if (volume == null || quoteVolume == null || volume.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return quoteVolume.divide(volume, 8, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 检查是否为上涨K线
     * 
     * @return true表示上涨，false表示下跌或平盘
     */
    public boolean isBullish() {
        if (openPrice == null || closePrice == null) {
            return false;
        }
        return closePrice.compareTo(openPrice) > 0;
    }
    
    /**
     * 检查是否为下跌K线
     * 
     * @return true表示下跌，false表示上涨或平盘
     */
    public boolean isBearish() {
        if (openPrice == null || closePrice == null) {
            return false;
        }
        return closePrice.compareTo(openPrice) < 0;
    }
    
    /**
     * 检查是否为十字星（开盘价等于收盘价）
     * 
     * @return true表示十字星，false表示非十字星
     */
    public boolean isDoji() {
        if (openPrice == null || closePrice == null) {
            return false;
        }
        return openPrice.compareTo(closePrice) == 0;
    }
    
    /**
     * 计算实体大小
     * 
     * @return 实体大小（开盘价和收盘价的差值绝对值）
     */
    public BigDecimal getBodySize() {
        if (openPrice == null || closePrice == null) {
            return BigDecimal.ZERO;
        }
        return closePrice.subtract(openPrice).abs();
    }
    
    /**
     * 计算上影线长度
     * 
     * @return 上影线长度
     */
    public BigDecimal getUpperShadow() {
        if (highPrice == null || openPrice == null || closePrice == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal bodyTop = openPrice.max(closePrice);
        return highPrice.subtract(bodyTop);
    }
    
    /**
     * 计算下影线长度
     * 
     * @return 下影线长度
     */
    public BigDecimal getLowerShadow() {
        if (lowPrice == null || openPrice == null || closePrice == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal bodyBottom = openPrice.min(closePrice);
        return bodyBottom.subtract(lowPrice);
    }
    
    /**
     * 更新数据时间戳
     */
    public void updateTimestamp() {
        this.updatedAt = LocalDateTime.now();
    }
    
    /**
     * 标记K线为已完成
     */
    public void markAsClosed() {
        this.isClosed = true;
        updateTimestamp();
    }
    
    /**
     * 验证K线数据的有效性
     * 
     * @return true表示有效，false表示无效
     */
    public boolean isValid() {
        if (openPrice == null || highPrice == null || lowPrice == null || closePrice == null) {
            return false;
        }
        
        // 检查价格逻辑关系
        if (highPrice.compareTo(openPrice) < 0 || highPrice.compareTo(closePrice) < 0 ||
            lowPrice.compareTo(openPrice) > 0 || lowPrice.compareTo(closePrice) > 0) {
            return false;
        }
        
        // 检查时间逻辑关系
        if (openTime != null && closeTime != null && openTime.isAfter(closeTime)) {
            return false;
        }
        
        return true;
    }
}
