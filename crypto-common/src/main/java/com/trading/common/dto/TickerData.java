package com.trading.common.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

// import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 24小时统计数据DTO
 * 用于传输24小时价格变动统计信息
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class TickerData {
    
    /**
     * 交易对符号
     */
    @JsonProperty("symbol")
    private String symbol;
    
    /**
     * 24小时价格变动
     */
    @JsonProperty("priceChange")
    private BigDecimal priceChange;
    
    /**
     * 24小时价格变动百分比
     */
    @JsonProperty("priceChangePercent")
    private BigDecimal priceChangePercent;
    
    /**
     * 加权平均价格
     */
    @JsonProperty("weightedAvgPrice")
    private BigDecimal weightedAvgPrice;
    
    /**
     * 24小时前价格
     */
    @JsonProperty("prevClosePrice")
    private BigDecimal prevClosePrice;
    
    /**
     * 最新价格
     */
    @JsonProperty("lastPrice")
    private BigDecimal lastPrice;
    
    /**
     * 最新成交量
     */
    @JsonProperty("lastQty")
    private BigDecimal lastQty;
    
    /**
     * 最佳买价
     */
    @JsonProperty("bidPrice")
    private BigDecimal bidPrice;
    
    /**
     * 最佳买量
     */
    @JsonProperty("bidQty")
    private BigDecimal bidQty;
    
    /**
     * 最佳卖价
     */
    @JsonProperty("askPrice")
    private BigDecimal askPrice;
    
    /**
     * 最佳卖量
     */
    @JsonProperty("askQty")
    private BigDecimal askQty;
    
    /**
     * 24小时开盘价
     */
    @JsonProperty("openPrice")
    private BigDecimal openPrice;
    
    /**
     * 24小时最高价
     */
    @JsonProperty("highPrice")
    private BigDecimal highPrice;
    
    /**
     * 24小时最低价
     */
    @JsonProperty("lowPrice")
    private BigDecimal lowPrice;
    
    /**
     * 24小时成交量
     */
    @JsonProperty("volume")
    private BigDecimal volume;
    
    /**
     * 24小时成交额
     */
    @JsonProperty("quoteVolume")
    private BigDecimal quoteVolume;
    
    /**
     * 统计开始时间
     */
    @JsonProperty("openTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime openTime;
    
    /**
     * 统计结束时间
     */
    @JsonProperty("closeTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime closeTime;
    
    /**
     * 首次交易ID
     */
    @JsonProperty("firstId")
    private Long firstId;
    
    /**
     * 最后交易ID
     */
    @JsonProperty("lastId")
    private Long lastId;
    
    /**
     * 24小时交易笔数
     */
    @JsonProperty("count")
    private Long count;
    
    /**
     * 数据创建时间
     */
    @JsonProperty("createdAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;
    
    /**
     * 默认构造函数
     */
    public TickerData() {
        this.createdAt = LocalDateTime.now();
    }
    
    /**
     * 构造函数
     * 
     * @param symbol 交易对符号
     */
    public TickerData(String symbol) {
        this();
        this.symbol = symbol;
    }
    
    /**
     * 计算买卖价差
     * 
     * @return 买卖价差
     */
    public BigDecimal getSpread() {
        if (bidPrice == null || askPrice == null) {
            return BigDecimal.ZERO;
        }
        return askPrice.subtract(bidPrice);
    }
    
    /**
     * 计算买卖价差百分比
     * 
     * @return 买卖价差百分比
     */
    public BigDecimal getSpreadPercent() {
        BigDecimal spread = getSpread();
        if (spread.compareTo(BigDecimal.ZERO) == 0 || bidPrice == null || bidPrice.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return spread.divide(bidPrice, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
    }
    
    /**
     * 计算中间价
     * 
     * @return 中间价
     */
    public BigDecimal getMidPrice() {
        if (bidPrice == null || askPrice == null) {
            return lastPrice != null ? lastPrice : BigDecimal.ZERO;
        }
        return bidPrice.add(askPrice).divide(new BigDecimal("2"), 8, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 计算24小时振幅
     * 
     * @return 振幅百分比
     */
    public BigDecimal getAmplitude() {
        if (highPrice == null || lowPrice == null || openPrice == null || openPrice.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        BigDecimal range = highPrice.subtract(lowPrice);
        return range.divide(openPrice, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
    }
    
    /**
     * 计算换手率（需要流通量参数）
     * 
     * @param circulatingSupply 流通量
     * @return 换手率百分比
     */
    public BigDecimal getTurnoverRate(BigDecimal circulatingSupply) {
        if (volume == null || circulatingSupply == null || circulatingSupply.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return volume.divide(circulatingSupply, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
    }
    
    /**
     * 计算平均成交价
     * 
     * @return 平均成交价
     */
    public BigDecimal getAvgPrice() {
        if (volume == null || quoteVolume == null || volume.compareTo(BigDecimal.ZERO) == 0) {
            return weightedAvgPrice != null ? weightedAvgPrice : BigDecimal.ZERO;
        }
        return quoteVolume.divide(volume, 8, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 检查是否为上涨
     * 
     * @return true表示上涨，false表示下跌或平盘
     */
    public boolean isRising() {
        return priceChange != null && priceChange.compareTo(BigDecimal.ZERO) > 0;
    }
    
    /**
     * 检查是否为下跌
     * 
     * @return true表示下跌，false表示上涨或平盘
     */
    public boolean isFalling() {
        return priceChange != null && priceChange.compareTo(BigDecimal.ZERO) < 0;
    }
    
    /**
     * 检查是否为平盘
     * 
     * @return true表示平盘，false表示有涨跌
     */
    public boolean isFlat() {
        return priceChange != null && priceChange.compareTo(BigDecimal.ZERO) == 0;
    }
    
    /**
     * 获取涨跌状态描述
     * 
     * @return 涨跌状态描述
     */
    public String getTrendDescription() {
        if (isRising()) {
            return "上涨";
        } else if (isFalling()) {
            return "下跌";
        } else {
            return "平盘";
        }
    }
    
    /**
     * 检查是否为活跃交易
     * 
     * @param volumeThreshold 成交量阈值
     * @return true表示活跃，false表示不活跃
     */
    public boolean isActiveTrading(BigDecimal volumeThreshold) {
        if (volumeThreshold == null || volume == null) {
            return false;
        }
        return volume.compareTo(volumeThreshold) >= 0;
    }
    
    /**
     * 检查是否为大幅波动
     * 
     * @param changeThreshold 变动阈值（百分比）
     * @return true表示大幅波动，false表示正常波动
     */
    public boolean isHighVolatility(BigDecimal changeThreshold) {
        if (changeThreshold == null || priceChangePercent == null) {
            return false;
        }
        return priceChangePercent.abs().compareTo(changeThreshold) >= 0;
    }
    
    /**
     * 获取价格变动等级
     * 
     * @return 价格变动等级（1-5，数值越大变动越大）
     */
    public int getVolatilityLevel() {
        if (priceChangePercent == null) {
            return 1;
        }
        
        BigDecimal absChange = priceChangePercent.abs();
        if (absChange.compareTo(new BigDecimal("10")) >= 0) {
            return 5; // 极大波动
        } else if (absChange.compareTo(new BigDecimal("5")) >= 0) {
            return 4; // 大幅波动
        } else if (absChange.compareTo(new BigDecimal("2")) >= 0) {
            return 3; // 中等波动
        } else if (absChange.compareTo(new BigDecimal("0.5")) >= 0) {
            return 2; // 小幅波动
        } else {
            return 1; // 微小波动
        }
    }
    
    /**
     * 获取市场情绪指标
     * 
     * @return 市场情绪指标（-1到1，负数表示悲观，正数表示乐观）
     */
    public BigDecimal getMarketSentiment() {
        if (priceChangePercent == null) {
            return BigDecimal.ZERO;
        }
        
        // 简单的情绪计算：基于价格变动百分比
        BigDecimal sentiment = priceChangePercent.divide(new BigDecimal("10"), 2, BigDecimal.ROUND_HALF_UP);
        
        // 限制在-1到1之间
        if (sentiment.compareTo(BigDecimal.ONE) > 0) {
            return BigDecimal.ONE;
        } else if (sentiment.compareTo(BigDecimal.ONE.negate()) < 0) {
            return BigDecimal.ONE.negate();
        }
        
        return sentiment;
    }
    
    /**
     * 检查数据是否有效
     * 
     * @return true表示有效，false表示无效
     */
    public boolean isValid() {
        if (symbol == null || symbol.trim().isEmpty()) {
            return false;
        }
        
        // 检查关键价格数据
        if (lastPrice == null || lastPrice.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        // 检查时间逻辑
        if (openTime != null && closeTime != null && openTime.isAfter(closeTime)) {
            return false;
        }
        
        // 检查价格逻辑
        if (highPrice != null && lowPrice != null && highPrice.compareTo(lowPrice) < 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取数据完整性评分
     * 
     * @return 完整性评分（0-100）
     */
    public int getCompletenessScore() {
        int score = 0;
        int totalFields = 20; // 总字段数
        
        if (symbol != null) score++;
        if (priceChange != null) score++;
        if (priceChangePercent != null) score++;
        if (weightedAvgPrice != null) score++;
        if (prevClosePrice != null) score++;
        if (lastPrice != null) score++;
        if (lastQty != null) score++;
        if (bidPrice != null) score++;
        if (bidQty != null) score++;
        if (askPrice != null) score++;
        if (askQty != null) score++;
        if (openPrice != null) score++;
        if (highPrice != null) score++;
        if (lowPrice != null) score++;
        if (volume != null) score++;
        if (quoteVolume != null) score++;
        if (openTime != null) score++;
        if (closeTime != null) score++;
        if (count != null) score++;
        if (createdAt != null) score++;
        
        return (score * 100) / totalFields;
    }
}
