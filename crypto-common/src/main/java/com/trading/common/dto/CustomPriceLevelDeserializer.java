package com.trading.common.dto;

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class CustomPriceLevelDeserializer extends JsonDeserializer<List<DepthData.PriceLevel>> {

    @Override
    public List<DepthData.PriceLevel> deserialize(JsonParser p, DeserializationContext ctxt)
            throws IOException, JsonProcessingException {
        ObjectMapper mapper = (ObjectMapper) p.getCodec();
        JsonNode node = mapper.readTree(p);
        List<DepthData.PriceLevel> priceLevels = new ArrayList<>();

        if (node.isArray()) {
            for (JsonNode arrayNode : node) {
                if (arrayNode.isArray() && arrayNode.size() == 2) {
                    BigDecimal price = new BigDecimal(arrayNode.get(0).asText());
                    BigDecimal quantity = new BigDecimal(arrayNode.get(1).asText());
                    priceLevels.add(new DepthData.PriceLevel(price, quantity));
                }
            }
        }
        return priceLevels;
    }
}
