package com.trading.common.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.trading.common.enums.OrderSide;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

// import javax.validation.constraints.NotNull;
// import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 交易数据DTO
 * 用于传输实时交易数据
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
@TableName("market_data")
public class TradeData {
    
    @TableId(type = com.baomidou.mybatisplus.annotation.IdType.AUTO)
    private Long id;
    
    /**
     * 交易ID
     */
    @JsonProperty("tradeId")
    @TableField("trade_id")
    private Long tradeId;
    
    /**
     * 交易对符号
     */
    @JsonProperty("symbol")
    private String symbol;

    /**
     * 成交价格
     */
    @JsonProperty("price")
    private BigDecimal price;

    /**
     * 成交数量
     */
    @JsonProperty("quantity")
    private BigDecimal quantity;
    
    /**
     * 成交金额
     */
    @JsonProperty("quoteQuantity")
    @TableField("quote_quantity")
    private BigDecimal quoteQuantity;

    /**
     * 成交金额 (同 quoteQuantity, 为兼容不同数据源)
     */
    @JsonProperty("amount")
    private BigDecimal amount;
    
    /**
     * 成交时间（入库到 trade_time 列）
     */
    @JsonProperty("tradeTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    @TableField("trade_time")
    private LocalDateTime tradeTime;

    /**
     * 数据时间戳
     */
    @JsonProperty("timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime timestamp;
     
    
    /**
     * 买方订单ID
     */
    @JsonProperty("buyerOrderId")
    private Long buyerOrderId;
    
    /**
     * 卖方订单ID
     */
    @JsonProperty("sellerOrderId")
    private Long sellerOrderId;
    
    /**
     * 主动方向（买入或卖出）
     */
    @JsonProperty("side")
    private OrderSide side;
    
    /**
     * 是否为买方主动成交
     */
    @JsonProperty("isBuyerMaker")
    private Boolean isBuyerMaker;
    
    /**
     * 是否为最佳价格成交
     */
    @JsonProperty("isBestMatch")
    private Boolean isBestMatch;
    
    /**
     * 手续费
     */
    @JsonProperty("commission")
    private BigDecimal commission;
    
    /**
     * 手续费资产
     */
    @JsonProperty("commissionAsset")
    private String commissionAsset;

    /**
     * 数据来源
     */
    @JsonProperty("source")
    private String source;

    /**
     * 数据类型
     */
    @JsonProperty("dataType")
    private String dataType;

    /**
     * 原始数据
     */
    @JsonProperty("rawData")
    private String rawData;

    /**
     * 元数据
     */
    @JsonProperty("metadata")
    private String metadata;

    /**
     * 数据质量分
     */
    @JsonProperty("qualityScore")
    private double qualityScore;

    /**
     * 数据版本
     */
    @JsonProperty("version")
    private String version;

    /**
     * 创建时间
     */
    @JsonProperty("createdAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonProperty("updatedAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;
    
    /**
     * 构造函数
     * 
     * @param tradeId 交易ID
     * @param symbol 交易对符号
     * @param price 成交价格
     * @param quantity 成交数量
     * @param tradeTime 成交时间
     * @param side 主动方向
     */
    public TradeData(Long tradeId, String symbol, BigDecimal price, BigDecimal quantity, 
                     LocalDateTime tradeTime, OrderSide side) {
        this();
        this.tradeId = tradeId;
        this.symbol = symbol;
        this.price = price;
        this.quantity = quantity;
        this.tradeTime = tradeTime;
        this.side = side;
        this.quoteQuantity = price.multiply(quantity);
    }
    
    /**
     * 构造函数（完整参数）
     * 
     * @param tradeId 交易ID
     * @param symbol 交易对符号
     * @param price 成交价格
     * @param quantity 成交数量
     * @param quoteQuantity 成交金额
     * @param tradeTime 成交时间
     * @param buyerOrderId 买方订单ID
     * @param sellerOrderId 卖方订单ID
     * @param side 主动方向
     * @param isBuyerMaker 是否为买方主动成交
     */
    public TradeData(Long tradeId, String symbol, BigDecimal price, BigDecimal quantity, 
                     BigDecimal quoteQuantity, LocalDateTime tradeTime, Long buyerOrderId, 
                     Long sellerOrderId, OrderSide side, Boolean isBuyerMaker) {
        this();
        this.tradeId = tradeId;
        this.symbol = symbol;
        this.price = price;
        this.quantity = quantity;
        this.quoteQuantity = quoteQuantity;
        this.tradeTime = tradeTime;
        this.buyerOrderId = buyerOrderId;
        this.sellerOrderId = sellerOrderId;
        this.side = side;
        this.isBuyerMaker = isBuyerMaker;
    }
    
    /**
     * 计算成交金额（如果未设置）
     * 
     * @return 成交金额
     */
    public BigDecimal calculateQuoteQuantity() {
        if (price == null || quantity == null) {
            return BigDecimal.ZERO;
        }
        return price.multiply(quantity);
    }
    
    /**
     * 获取成交金额（自动计算）
     * 
     * @return 成交金额
     */
    public BigDecimal getQuoteQuantity() {
        if (quoteQuantity == null) {
            quoteQuantity = calculateQuoteQuantity();
        }
        return quoteQuantity;
    }
    
    /**
     * 检查是否为买入交易
     * 
     * @return true表示买入，false表示卖出
     */
    public boolean isBuyTrade() {
        return side == OrderSide.BUY;
    }
    
    /**
     * 检查是否为卖出交易
     * 
     * @return true表示卖出，false表示买入
     */
    public boolean isSellTrade() {
        return side == OrderSide.SELL;
    }
    
    /**
     * 检查是否为大额交易
     * 
     * @param threshold 大额交易阈值
     * @return true表示大额交易，false表示普通交易
     */
    public boolean isLargeTrade(BigDecimal threshold) {
        if (threshold == null) {
            return false;
        }
        BigDecimal amount = getQuoteQuantity();
        return amount != null && amount.compareTo(threshold) >= 0;
    }
    
    /**
     * 计算手续费率
     * 
     * @return 手续费率
     */
    public BigDecimal getCommissionRate() {
        if (commission == null || commission.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal amount = getQuoteQuantity();
        if (amount == null || amount.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        return commission.divide(amount, 6, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 获取交易方向描述
     * 
     * @return 交易方向描述
     */
    public String getSideDescription() {
        if (side == null) {
            return "未知";
        }
        return side.getName();
    }
    
    /**
     * 获取主动方描述
     * 
     * @return 主动方描述
     */
    public String getMakerDescription() {
        if (isBuyerMaker == null) {
            return "未知";
        }
        return isBuyerMaker ? "买方主动" : "卖方主动";
    }
    
    /**
     * 检查交易数据是否有效
     * 
     * @return true表示有效，false表示无效
     */
    public boolean isValid() {
        if (symbol == null || symbol.trim().isEmpty()) {
            return false;
        }
        
        if (price == null || price.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        if (quantity == null || quantity.compareTo(BigDecimal.ZERO) <= 0) {
            return false;
        }
        
        if (tradeTime == null) {
            return false;
        }
        
        // 验证成交金额
        BigDecimal calculatedAmount = calculateQuoteQuantity();
        if (quoteQuantity != null && quoteQuantity.compareTo(calculatedAmount) != 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取交易唯一标识
     * 
     * @return 交易唯一标识
     */
    public String getTradeKey() {
        return String.format("%s_%d_%d", symbol, tradeId, tradeTime.toLocalDate().toEpochDay());
    }
    
    /**
     * 比较两个交易的时间先后
     * 
     * @param other 另一个交易数据
     * @return 负数表示当前交易更早，正数表示更晚，0表示同时
     */
    public int compareTradeTime(TradeData other) {
        if (other == null || other.tradeTime == null) {
            return 1;
        }
        if (this.tradeTime == null) {
            return -1;
        }
        return this.tradeTime.compareTo(other.tradeTime);
    }
    
    /**
     * 获取交易的市场影响方向
     * 
     * @return 1表示推高价格，-1表示压低价格，0表示无影响
     */
    public int getMarketImpact() {
        if (side == null) {
            return 0;
        }
        
        // 主动买入推高价格，主动卖出压低价格
        return side == OrderSide.BUY ? 1 : -1;
    }
    
    /**
     * 转换为简化的交易信息字符串
     * 
     * @return 简化的交易信息
     */
    public String toSimpleString() {
        return String.format("%s %s@%s %s", 
                symbol, 
                quantity, 
                price, 
                side != null ? side.getName() : "未知");
    }
}
