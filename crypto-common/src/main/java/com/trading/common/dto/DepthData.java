package com.trading.common.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import com.baomidou.mybatisplus.annotation.TableField;
import com.trading.common.handler.JsonListTypeHandler;

// import javax.validation.constraints.NotEmpty;
// import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 深度数据DTO
 * 用于传输市场深度（订单簿）数据
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Data
public class DepthData {

    @TableId(type = com.baomidou.mybatisplus.annotation.IdType.AUTO)
    private Long id;
    
    /**
     * 交易对符号
     */
    @JsonProperty("symbol")
    private String symbol;

    /**
     * 最后更新ID
     */
    @JsonProperty("lastUpdateId")
    private Long lastUpdateId;

    /**
     * 买单列表（按价格从高到低排序）
     */
    @JsonProperty("bids")
    @TableField(typeHandler = JsonListTypeHandler.class)
    private List<PriceLevel> bids;

    /**
     * 卖单列表（按价格从低到高排序）
     */
    @JsonProperty("asks")
    @TableField(typeHandler = JsonListTypeHandler.class)
    private List<PriceLevel> asks;
    
    /**
     * 数据时间戳
     */
    @TableField(exist = false)
    @JsonProperty("timestamp")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss.SSS")
    private LocalDateTime timestamp;
    
    /**
     * 数据创建时间
     */
    @TableField(exist = false)
    @JsonProperty("createdAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 数据更新时间
     */
    @TableField(exist = false)
    @JsonProperty("updatedAt")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 数据来源
     */
    @JsonProperty("source")
    private String source;

    /**
     * 深度级别
     */
    @JsonProperty("levels")
    private Integer levels;
    
    /**
     * 构造函数
     *
     * @param symbol 交易对符号
     * @param lastUpdateId 最后更新ID
     * @param bids 买单列表
     * @param asks 卖单列表
     */
    public DepthData(String symbol, Long lastUpdateId, List<PriceLevel> bids, List<PriceLevel> asks) {
        this();
        this.symbol = symbol;
        this.lastUpdateId = lastUpdateId;
        this.bids = bids != null ? bids : new ArrayList<>();
        this.asks = asks != null ? asks : new ArrayList<>();

        // 设置默认值
        this.source = "binance";
        this.levels = Math.max(this.bids.size(), this.asks.size());
        this.timestamp = LocalDateTime.now();
    }
    
    /**
     * 价格档位内部类
     */
    @Data
    @JsonPropertyOrder({"price", "quantity"})
    public static class PriceLevel {
        
        /**
         * 价格
         */
        @JsonProperty(index = 0)
        private BigDecimal price;

        /**
         * 数量
         */
        @JsonProperty(index = 1)
        private BigDecimal quantity;
        
        /**
         * 默认构造函数
         */
        public PriceLevel() {
        }
        
        /**
         * 构造函数
         * 
         * @param price 价格
         * @param quantity 数量
         */
        public PriceLevel(BigDecimal price, BigDecimal quantity) {
            this.price = price;
            this.quantity = quantity;
        }
        
        /**
         * 构造函数（字符串参数）
         * 
         * @param price 价格字符串
         * @param quantity 数量字符串
         */
        public PriceLevel(String price, String quantity) {
            this.price = new BigDecimal(price);
            this.quantity = new BigDecimal(quantity);
        }
        
        /**
         * 计算总价值
         * 
         * @return 价格 * 数量
         */
        public BigDecimal getTotalValue() {
            if (price == null || quantity == null) {
                return BigDecimal.ZERO;
            }
            return price.multiply(quantity);
        }
        
        /**
         * 检查是否为有效档位
         * 
         * @return true表示有效，false表示无效
         */
        public boolean isValid() {
            return price != null && quantity != null && 
                   price.compareTo(BigDecimal.ZERO) > 0 && 
                   quantity.compareTo(BigDecimal.ZERO) > 0;
        }
    }
    
    /**
     * 获取最佳买价
     * 
     * @return 最佳买价，如果没有买单则返回null
     */
    public BigDecimal getBestBidPrice() {
        if (bids == null || bids.isEmpty()) {
            return null;
        }
        return bids.get(0).getPrice();
    }
    
    /**
     * 获取最佳卖价
     * 
     * @return 最佳卖价，如果没有卖单则返回null
     */
    public BigDecimal getBestAskPrice() {
        if (asks == null || asks.isEmpty()) {
            return null;
        }
        return asks.get(0).getPrice();
    }
    
    /**
     * 获取最佳买量
     * 
     * @return 最佳买量，如果没有买单则返回null
     */
    public BigDecimal getBestBidQuantity() {
        if (bids == null || bids.isEmpty()) {
            return null;
        }
        return bids.get(0).getQuantity();
    }
    
    /**
     * 获取最佳卖量
     * 
     * @return 最佳卖量，如果没有卖单则返回null
     */
    public BigDecimal getBestAskQuantity() {
        if (asks == null || asks.isEmpty()) {
            return null;
        }
        return asks.get(0).getQuantity();
    }
    
    /**
     * 计算买卖价差
     * 
     * @return 买卖价差，如果无法计算则返回null
     */
    public BigDecimal getSpread() {
        BigDecimal bestBid = getBestBidPrice();
        BigDecimal bestAsk = getBestAskPrice();
        
        if (bestBid == null || bestAsk == null) {
            return null;
        }
        
        return bestAsk.subtract(bestBid);
    }
    
    /**
     * 计算买卖价差百分比
     * 
     * @return 买卖价差百分比，如果无法计算则返回null
     */
    public BigDecimal getSpreadPercent() {
        BigDecimal spread = getSpread();
        BigDecimal bestBid = getBestBidPrice();
        
        if (spread == null || bestBid == null || bestBid.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        
        return spread.divide(bestBid, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
    }
    
    /**
     * 计算中间价
     * 
     * @return 中间价，如果无法计算则返回null
     */
    public BigDecimal getMidPrice() {
        BigDecimal bestBid = getBestBidPrice();
        BigDecimal bestAsk = getBestAskPrice();
        
        if (bestBid == null || bestAsk == null) {
            return null;
        }
        
        return bestBid.add(bestAsk).divide(new BigDecimal("2"), 8, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 计算买单总量
     * 
     * @return 买单总量
     */
    public BigDecimal getTotalBidQuantity() {
        if (bids == null || bids.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        return bids.stream()
                .map(PriceLevel::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    /**
     * 计算卖单总量
     * 
     * @return 卖单总量
     */
    public BigDecimal getTotalAskQuantity() {
        if (asks == null || asks.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        return asks.stream()
                .map(PriceLevel::getQuantity)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    /**
     * 计算买单总价值
     * 
     * @return 买单总价值
     */
    public BigDecimal getTotalBidValue() {
        if (bids == null || bids.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        return bids.stream()
                .map(PriceLevel::getTotalValue)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    /**
     * 计算卖单总价值
     * 
     * @return 卖单总价值
     */
    public BigDecimal getTotalAskValue() {
        if (asks == null || asks.isEmpty()) {
            return BigDecimal.ZERO;
        }
        
        return asks.stream()
                .map(PriceLevel::getTotalValue)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }
    
    /**
     * 计算买卖比例
     * 
     * @return 买单量/卖单量的比例
     */
    public BigDecimal getBidAskRatio() {
        BigDecimal totalBidQuantity = getTotalBidQuantity();
        BigDecimal totalAskQuantity = getTotalAskQuantity();
        
        if (totalAskQuantity.compareTo(BigDecimal.ZERO) == 0) {
            return null;
        }
        
        return totalBidQuantity.divide(totalAskQuantity, 4, BigDecimal.ROUND_HALF_UP);
    }
    
    /**
     * 获取指定深度的买单
     * 
     * @param depth 深度级别
     * @return 指定深度的买单列表
     */
    public List<PriceLevel> getBids(int depth) {
        if (bids == null || bids.isEmpty()) {
            return new ArrayList<>();
        }
        
        int size = Math.min(depth, bids.size());
        return new ArrayList<>(bids.subList(0, size));
    }
    
    /**
     * 获取指定深度的卖单
     * 
     * @param depth 深度级别
     * @return 指定深度的卖单列表
     */
    public List<PriceLevel> getAsks(int depth) {
        if (asks == null || asks.isEmpty()) {
            return new ArrayList<>();
        }
        
        int size = Math.min(depth, asks.size());
        return new ArrayList<>(asks.subList(0, size));
    }
    
    /**
     * 检查深度数据是否有效
     * 
     * @return true表示有效，false表示无效
     */
    public boolean isValid() {
        if (symbol == null || symbol.trim().isEmpty()) {
            return false;
        }
        
        if (bids == null || asks == null) {
            return false;
        }
        
        // 检查买单价格是否递减
        for (int i = 1; i < bids.size(); i++) {
            if (bids.get(i - 1).getPrice().compareTo(bids.get(i).getPrice()) <= 0) {
                return false;
            }
        }
        
        // 检查卖单价格是否递增
        for (int i = 1; i < asks.size(); i++) {
            if (asks.get(i - 1).getPrice().compareTo(asks.get(i).getPrice()) >= 0) {
                return false;
            }
        }
        
        // 检查买卖价格是否合理
        BigDecimal bestBid = getBestBidPrice();
        BigDecimal bestAsk = getBestAskPrice();
        if (bestBid != null && bestAsk != null && bestBid.compareTo(bestAsk) >= 0) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 更新时间戳
     */
    public void updateTimestamp() {
        this.timestamp = LocalDateTime.now();
        this.updatedAt = this.timestamp;
    }
}
