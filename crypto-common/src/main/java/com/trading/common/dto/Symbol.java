package com.trading.common.dto;

import java.util.Objects;

/**
 * 交易对符号
 * 标准化的交易对表示
 */
public class Symbol {
    
    private final String symbol;
    private final String baseAsset;
    private final String quoteAsset;

    public Symbol(String symbol) {
        this.symbol = symbol.toUpperCase();
        // 解析基础资产和报价资产
        if (symbol.endsWith("USDT")) {
            this.quoteAsset = "USDT";
            this.baseAsset = symbol.substring(0, symbol.length() - 4);
        } else if (symbol.endsWith("BUSD")) {
            this.quoteAsset = "BUSD";
            this.baseAsset = symbol.substring(0, symbol.length() - 4);
        } else if (symbol.endsWith("BTC")) {
            this.quoteAsset = "BTC";
            this.baseAsset = symbol.substring(0, symbol.length() - 3);
        } else if (symbol.endsWith("ETH")) {
            this.quoteAsset = "ETH";
            this.baseAsset = symbol.substring(0, symbol.length() - 3);
        } else {
            // 默认处理
            this.baseAsset = symbol;
            this.quoteAsset = "";
        }
    }

    public Symbol(String baseAsset, String quoteAsset) {
        this.baseAsset = baseAsset.toUpperCase();
        this.quoteAsset = quoteAsset.toUpperCase();
        this.symbol = this.baseAsset + this.quoteAsset;
    }

    public String getSymbol() {
        return symbol;
    }

    public String getBaseAsset() {
        return baseAsset;
    }

    public String getQuoteAsset() {
        return quoteAsset;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Symbol symbol1 = (Symbol) o;
        return Objects.equals(symbol, symbol1.symbol);
    }

    @Override
    public int hashCode() {
        return Objects.hash(symbol);
    }

    @Override
    public String toString() {
        return symbol;
    }

    /**
     * 创建Symbol实例的静态方法
     */
    public static Symbol of(String symbol) {
        return new Symbol(symbol);
    }

    public static Symbol of(String baseAsset, String quoteAsset) {
        return new Symbol(baseAsset, quoteAsset);
    }
}
