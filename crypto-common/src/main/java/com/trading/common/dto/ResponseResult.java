package com.trading.common.dto;

import com.trading.common.constant.ErrorCodes;
import com.trading.common.constant.SystemConstants;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 统一响应结果封装类
 * 提供标准的API响应格式
 * 
 * @param <T> 响应数据类型
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
public class ResponseResult<T> implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 响应状态码
     */
    private String code;
    
    /**
     * 响应消息
     */
    private String message;
    
    /**
     * 响应数据
     */
    private T data;
    
    /**
     * 响应时间戳
     */
    private LocalDateTime timestamp;
    
    /**
     * 请求追踪ID
     */
    private String traceId;
    
    /**
     * 是否成功
     */
    private boolean success;
    
    /**
     * 错误详情（仅在失败时返回）
     */
    private Object errorDetails;
    
    /**
     * 私有构造函数
     */
    private ResponseResult() {
        this.timestamp = LocalDateTime.now();
    }
    
    /**
     * 私有构造函数
     * 
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     * @param success 是否成功
     */
    private ResponseResult(String code, String message, T data, boolean success) {
        this();
        this.code = code;
        this.message = message;
        this.data = data;
        this.success = success;
    }
    
    /**
     * 私有构造函数
     * 
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     * @param success 是否成功
     * @param errorDetails 错误详情
     */
    private ResponseResult(String code, String message, T data, boolean success, Object errorDetails) {
        this(code, message, data, success);
        this.errorDetails = errorDetails;
    }
    
    /**
     * 创建成功响应
     * 
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ResponseResult<T> success() {
        return new ResponseResult<>(ErrorCodes.SUCCESS, SystemConstants.SUCCESS_MESSAGE, null, true);
    }
    
    /**
     * 创建成功响应
     * 
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ResponseResult<T> success(T data) {
        return new ResponseResult<>(ErrorCodes.SUCCESS, SystemConstants.SUCCESS_MESSAGE, data, true);
    }
    
    /**
     * 创建成功响应
     * 
     * @param message 响应消息
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应
     */
    public static <T> ResponseResult<T> success(String message, T data) {
        return new ResponseResult<>(ErrorCodes.SUCCESS, message, data, true);
    }
    
    /**
     * 创建失败响应
     * 
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ResponseResult<T> fail() {
        return new ResponseResult<>(SystemConstants.FAIL_CODE, SystemConstants.FAIL_MESSAGE, null, false);
    }
    
    /**
     * 创建失败响应
     * 
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ResponseResult<T> fail(String message) {
        return new ResponseResult<>(SystemConstants.FAIL_CODE, message, null, false);
    }
    
    /**
     * 创建失败响应
     * 
     * @param code 错误码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ResponseResult<T> fail(String code, String message) {
        return new ResponseResult<>(code, message, null, false);
    }
    
    /**
     * 创建失败响应
     *
     * @param code 错误码
     * @param message 错误消息
     * @param errorDetails 错误详情
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ResponseResult<T> fail(String code, String message, Object errorDetails) {
        return new ResponseResult<>(code, message, null, false, errorDetails);
    }

    /**
     * 创建失败响应（别名方法）
     *
     * @param code 错误码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 失败响应
     */
    public static <T> ResponseResult<T> failure(String code, String message) {
        return fail(code, message);
    }
    
    /**
     * 创建自定义响应
     * 
     * @param code 状态码
     * @param message 消息
     * @param data 数据
     * @param success 是否成功
     * @param <T> 数据类型
     * @return 自定义响应
     */
    public static <T> ResponseResult<T> of(String code, String message, T data, boolean success) {
        return new ResponseResult<>(code, message, data, success);
    }
    
    /**
     * 设置追踪ID
     * 
     * @param traceId 追踪ID
     * @return 当前对象
     */
    public ResponseResult<T> withTraceId(String traceId) {
        this.traceId = traceId;
        return this;
    }
    
    /**
     * 设置错误详情
     * 
     * @param errorDetails 错误详情
     * @return 当前对象
     */
    public ResponseResult<T> withErrorDetails(Object errorDetails) {
        this.errorDetails = errorDetails;
        return this;
    }
    
    /**
     * 检查响应是否成功
     * 
     * @return true表示成功，false表示失败
     */
    public boolean isSuccess() {
        return success;
    }
    
    /**
     * 检查响应是否失败
     * 
     * @return true表示失败，false表示成功
     */
    public boolean isFail() {
        return !success;
    }
    
    /**
     * 获取数据，如果为null则返回默认值
     * 
     * @param defaultValue 默认值
     * @return 数据或默认值
     */
    public T getDataOrDefault(T defaultValue) {
        return data != null ? data : defaultValue;
    }
    
    /**
     * 检查是否有数据
     * 
     * @return true表示有数据，false表示无数据
     */
    public boolean hasData() {
        return data != null;
    }
    
    /**
     * 检查是否有错误详情
     * 
     * @return true表示有错误详情，false表示无错误详情
     */
    public boolean hasErrorDetails() {
        return errorDetails != null;
    }
    
    @Override
    public String toString() {
        return String.format("ResponseResult{code='%s', message='%s', success=%s, timestamp=%s, traceId='%s'}", 
                code, message, success, timestamp, traceId);
    }
}
