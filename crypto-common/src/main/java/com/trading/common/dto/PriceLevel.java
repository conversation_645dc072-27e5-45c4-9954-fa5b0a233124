package com.trading.common.dto;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 价格档位数据传输对象
 * 用于表示订单簿中的买卖盘价格和数量
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class PriceLevel {
    
    /**
     * 价格
     */
    private BigDecimal price;
    
    /**
     * 数量
     */
    private BigDecimal quantity;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 默认构造函数
     */
    public PriceLevel() {
        this.createTime = LocalDateTime.now();
    }
    
    /**
     * 构造函数
     * 
     * @param price 价格
     * @param quantity 数量
     */
    public PriceLevel(BigDecimal price, BigDecimal quantity) {
        this.price = price;
        this.quantity = quantity;
        this.createTime = LocalDateTime.now();
    }
    
    /**
     * 构造函数
     * 
     * @param price 价格字符串
     * @param quantity 数量字符串
     */
    public PriceLevel(String price, String quantity) {
        this.price = new BigDecimal(price);
        this.quantity = new BigDecimal(quantity);
        this.createTime = LocalDateTime.now();
    }
    
    // Getters and Setters
    
    public BigDecimal getPrice() {
        return price;
    }
    
    public void setPrice(BigDecimal price) {
        this.price = price;
    }
    
    public BigDecimal getQuantity() {
        return quantity;
    }
    
    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    /**
     * 计算总价值（价格 * 数量）
     */
    public BigDecimal getTotalValue() {
        if (price == null || quantity == null) {
            return BigDecimal.ZERO;
        }
        return price.multiply(quantity);
    }
    
    /**
     * 检查价格档位是否有效
     */
    public boolean isValid() {
        return price != null && quantity != null && 
               price.compareTo(BigDecimal.ZERO) > 0 && 
               quantity.compareTo(BigDecimal.ZERO) > 0;
    }
    
    @Override
    public String toString() {
        return String.format("PriceLevel{price=%s, quantity=%s, totalValue=%s}", 
                           price, quantity, getTotalValue());
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        PriceLevel that = (PriceLevel) obj;
        
        if (price != null ? !price.equals(that.price) : that.price != null) return false;
        return quantity != null ? quantity.equals(that.quantity) : that.quantity == null;
    }
    
    @Override
    public int hashCode() {
        int result = price != null ? price.hashCode() : 0;
        result = 31 * result + (quantity != null ? quantity.hashCode() : 0);
        return result;
    }
}
