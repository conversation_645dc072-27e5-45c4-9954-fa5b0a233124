package com.trading.common.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.influxdb.annotations.Column;
import com.influxdb.annotations.Measurement;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.time.LocalDateTime;

/**
 * 数据质量统计实体类
 * 支持InfluxDB时序存储和MySQL关系存储
 * 对应data_quality_stats表
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@TableName("data_quality_stats")
@Measurement(name = "data_quality_stats")
public class DataQualityStats {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 交易对符号
     */
    @Column(tag = true)
    @TableField("symbol")
    private String symbol;

    /**
     * 数据类型
     */
    @Column(tag = true)
    @TableField("data_type")
    private String dataType;

    /**
     * 统计时间（小时）
     */
    @Column(timestamp = true)
    @TableField("date_hour")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss", timezone = "UTC")
    private LocalDateTime dateHour;

    /**
     * 总数据量
     */
    @Column
    @TableField("total_count")
    private Long totalCount;

    /**
     * 成功数量
     */
    @Column
    @TableField("success_count")
    private Long successCount;

    /**
     * 错误数量
     */
    @Column
    @TableField("error_count")
    private Long errorCount;

    /**
     * 平均延迟
     */
    @Column
    @TableField("avg_latency")
    private Double avgLatency;

    /**
     * 最大延迟
     */
    @Column
    @TableField("max_latency")
    private Long maxLatency;

    /**
     * 最低质量分数
     */
    @Column
    @TableField("min_quality_score")
    private Double minQualityScore;

    /**
     * 平均质量分数
     */
    @Column
    @TableField("avg_quality_score")
    private Double avgQualityScore;

    /**
     * 创建时间
     */
    @Column
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Instant updatedAt;

    /**
     * 创建数据质量统计实例
     */
    public static DataQualityStats create(String symbol, String dataType, LocalDateTime dateHour) {
        return DataQualityStats.builder()
                .symbol(symbol)
                .dataType(dataType)
                .dateHour(dateHour)
                .totalCount(0L)
                .successCount(0L)
                .errorCount(0L)
                .avgLatency(0.0)
                .maxLatency(0L)
                .minQualityScore(1.0)
                .avgQualityScore(1.0)
                .createdAt(Instant.now())
                .build();
    }

    /**
     * 获取时间戳（兼容方法）
     * 将dateHour转换为Instant
     */
    public Instant getTimestamp() {
        if (dateHour == null) {
            return null;
        }
        return dateHour.atZone(java.time.ZoneOffset.UTC).toInstant();
    }

    /**
     * 计算成功率
     */
    public double getSuccessRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        if (successCount == null) {
            return 0.0;
        }
        return (double) successCount / totalCount;
    }

    /**
     * 计算错误率
     */
    public double getErrorRate() {
        if (totalCount == null || totalCount == 0) {
            return 0.0;
        }
        if (errorCount == null) {
            return 0.0;
        }
        return (double) errorCount / totalCount;
    }

    /**
     * 增加总数量
     */
    public void incrementTotal() {
        if (totalCount == null) {
            totalCount = 0L;
        }
        totalCount++;
    }

    /**
     * 增加成功数量
     */
    public void incrementSuccess() {
        if (successCount == null) {
            successCount = 0L;
        }
        successCount++;
        incrementTotal();
    }

    /**
     * 增加错误数量
     */
    public void incrementError() {
        if (errorCount == null) {
            errorCount = 0L;
        }
        errorCount++;
        incrementTotal();
    }

    /**
     * 更新延迟统计
     */
    public void updateLatency(long latency) {
        if (avgLatency == null) {
            avgLatency = 0.0;
        }
        if (maxLatency == null) {
            maxLatency = 0L;
        }
        
        // 更新最大延迟
        if (latency > maxLatency) {
            maxLatency = latency;
        }
        
        // 更新平均延迟（简单移动平均）
        if (totalCount != null && totalCount > 0) {
            avgLatency = (avgLatency * (totalCount - 1) + latency) / totalCount;
        } else {
            avgLatency = (double) latency;
        }
    }

    /**
     * 更新质量分数统计
     */
    public void updateQualityScore(double qualityScore) {
        if (minQualityScore == null) {
            minQualityScore = 1.0;
        }
        if (avgQualityScore == null) {
            avgQualityScore = 1.0;
        }
        
        // 更新最低质量分数
        if (qualityScore < minQualityScore) {
            minQualityScore = qualityScore;
        }
        
        // 更新平均质量分数（简单移动平均）
        if (totalCount != null && totalCount > 0) {
            avgQualityScore = (avgQualityScore * (totalCount - 1) + qualityScore) / totalCount;
        } else {
            avgQualityScore = qualityScore;
        }
    }

    /**
     * 检查数据质量是否良好
     */
    public boolean isGoodQuality() {
        return getSuccessRate() >= 0.95 && 
               (avgQualityScore != null && avgQualityScore >= 0.8) &&
               (avgLatency != null && avgLatency <= 100.0);
    }

    /**
     * 获取统计唯一标识
     */
    public String getStatsKey() {
        return String.format("%s_%s_%s", symbol, dataType, dateHour.toString());
    }

    /**
     * 验证统计数据有效性
     */
    public boolean isValid() {
        return symbol != null && !symbol.trim().isEmpty() &&
               dataType != null && !dataType.trim().isEmpty() &&
               dateHour != null &&
               totalCount != null && totalCount >= 0;
    }
}