package com.trading.common.validator.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.Instant;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.HashMap;

/**
 * 验证报告模型
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationReport {
    
    /**
     * 报告ID
     */
    private String reportId;
    
    /**
     * 报告生成时间
     */
    @Builder.Default
    private Instant timestamp = Instant.now();
    
    /**
     * 总体验证状态
     */
    private boolean overallSuccess;
    
    /**
     * 验证结果列表
     */
    @Builder.Default
    private List<ValidationResult> results = new ArrayList<>();
    
    /**
     * 验证摘要
     */
    @Builder.Default
    private Map<String, Object> summary = new HashMap<>();
    
    /**
     * 性能统计
     */
    @Builder.Default
    private Map<String, Double> performanceStats = new HashMap<>();
    
    /**
     * 错误统计
     */
    @Builder.Default
    private Map<String, Integer> errorStats = new HashMap<>();
    
    /**
     * 建议和警告
     */
    @Builder.Default
    private List<String> recommendations = new ArrayList<>();
    
    /**
     * 添加验证结果
     */
    public ValidationReport addResult(ValidationResult result) {
        this.results.add(result);
        updateOverallSuccess();
        return this;
    }
    
    /**
     * 添加多个验证结果
     */
    public ValidationReport addResults(List<ValidationResult> results) {
        this.results.addAll(results);
        updateOverallSuccess();
        return this;
    }
    
    /**
     * 更新总体成功状态
     */
    private void updateOverallSuccess() {
        this.overallSuccess = results.stream().allMatch(ValidationResult::isSuccess);
    }
    
    /**
     * 添加摘要信息
     */
    public ValidationReport addSummary(String key, Object value) {
        this.summary.put(key, value);
        return this;
    }
    
    /**
     * 添加性能统计
     */
    public ValidationReport addPerformanceStat(String key, Double value) {
        this.performanceStats.put(key, value);
        return this;
    }
    
    /**
     * 添加错误统计
     */
    public ValidationReport addErrorStat(String key, Integer count) {
        this.errorStats.put(key, count);
        return this;
    }
    
    /**
     * 添加建议
     */
    public ValidationReport addRecommendation(String recommendation) {
        this.recommendations.add(recommendation);
        return this;
    }
    
    /**
     * 获取成功的验证数量
     */
    public long getSuccessCount() {
        return results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum();
    }
    
    /**
     * 获取失败的验证数量
     */
    public long getFailureCount() {
        return results.stream().mapToLong(r -> r.isSuccess() ? 0 : 1).sum();
    }
    
    /**
     * 获取总验证数量
     */
    public int getTotalCount() {
        return results.size();
    }
    
    /**
     * 获取成功率
     */
    public double getSuccessRate() {
        if (results.isEmpty()) {
            return 0.0;
        }
        return (double) getSuccessCount() / getTotalCount() * 100.0;
    }
    
    /**
     * 生成简要报告
     */
    public String generateSummaryText() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 数据存储验证报告 ===\n");
        sb.append("报告时间: ").append(timestamp).append("\n");
        sb.append("总体状态: ").append(overallSuccess ? "✅ 通过" : "❌ 失败").append("\n");
        sb.append("验证统计: ").append(getSuccessCount()).append("/").append(getTotalCount()).append(" 通过");
        sb.append(" (").append(String.format("%.1f", getSuccessRate())).append("%)\n");
        
        if (!recommendations.isEmpty()) {
            sb.append("\n建议:\n");
            recommendations.forEach(rec -> sb.append("- ").append(rec).append("\n"));
        }
        
        return sb.toString();
    }
}
