package com.trading.common.validator;

import com.trading.common.validator.model.ValidationResult;
import java.util.concurrent.CompletableFuture;

/**
 * 存储系统验证器接口
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public interface StorageValidator {
    
    /**
     * 获取验证器名称
     */
    String getName();
    
    /**
     * 验证存储系统连接状态
     */
    CompletableFuture<ValidationResult> validateConnection();
    
    /**
     * 验证数据写入功能
     */
    CompletableFuture<ValidationResult> validateWrite();
    
    /**
     * 验证数据读取功能
     */
    CompletableFuture<ValidationResult> validateRead();
    
    /**
     * 验证数据完整性
     */
    CompletableFuture<ValidationResult> validateDataIntegrity();
    
    /**
     * 验证性能指标
     */
    CompletableFuture<ValidationResult> validatePerformance();
    
    /**
     * 执行完整验证
     */
    default CompletableFuture<ValidationResult> validateAll() {
        return validateConnection()
                .thenCompose(connectionResult -> {
                    if (!connectionResult.isSuccess()) {
                        return CompletableFuture.completedFuture(connectionResult);
                    }
                    
                    return CompletableFuture.allOf(
                            validateWrite(),
                            validateRead(),
                            validateDataIntegrity(),
                            validatePerformance()
                    ).thenApply(v -> ValidationResult.success(getName(), "所有验证通过"));
                });
    }
}
