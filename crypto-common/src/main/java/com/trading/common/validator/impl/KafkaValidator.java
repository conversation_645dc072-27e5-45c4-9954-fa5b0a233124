package com.trading.common.validator.impl;

import com.trading.common.validator.StorageValidator;
import com.trading.common.validator.model.ValidationResult;
import lombok.RequiredArgsConstructor;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.DescribeTopicsResult;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.admin.TopicDescription;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;

/**
 * Kafka验证器实现
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
public class KafkaValidator implements StorageValidator {
    
    private static final Logger log = LoggerFactory.getLogger(KafkaValidator.class);
    
    @Value("${spring.kafka.bootstrap-servers:localhost:9092}")
    private String bootstrapServers;
    
    @Value("${trading.market-data.kafka.topic-prefix:crypto-market-data}")
    private String topicPrefix;
    
    @Override
    public String getName() {
        return "Kafka";
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateConnection() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                Properties adminProps = new Properties();
                adminProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
                adminProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 5000);
                
                try (AdminClient adminClient = AdminClient.create(adminProps)) {
                    // 获取集群信息
                    ListTopicsResult topicsResult = adminClient.listTopics();
                    Set<String> topics = topicsResult.names().get();
                    
                    long duration = System.currentTimeMillis() - startTime;
                    
                    Map<String, Object> details = new HashMap<>();
                    details.put("bootstrap_servers", bootstrapServers);
                    details.put("topics_count", topics.size());
                    details.put("response_time_ms", duration);
                    
                    // 检查市场数据相关的topics
                    List<String> marketDataTopics = topics.stream()
                            .filter(topic -> topic.contains("crypto-market-data") || topic.contains("market-data"))
                            .toList();
                    
                    details.put("market_data_topics", marketDataTopics);
                    details.put("market_data_topics_count", marketDataTopics.size());
                    
                    return ValidationResult.success(getName(), "Kafka连接正常")
                            .withType("connection")
                            .withDuration(duration)
                            .addDetail("details", details);
                }
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("Kafka连接验证失败", e);
                return ValidationResult.failure(getName(), "Kafka连接验证异常", e)
                        .withType("connection")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateWrite() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                Properties producerProps = new Properties();
                producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
                producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
                producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
                producerProps.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 5000);
                producerProps.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, 10000);
                
                try (KafkaProducer<String, String> producer = new KafkaProducer<>(producerProps)) {
                    String testTopic = topicPrefix + "-validation-test";
                    String testKey = "validation-test-" + System.currentTimeMillis();
                    String testValue = "{\"test\":\"validation\",\"timestamp\":" + System.currentTimeMillis() + "}";
                    
                    ProducerRecord<String, String> record = new ProducerRecord<>(testTopic, testKey, testValue);
                    
                    Future<RecordMetadata> future = producer.send(record);
                    RecordMetadata metadata = future.get();
                    
                    long duration = System.currentTimeMillis() - startTime;
                    
                    Map<String, Object> details = new HashMap<>();
                    details.put("test_topic", testTopic);
                    details.put("test_key", testKey);
                    details.put("partition", metadata.partition());
                    details.put("offset", metadata.offset());
                    details.put("write_time_ms", duration);
                    
                    return ValidationResult.success(getName(), "Kafka写入测试通过")
                            .withType("write")
                            .withDuration(duration)
                            .addDetail("details", details)
                            .addMetric("write_latency_ms", (double) duration);
                }
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("Kafka写入验证失败", e);
                return ValidationResult.failure(getName(), "Kafka写入测试失败", e)
                        .withType("write")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateRead() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                Properties consumerProps = new Properties();
                consumerProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
                consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, "validation-consumer-" + System.currentTimeMillis());
                consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
                consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class.getName());
                consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
                consumerProps.put(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG, false);
                consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 10000);
                
                try (KafkaConsumer<String, String> consumer = new KafkaConsumer<>(consumerProps)) {
                    // 查找市场数据相关的topics
                    List<String> topicsToSubscribe = findMarketDataTopics();
                    
                    if (topicsToSubscribe.isEmpty()) {
                        long duration = System.currentTimeMillis() - startTime;
                        return ValidationResult.failure(getName(), "Kafka读取测试失败", "未找到市场数据相关的topics")
                                .withType("read")
                                .withDuration(duration);
                    }
                    
                    consumer.subscribe(topicsToSubscribe);
                    
                    // 尝试读取消息（短时间轮询）
                    ConsumerRecords<String, String> records = consumer.poll(Duration.ofSeconds(2));
                    
                    long duration = System.currentTimeMillis() - startTime;
                    
                    Map<String, Object> details = new HashMap<>();
                    details.put("subscribed_topics", topicsToSubscribe);
                    details.put("records_count", records.count());
                    details.put("poll_time_ms", duration);
                    
                    // 统计每个topic的消息数量
                    Map<String, Integer> topicMessageCounts = new HashMap<>();
                    for (ConsumerRecord<String, String> record : records) {
                        topicMessageCounts.merge(record.topic(), 1, Integer::sum);
                    }
                    details.put("topic_message_counts", topicMessageCounts);
                    
                    return ValidationResult.success(getName(), "Kafka读取测试通过")
                            .withType("read")
                            .withDuration(duration)
                            .addDetail("details", details)
                            .addMetric("query_latency_ms", (double) duration);
                }
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("Kafka读取验证失败", e);
                return ValidationResult.failure(getName(), "Kafka读取测试失败", e)
                        .withType("read")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateDataIntegrity() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                Properties adminProps = new Properties();
                adminProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
                
                try (AdminClient adminClient = AdminClient.create(adminProps)) {
                    // 获取所有topics
                    Set<String> allTopics = adminClient.listTopics().names().get();
                    
                    // 过滤市场数据相关的topics
                    List<String> marketDataTopics = allTopics.stream()
                            .filter(topic -> topic.contains("crypto-market-data") || 
                                           topic.contains("market-data") ||
                                           topic.contains("kline") ||
                                           topic.contains("depth") ||
                                           topic.contains("trade") ||
                                           topic.contains("ticker"))
                            .toList();
                    
                    Map<String, Object> details = new HashMap<>();
                    details.put("total_topics", allTopics.size());
                    details.put("market_data_topics", marketDataTopics);
                    details.put("market_data_topics_count", marketDataTopics.size());
                    
                    // 获取topics详细信息
                    if (!marketDataTopics.isEmpty()) {
                        DescribeTopicsResult topicsDescription = adminClient.describeTopics(marketDataTopics);
                        Map<String, TopicDescription> topicDescriptions = topicsDescription.all().get();
                        
                        Map<String, Object> topicDetails = new HashMap<>();
                        int totalPartitions = 0;
                        
                        for (Map.Entry<String, TopicDescription> entry : topicDescriptions.entrySet()) {
                            TopicDescription desc = entry.getValue();
                            Map<String, Object> topicInfo = new HashMap<>();
                            topicInfo.put("partitions", desc.partitions().size());
                            topicInfo.put("replication_factor", desc.partitions().get(0).replicas().size());
                            topicDetails.put(entry.getKey(), topicInfo);
                            totalPartitions += desc.partitions().size();
                        }
                        
                        details.put("topic_details", topicDetails);
                        details.put("total_partitions", totalPartitions);
                    }
                    
                    long duration = System.currentTimeMillis() - startTime;
                    
                    // 计算数据完整性分数
                    double integrityScore = calculateIntegrityScore(marketDataTopics.size(), allTopics.size());
                    
                    return ValidationResult.success(getName(), "Kafka数据完整性验证通过")
                            .withType("data_integrity")
                            .withDuration(duration)
                            .addDetail("details", details)
                            .addMetric("data_integrity_score", integrityScore);
                }
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("Kafka数据完整性验证失败", e);
                return ValidationResult.failure(getName(), "Kafka数据完整性验证失败", e)
                        .withType("data_integrity")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validatePerformance() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                Properties producerProps = new Properties();
                producerProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
                producerProps.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
                producerProps.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
                producerProps.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
                producerProps.put(ProducerConfig.LINGER_MS_CONFIG, 5);
                
                try (KafkaProducer<String, String> producer = new KafkaProducer<>(producerProps)) {
                    String testTopic = topicPrefix + "-performance-test";
                    
                    // 性能测试：批量发送消息
                    long writeStartTime = System.currentTimeMillis();
                    List<Future<RecordMetadata>> futures = new ArrayList<>();
                    
                    for (int i = 0; i < 100; i++) {
                        String key = "perf-test-" + i;
                        String value = "{\"test\":\"performance\",\"index\":" + i + ",\"timestamp\":" + System.currentTimeMillis() + "}";
                        ProducerRecord<String, String> record = new ProducerRecord<>(testTopic, key, value);
                        futures.add(producer.send(record));
                    }
                    
                    // 等待所有消息发送完成
                    for (Future<RecordMetadata> future : futures) {
                        future.get();
                    }
                    
                    long writeTime = System.currentTimeMillis() - writeStartTime;
                    long totalDuration = System.currentTimeMillis() - startTime;
                    
                    Map<String, Object> details = new HashMap<>();
                    details.put("test_topic", testTopic);
                    details.put("messages_sent", 100);
                    details.put("batch_write_time_ms", writeTime);
                    details.put("total_test_time_ms", totalDuration);
                    
                    Map<String, Double> metrics = new HashMap<>();
                    metrics.put("write_throughput_msgs_per_sec", 100.0 / (writeTime / 1000.0));
                    metrics.put("avg_write_latency_ms", (double) writeTime / 100);
                    
                    double performanceScore = calculatePerformanceScore(writeTime);
                    metrics.put("performance_score", performanceScore);
                    
                    return ValidationResult.success(getName(), "Kafka性能测试通过")
                            .withType("performance")
                            .withDuration(totalDuration)
                            .addDetail("details", details)
                            .addMetric("performance_score", performanceScore);
                }
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("Kafka性能验证失败", e);
                return ValidationResult.failure(getName(), "Kafka性能测试失败", e)
                        .withType("performance")
                        .withDuration(duration);
            }
        });
    }
    
    /**
     * 查找市场数据相关的topics
     */
    private List<String> findMarketDataTopics() {
        try {
            Properties adminProps = new Properties();
            adminProps.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, bootstrapServers);
            
            try (AdminClient adminClient = AdminClient.create(adminProps)) {
                Set<String> allTopics = adminClient.listTopics().names().get();
                return allTopics.stream()
                        .filter(topic -> topic.contains("crypto-market-data") || topic.contains("market-data"))
                        .toList();
            }
        } catch (Exception e) {
            log.warn("查找市场数据topics失败", e);
            return Collections.emptyList();
        }
    }
    
    /**
     * 计算数据完整性分数
     */
    private double calculateIntegrityScore(int marketDataTopicsCount, int totalTopicsCount) {
        if (totalTopicsCount == 0) {
            return 0.0;
        }
        
        // 基于市场数据topics的数量和比例计算分数
        double score = 0.0;
        
        if (marketDataTopicsCount > 0) score += 50.0;
        if (marketDataTopicsCount >= 4) score += 30.0; // kline, depth, trade, ticker
        if (marketDataTopicsCount >= 6) score += 20.0; // 更多数据类型
        
        return Math.min(100.0, score);
    }
    
    /**
     * 计算性能分数
     */
    private double calculatePerformanceScore(long writeTime) {
        // 性能评分：100条消息写入时间 < 1000ms 为满分
        return Math.max(0, 100 - (writeTime / 10.0));
    }
}
