package com.trading.common.validator;

import com.trading.common.validator.impl.InfluxDBValidator;
import com.trading.common.validator.impl.KafkaValidator;
import com.trading.common.validator.impl.MySQLValidator;
import com.trading.common.validator.impl.RedisValidator;
import com.trading.common.validator.model.ValidationReport;
import com.trading.common.validator.model.ValidationResult;
import com.trading.common.validator.model.StorageHealthStatus;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 数据存储验证服务
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
public class DataStorageValidationService {
    
    private static final Logger log = LoggerFactory.getLogger(DataStorageValidationService.class);
    
    private final InfluxDBValidator influxDBValidator;
    private final RedisValidator redisValidator;
    private final KafkaValidator kafkaValidator;
    private final MySQLValidator mySQLValidator;

    @Autowired
    public DataStorageValidationService(
            @Autowired(required = false) InfluxDBValidator influxDBValidator,
            RedisValidator redisValidator,
            KafkaValidator kafkaValidator,
            MySQLValidator mySQLValidator) {
        this.influxDBValidator = influxDBValidator;
        this.redisValidator = redisValidator;
        this.kafkaValidator = kafkaValidator;
        this.mySQLValidator = mySQLValidator;
    }
    
    /**
     * 执行完整的数据存储验证
     */
    public CompletableFuture<ValidationReport> validateAllStorageSystems() {
        log.info("开始执行数据存储完整性验证...");
        
        return CompletableFuture.supplyAsync(() -> {
            String reportId = "validation-" + UUID.randomUUID().toString().substring(0, 8);
            ValidationReport report = ValidationReport.builder()
                    .reportId(reportId)
                    .timestamp(Instant.now())
                    .build();
            
            List<StorageValidator> validators = new ArrayList<>();
            if (influxDBValidator != null) {
                validators.add(influxDBValidator);
            }
            validators.add(redisValidator);
            validators.add(kafkaValidator);
            validators.add(mySQLValidator);
            
            // 并行执行所有验证器的完整验证
            List<CompletableFuture<ValidationResult>> validationFutures = validators.stream()
                    .map(validator -> {
                        log.info("开始验证存储系统: {}", validator.getName());
                        return validator.validateAll()
                                .whenComplete((result, throwable) -> {
                                    if (throwable != null) {
                                        log.error("验证器 {} 执行失败", validator.getName(), throwable);
                                    } else {
                                        log.info("验证器 {} 执行完成: {}", validator.getName(), 
                                                result.isSuccess() ? "成功" : "失败");
                                    }
                                });
                    })
                    .toList();
            
            // 等待所有验证完成
            CompletableFuture<Void> allValidations = CompletableFuture.allOf(
                    validationFutures.toArray(new CompletableFuture[0])
            );
            
            try {
                allValidations.join();
                
                // 收集所有验证结果
                List<ValidationResult> results = validationFutures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList());
                
                report.addResults(results);
                
                // 生成验证摘要
                generateValidationSummary(report, results);
                
                // 生成性能统计
                generatePerformanceStats(report, results);
                
                // 生成建议
                generateRecommendations(report, results);
                
                log.info("数据存储验证完成 - 报告ID: {}, 总体状态: {}, 成功率: {:.1f}%", 
                        reportId, report.isOverallSuccess() ? "通过" : "失败", report.getSuccessRate());
                
                return report;
                
            } catch (Exception e) {
                log.error("数据存储验证过程中发生异常", e);
                
                ValidationResult errorResult = ValidationResult.failure("ValidationService", 
                        "验证过程异常", e);
                report.addResult(errorResult);
                
                return report;
            }
        });
    }
    
    /**
     * 验证特定存储系统
     */
    public CompletableFuture<ValidationResult> validateSpecificStorage(String storageName) {
        log.info("开始验证特定存储系统: {}", storageName);
        
        StorageValidator validator = getValidatorByName(storageName);
        if (validator == null) {
            return CompletableFuture.completedFuture(
                    ValidationResult.failure("ValidationService", 
                            "未找到存储验证器", "不支持的存储系统: " + storageName)
            );
        }
        
        return validator.validateAll();
    }
    
    /**
     * 获取所有存储系统的健康状态
     */
    public CompletableFuture<List<StorageHealthStatus>> getStorageHealthStatus() {
        log.info("获取存储系统健康状态...");
        
        List<StorageValidator> validators = new ArrayList<>();
        if (influxDBValidator != null) {
            validators.add(influxDBValidator);
        }
        validators.add(redisValidator);
        validators.add(kafkaValidator);
        validators.add(mySQLValidator);
        
        List<CompletableFuture<StorageHealthStatus>> healthFutures = validators.stream()
                .map(this::getHealthStatusForValidator)
                .toList();
        
        return CompletableFuture.allOf(healthFutures.toArray(new CompletableFuture[0]))
                .thenApply(v -> healthFutures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toList()));
    }
    
    /**
     * 快速连接性检查
     */
    public CompletableFuture<Map<String, Boolean>> quickConnectivityCheck() {
        log.info("执行快速连接性检查...");
        
        List<StorageValidator> validators = new ArrayList<>();
        if (influxDBValidator != null) {
            validators.add(influxDBValidator);
        }
        validators.add(redisValidator);
        validators.add(kafkaValidator);
        validators.add(mySQLValidator);
        
        List<CompletableFuture<Map.Entry<String, Boolean>>> connectivityFutures = validators.stream()
                .map(validator -> validator.validateConnection()
                        .thenApply(result -> Map.entry(validator.getName(), result.isSuccess()))
                        .exceptionally(throwable -> Map.entry(validator.getName(), false)))
                .toList();
        
        return CompletableFuture.allOf(connectivityFutures.toArray(new CompletableFuture[0]))
                .thenApply(v -> connectivityFutures.stream()
                        .map(CompletableFuture::join)
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
    }
    
    /**
     * 根据名称获取验证器
     */
    private StorageValidator getValidatorByName(String name) {
        return switch (name.toLowerCase()) {
            case "influxdb" -> influxDBValidator;
            case "redis" -> redisValidator;
            case "kafka" -> kafkaValidator;
            case "mysql" -> mySQLValidator;
            default -> null;
        };
    }
    
    /**
     * 获取单个验证器的健康状态
     */
    private CompletableFuture<StorageHealthStatus> getHealthStatusForValidator(StorageValidator validator) {
        return validator.validateConnection()
                .thenCompose(connectionResult -> {
                    if (!connectionResult.isSuccess()) {
                        return CompletableFuture.completedFuture(
                                StorageHealthStatus.down(validator.getName(), connectionResult.getError())
                        );
                    }
                    
                    // 如果连接正常，进行快速的读写测试
                    return CompletableFuture.allOf(
                            validator.validateWrite(),
                            validator.validateRead()
                    ).thenApply(v -> {
                        // 这里可以根据具体的验证结果来确定健康状态
                        return StorageHealthStatus.healthy(validator.getName())
                                .addDetail("last_check", Instant.now())
                                .addMetric("response_time_ms", (double) connectionResult.getDuration());
                    });
                })
                .exceptionally(throwable -> 
                        StorageHealthStatus.critical(validator.getName(), throwable.getMessage())
                );
    }
    
    /**
     * 生成验证摘要
     */
    private void generateValidationSummary(ValidationReport report, List<ValidationResult> results) {
        Map<String, Object> summary = new HashMap<>();
        
        summary.put("total_validators", results.size());
        summary.put("successful_validators", results.stream().mapToLong(r -> r.isSuccess() ? 1 : 0).sum());
        summary.put("failed_validators", results.stream().mapToLong(r -> r.isSuccess() ? 0 : 1).sum());
        summary.put("success_rate", report.getSuccessRate());
        
        // 按验证器分组统计
        Map<String, String> validatorStatus = results.stream()
                .collect(Collectors.toMap(
                        ValidationResult::getValidatorName,
                        r -> r.isSuccess() ? "成功" : "失败"
                ));
        summary.put("validator_status", validatorStatus);
        
        // 统计验证类型
        Map<String, Long> typeStats = results.stream()
                .collect(Collectors.groupingBy(
                        r -> r.getValidationType() != null ? r.getValidationType() : "unknown",
                        Collectors.counting()
                ));
        summary.put("validation_type_stats", typeStats);
        
        report.addSummary("validation_summary", summary);
    }
    
    /**
     * 生成性能统计
     */
    private void generatePerformanceStats(ValidationReport report, List<ValidationResult> results) {
        // 计算平均响应时间
        double avgDuration = results.stream()
                .filter(r -> r.getDuration() != null)
                .mapToLong(ValidationResult::getDuration)
                .average()
                .orElse(0.0);
        
        report.addPerformanceStat("avg_validation_duration_ms", avgDuration);
        
        // 计算最大和最小响应时间
        results.stream()
                .filter(r -> r.getDuration() != null)
                .mapToLong(ValidationResult::getDuration)
                .max()
                .ifPresent(max -> report.addPerformanceStat("max_validation_duration_ms", (double) max));
        
        results.stream()
                .filter(r -> r.getDuration() != null)
                .mapToLong(ValidationResult::getDuration)
                .min()
                .ifPresent(min -> report.addPerformanceStat("min_validation_duration_ms", (double) min));
        
        // 统计各验证器的性能指标
        results.forEach(result -> {
            if (result.getMetrics() != null && !result.getMetrics().isEmpty()) {
                result.getMetrics().forEach((key, value) -> {
                    String metricKey = result.getValidatorName().toLowerCase() + "_" + key;
                    report.addPerformanceStat(metricKey, value);
                });
            }
        });
    }
    
    /**
     * 生成建议
     */
    private void generateRecommendations(ValidationReport report, List<ValidationResult> results) {
        List<String> recommendations = new ArrayList<>();
        
        // 检查失败的验证器
        List<ValidationResult> failedResults = results.stream()
                .filter(r -> !r.isSuccess())
                .toList();
        
        if (!failedResults.isEmpty()) {
            recommendations.add("发现 " + failedResults.size() + " 个存储系统验证失败，需要检查配置和连接状态");
            
            failedResults.forEach(result -> {
                recommendations.add("- " + result.getValidatorName() + ": " + result.getError());
            });
        }
        
        // 检查性能问题
        results.stream()
                .filter(r -> r.getDuration() != null && r.getDuration() > 5000)
                .forEach(result -> {
                    recommendations.add("- " + result.getValidatorName() + " 响应时间较长 (" + 
                            result.getDuration() + "ms)，建议检查网络和系统负载");
                });
        
        // 检查MySQL表缺失问题
        results.stream()
                .filter(r -> "MySQL".equals(r.getValidatorName()) && r.isSuccess())
                .forEach(result -> {
                    if (result.getDetails() != null && result.getDetails().containsKey("details")) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> details = (Map<String, Object>) result.getDetails().get("details");
                        if (details.containsKey("missing_tables_count")) {
                            Integer missingCount = (Integer) details.get("missing_tables_count");
                            if (missingCount != null && missingCount > 0) {
                                recommendations.add("- MySQL缺少 " + missingCount + " 个市场数据表，需要创建相应的数据表结构");
                            }
                        }
                    }
                });
        
        if (recommendations.isEmpty()) {
            recommendations.add("所有存储系统验证通过，系统运行正常");
        }
        
        recommendations.forEach(report::addRecommendation);
    }
}
