package com.trading.common.validator.impl;

import com.trading.common.validator.StorageValidator;
import com.trading.common.validator.model.ValidationResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * Redis验证器实现
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class RedisValidator implements StorageValidator {
    
    private static final Logger log = LoggerFactory.getLogger(RedisValidator.class);
    
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired(required = false)
    public void setRedisTemplate(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }
    
    @Override
    public String getName() {
        return "Redis";
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateConnection() {
        if (redisTemplate == null) {
            return CompletableFuture.completedFuture(ValidationResult.builder().success(true).validatorName(getName()).message("Redis not configured").validationType("skipped").build());
        }
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                // 执行PING命令测试连接
                String pong = redisTemplate.getConnectionFactory()
                        .getConnection()
                        .ping();
                
                long duration = System.currentTimeMillis() - startTime;
                
                if ("PONG".equals(pong)) {
                    Map<String, Object> details = new HashMap<>();
                    details.put("ping_response", pong);
                    details.put("response_time_ms", duration);
                    
                    return ValidationResult.success(getName(), "Redis连接正常")
                            .withType("connection")
                            .withDuration(duration)
                            .addDetail("details", details);
                } else {
                    return ValidationResult.failure(getName(), "Redis连接异常", "PING响应异常: " + pong)
                            .withType("connection")
                            .withDuration(duration);
                }
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("Redis连接验证失败", e);
                return ValidationResult.failure(getName(), "Redis连接验证异常", e)
                        .withType("connection")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateWrite() {
        if (redisTemplate == null) {
            return CompletableFuture.completedFuture(ValidationResult.builder().success(true).validatorName(getName()).message("Redis not configured").validationType("skipped").build());
        }
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                String testKey = "validation:test:write:" + System.currentTimeMillis();
                String testValue = "test_value_" + System.currentTimeMillis();
                
                // 写入测试数据
                redisTemplate.opsForValue().set(testKey, testValue, Duration.ofMinutes(1));
                
                // 验证写入结果
                String retrievedValue = (String) redisTemplate.opsForValue().get(testKey);
                
                long duration = System.currentTimeMillis() - startTime;
                
                if (testValue.equals(retrievedValue)) {
                    Map<String, Object> details = new HashMap<>();
                    details.put("test_key", testKey);
                    details.put("write_time_ms", duration);
                    details.put("ttl_seconds", 60);
                    
                    // 清理测试数据
                    redisTemplate.delete(testKey);
                    
                    return ValidationResult.success(getName(), "Redis写入测试通过")
                            .withType("write")
                            .withDuration(duration)
                            .addDetail("details", details)
                            .addMetric("write_latency_ms", (double) duration);
                } else {
                    return ValidationResult.failure(getName(), "Redis写入测试失败", 
                            "写入值与读取值不匹配: " + testValue + " != " + retrievedValue)
                            .withType("write")
                            .withDuration(duration);
                }
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("Redis写入验证失败", e);
                return ValidationResult.failure(getName(), "Redis写入测试异常", e)
                        .withType("write")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateRead() {
        if (redisTemplate == null) {
            return CompletableFuture.completedFuture(ValidationResult.builder().success(true).validatorName(getName()).message("Redis not configured").validationType("skipped").build());
        }
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                // 查询市场数据相关的键
                Set<String> marketDataKeys = redisTemplate.keys("market-data:*");
                Set<String> latestPriceKeys = redisTemplate.keys("latest-price:*");
                
                long duration = System.currentTimeMillis() - startTime;
                
                Map<String, Object> details = new HashMap<>();
                details.put("market_data_keys_count", marketDataKeys != null ? marketDataKeys.size() : 0);
                details.put("latest_price_keys_count", latestPriceKeys != null ? latestPriceKeys.size() : 0);
                details.put("query_time_ms", duration);
                
                // 测试读取一个具体的键（如果存在）
                if (marketDataKeys != null && !marketDataKeys.isEmpty()) {
                    String sampleKey = marketDataKeys.iterator().next();
                    Object sampleValue = redisTemplate.opsForValue().get(sampleKey);
                    details.put("sample_key", sampleKey);
                    details.put("sample_value_exists", sampleValue != null);
                }
                
                return ValidationResult.success(getName(), "Redis读取测试通过")
                        .withType("read")
                        .withDuration(duration)
                        .addDetail("details", details)
                        .addMetric("query_latency_ms", (double) duration);
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("Redis读取验证失败", e);
                return ValidationResult.failure(getName(), "Redis读取测试失败", e)
                        .withType("read")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateDataIntegrity() {
        if (redisTemplate == null) {
            return CompletableFuture.completedFuture(ValidationResult.builder().success(true).validatorName(getName()).message("Redis not configured").validationType("skipped").build());
        }
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                Map<String, Object> details = new HashMap<>();
                Map<String, Double> metrics = new HashMap<>();
                
                // 使用安全的方式检查缓存数据（避免keys(*)操作）
                // 直接统计已知的关键模式，避免全量扫描
                long marketDataCount = countKeysByPatternSafe("market-data:*");
                long latestPriceCount = countKeysByPatternSafe("latest-price:*");
                long klineDataCount = countKeysByPatternSafe("kline:*");
                long depthDataCount = countKeysByPatternSafe("depth:*");

                // 估算总键数（基于已知模式）
                int estimatedTotalKeys = (int)(marketDataCount + latestPriceCount + klineDataCount + depthDataCount);

                details.put("estimated_total_keys", estimatedTotalKeys);
                details.put("market_data_keys", marketDataCount);
                details.put("latest_price_keys", latestPriceCount);
                details.put("kline_data_keys", klineDataCount);
                details.put("depth_data_keys", depthDataCount);
                details.put("scan_method", "pattern_based_safe");

                // 检查少量样本键的TTL设置（安全方式）
                int keysWithTtl = 0;
                int keysWithoutTtl = 0;
                int sampleSize = 0;

                // 检查每种类型的少量样本
                String[] samplePatterns = {"market-data:*", "latest-price:*", "kline:*"};
                for (String pattern : samplePatterns) {
                    Set<String> sampleKeys = redisTemplate.keys(pattern);
                    if (sampleKeys != null) {
                        int checked = 0;
                        for (String key : sampleKeys) {
                            if (checked >= 5) break; // 每种类型最多检查5个键

                            try {
                                Long ttl = redisTemplate.getExpire(key, TimeUnit.SECONDS);
                                if (ttl != null && ttl > 0) {
                                    keysWithTtl++;
                                } else {
                                    keysWithoutTtl++;
                                }
                                sampleSize++;
                                checked++;
                            } catch (Exception e) {
                                // 忽略单个键的检查异常
                                log.debug("检查键TTL异常: {}", key, e);
                            }
                        }
                    }
                }

                details.put("sample_keys_with_ttl", keysWithTtl);
                details.put("sample_keys_without_ttl", keysWithoutTtl);
                details.put("sample_size", sampleSize);
                
                long duration = System.currentTimeMillis() - startTime;
                
                // 计算数据完整性分数
                double integrityScore = calculateIntegrityScore(estimatedTotalKeys, marketDataCount, latestPriceCount);
                metrics.put("data_integrity_score", integrityScore);
                metrics.put("ttl_coverage_rate", keysWithTtl > 0 ? 
                        (double) keysWithTtl / (keysWithTtl + keysWithoutTtl) * 100 : 0);
                
                return ValidationResult.success(getName(), "Redis数据完整性验证通过")
                        .withType("data_integrity")
                        .withDuration(duration)
                        .addDetail("details", details)
                        .addMetric("integrity_check_ms", (double) duration);
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("Redis数据完整性验证失败", e);
                return ValidationResult.failure(getName(), "Redis数据完整性验证失败", e)
                        .withType("data_integrity")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validatePerformance() {
        if (redisTemplate == null) {
            return CompletableFuture.completedFuture(ValidationResult.builder().success(true).validatorName(getName()).message("Redis not configured").validationType("skipped").build());
        }
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                Map<String, Object> details = new HashMap<>();
                Map<String, Double> metrics = new HashMap<>();
                
                // 性能测试：批量写入
                long writeStartTime = System.currentTimeMillis();
                String keyPrefix = "perf:test:" + System.currentTimeMillis() + ":";
                
                for (int i = 0; i < 100; i++) {
                    String key = keyPrefix + i;
                    String value = "test_value_" + i;
                    redisTemplate.opsForValue().set(key, value, Duration.ofMinutes(1));
                }
                
                long writeTime = System.currentTimeMillis() - writeStartTime;
                
                // 性能测试：批量读取
                long readStartTime = System.currentTimeMillis();
                
                for (int i = 0; i < 100; i++) {
                    String key = keyPrefix + i;
                    redisTemplate.opsForValue().get(key);
                }
                
                long readTime = System.currentTimeMillis() - readStartTime;
                
                // 清理测试数据
                Set<String> testKeys = redisTemplate.keys(keyPrefix + "*");
                if (testKeys != null && !testKeys.isEmpty()) {
                    redisTemplate.delete(testKeys);
                }
                
                long totalDuration = System.currentTimeMillis() - startTime;
                
                details.put("batch_write_time_ms", writeTime);
                details.put("batch_read_time_ms", readTime);
                details.put("total_test_time_ms", totalDuration);
                details.put("test_operations", 200); // 100 writes + 100 reads
                
                metrics.put("write_throughput_ops_per_sec", 100.0 / (writeTime / 1000.0));
                metrics.put("read_throughput_ops_per_sec", 100.0 / (readTime / 1000.0));
                metrics.put("avg_write_latency_ms", (double) writeTime / 100);
                metrics.put("avg_read_latency_ms", (double) readTime / 100);
                
                double performanceScore = calculatePerformanceScore(writeTime, readTime);
                metrics.put("performance_score", performanceScore);
                
                return ValidationResult.success(getName(), "Redis性能测试通过")
                        .withType("performance")
                        .withDuration(totalDuration)
                        .addDetail("details", details)
                        .addMetric("performance_score", performanceScore);
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("Redis性能验证失败", e);
                return ValidationResult.failure(getName(), "Redis性能测试失败", e)
                        .withType("performance")
                        .withDuration(duration);
            }
        });
    }
    
    /**
     * 按模式统计键数量
     */
    private long countKeysByPattern(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            return keys != null ? keys.size() : 0;
        } catch (Exception e) {
            log.warn("统计Redis键数量失败: pattern={}", pattern, e);
            return 0;
        }
    }
    
    /**
     * 计算数据完整性分数
     */
    private double calculateIntegrityScore(int totalKeys, long marketDataCount, long latestPriceCount) {
        if (totalKeys == 0) {
            return 0.0;
        }
        
        // 基于不同类型数据的存在性计算分数
        double score = 0.0;
        
        if (marketDataCount > 0) score += 40.0;
        if (latestPriceCount > 0) score += 30.0;
        if (totalKeys > 10) score += 20.0;
        if (totalKeys > 100) score += 10.0;
        
        return Math.min(100.0, score);
    }
    
    /**
     * 计算性能分数
     */
    private double calculatePerformanceScore(long writeTime, long readTime) {
        // 性能评分：写入时间 < 500ms 且读取时间 < 200ms 为满分
        double writeScore = Math.max(0, 100 - (writeTime / 5.0));
        double readScore = Math.max(0, 100 - (readTime / 2.0));
        return (writeScore + readScore) / 2.0;
    }

    /**
     * 安全的键计数方法（限制扫描数量）
     */
    private long countKeysByPatternSafe(String pattern) {
        try {
            Set<String> keys = redisTemplate.keys(pattern);
            if (keys != null) {
                // 限制最大计数，避免大量键时的性能问题
                return Math.min(keys.size(), 10000);
            }
            return 0;
        } catch (Exception e) {
            log.warn("安全统计Redis键数量失败: pattern={}", pattern, e);
            return 0;
        }
    }
}
