package com.trading.common.validator.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.Instant;
import java.util.Map;
import java.util.HashMap;

/**
 * 存储系统健康状态模型
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StorageHealthStatus {
    
    /**
     * 存储系统名称
     */
    private String storageName;
    
    /**
     * 健康状态
     */
    private HealthLevel healthLevel;
    
    /**
     * 连接状态
     */
    private boolean connected;
    
    /**
     * 可写状态
     */
    private boolean writable;
    
    /**
     * 可读状态
     */
    private boolean readable;
    
    /**
     * 最后检查时间
     */
    @Builder.Default
    private Instant lastCheckTime = Instant.now();
    
    /**
     * 响应时间（毫秒）
     */
    private Long responseTime;
    
    /**
     * 错误计数
     */
    @Builder.Default
    private Integer errorCount = 0;
    
    /**
     * 警告计数
     */
    @Builder.Default
    private Integer warningCount = 0;
    
    /**
     * 详细信息
     */
    @Builder.Default
    private Map<String, Object> details = new HashMap<>();
    
    /**
     * 性能指标
     */
    @Builder.Default
    private Map<String, Double> metrics = new HashMap<>();
    
    /**
     * 健康等级枚举
     */
    public enum HealthLevel {
        HEALTHY("健康", "green"),
        WARNING("警告", "yellow"),
        CRITICAL("严重", "orange"),
        DOWN("宕机", "red");
        
        private final String description;
        private final String color;
        
        HealthLevel(String description, String color) {
            this.description = description;
            this.color = color;
        }
        
        public String getDescription() {
            return description;
        }
        
        public String getColor() {
            return color;
        }
    }
    
    /**
     * 创建健康状态
     */
    public static StorageHealthStatus healthy(String storageName) {
        return StorageHealthStatus.builder()
                .storageName(storageName)
                .healthLevel(HealthLevel.HEALTHY)
                .connected(true)
                .writable(true)
                .readable(true)
                .build();
    }
    
    /**
     * 创建警告状态
     */
    public static StorageHealthStatus warning(String storageName, String reason) {
        return StorageHealthStatus.builder()
                .storageName(storageName)
                .healthLevel(HealthLevel.WARNING)
                .connected(true)
                .build()
                .addDetail("warning_reason", reason);
    }
    
    /**
     * 创建严重状态
     */
    public static StorageHealthStatus critical(String storageName, String reason) {
        return StorageHealthStatus.builder()
                .storageName(storageName)
                .healthLevel(HealthLevel.CRITICAL)
                .build()
                .addDetail("critical_reason", reason);
    }
    
    /**
     * 创建宕机状态
     */
    public static StorageHealthStatus down(String storageName, String reason) {
        return StorageHealthStatus.builder()
                .storageName(storageName)
                .healthLevel(HealthLevel.DOWN)
                .connected(false)
                .writable(false)
                .readable(false)
                .build()
                .addDetail("down_reason", reason);
    }
    
    /**
     * 添加详细信息
     */
    public StorageHealthStatus addDetail(String key, Object value) {
        this.details.put(key, value);
        return this;
    }
    
    /**
     * 添加性能指标
     */
    public StorageHealthStatus addMetric(String key, Double value) {
        this.metrics.put(key, value);
        return this;
    }
    
    /**
     * 增加错误计数
     */
    public StorageHealthStatus incrementErrorCount() {
        this.errorCount++;
        return this;
    }
    
    /**
     * 增加警告计数
     */
    public StorageHealthStatus incrementWarningCount() {
        this.warningCount++;
        return this;
    }
    
    /**
     * 判断是否健康
     */
    public boolean isHealthy() {
        return healthLevel == HealthLevel.HEALTHY;
    }
    
    /**
     * 判断是否可用
     */
    public boolean isAvailable() {
        return connected && (healthLevel == HealthLevel.HEALTHY || healthLevel == HealthLevel.WARNING);
    }
    
    /**
     * 生成状态描述
     */
    public String getStatusDescription() {
        return String.format("%s: %s (%s)", 
                storageName, 
                healthLevel.getDescription(),
                connected ? "已连接" : "未连接");
    }
}
