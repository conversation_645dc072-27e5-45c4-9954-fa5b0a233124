package com.trading.common.validator.impl;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.QueryApi;
import com.influxdb.client.WriteApiBlocking;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;
import com.influxdb.query.FluxTable;
import com.trading.common.validator.StorageValidator;
import com.trading.common.validator.model.ValidationResult;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * InfluxDB验证器实现
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
@ConditionalOnProperty(prefix = "influxdb", name = "enabled", havingValue = "true")
@RequiredArgsConstructor
public class InfluxDBValidator implements StorageValidator {
    
    private static final Logger log = LoggerFactory.getLogger(InfluxDBValidator.class);
    
    private final InfluxDBClient influxDBClient;
    
    @Value("${influxdb.org:crypto}")
    private String org;
    
    @Value("${influxdb.bucket:market_data}")
    private String bucket;
    
    @Override
    public String getName() {
        return "InfluxDB";
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateConnection() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                // 检查InfluxDB连接状态
                boolean isReady = influxDBClient.ready() != null;
                long duration = System.currentTimeMillis() - startTime;
                
                if (isReady) {
                    Map<String, Object> details = new HashMap<>();
                    details.put("org", org);
                    details.put("bucket", bucket);
                    details.put("response_time_ms", duration);
                    
                    return ValidationResult.success(getName(), "InfluxDB连接正常")
                            .withType("connection")
                            .withDuration(duration)
                            .addDetail("details", details);
                } else {
                    return ValidationResult.failure(getName(), "InfluxDB连接失败", "服务不可用")
                            .withType("connection")
                            .withDuration(duration);
                }
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("InfluxDB连接验证失败", e);
                return ValidationResult.failure(getName(), "InfluxDB连接验证异常", e)
                        .withType("connection")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateWrite() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                WriteApiBlocking writeApi = influxDBClient.getWriteApiBlocking();
                
                // 创建测试数据点
                Point testPoint = Point.measurement("validation_test")
                        .addTag("validator", "influxdb")
                        .addTag("test_type", "write")
                        .addField("test_value", 1.0)
                        .addField("timestamp", System.currentTimeMillis())
                        .time(Instant.now(), WritePrecision.MS);
                
                // 写入测试数据
                writeApi.writePoint(bucket, org, testPoint);
                
                long duration = System.currentTimeMillis() - startTime;
                
                Map<String, Object> details = new HashMap<>();
                details.put("test_measurement", "validation_test");
                details.put("write_time_ms", duration);
                
                return ValidationResult.success(getName(), "InfluxDB写入测试通过")
                        .withType("write")
                        .withDuration(duration)
                        .addDetail("details", details)
                        .addMetric("write_latency_ms", (double) duration);
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("InfluxDB写入验证失败", e);
                return ValidationResult.failure(getName(), "InfluxDB写入测试失败", e)
                        .withType("write")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateRead() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                QueryApi queryApi = influxDBClient.getQueryApi();
                
                // 查询最近1小时的数据
                String query = String.format(
                        "from(bucket: \"%s\") " +
                        "|> range(start: -1h) " +
                        "|> filter(fn: (r) => r._measurement == \"market_data\" or r._measurement == \"kline_data\") " +
                        "|> count()",
                        bucket
                );
                
                List<FluxTable> tables = queryApi.query(query, org);
                long duration = System.currentTimeMillis() - startTime;
                
                Map<String, Object> details = new HashMap<>();
                details.put("query_time_ms", duration);
                details.put("tables_count", tables.size());
                
                if (!tables.isEmpty()) {
                    details.put("records_count", tables.get(0).getRecords().size());
                }
                
                return ValidationResult.success(getName(), "InfluxDB读取测试通过")
                        .withType("read")
                        .withDuration(duration)
                        .addDetail("details", details)
                        .addMetric("query_latency_ms", (double) duration);
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("InfluxDB读取验证失败", e);
                return ValidationResult.failure(getName(), "InfluxDB读取测试失败", e)
                        .withType("read")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateDataIntegrity() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                QueryApi queryApi = influxDBClient.getQueryApi();
                
                // 检查最近1小时的数据完整性
                Instant endTime = Instant.now();
                Instant startTimeQuery = endTime.minus(1, ChronoUnit.HOURS);
                
                Map<String, Object> details = new HashMap<>();
                Map<String, Double> metrics = new HashMap<>();
                
                // 检查K线数据
                String klineQuery = String.format(
                        "from(bucket: \"%s\") " +
                        "|> range(start: %s, stop: %s) " +
                        "|> filter(fn: (r) => r._measurement == \"kline_data\") " +
                        "|> group(columns: [\"symbol\", \"interval\"]) " +
                        "|> count()",
                        bucket, startTimeQuery, endTime
                );
                
                List<FluxTable> klineTables = queryApi.query(klineQuery, org);
                details.put("kline_data_groups", klineTables.size());
                
                // 检查市场数据
                String marketDataQuery = String.format(
                        "from(bucket: \"%s\") " +
                        "|> range(start: %s, stop: %s) " +
                        "|> filter(fn: (r) => r._measurement == \"market_data\") " +
                        "|> group(columns: [\"symbol\", \"dataType\"]) " +
                        "|> count()",
                        bucket, startTimeQuery, endTime
                );
                
                List<FluxTable> marketDataTables = queryApi.query(marketDataQuery, org);
                details.put("market_data_groups", marketDataTables.size());
                
                long duration = System.currentTimeMillis() - startTime;
                
                details.put("validation_time_ms", duration);
                metrics.put("data_integrity_score", calculateIntegrityScore(klineTables, marketDataTables));
                
                return ValidationResult.success(getName(), "InfluxDB数据完整性验证通过")
                        .withType("data_integrity")
                        .withDuration(duration)
                        .addDetail("details", details)
                        .addMetric("integrity_check_ms", (double) duration);
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("InfluxDB数据完整性验证失败", e);
                return ValidationResult.failure(getName(), "InfluxDB数据完整性验证失败", e)
                        .withType("data_integrity")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validatePerformance() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            try {
                // 性能测试：批量写入
                long writeStartTime = System.currentTimeMillis();
                WriteApiBlocking writeApi = influxDBClient.getWriteApiBlocking();
                
                for (int i = 0; i < 10; i++) {
                    Point point = Point.measurement("performance_test")
                            .addTag("test_batch", "performance")
                            .addField("value", i)
                            .time(Instant.now().plusMillis(i), WritePrecision.MS);
                    writeApi.writePoint(bucket, org, point);
                }
                
                long writeTime = System.currentTimeMillis() - writeStartTime;
                
                // 性能测试：查询
                long queryStartTime = System.currentTimeMillis();
                QueryApi queryApi = influxDBClient.getQueryApi();
                
                String perfQuery = String.format(
                        "from(bucket: \"%s\") " +
                        "|> range(start: -5m) " +
                        "|> filter(fn: (r) => r._measurement == \"performance_test\") " +
                        "|> count()",
                        bucket
                );
                
                queryApi.query(perfQuery, org);
                long queryTime = System.currentTimeMillis() - queryStartTime;
                
                long totalDuration = System.currentTimeMillis() - startTime;
                
                Map<String, Object> details = new HashMap<>();
                details.put("batch_write_time_ms", writeTime);
                details.put("query_time_ms", queryTime);
                details.put("total_test_time_ms", totalDuration);
                
                Map<String, Double> metrics = new HashMap<>();
                metrics.put("write_throughput_ops_per_sec", 10.0 / (writeTime / 1000.0));
                metrics.put("query_latency_ms", (double) queryTime);
                metrics.put("write_latency_ms", (double) writeTime);
                
                return ValidationResult.success(getName(), "InfluxDB性能测试通过")
                        .withType("performance")
                        .withDuration(totalDuration)
                        .addDetail("details", details)
                        .addMetric("performance_score", calculatePerformanceScore(writeTime, queryTime));
                
            } catch (Exception e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("InfluxDB性能验证失败", e);
                return ValidationResult.failure(getName(), "InfluxDB性能测试失败", e)
                        .withType("performance")
                        .withDuration(duration);
            }
        });
    }
    
    /**
     * 计算数据完整性分数
     */
    private double calculateIntegrityScore(List<FluxTable> klineTables, List<FluxTable> marketDataTables) {
        // 简单的完整性评分算法
        int totalGroups = klineTables.size() + marketDataTables.size();
        if (totalGroups == 0) {
            return 0.0;
        }
        
        // 基于数据组数量计算分数
        return Math.min(100.0, totalGroups * 10.0);
    }
    
    /**
     * 计算性能分数
     */
    private double calculatePerformanceScore(long writeTime, long queryTime) {
        // 性能评分：写入时间 < 100ms 且查询时间 < 50ms 为满分
        double writeScore = Math.max(0, 100 - writeTime);
        double queryScore = Math.max(0, 100 - queryTime * 2);
        return (writeScore + queryScore) / 2.0;
    }
}
