package com.trading.common.validator.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.time.Instant;
import java.util.Map;
import java.util.HashMap;

/**
 * 验证结果模型
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ValidationResult {
    
    /**
     * 验证是否成功
     */
    private boolean success;
    
    /**
     * 验证器名称
     */
    private String validatorName;
    
    /**
     * 验证类型
     */
    private String validationType;
    
    /**
     * 验证消息
     */
    private String message;
    
    /**
     * 错误信息
     */
    private String error;
    
    /**
     * 验证时间
     */
    @Builder.Default
    private Instant timestamp = Instant.now();
    
    /**
     * 验证耗时（毫秒）
     */
    private Long duration;
    
    /**
     * 验证详细数据
     */
    @Builder.Default
    private Map<String, Object> details = new HashMap<>();
    
    /**
     * 性能指标
     */
    @Builder.Default
    private Map<String, Double> metrics = new HashMap<>();
    
    /**
     * 创建成功结果
     */
    public static ValidationResult success(String validatorName, String message) {
        return ValidationResult.builder()
                .success(true)
                .validatorName(validatorName)
                .message(message)
                .build();
    }
    
    /**
     * 创建成功结果（带详细信息）
     */
    public static ValidationResult success(String validatorName, String message, Map<String, Object> details) {
        return ValidationResult.builder()
                .success(true)
                .validatorName(validatorName)
                .message(message)
                .details(details)
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static ValidationResult failure(String validatorName, String message, String error) {
        return ValidationResult.builder()
                .success(false)
                .validatorName(validatorName)
                .message(message)
                .error(error)
                .build();
    }
    
    /**
     * 创建失败结果（带异常）
     */
    public static ValidationResult failure(String validatorName, String message, Throwable throwable) {
        return ValidationResult.builder()
                .success(false)
                .validatorName(validatorName)
                .message(message)
                .error(throwable.getMessage())
                .build();
    }
    
    /**
     * 添加详细信息
     */
    public ValidationResult addDetail(String key, Object value) {
        this.details.put(key, value);
        return this;
    }
    
    /**
     * 添加性能指标
     */
    public ValidationResult addMetric(String key, Double value) {
        this.metrics.put(key, value);
        return this;
    }
    
    /**
     * 设置验证类型
     */
    public ValidationResult withType(String type) {
        this.validationType = type;
        return this;
    }
    
    /**
     * 设置验证耗时
     */
    public ValidationResult withDuration(Long duration) {
        this.duration = duration;
        return this;
    }

    /**
     * 创建一个表示验证被跳过的成功结果
     * @param validatorName 验证器名称
     * @param reason 跳过的原因
     * @return 验证结果实例
     */
    public static ValidationResult skipped(String validatorName, String reason) {
        return ValidationResult.builder()
                .success(true) // 跳过被视为一种“非失败”状态
                .validatorName(validatorName)
                .message(reason)
                .validationType("skipped")
                .build();
    }

}
