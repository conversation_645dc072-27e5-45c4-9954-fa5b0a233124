package com.trading.common.validator.impl;

import com.trading.common.validator.StorageValidator;
import com.trading.common.validator.model.ValidationResult;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * MySQL验证器实现
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
public class MySQLValidator implements StorageValidator {
    
    private static final Logger log = LoggerFactory.getLogger(MySQLValidator.class);
    
    @Autowired(required = false)
    private DataSource dataSource;
    
    @Autowired(required = false)
    private JdbcTemplate jdbcTemplate;
    
    @Override
    public String getName() {
        return "MySQL";
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateConnection() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            if (dataSource == null) {
                return ValidationResult.failure(getName(), "MySQL数据源未配置", "DataSource bean不存在")
                        .withType("connection")
                        .withDuration(System.currentTimeMillis() - startTime);
            }
            
            try (Connection connection = dataSource.getConnection()) {
                DatabaseMetaData metaData = connection.getMetaData();
                
                long duration = System.currentTimeMillis() - startTime;
                
                Map<String, Object> details = new HashMap<>();
                details.put("database_product_name", metaData.getDatabaseProductName());
                details.put("database_product_version", metaData.getDatabaseProductVersion());
                details.put("driver_name", metaData.getDriverName());
                details.put("driver_version", metaData.getDriverVersion());
                details.put("url", metaData.getURL());
                details.put("username", metaData.getUserName());
                details.put("connection_time_ms", duration);
                
                return ValidationResult.success(getName(), "MySQL连接正常")
                        .withType("connection")
                        .withDuration(duration)
                        .addDetail("details", details);
                
            } catch (SQLException e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("MySQL连接验证失败", e);
                return ValidationResult.failure(getName(), "MySQL连接验证异常", e)
                        .withType("connection")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateWrite() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            if (jdbcTemplate == null) {
                return ValidationResult.failure(getName(), "JdbcTemplate未配置", "JdbcTemplate bean不存在")
                        .withType("write")
                        .withDuration(System.currentTimeMillis() - startTime);
            }
            
            try {
                // 创建测试表（如果不存在）
                String createTableSql = """
                    CREATE TABLE IF NOT EXISTS validation_test (
                        id BIGINT AUTO_INCREMENT PRIMARY KEY,
                        test_key VARCHAR(100) NOT NULL,
                        test_value VARCHAR(500),
                        test_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_test_key (test_key)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                    """;
                
                jdbcTemplate.execute(createTableSql);
                
                // 插入测试数据
                String testKey = "validation_test_" + System.currentTimeMillis();
                String testValue = "test_value_" + System.currentTimeMillis();
                
                String insertSql = "INSERT INTO validation_test (test_key, test_value) VALUES (?, ?)";
                int rowsAffected = jdbcTemplate.update(insertSql, testKey, testValue);
                
                // 验证插入结果
                String selectSql = "SELECT test_value FROM validation_test WHERE test_key = ?";
                String retrievedValue = jdbcTemplate.queryForObject(selectSql, String.class, testKey);
                
                // 清理测试数据
                String deleteSql = "DELETE FROM validation_test WHERE test_key = ?";
                jdbcTemplate.update(deleteSql, testKey);
                
                long duration = System.currentTimeMillis() - startTime;
                
                if (rowsAffected > 0 && testValue.equals(retrievedValue)) {
                    Map<String, Object> details = new HashMap<>();
                    details.put("test_key", testKey);
                    details.put("rows_affected", rowsAffected);
                    details.put("write_time_ms", duration);
                    
                    return ValidationResult.success(getName(), "MySQL写入测试通过")
                            .withType("write")
                            .withDuration(duration)
                            .addDetail("details", details)
                            .addMetric("write_latency_ms", (double) duration);
                } else {
                    return ValidationResult.failure(getName(), "MySQL写入测试失败", 
                            "写入验证失败: rowsAffected=" + rowsAffected + ", valueMatch=" + testValue.equals(retrievedValue))
                            .withType("write")
                            .withDuration(duration);
                }
                
            } catch (DataAccessException e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("MySQL写入验证失败", e);
                return ValidationResult.failure(getName(), "MySQL写入测试异常", e)
                        .withType("write")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateRead() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            if (jdbcTemplate == null) {
                return ValidationResult.failure(getName(), "JdbcTemplate未配置", "JdbcTemplate bean不存在")
                        .withType("read")
                        .withDuration(System.currentTimeMillis() - startTime);
            }
            
            try {
                // 查询数据库基本信息
                String versionSql = "SELECT VERSION() as version";
                String version = jdbcTemplate.queryForObject(versionSql, String.class);
                
                // 查询当前数据库名
                String databaseSql = "SELECT DATABASE() as database_name";
                String databaseName = jdbcTemplate.queryForObject(databaseSql, String.class);
                
                // 查询表数量
                String tableCountSql = "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = ?";
                Integer tableCount = jdbcTemplate.queryForObject(tableCountSql, Integer.class, databaseName);
                
                long duration = System.currentTimeMillis() - startTime;
                
                Map<String, Object> details = new HashMap<>();
                details.put("mysql_version", version);
                details.put("database_name", databaseName);
                details.put("table_count", tableCount);
                details.put("query_time_ms", duration);
                
                return ValidationResult.success(getName(), "MySQL读取测试通过")
                        .withType("read")
                        .withDuration(duration)
                        .addDetail("details", details)
                        .addMetric("query_latency_ms", (double) duration);
                
            } catch (DataAccessException e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("MySQL读取验证失败", e);
                return ValidationResult.failure(getName(), "MySQL读取测试失败", e)
                        .withType("read")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validateDataIntegrity() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            if (jdbcTemplate == null) {
                return ValidationResult.failure(getName(), "JdbcTemplate未配置", "JdbcTemplate bean不存在")
                        .withType("data_integrity")
                        .withDuration(System.currentTimeMillis() - startTime);
            }
            
            try {
                Map<String, Object> details = new HashMap<>();
                Map<String, Double> metrics = new HashMap<>();
                
                // 获取当前数据库名
                String databaseName = jdbcTemplate.queryForObject("SELECT DATABASE()", String.class);
                
                // 检查市场数据相关表是否存在
                List<String> expectedTables = List.of(
                    "t_kline_data", "t_market_data", "t_depth_data", 
                    "t_trade_data", "t_ticker_data", "t_order_book"
                );
                
                List<String> existingTables = new ArrayList<>();
                List<String> missingTables = new ArrayList<>();
                
                for (String tableName : expectedTables) {
                    String checkTableSql = """
                        SELECT COUNT(*) FROM information_schema.tables 
                        WHERE table_schema = ? AND table_name = ?
                        """;
                    
                    Integer count = jdbcTemplate.queryForObject(checkTableSql, Integer.class, databaseName, tableName);
                    if (count != null && count > 0) {
                        existingTables.add(tableName);
                        
                        // 检查表中的数据量
                        try {
                            String countSql = "SELECT COUNT(*) FROM " + tableName;
                            Integer rowCount = jdbcTemplate.queryForObject(countSql, Integer.class);
                            details.put(tableName + "_row_count", rowCount);
                        } catch (Exception e) {
                            log.warn("无法查询表 {} 的数据量: {}", tableName, e.getMessage());
                            details.put(tableName + "_row_count", "查询失败");
                        }
                    } else {
                        missingTables.add(tableName);
                    }
                }
                
                details.put("existing_tables", existingTables);
                details.put("missing_tables", missingTables);
                details.put("existing_tables_count", existingTables.size());
                details.put("missing_tables_count", missingTables.size());
                
                // 检查索引情况
                if (!existingTables.isEmpty()) {
                    String indexSql = """
                        SELECT table_name, index_name, column_name 
                        FROM information_schema.statistics 
                        WHERE table_schema = ? AND table_name IN (%s)
                        ORDER BY table_name, index_name
                        """.formatted(existingTables.stream()
                            .map(t -> "'" + t + "'")
                            .reduce((a, b) -> a + "," + b)
                            .orElse(""));
                    
                    try {
                        List<Map<String, Object>> indexes = jdbcTemplate.queryForList(indexSql, databaseName);
                        details.put("indexes_count", indexes.size());
                        details.put("indexes_info", indexes);
                    } catch (Exception e) {
                        log.warn("查询索引信息失败: {}", e.getMessage());
                    }
                }
                
                long duration = System.currentTimeMillis() - startTime;
                
                // 计算数据完整性分数
                double integrityScore = calculateIntegrityScore(existingTables.size(), expectedTables.size());
                metrics.put("data_integrity_score", integrityScore);
                metrics.put("table_coverage_rate", (double) existingTables.size() / expectedTables.size() * 100);
                
                String message = existingTables.isEmpty() ? 
                    "MySQL数据完整性验证完成，但缺少市场数据表" : 
                    "MySQL数据完整性验证通过";
                
                return ValidationResult.success(getName(), message)
                        .withType("data_integrity")
                        .withDuration(duration)
                        .addDetail("details", details)
                        .addMetric("integrity_check_ms", (double) duration);
                
            } catch (DataAccessException e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("MySQL数据完整性验证失败", e);
                return ValidationResult.failure(getName(), "MySQL数据完整性验证失败", e)
                        .withType("data_integrity")
                        .withDuration(duration);
            }
        });
    }
    
    @Override
    public CompletableFuture<ValidationResult> validatePerformance() {
        return CompletableFuture.supplyAsync(() -> {
            long startTime = System.currentTimeMillis();
            
            if (jdbcTemplate == null) {
                return ValidationResult.failure(getName(), "JdbcTemplate未配置", "JdbcTemplate bean不存在")
                        .withType("performance")
                        .withDuration(System.currentTimeMillis() - startTime);
            }
            
            try {
                Map<String, Object> details = new HashMap<>();
                Map<String, Double> metrics = new HashMap<>();
                
                // 性能测试：批量插入
                long writeStartTime = System.currentTimeMillis();
                
                // 确保测试表存在
                String createTableSql = """
                    CREATE TABLE IF NOT EXISTS performance_test (
                        id BIGINT AUTO_INCREMENT PRIMARY KEY,
                        test_batch VARCHAR(50),
                        test_value INT,
                        test_timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        INDEX idx_test_batch (test_batch)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
                    """;
                
                jdbcTemplate.execute(createTableSql);
                
                String batchId = "perf_test_" + System.currentTimeMillis();
                String insertSql = "INSERT INTO performance_test (test_batch, test_value) VALUES (?, ?)";
                
                // 批量插入100条记录
                for (int i = 0; i < 100; i++) {
                    jdbcTemplate.update(insertSql, batchId, i);
                }
                
                long writeTime = System.currentTimeMillis() - writeStartTime;
                
                // 性能测试：批量查询
                long readStartTime = System.currentTimeMillis();
                
                String selectSql = "SELECT COUNT(*) FROM performance_test WHERE test_batch = ?";
                Integer count = jdbcTemplate.queryForObject(selectSql, Integer.class, batchId);
                
                String selectAllSql = "SELECT * FROM performance_test WHERE test_batch = ? ORDER BY test_value";
                List<Map<String, Object>> results = jdbcTemplate.queryForList(selectAllSql, batchId);
                
                long readTime = System.currentTimeMillis() - readStartTime;
                
                // 清理测试数据
                String deleteSql = "DELETE FROM performance_test WHERE test_batch = ?";
                jdbcTemplate.update(deleteSql, batchId);
                
                long totalDuration = System.currentTimeMillis() - startTime;
                
                details.put("batch_id", batchId);
                details.put("records_inserted", 100);
                details.put("records_queried", results.size());
                details.put("batch_write_time_ms", writeTime);
                details.put("batch_read_time_ms", readTime);
                details.put("total_test_time_ms", totalDuration);
                
                metrics.put("write_throughput_ops_per_sec", 100.0 / (writeTime / 1000.0));
                metrics.put("read_throughput_ops_per_sec", (double) results.size() / (readTime / 1000.0));
                metrics.put("avg_write_latency_ms", (double) writeTime / 100);
                metrics.put("avg_read_latency_ms", (double) readTime);
                
                double performanceScore = calculatePerformanceScore(writeTime, readTime);
                metrics.put("performance_score", performanceScore);
                
                return ValidationResult.success(getName(), "MySQL性能测试通过")
                        .withType("performance")
                        .withDuration(totalDuration)
                        .addDetail("details", details)
                        .addMetric("performance_score", performanceScore);
                
            } catch (DataAccessException e) {
                long duration = System.currentTimeMillis() - startTime;
                log.error("MySQL性能验证失败", e);
                return ValidationResult.failure(getName(), "MySQL性能测试失败", e)
                        .withType("performance")
                        .withDuration(duration);
            }
        });
    }
    
    /**
     * 计算数据完整性分数
     */
    private double calculateIntegrityScore(int existingTablesCount, int expectedTablesCount) {
        if (expectedTablesCount == 0) {
            return 100.0;
        }
        
        double coverage = (double) existingTablesCount / expectedTablesCount;
        return coverage * 100.0;
    }
    
    /**
     * 计算性能分数
     */
    private double calculatePerformanceScore(long writeTime, long readTime) {
        // 性能评分：写入时间 < 2000ms 且读取时间 < 500ms 为满分
        double writeScore = Math.max(0, 100 - (writeTime / 20.0));
        double readScore = Math.max(0, 100 - (readTime / 5.0));
        return (writeScore + readScore) / 2.0;
    }
}
