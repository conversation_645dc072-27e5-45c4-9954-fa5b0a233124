package com.trading.common.ratelimit;

import com.trading.common.constant.ApiLimitConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 增强型限流器 - 优化版本
 * 基于滑动时间窗口的高性能限流实现
 * 支持亚毫秒级响应时间和高并发场景
 * 
 * 主要优化：
 * 1. 滑动时间窗口替代固定时间窗口，提升限流精度
 * 2. 无锁算法减少竞争，提升并发性能
 * 3. 内存优化减少GC压力
 * 4. 详细性能监控和告警机制
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class EnhancedRateLimiter {

    private static final Logger log = LoggerFactory.getLogger(EnhancedRateLimiter.class);

    /**
     * 滑动时间窗口限流器
     */
    private static class SlidingWindowRateLimiter {
        private final int maxRequests;
        private final long windowSizeNanos;
        private final ConcurrentHashMap<Long, LongAdder> timeSlots;
        private final AtomicLong lastCleanupTime;
        private final ReentrantLock cleanupLock;
        
        // 性能监控指标
        private final LongAdder totalRequests = new LongAdder();
        private final LongAdder rejectedRequests = new LongAdder();
        private final LongAdder totalLatency = new LongAdder();

        public SlidingWindowRateLimiter(int maxRequests, long windowSizeNanos) {
            this.maxRequests = maxRequests;
            this.windowSizeNanos = windowSizeNanos;
            this.timeSlots = new ConcurrentHashMap<>();
            this.lastCleanupTime = new AtomicLong(System.nanoTime());
            this.cleanupLock = new ReentrantLock();
        }

        /**
         * 尝试获取许可证
         * 
         * @param weight 请求权重
         * @param timeoutNanos 超时时间（纳秒）
         * @return 是否成功获取许可证
         */
        public boolean tryAcquire(int weight, long timeoutNanos) {
            long startTime = System.nanoTime();
            long currentTime = startTime;
            long endTime = startTime + timeoutNanos;

            while (currentTime <= endTime) {
                // 清理过期时间槽
                cleanupExpiredSlots(currentTime);

                // 计算当前时间槽
                long timeSlot = currentTime / 1_000_000; // 毫秒级时间槽

                // 计算当前窗口内的请求数
                long windowStart = currentTime - windowSizeNanos;
                long currentRequests = calculateCurrentRequests(windowStart);

                // 检查是否可以获取许可证
                if (currentRequests + weight <= maxRequests) {
                    // 原子性地增加请求计数
                    timeSlots.computeIfAbsent(timeSlot, k -> new LongAdder()).add(weight);
                    
                    // 更新统计信息
                    totalRequests.increment();
                    totalLatency.add(System.nanoTime() - startTime);
                    
                    return true;
                }

                // 如果没有超时设置，直接返回失败
                if (timeoutNanos <= 0) {
                    rejectedRequests.increment();
                    return false;
                }

                // 等待一小段时间后重试
                try {
                    TimeUnit.NANOSECONDS.sleep(100_000); // 0.1ms
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    rejectedRequests.increment();
                    return false;
                }

                currentTime = System.nanoTime();
            }

            rejectedRequests.increment();
            return false;
        }

        /**
         * 计算当前窗口内的请求数
         */
        private long calculateCurrentRequests(long windowStart) {
            long windowStartSlot = windowStart / 1_000_000;
            return timeSlots.entrySet().stream()
                    .filter(entry -> entry.getKey() >= windowStartSlot)
                    .mapToLong(entry -> entry.getValue().sum())
                    .sum();
        }

        /**
         * 清理过期的时间槽
         */
        private void cleanupExpiredSlots(long currentTime) {
            long currentTimeNanos = System.nanoTime();
            long lastCleanup = lastCleanupTime.get();
            
            // 每秒清理一次
            if (currentTimeNanos - lastCleanup > 1_000_000_000L && 
                lastCleanupTime.compareAndSet(lastCleanup, currentTimeNanos)) {
                
                if (cleanupLock.tryLock()) {
                    try {
                        long expiredThreshold = (currentTime - windowSizeNanos) / 1_000_000;
                        timeSlots.entrySet().removeIf(entry -> entry.getKey() < expiredThreshold);
                    } finally {
                        cleanupLock.unlock();
                    }
                }
            }
        }

        /**
         * 获取可用许可数
         */
        public int getAvailablePermits() {
            long currentTime = System.nanoTime();
            long windowStart = currentTime - windowSizeNanos;
            long currentRequests = calculateCurrentRequests(windowStart);
            return Math.max(0, (int) (maxRequests - currentRequests));
        }

        /**
         * 获取性能统计
         */
        public PerformanceStats getStats() {
            return new PerformanceStats(
                totalRequests.sum(),
                rejectedRequests.sum(),
                totalRequests.sum() > 0 ? totalLatency.sum() / totalRequests.sum() : 0,
                maxRequests,
                getAvailablePermits()
            );
        }

        /**
         * 重置统计信息
         */
        public void resetStats() {
            totalRequests.reset();
            rejectedRequests.reset();
            totalLatency.reset();
        }
    }

    /**
     * 性能统计数据类
     */
    public static class PerformanceStats {
        private final long totalRequests;
        private final long rejectedRequests;
        private final long avgLatencyNanos;
        private final int maxPermits;
        private final int availablePermits;

        public PerformanceStats(long totalRequests, long rejectedRequests, long avgLatencyNanos, 
                               int maxPermits, int availablePermits) {
            this.totalRequests = totalRequests;
            this.rejectedRequests = rejectedRequests;
            this.avgLatencyNanos = avgLatencyNanos;
            this.maxPermits = maxPermits;
            this.availablePermits = availablePermits;
        }

        // Getters
        public long getTotalRequests() { return totalRequests; }
        public long getRejectedRequests() { return rejectedRequests; }
        public double getAvgLatencyMs() { return avgLatencyNanos / 1_000_000.0; }
        public double getSuccessRate() { 
            return totalRequests > 0 ? (double)(totalRequests - rejectedRequests) / totalRequests * 100 : 0; 
        }
        public int getMaxPermits() { return maxPermits; }
        public int getAvailablePermits() { return availablePermits; }
        public double getUtilizationRate() { 
            return maxPermits > 0 ? (double)(maxPermits - availablePermits) / maxPermits * 100 : 0; 
        }
    }

    // 各类型限流器实例
    private final SlidingWindowRateLimiter usdmLimiter;
    private final SlidingWindowRateLimiter coinmLimiter;
    private final SlidingWindowRateLimiter order10sLimiter;
    private final SlidingWindowRateLimiter order1mLimiter;

    // 全局统计
    private final LongAdder globalTotalRequests = new LongAdder();
    private final LongAdder globalRejectedRequests = new LongAdder();

    public EnhancedRateLimiter() {
        // 基于ApiLimitConstants初始化各类型限流器
        this.usdmLimiter = new SlidingWindowRateLimiter(
            ApiLimitConstants.USDM_REQUEST_WEIGHT_PER_MINUTE, 
            TimeUnit.MINUTES.toNanos(1)
        );
        
        this.coinmLimiter = new SlidingWindowRateLimiter(
            ApiLimitConstants.COINM_REQUEST_WEIGHT_PER_MINUTE, 
            TimeUnit.MINUTES.toNanos(1)
        );
        
        this.order10sLimiter = new SlidingWindowRateLimiter(
            ApiLimitConstants.ORDER_REQUEST_WEIGHT_PER_10_SECONDS, 
            TimeUnit.SECONDS.toNanos(10)
        );
        
        this.order1mLimiter = new SlidingWindowRateLimiter(
            ApiLimitConstants.ORDER_REQUEST_WEIGHT_PER_MINUTE, 
            TimeUnit.MINUTES.toNanos(1)
        );

        log.info("增强型限流器初始化完成 - USD-M: {}/分钟, COIN-M: {}/分钟, 订单: {}/10秒, {}/分钟",
                ApiLimitConstants.USDM_REQUEST_WEIGHT_PER_MINUTE,
                ApiLimitConstants.COINM_REQUEST_WEIGHT_PER_MINUTE,
                ApiLimitConstants.ORDER_REQUEST_WEIGHT_PER_10_SECONDS,
                ApiLimitConstants.ORDER_REQUEST_WEIGHT_PER_MINUTE);
    }

    /**
     * 获取USD-M期货API许可证
     */
    public boolean tryAcquireUSDM(int weight) {
        return tryAcquireUSDM(weight, 0, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取USD-M期货API许可证（带超时）
     */
    public boolean tryAcquireUSDM(int weight, long timeout, TimeUnit unit) {
        boolean result = usdmLimiter.tryAcquire(weight, unit.toNanos(timeout));
        updateGlobalStats(result);
        return result;
    }

    /**
     * 获取COIN-M期货API许可证
     */
    public boolean tryAcquireCOINM(int weight) {
        return tryAcquireCOINM(weight, 0, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取COIN-M期货API许可证（带超时）
     */
    public boolean tryAcquireCOINM(int weight, long timeout, TimeUnit unit) {
        boolean result = coinmLimiter.tryAcquire(weight, unit.toNanos(timeout));
        updateGlobalStats(result);
        return result;
    }

    /**
     * 获取订单API许可证（10秒限制）
     */
    public boolean tryAcquireOrder10s(int weight) {
        return tryAcquireOrder10s(weight, 0, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取订单API许可证（10秒限制，带超时）
     */
    public boolean tryAcquireOrder10s(int weight, long timeout, TimeUnit unit) {
        boolean result = order10sLimiter.tryAcquire(weight, unit.toNanos(timeout));
        updateGlobalStats(result);
        return result;
    }

    /**
     * 获取订单API许可证（1分钟限制）
     */
    public boolean tryAcquireOrder1m(int weight) {
        return tryAcquireOrder1m(weight, 0, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取订单API许可证（1分钟限制，带超时）
     */
    public boolean tryAcquireOrder1m(int weight, long timeout, TimeUnit unit) {
        boolean result = order1mLimiter.tryAcquire(weight, unit.toNanos(timeout));
        updateGlobalStats(result);
        return result;
    }

    /**
     * 更新全局统计
     */
    private void updateGlobalStats(boolean success) {
        globalTotalRequests.increment();
        if (!success) {
            globalRejectedRequests.increment();
        }
    }

    /**
     * 获取详细的性能统计信息
     */
    public String getDetailedStats() {
        PerformanceStats usdmStats = usdmLimiter.getStats();
        PerformanceStats coinmStats = coinmLimiter.getStats();
        PerformanceStats order10sStats = order10sLimiter.getStats();
        PerformanceStats order1mStats = order1mLimiter.getStats();

        return String.format("""
            增强型限流器详细统计:
            全局统计:
              总请求数: %d
              被拒绝请求数: %d
              全局成功率: %.2f%%
            
            USD-M期货限流:
              请求数: %d, 拒绝数: %d, 成功率: %.2f%%
              平均延迟: %.3fms, 可用许可: %d/%d, 利用率: %.2f%%
            
            COIN-M期货限流:
              请求数: %d, 拒绝数: %d, 成功率: %.2f%%
              平均延迟: %.3fms, 可用许可: %d/%d, 利用率: %.2f%%
            
            订单10秒限流:
              请求数: %d, 拒绝数: %d, 成功率: %.2f%%
              平均延迟: %.3fms, 可用许可: %d/%d, 利用率: %.2f%%
            
            订单1分钟限流:
              请求数: %d, 拒绝数: %d, 成功率: %.2f%%
              平均延迟: %.3fms, 可用许可: %d/%d, 利用率: %.2f%%
            """,
            globalTotalRequests.sum(), globalRejectedRequests.sum(),
            globalTotalRequests.sum() > 0 ? (double)(globalTotalRequests.sum() - globalRejectedRequests.sum()) / globalTotalRequests.sum() * 100 : 0,
            
            usdmStats.getTotalRequests(), usdmStats.getRejectedRequests(), usdmStats.getSuccessRate(),
            usdmStats.getAvgLatencyMs(), usdmStats.getAvailablePermits(), usdmStats.getMaxPermits(), usdmStats.getUtilizationRate(),
            
            coinmStats.getTotalRequests(), coinmStats.getRejectedRequests(), coinmStats.getSuccessRate(),
            coinmStats.getAvgLatencyMs(), coinmStats.getAvailablePermits(), coinmStats.getMaxPermits(), coinmStats.getUtilizationRate(),
            
            order10sStats.getTotalRequests(), order10sStats.getRejectedRequests(), order10sStats.getSuccessRate(),
            order10sStats.getAvgLatencyMs(), order10sStats.getAvailablePermits(), order10sStats.getMaxPermits(), order10sStats.getUtilizationRate(),
            
            order1mStats.getTotalRequests(), order1mStats.getRejectedRequests(), order1mStats.getSuccessRate(),
            order1mStats.getAvgLatencyMs(), order1mStats.getAvailablePermits(), order1mStats.getMaxPermits(), order1mStats.getUtilizationRate()
        );
    }

    /**
     * 检查是否接近限流
     */
    public boolean isNearLimit(String limitType) {
        return switch (limitType.toLowerCase()) {
            case "usdm" -> usdmLimiter.getAvailablePermits() < ApiLimitConstants.USDM_REQUEST_WEIGHT_PER_MINUTE * 0.2;
            case "coinm" -> coinmLimiter.getAvailablePermits() < ApiLimitConstants.COINM_REQUEST_WEIGHT_PER_MINUTE * 0.2;
            case "order_10s" -> order10sLimiter.getAvailablePermits() < ApiLimitConstants.ORDER_REQUEST_WEIGHT_PER_10_SECONDS * 0.2;
            case "order_1m" -> order1mLimiter.getAvailablePermits() < ApiLimitConstants.ORDER_REQUEST_WEIGHT_PER_MINUTE * 0.2;
            default -> false;
        };
    }

    /**
     * 重置所有统计信息
     */
    public void resetAllStats() {
        usdmLimiter.resetStats();
        coinmLimiter.resetStats();
        order10sLimiter.resetStats();
        order1mLimiter.resetStats();
        globalTotalRequests.reset();
        globalRejectedRequests.reset();
        log.info("增强型限流器统计信息已重置");
    }
}
