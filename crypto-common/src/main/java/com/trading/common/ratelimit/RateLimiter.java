package com.trading.common.ratelimit;

import com.trading.common.constant.ApiLimitConstants;
import com.trading.common.enums.ApiRequestWeight;
import com.trading.common.enums.ErrorCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 币安API限流器
 * 基于ApiLimitConstants中定义的限流规则实现
 * 支持USD-M期货和COIN-M期货的不同限流策略
 * 使用令牌桶算法确保API调用不超过限制
 *
 * 币安期货API限流规则（基于ApiLimitConstants）：
 * - USD-M期货：每分钟2400权重
 * - COIN-M期货：每分钟1200权重
 * - 订单相关：每10秒300权重，每分钟1200权重
 *
 * <AUTHOR> System
 * @since 1.0.0
 */
@Component
public class RateLimiter {

    private static final Logger log = LoggerFactory.getLogger(RateLimiter.class);

    // 基于ApiLimitConstants的限流器配置
    private static final int USDM_WEIGHT_LIMIT = ApiLimitConstants.USDM_REQUEST_WEIGHT_PER_MINUTE;
    private static final int COINM_WEIGHT_LIMIT = ApiLimitConstants.COINM_REQUEST_WEIGHT_PER_MINUTE;
    private static final int ORDER_WEIGHT_LIMIT_10S = ApiLimitConstants.ORDER_REQUEST_WEIGHT_PER_10_SECONDS;
    private static final int ORDER_WEIGHT_LIMIT_1M = ApiLimitConstants.ORDER_REQUEST_WEIGHT_PER_MINUTE;
    
    // 时间窗口
    private static final Duration WINDOW_DURATION_1M = Duration.ofMinutes(1);
    private static final Duration WINDOW_DURATION_10S = Duration.ofSeconds(10);

    // USD-M期货和COIN-M期货的信号量
    private final Semaphore usdmSemaphore;
    private final Semaphore coinmSemaphore;
    private final Semaphore orderSemaphore10s;
    private final Semaphore orderSemaphore1m;

    // 请求计数器和重置时间
    private final ConcurrentHashMap<String, AtomicLong> requestCounters;
    private final ConcurrentHashMap<String, Instant> lastResetTime;

    // 统计信息
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong rejectedRequests = new AtomicLong(0);
    private final AtomicLong waitingTime = new AtomicLong(0);

    public RateLimiter() {
        // 基于ApiLimitConstants初始化信号量
        this.usdmSemaphore = new Semaphore(USDM_WEIGHT_LIMIT, true);
        this.coinmSemaphore = new Semaphore(COINM_WEIGHT_LIMIT, true);
        this.orderSemaphore10s = new Semaphore(ORDER_WEIGHT_LIMIT_10S, true);
        this.orderSemaphore1m = new Semaphore(ORDER_WEIGHT_LIMIT_1M, true);

        // 初始化计数器
        this.requestCounters = new ConcurrentHashMap<>();
        this.lastResetTime = new ConcurrentHashMap<>();

        // 初始化各类型的计数器
        String[] limitTypes = {"usdm", "coinm", "order_10s", "order_1m"};
        for (String type : limitTypes) {
            requestCounters.put(type, new AtomicLong(0));
            lastResetTime.put(type, Instant.now());
        }

        log.info("限流器初始化完成 - USD-M期货: {}/分钟, COIN-M期货: {}/分钟, 订单限制: {}/10秒, {}/分钟",
                USDM_WEIGHT_LIMIT, COINM_WEIGHT_LIMIT, ORDER_WEIGHT_LIMIT_10S, ORDER_WEIGHT_LIMIT_1M);
    }
    
    /**
     * 获取USD-M期货API许可证
     *
     * @param weight 请求权重
     * @return 是否成功获取许可证
     */
    public boolean tryAcquireUSDM(int weight) {
        return tryAcquireUSDM(weight, 0, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取USD-M期货API许可证（带超时）
     *
     * @param weight 请求权重
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否成功获取许可证
     */
    public boolean tryAcquireUSDM(int weight, long timeout, TimeUnit unit) {
        return tryAcquireInternal("usdm", usdmSemaphore, weight, timeout, unit);
    }

    /**
     * 获取COIN-M期货API许可证
     *
     * @param weight 请求权重
     * @return 是否成功获取许可证
     */
    public boolean tryAcquireCOINM(int weight) {
        return tryAcquireCOINM(weight, 0, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取COIN-M期货API许可证（带超时）
     *
     * @param weight 请求权重
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否成功获取许可证
     */
    public boolean tryAcquireCOINM(int weight, long timeout, TimeUnit unit) {
        return tryAcquireInternal("coinm", coinmSemaphore, weight, timeout, unit);
    }

    /**
     * 获取订单API许可证（10秒限制）
     *
     * @param weight 请求权重
     * @return 是否成功获取许可证
     */
    public boolean tryAcquireOrder10s(int weight) {
        return tryAcquireOrder10s(weight, 0, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取订单API许可证（10秒限制，带超时）
     *
     * @param weight 请求权重
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否成功获取许可证
     */
    public boolean tryAcquireOrder10s(int weight, long timeout, TimeUnit unit) {
        return tryAcquireInternal("order_10s", orderSemaphore10s, weight, timeout, unit);
    }

    /**
     * 获取订单API许可证（1分钟限制）
     *
     * @param weight 请求权重
     * @return 是否成功获取许可证
     */
    public boolean tryAcquireOrder1m(int weight) {
        return tryAcquireOrder1m(weight, 0, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取订单API许可证（1分钟限制，带超时）
     *
     * @param weight 请求权重
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否成功获取许可证
     */
    public boolean tryAcquireOrder1m(int weight, long timeout, TimeUnit unit) {
        return tryAcquireInternal("order_1m", orderSemaphore1m, weight, timeout, unit);
    }
    
    /**
     * 内部获取许可证方法
     *
     * @param limitType 限制类型
     * @param semaphore 信号量
     * @param weight 请求权重
     * @param timeout 超时时间
     * @param unit 时间单位
     * @return 是否成功获取许可证
     */
    private boolean tryAcquireInternal(String limitType, Semaphore semaphore, int weight, long timeout, TimeUnit unit) {
        try {
            // 重置计数器（如果需要）
            resetCounterIfNeeded(limitType);

            // 快速路径：如果超时为0，直接尝试获取，避免时间计算开销
            boolean acquired;
            if (timeout == 0) {
                acquired = semaphore.tryAcquire(weight);
            } else {
                acquired = semaphore.tryAcquire(weight, timeout, unit);
            }

            if (acquired) {
                // 更新计数器
                requestCounters.get(limitType).addAndGet(weight);
                totalRequests.incrementAndGet();

                // 只在debug模式下记录详细日志，减少日志开销
                if (log.isDebugEnabled()) {
                    log.debug("获取限流许可成功: limitType={}, weight={}, available={}",
                            limitType, weight, semaphore.availablePermits());
                }
                return true;
            } else {
                rejectedRequests.incrementAndGet();
                // 在高并发测试中减少日志输出以提升性能
                // 只在debug模式下记录详细的拒绝信息
                if (log.isDebugEnabled()) {
                    log.debug("获取限流许可失败: limitType={}, weight={}, timeout={}ms, available={}",
                            limitType, weight, unit.toMillis(timeout), semaphore.availablePermits());
                }
                return false;
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            rejectedRequests.incrementAndGet();
            log.error("获取限流许可被中断: limitType={}, weight={}", limitType, weight, e);
            return false;
        }
    }

    /**
     * 释放许可证
     *
     * @param limitType 限制类型
     * @param weight 权重
     */
    public void release(String limitType, int weight) {
        Semaphore semaphore = getSemaphoreByType(limitType);
        if (semaphore != null) {
            semaphore.release(weight);
            log.debug("释放限流许可: limitType={}, weight={}, available={}", limitType, weight, semaphore.availablePermits());
        }
    }
    
    /**
     * 根据类型获取对应的信号量
     *
     * @param limitType 限制类型
     * @return 对应的信号量
     */
    private Semaphore getSemaphoreByType(String limitType) {
        switch (limitType) {
            case "usdm":
                return usdmSemaphore;
            case "coinm":
                return coinmSemaphore;
            case "order_10s":
                return orderSemaphore10s;
            case "order_1m":
                return orderSemaphore1m;
            default:
                log.warn("不支持的限制类型: {}", limitType);
                return null;
        }
    }

    /**
     * 重置计数器（如果需要）
     *
     * @param limitType 限制类型
     */
    private void resetCounterIfNeeded(String limitType) {
        Instant now = Instant.now();
        Instant lastReset = lastResetTime.get(limitType);

        Duration windowDuration = limitType.equals("order_10s") ? WINDOW_DURATION_10S : WINDOW_DURATION_1M;

        if (Duration.between(lastReset, now).compareTo(windowDuration) >= 0) {
            // 重置信号量
            Semaphore semaphore = getSemaphoreByType(limitType);
            if (semaphore != null) {
                int limit = getLimitByType(limitType);
                int available = semaphore.availablePermits();
                int used = limit - available;

                // 释放所有许可证并重新设置
                if (used > 0) {
                    semaphore.release(used);
                }

                // 重置计数器和时间
                requestCounters.get(limitType).set(0);
                lastResetTime.put(limitType, now);

                log.debug("重置限流器: limitType={}, used={}, limit={}, window={}",
                    limitType, used, limit, windowDuration);
            }
        }
    }

    /**
     * 获取限制类型对应的限制数量
     *
     * @param limitType 限制类型
     * @return 限制数量
     */
    private int getLimitByType(String limitType) {
        switch (limitType) {
            case "usdm":
                return USDM_WEIGHT_LIMIT;
            case "coinm":
                return COINM_WEIGHT_LIMIT;
            case "order_10s":
                return ORDER_WEIGHT_LIMIT_10S;
            case "order_1m":
                return ORDER_WEIGHT_LIMIT_1M;
            default:
                throw new IllegalArgumentException("不支持的限制类型: " + limitType);
        }
    }
    
    /**
     * 获取限流统计信息
     *
     * @return 限流统计信息字符串
     */
    public String getStats() {
        StringBuilder stats = new StringBuilder("限流器统计信息:\n");
        stats.append(String.format("  总请求数: %d\n", totalRequests.get()));
        stats.append(String.format("  被拒绝请求数: %d\n", rejectedRequests.get()));
        stats.append(String.format("  平均等待时间: %dms\n",
            totalRequests.get() > 0 ? waitingTime.get() / totalRequests.get() : 0));
        stats.append(String.format("  USD-M期货可用许可: %d/%d\n",
            usdmSemaphore.availablePermits(), USDM_WEIGHT_LIMIT));
        stats.append(String.format("  COIN-M期货可用许可: %d/%d\n",
            coinmSemaphore.availablePermits(), COINM_WEIGHT_LIMIT));
        stats.append(String.format("  订单10秒可用许可: %d/%d\n",
            orderSemaphore10s.availablePermits(), ORDER_WEIGHT_LIMIT_10S));
        stats.append(String.format("  订单1分钟可用许可: %d/%d\n",
            orderSemaphore1m.availablePermits(), ORDER_WEIGHT_LIMIT_1M));
        return stats.toString();
    }

    /**
     * 检查是否接近限流
     *
     * @param limitType 限制类型
     * @return 是否接近限流（可用许可证少于20%）
     */
    public boolean isNearLimit(String limitType) {
        Semaphore semaphore = getSemaphoreByType(limitType);
        if (semaphore == null) {
            return false;
        }

        int limit = getLimitByType(limitType);
        int available = semaphore.availablePermits();

        return available < (limit * 0.2);
    }

    /**
     * 重置所有统计信息
     */
    public void resetStats() {
        totalRequests.set(0);
        rejectedRequests.set(0);
        waitingTime.set(0);

        // 重置所有计数器
        requestCounters.values().forEach(counter -> counter.set(0));

        // 重置所有时间戳
        Instant now = Instant.now();
        lastResetTime.replaceAll((key, value) -> now);

        log.info("限流器统计信息已重置");
    }
}
