package com.trading.common.ratelimit;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 高性能令牌桶限流器
 * 基于令牌桶算法实现，支持突发流量和平滑限流
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class TokenBucket {
    
    private final long capacity;           // 桶容量
    private final long refillRate;         // 令牌补充速率（每秒）
    private final AtomicLong tokens;       // 当前令牌数
    private final AtomicLong lastRefillTime; // 上次补充时间
    private final ReentrantLock lock = new ReentrantLock();
    
    /**
     * 构造函数
     * 
     * @param capacity 桶容量
     * @param refillRate 每秒补充的令牌数
     */
    public TokenBucket(long capacity, long refillRate) {
        this.capacity = capacity;
        this.refillRate = refillRate;
        this.tokens = new AtomicLong(capacity);
        this.lastRefillTime = new AtomicLong(System.nanoTime());
    }
    
    /**
     * 高性能令牌获取算法 - 减少CAS操作和锁竞争
     *
     * @param tokensRequested 请求的令牌数
     * @return 是否成功获取
     */
    public boolean tryAcquire(long tokensRequested) {
        if (tokensRequested <= 0) {
            return true;
        }

        if (tokensRequested > capacity) {
            return false; // 请求的令牌数超过桶容量
        }

        refill();

        // 优化的CAS循环 - 减少失败重试
        for (int attempts = 0; attempts < 3; attempts++) {
            long currentTokens = tokens.get();
            if (currentTokens < tokensRequested) {
                return false; // 令牌不足，直接返回
            }

            // 使用CAS操作确保线程安全
            if (tokens.compareAndSet(currentTokens, currentTokens - tokensRequested)) {
                return true;
            }

            // 短暂让出CPU，减少竞争
            if (attempts < 2) {
                Thread.onSpinWait(); // JDK9+ 优化的自旋等待
            }
        }

        return false; // CAS失败次数过多，返回失败
    }
    
    /**
     * 高性能令牌补充算法 - 减少锁使用和提高精度
     */
    private void refill() {
        long now = System.nanoTime();
        long lastRefill = lastRefillTime.get();

        // 计算时间差，避免频繁的时间检查
        long elapsedNanos = now - lastRefill;
        if (elapsedNanos <= 0) {
            return; // 时间未推进，无需补充
        }

        // 计算需要补充的令牌数 - 使用更精确的计算
        long tokensToAdd = (elapsedNanos * refillRate) / 1_000_000_000L;

        if (tokensToAdd > 0) {
            // 优化的无锁补充算法
            if (lastRefillTime.compareAndSet(lastRefill, now)) {
                // 成功更新时间戳，进行令牌补充
                long currentTokens, newTokens;
                do {
                    currentTokens = tokens.get();
                    newTokens = Math.min(capacity, currentTokens + tokensToAdd);

                    // 如果令牌数没有变化，直接返回
                    if (newTokens == currentTokens) {
                        return;
                    }
                } while (!tokens.compareAndSet(currentTokens, newTokens));
            }
        }
    }
    
    /**
     * 获取当前可用令牌数
     * 
     * @return 当前令牌数
     */
    public long getAvailableTokens() {
        refill();
        return tokens.get();
    }
    
    /**
     * 获取桶容量
     * 
     * @return 桶容量
     */
    public long getCapacity() {
        return capacity;
    }
    
    /**
     * 获取补充速率
     * 
     * @return 每秒补充的令牌数
     */
    public long getRefillRate() {
        return refillRate;
    }
}
