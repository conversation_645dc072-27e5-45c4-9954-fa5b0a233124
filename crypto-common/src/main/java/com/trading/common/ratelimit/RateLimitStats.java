package com.trading.common.ratelimit;

import lombok.Builder;
import lombok.Data;

/**
 * 限流统计信息
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
public class RateLimitStats {
    
    /**
     * 总请求数
     */
    private long totalRequests;
    
    /**
     * 被拒绝的请求数
     */
    private long rejectedRequests;
    
    /**
     * 平均等待时间（毫秒）
     */
    private long averageWaitTime;
    
    /**
     * 权重1可用许可证数
     */
    private int weight1Available;
    
    /**
     * 权重5可用许可证数
     */
    private int weight5Available;
    
    /**
     * 权重10可用许可证数
     */
    private int weight10Available;
    
    /**
     * 权重20可用许可证数
     */
    private int weight20Available;
    
    /**
     * 权重50可用许可证数
     */
    private int weight50Available;
    
    /**
     * 计算拒绝率
     * 
     * @return 拒绝率（0-1之间）
     */
    public double getRejectionRate() {
        if (totalRequests == 0) {
            return 0.0;
        }
        return (double) rejectedRequests / totalRequests;
    }
    
    /**
     * 计算成功率
     * 
     * @return 成功率（0-1之间）
     */
    public double getSuccessRate() {
        return 1.0 - getRejectionRate();
    }
    
    /**
     * 检查是否健康
     * 
     * @return 是否健康（拒绝率低于10%）
     */
    public boolean isHealthy() {
        return getRejectionRate() < 0.1;
    }
    
    @Override
    public String toString() {
        return String.format(
                "RateLimitStats{totalRequests=%d, rejectedRequests=%d, rejectionRate=%.2f%%, " +
                "averageWaitTime=%dms, available=[w1:%d, w5:%d, w10:%d, w20:%d, w50:%d]}",
                totalRequests, rejectedRequests, getRejectionRate() * 100,
                averageWaitTime, weight1Available, weight5Available, weight10Available,
                weight20Available, weight50Available
        );
    }
}
