package com.trading.common.recovery;

import com.trading.common.client.BinanceClientPoolProvider;
import com.trading.common.exception.SdkException;
import com.trading.common.circuit.CircuitBreakerManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 错误恢复管理器
 * 负责自动错误恢复、连接重建和健康检查
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class ErrorRecoveryManager {
    
    private static final Logger log = LoggerFactory.getLogger(ErrorRecoveryManager.class);
    
    // 恢复配置
    private static final Duration HEALTH_CHECK_INTERVAL = Duration.ofSeconds(30);
    private static final Duration RECOVERY_TIMEOUT = Duration.ofSeconds(5);
    private static final int MAX_RECOVERY_ATTEMPTS = 3;
    
    private final CircuitBreakerManager circuitBreakerManager;
    private final BinanceClientPoolProvider clientPool;
    private final ScheduledExecutorService recoveryExecutor;
    private final AtomicBoolean isRecoveryActive = new AtomicBoolean(false);
    private final AtomicInteger recoveryAttempts = new AtomicInteger(0);
    private volatile Instant lastRecoveryTime = Instant.now();
    
    /**
     * 构造函数
     * 
     * @param circuitBreakerManager 熔断器管理器
     * @param clientPool 客户端连接池
     */
    public ErrorRecoveryManager(CircuitBreakerManager circuitBreakerManager,
                               BinanceClientPoolProvider clientPool) {
        this.circuitBreakerManager = circuitBreakerManager;
        this.clientPool = clientPool;
        this.recoveryExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "error-recovery-" + System.currentTimeMillis());
            thread.setDaemon(true);
            return thread;
        });
        
        // 启动健康检查
        startHealthCheck();
        
        log.info("错误恢复管理器已初始化");
    }
    
    /**
     * 启动健康检查
     */
    private void startHealthCheck() {
        recoveryExecutor.scheduleWithFixedDelay(
                this::performHealthCheck,
                HEALTH_CHECK_INTERVAL.toSeconds(),
                HEALTH_CHECK_INTERVAL.toSeconds(),
                TimeUnit.SECONDS
        );
        
        log.info("健康检查已启动，检查间隔: {}秒", HEALTH_CHECK_INTERVAL.toSeconds());
    }
    
    /**
     * 执行健康检查
     */
    private void performHealthCheck() {
        try {
            log.debug("执行系统健康检查");
            
            // 检查客户端连接状态
            checkClientHealth();
            
            // 检查熔断器状态
            checkCircuitBreakerHealth();
            
            log.debug("健康检查完成");
            
        } catch (Exception e) {
            log.error("健康检查失败", e);
        }
    }
    
    /**
     * 检查客户端健康状态
     */
    private void checkClientHealth() {
        try {
            // 检查UM期货客户端
            if (!isClientHealthy("UM_FUTURES")) {
                log.warn("UM期货客户端健康检查失败，尝试恢复");
                triggerRecovery("UM_FUTURES_CLIENT");
            }
            
            // 检查CM期货客户端
            if (!isClientHealthy("CM_FUTURES")) {
                log.warn("CM期货客户端健康检查失败，尝试恢复");
                triggerRecovery("CM_FUTURES_CLIENT");
            }
            
        } catch (Exception e) {
            log.error("客户端健康检查失败", e);
        }
    }
    
    /**
     * 检查客户端是否健康
     * 
     * @param clientType 客户端类型
     * @return 如果健康返回true，否则返回false
     */
    private boolean isClientHealthy(String clientType) {
        try {
            // 这里可以实现具体的健康检查逻辑
            // 例如发送ping请求或检查连接状态
            return true;
        } catch (Exception e) {
            log.debug("客户端健康检查失败: {}", clientType, e);
            return false;
        }
    }
    
    /**
     * 检查熔断器健康状态
     */
    private void checkCircuitBreakerHealth() {
        try {
            String status = circuitBreakerManager.getCircuitBreakerStatus();
            log.debug("熔断器状态: {}", status);
            
            // 检查是否有过多的开启状态熔断器
            // 如果有，可以考虑触发恢复操作
            
        } catch (Exception e) {
            log.error("熔断器健康检查失败", e);
        }
    }
    
    /**
     * 触发错误恢复
     * 
     * @param component 组件名称
     */
    public void triggerRecovery(String component) {
        if (!isRecoveryActive.compareAndSet(false, true)) {
            log.debug("恢复操作已在进行中，跳过: {}", component);
            return;
        }
        
        try {
            log.info("开始错误恢复: component={}", component);
            
            CompletableFuture<Void> recoveryFuture = CompletableFuture.runAsync(() -> {
                try {
                    performRecovery(component);
                } catch (Exception e) {
                    log.error("恢复操作失败: component={}", component, e);
                }
            }, recoveryExecutor);
            
            // 设置恢复超时
            recoveryFuture.orTimeout(RECOVERY_TIMEOUT.toSeconds(), TimeUnit.SECONDS)
                    .whenComplete((result, throwable) -> {
                        isRecoveryActive.set(false);
                        if (throwable != null) {
                            log.error("恢复操作超时或失败: component={}", component, throwable);
                        } else {
                            log.info("恢复操作完成: component={}", component);
                        }
                    });
            
        } catch (Exception e) {
            isRecoveryActive.set(false);
            log.error("触发恢复操作失败: component={}", component, e);
        }
    }
    
    /**
     * 执行恢复操作
     * 
     * @param component 组件名称
     */
    private void performRecovery(String component) {
        int attempts = recoveryAttempts.incrementAndGet();
        
        if (attempts > MAX_RECOVERY_ATTEMPTS) {
            log.error("恢复尝试次数超过最大限制: component={}, attempts={}", component, attempts);
            return;
        }
        
        try {
            log.info("执行恢复操作: component={}, attempt={}/{}", component, attempts, MAX_RECOVERY_ATTEMPTS);
            
            switch (component) {
                case "UM_FUTURES_CLIENT":
                    recoverUMFuturesClient();
                    break;
                case "CM_FUTURES_CLIENT":
                    recoverCMFuturesClient();
                    break;
                case "WEBSOCKET_CONNECTION":
                    recoverWebSocketConnection();
                    break;
                case "CIRCUIT_BREAKER":
                    recoverCircuitBreaker();
                    break;
                default:
                    log.warn("未知的恢复组件: {}", component);
                    return;
            }
            
            // 恢复成功，重置计数器
            recoveryAttempts.set(0);
            lastRecoveryTime = Instant.now();
            
            log.info("恢复操作成功: component={}", component);
            
        } catch (Exception e) {
            log.error("恢复操作失败: component={}, attempt={}", component, attempts, e);
            
            // 如果还有重试机会，延迟后重试
            if (attempts < MAX_RECOVERY_ATTEMPTS) {
                long delayMs = calculateRecoveryDelay(attempts);
                log.info("将在{}ms后重试恢复: component={}", delayMs, component);
                
                recoveryExecutor.schedule(() -> performRecovery(component), delayMs, TimeUnit.MILLISECONDS);
            }
        }
    }
    
    /**
     * 恢复UM期货客户端
     */
    private void recoverUMFuturesClient() throws Exception {
        log.info("恢复UM期货客户端");
        
        try {
            // 重新初始化客户端
            clientPool.refreshUMClientPool();
            
            // 重置相关熔断器
            circuitBreakerManager.getClientCircuitBreaker("UM_FUTURES").forceClose();
            
            log.info("UM期货客户端恢复成功");
            
        } catch (Exception e) {
            log.error("UM期货客户端恢复失败", e);
            throw e;
        }
    }
    
    /**
     * 恢复CM期货客户端
     */
    private void recoverCMFuturesClient() throws Exception {
        log.info("恢复CM期货客户端");
        
        try {
            // 重新初始化客户端
            clientPool.refreshCMClientPool();
            
            // 重置相关熔断器
            circuitBreakerManager.getClientCircuitBreaker("CM_FUTURES").forceClose();
            
            log.info("CM期货客户端恢复成功");
            
        } catch (Exception e) {
            log.error("CM期货客户端恢复失败", e);
            throw e;
        }
    }
    
    /**
     * 恢复WebSocket连接
     */
    private void recoverWebSocketConnection() throws Exception {
        log.info("恢复WebSocket连接");
        
        try {
            // 这里可以实现WebSocket连接恢复逻辑
            // 例如重新建立连接、重新订阅等
            
            log.info("WebSocket连接恢复成功");
            
        } catch (Exception e) {
            log.error("WebSocket连接恢复失败", e);
            throw e;
        }
    }
    
    /**
     * 恢复熔断器
     */
    private void recoverCircuitBreaker() throws Exception {
        log.info("恢复熔断器");
        
        try {
            // 重置所有熔断器
            circuitBreakerManager.resetAllCircuitBreakers();
            
            log.info("熔断器恢复成功");
            
        } catch (Exception e) {
            log.error("熔断器恢复失败", e);
            throw e;
        }
    }
    
    /**
     * 计算恢复延迟时间
     * 
     * @param attempt 尝试次数
     * @return 延迟时间（毫秒）
     */
    private long calculateRecoveryDelay(int attempt) {
        // 指数退避：1秒、2秒、4秒
        return Math.min(1000L * (1L << (attempt - 1)), 4000L);
    }
    
    /**
     * 获取恢复状态
     * 
     * @return 恢复状态信息
     */
    public String getRecoveryStatus() {
        return String.format("恢复状态: active=%s, attempts=%d, lastRecovery=%s",
                isRecoveryActive.get(),
                recoveryAttempts.get(),
                lastRecoveryTime);
    }
    
    /**
     * 强制触发全面恢复
     */
    public void forceFullRecovery() {
        log.warn("强制触发全面恢复");
        
        triggerRecovery("UM_FUTURES_CLIENT");
        triggerRecovery("CM_FUTURES_CLIENT");
        triggerRecovery("CIRCUIT_BREAKER");
    }
    
    /**
     * 关闭恢复管理器
     */
    public void shutdown() {
        log.info("关闭错误恢复管理器");
        
        recoveryExecutor.shutdown();
        try {
            if (!recoveryExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                recoveryExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            recoveryExecutor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}
