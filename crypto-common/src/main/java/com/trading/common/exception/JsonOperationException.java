package com.trading.common.exception;

/**
 * Custom exception for JSON processing errors.
 * This exception is thrown when there is an issue with JSON serialization or deserialization.
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class JsonOperationException extends RuntimeException {

    /**
     * Constructs a new JsonOperationException with the specified detail message.
     *
     * @param message the detail message.
     */
    public JsonOperationException(String message) {
        super(message);
    }

    /**
     * Constructs a new JsonOperationException with the specified detail message and cause.
     *
     * @param message the detail message.
     * @param cause   the cause.
     */
    public JsonOperationException(String message, Throwable cause) {
        super(message, cause);
    }
}
