package com.trading.common.exception;

import lombok.Getter;

/**
 * 基础异常类
 * 所有自定义异常的父类，提供统一的异常处理机制
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Getter
public abstract class BaseException extends RuntimeException {
    
    /**
     * 错误代码
     */
    private final String errorCode;
    
    /**
     * 错误消息
     */
    private final String errorMessage;
    
    /**
     * 错误详情
     */
    private final Object errorDetails;
    
    /**
     * 时间戳
     */
    private final long timestamp;
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     */
    public BaseException(String errorCode, String errorMessage) {
        this(errorCode, errorMessage, null, null);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public BaseException(String errorCode, String errorMessage, Throwable cause) {
        this(errorCode, errorMessage, null, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     */
    public BaseException(String errorCode, String errorMessage, Object errorDetails) {
        this(errorCode, errorMessage, errorDetails, null);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     * @param cause 原因异常
     */
    public BaseException(String errorCode, String errorMessage, Object errorDetails, Throwable cause) {
        super(errorMessage, cause);
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
        this.errorDetails = errorDetails;
        this.timestamp = System.currentTimeMillis();
    }
    
    /**
     * 获取错误代码（别名方法，兼容测试）
     *
     * @return 错误代码
     */
    public String getErrorCodes() {
        return errorCode;
    }

    /**
     * 获取错误详情（别名方法，兼容测试）
     *
     * @return 错误详情
     */
    public Object getData() {
        return errorDetails;
    }

    /**
     * 获取异常类型
     *
     * @return 异常类型
     */
    public abstract String getExceptionType();
    
    /**
     * 是否为可重试异常
     * 
     * @return true表示可重试，false表示不可重试
     */
    public boolean isRetryable() {
        return false;
    }
    
    /**
     * 获取异常的严重级别
     * 
     * @return 严重级别
     */
    public SeverityLevel getSeverityLevel() {
        return SeverityLevel.ERROR;
    }
    
    /**
     * 严重级别枚举
     */
    public enum SeverityLevel {
        /**
         * 信息级别
         */
        INFO,
        
        /**
         * 警告级别
         */
        WARN,
        
        /**
         * 错误级别
         */
        ERROR,
        
        /**
         * 致命级别
         */
        FATAL
    }
    
    @Override
    public String toString() {
        return String.format("%s{errorCode='%s', errorMessage='%s', errorDetails=%s, timestamp=%d}", 
                getClass().getSimpleName(), errorCode, errorMessage, errorDetails, timestamp);
    }
}
