package com.trading.common.exception;

/**
 * SDK异常基类
 * 继承自crypto-common的TradeException
 */
public class SdkException extends TradeException {

    public SdkException(String message) {
        super(message);
    }

    public SdkException(String message, Throwable cause) {
        super("SDK_ERROR", message, cause);
    }

    public SdkException(String errorCode, String message) {
        super(errorCode, message);
    }

    public SdkException(String errorCode, String message, Throwable cause) {
        super(errorCode, message, cause);
    }
}
