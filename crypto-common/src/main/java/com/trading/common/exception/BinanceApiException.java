package com.trading.common.exception;

/**
 * 币安API异常
 * 封装币安API调用过程中的异常
 */
public class BinanceApiException extends SdkException {

    private final int httpStatusCode;
    private final String binanceErrorCode;

    public BinanceApiException(String message) {
        super(message);
        this.httpStatusCode = -1;
        this.binanceErrorCode = null;
    }

    public BinanceApiException(String message, Throwable cause) {
        super(message, cause);
        this.httpStatusCode = -1;
        this.binanceErrorCode = null;
    }

    public BinanceApiException(int httpStatusCode, String binanceErrorCode, String message) {
        super("BINANCE_API_ERROR", message);
        this.httpStatusCode = httpStatusCode;
        this.binanceErrorCode = binanceErrorCode;
    }

    public BinanceApiException(int httpStatusCode, String binanceErrorCode, String message, Throwable cause) {
        super("BINANCE_API_ERROR", message, cause);
        this.httpStatusCode = httpStatusCode;
        this.binanceErrorCode = binanceErrorCode;
    }

    public int getHttpStatusCode() {
        return httpStatusCode;
    }

    public String getBinanceErrorCode() {
        return binanceErrorCode;
    }

    @Override
    public String toString() {
        return String.format("BinanceApiException{httpStatusCode=%d, binanceErrorCode='%s', message='%s'}", 
                httpStatusCode, binanceErrorCode, getMessage());
    }
}
