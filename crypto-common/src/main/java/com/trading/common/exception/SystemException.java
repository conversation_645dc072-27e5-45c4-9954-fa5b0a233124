package com.trading.common.exception;

/**
 * 系统异常类
 * 用于处理系统级别的异常情况，如配置错误、资源不可用等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class SystemException extends BaseException {
    
    private static final String EXCEPTION_TYPE = "SYSTEM";

    /**
     * 构造函数（兼容测试）
     *
     * @param errorMessage 错误消息
     */
    public SystemException(String errorMessage) {
        super("SYSTEM_ERROR", errorMessage);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     */
    public SystemException(String errorCode, String errorMessage) {
        super(errorCode, errorMessage);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public SystemException(String errorCode, String errorMessage, Throwable cause) {
        super(errorCode, errorMessage, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     */
    public SystemException(String errorCode, String errorMessage, Object errorDetails) {
        super(errorCode, errorMessage, errorDetails);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     * @param cause 原因异常
     */
    public SystemException(String errorCode, String errorMessage, Object errorDetails, Throwable cause) {
        super(errorCode, errorMessage, errorDetails, cause);
    }
    
    @Override
    public String getExceptionType() {
        return EXCEPTION_TYPE;
    }
    
    @Override
    public SeverityLevel getSeverityLevel() {
        return SeverityLevel.FATAL;
    }
    
    @Override
    public boolean isRetryable() {
        // 某些系统异常可能是可重试的，如网络连接异常
        return getErrorCode().startsWith("NETWORK_") || getErrorCode().startsWith("TIMEOUT_");
    }
    
    /**
     * 创建配置错误异常
     * 
     * @param configKey 配置键
     * @param configValue 配置值
     * @return SystemException
     */
    public static SystemException configurationError(String configKey, Object configValue) {
        return new SystemException(
                "CONFIGURATION_ERROR",
                String.format("Configuration error: %s = %s", configKey, configValue),
                configValue
        );
    }
    
    /**
     * 创建资源不可用异常
     * 
     * @param resourceType 资源类型
     * @param resourceName 资源名称
     * @return SystemException
     */
    public static SystemException resourceUnavailable(String resourceType, String resourceName) {
        return new SystemException(
                "RESOURCE_UNAVAILABLE",
                String.format("%s unavailable: %s", resourceType, resourceName),
                resourceName
        );
    }
    
    /**
     * 创建网络连接异常
     * 
     * @param endpoint 端点
     * @param cause 原因异常
     * @return SystemException
     */
    public static SystemException networkConnectionError(String endpoint, Throwable cause) {
        return new SystemException(
                "NETWORK_CONNECTION_ERROR",
                String.format("Network connection error to: %s", endpoint),
                endpoint,
                cause
        );
    }
    
    /**
     * 创建超时异常
     * 
     * @param operation 操作名称
     * @param timeoutMs 超时时间（毫秒）
     * @return SystemException
     */
    public static SystemException timeoutError(String operation, long timeoutMs) {
        return new SystemException(
                "TIMEOUT_ERROR",
                String.format("Operation timeout: %s (timeout: %dms)", operation, timeoutMs),
                timeoutMs
        );
    }
    
    /**
     * 创建数据库连接异常
     * 
     * @param databaseName 数据库名称
     * @param cause 原因异常
     * @return SystemException
     */
    public static SystemException databaseConnectionError(String databaseName, Throwable cause) {
        return new SystemException(
                "DATABASE_CONNECTION_ERROR",
                String.format("Database connection error: %s", databaseName),
                databaseName,
                cause
        );
    }
    
    /**
     * 创建初始化失败异常
     *
     * @param componentName 组件名称
     * @param cause 原因异常
     * @return SystemException
     */
    public static SystemException initializationError(String componentName, Throwable cause) {
        return new SystemException(
                "INITIALIZATION_ERROR",
                String.format("Component initialization failed: %s", componentName),
                componentName,
                cause
        );
    }

    /**
     * 静态工厂方法
     *
     * @param message 错误消息
     * @return SystemException
     */
    public static SystemException of(String message) {
        return new SystemException(message);
    }
}
