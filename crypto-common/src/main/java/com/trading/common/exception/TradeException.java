package com.trading.common.exception;

/**
 * 交易异常类
 * 用于处理交易相关的异常情况，如订单失败、余额不足等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class TradeException extends BaseException {
    
    private static final String EXCEPTION_TYPE = "TRADE";

    /**
     * 构造函数（兼容测试）
     *
     * @param errorMessage 错误消息
     */
    public TradeException(String errorMessage) {
        super("TRADE_ERROR", errorMessage);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     */
    public TradeException(String errorCode, String errorMessage) {
        super(errorCode, errorMessage);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public TradeException(String errorCode, String errorMessage, Throwable cause) {
        super(errorCode, errorMessage, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     */
    public TradeException(String errorCode, String errorMessage, Object errorDetails) {
        super(errorCode, errorMessage, errorDetails);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     * @param cause 原因异常
     */
    public TradeException(String errorCode, String errorMessage, Object errorDetails, Throwable cause) {
        super(errorCode, errorMessage, errorDetails, cause);
    }
    
    @Override
    public String getExceptionType() {
        return EXCEPTION_TYPE;
    }
    
    @Override
    public SeverityLevel getSeverityLevel() {
        return SeverityLevel.ERROR;
    }
    
    @Override
    public boolean isRetryable() {
        // 某些交易异常可能是可重试的，如网络异常导致的订单失败
        return getErrorCode().equals("ORDER_NETWORK_ERROR") || 
               getErrorCode().equals("ORDER_TIMEOUT") ||
               getErrorCode().equals("EXCHANGE_UNAVAILABLE");
    }
    
    /**
     * 创建余额不足异常
     * 
     * @param symbol 交易对
     * @param requiredAmount 需要金额
     * @param availableAmount 可用金额
     * @return TradeException
     */
    public static TradeException insufficientBalance(String symbol, String requiredAmount, String availableAmount) {
        return new TradeException(
                "INSUFFICIENT_BALANCE",
                String.format("Insufficient balance for %s: required=%s, available=%s", 
                        symbol, requiredAmount, availableAmount),
                String.format("symbol=%s,required=%s,available=%s", symbol, requiredAmount, availableAmount)
        );
    }
    
    /**
     * 创建订单失败异常
     * 
     * @param orderId 订单ID
     * @param reason 失败原因
     * @return TradeException
     */
    public static TradeException orderFailed(String orderId, String reason) {
        return new TradeException(
                "ORDER_FAILED",
                String.format("Order failed: %s, reason: %s", orderId, reason),
                String.format("orderId=%s,reason=%s", orderId, reason)
        );
    }
    
    /**
     * 创建订单不存在异常
     * 
     * @param orderId 订单ID
     * @return TradeException
     */
    public static TradeException orderNotFound(String orderId) {
        return new TradeException(
                "ORDER_NOT_FOUND",
                String.format("Order not found: %s", orderId),
                orderId
        );
    }
    
    /**
     * 创建价格超出限制异常
     * 
     * @param symbol 交易对
     * @param price 价格
     * @param minPrice 最小价格
     * @param maxPrice 最大价格
     * @return TradeException
     */
    public static TradeException priceOutOfRange(String symbol, String price, String minPrice, String maxPrice) {
        return new TradeException(
                "PRICE_OUT_OF_RANGE",
                String.format("Price out of range for %s: price=%s, range=[%s, %s]", 
                        symbol, price, minPrice, maxPrice),
                String.format("symbol=%s,price=%s,min=%s,max=%s", symbol, price, minPrice, maxPrice)
        );
    }
    
    /**
     * 创建数量超出限制异常
     * 
     * @param symbol 交易对
     * @param quantity 数量
     * @param minQuantity 最小数量
     * @param maxQuantity 最大数量
     * @return TradeException
     */
    public static TradeException quantityOutOfRange(String symbol, String quantity, String minQuantity, String maxQuantity) {
        return new TradeException(
                "QUANTITY_OUT_OF_RANGE",
                String.format("Quantity out of range for %s: quantity=%s, range=[%s, %s]", 
                        symbol, quantity, minQuantity, maxQuantity),
                String.format("symbol=%s,quantity=%s,min=%s,max=%s", symbol, quantity, minQuantity, maxQuantity)
        );
    }
    
    /**
     * 创建交易所不可用异常
     * 
     * @param exchangeName 交易所名称
     * @return TradeException
     */
    public static TradeException exchangeUnavailable(String exchangeName) {
        return new TradeException(
                "EXCHANGE_UNAVAILABLE",
                String.format("Exchange unavailable: %s", exchangeName),
                exchangeName
        );
    }
    
    /**
     * 创建市场关闭异常
     * 
     * @param symbol 交易对
     * @return TradeException
     */
    public static TradeException marketClosed(String symbol) {
        return new TradeException(
                "MARKET_CLOSED",
                String.format("Market closed for symbol: %s", symbol),
                symbol
        );
    }
    
    /**
     * 创建风控拒绝异常
     *
     * @param reason 拒绝原因
     * @return TradeException
     */
    public static TradeException riskControlRejected(String reason) {
        return new TradeException(
                "RISK_CONTROL_REJECTED",
                String.format("Order rejected by risk control: %s", reason),
                reason
        );
    }

    /**
     * 静态工厂方法
     *
     * @param message 错误消息
     * @return TradeException
     */
    public static TradeException of(String message) {
        return new TradeException(message);
    }
}
