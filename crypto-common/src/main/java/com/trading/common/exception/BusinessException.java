package com.trading.common.exception;

/**
 * 业务异常类
 * 用于处理业务逻辑相关的异常情况
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class BusinessException extends BaseException {
    
    private static final String EXCEPTION_TYPE = "BUSINESS";

    /**
     * 构造函数（兼容测试）
     *
     * @param errorMessage 错误消息
     */
    public BusinessException(String errorMessage) {
        super("BUSINESS_ERROR", errorMessage);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     */
    public BusinessException(String errorCode, String errorMessage) {
        super(errorCode, errorMessage);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param cause 原因异常
     */
    public BusinessException(String errorCode, String errorMessage, Throwable cause) {
        super(errorCode, errorMessage, cause);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     */
    public BusinessException(String errorCode, String errorMessage, Object errorDetails) {
        super(errorCode, errorMessage, errorDetails);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param errorDetails 错误详情
     * @param cause 原因异常
     */
    public BusinessException(String errorCode, String errorMessage, Object errorDetails, Throwable cause) {
        super(errorCode, errorMessage, errorDetails, cause);
    }
    
    @Override
    public String getExceptionType() {
        return EXCEPTION_TYPE;
    }
    
    @Override
    public SeverityLevel getSeverityLevel() {
        return SeverityLevel.WARN;
    }
    
    /**
     * 创建参数验证异常
     * 
     * @param paramName 参数名称
     * @param paramValue 参数值
     * @return BusinessException
     */
    public static BusinessException invalidParameter(String paramName, Object paramValue) {
        return new BusinessException(
                "INVALID_PARAMETER",
                String.format("Invalid parameter: %s = %s", paramName, paramValue),
                paramValue
        );
    }
    
    /**
     * 创建资源不存在异常
     * 
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @return BusinessException
     */
    public static BusinessException resourceNotFound(String resourceType, Object resourceId) {
        return new BusinessException(
                "RESOURCE_NOT_FOUND",
                String.format("%s not found: %s", resourceType, resourceId),
                resourceId
        );
    }
    
    /**
     * 创建资源已存在异常
     * 
     * @param resourceType 资源类型
     * @param resourceId 资源ID
     * @return BusinessException
     */
    public static BusinessException resourceAlreadyExists(String resourceType, Object resourceId) {
        return new BusinessException(
                "RESOURCE_ALREADY_EXISTS",
                String.format("%s already exists: %s", resourceType, resourceId),
                resourceId
        );
    }
    
    /**
     * 创建操作不允许异常
     * 
     * @param operation 操作名称
     * @param reason 原因
     * @return BusinessException
     */
    public static BusinessException operationNotAllowed(String operation, String reason) {
        return new BusinessException(
                "OPERATION_NOT_ALLOWED",
                String.format("Operation '%s' not allowed: %s", operation, reason),
                reason
        );
    }
    
    /**
     * 创建状态不正确异常
     *
     * @param currentState 当前状态
     * @param expectedState 期望状态
     * @return BusinessException
     */
    public static BusinessException invalidState(String currentState, String expectedState) {
        return new BusinessException(
                "INVALID_STATE",
                String.format("Invalid state: current=%s, expected=%s", currentState, expectedState),
                String.format("current=%s,expected=%s", currentState, expectedState)
        );
    }

    /**
     * 静态工厂方法
     *
     * @param message 错误消息
     * @return BusinessException
     */
    public static BusinessException of(String message) {
        return new BusinessException(message);
    }
}
