package com.trading.common.exception;

/**
 * API异常类
 * 用于处理API调用相关的异常情况，如HTTP错误、认证失败等
 * 特别用于封装币安API的异常
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public class ApiException extends BaseException {
    
    private static final String EXCEPTION_TYPE = "API";

    /**
     * HTTP状态码
     */
    private final Integer httpStatusCode;

    /**
     * API错误码
     */
    private final String apiErrorCode;

    /**
     * 构造函数（兼容测试）
     *
     * @param errorMessage 错误消息
     */
    public ApiException(String errorMessage) {
        this("API_ERROR", errorMessage, null, null, null);
    }

    /**
     * 构造函数
     *
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     */
    public ApiException(String errorCode, String errorMessage) {
        this(errorCode, errorMessage, null, null, null);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param httpStatusCode HTTP状态码
     */
    public ApiException(String errorCode, String errorMessage, Integer httpStatusCode) {
        this(errorCode, errorMessage, httpStatusCode, null, null);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param httpStatusCode HTTP状态码
     * @param apiErrorCode API错误码
     */
    public ApiException(String errorCode, String errorMessage, Integer httpStatusCode, String apiErrorCode) {
        this(errorCode, errorMessage, httpStatusCode, apiErrorCode, null);
    }
    
    /**
     * 构造函数
     * 
     * @param errorCode 错误代码
     * @param errorMessage 错误消息
     * @param httpStatusCode HTTP状态码
     * @param apiErrorCode API错误码
     * @param cause 原因异常
     */
    public ApiException(String errorCode, String errorMessage, Integer httpStatusCode, String apiErrorCode, Throwable cause) {
        super(errorCode, errorMessage, 
              String.format("httpStatus=%s,apiErrorCode=%s", httpStatusCode, apiErrorCode), 
              cause);
        this.httpStatusCode = httpStatusCode;
        this.apiErrorCode = apiErrorCode;
    }
    
    public Integer getHttpStatusCode() {
        return httpStatusCode;
    }
    
    public String getApiErrorCode() {
        return apiErrorCode;
    }
    
    @Override
    public String getExceptionType() {
        return EXCEPTION_TYPE;
    }
    
    @Override
    public SeverityLevel getSeverityLevel() {
        if (httpStatusCode != null) {
            if (httpStatusCode >= 500) {
                return SeverityLevel.ERROR;
            } else if (httpStatusCode >= 400) {
                return SeverityLevel.WARN;
            }
        }
        return SeverityLevel.ERROR;
    }
    
    @Override
    public boolean isRetryable() {
        if (httpStatusCode != null) {
            // 5xx错误通常是可重试的
            // 429 (Too Many Requests) 也是可重试的
            return httpStatusCode >= 500 || httpStatusCode == 429;
        }
        return false;
    }
    
    /**
     * 创建认证失败异常
     * 
     * @param reason 失败原因
     * @return ApiException
     */
    public static ApiException authenticationFailed(String reason) {
        return new ApiException(
                "AUTHENTICATION_FAILED",
                String.format("Authentication failed: %s", reason),
                401,
                null
        );
    }
    
    /**
     * 创建权限不足异常
     * 
     * @param resource 资源
     * @return ApiException
     */
    public static ApiException accessDenied(String resource) {
        return new ApiException(
                "ACCESS_DENIED",
                String.format("Access denied to resource: %s", resource),
                403,
                null
        );
    }
    
    /**
     * 创建请求频率限制异常
     * 
     * @param retryAfter 重试间隔（秒）
     * @return ApiException
     */
    public static ApiException rateLimitExceeded(Integer retryAfter) {
        return new ApiException(
                "RATE_LIMIT_EXCEEDED",
                String.format("Rate limit exceeded, retry after %d seconds", retryAfter),
                429,
                null
        );
    }
    
    /**
     * 创建服务器错误异常
     * 
     * @param httpStatusCode HTTP状态码
     * @param serverMessage 服务器错误消息
     * @return ApiException
     */
    public static ApiException serverError(Integer httpStatusCode, String serverMessage) {
        return new ApiException(
                "SERVER_ERROR",
                String.format("Server error: %s", serverMessage),
                httpStatusCode,
                null
        );
    }
    
    /**
     * 创建请求参数错误异常
     * 
     * @param parameterName 参数名称
     * @param parameterValue 参数值
     * @return ApiException
     */
    public static ApiException invalidParameter(String parameterName, Object parameterValue) {
        return new ApiException(
                "INVALID_PARAMETER",
                String.format("Invalid parameter: %s = %s", parameterName, parameterValue),
                400,
                null
        );
    }
    
    /**
     * 从币安客户端异常创建API异常
     * 
     * @param httpStatusCode HTTP状态码
     * @param apiErrorCode API错误码
     * @param errorMessage 错误消息
     * @return ApiException
     */
    public static ApiException fromBinanceClientException(Integer httpStatusCode, String apiErrorCode, String errorMessage) {
        return new ApiException(
                "BINANCE_CLIENT_ERROR",
                String.format("Binance API error: %s", errorMessage),
                httpStatusCode,
                apiErrorCode
        );
    }
    
    /**
     * 从币安服务器异常创建API异常
     * 
     * @param httpStatusCode HTTP状态码
     * @param errorMessage 错误消息
     * @return ApiException
     */
    public static ApiException fromBinanceServerException(Integer httpStatusCode, String errorMessage) {
        return new ApiException(
                "BINANCE_SERVER_ERROR",
                String.format("Binance server error: %s", errorMessage),
                httpStatusCode,
                null
        );
    }
    
    /**
     * 从币安连接器异常创建API异常
     *
     * @param errorMessage 错误消息
     * @param cause 原因异常
     * @return ApiException
     */
    public static ApiException fromBinanceConnectorException(String errorMessage, Throwable cause) {
        return new ApiException(
                "BINANCE_CONNECTOR_ERROR",
                String.format("Binance connector error: %s", errorMessage),
                null,
                null,
                cause
        );
    }

    /**
     * 静态工厂方法
     *
     * @param message 错误消息
     * @return ApiException
     */
    public static ApiException of(String message) {
        return new ApiException(message);
    }
}
