package com.trading.common.config;

import com.trading.common.logging.LogSampler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import jakarta.annotation.PostConstruct;

/**
 * 日志配置类
 * 配置日志采样和清理策略
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Slf4j
@Configuration
@EnableScheduling
public class LoggingConfiguration {
    
    @PostConstruct
    public void initializeLogging() {
        log.info("初始化日志配置...");
        
        // 设置日志格式建议
        log.info("日志配置建议:");
        log.info("  - 使用参数化日志: log.info(\"User {} logged in at {}\", username, timestamp)");
        log.info("  - 避免字符串拼接: 不要使用 log.info(\"User \" + username + \" logged in\")");
        log.info("  - 使用LogSampler防止高频日志刷屏");
        log.info("  - 循环中的日志使用LogSampler.loopLog()");
        
        log.info("日志配置初始化完成");
    }
    
    /**
     * 定期清理日志采样器状态
     * 每小时执行一次
     */
    @Scheduled(fixedRate = 3600000) // 1小时
    public void cleanupLogSampler() {
        try {
            LogSampler.cleanup();
            log.debug("日志采样器清理任务完成");
        } catch (Exception e) {
            log.error("日志采样器清理任务失败", e);
        }
    }
    
    /**
     * 定期输出日志采样统计信息
     * 每6小时执行一次
     */
    @Scheduled(fixedRate = 21600000) // 6小时
    public void logSamplerStatistics() {
        try {
            String stats = LogSampler.getStatistics();
            log.info("日志采样统计信息:\n{}", stats);
        } catch (Exception e) {
            log.error("获取日志采样统计信息失败", e);
        }
    }
}
