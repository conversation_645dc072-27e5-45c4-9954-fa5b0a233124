package com.trading.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trading.common.cache.MultiLevelCacheManager;
import com.trading.common.cache.MultiLevelCacheManagerImpl;
import com.trading.common.monitoring.UnifiedMetricsService;
import com.trading.common.retry.UnifiedRetryService;
import org.springframework.data.redis.core.RedisTemplate;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
// import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import org.mybatis.spring.annotation.MapperScan;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * MyBatis-Plus 核心配置类。
 * <p>
 * 此配置类负责整合 MyBatis-Plus 框架到 Spring Boot 应用中。主要职责包括：
 * <ul>
 *     <li>启用声明式事务管理 ({@code @EnableTransactionManagement})。</li>
 *     <li>扫描指定的 Mapper 接口路径 ({@code @MapperScan})，将其注册为 Spring Bean。</li>
 *     <li>配置 MyBatis-Plus 的核心功能插件，例如分页插件。</li>
 *     <li>集成项目自定义的组件，如多级缓存管理器 ({@link MultiLevelCacheManager})。</li>
 * </ul>
 * 当前配置已简化，以最大程度利用 Spring Boot 的自动配置能力。
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Configuration
@EnableTransactionManagement
public class MyBatisPlusConfig {

    private static final Logger log = LoggerFactory.getLogger(MyBatisPlusConfig.class);

    /**
     * 配置并提供 MyBatis-Plus 的核心拦截器插件。
     * <p>
     * MybatisPlusInterceptor 是一个容器，可以注册多个内部拦截器（InnerInterceptor），
     * 如分页、乐观锁、防全表更新等。
     * 当前版本中，分页插件 (PaginationInnerInterceptor) 已被注释掉，
     * 依赖于 Spring Boot 的自动配置或其他地方的自定义配置。
     * 未来可以根据需要在这里集中添加和管理所有拦截器。
     * </p>
     *
     * @return 配置好的 {@link MybatisPlusInterceptor} 实例。
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor() {
        log.info("正在配置MyBatis-Plus拦截器...");

        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();

        // 分页插件 - 暂时注释掉，使用Spring Boot自动配置
        // PaginationInnerInterceptor paginationInterceptor = new PaginationInnerInterceptor(DbType.MYSQL);
        // paginationInterceptor.setMaxLimit(1000L); // 最大分页限制
        // paginationInterceptor.setOverflow(false); // 溢出总页数后是否进行处理
        // interceptor.addInnerInterceptor(paginationInterceptor);

        log.info("MyBatis-Plus拦截器配置完成");
        return interceptor;
    }
}
