package com.trading.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * Kafka配置类
 * 管理Kafka消息队列的配置参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Component
@Validated
@ConfigurationProperties(prefix = "kafka")
public class KafkaConfig {
    
    /**
     * Kafka服务器地址
     */
    @NotBlank(message = "Kafka bootstrap servers cannot be blank")
    private String bootstrapServers = "localhost:29092";
    
    /**
     * 生产者配置
     */
    @NotNull(message = "Producer config cannot be null")
    private Producer producer = new Producer();
    
    /**
     * 消费者配置
     */
    @NotNull(message = "Consumer config cannot be null")
    private Consumer consumer = new Consumer();
    
    /**
     * 主题配置
     */
    @NotNull(message = "Topics config cannot be null")
    private Topics topics = new Topics();
    
    /**
     * 安全配置
     */
    @NotNull(message = "Security config cannot be null")
    private Security security = new Security();
    
    /**
     * 生产者配置内部类
     */
    @Data
    public static class Producer {
        
        /**
         * 确认模式
         */
        @NotBlank(message = "Producer acks cannot be blank")
        private String acks = "all";
        
        /**
         * 重试次数
         */
        @Min(value = 0, message = "Producer retries cannot be negative")
        @Max(value = 10, message = "Producer retries cannot exceed 10")
        private int retries = 3;
        
        /**
         * 批处理大小
         */
        @Min(value = 1024, message = "Producer batch size must be at least 1024")
        @Max(value = 1048576, message = "Producer batch size cannot exceed 1048576")
        private int batchSize = 16384;
        
        /**
         * 延迟时间（毫秒）
         */
        @Min(value = 0, message = "Producer linger ms cannot be negative")
        @Max(value = 1000, message = "Producer linger ms cannot exceed 1000")
        private int lingerMs = 5;
        
        /**
         * 缓冲区内存大小
         */
        @Min(value = 1048576, message = "Producer buffer memory must be at least 1048576")
        @Max(value = 134217728, message = "Producer buffer memory cannot exceed 134217728")
        private long bufferMemory = 33554432L;
        
        /**
         * 键序列化器
         */
        @NotBlank(message = "Producer key serializer cannot be blank")
        private String keySerializer = "org.apache.kafka.common.serialization.StringSerializer";
        
        /**
         * 值序列化器
         */
        @NotBlank(message = "Producer value serializer cannot be blank")
        private String valueSerializer = "org.apache.kafka.common.serialization.StringSerializer";
        
        /**
         * 压缩类型
         */
        @NotBlank(message = "Producer compression type cannot be blank")
        private String compressionType = "snappy";
        
        /**
         * 幂等性
         */
        private boolean enableIdempotence = true;
        
        /**
         * 最大请求大小
         */
        @Min(value = 1024, message = "Producer max request size must be at least 1024")
        @Max(value = 10485760, message = "Producer max request size cannot exceed 10485760")
        private int maxRequestSize = 1048576;
        
        /**
         * 请求超时时间（毫秒）
         */
        @Min(value = 1000, message = "Producer request timeout must be at least 1000ms")
        @Max(value = 300000, message = "Producer request timeout cannot exceed 300000ms")
        private int requestTimeoutMs = 30000;
        
        /**
         * 传输超时时间（毫秒）
         */
        @Min(value = 1000, message = "Producer delivery timeout must be at least 1000ms")
        @Max(value = 600000, message = "Producer delivery timeout cannot exceed 600000ms")
        private int deliveryTimeoutMs = 120000;
    }
    
    /**
     * 消费者配置内部类
     */
    @Data
    public static class Consumer {
        
        /**
         * 消费者组ID
         */
        @NotBlank(message = "Consumer group id cannot be blank")
        private String groupId = "crypto-trading-group";
        
        /**
         * 自动提交偏移量
         */
        private boolean enableAutoCommit = false;
        
        /**
         * 自动提交间隔（毫秒）
         */
        @Min(value = 100, message = "Consumer auto commit interval must be at least 100ms")
        @Max(value = 60000, message = "Consumer auto commit interval cannot exceed 60000ms")
        private int autoCommitIntervalMs = 5000;
        
        /**
         * 会话超时时间（毫秒）
         */
        @Min(value = 6000, message = "Consumer session timeout must be at least 6000ms")
        @Max(value = 300000, message = "Consumer session timeout cannot exceed 300000ms")
        private int sessionTimeoutMs = 30000;
        
        /**
         * 心跳间隔（毫秒）
         */
        @Min(value = 1000, message = "Consumer heartbeat interval must be at least 1000ms")
        @Max(value = 10000, message = "Consumer heartbeat interval cannot exceed 10000ms")
        private int heartbeatIntervalMs = 3000;
        
        /**
         * 最大轮询记录数
         */
        @Min(value = 1, message = "Consumer max poll records must be at least 1")
        @Max(value = 10000, message = "Consumer max poll records cannot exceed 10000")
        private int maxPollRecords = 500;
        
        /**
         * 最大轮询间隔（毫秒）
         */
        @Min(value = 1000, message = "Consumer max poll interval must be at least 1000ms")
        @Max(value = 600000, message = "Consumer max poll interval cannot exceed 600000ms")
        private int maxPollIntervalMs = 300000;
        
        /**
         * 获取数据最大等待时间（毫秒）
         */
        @Min(value = 100, message = "Consumer fetch max wait must be at least 100ms")
        @Max(value = 10000, message = "Consumer fetch max wait cannot exceed 10000ms")
        private int fetchMaxWaitMs = 500;
        
        /**
         * 最小获取字节数
         */
        @Min(value = 1, message = "Consumer fetch min bytes must be at least 1")
        @Max(value = 1048576, message = "Consumer fetch min bytes cannot exceed 1048576")
        private int fetchMinBytes = 1;
        
        /**
         * 最大获取字节数
         */
        @Min(value = 1024, message = "Consumer fetch max bytes must be at least 1024")
        @Max(value = 52428800, message = "Consumer fetch max bytes cannot exceed 52428800")
        private int fetchMaxBytes = 52428800;
        
        /**
         * 键反序列化器
         */
        @NotBlank(message = "Consumer key deserializer cannot be blank")
        private String keyDeserializer = "org.apache.kafka.common.serialization.StringDeserializer";
        
        /**
         * 值反序列化器
         */
        @NotBlank(message = "Consumer value deserializer cannot be blank")
        private String valueDeserializer = "org.apache.kafka.common.serialization.StringDeserializer";
        
        /**
         * 自动偏移量重置策略
         */
        @NotBlank(message = "Consumer auto offset reset cannot be blank")
        private String autoOffsetReset = "earliest";
        
        /**
         * 隔离级别
         */
        @NotBlank(message = "Consumer isolation level cannot be blank")
        private String isolationLevel = "read_committed";
    }

    /**
     * 主题配置内部类
     */
    @Data
    public static class Topics {

        /**
         * 市场数据主题
         */
        @NotBlank(message = "Market data topic cannot be blank")
        private String marketData = "market-data";

        /**
         * 交易信号主题
         */
        @NotBlank(message = "Trading signals topic cannot be blank")
        private String tradingSignals = "trading-signals";

        /**
         * 订单事件主题
         */
        @NotBlank(message = "Order events topic cannot be blank")
        private String orderEvents = "order-events";

        /**
         * 风控事件主题
         */
        @NotBlank(message = "Risk events topic cannot be blank")
        private String riskEvents = "risk-events";

        /**
         * 系统事件主题
         */
        @NotBlank(message = "System events topic cannot be blank")
        private String systemEvents = "system-events";

        /**
         * 策略事件主题
         */
        @NotBlank(message = "Strategy events topic cannot be blank")
        private String strategyEvents = "strategy-events";

        /**
         * 默认分区数
         */
        @Min(value = 1, message = "Default partitions must be at least 1")
        @Max(value = 100, message = "Default partitions cannot exceed 100")
        private int defaultPartitions = 3;

        /**
         * 默认副本数
         */
        @Min(value = 1, message = "Default replication factor must be at least 1")
        @Max(value = 10, message = "Default replication factor cannot exceed 10")
        private short defaultReplicationFactor = 1;
    }

    /**
     * 安全配置内部类
     */
    @Data
    public static class Security {

        /**
         * 安全协议
         */
        @NotBlank(message = "Security protocol cannot be blank")
        private String protocol = "PLAINTEXT";

        /**
         * SASL机制
         */
        private String saslMechanism = "PLAIN";

        /**
         * SASL JAAS配置
         */
        private String saslJaasConfig;

        /**
         * SSL信任库位置
         */
        private String sslTruststoreLocation;

        /**
         * SSL信任库密码
         */
        private String sslTruststorePassword;

        /**
         * SSL密钥库位置
         */
        private String sslKeystoreLocation;

        /**
         * SSL密钥库密码
         */
        private String sslKeystorePassword;

        /**
         * SSL密钥密码
         */
        private String sslKeyPassword;

        /**
         * SSL端点识别算法
         */
        private String sslEndpointIdentificationAlgorithm = "https";
    }

    /**
     * 检查是否启用安全认证
     *
     * @return true表示启用安全认证，false表示未启用
     */
    public boolean isSecurityEnabled() {
        return !"PLAINTEXT".equals(security.getProtocol());
    }

    /**
     * 检查是否启用SSL
     *
     * @return true表示启用SSL，false表示未启用
     */
    public boolean isSslEnabled() {
        return security.getProtocol().contains("SSL");
    }

    /**
     * 检查是否启用SASL
     *
     * @return true表示启用SASL，false表示未启用
     */
    public boolean isSaslEnabled() {
        return security.getProtocol().contains("SASL");
    }
}
