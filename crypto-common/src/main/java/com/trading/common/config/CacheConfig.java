package com.trading.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

/**
 * 缓存配置类
 * 配置多级缓存架构和Spring Cache集成
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Configuration
@EnableCaching
public class CacheConfig {

    private static final Logger log = LoggerFactory.getLogger(CacheConfig.class);

    /**
     * 配置Spring Cache Manager (使用Caffeine)
     */
    @Bean
    @ConditionalOnMissingBean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();

        // 配置Caffeine缓存
        cacheManager.setCaffeine(Caffeine.newBuilder()
                .maximumSize(10000)  // 最大缓存条目数
                .expireAfterWrite(10, TimeUnit.MINUTES)  // 写入后10分钟过期
                .expireAfterAccess(5, TimeUnit.MINUTES)  // 访问后5分钟过期
                .recordStats());  // 启用统计

        // 预定义缓存名称
        cacheManager.setCacheNames(java.util.Arrays.asList("market-data", "kline-data", "depth-data", "trade-data", "latest-price"));

        log.info("Spring Cache Manager配置完成: 使用Caffeine作为缓存提供者");
        return cacheManager;
    }

    /**
     * 配置ObjectMapper (如果不存在)
     */
    @Bean
    @ConditionalOnMissingBean
    public ObjectMapper objectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 配置时间格式
        mapper.findAndRegisterModules();

        log.info("ObjectMapper配置完成");
        return mapper;
    }
}
