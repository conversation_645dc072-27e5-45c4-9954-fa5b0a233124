package com.trading.common.config;

import com.influxdb.client.InfluxDBClient;
import com.influxdb.client.InfluxDBClientFactory;
import com.influxdb.client.InfluxDBClientOptions;
import okhttp3.OkHttpClient;
import com.influxdb.client.WriteApi;
import com.influxdb.client.WriteOptions;
import com.influxdb.client.domain.WritePrecision;
import com.influxdb.client.write.Point;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;

import jakarta.annotation.PreDestroy;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;


/**
 * InfluxDB配置类
 * 统一的InfluxDB配置，用于所有模块的时序数据存储
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Configuration
@AutoConfigureAfter(MarketDataConfig.class)
public class InfluxDBConfig {

    private static final Logger log = LoggerFactory.getLogger(InfluxDBConfig.class);

    @Autowired
    private MarketDataConfig marketDataConfig;

    private InfluxDBClient influxDBClient;

    /**
     * 创建并配置核心的 InfluxDB 客户端 Bean。
     * <p>
     * 此 Bean 仅在配置文件中 `trading.market-data.storage.influxdb.enabled` 为 `true` 时被激活。
     * 它负责建立与 InfluxDB 的连接，并使用从 {@link MarketDataConfig} 获取的 URL、Token、组织和桶信息。
     * 为了提高启动性能和鲁棒性，连接健康检查是异步执行的。
     * </p>
     *
     * @return 配置好的 {@link InfluxDBClient} 实例。
     * @throws IllegalStateException 如果 InfluxDB Token 未配置或无效。
     * @throws RuntimeException 如果客户端创建过程中发生无法恢复的错误。
     */
    @Bean(name = "influxDBClient")
    @Primary
    @ConditionalOnProperty(prefix = "trading.market-data.storage.influxdb", name = "enabled", havingValue = "true")
    @ConditionalOnMissingBean(InfluxDBClient.class)
    public InfluxDBClient influxDBClient() {
        log.info("=== InfluxDB enabled, creating InfluxDBClient Bean ===");
        
        String url = marketDataConfig.getStorage().getInfluxdb().getUrl();
        String token = marketDataConfig.getStorage().getInfluxdb().getToken();
        String org = marketDataConfig.getStorage().getInfluxdb().getOrg();
        String bucket = marketDataConfig.getStorage().getInfluxdb().getBucket();

        log.info("初始化InfluxDB客户端: url={}, org={}, bucket={}", url, org, bucket);

        try {
            if (token == null || token.equals("your-token-here") || token.trim().isEmpty()) {
                log.error("InfluxDB token is missing or invalid. Please configure market-data.storage.influxdb.token in your application properties.");
                throw new IllegalStateException("InfluxDB token is not configured.");
            }

            // 创建一个隔离的OkHttpClient，避免受Spring Boot自动配置的影响
            OkHttpClient.Builder okHttpClientBuilder = new OkHttpClient.Builder();

            InfluxDBClientOptions options = InfluxDBClientOptions.builder()
                    .url(url)
                    .authenticateToken(token.toCharArray())
                    .org(org)
                    .bucket(bucket)
                    .okHttpClient(okHttpClientBuilder)
                    .build();

            influxDBClient = InfluxDBClientFactory.create(options);

            // 异步测试连接，避免阻塞启动
            CompletableFuture.runAsync(() -> {
                try {
                    if (influxDBClient.ready() != null) {
                        log.info("InfluxDB客户端创建成功并连接正常");
                    } else {
                        log.warn("InfluxDB客户端创建成功但连接可能有问题");
                    }
                } catch (Exception e) {
                    log.warn("InfluxDB连接检查失败，但客户端已创建: {}", e.getMessage());
                }
            });

            return influxDBClient;

        } catch (Exception e) {
            log.error("创建InfluxDB客户端失败: {}", e.getMessage());
            throw new RuntimeException("InfluxDB连接失败", e);
        }
    }



    /**
     * 内部工具方法，用于判断一个 InfluxDBClient 实例是否为模拟（Mock）客户端。
     * <p>
     * 在测试环境中，系统可能会注入一个 Mock 客户端以实现与数据库的解耦。
     * 此方法通过检查客户端返回的版本信息是否为 "mock-version" 来进行判断。
     * 如果调用 {@code client.version()} 时发生异常，也将其视为 Mock 客户端以实现安全容错。
     * </p>
     *
     * @param client 待检查的 {@link InfluxDBClient} 实例。
     * @return 如果是 Mock 客户端则返回 {@code true}，否则返回 {@code false}。
     */
    private boolean isMockClient(InfluxDBClient client) {
        try {
            // 通过版本信息判断是否为Mock客户端
            String version = client.version();
            return "mock-version".equals(version);
        } catch (Exception e) {
            // 如果调用version()方法出错，也认为是Mock客户端
            return true;
        }
    }

    /**
     * 创建用于异步批量写入数据的 {@link WriteApi} Bean。
     * <p>
     * 此 Bean 依赖于 {@link InfluxDBClient} 的存在，并且仅在客户端不是 Mock 实例时创建。
     * {@link WriteOptions} 经过精心调优以适应高吞吐量和高并发的写入场景：
     * <ul>
     *     <li>{@code batchSize}: 500 - 较小的批量大小以降低单次写入延迟。</li>
     *     <li>{@code flushInterval}: 2000ms - 2秒的刷新间隔，确保数据能较快地持久化。</li>
     *     <li>{@code bufferLimit}: 10000 - 较大的缓冲区以应对临时的写入峰值和背压。</li>
     *     <li>{@code retryInterval}: 2000ms - 发生写入失败后的重试间隔。</li>
     * </ul>
     * 如果标准创建过程失败，系统会尝试以容错模式创建 Bean。
     * </p>
     *
     * @param influxDBClient 已配置的 InfluxDB 客户端。
     * @return 配置好的 {@link WriteApi} 实例，如果无法创建则返回 {@code null}。
     */
    @Bean
    @ConditionalOnBean(InfluxDBClient.class)
    public WriteApi writeApi(InfluxDBClient influxDBClient) {
        if (influxDBClient == null) {
            log.warn("InfluxDBClient为null，跳过WriteApi创建");
            return null;
        }

        // 如果是Mock客户端，返回null
        if (isMockClient(influxDBClient)) {
            log.info("检测到Mock InfluxDBClient，跳过WriteApi创建");
            return null;
        }

        try {
            // 跳过连接检查，直接创建WriteApi以避免启动阻塞
            log.info("跳过InfluxDB连接检查，直接创建WriteApi以避免启动阻塞");

            WriteOptions writeOptions = WriteOptions.builder()
                    .batchSize(500)          // 减少批量大小，提高写入频率
                    .flushInterval(2000)     // 减少刷新间隔，更频繁地写入
                    .bufferLimit(10000)      // 大幅增加缓冲区限制，解决背压问题
                    .jitterInterval(500)     // 减少抖动间隔
                    .retryInterval(2000)     // 增加重试间隔
                    .maxRetries(5)           // 增加最大重试次数
                    .maxRetryDelay(60000)    // 增加最大重试延迟
                    .exponentialBase(2)
                    .build();

            WriteApi writeApi = influxDBClient.makeWriteApi(writeOptions);

            log.info("InfluxDB WriteAPI初始化完成: batchSize={}, flushInterval={}ms, bufferLimit={}", 500, 2000, 10000);

            return writeApi;
        } catch (Exception e) {
            log.warn("创建WriteApi遇到问题，尝试容错创建: {}", e.getMessage());
            try {
                // 容错模式：即使连接有问题也尝试创建WriteApi
                WriteOptions writeOptions = WriteOptions.builder()
                        .batchSize(500)          // 减少批量大小
                        .flushInterval(2000)     // 减少刷新间隔
                        .bufferLimit(10000)      // 增加缓冲区限制
                        .jitterInterval(500)     // 减少抖动间隔
                        .retryInterval(2000)     // 增加重试间隔
                        .maxRetries(5)           // 增加最大重试次数
                        .maxRetryDelay(60000)    // 增加最大重试延迟
                        .exponentialBase(2)
                        .build();

                WriteApi writeApi = influxDBClient.makeWriteApi(writeOptions);
                log.info("容错模式WriteApi创建成功");
                return writeApi;
            } catch (Exception fallbackException) {
                log.error("容错模式WriteApi创建也失败，系统将无法写入InfluxDB: {}", fallbackException.getMessage());
                return null;
            }
        }
    }



    /**
     * 创建用于执行 Flux 查询的 {@link com.influxdb.client.QueryApi} Bean。
     * <p>
     * 此 API 是从 InfluxDB 读取数据的核心接口。
     * 仅在 {@link InfluxDBClient} 存在且不为 Mock 实例时创建。
     * </p>
     *
     * @param influxDBClient 已配置的 InfluxDB 客户端。
     * @return {@link com.influxdb.client.QueryApi} 实例，如果条件不满足则返回 {@code null}。
     */
    @Bean
    @ConditionalOnBean(InfluxDBClient.class)
    public com.influxdb.client.QueryApi queryApi(InfluxDBClient influxDBClient) {
        if (influxDBClient == null || isMockClient(influxDBClient)) {
            log.info("跳过QueryApi创建（无InfluxDB模式）");
            return null;
        }
        return influxDBClient.getQueryApi();
    }

    /**
     * 创建用于删除数据的 {@link com.influxdb.client.DeleteApi} Bean。
     * <p>
     * 此 API 允许根据时间范围和谓词从 InfluxDB 中删除数据。
     * 仅在 {@link InfluxDBClient} 存在且不为 Mock 实例时创建。
     * </p>
     *
     * @param influxDBClient 已配置的 InfluxDB 客户端。
     * @return {@link com.influxdb.client.DeleteApi} 实例，如果条件不满足则返回 {@code null}。
     */
    @Bean
    @ConditionalOnBean(InfluxDBClient.class)
    public com.influxdb.client.DeleteApi deleteApi(InfluxDBClient influxDBClient) {
        if (influxDBClient == null || isMockClient(influxDBClient)) {
            log.info("跳过DeleteApi创建（无InfluxDB模式）");
            return null;
        }
        return influxDBClient.getDeleteApi();
    }

    /**
     * 创建用于管理桶 (Bucket) 的 {@link com.influxdb.client.BucketsApi} Bean。
     * <p>
     * 桶是 InfluxDB 中存储时序数据的基本命名空间。此 API 可用于创建、查询和删除桶。
     * 此 Bean 直接从主客户端获取，无需额外条件判断。
     * </p>
     *
     * @param influxDBClient 已配置的 InfluxDB 客户端。
     * @return {@link com.influxdb.client.BucketsApi} 实例。
     */
    @Bean
    @ConditionalOnBean(InfluxDBClient.class)
    public com.influxdb.client.BucketsApi bucketsApi(InfluxDBClient influxDBClient) {
        return influxDBClient.getBucketsApi();
    }

    /**
     * 创建用于管理组织 (Organization) 的 {@link com.influxdb.client.OrganizationsApi} Bean。
     * <p>
     * 组织是 InfluxDB 中的顶层资源容器。此 API 允许对组织进行管理。
     * 此 Bean 直接从主客户端获取。
     * </p>
     *
     * @param influxDBClient 已配置的 InfluxDB 客户端。
     * @return {@link com.influxdb.client.OrganizationsApi} 实例。
     */
    @Bean
    @ConditionalOnBean(InfluxDBClient.class)
    public com.influxdb.client.OrganizationsApi organizationsApi(InfluxDBClient influxDBClient) {
        return influxDBClient.getOrganizationsApi();
    }
    
    /**
     * 定义全局的 InfluxDB 写入精度 Bean。
     * <p>
     * 为确保系统内时间戳的一致性，所有模块向 InfluxDB 写入数据时都应使用此精度。
     * 当前配置为毫秒 (MS)，与大多数交易所API的时间戳格式保持一致。
     * </p>
     *
     * @return {@link WritePrecision#MS} 写入精度。
     */
    @Bean
    public WritePrecision writePrecision() {
        return WritePrecision.MS; // 毫秒精度
    }
    
    /**
     * 对 InfluxDB 连接执行一次快速、非阻塞的健康检查。
     * <p>
     * 此方法对于集成到Spring Actuator的健康端点非常有用。
     * 它通过异步执行 {@code influxDBClient.ready()} 并设置一个较短的超时（1秒）来实现非阻塞。
     * 这可以防止因 InfluxDB 网络问题而导致健康检查接口长时间挂起。
     * </p>
     *
     * @return 如果连接在1秒内被确认为健康，则返回 {@code true}，否则返回 {@code false}。
     */
    public boolean isHealthy() {
        try {
            if (influxDBClient == null) {
                return false;
            }
            // 使用超时机制避免长时间阻塞
            CompletableFuture<Boolean> healthCheck = CompletableFuture.supplyAsync(() -> {
                try {
                    return influxDBClient.ready() != null;
                } catch (Exception e) {
                    return false;
                }
            });

            // 1秒超时
            return healthCheck.get(1, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.debug("InfluxDB健康检查失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取当前 InfluxDB 连接的摘要信息。
     * <p>
     * 此方法用于日志记录和监控，可以快速了解系统当前连接的 InfluxDB 实例。
     * 它从 {@link MarketDataConfig} 安全地检索连接参数。
     * </p>
     *
     * @return 包含 URL、组织和桶信息的格式化字符串。如果信息获取失败，则返回错误提示。
     */
    public String getDatabaseInfo() {
        try {
            String url = marketDataConfig.getStorage().getInfluxdb().getUrl();
            String org = marketDataConfig.getStorage().getInfluxdb().getOrg();
            String bucket = marketDataConfig.getStorage().getInfluxdb().getBucket();
            return String.format("InfluxDB[url=%s, org=%s, bucket=%s]", url, org, bucket);
        } catch (Exception e) {
            return "InfluxDB[连接信息获取失败]";
        }
    }

    /**
     * 在 Spring 容器销毁此 Bean 之前，优雅地关闭 InfluxDB 客户端连接。
     * <p>
     * 使用 {@code @PreDestroy} 注解可以确保在应用程序关闭时，所有网络资源都被正确释放，
     * 避免连接泄露。
     * </p>
     */
    @PreDestroy
    public void cleanup() {
        if (influxDBClient != null) {
            try {
                log.info("关闭InfluxDB连接...");
                influxDBClient.close();
                log.info("InfluxDB连接已关闭");
            } catch (Exception e) {
                log.error("关闭InfluxDB连接失败", e);
            }
        }
    }
}
