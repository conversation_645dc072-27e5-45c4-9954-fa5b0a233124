package com.trading.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 应用配置类
 * 管理应用级别的配置参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Component
@Validated
@ConfigurationProperties(prefix = "app")
public class AppConfig {
    
    /**
     * 应用名称
     */
    @NotBlank(message = "Application name cannot be blank")
    private String name = "crypto-trading-system";
    
    /**
     * 应用版本
     */
    @NotBlank(message = "Application version cannot be blank")
    private String version = "1.0.0";
    
    /**
     * 应用描述
     */
    private String description = "A high-performance cryptocurrency quantitative trading system";
    
    /**
     * 环境配置
     */
    @NotBlank(message = "Environment cannot be blank")
    private String environment = "dev";
    
    /**
     * 时区配置
     */
    @NotBlank(message = "Timezone cannot be blank")
    private String timezone = "UTC";
    
    /**
     * 是否启用调试模式
     */
    private boolean debugEnabled = false;
    
    /**
     * 是否启用虚拟线程
     */
    private boolean virtualThreadsEnabled = true;
    
    /**
     * 性能配置
     */
    @NotNull(message = "Performance config cannot be null")
    private Performance performance = new Performance();
    
    /**
     * 监控配置
     */
    @NotNull(message = "Monitoring config cannot be null")
    private Monitoring monitoring = new Monitoring();
    
    /**
     * 安全配置
     */
    @NotNull(message = "Security config cannot be null")
    private Security security = new Security();
    
    /**
     * 性能配置内部类
     */
    @Data
    public static class Performance {
        
        /**
         * 最大延迟要求（毫秒）
         */
        @Min(value = 1, message = "Max latency must be at least 1ms")
        @Max(value = 10000, message = "Max latency cannot exceed 10000ms")
        private long maxLatencyMs = 100L;
        
        /**
         * 系统可用性要求（百分比）
         */
        @Min(value = 90, message = "Availability must be at least 90%")
        @Max(value = 100, message = "Availability cannot exceed 100%")
        private double availability = 99.9;
        
        /**
         * 核心线程池大小
         */
        @Min(value = 1, message = "Core pool size must be at least 1")
        @Max(value = 200, message = "Core pool size cannot exceed 200")
        private int corePoolSize = 10;
        
        /**
         * 最大线程池大小
         */
        @Min(value = 1, message = "Max pool size must be at least 1")
        @Max(value = 500, message = "Max pool size cannot exceed 500")
        private int maxPoolSize = 50;
        
        /**
         * 队列容量
         */
        @Min(value = 100, message = "Queue capacity must be at least 100")
        @Max(value = 10000, message = "Queue capacity cannot exceed 10000")
        private int queueCapacity = 1000;
        
        /**
         * 线程存活时间（秒）
         */
        @Min(value = 10, message = "Keep alive time must be at least 10 seconds")
        @Max(value = 3600, message = "Keep alive time cannot exceed 3600 seconds")
        private long keepAliveSeconds = 60L;
    }
    
    /**
     * 监控配置内部类
     */
    @Data
    public static class Monitoring {
        
        /**
         * 是否启用监控
         */
        private boolean enabled = true;
        
        /**
         * 指标收集间隔（秒）
         */
        @Min(value = 1, message = "Metrics interval must be at least 1 second")
        @Max(value = 300, message = "Metrics interval cannot exceed 300 seconds")
        private int metricsIntervalSeconds = 30;
        
        /**
         * 健康检查间隔（秒）
         */
        @Min(value = 5, message = "Health check interval must be at least 5 seconds")
        @Max(value = 600, message = "Health check interval cannot exceed 600 seconds")
        private int healthCheckIntervalSeconds = 60;
        
        /**
         * 是否启用性能监控
         */
        private boolean performanceMonitoringEnabled = true;
        
        /**
         * 是否启用链路追踪
         */
        private boolean tracingEnabled = true;
        
        /**
         * 日志级别
         */
        @NotBlank(message = "Log level cannot be blank")
        private String logLevel = "INFO";
    }
    
    /**
     * 安全配置内部类
     */
    @Data
    public static class Security {
        
        /**
         * 是否启用API认证
         */
        private boolean authenticationEnabled = true;
        
        /**
         * 是否启用HTTPS
         */
        private boolean httpsEnabled = false;
        
        /**
         * 令牌过期时间（秒）
         */
        @Min(value = 300, message = "Token expiry must be at least 300 seconds")
        @Max(value = 86400, message = "Token expiry cannot exceed 86400 seconds")
        private long tokenExpirySeconds = 3600L;
        
        /**
         * 最大登录尝试次数
         */
        @Min(value = 1, message = "Max login attempts must be at least 1")
        @Max(value = 10, message = "Max login attempts cannot exceed 10")
        private int maxLoginAttempts = 5;
        
        /**
         * 账户锁定时间（秒）
         */
        @Min(value = 60, message = "Account lockout time must be at least 60 seconds")
        @Max(value = 3600, message = "Account lockout time cannot exceed 3600 seconds")
        private long accountLockoutSeconds = 300L;
        
        /**
         * 是否启用IP白名单
         */
        private boolean ipWhitelistEnabled = false;
        
        /**
         * 允许的IP地址列表
         */
        private String[] allowedIps = {};
    }
    
    /**
     * 检查是否为生产环境
     * 
     * @return true表示生产环境，false表示非生产环境
     */
    public boolean isProductionEnvironment() {
        return "prod".equalsIgnoreCase(environment) || "production".equalsIgnoreCase(environment);
    }
    
    /**
     * 检查是否为开发环境
     * 
     * @return true表示开发环境，false表示非开发环境
     */
    public boolean isDevelopmentEnvironment() {
        return "dev".equalsIgnoreCase(environment) || "development".equalsIgnoreCase(environment);
    }
    
    /**
     * 检查是否为测试环境
     * 
     * @return true表示测试环境，false表示非测试环境
     */
    public boolean isTestEnvironment() {
        return "test".equalsIgnoreCase(environment) || "testing".equalsIgnoreCase(environment);
    }
    
    /**
     * 获取完整的应用标识
     * 
     * @return 应用标识字符串
     */
    public String getApplicationIdentifier() {
        return String.format("%s-%s-%s", name, version, environment);
    }
}
