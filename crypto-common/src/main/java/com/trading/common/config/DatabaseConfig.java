package com.trading.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 数据库配置类
 * 管理MySQL和InfluxDB的配置参数
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Component
@Validated
@ConfigurationProperties(prefix = "database")
public class DatabaseConfig {
    
    /**
     * MySQL配置
     */
    @NotNull(message = "MySQL config cannot be null")
    private MySQL mysql = new MySQL();
    
    /**
     * InfluxDB配置
     */
    @NotNull(message = "InfluxDB config cannot be null")
    private InfluxDB influxdb = new InfluxDB();
    
    /**
     * 连接池配置
     */
    @NotNull(message = "Connection pool config cannot be null")
    private ConnectionPool connectionPool = new ConnectionPool();
    
    /**
     * MySQL配置内部类
     */
    @Data
    public static class MySQL {
        
        /**
         * 数据库主机
         */
        @NotBlank(message = "MySQL host cannot be blank")
        private String host = "localhost";
        
        /**
         * 数据库端口
         */
        @Min(value = 1, message = "MySQL port must be at least 1")
        @Max(value = 65535, message = "MySQL port cannot exceed 65535")
        private int port = 3306;
        
        /**
         * 数据库名称
         */
        @NotBlank(message = "MySQL database name cannot be blank")
        private String database = "crypto_trading";
        
        /**
         * 用户名
         */
        @NotBlank(message = "MySQL username cannot be blank")
        private String username;
        
        /**
         * 密码
         */
        @NotBlank(message = "MySQL password cannot be blank")
        private String password;
        
        /**
         * 字符集
         */
        @NotBlank(message = "MySQL charset cannot be blank")
        private String charset = "utf8mb4";
        
        /**
         * 时区
         */
        @NotBlank(message = "MySQL timezone cannot be blank")
        private String timezone = "UTC";
        
        /**
         * 是否使用SSL
         */
        private boolean useSSL = false;
        
        /**
         * 是否允许公钥检索
         */
        private boolean allowPublicKeyRetrieval = true;
        
        /**
         * 连接超时时间（毫秒）
         */
        @Min(value = 1000, message = "MySQL connect timeout must be at least 1000ms")
        @Max(value = 60000, message = "MySQL connect timeout cannot exceed 60000ms")
        private int connectTimeoutMs = 10000;
        
        /**
         * Socket超时时间（毫秒）
         */
        @Min(value = 1000, message = "MySQL socket timeout must be at least 1000ms")
        @Max(value = 300000, message = "MySQL socket timeout cannot exceed 300000ms")
        private int socketTimeoutMs = 30000;
        
        /**
         * 是否自动重连
         */
        private boolean autoReconnect = true;
        
        /**
         * 最大重连次数
         */
        @Min(value = 1, message = "MySQL max reconnect count must be at least 1")
        @Max(value = 10, message = "MySQL max reconnect count cannot exceed 10")
        private int maxReconnectCount = 3;
        
        /**
         * 是否启用批处理
         */
        private boolean rewriteBatchedStatements = true;
        
        /**
         * 是否缓存预处理语句
         */
        private boolean cachePrepStmts = true;
        
        /**
         * 预处理语句缓存大小
         */
        @Min(value = 25, message = "MySQL prep stmt cache size must be at least 25")
        @Max(value = 500, message = "MySQL prep stmt cache size cannot exceed 500")
        private int prepStmtCacheSize = 250;
        
        /**
         * 预处理语句SQL限制
         */
        @Min(value = 256, message = "MySQL prep stmt cache SQL limit must be at least 256")
        @Max(value = 4096, message = "MySQL prep stmt cache SQL limit cannot exceed 4096")
        private int prepStmtCacheSqlLimit = 2048;
    }
    
    /**
     * InfluxDB配置内部类
     */
    @Data
    public static class InfluxDB {
        
        /**
         * InfluxDB URL
         */
        @NotBlank(message = "InfluxDB URL cannot be blank")
        private String url = "http://localhost:8086";
        
        /**
         * 令牌
         */
        @NotBlank(message = "InfluxDB token cannot be blank")
        private String token = "default-token";
        
        /**
         * 组织
         */
        @NotBlank(message = "InfluxDB organization cannot be blank")
        private String organization = "crypto-trading";
        
        /**
         * 默认存储桶
         */
        @NotBlank(message = "InfluxDB default bucket cannot be blank")
        private String defaultBucket = "trading_data";
        
        /**
         * 连接超时时间（毫秒）
         */
        @Min(value = 1000, message = "InfluxDB connect timeout must be at least 1000ms")
        @Max(value = 60000, message = "InfluxDB connect timeout cannot exceed 60000ms")
        private int connectTimeoutMs = 10000;
        
        /**
         * 读取超时时间（毫秒）
         */
        @Min(value = 1000, message = "InfluxDB read timeout must be at least 1000ms")
        @Max(value = 300000, message = "InfluxDB read timeout cannot exceed 300000ms")
        private int readTimeoutMs = 30000;
        
        /**
         * 写入超时时间（毫秒）
         */
        @Min(value = 1000, message = "InfluxDB write timeout must be at least 1000ms")
        @Max(value = 300000, message = "InfluxDB write timeout cannot exceed 300000ms")
        private int writeTimeoutMs = 30000;
        
        /**
         * 批处理大小
         */
        @Min(value = 100, message = "InfluxDB batch size must be at least 100")
        @Max(value = 10000, message = "InfluxDB batch size cannot exceed 10000")
        private int batchSize = 1000;
        
        /**
         * 刷新间隔（毫秒）
         */
        @Min(value = 100, message = "InfluxDB flush interval must be at least 100ms")
        @Max(value = 60000, message = "InfluxDB flush interval cannot exceed 60000ms")
        private int flushIntervalMs = 1000;
        
        /**
         * 重试间隔（毫秒）
         */
        @Min(value = 100, message = "InfluxDB retry interval must be at least 100ms")
        @Max(value = 10000, message = "InfluxDB retry interval cannot exceed 10000ms")
        private int retryIntervalMs = 1000;
        
        /**
         * 最大重试次数
         */
        @Min(value = 0, message = "InfluxDB max retry count cannot be negative")
        @Max(value = 10, message = "InfluxDB max retry count cannot exceed 10")
        private int maxRetryCount = 3;
        
        /**
         * 是否启用GZIP压缩
         */
        private boolean gzipEnabled = true;
        
        /**
         * 是否启用日志
         */
        private boolean logEnabled = false;
    }
    
    /**
     * 连接池配置内部类
     */
    @Data
    public static class ConnectionPool {
        
        /**
         * 初始连接数
         */
        @Min(value = 1, message = "Initial pool size must be at least 1")
        @Max(value = 50, message = "Initial pool size cannot exceed 50")
        private int initialSize = 5;
        
        /**
         * 最小连接数
         */
        @Min(value = 1, message = "Min pool size must be at least 1")
        @Max(value = 50, message = "Min pool size cannot exceed 50")
        private int minIdle = 5;
        
        /**
         * 最大连接数
         */
        @Min(value = 1, message = "Max pool size must be at least 1")
        @Max(value = 200, message = "Max pool size cannot exceed 200")
        private int maxActive = 20;
        
        /**
         * 获取连接等待超时时间（毫秒）
         */
        @Min(value = 1000, message = "Max wait time must be at least 1000ms")
        @Max(value = 60000, message = "Max wait time cannot exceed 60000ms")
        private long maxWaitMs = 10000L;
        
        /**
         * 连接空闲超时时间（毫秒）
         */
        @Min(value = 60000, message = "Min evictable idle time must be at least 60000ms")
        @Max(value = 1800000, message = "Min evictable idle time cannot exceed 1800000ms")
        private long minEvictableIdleTimeMs = 300000L;
        
        /**
         * 验证查询SQL
         */
        @NotBlank(message = "Validation query cannot be blank")
        private String validationQuery = "SELECT 1";
        
        /**
         * 是否在借用时验证连接
         */
        private boolean testOnBorrow = false;
        
        /**
         * 是否在归还时验证连接
         */
        private boolean testOnReturn = false;
        
        /**
         * 是否在空闲时验证连接
         */
        private boolean testWhileIdle = true;
        
        /**
         * 空闲连接检测间隔（毫秒）
         */
        @Min(value = 30000, message = "Time between eviction runs must be at least 30000ms")
        @Max(value = 300000, message = "Time between eviction runs cannot exceed 300000ms")
        private long timeBetweenEvictionRunsMs = 60000L;
        
        /**
         * 是否移除废弃连接
         */
        private boolean removeAbandoned = true;
        
        /**
         * 废弃连接超时时间（秒）
         */
        @Min(value = 60, message = "Remove abandoned timeout must be at least 60 seconds")
        @Max(value = 3600, message = "Remove abandoned timeout cannot exceed 3600 seconds")
        private int removeAbandonedTimeoutSeconds = 300;
        
        /**
         * 是否记录废弃连接日志
         */
        private boolean logAbandoned = true;
    }
    
    /**
     * 获取MySQL JDBC URL
     * 
     * @return MySQL JDBC URL
     */
    public String getMySQLJdbcUrl() {
        return String.format("*****************************************************************************************************************************",
                mysql.getHost(),
                mysql.getPort(),
                mysql.getDatabase(),
                mysql.getCharset(),
                mysql.getTimezone(),
                mysql.isUseSSL(),
                mysql.isAllowPublicKeyRetrieval(),
                mysql.isRewriteBatchedStatements());
    }
}
