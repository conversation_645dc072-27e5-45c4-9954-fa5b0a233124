package com.trading.common.config;

import lombok.Builder;
import lombok.Data;

import java.time.Duration;
import java.util.function.Predicate;

/**
 * 通用重试配置
 * 统一的重试配置类，用于所有模块的重试机制
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
public class RetryConfig {
    
    /**
     * 最大重试次数
     */
    @Builder.Default
    private int maxAttempts = 3;
    
    /**
     * 初始延迟时间
     */
    @Builder.Default
    private Duration initialDelay = Duration.ofMillis(1000);
    
    /**
     * 退避倍数
     */
    @Builder.Default
    private double multiplier = 2.0;
    
    /**
     * 最大延迟时间
     */
    @Builder.Default
    private Duration maxDelay = Duration.ofSeconds(30);
    
    /**
     * 抖动因子（0-1之间）
     */
    @Builder.Default
    private double jitter = 0.1;
    
    /**
     * 重试条件
     */
    private Predicate<Exception> retryCondition;
    
    /**
     * 创建默认配置
     *
     * @return 默认重试配置
     */
    public static RetryConfig defaultConfig() {
        return RetryConfig.builder().build();
    }

    /**
     * 创建快速重试配置
     *
     * @return 快速重试配置
     */
    public static RetryConfig fastRetry() {
        return RetryConfig.builder()
                .maxAttempts(2)
                .initialDelay(Duration.ofMillis(500))
                .multiplier(1.5)
                .maxDelay(Duration.ofSeconds(5))
                .jitter(0.05)
                .build();
    }

    /**
     * 创建慢速重试配置
     *
     * @return 慢速重试配置
     */
    public static RetryConfig slowRetry() {
        return RetryConfig.builder()
                .maxAttempts(5)
                .initialDelay(Duration.ofSeconds(2))
                .multiplier(2.5)
                .maxDelay(Duration.ofMinutes(2))
                .jitter(0.2)
                .build();
    }

    /**
     * 创建无重试配置
     *
     * @return 无重试配置
     */
    public static RetryConfig noRetry() {
        return RetryConfig.builder()
                .maxAttempts(1)
                .build();
    }

    /**
     * 创建API重试配置
     *
     * @return API重试配置
     */
    public static RetryConfig apiRetryConfig() {
        return RetryConfig.builder()
                .maxAttempts(3)
                .initialDelay(Duration.ofMillis(1000))
                .multiplier(2.0)
                .maxDelay(Duration.ofSeconds(10))
                .jitter(0.1)
                .build();
    }

    /**
     * 创建WebSocket重试配置
     *
     * @return WebSocket重试配置
     */
    public static RetryConfig websocketRetryConfig() {
        return RetryConfig.builder()
                .maxAttempts(5)
                .initialDelay(Duration.ofSeconds(1))
                .multiplier(2.0)
                .maxDelay(Duration.ofSeconds(30))
                .jitter(0.2)
                .build();
    }

    /**
     * 创建存储重试配置
     *
     * @return 存储重试配置
     */
    public static RetryConfig storageRetryConfig() {
        return RetryConfig.builder()
                .maxAttempts(3)
                .initialDelay(Duration.ofMillis(500))
                .multiplier(2.0)
                .maxDelay(Duration.ofSeconds(15))
                .jitter(0.1)
                .build();
    }
    
    /**
     * 验证配置的有效性
     * 
     * @throws IllegalArgumentException 当配置无效时抛出
     */
    public void validate() {
        if (maxAttempts < 1) {
            throw new IllegalArgumentException("最大重试次数必须大于等于1");
        }
        
        if (initialDelay.isNegative() || initialDelay.isZero()) {
            throw new IllegalArgumentException("初始延迟时间必须大于0");
        }
        
        if (multiplier <= 0) {
            throw new IllegalArgumentException("退避倍数必须大于0");
        }
        
        if (maxDelay.isNegative() || maxDelay.isZero()) {
            throw new IllegalArgumentException("最大延迟时间必须大于0");
        }
        
        if (jitter < 0 || jitter > 1) {
            throw new IllegalArgumentException("抖动因子必须在0-1之间");
        }
        
        if (maxDelay.compareTo(initialDelay) < 0) {
            throw new IllegalArgumentException("最大延迟时间不能小于初始延迟时间");
        }
    }
    
    @Override
    public String toString() {
        return String.format(
                "RetryConfig{maxAttempts=%d, initialDelay=%dms, multiplier=%.1f, " +
                "maxDelay=%dms, jitter=%.2f, hasCustomCondition=%s}",
                maxAttempts, initialDelay.toMillis(), multiplier,
                maxDelay.toMillis(), jitter, retryCondition != null
        );
    }
}
