package com.trading.common.config;

import com.baomidou.mybatisplus.core.handlers.MetaObjectHandler;
import com.trading.common.config.TimestampConfiguration;
import org.apache.ibatis.reflection.MetaObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Instant;

/**
 * MyBatis-Plus自动填充处理器
 * 负责自动填充创建时间和更新时间字段
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class MyBatisMetaObjectHandler implements MetaObjectHandler {
    
    private static final Logger log = LoggerFactory.getLogger(MyBatisMetaObjectHandler.class);
    
    /**
     * 插入时自动填充
     * 使用服务器时间（UTC）
     */
    @Override
    public void insertFill(MetaObject metaObject) {
        log.debug("开始插入填充...");

        Instant serverTime = TimestampConfiguration.getCurrentServerTime();

        // 自动填充创建时间（使用服务器时间）
        this.strictInsertFill(metaObject, "createdAt", Instant.class, serverTime);

        // 自动填充更新时间（使用服务器时间）
        this.strictInsertFill(metaObject, "updatedAt", Instant.class, serverTime);

        log.debug("插入填充完成，使用服务器时间: {}", TimestampConfiguration.formatTimestamp(serverTime));
    }
    
    /**
     * 更新时自动填充
     * 使用服务器时间（UTC）
     */
    @Override
    public void updateFill(MetaObject metaObject) {
        log.debug("开始更新填充...");

        Instant serverTime = TimestampConfiguration.getCurrentServerTime();

        // 自动填充更新时间（使用服务器时间）
        this.strictUpdateFill(metaObject, "updatedAt", Instant.class, serverTime);

        log.debug("更新填充完成，使用服务器时间: {}", TimestampConfiguration.formatTimestamp(serverTime));
    }
}
