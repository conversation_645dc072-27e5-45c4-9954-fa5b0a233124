package com.trading.common.config;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ComponentScan;

import java.util.List;
import java.util.Map;

/**
 * 统一的市场数据配置类，绑定了 `trading.market-data` 前缀的属性。
 * <p>
 * 此类作为所有市场数据相关配置的单一来源，通过分层的静态内部类来组织配置项，
 * 包括数据收集 ({@link CollectorConfig})、处理 ({@link ProcessorConfig})、
 * 存储 ({@link StorageConfig}) 和消息队列 ({@link KafkaConfig})。
 * <p>
 * 它利用了 Spring Boot 的 {@code @ConfigurationProperties} 特性，实现了类型安全的配置管理。
 * 在初始化后 ({@code @PostConstruct})，会执行 {@link #validateConfiguration()} 方法，
 * 对关键配置（如K-line的intervals）进行自检，以保证系统的健壮性。
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "trading.market-data")
public class MarketDataConfig {

    private static final Logger log = LoggerFactory.getLogger(MarketDataConfig.class);

    /**
     * 配置验证和调试
     */
    @PostConstruct
    public void validateConfiguration() {
        log.info("=== MarketDataConfig 配置验证开始 ===");

        if (collector != null) {
            log.info("Collector配置: enabled={}, symbols={}",
                    collector.isEnabled(),
                    collector.getSymbols() != null ? collector.getSymbols().size() : "null");

            if (collector.getDataTypes() != null) {
                var klineConfig = collector.getDataTypes().getKline();
                if (klineConfig != null) {
                    log.info("Kline配置: enabled={}, intervals={}",
                            klineConfig.isEnabled(),
                            klineConfig.getIntervals());

                    if (klineConfig.getIntervals() == null) {
                        log.error("❌ CRITICAL: intervals配置为null！");
                    } else if (klineConfig.getIntervals().isEmpty()) {
                        log.warn("⚠️ WARNING: intervals配置为空列表！");
                    } else {
                        log.info("✅ intervals配置正常: {}", klineConfig.getIntervals());
                    }
                } else {
                    log.error("❌ CRITICAL: kline配置为null！");
                }
            } else {
                log.error("❌ CRITICAL: dataTypes配置为null！");
            }
        } else {
            log.error("❌ CRITICAL: collector配置为null！");
        }

        log.info("=== MarketDataConfig 配置验证结束 ===");
    }
    
    /**
     * 数据收集器 (Collector) 的相关配置。
     * <p>
     * 负责定义从交易所收集哪些交易对 (symbols)、哪些数据类型 (dataTypes)，
     * 以及如何处理历史数据 (historical) 的回填逻辑。
     */
    private CollectorConfig collector = new CollectorConfig();

    /**
     * 数据处理器 (Processor) 的相关配置。
     * <p>
     * 定义了数据在被存储前的处理流程，包括重试逻辑 (retry)、数据质量校验 (quality)、
     * 性能调优 (performance)、数据格式转换 (conversion) 和监控 (monitoring)。
     */
    private ProcessorConfig processor = new ProcessorConfig();

    /**
     * Apache Kafka 的相关配置。
     * <p>
     * 用于配置消息队列的连接信息、主题 (topics) 和生产者 (producer) 参数。
     * 仅在 {@code kafka.enabled} 为 `true` 时生效。
     */
    private KafkaConfig kafka = new KafkaConfig();

    /**
     * 数据存储 (Storage) 的相关配置。
     * <p>
     * 定义了数据的持久化方式，目前支持 Redis ({@link StorageConfig.RedisConfig})
     * 和 InfluxDB ({@link StorageConfig.InfluxDbConfig})。
     */
    private StorageConfig storage = new StorageConfig();
    
    /**
     * 数据收集器的配置参数。
     */
    @Data
    public static class CollectorConfig {
        private boolean enabled = true;
        private List<String> symbols;
        private DataTypesConfig dataTypes = new DataTypesConfig();
        private HistoricalConfig historical = new HistoricalConfig();
        private PerformanceConfig performance = new PerformanceConfig();

        @Data
        public static class HistoricalConfig {
            /**
             * 是否强制重新收集所有历史数据（忽略数据库中已有数据）
             */
            private boolean forceFullCollection = false;

            /**
             * 默认历史数据收集天数
             */
            private int defaultDays = 90;

            /**
             * 增量更新检查间隔（小时）
             */
            private int incrementalCheckHours = 1;
        }

        @Data
        public static class DataTypesConfig {
            private KlineConfig kline = new KlineConfig();
            private DepthConfig depth = new DepthConfig();
            private TradeConfig trade = new TradeConfig();
            private TickerConfig ticker = new TickerConfig();
            private BookTickerConfig bookTicker = new BookTickerConfig();

            @Data
            public static class KlineConfig {
                private boolean enabled = true;
                private List<String> intervals = new java.util.ArrayList<>();
            }

            @Data
            public static class DepthConfig {
                private boolean enabled = true;
                private List<Integer> levels;
                private int speed = 100;
            }

            @Data
            public static class TradeConfig {
                private boolean enabled = true;
            }

            @Data
            public static class TickerConfig {
                private boolean enabled = true;
            }

            @Data
            public static class BookTickerConfig {
                private boolean enabled = true;
            }
        }

        @Data
        public static class PerformanceConfig {
            private BatchConfig batch = new BatchConfig();
            private RateLimitConfig rateLimit = new RateLimitConfig();
            private RetryConfig retry = new RetryConfig();

            @Data
            public static class BatchConfig {
                private int klineSize = 1000;
                private int tradeSize = 500;
                private int depthSize = 100;
            }

            @Data
            public static class RateLimitConfig {
                private long requestDelayMs = 100;
                private int maxConcurrentRequests = 4;
                private int tokenBucketCapacity = 10;
                private double tokenRefillRate = 5.0;
            }

            @Data
            public static class RetryConfig {
                private int maxAttempts = 3;
                private long delayMs = 1000;
                private double exponentialBackoffMultiplier = 2.0;
                private long maxDelayMs = 30000;
            }
        }
    }

    @Data
    public static class ProcessorConfig {
        private RetryConfig retry = new RetryConfig();
        private QualityConfig quality = new QualityConfig();
        private PerformanceConfig performance = new PerformanceConfig();
        private VirtualThreadsConfig virtualThreads = new VirtualThreadsConfig();
        private ValidationConfig validation = new ValidationConfig();
        private ConversionConfig conversion = new ConversionConfig();
        private MonitoringConfig monitoring = new MonitoringConfig();

        @Data
        public static class RetryConfig {
            private boolean enabled = true;
            private int maxAttempts = 3; // 适度增加重试次数，平衡稳定性和性能
            private long baseDelayMs = 1000L; // 减少基础延迟，提升响应速度
            private double multiplier = 1.5; // 减少退避倍数，避免过长等待
            private long maxDelayMs = 10000L; // 减少最大延迟
            private long timeoutMs = 3000L; // 适中的超时时间
            private boolean enableBackpressure = true; // 启用背压控制
            private int maxConcurrentRetries = 10; // 最大并发重试数
        }

        @Data
        public static class QualityConfig {
            private boolean enabled = true;
            private double priceChangeThreshold = 0.1;
            private double volumeChangeThreshold = 10.0;
            private long timestampToleranceMs = 5000L;
            private int duplicateCheckWindowSize = 100;
            // 时间戳检查配置
            private TimestampCheckConfig timestampCheck = new TimestampCheckConfig();

            @Data
            public static class TimestampCheckConfig {
                private boolean enabled = true;
                private long maxAgeHours = 48L; // 增加最大数据年龄到48小时，更宽松的历史数据处理
                private long maxFutureMinutes = 5L; // 增加最大未来时间到5分钟，考虑时区和网络延迟
                private boolean strictModeForRealtime = false; // 关闭实时数据严格模式，减少误判
                private long realtimeMaxAgeHours = 6L; // 增加实时数据最大年龄到6小时
                private boolean enableHistoricalDataMode = true; // 启用历史数据模式
            }
        }

        @Data
        public static class PerformanceConfig {
            private boolean enabled = true;
            private long processingTimeoutMs = 1000L;
            private int batchSize = 200; // 增加批处理大小，减少处理频率
            private int queueCapacity = 5000; // 减少队列容量，避免内存过度使用
            private int threadPoolSize = 5; // 减少线程池大小
        }

        @Data
        public static class VirtualThreadsConfig {
            private boolean enabled = true;
            private int maxThreads = 200; // 减少最大线程数，避免资源耗尽
            private String namePrefix = "market-data-vt-";
            private int corePoolSize = 5; // 减少核心线程数
        }

        @Data
        public static class ValidationConfig {
            private boolean enabled = true;
            private boolean skipTimeValidationForHistorical = false; // 新增：为历史数据回补跳过时间验证
            private double priceChangeThreshold = 0.1;
            private double volumeChangeThreshold = 10.0;
            private long timestampToleranceMs = 5000L;
            private boolean strictMode = false;
            private double priceDeviationThreshold = 0.20; // 进一步放宽价格偏差阈值到20%，适应加密货币市场波动
            private double volumeThreshold = 0.0; // 设为0允许零交易量，由智能逻辑判断合理性
            private boolean enableSmartVolumeValidation = true; // 启用智能交易量验证
            private boolean allowZeroVolumeOnMarketClose = true; // 允许市场休市时的零交易量
        }

        @Data
        public static class ConversionConfig {
            private boolean enabled = true;
            private String timestampFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
            private int decimalPlaces = 8;
            private boolean normalizeSymbols = true;
            private NormalizationConfig normalization = new NormalizationConfig();
            private QlibFormatConfig qlibFormat = new QlibFormatConfig();

            @Data
            public static class NormalizationConfig {
                private boolean enabled = true;
                private boolean removeOutliers = true;
                private double outlierThreshold = 3.0;
                private double scaleFactor = 1.0;
            }

            @Data
            public static class QlibFormatConfig {
                private boolean enabled = true;
                private String dateFormat = "yyyy-MM-dd";
                private String timeFormat = "HH:mm:ss";
                private boolean includeVolume = true;
                private int precision = 8;
            }
        }

        @Data
        public static class MonitoringConfig {
            private boolean enabled = true;
            private long metricsIntervalMs = 300000L; // 调整为5分钟，减少CPU消耗
            private boolean enableDetailedMetrics = false;
            private int historySize = 1000;
            private long reportIntervalSeconds = 300L; // 调整为5分钟，减少CPU消耗
        }
    }

    @Data
    public static class KafkaConfig {
        private boolean enabled = true;
        private String bootstrapServers;
        private String topicPrefix = "crypto-market-data";
        private int partitions = 3;
        private short replicationFactor = 1;
        private ProducerConfig producer = new ProducerConfig();
        private TopicsConfig topics = new TopicsConfig();

        @Data
        public static class TopicsConfig {
            private String kline = "kline";
            private String depth = "depth";
            private String trade = "trade";
            private String ticker = "ticker";
        }

        @Data
        public static class ProducerConfig {
            private int batchSize = 16384;
            private long lingerMs = 5L;
            private int bufferMemory = 33554432;
            private String compressionType = "snappy";
            private int retries = 3;
            private String acks = "all";
        }
    }

    @Data
    public static class StorageConfig {
        private RedisConfig redis = new RedisConfig();
        private InfluxDbConfig influxdb = new InfluxDbConfig();

        @Data
        public static class RedisConfig {
            private boolean enabled = true;
            private String host;
            private int port;
            private String password;
            private int database = 0;
            private int maxConnections = 20;
            private long timeoutMs = 3000L;
            private long cacheTtl = 300L;
            private String keyPrefix = "crypto:market-data:";
        }

        @Data
        public static class InfluxDbConfig {
            private boolean enabled = true;
            private String url;
            private String token;
            private String org;
            private String bucket;
            private int batchSize = 1000;
            private long flushIntervalMs = 1000L;
        }
    }
}
