package com.trading.common.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import jakarta.annotation.PostConstruct;
import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.TimeZone;

/**
 * 时间戳配置类
 * 确保所有时间戳使用服务器时间（UTC）
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Configuration
public class TimestampConfiguration {

    private static final Logger log = LoggerFactory.getLogger(TimestampConfiguration.class);
    
    /**
     * 标准时间格式
     */
    public static final String STANDARD_DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    
    /**
     * UTC时区
     */
    public static final ZoneId UTC_ZONE = ZoneOffset.UTC;
    
    /**
     * 系统时钟（UTC）
     */
    private static final Clock SYSTEM_CLOCK = Clock.systemUTC();
    
    @PostConstruct
    public void initializeTimezone() {
        // 设置JVM默认时区为UTC
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        System.setProperty("user.timezone", "UTC");
        
        log.info("时间戳配置初始化完成:");
        log.info("  - JVM默认时区: {}", TimeZone.getDefault().getID());
        log.info("  - 系统属性时区: {}", System.getProperty("user.timezone"));
        log.info("  - 当前服务器时间: {}", getCurrentServerTime());
        log.info("  - 时间格式: {}", STANDARD_DATETIME_FORMAT);
    }
    
    /**
     * 获取当前服务器时间
     * 确保始终返回UTC时间
     */
    public static Instant getCurrentServerTime() {
        return Instant.now(SYSTEM_CLOCK);
    }
    
    /**
     * 获取格式化的当前服务器时间
     */
    public static String getCurrentServerTimeFormatted() {
        return DateTimeFormatter.ofPattern(STANDARD_DATETIME_FORMAT)
                .withZone(UTC_ZONE)
                .format(getCurrentServerTime());
    }
    
    /**
     * 格式化时间戳
     */
    public static String formatTimestamp(Instant timestamp) {
        if (timestamp == null) {
            return null;
        }
        return DateTimeFormatter.ofPattern(STANDARD_DATETIME_FORMAT)
                .withZone(UTC_ZONE)
                .format(timestamp);
    }
    
    /**
     * 解析时间戳字符串
     */
    public static Instant parseTimestamp(String timestampStr) {
        if (timestampStr == null || timestampStr.trim().isEmpty()) {
            return null;
        }
        try {
            return Instant.parse(timestampStr);
        } catch (Exception e) {
            log.warn("解析时间戳失败: {}, 使用当前服务器时间", timestampStr, e);
            return getCurrentServerTime();
        }
    }
    
    /**
     * 验证时间戳是否有效
     */
    public static boolean isValidTimestamp(Instant timestamp) {
        if (timestamp == null) {
            return false;
        }
        
        Instant now = getCurrentServerTime();
        Instant maxPast = now.minusSeconds(86400 * 30); // 30天前
        Instant maxFuture = now.plusSeconds(300); // 5分钟后
        
        return timestamp.isAfter(maxPast) && timestamp.isBefore(maxFuture);
    }
    
    /**
     * 获取安全的时间戳
     * 如果传入的时间戳无效，返回当前服务器时间
     */
    public static Instant getSafeTimestamp(Instant timestamp) {
        if (isValidTimestamp(timestamp)) {
            return timestamp;
        }
        log.debug("时间戳无效: {}, 使用当前服务器时间", timestamp);
        return getCurrentServerTime();
    }
    
    /**
     * 时间戳工具类
     */
    @Bean
    public TimestampUtils timestampUtils() {
        return new TimestampUtils();
    }
    
    /**
     * 时间戳工具类实现
     */
    public static class TimestampUtils {
        
        /**
         * 创建时间戳（用于created_at字段）
         */
        public Instant createTimestamp() {
            return getCurrentServerTime();
        }
        
        /**
         * 更新时间戳（用于updated_at字段）
         */
        public Instant updateTimestamp() {
            return getCurrentServerTime();
        }
        
        /**
         * 获取数据库时间戳（确保与数据库时区一致）
         */
        public Instant getDatabaseTimestamp() {
            // 数据库配置为UTC时区，直接返回UTC时间
            return getCurrentServerTime();
        }
        
        /**
         * 转换为数据库兼容的时间戳
         */
        public java.sql.Timestamp toSqlTimestamp(Instant instant) {
            if (instant == null) {
                instant = getCurrentServerTime();
            }
            return java.sql.Timestamp.from(instant);
        }
        
        /**
         * 从SQL时间戳转换
         */
        public Instant fromSqlTimestamp(java.sql.Timestamp timestamp) {
            if (timestamp == null) {
                return getCurrentServerTime();
            }
            return timestamp.toInstant();
        }
        
        /**
         * 获取时间戳的毫秒值
         */
        public long getTimestampMillis(Instant instant) {
            if (instant == null) {
                instant = getCurrentServerTime();
            }
            return instant.toEpochMilli();
        }
        
        /**
         * 从毫秒值创建时间戳
         */
        public Instant fromMillis(long millis) {
            try {
                return Instant.ofEpochMilli(millis);
            } catch (Exception e) {
                log.warn("从毫秒值创建时间戳失败: {}, 使用当前服务器时间", millis, e);
                return getCurrentServerTime();
            }
        }
        
        /**
         * 计算时间差（秒）
         */
        public long getTimeDifferenceSeconds(Instant start, Instant end) {
            if (start == null || end == null) {
                return 0;
            }
            return java.time.Duration.between(start, end).getSeconds();
        }
        
        /**
         * 检查时间戳是否过期
         */
        public boolean isExpired(Instant timestamp, long maxAgeSeconds) {
            if (timestamp == null) {
                return true;
            }
            Instant now = getCurrentServerTime();
            return getTimeDifferenceSeconds(timestamp, now) > maxAgeSeconds;
        }
    }
    
    /**
     * 自定义Jackson时间模块
     * 确保JSON序列化/反序列化使用UTC时间
     */
    @Bean
    @Primary
    public JavaTimeModule customJavaTimeModule() {
        JavaTimeModule module = new JavaTimeModule();
        
        // 使用默认的Instant序列化器和反序列化器
        // Jackson会自动使用ISO-8601格式，这与我们的UTC配置兼容
        
        log.info("自定义JavaTimeModule配置完成，使用UTC时区");
        return module;
    }
}
