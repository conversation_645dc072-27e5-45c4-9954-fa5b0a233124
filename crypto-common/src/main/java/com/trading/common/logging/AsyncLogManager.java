package com.trading.common.logging;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.trading.common.utils.AsyncDelayUtils;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 异步日志管理器
 * 实现高性能异步日志处理，减少日志对主线程的影响
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class AsyncLogManager {

    private static final Logger log = LoggerFactory.getLogger(AsyncLogManager.class);

    // 异步日志配置
    @Value("${app.performance.logging.async.enabled:true}")
    private boolean asyncLoggingEnabled;
    
    @Value("${app.performance.logging.async.queue-size:10000}")
    private int asyncQueueSize;
    
    @Value("${app.performance.logging.async.batch-size:100}")
    private int batchSize;
    
    @Value("${app.performance.logging.async.flush-interval-ms:1000}")
    private int flushIntervalMs;
    
    @Value("${app.performance.logging.frequency-control.enabled:true}")
    private boolean frequencyControlEnabled;
    
    @Value("${app.performance.logging.frequency-control.max-per-second:100}")
    private int maxLogsPerSecond;

    // 异步日志处理
    private ExecutorService logProcessingExecutor;
    private ScheduledExecutorService flushExecutor;
    private final Queue<LogEntry> logQueue = new ConcurrentLinkedQueue<>();
    private final AtomicLong queueSize = new AtomicLong(0);
    
    // 频率控制
    private final Map<String, LogFrequencyController> frequencyControllers = new ConcurrentHashMap<>();
    
    // 性能统计
    private final LongAdder totalLogsProcessed = new LongAdder();
    private final LongAdder totalLogsDropped = new LongAdder();
    private final LongAdder totalBatchesProcessed = new LongAdder();
    private final LongAdder asyncProcessingTime = new LongAdder();

    @PostConstruct
    public void initialize() {
        log.info("初始化异步日志管理器...");
        
        if (asyncLoggingEnabled) {
            // 创建异步日志处理线程池
            logProcessingExecutor = Executors.newFixedThreadPool(2, r -> {
                Thread thread = new Thread(r, "async-log-processor");
                thread.setDaemon(true);
                return thread;
            });
            
            // 创建定期刷新执行器
            flushExecutor = Executors.newScheduledThreadPool(1, r -> {
                Thread thread = new Thread(r, "async-log-flusher");
                thread.setDaemon(true);
                return thread;
            });
            
            // 启动异步日志处理
            startAsyncLogProcessing();
            
            log.info("异步日志管理器初始化完成 - 队列大小: {}, 批处理大小: {}, 刷新间隔: {}ms", 
                    asyncQueueSize, batchSize, flushIntervalMs);
        } else {
            log.info("异步日志已禁用，使用同步日志");
        }
    }

    /**
     * 启动异步日志处理
     */
    private void startAsyncLogProcessing() {
        // 启动日志处理任务
        logProcessingExecutor.submit(this::processLogQueue);
        
        // 启动定期刷新任务
        flushExecutor.scheduleAtFixedRate(this::flushLogs, 
                flushIntervalMs, flushIntervalMs, TimeUnit.MILLISECONDS);
    }

    /**
     * 异步记录日志
     */
    public void logAsync(String loggerName, LogLevel level, String message, Object... args) {
        if (!asyncLoggingEnabled) {
            // 直接同步记录
            logSync(loggerName, level, message, args);
            return;
        }
        
        // 频率控制检查
        if (frequencyControlEnabled && !checkFrequencyLimit(loggerName, level)) {
            totalLogsDropped.increment();
            return;
        }
        
        // 检查队列容量
        if (queueSize.get() >= asyncQueueSize) {
            totalLogsDropped.increment();
            return;
        }
        
        // 创建日志条目
        LogEntry entry = new LogEntry(loggerName, level, message, args, System.currentTimeMillis());
        
        // 添加到队列
        logQueue.offer(entry);
        queueSize.incrementAndGet();
        totalLogsProcessed.increment();
    }

    /**
     * 同步记录日志
     */
    private void logSync(String loggerName, LogLevel level, String message, Object... args) {
        Logger logger = LoggerFactory.getLogger(loggerName);
        
        switch (level) {
            case DEBUG:
                if (logger.isDebugEnabled()) {
                    logger.debug(message, args);
                }
                break;
            case INFO:
                if (logger.isInfoEnabled()) {
                    logger.info(message, args);
                }
                break;
            case WARN:
                if (logger.isWarnEnabled()) {
                    logger.warn(message, args);
                }
                break;
            case ERROR:
                if (logger.isErrorEnabled()) {
                    logger.error(message, args);
                }
                break;
        }
    }

    /**
     * 检查频率限制
     */
    private boolean checkFrequencyLimit(String loggerName, LogLevel level) {
        String key = loggerName + ":" + level;
        LogFrequencyController controller = frequencyControllers.computeIfAbsent(
                key, k -> new LogFrequencyController(maxLogsPerSecond));
        
        return controller.allowLog();
    }

    /**
     * 处理日志队列
     */
    private void processLogQueue() {
        while (!Thread.currentThread().isInterrupted()) {
            try {
                processBatch();
                AsyncDelayUtils.delay(10).join(); // 短暂休眠避免CPU占用过高
            } catch (Exception e) {
                log.error("异步日志处理失败", e);
                break;
            }
        }
    }

    /**
     * 处理一批日志
     */
    private void processBatch() {
        if (logQueue.isEmpty()) {
            return;
        }
        
        long startTime = System.currentTimeMillis();
        int processedCount = 0;
        
        // 处理一批日志
        for (int i = 0; i < batchSize && !logQueue.isEmpty(); i++) {
            LogEntry entry = logQueue.poll();
            if (entry != null) {
                queueSize.decrementAndGet();
                processLogEntry(entry);
                processedCount++;
            }
        }
        
        if (processedCount > 0) {
            totalBatchesProcessed.increment();
            asyncProcessingTime.add(System.currentTimeMillis() - startTime);
        }
    }

    /**
     * 处理单个日志条目
     */
    private void processLogEntry(LogEntry entry) {
        try {
            logSync(entry.getLoggerName(), entry.getLevel(), entry.getMessage(), entry.getArgs());
        } catch (Exception e) {
            // 避免日志处理异常导致的递归问题 - 使用标准错误输出但避免递归
            // 这里保持System.err是合理的，因为这是日志系统本身的错误处理
            System.err.println("Failed to process log entry: " + e.getMessage());
        }
    }

    /**
     * 刷新日志
     */
    private void flushLogs() {
        try {
            // 处理剩余的日志
            while (!logQueue.isEmpty()) {
                processBatch();
            }
        } catch (Exception e) {
            log.error("刷新日志失败", e);
        }
    }

    /**
     * 获取异步日志统计信息
     */
    public AsyncLogStatistics getStatistics() {
        return new AsyncLogStatistics(
                totalLogsProcessed.sum(),
                totalLogsDropped.sum(),
                totalBatchesProcessed.sum(),
                asyncProcessingTime.sum(),
                queueSize.get(),
                frequencyControllers.size(),
                asyncLoggingEnabled
        );
    }

    /**
     * 清理过期的频率控制器
     */
    public void cleanupFrequencyControllers() {
        long currentTime = System.currentTimeMillis();
        frequencyControllers.entrySet().removeIf(entry -> 
                entry.getValue().isExpired(currentTime, 300000)); // 5分钟过期
    }

    @PreDestroy
    public void shutdown() {
        log.info("关闭异步日志管理器...");

        // 刷新剩余日志
        flushLogs();

        // 关闭执行器 - 减少超时时间以避免测试环境中的长时间等待
        if (flushExecutor != null && !flushExecutor.isShutdown()) {
            flushExecutor.shutdown();
            try {
                if (!flushExecutor.awaitTermination(1, TimeUnit.SECONDS)) {
                    log.warn("刷新执行器未能在1秒内正常关闭，强制关闭");
                    flushExecutor.shutdownNow();
                    // 再给500ms时间进行强制关闭
                    if (!flushExecutor.awaitTermination(500, TimeUnit.MILLISECONDS)) {
                        log.error("刷新执行器强制关闭失败");
                    }
                }
            } catch (InterruptedException e) {
                log.warn("关闭刷新执行器时被中断");
                flushExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        if (logProcessingExecutor != null && !logProcessingExecutor.isShutdown()) {
            logProcessingExecutor.shutdown();
            try {
                if (!logProcessingExecutor.awaitTermination(1, TimeUnit.SECONDS)) {
                    log.warn("日志处理执行器未能在1秒内正常关闭，强制关闭");
                    logProcessingExecutor.shutdownNow();
                    // 再给500ms时间进行强制关闭
                    if (!logProcessingExecutor.awaitTermination(500, TimeUnit.MILLISECONDS)) {
                        log.error("日志处理执行器强制关闭失败");
                    }
                }
            } catch (InterruptedException e) {
                log.warn("关闭日志处理执行器时被中断");
                logProcessingExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        log.info("异步日志管理器已关闭");
    }

    /**
     * 日志级别枚举
     */
    public enum LogLevel {
        DEBUG, INFO, WARN, ERROR
    }

    /**
     * 日志条目
     */
    private static class LogEntry {
        private final String loggerName;
        private final LogLevel level;
        private final String message;
        private final Object[] args;
        private final long timestamp;

        public LogEntry(String loggerName, LogLevel level, String message, Object[] args, long timestamp) {
            this.loggerName = loggerName;
            this.level = level;
            this.message = message;
            this.args = args;
            this.timestamp = timestamp;
        }

        // Getters
        public String getLoggerName() { return loggerName; }
        public LogLevel getLevel() { return level; }
        public String getMessage() { return message; }
        public Object[] getArgs() { return args; }
        public long getTimestamp() { return timestamp; }
    }

    /**
     * 日志频率控制器
     */
    private static class LogFrequencyController {
        private final int maxLogsPerSecond;
        private final AtomicLong lastResetTime = new AtomicLong(System.currentTimeMillis());
        private final AtomicLong logCount = new AtomicLong(0);

        public LogFrequencyController(int maxLogsPerSecond) {
            this.maxLogsPerSecond = maxLogsPerSecond;
        }

        public boolean allowLog() {
            long currentTime = System.currentTimeMillis();
            long lastReset = lastResetTime.get();
            
            // 每秒重置计数器
            if (currentTime - lastReset >= 1000) {
                if (lastResetTime.compareAndSet(lastReset, currentTime)) {
                    logCount.set(0);
                }
            }
            
            return logCount.incrementAndGet() <= maxLogsPerSecond;
        }

        public boolean isExpired(long currentTime, long expireTimeMs) {
            return currentTime - lastResetTime.get() > expireTimeMs;
        }
    }

    /**
     * 异步日志统计信息
     */
    public static class AsyncLogStatistics {
        private final long totalProcessed;
        private final long totalDropped;
        private final long totalBatches;
        private final long processingTime;
        private final long currentQueueSize;
        private final int frequencyControllers;
        private final boolean asyncEnabled;

        public AsyncLogStatistics(long totalProcessed, long totalDropped, long totalBatches,
                                long processingTime, long currentQueueSize, int frequencyControllers,
                                boolean asyncEnabled) {
            this.totalProcessed = totalProcessed;
            this.totalDropped = totalDropped;
            this.totalBatches = totalBatches;
            this.processingTime = processingTime;
            this.currentQueueSize = currentQueueSize;
            this.frequencyControllers = frequencyControllers;
            this.asyncEnabled = asyncEnabled;
        }

        // Getters
        public long getTotalProcessed() { return totalProcessed; }
        public long getTotalDropped() { return totalDropped; }
        public long getTotalBatches() { return totalBatches; }
        public long getProcessingTime() { return processingTime; }
        public long getCurrentQueueSize() { return currentQueueSize; }
        public int getFrequencyControllers() { return frequencyControllers; }
        public boolean isAsyncEnabled() { return asyncEnabled; }

        public double getDropRate() {
            long total = totalProcessed + totalDropped;
            return total == 0 ? 0.0 : (double) totalDropped / total * 100;
        }

        public double getAverageProcessingTime() {
            return totalBatches == 0 ? 0.0 : (double) processingTime / totalBatches;
        }

        @Override
        public String toString() {
            return String.format("AsyncLog: processed=%d, dropped=%d, batches=%d, " +
                            "queueSize=%d, dropRate=%.2f%%, avgTime=%.2fms, async=%s",
                    totalProcessed, totalDropped, totalBatches, currentQueueSize,
                    getDropRate(), getAverageProcessingTime(), asyncEnabled);
        }
    }
}
