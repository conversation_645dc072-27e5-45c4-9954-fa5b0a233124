package com.trading.common.logging;

import com.trading.common.gc.MemoryAllocationOptimizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 高性能日志工具
 * 提供优化的日志记录功能，减少字符串拼接和内存分配开销
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class HighPerformanceLogger {

    private static final Logger log = LoggerFactory.getLogger(HighPerformanceLogger.class);

    @Autowired
    private AsyncLogManager asyncLogManager;
    
    @Autowired
    private MemoryAllocationOptimizer memoryOptimizer;

    // 日志缓存
    private final Map<String, Logger> loggerCache = new ConcurrentHashMap<>();
    
    // 性能统计
    private final LongAdder totalLogCalls = new LongAdder();
    private final LongAdder cachedLoggerHits = new LongAdder();
    private final LongAdder stringOptimizations = new LongAdder();
    private final AtomicLong totalStringSaved = new AtomicLong(0);

    @PostConstruct
    public void initialize() {
        log.info("初始化高性能日志工具...");
    }

    /**
     * 获取缓存的Logger
     */
    private Logger getCachedLogger(String name) {
        Logger logger = loggerCache.get(name);
        if (logger != null) {
            cachedLoggerHits.increment();
            return logger;
        }
        
        logger = LoggerFactory.getLogger(name);
        loggerCache.put(name, logger);
        return logger;
    }

    /**
     * 获取缓存的Logger（通过Class）
     */
    private Logger getCachedLogger(Class<?> clazz) {
        return getCachedLogger(clazz.getName());
    }

    /**
     * 高性能DEBUG日志
     */
    public void debug(Class<?> clazz, String message, Object... args) {
        totalLogCalls.increment();
        
        Logger logger = getCachedLogger(clazz);
        if (logger.isDebugEnabled()) {
            asyncLogManager.logAsync(clazz.getName(), AsyncLogManager.LogLevel.DEBUG, message, args);
        }
    }

    /**
     * 高性能INFO日志
     */
    public void info(Class<?> clazz, String message, Object... args) {
        totalLogCalls.increment();
        
        Logger logger = getCachedLogger(clazz);
        if (logger.isInfoEnabled()) {
            asyncLogManager.logAsync(clazz.getName(), AsyncLogManager.LogLevel.INFO, message, args);
        }
    }

    /**
     * 高性能WARN日志
     */
    public void warn(Class<?> clazz, String message, Object... args) {
        totalLogCalls.increment();
        
        Logger logger = getCachedLogger(clazz);
        if (logger.isWarnEnabled()) {
            asyncLogManager.logAsync(clazz.getName(), AsyncLogManager.LogLevel.WARN, message, args);
        }
    }

    /**
     * 高性能ERROR日志
     */
    public void error(Class<?> clazz, String message, Object... args) {
        totalLogCalls.increment();
        
        Logger logger = getCachedLogger(clazz);
        if (logger.isErrorEnabled()) {
            asyncLogManager.logAsync(clazz.getName(), AsyncLogManager.LogLevel.ERROR, message, args);
        }
    }

    /**
     * 优化的字符串拼接日志
     */
    public void infoWithOptimizedString(Class<?> clazz, String separator, Object... parts) {
        totalLogCalls.increment();
        stringOptimizations.increment();
        
        Logger logger = getCachedLogger(clazz);
        if (logger.isInfoEnabled()) {
            String optimizedMessage = memoryOptimizer.createOptimizedString(separator, parts);
            totalStringSaved.addAndGet(estimateStringSavings(parts));
            asyncLogManager.logAsync(clazz.getName(), AsyncLogManager.LogLevel.INFO, optimizedMessage);
        }
    }

    /**
     * 优化的字符串拼接日志（DEBUG级别）
     */
    public void debugWithOptimizedString(Class<?> clazz, String separator, Object... parts) {
        totalLogCalls.increment();
        stringOptimizations.increment();
        
        Logger logger = getCachedLogger(clazz);
        if (logger.isDebugEnabled()) {
            String optimizedMessage = memoryOptimizer.createOptimizedString(separator, parts);
            totalStringSaved.addAndGet(estimateStringSavings(parts));
            asyncLogManager.logAsync(clazz.getName(), AsyncLogManager.LogLevel.DEBUG, optimizedMessage);
        }
    }

    /**
     * 条件日志记录（避免不必要的字符串构建）
     */
    public void infoIf(Class<?> clazz, boolean condition, String message, Object... args) {
        if (!condition) {
            return;
        }
        
        totalLogCalls.increment();
        
        Logger logger = getCachedLogger(clazz);
        if (logger.isInfoEnabled()) {
            asyncLogManager.logAsync(clazz.getName(), AsyncLogManager.LogLevel.INFO, message, args);
        }
    }

    /**
     * 条件日志记录（DEBUG级别）
     */
    public void debugIf(Class<?> clazz, boolean condition, String message, Object... args) {
        if (!condition) {
            return;
        }
        
        totalLogCalls.increment();
        
        Logger logger = getCachedLogger(clazz);
        if (logger.isDebugEnabled()) {
            asyncLogManager.logAsync(clazz.getName(), AsyncLogManager.LogLevel.DEBUG, message, args);
        }
    }

    /**
     * 批量日志记录（减少频繁调用）
     */
    public void infoBatch(Class<?> clazz, String messageTemplate, Object[]... argsList) {
        Logger logger = getCachedLogger(clazz);
        if (!logger.isInfoEnabled()) {
            return;
        }
        
        for (Object[] args : argsList) {
            totalLogCalls.increment();
            asyncLogManager.logAsync(clazz.getName(), AsyncLogManager.LogLevel.INFO, messageTemplate, args);
        }
    }

    /**
     * 性能敏感的日志记录（带频率控制）
     */
    public void infoThrottled(Class<?> clazz, String key, String message, Object... args) {
        totalLogCalls.increment();
        
        Logger logger = getCachedLogger(clazz);
        if (logger.isInfoEnabled()) {
            // 使用key作为频率控制的标识
            asyncLogManager.logAsync(clazz.getName() + ":" + key, AsyncLogManager.LogLevel.INFO, message, args);
        }
    }

    /**
     * 数据处理日志（专门优化）
     */
    public void logDataProcessing(Class<?> clazz, String operation, String symbol, String dataType, 
                                 long processingTime, long totalCount) {
        totalLogCalls.increment();
        
        Logger logger = getCachedLogger(clazz);
        if (logger.isInfoEnabled()) {
            // 使用预定义的消息模板避免字符串拼接
            asyncLogManager.logAsync(clazz.getName(), AsyncLogManager.LogLevel.INFO,
                    "数据处理完成: operation={}, symbol={}, dataType={}, 耗时={}ms, 总数={}",
                    operation, symbol, dataType, processingTime, totalCount);
        }
    }

    /**
     * 错误日志（带异常优化）
     */
    public void errorWithException(Class<?> clazz, String message, Throwable throwable, Object... args) {
        totalLogCalls.increment();
        
        Logger logger = getCachedLogger(clazz);
        if (logger.isErrorEnabled()) {
            // 构建包含异常信息的参数数组
            Object[] allArgs = new Object[args.length + 1];
            System.arraycopy(args, 0, allArgs, 0, args.length);
            allArgs[args.length] = throwable;
            
            asyncLogManager.logAsync(clazz.getName(), AsyncLogManager.LogLevel.ERROR, 
                    message + " - Exception: {}", allArgs);
        }
    }

    /**
     * 估算字符串节省的内存
     */
    private long estimateStringSavings(Object[] parts) {
        long totalLength = 0;
        for (Object part : parts) {
            if (part != null) {
                totalLength += part.toString().length();
            }
        }
        return totalLength * 2; // 每个字符2字节
    }

    /**
     * 清理缓存
     */
    public void clearCaches() {
        loggerCache.clear();
        asyncLogManager.cleanupFrequencyControllers();
        log.info("日志缓存已清理");
    }

    /**
     * 获取性能统计信息
     */
    public LoggerStatistics getStatistics() {
        AsyncLogManager.AsyncLogStatistics asyncStats = asyncLogManager.getStatistics();
        
        return new LoggerStatistics(
                totalLogCalls.sum(),
                cachedLoggerHits.sum(),
                stringOptimizations.sum(),
                totalStringSaved.get(),
                loggerCache.size(),
                asyncStats
        );
    }

    /**
     * 日志工具统计信息
     */
    public static class LoggerStatistics {
        private final long totalLogCalls;
        private final long cachedLoggerHits;
        private final long stringOptimizations;
        private final long totalStringSaved;
        private final int cachedLoggers;
        private final AsyncLogManager.AsyncLogStatistics asyncStats;

        public LoggerStatistics(long totalLogCalls, long cachedLoggerHits, long stringOptimizations,
                              long totalStringSaved, int cachedLoggers, 
                              AsyncLogManager.AsyncLogStatistics asyncStats) {
            this.totalLogCalls = totalLogCalls;
            this.cachedLoggerHits = cachedLoggerHits;
            this.stringOptimizations = stringOptimizations;
            this.totalStringSaved = totalStringSaved;
            this.cachedLoggers = cachedLoggers;
            this.asyncStats = asyncStats;
        }

        // Getters
        public long getTotalLogCalls() { return totalLogCalls; }
        public long getCachedLoggerHits() { return cachedLoggerHits; }
        public long getStringOptimizations() { return stringOptimizations; }
        public long getTotalStringSaved() { return totalStringSaved; }
        public int getCachedLoggers() { return cachedLoggers; }
        public AsyncLogManager.AsyncLogStatistics getAsyncStats() { return asyncStats; }

        public double getCacheHitRate() {
            return totalLogCalls == 0 ? 0.0 : (double) cachedLoggerHits / totalLogCalls * 100;
        }

        public double getStringOptimizationRate() {
            return totalLogCalls == 0 ? 0.0 : (double) stringOptimizations / totalLogCalls * 100;
        }

        @Override
        public String toString() {
            return String.format("Logger: calls=%d, cacheHit=%.1f%%, stringOpt=%.1f%%, " +
                            "saved=%dKB, loggers=%d, async=[%s]",
                    totalLogCalls, getCacheHitRate(), getStringOptimizationRate(),
                    totalStringSaved / 1024, cachedLoggers, asyncStats);
        }
    }
}
