package com.trading.common.logging;

import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 日志采样器
 * 防止高频日志刷屏，支持基于时间和计数的采样策略
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Slf4j
public class LogSampler {
    
    // 采样状态缓存
    private static final ConcurrentHashMap<String, SamplingState> samplingStates = new ConcurrentHashMap<>();
    
    /**
     * 采样状态
     */
    private static class SamplingState {
        private final AtomicLong counter = new AtomicLong(0);
        private volatile Instant lastLogTime = Instant.now();
        private volatile Instant firstSuppressedTime = null;
        private final AtomicLong suppressedCount = new AtomicLong(0);
        
        public boolean shouldLog(int sampleRate, Duration minInterval) {
            long count = counter.incrementAndGet();
            Instant now = Instant.now();
            
            // 基于计数的采样
            boolean countSample = (count % sampleRate == 1);
            
            // 基于时间的采样
            boolean timeSample = Duration.between(lastLogTime, now).compareTo(minInterval) >= 0;
            
            if (countSample || timeSample) {
                lastLogTime = now;
                
                // 如果有被抑制的日志，记录统计信息
                if (suppressedCount.get() > 0) {
                    return true; // 需要记录抑制统计
                }
                
                return true;
            } else {
                // 记录被抑制的日志
                if (firstSuppressedTime == null) {
                    firstSuppressedTime = now;
                }
                suppressedCount.incrementAndGet();
                return false;
            }
        }
        
        public String getSuppressedInfo() {
            long suppressed = suppressedCount.getAndSet(0);
            Instant firstTime = firstSuppressedTime;
            firstSuppressedTime = null;
            
            if (suppressed > 0 && firstTime != null) {
                Duration suppressedDuration = Duration.between(firstTime, Instant.now());
                return String.format(" [抑制了%d条类似日志，持续时间: %s]", 
                        suppressed, formatDuration(suppressedDuration));
            }
            return "";
        }
    }
    
    /**
     * 基于计数的采样日志
     * 
     * @param logger 日志记录器
     * @param key 采样键（用于区分不同的日志类型）
     * @param sampleRate 采样率（每N条记录1条）
     * @param level 日志级别
     * @param message 日志消息
     * @param args 日志参数
     */
    public static void sampleLog(Logger logger, String key, int sampleRate, LogLevel level, String message, Object... args) {
        sampleLog(logger, key, sampleRate, Duration.ofSeconds(30), level, message, args);
    }
    
    /**
     * 基于时间和计数的采样日志
     * 
     * @param logger 日志记录器
     * @param key 采样键
     * @param sampleRate 采样率
     * @param minInterval 最小时间间隔
     * @param level 日志级别
     * @param message 日志消息
     * @param args 日志参数
     */
    public static void sampleLog(Logger logger, String key, int sampleRate, Duration minInterval, 
                               LogLevel level, String message, Object... args) {
        SamplingState state = samplingStates.computeIfAbsent(key, k -> new SamplingState());
        
        if (state.shouldLog(sampleRate, minInterval)) {
            String suppressedInfo = state.getSuppressedInfo();
            String finalMessage = message + suppressedInfo;
            
            switch (level) {
                case DEBUG -> logger.debug(finalMessage, args);
                case INFO -> logger.info(finalMessage, args);
                case WARN -> logger.warn(finalMessage, args);
                case ERROR -> logger.error(finalMessage, args);
            }
        }
    }
    
    /**
     * 高频DEBUG日志采样（每100条记录1条，最小间隔1分钟）
     */
    public static void debugSample(Logger logger, String key, String message, Object... args) {
        sampleLog(logger, key, 100, Duration.ofMinutes(1), LogLevel.DEBUG, message, args);
    }
    
    /**
     * 高频INFO日志采样（每50条记录1条，最小间隔30秒）
     */
    public static void infoSample(Logger logger, String key, String message, Object... args) {
        sampleLog(logger, key, 50, Duration.ofSeconds(30), LogLevel.INFO, message, args);
    }
    
    /**
     * 高频WARN日志采样（每10条记录1条，最小间隔10秒）
     */
    public static void warnSample(Logger logger, String key, String message, Object... args) {
        sampleLog(logger, key, 10, Duration.ofSeconds(10), LogLevel.WARN, message, args);
    }
    
    /**
     * ERROR日志采样（每5条记录1条，最小间隔5秒）
     */
    public static void errorSample(Logger logger, String key, String message, Object... args) {
        sampleLog(logger, key, 5, Duration.ofSeconds(5), LogLevel.ERROR, message, args);
    }
    
    /**
     * 循环中的日志采样（高采样率，防止刷屏）
     */
    public static void loopLog(Logger logger, String key, LogLevel level, String message, Object... args) {
        sampleLog(logger, key, 1000, Duration.ofMinutes(5), level, message, args);
    }
    
    /**
     * API调用日志采样
     */
    public static void apiLog(Logger logger, String apiName, LogLevel level, String message, Object... args) {
        String key = "api-" + apiName;
        sampleLog(logger, key, 20, Duration.ofSeconds(15), level, message, args);
    }
    
    /**
     * 数据库操作日志采样
     */
    public static void dbLog(Logger logger, String operation, LogLevel level, String message, Object... args) {
        String key = "db-" + operation;
        sampleLog(logger, key, 50, Duration.ofSeconds(30), level, message, args);
    }
    
    /**
     * 缓存操作日志采样
     */
    public static void cacheLog(Logger logger, String operation, LogLevel level, String message, Object... args) {
        String key = "cache-" + operation;
        sampleLog(logger, key, 100, Duration.ofMinutes(1), level, message, args);
    }
    
    /**
     * 清理过期的采样状态（定期调用）
     */
    public static void cleanup() {
        Instant cutoff = Instant.now().minus(Duration.ofHours(1));
        samplingStates.entrySet().removeIf(entry -> {
            SamplingState state = entry.getValue();
            return Duration.between(state.lastLogTime, Instant.now()).compareTo(Duration.ofHours(1)) > 0;
        });
        
        if (log.isDebugEnabled()) {
            log.debug("日志采样器清理完成，当前状态数: {}", samplingStates.size());
        }
    }
    
    /**
     * 获取采样统计信息
     */
    public static String getStatistics() {
        StringBuilder stats = new StringBuilder();
        stats.append("日志采样统计:\n");
        stats.append(String.format("  活跃采样键数量: %d\n", samplingStates.size()));
        
        samplingStates.forEach((key, state) -> {
            stats.append(String.format("  %s: 总计数=%d, 最后记录时间=%s\n", 
                    key, state.counter.get(), state.lastLogTime));
        });
        
        return stats.toString();
    }
    
    /**
     * 格式化持续时间
     */
    private static String formatDuration(Duration duration) {
        long seconds = duration.getSeconds();
        if (seconds < 60) {
            return seconds + "秒";
        } else if (seconds < 3600) {
            return (seconds / 60) + "分" + (seconds % 60) + "秒";
        } else {
            long hours = seconds / 3600;
            long minutes = (seconds % 3600) / 60;
            return hours + "小时" + minutes + "分钟";
        }
    }
    
    /**
     * 日志级别枚举
     */
    public enum LogLevel {
        DEBUG, INFO, WARN, ERROR
    }
}
