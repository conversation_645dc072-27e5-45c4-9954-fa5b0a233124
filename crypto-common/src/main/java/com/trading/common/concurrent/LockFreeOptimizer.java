package com.trading.common.concurrent;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.atomic.LongAdder;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 无锁并发优化器
 * 提供无锁数据结构和算法，替换传统的同步机制
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class LockFreeOptimizer {

    private static final Logger log = LoggerFactory.getLogger(LockFreeOptimizer.class);

    /**
     * 无锁队列实现
     * 替换传统的同步队列
     */
    public static class LockFreeQueue<T> {
        private final ConcurrentLinkedQueue<T> queue = new ConcurrentLinkedQueue<>();
        private final LongAdder size = new LongAdder();
        private final LongAdder offerCount = new LongAdder();
        private final LongAdder pollCount = new LongAdder();

        public boolean offer(T item) {
            if (item == null) {
                return false;
            }
            boolean result = queue.offer(item);
            if (result) {
                size.increment();
                offerCount.increment();
            }
            return result;
        }

        public T poll() {
            T item = queue.poll();
            if (item != null) {
                size.decrement();
                pollCount.increment();
            }
            return item;
        }

        public boolean isEmpty() {
            return queue.isEmpty();
        }

        public long size() {
            return size.sum();
        }

        public long getOfferCount() {
            return offerCount.sum();
        }

        public long getPollCount() {
            return pollCount.sum();
        }

        public void clear() {
            queue.clear();
            size.reset();
        }
    }

    /**
     * 无锁计数器
     * 使用LongAdder替换AtomicLong，减少竞争
     */
    public static class LockFreeCounter {
        private final LongAdder counter = new LongAdder();
        private final String name;

        public LockFreeCounter(String name) {
            this.name = name;
        }

        public void increment() {
            counter.increment();
        }

        public void add(long value) {
            counter.add(value);
        }

        public long sum() {
            return counter.sum();
        }

        public void reset() {
            counter.reset();
        }

        public String getName() {
            return name;
        }

        @Override
        public String toString() {
            return String.format("Counter[%s]: %d", name, sum());
        }
    }

    /**
     * 无锁缓存更新器
     * 使用CAS操作实现无锁缓存更新
     */
    public static class LockFreeCacheUpdater<K, V> {
        private final AtomicReference<CacheEntry<K, V>> head = new AtomicReference<>();
        private final LongAdder updateCount = new LongAdder();
        private final LongAdder hitCount = new LongAdder();
        private final LongAdder missCount = new LongAdder();

        private static class CacheEntry<K, V> {
            final K key;
            final V value;
            final long timestamp;
            volatile CacheEntry<K, V> next;

            CacheEntry(K key, V value, long timestamp) {
                this.key = key;
                this.value = value;
                this.timestamp = timestamp;
            }
        }

        public void put(K key, V value) {
            long timestamp = System.currentTimeMillis();
            CacheEntry<K, V> newEntry = new CacheEntry<>(key, value, timestamp);
            
            CacheEntry<K, V> currentHead;
            do {
                currentHead = head.get();
                newEntry.next = currentHead;
            } while (!head.compareAndSet(currentHead, newEntry));
            
            updateCount.increment();
        }

        public V get(K key) {
            CacheEntry<K, V> current = head.get();
            while (current != null) {
                if (key.equals(current.key)) {
                    hitCount.increment();
                    return current.value;
                }
                current = current.next;
            }
            missCount.increment();
            return null;
        }

        public long getUpdateCount() {
            return updateCount.sum();
        }

        public long getHitCount() {
            return hitCount.sum();
        }

        public long getMissCount() {
            return missCount.sum();
        }

        public double getHitRate() {
            long hits = hitCount.sum();
            long total = hits + missCount.sum();
            return total == 0 ? 0.0 : (double) hits / total;
        }
    }

    /**
     * 无锁状态机
     * 使用CAS操作实现状态转换
     */
    public static class LockFreeStateMachine<T> {
        private final AtomicReference<T> currentState = new AtomicReference<>();
        private final LongAdder transitionCount = new LongAdder();

        public LockFreeStateMachine(T initialState) {
            currentState.set(initialState);
        }

        public boolean compareAndSet(T expect, T update) {
            boolean result = currentState.compareAndSet(expect, update);
            if (result) {
                transitionCount.increment();
            }
            return result;
        }

        public T get() {
            return currentState.get();
        }

        public void set(T newState) {
            currentState.set(newState);
            transitionCount.increment();
        }

        public long getTransitionCount() {
            return transitionCount.sum();
        }
    }

    /**
     * 无锁环形缓冲区
     * 高性能的固定大小缓冲区
     */
    public static class LockFreeRingBuffer<T> {
        private final Object[] buffer;
        private final int mask;
        private final AtomicReference<Long> writeIndex = new AtomicReference<>(0L);
        private final AtomicReference<Long> readIndex = new AtomicReference<>(0L);
        private final LongAdder writeCount = new LongAdder();
        private final LongAdder readCount = new LongAdder();

        public LockFreeRingBuffer(int capacity) {
            // 确保容量是2的幂
            int actualCapacity = 1;
            while (actualCapacity < capacity) {
                actualCapacity <<= 1;
            }
            this.buffer = new Object[actualCapacity];
            this.mask = actualCapacity - 1;
        }

        @SuppressWarnings("unchecked")
        public boolean offer(T item) {
            if (item == null) {
                return false;
            }

            long currentWrite = writeIndex.get();
            long currentRead = readIndex.get();
            
            // 检查缓冲区是否已满
            if (currentWrite - currentRead >= buffer.length) {
                return false;
            }

            int index = (int) (currentWrite & mask);
            buffer[index] = item;
            
            if (writeIndex.compareAndSet(currentWrite, currentWrite + 1)) {
                writeCount.increment();
                return true;
            }
            
            return false;
        }

        @SuppressWarnings("unchecked")
        public T poll() {
            long currentRead = readIndex.get();
            long currentWrite = writeIndex.get();
            
            // 检查缓冲区是否为空
            if (currentRead >= currentWrite) {
                return null;
            }

            int index = (int) (currentRead & mask);
            T item = (T) buffer[index];
            buffer[index] = null; // 清理引用
            
            if (readIndex.compareAndSet(currentRead, currentRead + 1)) {
                readCount.increment();
                return item;
            }
            
            return null;
        }

        public boolean isEmpty() {
            return readIndex.get() >= writeIndex.get();
        }

        public boolean isFull() {
            return writeIndex.get() - readIndex.get() >= buffer.length;
        }

        public int size() {
            return (int) (writeIndex.get() - readIndex.get());
        }

        public long getWriteCount() {
            return writeCount.sum();
        }

        public long getReadCount() {
            return readCount.sum();
        }
    }

    /**
     * 创建无锁队列
     */
    public <T> LockFreeQueue<T> createQueue() {
        return new LockFreeQueue<>();
    }

    /**
     * 创建无锁计数器
     */
    public LockFreeCounter createCounter(String name) {
        return new LockFreeCounter(name);
    }

    /**
     * 创建无锁缓存更新器
     */
    public <K, V> LockFreeCacheUpdater<K, V> createCacheUpdater() {
        return new LockFreeCacheUpdater<>();
    }

    /**
     * 创建无锁状态机
     */
    public <T> LockFreeStateMachine<T> createStateMachine(T initialState) {
        return new LockFreeStateMachine<>(initialState);
    }

    /**
     * 创建无锁环形缓冲区
     */
    public <T> LockFreeRingBuffer<T> createRingBuffer(int capacity) {
        return new LockFreeRingBuffer<>(capacity);
    }

    /**
     * 获取优化器统计信息
     */
    public String getStatistics() {
        return "LockFreeOptimizer: 提供无锁数据结构和算法，减少同步开销";
    }
}
