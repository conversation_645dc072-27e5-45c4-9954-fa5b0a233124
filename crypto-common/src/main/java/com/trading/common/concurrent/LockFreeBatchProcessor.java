package com.trading.common.concurrent;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Consumer;

/**
 * 无锁批处理器
 * 使用无锁队列和CAS操作实现高性能批处理
 *
 * @param <T> 处理的数据类型
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class LockFreeBatchProcessor<T> {

    private static final Logger log = LoggerFactory.getLogger(LockFreeBatchProcessor.class);

    private final int batchSize;
    private final long flushIntervalMs;
    private final Consumer<List<T>> batchProcessor;
    private final String processorName;

    // 无锁队列存储待处理数据
    private final ConcurrentLinkedQueue<T> queue = new ConcurrentLinkedQueue<>();
    
    // 处理状态控制
    private final AtomicBoolean processing = new AtomicBoolean(false);
    private final AtomicBoolean shutdown = new AtomicBoolean(false);
    
    // 性能统计
    private final LongAdder itemsAdded = new LongAdder();
    private final LongAdder batchesProcessed = new LongAdder();
    private final LongAdder itemsProcessed = new LongAdder();
    private final LongAdder processingErrors = new LongAdder();
    private final LongAdder queueOverflows = new LongAdder();
    
    // 定时刷新执行器
    private final ScheduledExecutorService flushExecutor;
    
    // 队列大小限制
    private final int maxQueueSize;
    private volatile long lastFlushTime = System.currentTimeMillis();

    /**
     * 构造函数
     * 
     * @param processorName 处理器名称
     * @param batchSize 批处理大小
     * @param flushIntervalMs 强制刷新间隔（毫秒）
     * @param maxQueueSize 最大队列大小
     * @param batchProcessor 批处理函数
     */
    public LockFreeBatchProcessor(String processorName, 
                                 int batchSize, 
                                 long flushIntervalMs,
                                 int maxQueueSize,
                                 Consumer<List<T>> batchProcessor) {
        this.processorName = processorName;
        this.batchSize = batchSize;
        this.flushIntervalMs = flushIntervalMs;
        this.maxQueueSize = maxQueueSize;
        this.batchProcessor = batchProcessor;
        
        // 创建定时刷新执行器
        this.flushExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread thread = new Thread(r, "batch-flush-" + processorName);
            thread.setDaemon(true);
            return thread;
        });
        
        // 启动定时刷新任务
        startFlushTask();
        
        log.info("无锁批处理器已创建: name={}, batchSize={}, flushInterval={}ms, maxQueueSize={}", 
                processorName, batchSize, flushIntervalMs, maxQueueSize);
    }

    /**
     * 添加数据到批处理队列
     * 
     * @param item 待处理数据
     * @return 是否成功添加
     */
    public boolean add(T item) {
        if (item == null || shutdown.get()) {
            return false;
        }

        // 检查队列大小限制
        if (getQueueSize() >= maxQueueSize) {
            queueOverflows.increment();
            log.warn("批处理队列已满，丢弃数据: processor={}, queueSize={}", processorName, getQueueSize());
            return false;
        }

        // 添加到无锁队列
        boolean added = queue.offer(item);
        if (added) {
            itemsAdded.increment();
            
            // 检查是否需要立即处理
            if (getQueueSize() >= batchSize) {
                tryProcessBatch();
            }
        }
        
        return added;
    }

    /**
     * 强制处理当前队列中的所有数据
     */
    public void flush() {
        if (!queue.isEmpty()) {
            tryProcessBatch();
        }
    }

    /**
     * 尝试处理批次（无锁实现）
     */
    private void tryProcessBatch() {
        // 使用CAS确保只有一个线程处理批次
        if (!processing.compareAndSet(false, true)) {
            return; // 已有其他线程在处理
        }

        try {
            processBatch();
        } finally {
            processing.set(false);
        }
    }

    /**
     * 实际处理批次
     */
    private void processBatch() {
        List<T> batch = new ArrayList<>(batchSize);
        
        // 从队列中取出数据
        int collected = 0;
        while (collected < batchSize && !queue.isEmpty()) {
            T item = queue.poll();
            if (item != null) {
                batch.add(item);
                collected++;
            }
        }

        // 处理批次
        if (!batch.isEmpty()) {
            try {
                long startTime = System.currentTimeMillis();
                batchProcessor.accept(batch);
                long processingTime = System.currentTimeMillis() - startTime;
                
                batchesProcessed.increment();
                itemsProcessed.add(batch.size());
                lastFlushTime = System.currentTimeMillis();
                
                log.debug("批处理完成: processor={}, batchSize={}, processingTime={}ms", 
                        processorName, batch.size(), processingTime);
                
            } catch (Exception e) {
                processingErrors.increment();
                log.error("批处理失败: processor={}, batchSize={}", processorName, batch.size(), e);
            }
        }
    }

    /**
     * 启动定时刷新任务
     */
    private void startFlushTask() {
        flushExecutor.scheduleAtFixedRate(() -> {
            try {
                // 检查是否需要强制刷新
                long timeSinceLastFlush = System.currentTimeMillis() - lastFlushTime;
                if (timeSinceLastFlush >= flushIntervalMs && !queue.isEmpty()) {
                    log.debug("定时刷新触发: processor={}, timeSinceLastFlush={}ms, queueSize={}", 
                            processorName, timeSinceLastFlush, getQueueSize());
                    tryProcessBatch();
                }
            } catch (Exception e) {
                log.error("定时刷新任务失败: processor={}", processorName, e);
            }
        }, flushIntervalMs, flushIntervalMs / 2, TimeUnit.MILLISECONDS);
    }

    /**
     * 获取当前队列大小
     */
    public int getQueueSize() {
        return queue.size();
    }

    /**
     * 获取处理器统计信息
     */
    public BatchProcessorStatistics getStatistics() {
        return new BatchProcessorStatistics(
                processorName,
                getQueueSize(),
                maxQueueSize,
                itemsAdded.sum(),
                batchesProcessed.sum(),
                itemsProcessed.sum(),
                processingErrors.sum(),
                queueOverflows.sum(),
                processing.get(),
                shutdown.get()
        );
    }

    /**
     * 检查处理器健康状态
     */
    public boolean isHealthy() {
        long totalItems = itemsAdded.sum();
        long errors = processingErrors.sum();
        long overflows = queueOverflows.sum();
        
        // 错误率检查
        if (totalItems > 100 && errors > totalItems * 0.1) {
            log.warn("批处理器错误率过高: processor={}, errorRate={:.2f}%", 
                    processorName, (double) errors / totalItems * 100);
            return false;
        }
        
        // 溢出率检查
        if (totalItems > 100 && overflows > totalItems * 0.05) {
            log.warn("批处理器溢出率过高: processor={}, overflowRate={:.2f}%", 
                    processorName, (double) overflows / totalItems * 100);
            return false;
        }
        
        return !shutdown.get();
    }

    /**
     * 关闭批处理器
     */
    public void shutdown() {
        if (shutdown.compareAndSet(false, true)) {
            log.info("关闭批处理器: processor={}", processorName);
            
            // 处理剩余数据
            flush();
            
            // 关闭定时任务
            flushExecutor.shutdown();
            try {
                if (!flushExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    flushExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                flushExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
            
            log.info("批处理器已关闭: processor={}, 最终统计={}", processorName, getStatistics());
        }
    }

    /**
     * 批处理器统计信息
     */
    public static class BatchProcessorStatistics {
        private final String processorName;
        private final int currentQueueSize;
        private final int maxQueueSize;
        private final long itemsAdded;
        private final long batchesProcessed;
        private final long itemsProcessed;
        private final long processingErrors;
        private final long queueOverflows;
        private final boolean processing;
        private final boolean shutdown;

        public BatchProcessorStatistics(String processorName, int currentQueueSize, int maxQueueSize,
                                      long itemsAdded, long batchesProcessed, long itemsProcessed,
                                      long processingErrors, long queueOverflows, 
                                      boolean processing, boolean shutdown) {
            this.processorName = processorName;
            this.currentQueueSize = currentQueueSize;
            this.maxQueueSize = maxQueueSize;
            this.itemsAdded = itemsAdded;
            this.batchesProcessed = batchesProcessed;
            this.itemsProcessed = itemsProcessed;
            this.processingErrors = processingErrors;
            this.queueOverflows = queueOverflows;
            this.processing = processing;
            this.shutdown = shutdown;
        }

        // Getters
        public String getProcessorName() { return processorName; }
        public int getCurrentQueueSize() { return currentQueueSize; }
        public int getMaxQueueSize() { return maxQueueSize; }
        public long getItemsAdded() { return itemsAdded; }
        public long getBatchesProcessed() { return batchesProcessed; }
        public long getItemsProcessed() { return itemsProcessed; }
        public long getProcessingErrors() { return processingErrors; }
        public long getQueueOverflows() { return queueOverflows; }
        public boolean isProcessing() { return processing; }
        public boolean isShutdown() { return shutdown; }

        public double getProcessingRate() {
            return batchesProcessed == 0 ? 0.0 : (double) itemsProcessed / batchesProcessed;
        }

        public double getErrorRate() {
            return itemsAdded == 0 ? 0.0 : (double) processingErrors / itemsAdded * 100;
        }

        @Override
        public String toString() {
            return String.format("BatchProcessor[%s]: queue=%d/%d, added=%d, processed=%d, batches=%d, errors=%d, rate=%.2f",
                    processorName, currentQueueSize, maxQueueSize, itemsAdded, itemsProcessed, 
                    batchesProcessed, processingErrors, getProcessingRate());
        }
    }
}
