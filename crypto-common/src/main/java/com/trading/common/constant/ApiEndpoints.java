package com.trading.common.constant;

/**
 * API端点常量类
 * 定义所有Binance期货API的端点路径
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public final class ApiEndpoints {
    
    private ApiEndpoints() {
        // 工具类，禁止实例化
    }
    
    // ==================== 市场数据端点 ====================
    
    /**
     * 获取服务器时间
     */
    public static final String TIME = "/v1/time";
    
    /**
     * 获取交易所信息
     */
    public static final String EXCHANGE_INFO = "/v1/exchangeInfo";
    
    /**
     * 获取订单簿深度
     */
    public static final String DEPTH = "/v1/depth";
    
    /**
     * 获取最近成交记录
     */
    public static final String TRADES = "/v1/trades";
    
    /**
     * 获取历史成交记录
     */
    public static final String HISTORICAL_TRADES = "/v1/historicalTrades";
    
    /**
     * 获取聚合成交记录
     */
    public static final String AGG_TRADES = "/v1/aggTrades";
    
    /**
     * 获取K线数据
     */
    public static final String KLINES = "/v1/klines";
    
    /**
     * 获取连续合约K线数据
     */
    public static final String CONTINUOUS_KLINES = "/v1/continuousKlines";
    
    /**
     * 获取价格指数K线数据
     */
    public static final String INDEX_PRICE_KLINES = "/v1/indexPriceKlines";
    
    /**
     * 获取标记价格K线数据
     */
    public static final String MARK_PRICE_KLINES = "/v1/markPriceKlines";
    
    /**
     * 获取24小时价格变动统计
     */
    public static final String TICKER_24HR = "/v1/ticker/24hr";
    
    /**
     * 获取最新价格
     */
    public static final String TICKER_PRICE = "/v1/ticker/price";
    
    /**
     * 获取最优挂单价格
     */
    public static final String TICKER_BOOK_TICKER = "/v1/ticker/bookTicker";
    
    /**
     * 获取所有交易对价格
     */
    public static final String TICKER_ALL_PRICES = "/v1/ticker/allPrices";
    
    /**
     * 获取所有交易对最优挂单
     */
    public static final String TICKER_ALL_BOOK_TICKERS = "/v1/ticker/allBookTickers";
    
    // ==================== 交易端点 ====================
    
    /**
     * 测试下单
     */
    public static final String ORDER_TEST = "/v1/order/test";
    
    /**
     * 下单
     */
    public static final String ORDER = "/v1/order";
    
    /**
     * 批量下单
     */
    public static final String BATCH_ORDERS = "/v1/batchOrders";
    
    /**
     * 查询订单
     */
    public static final String ORDER_QUERY = "/v1/order";
    
    /**
     * 撤销订单
     */
    public static final String ORDER_CANCEL = "/v1/order";
    
    /**
     * 撤销所有订单
     */
    public static final String ALL_OPEN_ORDERS = "/v1/allOpenOrders";
    
    /**
     * 批量撤销订单
     */
    public static final String BATCH_ORDERS_CANCEL = "/v1/batchOrders";
    
    /**
     * 倒计时撤销所有订单
     */
    public static final String COUNTDOWN_CANCEL_ALL = "/v1/countdownCancelAll";
    
    /**
     * 查询当前挂单
     */
    public static final String OPEN_ORDERS = "/v1/openOrders";
    
    /**
     * 查询所有订单
     */
    public static final String ALL_ORDERS = "/v1/allOrders";
    
    /**
     * 查询成交历史
     */
    public static final String USER_TRADES = "/v1/userTrades";
    
    // ==================== 账户端点 ====================
    
    /**
     * 获取账户信息
     */
    public static final String ACCOUNT = "/v2/account";
    
    /**
     * 获取期货账户余额
     */
    public static final String BALANCE = "/v2/balance";
    
    /**
     * 获取持仓信息
     */
    public static final String POSITION_RISK = "/v2/positionRisk";
    
    /**
     * 获取账户交易状态
     */
    public static final String TRADING_STATUS = "/v1/apiTradingStatus";
    
    /**
     * 获取佣金费率
     */
    public static final String COMMISSION_RATE = "/v1/commissionRate";
    
    /**
     * 获取收入历史
     */
    public static final String INCOME = "/v1/income";
    
    /**
     * 调整杠杆倍数
     */
    public static final String LEVERAGE = "/v1/leverage";
    
    /**
     * 变更保证金模式
     */
    public static final String MARGIN_TYPE = "/v1/marginType";
    
    /**
     * 调整逐仓保证金
     */
    public static final String POSITION_MARGIN = "/v1/positionMargin";
    
    /**
     * 获取逐仓保证金变动历史
     */
    public static final String POSITION_MARGIN_HISTORY = "/v1/positionMargin/history";
    
    /**
     * 获取持仓模式
     */
    public static final String POSITION_SIDE_DUAL = "/v1/positionSide/dual";
    
    /**
     * 更改持仓模式
     */
    public static final String POSITION_SIDE_DUAL_CHANGE = "/v1/positionSide/dual";
    
    /**
     * 获取多空持仓模式
     */
    public static final String MULTI_ASSETS_MARGIN = "/v1/multiAssetsMargin";
    
    /**
     * 更改多空持仓模式
     */
    public static final String MULTI_ASSETS_MARGIN_CHANGE = "/v1/multiAssetsMargin";
    
    // ==================== 用户数据流端点 ====================
    
    /**
     * 创建用户数据流监听密钥
     */
    public static final String LISTEN_KEY = "/v1/listenKey";
    
    // ==================== 其他端点 ====================
    
    /**
     * 获取强平订单
     */
    public static final String FORCE_ORDERS = "/v1/forceOrders";
    
    /**
     * 获取ADL队列估算
     */
    public static final String ADL_QUANTILE = "/v1/adlQuantile";
    
    /**
     * 获取用户强平历史
     */
    public static final String FORCE_ORDERS_USER = "/v1/forceOrders";
    
    /**
     * 获取标记价格
     */
    public static final String PREMIUM_INDEX = "/v1/premiumIndex";
    
    /**
     * 获取资金费率历史
     */
    public static final String FUNDING_RATE = "/v1/fundingRate";
    
    /**
     * 获取24小时成交量
     */
    public static final String TICKER_24HR_VOLUME = "/v1/ticker/24hr";
    
    /**
     * 获取持仓量
     */
    public static final String OPEN_INTEREST = "/v1/openInterest";
    
    /**
     * 获取持仓量历史
     */
    public static final String OPEN_INTEREST_HIST = "/v1/openInterestHist";
    
    /**
     * 获取大户持仓量多空比
     */
    public static final String TOP_LONG_SHORT_ACCOUNT_RATIO = "/v1/topLongShortAccountRatio";
    
    /**
     * 获取大户持仓量多空比（按持仓量）
     */
    public static final String TOP_LONG_SHORT_POSITION_RATIO = "/v1/topLongShortPositionRatio";
    
    /**
     * 获取多空持仓人数比
     */
    public static final String GLOBAL_LONG_SHORT_ACCOUNT_RATIO = "/v1/globalLongShortAccountRatio";
    
    /**
     * 获取合约主动买卖量
     */
    public static final String TAKER_LONG_SHORT_RATIO = "/v1/takerlongshortRatio";
    
    /**
     * 获取历史BLVT净值K线
     */
    public static final String LVTK_LINES = "/v1/lvtKlines";
    
    /**
     * 获取综合指数价格K线
     */
    public static final String COMPOSITE_INDEX = "/v1/compositeIndex";
}
