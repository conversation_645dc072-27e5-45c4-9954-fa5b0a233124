package com.trading.common.constant;

/**
 * 错误码常量类
 * 定义系统中所有的错误码，便于统一管理和国际化
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public final class ErrorCodes {
    
    private ErrorCodes() {
        // 防止实例化
    }
    
    // ==================== 通用错误码 (1000-1999) ====================
    
    /**
     * 成功
     */
    public static final String SUCCESS = "1000";
    
    /**
     * 系统内部错误
     */
    public static final String INTERNAL_ERROR = "1001";

    /**
     * 系统错误（别名）
     */
    public static final String SYSTEM_ERROR = "1001";

    /**
     * 业务错误
     */
    public static final String BUSINESS_ERROR = "1010";

    /**
     * 网络错误
     */
    public static final String NETWORK_ERROR = "1011";

    /**
     * 超时错误
     */
    public static final String TIMEOUT_ERROR = "1012";

    /**
     * API错误
     */
    public static final String API_ERROR = "1013";

    /**
     * 操作失败
     */
    public static final String OPERATION_FAILED = "1014";

    /**
     * 交易错误
     */
    public static final String TRADE_ERROR = "1015";

    /**
     * 无效交易对
     */
    public static final String INVALID_SYMBOL = "1016";

    /**
     * 订单失败
     */
    public static final String ORDER_FAILED = "1017";

    /**
     * 频率限制超出
     */
    public static final String RATE_LIMIT_EXCEEDED = "1018";

    /**
     * 未授权
     */
    public static final String UNAUTHORIZED = "1019";

    /**
     * API超时
     */
    public static final String API_TIMEOUT = "1020";
    
    /**
     * 参数错误
     */
    public static final String INVALID_PARAMETER = "1002";
    
    /**
     * 参数缺失
     */
    public static final String MISSING_PARAMETER = "1003";
    
    /**
     * 数据不存在
     */
    public static final String DATA_NOT_FOUND = "1004";
    
    /**
     * 数据已存在
     */
    public static final String DATA_ALREADY_EXISTS = "1005";
    
    /**
     * 操作不允许
     */
    public static final String OPERATION_NOT_ALLOWED = "1006";
    
    /**
     * 状态不正确
     */
    public static final String INVALID_STATE = "1007";
    
    /**
     * 权限不足
     */
    public static final String ACCESS_DENIED = "1008";
    
    /**
     * 请求超时
     */
    public static final String REQUEST_TIMEOUT = "1009";
    
    // ==================== 认证相关错误码 (2000-2999) ====================
    
    /**
     * 认证失败
     */
    public static final String AUTHENTICATION_FAILED = "2001";
    
    /**
     * 令牌无效
     */
    public static final String INVALID_TOKEN = "2002";
    
    /**
     * 令牌过期
     */
    public static final String TOKEN_EXPIRED = "2003";
    
    /**
     * API密钥无效
     */
    public static final String INVALID_API_KEY = "2004";
    
    /**
     * 签名验证失败
     */
    public static final String SIGNATURE_VERIFICATION_FAILED = "2005";
    
    /**
     * 时间戳过期
     */
    public static final String TIMESTAMP_EXPIRED = "2006";
    
    // ==================== 交易相关错误码 (3000-3999) ====================
    
    /**
     * 余额不足
     */
    public static final String INSUFFICIENT_BALANCE = "3001";
    
    /**
     * 订单不存在
     */
    public static final String ORDER_NOT_FOUND = "3002";
    
    /**
     * 订单已取消
     */
    public static final String ORDER_CANCELED = "3003";
    
    /**
     * 订单已成交
     */
    public static final String ORDER_FILLED = "3004";
    
    /**
     * 价格超出范围
     */
    public static final String PRICE_OUT_OF_RANGE = "3005";
    
    /**
     * 数量超出范围
     */
    public static final String QUANTITY_OUT_OF_RANGE = "3006";
    
    /**
     * 交易对不支持
     */
    public static final String SYMBOL_NOT_SUPPORTED = "3007";
    
    /**
     * 市场关闭
     */
    public static final String MARKET_CLOSED = "3008";
    
    /**
     * 订单类型不支持
     */
    public static final String ORDER_TYPE_NOT_SUPPORTED = "3009";
    
    /**
     * 杠杆倍数无效
     */
    public static final String INVALID_LEVERAGE = "3010";
    
    /**
     * 保证金不足
     */
    public static final String INSUFFICIENT_MARGIN = "3011";
    
    /**
     * 持仓不存在
     */
    public static final String POSITION_NOT_FOUND = "3012";
    
    /**
     * 风控拒绝
     */
    public static final String RISK_CONTROL_REJECTED = "3013";
    
    /**
     * 交易频率限制
     */
    public static final String TRADE_RATE_LIMIT = "3014";
    
    /**
     * 最小交易金额限制
     */
    public static final String MIN_NOTIONAL_LIMIT = "3015";
    
    // ==================== API相关错误码 (4000-4999) ====================
    
    /**
     * API请求频率限制
     */
    public static final String API_RATE_LIMIT = "4001";
    
    /**
     * API服务不可用
     */
    public static final String API_SERVICE_UNAVAILABLE = "4002";
    
    /**
     * API响应格式错误
     */
    public static final String API_RESPONSE_FORMAT_ERROR = "4003";
    
    /**
     * API连接超时
     */
    public static final String API_CONNECTION_TIMEOUT = "4004";
    
    /**
     * API读取超时
     */
    public static final String API_READ_TIMEOUT = "4005";
    
    /**
     * WebSocket连接失败
     */
    public static final String WEBSOCKET_CONNECTION_FAILED = "4006";
    
    /**
     * WebSocket连接断开
     */
    public static final String WEBSOCKET_CONNECTION_LOST = "4007";
    
    /**
     * 币安API错误
     */
    public static final String BINANCE_API_ERROR = "4008";
    
    /**
     * 币安服务器错误
     */
    public static final String BINANCE_SERVER_ERROR = "4009";
    
    /**
     * 币安连接器错误
     */
    public static final String BINANCE_CONNECTOR_ERROR = "4010";
    
    // ==================== 数据相关错误码 (5000-5999) ====================
    
    /**
     * 数据库连接失败
     */
    public static final String DATABASE_CONNECTION_FAILED = "5001";
    
    /**
     * 数据库操作失败
     */
    public static final String DATABASE_OPERATION_FAILED = "5002";
    
    /**
     * 数据序列化失败
     */
    public static final String DATA_SERIALIZATION_FAILED = "5003";
    
    /**
     * 数据反序列化失败
     */
    public static final String DATA_DESERIALIZATION_FAILED = "5004";
    
    /**
     * 缓存操作失败
     */
    public static final String CACHE_OPERATION_FAILED = "5005";
    
    /**
     * 消息队列连接失败
     */
    public static final String MESSAGE_QUEUE_CONNECTION_FAILED = "5006";
    
    /**
     * 消息发送失败
     */
    public static final String MESSAGE_SEND_FAILED = "5007";
    
    /**
     * 消息消费失败
     */
    public static final String MESSAGE_CONSUME_FAILED = "5008";
    
    /**
     * 数据验证失败
     */
    public static final String DATA_VALIDATION_FAILED = "5009";
    
    /**
     * 数据格式错误
     */
    public static final String DATA_FORMAT_ERROR = "5010";
    
    // ==================== 策略相关错误码 (6000-6999) ====================
    
    /**
     * 策略不存在
     */
    public static final String STRATEGY_NOT_FOUND = "6001";
    
    /**
     * 策略已存在
     */
    public static final String STRATEGY_ALREADY_EXISTS = "6002";
    
    /**
     * 策略配置错误
     */
    public static final String STRATEGY_CONFIG_ERROR = "6003";
    
    /**
     * 策略执行失败
     */
    public static final String STRATEGY_EXECUTION_FAILED = "6004";
    
    /**
     * 策略状态错误
     */
    public static final String STRATEGY_STATE_ERROR = "6005";
    
    /**
     * 模型加载失败
     */
    public static final String MODEL_LOAD_FAILED = "6006";
    
    /**
     * 模型预测失败
     */
    public static final String MODEL_PREDICTION_FAILED = "6007";
    
    /**
     * 特征计算失败
     */
    public static final String FEATURE_CALCULATION_FAILED = "6008";
    
    /**
     * 信号生成失败
     */
    public static final String SIGNAL_GENERATION_FAILED = "6009";
    
    /**
     * 回测失败
     */
    public static final String BACKTEST_FAILED = "6010";
    
    // ==================== 系统相关错误码 (7000-7999) ====================
    
    /**
     * 配置加载失败
     */
    public static final String CONFIG_LOAD_FAILED = "7001";
    
    /**
     * 组件初始化失败
     */
    public static final String COMPONENT_INIT_FAILED = "7002";
    
    /**
     * 服务启动失败
     */
    public static final String SERVICE_START_FAILED = "7003";
    
    /**
     * 服务停止失败
     */
    public static final String SERVICE_STOP_FAILED = "7004";
    
    /**
     * 资源不可用
     */
    public static final String RESOURCE_UNAVAILABLE = "7005";
    
    /**
     * 线程池满
     */
    public static final String THREAD_POOL_FULL = "7006";
    
    /**
     * 内存不足
     */
    public static final String OUT_OF_MEMORY = "7007";
    
    /**
     * 磁盘空间不足
     */
    public static final String DISK_SPACE_INSUFFICIENT = "7008";
    
    /**
     * 网络连接失败
     */
    public static final String NETWORK_CONNECTION_FAILED = "7009";
    
    /**
     * 健康检查失败
     */
    public static final String HEALTH_CHECK_FAILED = "7010";
    
    // ==================== 监控相关错误码 (8000-8999) ====================
    
    /**
     * 指标收集失败
     */
    public static final String METRICS_COLLECTION_FAILED = "8001";
    
    /**
     * 告警发送失败
     */
    public static final String ALERT_SEND_FAILED = "8002";
    
    /**
     * 日志写入失败
     */
    public static final String LOG_WRITE_FAILED = "8003";
    
    /**
     * 性能监控失败
     */
    public static final String PERFORMANCE_MONITORING_FAILED = "8004";
    
    /**
     * 链路追踪失败
     */
    public static final String TRACE_FAILED = "8005";
}
