package com.trading.common.constant;

/**
 * API限流常量类
 * 定义币安API的限流规则和限制参数
 * 基于币安官方文档的限流规则
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public final class ApiLimitConstants {
    
    private ApiLimitConstants() {
        // 防止实例化
    }
    
    // ==================== 请求权重限制 ====================
    
    /**
     * 每分钟请求权重限制（USD-M期货）
     */
    public static final int USDM_REQUEST_WEIGHT_PER_MINUTE = 2400;
    
    /**
     * 每分钟请求权重限制（COIN-M期货）
     */
    public static final int COINM_REQUEST_WEIGHT_PER_MINUTE = 2400;
    
    /**
     * 每分钟请求权重限制（现货）
     */
    public static final int SPOT_REQUEST_WEIGHT_PER_MINUTE = 6000;
    
    /**
     * 每秒请求权重限制（USD-M期货）
     */
    public static final int USDM_REQUEST_WEIGHT_PER_SECOND = 40;
    
    /**
     * 每秒请求权重限制（COIN-M期货）
     */
    public static final int COINM_REQUEST_WEIGHT_PER_SECOND = 40;
    
    /**
     * 每秒请求权重限制（现货）
     */
    public static final int SPOT_REQUEST_WEIGHT_PER_SECOND = 100;
    
    // ==================== 订单频率限制 ====================

    /**
     * 每10秒订单权重限制（通用）
     */
    public static final int ORDER_REQUEST_WEIGHT_PER_10_SECONDS = 300;

    /**
     * 每分钟订单权重限制（通用）
     */
    public static final int ORDER_REQUEST_WEIGHT_PER_MINUTE = 1200;

    /**
     * 每10秒订单数限制（USD-M期货）
     */
    public static final int USDM_ORDERS_PER_10_SECONDS = 300;

    /**
     * 每分钟订单数限制（USD-M期货）
     */
    public static final int USDM_ORDERS_PER_MINUTE = 1200;
    
    /**
     * 每10秒订单数限制（COIN-M期货）
     */
    public static final int COINM_ORDERS_PER_10_SECONDS = 300;
    
    /**
     * 每分钟订单数限制（COIN-M期货）
     */
    public static final int COINM_ORDERS_PER_MINUTE = 1200;
    
    /**
     * 每秒订单数限制（现货）
     */
    public static final int SPOT_ORDERS_PER_SECOND = 10;
    
    /**
     * 每天订单数限制（现货）
     */
    public static final int SPOT_ORDERS_PER_DAY = 200000;
    
    // ==================== WebSocket连接限制 ====================
    
    /**
     * 每个IP最大WebSocket连接数
     */
    public static final int MAX_WEBSOCKET_CONNECTIONS_PER_IP = 5;
    
    /**
     * 每个连接最大订阅数
     */
    public static final int MAX_SUBSCRIPTIONS_PER_CONNECTION = 1024;
    
    /**
     * WebSocket连接超时时间（毫秒）
     */
    public static final long WEBSOCKET_CONNECTION_TIMEOUT = 60000;
    
    /**
     * WebSocket心跳间隔（毫秒）
     */
    public static final long WEBSOCKET_PING_INTERVAL = 180000;
    
    // ==================== 请求权重值 ====================
    
    /**
     * 获取服务器时间权重
     */
    public static final int WEIGHT_SERVER_TIME = 1;
    
    /**
     * 获取交易规则权重
     */
    public static final int WEIGHT_EXCHANGE_INFO = 10;
    
    /**
     * 获取深度信息权重（默认）
     */
    public static final int WEIGHT_ORDER_BOOK = 2;
    
    /**
     * 获取深度信息权重（limit=500）
     */
    public static final int WEIGHT_ORDER_BOOK_500 = 5;
    
    /**
     * 获取深度信息权重（limit=1000）
     */
    public static final int WEIGHT_ORDER_BOOK_1000 = 10;
    
    /**
     * 获取深度信息权重（limit=5000）
     */
    public static final int WEIGHT_ORDER_BOOK_5000 = 50;
    
    /**
     * 获取最近成交权重
     */
    public static final int WEIGHT_RECENT_TRADES = 1;
    
    /**
     * 获取历史成交权重
     */
    public static final int WEIGHT_HISTORICAL_TRADES = 5;
    
    /**
     * 获取聚合成交权重
     */
    public static final int WEIGHT_AGG_TRADES = 1;
    
    /**
     * 获取K线数据权重
     */
    public static final int WEIGHT_KLINES = 1;
    
    /**
     * 获取连续合约K线权重
     */
    public static final int WEIGHT_CONTINUOUS_KLINES = 1;
    
    /**
     * 获取指数价格K线权重
     */
    public static final int WEIGHT_INDEX_PRICE_KLINES = 1;
    
    /**
     * 获取标记价格K线权重
     */
    public static final int WEIGHT_MARK_PRICE_KLINES = 1;
    
    /**
     * 获取24小时价格变动统计权重
     */
    public static final int WEIGHT_24HR_TICKER = 1;
    
    /**
     * 获取价格权重
     */
    public static final int WEIGHT_SYMBOL_PRICE_TICKER = 1;
    
    /**
     * 获取最优挂单价格权重
     */
    public static final int WEIGHT_SYMBOL_ORDER_BOOK_TICKER = 1;
    
    // ==================== 订单相关权重 ====================
    
    /**
     * 下单权重
     */
    public static final int WEIGHT_NEW_ORDER = 1;
    
    /**
     * 批量下单权重（每个订单）
     */
    public static final int WEIGHT_BATCH_ORDERS_PER_ORDER = 1;
    
    /**
     * 查询订单权重
     */
    public static final int WEIGHT_QUERY_ORDER = 1;
    
    /**
     * 撤销订单权重
     */
    public static final int WEIGHT_CANCEL_ORDER = 1;
    
    /**
     * 撤销所有订单权重
     */
    public static final int WEIGHT_CANCEL_ALL_ORDERS = 1;
    
    /**
     * 批量撤销订单权重
     */
    public static final int WEIGHT_CANCEL_BATCH_ORDERS = 1;
    
    /**
     * 查询当前挂单权重
     */
    public static final int WEIGHT_OPEN_ORDERS = 1;
    
    /**
     * 查询所有订单权重
     */
    public static final int WEIGHT_ALL_ORDERS = 5;
    
    // ==================== 账户相关权重 ====================
    
    /**
     * 获取账户信息权重
     */
    public static final int WEIGHT_ACCOUNT = 5;
    
    /**
     * 获取账户余额权重
     */
    public static final int WEIGHT_BALANCE = 5;
    
    /**
     * 获取持仓信息权重
     */
    public static final int WEIGHT_POSITION_RISK = 5;
    
    /**
     * 获取成交历史权重
     */
    public static final int WEIGHT_USER_TRADES = 5;
    
    /**
     * 获取收入历史权重
     */
    public static final int WEIGHT_INCOME = 30;
    
    /**
     * 调整杠杆权重
     */
    public static final int WEIGHT_LEVERAGE = 1;
    
    /**
     * 变更保证金模式权重
     */
    public static final int WEIGHT_MARGIN_TYPE = 1;
    
    /**
     * 调整逐仓保证金权重
     */
    public static final int WEIGHT_POSITION_MARGIN = 1;
    
    /**
     * 变更持仓模式权重
     */
    public static final int WEIGHT_POSITION_SIDE_DUAL = 1;
    
    // ==================== 限流检查方法 ====================
    
    /**
     * 检查请求权重是否超限
     * 
     * @param currentWeight 当前权重
     * @param maxWeight 最大权重
     * @return 如果超限返回true，否则返回false
     */
    public static boolean isWeightExceeded(int currentWeight, int maxWeight) {
        return currentWeight > maxWeight;
    }
    
    /**
     * 计算权重使用率
     * 
     * @param currentWeight 当前权重
     * @param maxWeight 最大权重
     * @return 权重使用率（0-1之间）
     */
    public static double calculateWeightUsage(int currentWeight, int maxWeight) {
        if (maxWeight <= 0) {
            return 0.0;
        }
        return Math.min(1.0, (double) currentWeight / maxWeight);
    }
    
    /**
     * 检查是否需要限流
     * 
     * @param currentWeight 当前权重
     * @param maxWeight 最大权重
     * @param threshold 阈值（0-1之间）
     * @return 如果需要限流返回true，否则返回false
     */
    public static boolean shouldThrottle(int currentWeight, int maxWeight, double threshold) {
        return calculateWeightUsage(currentWeight, maxWeight) >= threshold;
    }
}
