package com.trading.common.constant;

/**
 * 交易常量类
 * 定义交易相关的常量，包括交易对、订单类型、时间间隔等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public final class TradeConstants {
    
    private TradeConstants() {
        // 防止实例化
    }
    
    // ==================== 主要交易对常量 ====================
    
    /**
     * BTC/USDT交易对
     */
    public static final String SYMBOL_BTCUSDT = "BTCUSDT";
    
    /**
     * ETH/USDT交易对
     */
    public static final String SYMBOL_ETHUSDT = "ETHUSDT";
    
    /**
     * BNB/USDT交易对
     */
    public static final String SYMBOL_BNBUSDT = "BNBUSDT";
    
    /**
     * ADA/USDT交易对
     */
    public static final String SYMBOL_ADAUSDT = "ADAUSDT";
    
    /**
     * SOL/USDT交易对
     */
    public static final String SYMBOL_SOLUSDT = "SOLUSDT";
    
    // ==================== 订单类型常量 ====================
    
    /**
     * 市价单
     */
    public static final String ORDER_TYPE_MARKET = "MARKET";
    
    /**
     * 限价单
     */
    public static final String ORDER_TYPE_LIMIT = "LIMIT";
    
    /**
     * 止损限价单
     */
    public static final String ORDER_TYPE_STOP_LOSS_LIMIT = "STOP_LOSS_LIMIT";
    
    /**
     * 止盈限价单
     */
    public static final String ORDER_TYPE_TAKE_PROFIT_LIMIT = "TAKE_PROFIT_LIMIT";
    
    /**
     * 限价做市单
     */
    public static final String ORDER_TYPE_LIMIT_MAKER = "LIMIT_MAKER";
    
    // ==================== 订单方向常量 ====================
    
    /**
     * 买入
     */
    public static final String SIDE_BUY = "BUY";
    
    /**
     * 卖出
     */
    public static final String SIDE_SELL = "SELL";
    
    // ==================== 持仓方向常量 ====================
    
    /**
     * 多头持仓
     */
    public static final String POSITION_SIDE_LONG = "LONG";
    
    /**
     * 空头持仓
     */
    public static final String POSITION_SIDE_SHORT = "SHORT";
    
    /**
     * 双向持仓
     */
    public static final String POSITION_SIDE_BOTH = "BOTH";
    
    // ==================== 时间有效性常量 ====================
    
    /**
     * 成交为止
     */
    public static final String TIME_IN_FORCE_GTC = "GTC";
    
    /**
     * 立即成交或取消
     */
    public static final String TIME_IN_FORCE_IOC = "IOC";
    
    /**
     * 全部成交或取消
     */
    public static final String TIME_IN_FORCE_FOK = "FOK";
    
    /**
     * 当日有效
     */
    public static final String TIME_IN_FORCE_GTD = "GTD";
    
    // ==================== 订单状态常量 ====================
    
    /**
     * 新建订单
     */
    public static final String ORDER_STATUS_NEW = "NEW";
    
    /**
     * 部分成交
     */
    public static final String ORDER_STATUS_PARTIALLY_FILLED = "PARTIALLY_FILLED";
    
    /**
     * 完全成交
     */
    public static final String ORDER_STATUS_FILLED = "FILLED";
    
    /**
     * 已取消
     */
    public static final String ORDER_STATUS_CANCELED = "CANCELED";
    
    /**
     * 拒绝
     */
    public static final String ORDER_STATUS_REJECTED = "REJECTED";
    
    /**
     * 过期
     */
    public static final String ORDER_STATUS_EXPIRED = "EXPIRED";
    
    // ==================== K线时间间隔常量 ====================
    
    /**
     * 1分钟
     */
    public static final String INTERVAL_1M = "1m";
    
    /**
     * 3分钟
     */
    public static final String INTERVAL_3M = "3m";
    
    /**
     * 5分钟
     */
    public static final String INTERVAL_5M = "5m";
    
    /**
     * 15分钟
     */
    public static final String INTERVAL_15M = "15m";
    
    /**
     * 30分钟
     */
    public static final String INTERVAL_30M = "30m";
    
    /**
     * 1小时
     */
    public static final String INTERVAL_1H = "1h";
    
    /**
     * 2小时
     */
    public static final String INTERVAL_2H = "2h";
    
    /**
     * 4小时
     */
    public static final String INTERVAL_4H = "4h";
    
    /**
     * 6小时
     */
    public static final String INTERVAL_6H = "6h";
    
    /**
     * 8小时
     */
    public static final String INTERVAL_8H = "8h";
    
    /**
     * 12小时
     */
    public static final String INTERVAL_12H = "12h";
    
    /**
     * 1天
     */
    public static final String INTERVAL_1D = "1d";
    
    /**
     * 3天
     */
    public static final String INTERVAL_3D = "3d";
    
    /**
     * 1周
     */
    public static final String INTERVAL_1W = "1w";
    
    /**
     * 1月
     */
    public static final String INTERVAL_1M_MONTH = "1M";
    
    // ==================== 交易模式常量 ====================
    
    /**
     * 现货交易
     */
    public static final String TRADE_MODE_SPOT = "SPOT";
    
    /**
     * 期货交易
     */
    public static final String TRADE_MODE_FUTURES = "FUTURES";
    
    /**
     * 杠杆交易
     */
    public static final String TRADE_MODE_MARGIN = "MARGIN";
    
    // ==================== 保证金模式常量 ====================
    
    /**
     * 逐仓模式
     */
    public static final String MARGIN_TYPE_ISOLATED = "ISOLATED";
    
    /**
     * 全仓模式
     */
    public static final String MARGIN_TYPE_CROSSED = "CROSSED";
    
    // ==================== 数量限制常量 ====================
    
    /**
     * 最小交易数量
     */
    public static final String MIN_QUANTITY = "0.00000001";
    
    /**
     * 最大交易数量
     */
    public static final String MAX_QUANTITY = "9000000000";
    
    /**
     * 最小价格
     */
    public static final String MIN_PRICE = "0.00000001";
    
    /**
     * 最大价格
     */
    public static final String MAX_PRICE = "1000000";
    
    // ==================== 手续费相关常量 ====================
    
    /**
     * 默认手续费率（0.1%）
     */
    public static final String DEFAULT_FEE_RATE = "0.001";
    
    /**
     * VIP手续费率（0.075%）
     */
    public static final String VIP_FEE_RATE = "0.00075";
    
    /**
     * 做市商手续费率（0.02%）
     */
    public static final String MAKER_FEE_RATE = "0.0002";
    
    /**
     * 吃单手续费率（0.04%）
     */
    public static final String TAKER_FEE_RATE = "0.0004";
    
    // ==================== 风控相关常量 ====================
    
    /**
     * 最大杠杆倍数
     */
    public static final int MAX_LEVERAGE = 125;
    
    /**
     * 默认杠杆倍数
     */
    public static final int DEFAULT_LEVERAGE = 20;
    
    /**
     * 最大持仓比例（账户资金的百分比）
     */
    public static final String MAX_POSITION_RATIO = "0.8";
    
    /**
     * 最大单笔交易金额比例
     */
    public static final String MAX_SINGLE_TRADE_RATIO = "0.1";
    
    /**
     * 止损比例
     */
    public static final String STOP_LOSS_RATIO = "0.05";
    
    /**
     * 止盈比例
     */
    public static final String TAKE_PROFIT_RATIO = "0.1";
    
    // ==================== 策略相关常量 ====================
    
    /**
     * 默认策略周期（分钟）
     */
    public static final int DEFAULT_STRATEGY_PERIOD = 5;
    
    /**
     * 最小策略周期（分钟）
     */
    public static final int MIN_STRATEGY_PERIOD = 1;
    
    /**
     * 最大策略周期（分钟）
     */
    public static final int MAX_STRATEGY_PERIOD = 1440;
    
    /**
     * 默认回测天数
     */
    public static final int DEFAULT_BACKTEST_DAYS = 30;
}
