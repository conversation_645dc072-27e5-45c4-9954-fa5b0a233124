package com.trading.common.constant;

/**
 * 系统常量类
 * 定义系统级别的常量，包括配置键、默认值、系统参数等
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
public final class SystemConstants {
    
    private SystemConstants() {
        // 防止实例化
    }
    
    // ==================== 系统基础常量 ====================
    
    /**
     * 系统名称
     */
    public static final String SYSTEM_NAME = "Crypto Trading System";
    
    /**
     * 系统版本
     */
    public static final String SYSTEM_VERSION = "1.0.0";
    
    /**
     * 系统编码
     */
    public static final String SYSTEM_ENCODING = "UTF-8";
    
    /**
     * 默认时区
     */
    public static final String DEFAULT_TIMEZONE = "UTC";
    
    /**
     * 中国时区
     */
    public static final String CHINA_TIMEZONE = "Asia/Shanghai";
    
    // ==================== 日期时间格式常量 ====================
    
    /**
     * 标准日期时间格式
     */
    public static final String DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss";
    
    /**
     * ISO日期时间格式
     */
    public static final String ISO_DATETIME_FORMAT = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'";
    
    /**
     * 日期格式
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";
    
    /**
     * 时间格式
     */
    public static final String TIME_FORMAT = "HH:mm:ss";
    
    /**
     * 时间戳格式
     */
    public static final String TIMESTAMP_FORMAT = "yyyyMMddHHmmssSSS";
    
    // ==================== 数值精度常量 ====================

    /**
     * 默认精度（小数位数）
     */
    public static final int DEFAULT_PRECISION_SCALE = 8;

    /**
     * 价格精度（小数位数）
     */
    public static final int PRICE_PRECISION_SCALE = 8;

    /**
     * 数量精度（小数位数）
     */
    public static final int QUANTITY_PRECISION_SCALE = 8;
    
    /**
     * 百分比精度（小数位数）
     */
    public static final int PERCENTAGE_SCALE = 4;
    
    /**
     * 金额精度（小数位数）
     */
    public static final int AMOUNT_SCALE = 8;
    
    // ==================== 缓存相关常量 ====================
    
    /**
     * 默认缓存过期时间（秒）
     */
    public static final long DEFAULT_CACHE_EXPIRE_SECONDS = 300L;
    
    /**
     * 短期缓存过期时间（秒）
     */
    public static final long SHORT_CACHE_EXPIRE_SECONDS = 60L;
    
    /**
     * 长期缓存过期时间（秒）
     */
    public static final long LONG_CACHE_EXPIRE_SECONDS = 3600L;
    
    // ==================== 线程池相关常量 ====================
    
    /**
     * 默认核心线程数
     */
    public static final int DEFAULT_CORE_POOL_SIZE = 10;
    
    /**
     * 默认最大线程数
     */
    public static final int DEFAULT_MAX_POOL_SIZE = 50;
    
    /**
     * 默认队列容量
     */
    public static final int DEFAULT_QUEUE_CAPACITY = 1000;
    
    /**
     * 默认线程存活时间（秒）
     */
    public static final long DEFAULT_KEEP_ALIVE_SECONDS = 60L;
    
    // ==================== 网络相关常量 ====================
    
    /**
     * 默认连接超时时间（毫秒）
     */
    public static final int DEFAULT_CONNECT_TIMEOUT_MS = 10000;
    
    /**
     * 默认读取超时时间（毫秒）
     */
    public static final int DEFAULT_READ_TIMEOUT_MS = 30000;
    
    /**
     * 默认写入超时时间（毫秒）
     */
    public static final int DEFAULT_WRITE_TIMEOUT_MS = 30000;
    
    /**
     * 默认重试次数
     */
    public static final int DEFAULT_RETRY_COUNT = 3;
    
    /**
     * 默认重试间隔（毫秒）
     */
    public static final long DEFAULT_RETRY_INTERVAL_MS = 1000L;
    
    // ==================== 分页相关常量 ====================
    
    /**
     * 默认页码
     */
    public static final int DEFAULT_PAGE_NUM = 1;
    
    /**
     * 默认页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 20;
    
    /**
     * 最大页大小
     */
    public static final int MAX_PAGE_SIZE = 1000;
    
    // ==================== 配置键常量 ====================
    
    /**
     * 应用名称配置键
     */
    public static final String CONFIG_APP_NAME = "spring.application.name";
    
    /**
     * 环境配置键
     */
    public static final String CONFIG_PROFILE_ACTIVE = "spring.profiles.active";
    
    /**
     * 服务器端口配置键
     */
    public static final String CONFIG_SERVER_PORT = "server.port";
    
    // ==================== 环境常量 ====================
    
    /**
     * 开发环境
     */
    public static final String ENV_DEV = "dev";
    
    /**
     * 测试环境
     */
    public static final String ENV_TEST = "test";
    
    /**
     * 生产环境
     */
    public static final String ENV_PROD = "prod";
    
    // ==================== 响应状态常量 ====================
    
    /**
     * 成功状态码
     */
    public static final String SUCCESS_CODE = "200";
    
    /**
     * 成功消息
     */
    public static final String SUCCESS_MESSAGE = "Success";
    
    /**
     * 失败状态码
     */
    public static final String FAIL_CODE = "500";
    
    /**
     * 失败消息
     */
    public static final String FAIL_MESSAGE = "Internal Server Error";
    
    // ==================== 字符常量 ====================
    
    /**
     * 逗号分隔符
     */
    public static final String COMMA = ",";
    
    /**
     * 分号分隔符
     */
    public static final String SEMICOLON = ";";
    
    /**
     * 冒号分隔符
     */
    public static final String COLON = ":";
    
    /**
     * 下划线
     */
    public static final String UNDERSCORE = "_";
    
    /**
     * 横线
     */
    public static final String DASH = "-";
    
    /**
     * 点号
     */
    public static final String DOT = ".";
    
    /**
     * 空字符串
     */
    public static final String EMPTY_STRING = "";
    
    /**
     * 空格
     */
    public static final String SPACE = " ";
    
    // ==================== 系统性能常量 ====================
    
    /**
     * 最大延迟要求（毫秒）
     */
    public static final long MAX_LATENCY_MS = 100L;
    
    /**
     * 系统可用性要求（百分比）
     */
    public static final double SYSTEM_AVAILABILITY = 99.9;
    
    /**
     * 虚拟线程启用标志
     */
    public static final boolean VIRTUAL_THREADS_ENABLED = true;
}
