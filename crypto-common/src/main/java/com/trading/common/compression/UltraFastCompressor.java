package com.trading.common.compression;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.io.*;
import java.nio.ByteBuffer;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.zip.*;

/**
 * 超高性能数据压缩器
 * 使用多种压缩算法和智能选择策略
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class UltraFastCompressor {
    
    private static final Logger log = LoggerFactory.getLogger(UltraFastCompressor.class);
    
    // 压缩算法枚举
    public enum CompressionAlgorithm {
        GZIP,           // 通用压缩，平衡压缩率和速度
        DEFLATE,        // 快速压缩
        LZ4,            // 超快速压缩（需要额外库）
        SNAPPY,         // Google的快速压缩（需要额外库）
        BROTLI,         // 高压缩率（需要额外库）
        ADAPTIVE        // 自适应选择
    }
    
    // 压缩级别
    public static final int COMPRESSION_LEVEL_FASTEST = 1;
    public static final int COMPRESSION_LEVEL_BALANCED = 6;
    public static final int COMPRESSION_LEVEL_BEST = 9;
    
    // 缓存压缩结果
    private final ConcurrentHashMap<String, CompressedData> compressionCache = new ConcurrentHashMap<>();
    
    // 性能统计
    private final LongAdder compressionCount = new LongAdder();
    private final LongAdder decompressionCount = new LongAdder();
    private final AtomicLong totalCompressionTime = new AtomicLong(0);
    private final AtomicLong totalDecompressionTime = new AtomicLong(0);
    private final AtomicLong totalOriginalSize = new AtomicLong(0);
    private final AtomicLong totalCompressedSize = new AtomicLong(0);
    private final LongAdder cacheHits = new LongAdder();
    private final LongAdder cacheMisses = new LongAdder();
    
    // 算法性能统计
    private final ConcurrentHashMap<CompressionAlgorithm, AlgorithmStats> algorithmStats = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initialize() {
        // 初始化算法统计
        for (CompressionAlgorithm algorithm : CompressionAlgorithm.values()) {
            algorithmStats.put(algorithm, new AlgorithmStats());
        }
        
        log.info("超高性能数据压缩器已初始化");
    }
    
    /**
     * 压缩数据
     */
    public CompressedData compress(byte[] data) {
        return compress(data, CompressionAlgorithm.ADAPTIVE, COMPRESSION_LEVEL_BALANCED);
    }
    
    /**
     * 压缩数据（指定算法和级别）
     */
    public CompressedData compress(byte[] data, CompressionAlgorithm algorithm, int level) {
        if (data == null || data.length == 0) {
            return new CompressedData(new byte[0], algorithm, 0, 0);
        }
        
        long startTime = System.nanoTime();
        
        try {
            // 检查缓存
            String cacheKey = generateCacheKey(data, algorithm, level);
            CompressedData cached = compressionCache.get(cacheKey);
            if (cached != null) {
                cacheHits.increment();
                return cached;
            }
            
            cacheMisses.increment();
            
            // 选择最优算法
            CompressionAlgorithm selectedAlgorithm = algorithm == CompressionAlgorithm.ADAPTIVE ? 
                selectOptimalAlgorithm(data) : algorithm;
            
            // 执行压缩
            byte[] compressed = performCompression(data, selectedAlgorithm, level);
            
            // 创建压缩结果
            CompressedData result = new CompressedData(compressed, selectedAlgorithm, data.length, compressed.length);
            
            // 缓存结果（如果压缩效果好）
            if (result.getCompressionRatio() > 0.2) { // 压缩率超过20%
                compressionCache.put(cacheKey, result);
            }
            
            // 更新统计
            updateCompressionStats(selectedAlgorithm, data.length, compressed.length, startTime);
            
            return result;
            
        } catch (Exception e) {
            log.error("数据压缩失败", e);
            throw new RuntimeException("压缩失败", e);
        }
    }
    
    /**
     * 解压数据
     */
    public byte[] decompress(CompressedData compressedData) {
        if (compressedData == null || compressedData.getData().length == 0) {
            return new byte[0];
        }
        
        long startTime = System.nanoTime();
        
        try {
            byte[] decompressed = performDecompression(compressedData.getData(), compressedData.getAlgorithm());
            
            // 更新统计
            updateDecompressionStats(compressedData.getAlgorithm(), startTime);
            
            return decompressed;
            
        } catch (Exception e) {
            log.error("数据解压失败", e);
            throw new RuntimeException("解压失败", e);
        }
    }
    
    /**
     * 选择最优压缩算法
     */
    private CompressionAlgorithm selectOptimalAlgorithm(byte[] data) {
        // 基于数据特征选择算法
        if (data.length < 1024) {
            // 小数据，使用快速算法
            return CompressionAlgorithm.DEFLATE;
        } else if (data.length > 1024 * 1024) {
            // 大数据，使用高压缩率算法
            return CompressionAlgorithm.GZIP;
        } else {
            // 中等数据，基于历史性能选择
            return selectBestPerformingAlgorithm();
        }
    }
    
    /**
     * 选择性能最佳的算法
     */
    private CompressionAlgorithm selectBestPerformingAlgorithm() {
        CompressionAlgorithm best = CompressionAlgorithm.GZIP;
        double bestScore = 0;
        
        for (Map.Entry<CompressionAlgorithm, AlgorithmStats> entry : algorithmStats.entrySet()) {
            if (entry.getKey() == CompressionAlgorithm.ADAPTIVE) continue;
            
            AlgorithmStats stats = entry.getValue();
            if (stats.count > 0) {
                // 综合评分：压缩率 * 0.6 + 速度 * 0.4
                double compressionScore = stats.totalCompressionRatio / stats.count;
                double speedScore = 1.0 / (stats.avgCompressionTime / 1_000_000.0); // 转换为ms
                double totalScore = compressionScore * 0.6 + Math.min(speedScore / 100, 1.0) * 0.4;
                
                if (totalScore > bestScore) {
                    bestScore = totalScore;
                    best = entry.getKey();
                }
            }
        }
        
        return best;
    }
    
    /**
     * 执行压缩
     */
    private byte[] performCompression(byte[] data, CompressionAlgorithm algorithm, int level) throws IOException {
        switch (algorithm) {
            case GZIP:
                return compressWithGzip(data, level);
            case DEFLATE:
                return compressWithDeflate(data, level);
            case LZ4:
                // 如果有LZ4库，使用LZ4压缩
                return compressWithGzip(data, level); // 降级到GZIP
            case SNAPPY:
                // 如果有Snappy库，使用Snappy压缩
                return compressWithGzip(data, level); // 降级到GZIP
            case BROTLI:
                // 如果有Brotli库，使用Brotli压缩
                return compressWithGzip(data, level); // 降级到GZIP
            default:
                return compressWithGzip(data, level);
        }
    }
    
    /**
     * 执行解压
     */
    private byte[] performDecompression(byte[] data, CompressionAlgorithm algorithm) throws IOException {
        switch (algorithm) {
            case GZIP:
                return decompressWithGzip(data);
            case DEFLATE:
                return decompressWithDeflate(data);
            case LZ4:
                return decompressWithGzip(data); // 降级到GZIP
            case SNAPPY:
                return decompressWithGzip(data); // 降级到GZIP
            case BROTLI:
                return decompressWithGzip(data); // 降级到GZIP
            default:
                return decompressWithGzip(data);
        }
    }
    
    /**
     * GZIP压缩
     */
    private byte[] compressWithGzip(byte[] data, int level) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPOutputStream gzos = new GZIPOutputStream(baos) {{
            def.setLevel(level);
        }}) {
            gzos.write(data);
        }
        return baos.toByteArray();
    }
    
    /**
     * GZIP解压
     */
    private byte[] decompressWithGzip(byte[] data) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        try (GZIPInputStream gzis = new GZIPInputStream(new ByteArrayInputStream(data))) {
            byte[] buffer = new byte[8192];
            int len;
            while ((len = gzis.read(buffer)) != -1) {
                baos.write(buffer, 0, len);
            }
        }
        return baos.toByteArray();
    }
    
    /**
     * Deflate压缩
     */
    private byte[] compressWithDeflate(byte[] data, int level) throws IOException {
        Deflater deflater = new Deflater(level);
        deflater.setInput(data);
        deflater.finish();
        
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[8192];
        
        while (!deflater.finished()) {
            int count = deflater.deflate(buffer);
            baos.write(buffer, 0, count);
        }
        
        deflater.end();
        return baos.toByteArray();
    }
    
    /**
     * Deflate解压
     */
    private byte[] decompressWithDeflate(byte[] data) throws IOException {
        Inflater inflater = new Inflater();
        inflater.setInput(data);

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        byte[] buffer = new byte[8192];

        try {
            while (!inflater.finished()) {
                int count = inflater.inflate(buffer);
                baos.write(buffer, 0, count);
            }
        } catch (DataFormatException e) {
            throw new IOException("Deflate解压失败", e);
        } finally {
            inflater.end();
        }

        return baos.toByteArray();
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(byte[] data, CompressionAlgorithm algorithm, int level) {
        int hash = java.util.Arrays.hashCode(data);
        return String.format("%s_%d_%d", algorithm.name(), hash, level);
    }
    
    /**
     * 更新压缩统计
     */
    private void updateCompressionStats(CompressionAlgorithm algorithm, int originalSize, 
                                      int compressedSize, long startTime) {
        long endTime = System.nanoTime();
        long compressionTime = endTime - startTime;
        
        compressionCount.increment();
        totalCompressionTime.addAndGet(compressionTime);
        totalOriginalSize.addAndGet(originalSize);
        totalCompressedSize.addAndGet(compressedSize);
        
        // 更新算法统计
        AlgorithmStats stats = algorithmStats.get(algorithm);
        if (stats != null) {
            stats.count++;
            stats.totalCompressionTime += compressionTime;
            stats.totalCompressionRatio += (double) (originalSize - compressedSize) / originalSize;
            stats.avgCompressionTime = stats.totalCompressionTime / stats.count;
        }
    }
    
    /**
     * 更新解压统计
     */
    private void updateDecompressionStats(CompressionAlgorithm algorithm, long startTime) {
        long endTime = System.nanoTime();
        long decompressionTime = endTime - startTime;
        
        decompressionCount.increment();
        totalDecompressionTime.addAndGet(decompressionTime);
    }
    
    /**
     * 获取压缩统计信息
     */
    public CompressionStats getStats() {
        long compressions = compressionCount.sum();
        long decompressions = decompressionCount.sum();
        long totalOriginal = totalOriginalSize.get();
        long totalCompressed = totalCompressedSize.get();
        
        double avgCompressionRatio = totalOriginal > 0 ? 
            (double) (totalOriginal - totalCompressed) / totalOriginal : 0;
        double avgCompressionTime = compressions > 0 ? 
            (double) totalCompressionTime.get() / compressions / 1_000_000 : 0; // ms
        double avgDecompressionTime = decompressions > 0 ? 
            (double) totalDecompressionTime.get() / decompressions / 1_000_000 : 0; // ms
        
        long hits = cacheHits.sum();
        long misses = cacheMisses.sum();
        double cacheHitRate = (hits + misses) > 0 ? (double) hits / (hits + misses) : 0;
        
        return new CompressionStats(
            compressions, decompressions, avgCompressionRatio,
            avgCompressionTime, avgDecompressionTime, cacheHitRate,
            compressionCache.size(), totalOriginal, totalCompressed
        );
    }
    
    // 内部类定义
    public static class CompressedData {
        private final byte[] data;
        private final CompressionAlgorithm algorithm;
        private final int originalSize;
        private final int compressedSize;
        
        public CompressedData(byte[] data, CompressionAlgorithm algorithm, int originalSize, int compressedSize) {
            this.data = data;
            this.algorithm = algorithm;
            this.originalSize = originalSize;
            this.compressedSize = compressedSize;
        }
        
        public byte[] getData() { return data; }
        public CompressionAlgorithm getAlgorithm() { return algorithm; }
        public int getOriginalSize() { return originalSize; }
        public int getCompressedSize() { return compressedSize; }
        public double getCompressionRatio() { 
            return originalSize > 0 ? (double) (originalSize - compressedSize) / originalSize : 0; 
        }
    }
    
    private static class AlgorithmStats {
        public long count = 0;
        public long totalCompressionTime = 0;
        public double totalCompressionRatio = 0;
        public double avgCompressionTime = 0;
    }
    
    public static class CompressionStats {
        public final long compressionCount;
        public final long decompressionCount;
        public final double avgCompressionRatio;
        public final double avgCompressionTimeMs;
        public final double avgDecompressionTimeMs;
        public final double cacheHitRate;
        public final int cacheSize;
        public final long totalOriginalSize;
        public final long totalCompressedSize;
        
        public CompressionStats(long compressionCount, long decompressionCount, double avgCompressionRatio,
                              double avgCompressionTimeMs, double avgDecompressionTimeMs, double cacheHitRate,
                              int cacheSize, long totalOriginalSize, long totalCompressedSize) {
            this.compressionCount = compressionCount;
            this.decompressionCount = decompressionCount;
            this.avgCompressionRatio = avgCompressionRatio;
            this.avgCompressionTimeMs = avgCompressionTimeMs;
            this.avgDecompressionTimeMs = avgDecompressionTimeMs;
            this.cacheHitRate = cacheHitRate;
            this.cacheSize = cacheSize;
            this.totalOriginalSize = totalOriginalSize;
            this.totalCompressedSize = totalCompressedSize;
        }
        
        @Override
        public String toString() {
            return String.format(
                "CompressionStats{compressions=%d, decompressions=%d, ratio=%.2f%%, " +
                "compTime=%.2fms, decompTime=%.2fms, cacheHit=%.1f%%, cacheSize=%d, " +
                "totalSaved=%dMB}",
                compressionCount, decompressionCount, avgCompressionRatio * 100,
                avgCompressionTimeMs, avgDecompressionTimeMs, cacheHitRate * 100, cacheSize,
                (totalOriginalSize - totalCompressedSize) / 1024 / 1024
            );
        }
    }
}
