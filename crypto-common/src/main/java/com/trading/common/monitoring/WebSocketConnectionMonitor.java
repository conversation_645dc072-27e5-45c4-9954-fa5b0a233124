package com.trading.common.monitoring;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.Counter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.HashMap;

/**
 * WebSocket连接监控服务
 * 监控WebSocket连接状态、消息处理性能、连接质量等
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
public class WebSocketConnectionMonitor {

    private static final Logger log = LoggerFactory.getLogger(WebSocketConnectionMonitor.class);

    @Autowired
    private UnifiedMonitoringConfiguration.AlertManager alertManager;

    // 连接统计
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong activeConnections = new AtomicLong(0);
    private final AtomicLong failedConnections = new AtomicLong(0);
    private final AtomicLong reconnections = new AtomicLong(0);

    // 消息统计
    private final AtomicLong messagesReceived = new AtomicLong(0);
    private final AtomicLong messagesSent = new AtomicLong(0);
    private final AtomicLong messagesDropped = new AtomicLong(0);

    // 性能统计
    private final AtomicReference<Double> averageLatency = new AtomicReference<>(0.0);
    private final AtomicReference<Double> messageRate = new AtomicReference<>(0.0);
    private final AtomicReference<Instant> lastMessageTime = new AtomicReference<>(Instant.now());

    // 连接详情
    private final Map<String, ConnectionInfo> connectionDetails = new ConcurrentHashMap<>();

    // 监控指标
    private final MeterRegistry meterRegistry;
    private final Timer messageProcessingTimer;
    private final Gauge activeConnectionsGauge;
    private final Gauge messageRateGauge;
    private final Gauge averageLatencyGauge;

    public WebSocketConnectionMonitor(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.messageProcessingTimer = Timer.builder("websocket.message.processing.duration")
                .description("WebSocket消息处理时间")
                .register(meterRegistry);
        this.activeConnectionsGauge = Gauge.builder("websocket.connections.active", activeConnections, AtomicLong::doubleValue)
                .description("WebSocket活跃连接数")
                .register(meterRegistry);
        this.messageRateGauge = Gauge.builder("websocket.message.rate", messageRate, AtomicReference::get)
                .description("WebSocket消息速率")
                .register(meterRegistry);
        this.averageLatencyGauge = Gauge.builder("websocket.latency.average", averageLatency, AtomicReference::get)
                .description("WebSocket平均延迟")
                .register(meterRegistry);
        
        log.info("WebSocket连接监控服务已初始化");
    }

    /**
     * 记录连接建立
     */
    public void recordConnectionEstablished(String connectionId, String endpoint) {
        totalConnections.incrementAndGet();
        activeConnections.incrementAndGet();
        Counter.builder("websocket.connections.total")
                .tag("type", "established")
                .tag("endpoint", endpoint)
                .register(meterRegistry)
                .increment();
        
        ConnectionInfo info = new ConnectionInfo(connectionId, endpoint, Instant.now());
        connectionDetails.put(connectionId, info);
        
        log.debug("WebSocket连接建立: {} -> {}", connectionId, endpoint);
    }

    /**
     * 记录连接关闭
     */
    public void recordConnectionClosed(String connectionId, String reason) {
        activeConnections.decrementAndGet();
        Counter.builder("websocket.connections.total")
                .tag("type", "closed")
                .tag("reason", reason)
                .register(meterRegistry)
                .increment();
        
        ConnectionInfo info = connectionDetails.remove(connectionId);
        if (info != null) {
            long duration = Instant.now().toEpochMilli() - info.getConnectedAt().toEpochMilli();
            log.debug("WebSocket连接关闭: {} (持续时间: {}ms, 原因: {})", connectionId, duration, reason);
        }
    }

    /**
     * 记录连接失败
     */
    public void recordConnectionFailed(String endpoint, String reason) {
        failedConnections.incrementAndGet();
        Counter.builder("websocket.connections.total")
                .tag("type", "failed")
                .tag("endpoint", endpoint)
                .tag("reason", reason)
                .register(meterRegistry)
                .increment();
        
        log.warn("WebSocket连接失败: {} (原因: {})", endpoint, reason);
        
        // 发送告警
        alertManager.sendWarningAlert(
                "WebSocket连接失败",
                "端点: " + endpoint,
                "原因: " + reason,
                "失败总数: " + failedConnections.get()
        );
    }

    /**
     * 记录重连
     */
    public void recordReconnection(String connectionId, String endpoint) {
        reconnections.incrementAndGet();
        Counter.builder("websocket.connections.total")
                .tag("type", "reconnected")
                .tag("endpoint", endpoint)
                .register(meterRegistry)
                .increment();
        
        log.info("WebSocket重连成功: {} -> {}", connectionId, endpoint);
    }

    /**
     * 记录消息接收
     */
    public void recordMessageReceived(String connectionId, String messageType, long size) {
        messagesReceived.incrementAndGet();
        Counter.builder("websocket.messages.total")
                .tag("direction", "received")
                .tag("type", messageType)
                .register(meterRegistry)
                .increment();
        lastMessageTime.set(Instant.now());
        
        ConnectionInfo info = connectionDetails.get(connectionId);
        if (info != null) {
            info.incrementMessagesReceived();
            info.addBytesReceived(size);
        }
        
        log.debug("WebSocket消息接收: {} (类型: {}, 大小: {}字节)", connectionId, messageType, size);
    }

    /**
     * 记录消息发送
     */
    public void recordMessageSent(String connectionId, String messageType, long size) {
        messagesSent.incrementAndGet();
        Counter.builder("websocket.messages.total")
                .tag("direction", "sent")
                .tag("type", messageType)
                .register(meterRegistry)
                .increment();
        
        ConnectionInfo info = connectionDetails.get(connectionId);
        if (info != null) {
            info.incrementMessagesSent();
            info.addBytesSent(size);
        }
        
        log.debug("WebSocket消息发送: {} (类型: {}, 大小: {}字节)", connectionId, messageType, size);
    }

    /**
     * 记录消息丢弃
     */
    public void recordMessageDropped(String connectionId, String reason) {
        messagesDropped.incrementAndGet();
        Counter.builder("websocket.messages.total")
                .tag("direction", "dropped")
                .tag("reason", reason)
                .register(meterRegistry)
                .increment();
        
        log.warn("WebSocket消息丢弃: {} (原因: {})", connectionId, reason);
    }

    /**
     * 开始消息处理计时
     */
    public Timer.Sample startMessageProcessing() {
        return Timer.start(meterRegistry);
    }

    /**
     * 记录消息处理完成
     */
    public void recordMessageProcessed(Timer.Sample sample, String messageType) {
        sample.stop(Timer.builder("websocket.message.processing.duration")
                .tag("type", messageType)
                .register(meterRegistry));
    }

    /**
     * 定期计算性能指标 - 每30秒执行一次
     */
    @Scheduled(fixedRate = 30000)
    public void calculatePerformanceMetrics() {
        try {
            // 计算消息速率（每秒消息数）
            long currentMessages = messagesReceived.get() + messagesSent.get();
            double rate = currentMessages / 30.0; // 30秒内的平均速率
            messageRate.set(rate);
            
            // 计算平均延迟（从消息处理计时器获取）
            double avgLatency = messageProcessingTimer.mean(java.util.concurrent.TimeUnit.MILLISECONDS);
            averageLatency.set(avgLatency);
            
            log.debug("WebSocket性能指标 - 消息速率: {:.2f}/秒, 平均延迟: {:.2f}ms", rate, avgLatency);
            
        } catch (Exception e) {
            log.error("计算WebSocket性能指标失败", e);
        }
    }

    /**
     * 定期检查连接健康状态 - 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000)
    public void checkConnectionHealth() {
        try {
            long activeCount = activeConnections.get();
            long failedCount = failedConnections.get();
            long totalCount = totalConnections.get();
            
            // 检查连接失败率
            if (totalCount > 0) {
                double failureRate = (double) failedCount / totalCount * 100;
                if (failureRate > 10) {
                    alertManager.sendCriticalAlert(
                            "WebSocket连接失败率过高",
                            "失败率: " + String.format("%.2f%%", failureRate),
                            "失败连接: " + failedCount,
                            "总连接: " + totalCount
                    );
                }
            }
            
            // 检查活跃连接数
            if (activeCount > 800) {
                alertManager.sendWarningAlert(
                        "WebSocket活跃连接数过多",
                        "当前连接数: " + activeCount
                );
            }
            
            // 检查消息处理延迟
            double avgLatency = averageLatency.get();
            if (avgLatency > 100) {
                alertManager.sendWarningAlert(
                        "WebSocket消息处理延迟过高",
                        "平均延迟: " + String.format("%.2f ms", avgLatency)
                );
            }
            
            // 检查消息丢弃
            long droppedCount = messagesDropped.get();
            if (droppedCount > 0) {
                alertManager.sendWarningAlert(
                        "WebSocket消息丢弃",
                        "丢弃消息数: " + droppedCount
                );
            }
            
            log.info("WebSocket连接健康检查完成 - 活跃: {}, 失败: {}, 总数: {}, 平均延迟: {:.2f}ms",
                    activeCount, failedCount, totalCount, avgLatency);
            
        } catch (Exception e) {
            log.error("WebSocket连接健康检查失败", e);
        }
    }

    /**
     * 获取连接统计信息
     */
    public Map<String, Object> getConnectionStats() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalConnections", totalConnections.get());
        stats.put("activeConnections", activeConnections.get());
        stats.put("failedConnections", failedConnections.get());
        stats.put("reconnections", reconnections.get());
        stats.put("messagesReceived", messagesReceived.get());
        stats.put("messagesSent", messagesSent.get());
        stats.put("messagesDropped", messagesDropped.get());
        stats.put("averageLatency", averageLatency.get());
        stats.put("messageRate", messageRate.get());
        stats.put("lastMessageTime", lastMessageTime.get());
        return stats;
    }

    /**
     * 连接信息类
     */
    private static class ConnectionInfo {
        private final String connectionId;
        private final String endpoint;
        private final Instant connectedAt;
        private final AtomicLong messagesReceived = new AtomicLong(0);
        private final AtomicLong messagesSent = new AtomicLong(0);
        private final AtomicLong bytesReceived = new AtomicLong(0);
        private final AtomicLong bytesSent = new AtomicLong(0);

        public ConnectionInfo(String connectionId, String endpoint, Instant connectedAt) {
            this.connectionId = connectionId;
            this.endpoint = endpoint;
            this.connectedAt = connectedAt;
        }

        public String getConnectionId() { return connectionId; }
        public String getEndpoint() { return endpoint; }
        public Instant getConnectedAt() { return connectedAt; }
        
        public void incrementMessagesReceived() { messagesReceived.incrementAndGet(); }
        public void incrementMessagesSent() { messagesSent.incrementAndGet(); }
        public void addBytesReceived(long bytes) { bytesReceived.addAndGet(bytes); }
        public void addBytesSent(long bytes) { bytesSent.addAndGet(bytes); }
    }
}
