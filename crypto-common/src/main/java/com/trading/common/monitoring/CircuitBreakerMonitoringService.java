package com.trading.common.monitoring;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 熔断器监控服务
 * 监控熔断器状态变化和性能指标
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
public class CircuitBreakerMonitoringService {

    private static final Logger log = LoggerFactory.getLogger(CircuitBreakerMonitoringService.class);

    @Autowired
    private CircuitBreakerRegistry circuitBreakerRegistry;

    @Autowired
    private MeterRegistry meterRegistry;

    // 状态变化计数器
    private final Map<String, Counter> stateChangeCounters = new ConcurrentHashMap<>();
    private final Map<String, AtomicLong> lastStateChangeTime = new ConcurrentHashMap<>();
    private final Map<String, CircuitBreaker.State> lastKnownState = new ConcurrentHashMap<>();

    // 性能指标
    private final Map<String, Timer> operationTimers = new ConcurrentHashMap<>();
    private final Map<String, Counter> successCounters = new ConcurrentHashMap<>();
    private final Map<String, Counter> failureCounters = new ConcurrentHashMap<>();
    private final Map<String, Counter> rejectedCounters = new ConcurrentHashMap<>();

    @PostConstruct
    public void initialize() {
        log.info("初始化熔断器监控服务...");
        
        // 注册现有熔断器的监控
        circuitBreakerRegistry.getAllCircuitBreakers().forEach(this::registerCircuitBreakerMetrics);
        
        // 监听新熔断器的创建
        circuitBreakerRegistry.getEventPublisher().onEntryAdded(event -> {
            CircuitBreaker circuitBreaker = event.getAddedEntry();
            registerCircuitBreakerMetrics(circuitBreaker);
            log.info("新熔断器已注册监控: {}", circuitBreaker.getName());
        });
        
        log.info("熔断器监控服务初始化完成");
    }

    /**
     * 注册熔断器指标
     */
    private void registerCircuitBreakerMetrics(CircuitBreaker circuitBreaker) {
        String name = circuitBreaker.getName();
        
        // 注册状态变化监听器
        circuitBreaker.getEventPublisher().onStateTransition(event -> {
            CircuitBreaker.State fromState = event.getStateTransition().getFromState();
            CircuitBreaker.State toState = event.getStateTransition().getToState();
            
            log.info("熔断器状态变化: {} {} -> {}", name, fromState, toState);
            
            // 记录状态变化
            getStateChangeCounter(name, fromState, toState).increment();
            lastStateChangeTime.put(name, new AtomicLong(System.currentTimeMillis()));
            lastKnownState.put(name, toState);
            
            // 特别关注HALF_OPEN状态的变化
            if (toState == CircuitBreaker.State.HALF_OPEN) {
                log.warn("熔断器进入HALF_OPEN状态，需要密切监控: {}", name);
            } else if (fromState == CircuitBreaker.State.HALF_OPEN) {
                if (toState == CircuitBreaker.State.CLOSED) {
                    log.info("熔断器从HALF_OPEN恢复到CLOSED状态: {}", name);
                } else if (toState == CircuitBreaker.State.OPEN) {
                    log.warn("熔断器从HALF_OPEN回退到OPEN状态: {}", name);
                }
            }
        });

        // 注册成功事件监听器
        circuitBreaker.getEventPublisher().onSuccess(event -> {
            getSuccessCounter(name).increment();
            getOperationTimer(name).record(event.getElapsedDuration());
        });

        // 注册失败事件监听器
        circuitBreaker.getEventPublisher().onError(event -> {
            getFailureCounter(name).increment();
            log.debug("熔断器操作失败: {}, 错误: {}", name, event.getThrowable().getMessage());
        });

        // 注册拒绝事件监听器
        circuitBreaker.getEventPublisher().onCallNotPermitted(event -> {
            getRejectedCounter(name).increment();
            log.debug("熔断器拒绝调用: {}", name);
        });

        // 注册状态指标
        Gauge.builder("circuit_breaker_state", circuitBreaker, cb -> {
                    switch (cb.getState()) {
                        case CLOSED: return 0;
                        case OPEN: return 1;
                        case HALF_OPEN: return 2;
                        default: return -1;
                    }
                })
                .description("Circuit breaker state (0=CLOSED, 1=OPEN, 2=HALF_OPEN)")
                .tag("name", name)
                .register(meterRegistry);

        // 注册失败率指标
        Gauge.builder("circuit_breaker_failure_rate", circuitBreaker, cb -> cb.getMetrics().getFailureRate())
                .description("Circuit breaker failure rate")
                .tag("name", name)
                .register(meterRegistry);

        // 注册慢调用率指标
        Gauge.builder("circuit_breaker_slow_call_rate", circuitBreaker, cb -> cb.getMetrics().getSlowCallRate())
                .description("Circuit breaker slow call rate")
                .tag("name", name)
                .register(meterRegistry);

        log.debug("熔断器指标已注册: {}", name);
    }

    /**
     * 获取状态变化计数器
     */
    private Counter getStateChangeCounter(String circuitBreakerName, CircuitBreaker.State fromState, CircuitBreaker.State toState) {
        String key = circuitBreakerName + "_" + fromState + "_to_" + toState;
        return stateChangeCounters.computeIfAbsent(key, k ->
                Counter.builder("circuit_breaker_state_transitions")
                        .description("Circuit breaker state transitions")
                        .tag("name", circuitBreakerName)
                        .tag("from_state", fromState.toString())
                        .tag("to_state", toState.toString())
                        .register(meterRegistry));
    }

    /**
     * 获取操作计时器
     */
    private Timer getOperationTimer(String circuitBreakerName) {
        return operationTimers.computeIfAbsent(circuitBreakerName, name ->
                Timer.builder("circuit_breaker_operation_duration")
                        .description("Circuit breaker operation duration")
                        .tag("name", name)
                        .register(meterRegistry));
    }

    /**
     * 获取成功计数器
     */
    private Counter getSuccessCounter(String circuitBreakerName) {
        return successCounters.computeIfAbsent(circuitBreakerName, name ->
                Counter.builder("circuit_breaker_success")
                        .description("Circuit breaker successful operations")
                        .tag("name", name)
                        .register(meterRegistry));
    }

    /**
     * 获取失败计数器
     */
    private Counter getFailureCounter(String circuitBreakerName) {
        return failureCounters.computeIfAbsent(circuitBreakerName, name ->
                Counter.builder("circuit_breaker_failure")
                        .description("Circuit breaker failed operations")
                        .tag("name", name)
                        .register(meterRegistry));
    }

    /**
     * 获取拒绝计数器
     */
    private Counter getRejectedCounter(String circuitBreakerName) {
        return rejectedCounters.computeIfAbsent(circuitBreakerName, name ->
                Counter.builder("circuit_breaker_rejected")
                        .description("Circuit breaker rejected operations")
                        .tag("name", name)
                        .register(meterRegistry));
    }

    /**
     * 定期报告熔断器状态
     */
    @Scheduled(fixedRate = 60000) // 每分钟报告一次
    public void reportCircuitBreakerStatus() {
        circuitBreakerRegistry.getAllCircuitBreakers().forEach(circuitBreaker -> {
            String name = circuitBreaker.getName();
            CircuitBreaker.State currentState = circuitBreaker.getState();
            CircuitBreaker.Metrics metrics = circuitBreaker.getMetrics();

            // 只有在状态不是CLOSED或者有失败时才记录日志
            if (currentState != CircuitBreaker.State.CLOSED || metrics.getFailureRate() > 0) {
                log.info("熔断器状态报告: {} - 状态: {}, 失败率: {:.2f}%, 慢调用率: {:.2f}%, " +
                        "成功调用: {}, 失败调用: {}, 拒绝调用: {}",
                        name, currentState, metrics.getFailureRate(), metrics.getSlowCallRate(),
                        metrics.getNumberOfSuccessfulCalls(), metrics.getNumberOfFailedCalls(),
                        metrics.getNumberOfNotPermittedCalls());
            }
        });
    }

    /**
     * 获取熔断器健康状态
     */
    public boolean isCircuitBreakerHealthy(String name) {
        return circuitBreakerRegistry.find(name)
                .map(cb -> cb.getState() == CircuitBreaker.State.CLOSED)
                .orElse(false);
    }

    /**
     * 获取所有熔断器的健康状态
     */
    public Map<String, Boolean> getAllCircuitBreakerHealthStatus() {
        Map<String, Boolean> healthStatus = new ConcurrentHashMap<>();
        circuitBreakerRegistry.getAllCircuitBreakers().forEach(cb -> {
            healthStatus.put(cb.getName(), cb.getState() == CircuitBreaker.State.CLOSED);
        });
        return healthStatus;
    }
}
