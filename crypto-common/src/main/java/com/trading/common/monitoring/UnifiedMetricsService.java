package com.trading.common.monitoring;

import io.micrometer.core.instrument.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 统一的监控指标服务
 * 完整的企业级监控解决方案
 * 基于Micrometer的监控系统，支持多种监控后端
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
public class UnifiedMetricsService {

    private static final Logger log = LoggerFactory.getLogger(UnifiedMetricsService.class);
    
    @Autowired
    private MeterRegistry meterRegistry;
    
    // 缓存计数器和计时器，避免重复创建
    private final ConcurrentHashMap<String, Counter> counters = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Timer> timers = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Gauge> gauges = new ConcurrentHashMap<>();

    // 性能统计
    private final AtomicLong totalMetricsCreated = new AtomicLong(0);
    private final AtomicLong totalMetricsRecorded = new AtomicLong(0);
    
    @PostConstruct
    public void initializeMetrics() {
        log.info("初始化统一监控指标服务...");

        // 注册系统级指标
        registerSystemMetrics();

        log.info("统一监控指标服务初始化完成");
    }

    /**
     * 注册系统级指标
     */
    private void registerSystemMetrics() {
        // 注册JVM内存指标
        Gauge.builder("jvm.memory.used", Runtime.getRuntime(), runtime -> runtime.totalMemory() - runtime.freeMemory())
                .description("JVM已使用内存")
                .register(meterRegistry);

        Gauge.builder("jvm.memory.free", Runtime.getRuntime(), Runtime::freeMemory)
                .description("JVM空闲内存")
                .register(meterRegistry);

        // 注册指标统计
        Gauge.builder("metrics.counters.total", counters, Map::size)
                .description("计数器总数")
                .register(meterRegistry);

        Gauge.builder("metrics.timers.total", timers, Map::size)
                .description("计时器总数")
                .register(meterRegistry);
    }
    
    /**
     * 增加计数器
     */
    public void incrementCounter(String name, String... tags) {
        getOrCreateCounter(name, tags).increment();
        totalMetricsRecorded.incrementAndGet();
    }

    /**
     * 增加计数器指定数量
     */
    public void incrementCounter(String name, double amount, String... tags) {
        getOrCreateCounter(name, tags).increment(amount);
        totalMetricsRecorded.incrementAndGet();
    }
    
    /**
     * 记录时间
     */
    public void recordTime(String name, long timeInMillis, String... tags) {
        getOrCreateTimer(name, tags).record(timeInMillis, java.util.concurrent.TimeUnit.MILLISECONDS);
        totalMetricsRecorded.incrementAndGet();
    }

    /**
     * 记录计时器
     */
    public void recordTimer(String name, long duration, String... tags) {
        getOrCreateTimer(name, tags).record(duration, java.util.concurrent.TimeUnit.MILLISECONDS);
        totalMetricsRecorded.incrementAndGet();
    }

    /**
     * 设置仪表值
     */
    public void setGauge(String name, double value, String... tags) {
        meterRegistry.gauge(name, buildTags(tags), value);
        totalMetricsRecorded.incrementAndGet();
    }

    /**
     * 创建或获取仪表
     */
    public Gauge getOrCreateGauge(String name, Object obj, java.util.function.ToDoubleFunction<Object> valueFunction, String... tags) {
        String key = buildKey(name, tags);
        return gauges.computeIfAbsent(key, k -> {
            totalMetricsCreated.incrementAndGet();
            return Gauge.builder(name, obj, valueFunction)
                    .description("Gauge for " + name)
                    .tags(buildTags(tags))
                    .register(meterRegistry);
        });
    }
    
    // 私有辅助方法
    private Counter getOrCreateCounter(String name, String... tags) {
        String key = buildKey(name, tags);
        return counters.computeIfAbsent(key, k -> {
            totalMetricsCreated.incrementAndGet();
            return Counter.builder(name)
                    .description("Counter for " + name)
                    .tags(buildTags(tags))
                    .register(meterRegistry);
        });
    }

    private Timer getOrCreateTimer(String name, String... tags) {
        String key = buildKey(name, tags);
        return timers.computeIfAbsent(key, k -> {
            totalMetricsCreated.incrementAndGet();
            return Timer.builder(name)
                    .description("Timer for " + name)
                    .tags(buildTags(tags))
                    .register(meterRegistry);
        });
    }
    
    private String buildKey(String name, String... tags) {
        StringBuilder key = new StringBuilder(name);
        for (int i = 0; i < tags.length; i += 2) {
            if (i + 1 < tags.length) {
                key.append(":").append(tags[i]).append("=").append(tags[i + 1]);
            }
        }
        return key.toString();
    }
    
    private Tags buildTags(String... tags) {
        if (tags.length == 0) {
            return Tags.empty();
        }

        // 构建Tags，每两个元素为一对key-value
        Tags result = Tags.empty();
        for (int i = 0; i < tags.length; i += 2) {
            if (i + 1 < tags.length) {
                result = result.and(tags[i], tags[i + 1]);
            }
        }
        return result;
    }
    
    /**
     * 获取指标统计信息
     */
    public String getMetricsStats() {
        StringBuilder stats = new StringBuilder();
        stats.append("Unified Metrics Statistics:\n");
        stats.append(String.format("  Counters: %d\n", counters.size()));
        stats.append(String.format("  Timers: %d\n", timers.size()));
        stats.append(String.format("  Gauges: %d\n", gauges.size()));
        stats.append(String.format("  Total Meters: %d\n", meterRegistry.getMeters().size()));
        stats.append(String.format("  Metrics Created: %d\n", totalMetricsCreated.get()));
        stats.append(String.format("  Metrics Recorded: %d\n", totalMetricsRecorded.get()));
        return stats.toString();
    }

    /**
     * 获取详细的性能统计
     */
    public java.util.Map<String, Object> getDetailedStats() {
        java.util.Map<String, Object> stats = new java.util.HashMap<>();
        stats.put("counters", counters.size());
        stats.put("timers", timers.size());
        stats.put("gauges", gauges.size());
        stats.put("totalMeters", meterRegistry.getMeters().size());
        stats.put("metricsCreated", totalMetricsCreated.get());
        stats.put("metricsRecorded", totalMetricsRecorded.get());

        // JVM信息
        Runtime runtime = Runtime.getRuntime();
        stats.put("jvmMemoryUsed", runtime.totalMemory() - runtime.freeMemory());
        stats.put("jvmMemoryFree", runtime.freeMemory());
        stats.put("jvmMemoryTotal", runtime.totalMemory());

        return stats;
    }

    /**
     * 清理所有缓存的指标
     */
    public void clearCache() {
        counters.clear();
        timers.clear();
        gauges.clear();
        totalMetricsCreated.set(0);
        totalMetricsRecorded.set(0);
        log.info("指标缓存已清理");
    }
}
