package com.trading.common.monitoring;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.prometheus.PrometheusConfig;
import io.micrometer.prometheus.PrometheusMeterRegistry;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.autoconfigure.metrics.MeterRegistryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 统一监控配置
 * 提供全系统的监控指标、健康检查和告警配置
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Configuration
@EnableScheduling
public class UnifiedMonitoringConfiguration {

    private static final Logger log = LoggerFactory.getLogger(UnifiedMonitoringConfiguration.class);

    /**
     * 配置Prometheus指标注册表
     */
    @Bean
    @Primary
    public PrometheusMeterRegistry prometheusMeterRegistry() {
        PrometheusMeterRegistry registry = new PrometheusMeterRegistry(PrometheusConfig.DEFAULT);
        log.info("Prometheus指标注册表已配置");
        return registry;
    }

    /**
     * 自定义指标注册表配置
     */
    @Bean
    public MeterRegistryCustomizer<MeterRegistry> metricsCommonTags() {
        return registry -> {
            registry.config()
                    .commonTags(
                            "application", "crypto-trading-system",
                            "version", "1.0.0",
                            "environment", getEnvironment()
                    );
            log.info("通用指标标签已配置");
        };
    }

    /**
     * MySQL数据库健康检查
     */
    @Bean
    public HealthIndicator mysqlHealthIndicator(DataSource dataSource) {
        return new HealthIndicator() {
            private final AtomicLong lastCheckTime = new AtomicLong(0);
            private final AtomicReference<Health> cachedHealth = new AtomicReference<>();
            private static final long CACHE_DURATION_MS = 30000; // 30秒缓存

            @Override
            public Health health() {
                long currentTime = System.currentTimeMillis();
                long lastCheck = lastCheckTime.get();
                
                // 使用缓存避免频繁检查
                if (currentTime - lastCheck < CACHE_DURATION_MS && cachedHealth.get() != null) {
                    return cachedHealth.get();
                }

                Health health = checkMySQLHealth(dataSource);
                lastCheckTime.set(currentTime);
                cachedHealth.set(health);
                return health;
            }
        };
    }

    /**
     * 系统性能指标收集器
     */
    @Bean
    public SystemPerformanceMetrics systemPerformanceMetrics(MeterRegistry meterRegistry) {
        return new SystemPerformanceMetrics(meterRegistry);
    }

    /**
     * 业务指标收集器
     */
    @Bean
    public BusinessMetricsCollector businessMetricsCollector(MeterRegistry meterRegistry) {
        return new BusinessMetricsCollector(meterRegistry);
    }

    /**
     * 告警管理器
     */
    @Bean
    public AlertManager alertManager() {
        return new AlertManager();
    }

    /**
     * 检查MySQL健康状态
     */
    private Health checkMySQLHealth(DataSource dataSource) {
        try {
            long startTime = System.currentTimeMillis();
            
            try (Connection connection = dataSource.getConnection()) {
                // 执行简单查询测试连接
                boolean isValid = connection.isValid(5); // 5秒超时
                long responseTime = System.currentTimeMillis() - startTime;
                
                if (isValid && responseTime < 1000) {
                    return Health.up()
                            .withDetail("database", "MySQL")
                            .withDetail("status", "Connected")
                            .withDetail("responseTime", responseTime + "ms")
                            .withDetail("url", getMaskedUrl(connection))
                            .withDetail("driver", connection.getMetaData().getDriverName())
                            .withDetail("version", connection.getMetaData().getDatabaseProductVersion())
                            .build();
                } else if (isValid) {
                    return Health.up()
                            .withDetail("database", "MySQL")
                            .withDetail("status", "Slow Response")
                            .withDetail("responseTime", responseTime + "ms")
                            .withDetail("warning", "数据库响应时间超过1秒")
                            .build();
                } else {
                    return Health.down()
                            .withDetail("database", "MySQL")
                            .withDetail("status", "Connection Invalid")
                            .withDetail("responseTime", responseTime + "ms")
                            .build();
                }
            }
        } catch (SQLException e) {
            return Health.down()
                    .withDetail("database", "MySQL")
                    .withDetail("status", "Connection Failed")
                    .withDetail("error", e.getMessage())
                    .withException(e)
                    .build();
        }
    }

    /**
     * 获取掩码后的数据库URL（隐藏敏感信息）
     */
    private String getMaskedUrl(Connection connection) {
        try {
            String url = connection.getMetaData().getURL();
            // 移除密码等敏感信息
            return url.replaceAll("password=[^&]*", "password=***");
        } catch (SQLException e) {
            return "Unknown";
        }
    }

    /**
     * 获取当前环境
     */
    private String getEnvironment() {
        String env = System.getProperty("spring.profiles.active");
        return env != null ? env : "default";
    }

    /**
     * 系统性能指标收集器
     */
    public static class SystemPerformanceMetrics {
        private final MeterRegistry meterRegistry;
        private final Timer requestTimer;
        private final Counter errorCounter;
        private final Gauge memoryGauge;
        private final Gauge cpuGauge;

        public SystemPerformanceMetrics(MeterRegistry meterRegistry) {
            this.meterRegistry = meterRegistry;
            this.requestTimer = Timer.builder("system.request.duration")
                    .description("请求处理时间")
                    .register(meterRegistry);
            this.errorCounter = Counter.builder("system.errors.total")
                    .description("系统错误总数")
                    .register(meterRegistry);
            this.memoryGauge = Gauge.builder("system.memory.usage", this, SystemPerformanceMetrics::getMemoryUsage)
                    .description("内存使用率")
                    .register(meterRegistry);
            this.cpuGauge = Gauge.builder("system.cpu.usage", this, SystemPerformanceMetrics::getCpuUsage)
                    .description("CPU使用率")
                    .register(meterRegistry);
            
            log.info("系统性能指标收集器已初始化");
        }

        public Timer.Sample startTimer() {
            return Timer.start(meterRegistry);
        }

        public void recordError(String errorType) {
            Counter.builder("system.errors.total")
                    .tag("type", errorType)
                    .register(meterRegistry)
                    .increment();
        }

        private double getMemoryUsage() {
            Runtime runtime = Runtime.getRuntime();
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            return ((double) (totalMemory - freeMemory) / totalMemory) * 100;
        }

        private double getCpuUsage() {
            // 简化的CPU使用率计算
            return ((com.sun.management.OperatingSystemMXBean) 
                    java.lang.management.ManagementFactory.getOperatingSystemMXBean())
                    .getProcessCpuLoad() * 100;
        }
    }

    /**
     * 业务指标收集器
     */
    public static class BusinessMetricsCollector {
        private final MeterRegistry meterRegistry;
        private final Counter tradeCounter;
        private final Timer tradeProcessingTimer;
        private final Gauge activeConnectionsGauge;
        private final AtomicLong activeConnections = new AtomicLong(0);

        public BusinessMetricsCollector(MeterRegistry meterRegistry) {
            this.meterRegistry = meterRegistry;
            this.tradeCounter = Counter.builder("business.trades.total")
                    .description("交易总数")
                    .register(meterRegistry);
            this.tradeProcessingTimer = Timer.builder("business.trade.processing.duration")
                    .description("交易处理时间")
                    .register(meterRegistry);
            this.activeConnectionsGauge = Gauge.builder("business.connections.active", activeConnections, AtomicLong::doubleValue)
                    .description("活跃连接数")
                    .register(meterRegistry);
            
            log.info("业务指标收集器已初始化");
        }

        public void recordTrade(String symbol, String type) {
            Counter.builder("business.trades.total")
                    .tag("symbol", symbol)
                    .tag("type", type)
                    .register(meterRegistry)
                    .increment();
        }

        public Timer.Sample startTradeProcessing() {
            return Timer.start(meterRegistry);
        }

        public void incrementActiveConnections() {
            activeConnections.incrementAndGet();
        }

        public void decrementActiveConnections() {
            activeConnections.decrementAndGet();
        }
    }

    /**
     * 告警管理器
     */
    public static class AlertManager {
        private static final Logger alertLog = LoggerFactory.getLogger("ALERT");

        public void sendAlert(String level, String message, Object... details) {
            // 这里可以集成实际的告警系统，如钉钉、邮件等
            alertLog.warn("[{}] {}", level, message);
            if (details.length > 0) {
                alertLog.warn("告警详情: {}", java.util.Arrays.toString(details));
            }
        }

        public void sendCriticalAlert(String message, Object... details) {
            sendAlert("CRITICAL", message, details);
        }

        public void sendWarningAlert(String message, Object... details) {
            sendAlert("WARNING", message, details);
        }

        public void sendInfoAlert(String message, Object... details) {
            sendAlert("INFO", message, details);
        }
    }
}
