package com.trading.common.monitoring;

import com.zaxxer.hikari.HikariDataSource;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Gauge;
import io.micrometer.core.instrument.Counter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.Map;
import java.util.HashMap;

/**
 * MySQL专门监控服务
 * 监控MySQL数据库的性能指标、连接状态、查询性能等
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
public class MySQLMonitoringService {

    private static final Logger log = LoggerFactory.getLogger(MySQLMonitoringService.class);

    @Autowired
    private DataSource dataSource;

    @Autowired
    private MeterRegistry meterRegistry;

    @Autowired
    private UnifiedMonitoringConfiguration.AlertManager alertManager;

    // 监控指标
    private final AtomicLong activeConnections = new AtomicLong(0);
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong slowQueries = new AtomicLong(0);
    private final AtomicReference<Double> connectionUsagePercent = new AtomicReference<>(0.0);
    private final AtomicReference<Double> bufferPoolUsage = new AtomicReference<>(0.0);
    
    // 性能计数器
    private final Counter queryCounter;
    private final Counter errorCounter;
    private final Timer queryTimer;
    private final Gauge activeConnectionsGauge;
    private final Gauge connectionUsageGauge;
    private final Gauge bufferPoolGauge;

    public MySQLMonitoringService(MeterRegistry meterRegistry) {
        this.queryCounter = Counter.builder("mysql.queries.total")
                .description("MySQL查询总数")
                .register(meterRegistry);
        this.errorCounter = Counter.builder("mysql.errors.total")
                .description("MySQL错误总数")
                .register(meterRegistry);
        this.queryTimer = Timer.builder("mysql.query.duration")
                .description("MySQL查询执行时间")
                .register(meterRegistry);
        this.activeConnectionsGauge = Gauge.builder("mysql.connections.active", activeConnections, AtomicLong::doubleValue)
                .description("MySQL活跃连接数")
                .register(meterRegistry);
        this.connectionUsageGauge = Gauge.builder("mysql.connections.usage.percent", connectionUsagePercent, AtomicReference::get)
                .description("MySQL连接使用率")
                .register(meterRegistry);
        this.bufferPoolGauge = Gauge.builder("mysql.buffer.pool.usage.percent", bufferPoolUsage, AtomicReference::get)
                .description("MySQL缓冲池使用率")
                .register(meterRegistry);
        
        log.info("MySQL监控服务已初始化");
    }

    /**
     * 定期收集MySQL性能指标 - 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000)
    public void collectMySQLMetrics() {
        try {
            collectConnectionMetrics();
            collectPerformanceMetrics();
            collectBufferPoolMetrics();
            checkSlowQueries();
            
            // 检查告警条件
            checkAlertConditions();
            
        } catch (Exception e) {
            log.error("收集MySQL指标失败", e);
            Counter.builder("mysql.errors.total")
                    .tag("type", "metrics_collection")
                    .register(meterRegistry)
                    .increment();
        }
    }

    /**
     * 收集连接指标
     */
    private void collectConnectionMetrics() {
        // 优先从Hikari连接池获取最大连接数
        long maxConnections = 1L; // 默认值以防万一
        if (dataSource instanceof HikariDataSource) {
            maxConnections = ((HikariDataSource) dataSource).getMaximumPoolSize();
            log.debug("从HikariCP获取最大连接数: {}", maxConnections);
        }

        try (Connection connection = dataSource.getConnection()) {
            Timer.Sample sample = Timer.start(meterRegistry);
            Map<String, Long> metrics = new HashMap<>();

            // 查询状态变量
            String statusSql = "SHOW STATUS WHERE Variable_name IN ('Threads_connected', 'Threads_running')";
            try (PreparedStatement stmt = connection.prepareStatement(statusSql);
                 ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String variableName = rs.getString("Variable_name");
                    String valueStr = rs.getString("Value");
                    try {
                        if (valueStr != null && !valueStr.trim().isEmpty() && !valueStr.toLowerCase().contains("dumping") && !valueStr.toLowerCase().contains("not started")) {
                            Long value = Long.parseLong(valueStr.trim());
                            metrics.put(variableName, value);
                        } else {
                            log.debug("跳过非数字MySQL状态值: {} = {}", variableName, valueStr);
                        }
                    } catch (NumberFormatException e) {
                        log.debug("无法解析MySQL状态值: {} = {}, 跳过", variableName, valueStr);
                    }
                }
            }
            
            // 如果无法从HikariCP获取，则回退到查询数据库
            if (!(dataSource instanceof HikariDataSource)) {
                log.warn("DataSource不是HikariDataSource实例，回退到数据库查询获取max_connections");
                String varSql = "SHOW VARIABLES WHERE Variable_name = 'Max_connections'";
                try (PreparedStatement stmt = connection.prepareStatement(varSql);
                     ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        maxConnections = rs.getLong("Value");
                        log.debug("从数据库查询获取最大连接数: {}", maxConnections);
                    }
                } catch (SQLException ex) {
                    log.error("回退查询max_connections失败", ex);
                }
            }


            long threadsConnected = metrics.getOrDefault("Threads_connected", 0L);
            long threadsRunning = metrics.getOrDefault("Threads_running", 0L);

            activeConnections.set(threadsConnected);
            totalConnections.set(maxConnections);
            connectionUsagePercent.set(maxConnections > 0 ? (double) threadsConnected / maxConnections * 100 : 0.0);

            Counter.builder("mysql.queries.total")
                    .tag("type", "status")
                    .register(meterRegistry)
                    .increment();
            sample.stop(queryTimer);

            log.debug("MySQL连接指标 - 活跃: {}, 最大: {}, 使用率: {:.2f}%, 运行中: {}",
                    threadsConnected, maxConnections, connectionUsagePercent.get(), threadsRunning);

        } catch (SQLException e) {
            log.error("收集MySQL连接指标失败", e);
            Counter.builder("mysql.errors.total")
                    .tag("type", "connection_metrics")
                    .register(meterRegistry)
                    .increment();
        }
    }

    /**
     * 收集性能指标
     */
    private void collectPerformanceMetrics() {
        try (Connection connection = dataSource.getConnection()) {
            Timer.Sample sample = Timer.start(meterRegistry);
            
            // 查询性能状态
            String sql = "SHOW STATUS WHERE Variable_name IN ('Questions', 'Slow_queries', 'Uptime', 'Queries')";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                Map<String, Long> metrics = new HashMap<>();
                while (rs.next()) {
                    String variableName = rs.getString("Variable_name");
                    String valueStr = rs.getString("Value");
                    try {
                        // 安全解析数值，跳过非数字值
                        if (valueStr != null && !valueStr.trim().isEmpty() &&
                            !valueStr.toLowerCase().contains("dumping") &&
                            !valueStr.toLowerCase().contains("not started")) {
                            Long value = Long.parseLong(valueStr.trim());
                            metrics.put(variableName, value);
                        } else {
                            log.debug("跳过非数字MySQL状态值: {} = {}", variableName, valueStr);
                        }
                    } catch (NumberFormatException e) {
                        log.debug("无法解析MySQL状态值: {} = {}, 跳过", variableName, valueStr);
                    }
                }
                
                long currentSlowQueries = metrics.getOrDefault("Slow_queries", 0L);
                slowQueries.set(currentSlowQueries);
                
                Counter.builder("mysql.queries.total")
                        .tag("type", "performance")
                        .register(meterRegistry)
                        .increment();
                sample.stop(queryTimer);
                
                log.debug("MySQL性能指标 - 慢查询: {}", currentSlowQueries);
                
            }
        } catch (SQLException e) {
            log.error("收集MySQL性能指标失败", e);
            Counter.builder("mysql.errors.total")
                    .tag("type", "performance_metrics")
                    .register(meterRegistry)
                    .increment();
        }
    }

    /**
     * 收集缓冲池指标
     */
    private void collectBufferPoolMetrics() {
        try (Connection connection = dataSource.getConnection()) {
            Timer.Sample sample = Timer.start(meterRegistry);
            
            // 查询InnoDB缓冲池状态
            String sql = "SHOW STATUS WHERE Variable_name LIKE 'Innodb_buffer_pool_%'";
            try (PreparedStatement stmt = connection.prepareStatement(sql);
                 ResultSet rs = stmt.executeQuery()) {
                
                Map<String, Long> metrics = new HashMap<>();
                while (rs.next()) {
                    String variableName = rs.getString("Variable_name");
                    String valueStr = rs.getString("Value");
                    try {
                        // 安全解析数值，跳过非数字值
                        if (valueStr != null && !valueStr.trim().isEmpty() &&
                            !valueStr.toLowerCase().contains("dumping") &&
                            !valueStr.toLowerCase().contains("not started")) {
                            Long value = Long.parseLong(valueStr.trim());
                            metrics.put(variableName, value);
                        } else {
                            log.debug("跳过非数字MySQL状态值: {} = {}", variableName, valueStr);
                        }
                    } catch (NumberFormatException e) {
                        log.debug("无法解析MySQL状态值: {} = {}, 跳过", variableName, valueStr);
                    }
                }
                
                long totalPages = metrics.getOrDefault("Innodb_buffer_pool_pages_total", 1L);
                long freePages = metrics.getOrDefault("Innodb_buffer_pool_pages_free", 0L);
                long usedPages = totalPages - freePages;
                
                double usage = (double) usedPages / totalPages * 100;
                bufferPoolUsage.set(usage);
                
                Counter.builder("mysql.queries.total")
                        .tag("type", "buffer_pool")
                        .register(meterRegistry)
                        .increment();
                sample.stop(queryTimer);
                
                log.debug("MySQL缓冲池指标 - 总页数: {}, 已用: {}, 使用率: {:.2f}%", 
                        totalPages, usedPages, usage);
                
            }
        } catch (SQLException e) {
            log.error("收集MySQL缓冲池指标失败", e);
            Counter.builder("mysql.errors.total")
                    .tag("type", "buffer_pool_metrics")
                    .register(meterRegistry)
                    .increment();
        }
    }

    /**
     * 检查慢查询
     */
    private void checkSlowQueries() {
        long currentSlowQueries = slowQueries.get();
        if (currentSlowQueries > 0) {
            log.warn("检测到慢查询: {} 个", currentSlowQueries);
        }
    }

    /**
     * 检查告警条件
     */
    private void checkAlertConditions() {
        // 连接使用率告警
        double connUsage = connectionUsagePercent.get();
        if (connUsage > 80) {
            alertManager.sendCriticalAlert(
                    "MySQL连接使用率过高", 
                    "当前使用率: " + String.format("%.2f%%", connUsage),
                    "活跃连接: " + activeConnections.get(),
                    "最大连接: " + totalConnections.get()
            );
        } else if (connUsage > 60) {
            alertManager.sendWarningAlert(
                    "MySQL连接使用率较高", 
                    "当前使用率: " + String.format("%.2f%%", connUsage)
            );
        }

        // 缓冲池使用率告警
        double bufferUsage = bufferPoolUsage.get();
        if (bufferUsage > 95) {
            alertManager.sendWarningAlert(
                    "MySQL缓冲池使用率过高", 
                    "当前使用率: " + String.format("%.2f%%", bufferUsage)
            );
        }

        // 慢查询告警
        long slowQueryCount = slowQueries.get();
        if (slowQueryCount > 10) {
            alertManager.sendWarningAlert(
                    "MySQL慢查询数量过多", 
                    "慢查询数量: " + slowQueryCount
            );
        }
    }

    /**
     * 执行健康检查
     */
    public boolean isHealthy() {
        try (Connection connection = dataSource.getConnection()) {
            return connection.isValid(5);
        } catch (SQLException e) {
            log.error("MySQL健康检查失败", e);
            return false;
        }
    }

    /**
     * 获取连接使用率
     */
    public double getConnectionUsagePercent() {
        return connectionUsagePercent.get();
    }

    /**
     * 获取缓冲池使用率
     */
    public double getBufferPoolUsagePercent() {
        return bufferPoolUsage.get();
    }

    /**
     * 获取慢查询数量
     */
    public long getSlowQueriesCount() {
        return slowQueries.get();
    }

    /**
     * 记录查询执行
     */
    public Timer.Sample startQueryTimer() {
        return Timer.start(meterRegistry);
    }

    /**
     * 记录查询完成
     */
    public void recordQuery(String queryType) {
        Counter.builder("mysql.queries.total")
                .tag("type", queryType)
                .register(meterRegistry)
                .increment();
    }

    /**
     * 记录查询错误
     */
    public void recordError(String errorType) {
        Counter.builder("mysql.errors.total")
                .tag("type", errorType)
                .register(meterRegistry)
                .increment();
    }
}
