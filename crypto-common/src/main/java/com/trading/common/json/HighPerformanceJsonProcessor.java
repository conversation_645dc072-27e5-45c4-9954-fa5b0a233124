package com.trading.common.json;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.afterburner.AfterburnerModule;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.trading.common.pool.HighPerformanceObjectPool;
import com.trading.common.cache.OptimizedCacheConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.time.Duration;
import java.util.concurrent.atomic.LongAdder;
import java.util.List;
import java.util.Collections;
import java.util.ArrayList;
import java.math.BigDecimal;
import java.util.function.Function;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;
import java.util.HashMap;

/**
 * 统一的高性能JSON处理器
 * 整合了SimplifiedJsonProcessor和UltraFastJsonSerializer的功能
 * 集成Jackson Afterburner模块，实现对象池和缓存优化
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class HighPerformanceJsonProcessor {

    private static final Logger log = LoggerFactory.getLogger(HighPerformanceJsonProcessor.class);

    // ObjectMapper对象池
    private HighPerformanceObjectPool<ObjectMapper> objectMapperPool;
    
    // StringBuilder对象池
    private HighPerformanceObjectPool<StringBuilder> stringBuilderPool;

    // JSON序列化结果缓存
    private Cache<Object, String> serializationCache;
    
    // JSON反序列化结果缓存
    private Cache<String, Object> deserializationCache;

    // 性能统计
    private final LongAdder serializationCount = new LongAdder();
    private final LongAdder deserializationCount = new LongAdder();
    private final LongAdder cacheHits = new LongAdder();
    private final LongAdder cacheMisses = new LongAdder();
    private final LongAdder poolHits = new LongAdder();
    private final LongAdder poolMisses = new LongAdder();

    // BigDecimal常量池
    private OptimizedCacheConfiguration.BigDecimalPool bigDecimalPool;

    // 专用序列化器缓存 - 为常用类型提供专用序列化器
    private final ConcurrentHashMap<Class<?>, Function<Object, String>> serializerCache = new ConcurrentHashMap<>();

    // 小对象缓存 - 缓存常用的小JSON对象
    private final Map<Object, String> smallObjectCache = new ConcurrentHashMap<>(1000);

    @PostConstruct
    public void initialize() {
        log.info("初始化统一高性能JSON处理器...");

        // 初始化BigDecimal常量池
        bigDecimalPool = new OptimizedCacheConfiguration.BigDecimalPool();

        // 初始化ObjectMapper对象池
        initializeObjectMapperPool();

        // 初始化StringBuilder对象池
        initializeStringBuilderPool();

        // 初始化缓存
        initializeCaches();

        // 初始化专用序列化器
        initializeSpecializedSerializers();

        log.info("统一高性能JSON处理器初始化完成");
    }

    /**
     * 初始化ObjectMapper对象池
     */
    private void initializeObjectMapperPool() {
        objectMapperPool = new HighPerformanceObjectPool<>(
                "JsonObjectMapper",
                20, // 池大小
                this::createOptimizedObjectMapper,
                mapper -> {}, // ObjectMapper无需重置
                mapper -> {
                    if (mapper == null) {
                        throw new IllegalStateException("ObjectMapper不能为null");
                    }
                }
        );
        
        // 预热对象池
        objectMapperPool.warmUp(10);
    }

    /**
     * 初始化StringBuilder对象池
     */
    private void initializeStringBuilderPool() {
        stringBuilderPool = new HighPerformanceObjectPool<>(
                "JsonStringBuilder",
                50, // 池大小
                () -> new StringBuilder(2048), // 预分配2KB容量
                sb -> sb.setLength(0), // 重置长度
                sb -> {
                    if (sb == null || sb.capacity() > 16384) { // 限制最大容量16KB
                        throw new IllegalStateException("StringBuilder无效或容量过大");
                    }
                }
        );
        
        // 预热对象池
        stringBuilderPool.warmUp(25);
    }

    /**
     * 初始化缓存
     */
    private void initializeCaches() {
        // 序列化缓存 - 缓存对象到JSON字符串的映射
        serializationCache = Caffeine.newBuilder()
                .maximumSize(10000)
                .expireAfterWrite(Duration.ofMinutes(30))
                .recordStats()
                .build();

        // 反序列化缓存 - 缓存JSON字符串到对象的映射
        deserializationCache = Caffeine.newBuilder()
                .maximumSize(5000)
                .expireAfterWrite(Duration.ofMinutes(15))
                .recordStats()
                .build();
    }

    /**
     * 初始化专用序列化器
     */
    private void initializeSpecializedSerializers() {
        // 为String类型注册专用序列化器
        serializerCache.put(String.class, obj -> {
            String str = (String) obj;
            StringBuilder sb = stringBuilderPool.borrow();
            try {
                sb.append('"');
                escapeString(str, sb);
                sb.append('"');
                return sb.toString();
            } finally {
                stringBuilderPool.returnObject(sb);
            }
        });

        // 为Number类型注册专用序列化器
        serializerCache.put(Integer.class, obj -> obj.toString());
        serializerCache.put(Long.class, obj -> obj.toString());
        serializerCache.put(Double.class, obj -> obj.toString());
        serializerCache.put(BigDecimal.class, obj -> obj.toString());

        // 为Boolean类型注册专用序列化器
        serializerCache.put(Boolean.class, Object::toString);

        log.info("专用序列化器初始化完成，支持{}种类型", serializerCache.size());
    }

    /**
     * 创建优化的ObjectMapper
     */
    private ObjectMapper createOptimizedObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();

        // 注册Afterburner模块以提升性能30-40%
        mapper.registerModule(new AfterburnerModule());

        // 注册Java时间模块，支持JSR310时间类型
        mapper.registerModule(new JavaTimeModule());

        // 性能优化配置
        mapper.getFactory().disable(com.fasterxml.jackson.core.JsonGenerator.Feature.AUTO_CLOSE_TARGET);
        mapper.getFactory().disable(com.fasterxml.jackson.core.JsonParser.Feature.AUTO_CLOSE_SOURCE);

        // 序列化配置
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        mapper.configure(SerializationFeature.WRITE_BIGDECIMAL_AS_PLAIN, true); // BigDecimal不使用科学计数法

        // 反序列化配置
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true); // 浮点数使用BigDecimal
        mapper.configure(DeserializationFeature.USE_BIG_INTEGER_FOR_INTS, false);
        mapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);

        // 数字处理优化
        mapper.configure(com.fasterxml.jackson.core.JsonParser.Feature.ALLOW_NON_NUMERIC_NUMBERS, true);
        mapper.configure(com.fasterxml.jackson.core.JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN, true);

        log.debug("创建优化的ObjectMapper: Afterburner={}, JavaTime={}, BigDecimal优化={}",
                true, true, true);

        return mapper;
    }

    /**
     * 高性能序列化对象到JSON字符串
     * 整合了专用序列化器和缓存优化
     *
     * @param object 要序列化的对象
     * @return JSON字符串
     */
    public String serialize(Object object) {
        if (object == null) {
            return null;
        }

        serializationCount.increment();

        // 检查小对象缓存
        if (isSmallObject(object)) {
            String cached = smallObjectCache.get(object);
            if (cached != null) {
                cacheHits.increment();
                return cached;
            }
        }

        // 检查序列化缓存
        String cached = serializationCache.getIfPresent(object);
        if (cached != null) {
            cacheHits.increment();
            return cached;
        }

        cacheMisses.increment();

        // 检查是否有专用序列化器
        Class<?> clazz = object.getClass();
        Function<Object, String> serializer = serializerCache.get(clazz);

        if (serializer != null) {
            // 使用专用序列化器
            String result = serializer.apply(object);

            // 缓存小对象
            if (isSmallObject(object) && result.length() < 512) {
                smallObjectCache.put(object, result);
            }

            return result;
        }

        // 使用Jackson进行序列化
        ObjectMapper mapper = borrowObjectMapper();
        try {
            String json = mapper.writeValueAsString(object);

            // 缓存结果（仅缓存小于1KB的JSON）
            if (json.length() < 1024) {
                serializationCache.put(object, json);

                // 同时缓存小对象
                if (isSmallObject(object) && json.length() < 512) {
                    smallObjectCache.put(object, json);
                }
            }

            return json;
        } catch (JsonProcessingException e) {
            log.error("JSON序列化失败: object={}", object.getClass().getSimpleName(), e);
            throw new RuntimeException("JSON序列化失败", e);
        } finally {
            returnObjectMapper(mapper);
        }
    }

    /**
     * 高性能反序列化JSON字符串到对象
     * 
     * @param json JSON字符串
     * @param clazz 目标类型
     * @return 反序列化的对象
     */
    public <T> T deserialize(String json, Class<T> clazz) {
        if (json == null || json.isEmpty()) {
            return null;
        }

        deserializationCount.increment();

        // 生成缓存键
        String cacheKey = json + ":" + clazz.getName();
        
        // 检查缓存
        @SuppressWarnings("unchecked")
        T cached = (T) deserializationCache.getIfPresent(cacheKey);
        if (cached != null) {
            cacheHits.increment();
            return cached;
        }

        cacheMisses.increment();

        ObjectMapper mapper = borrowObjectMapper();
        try {
            T result = mapper.readValue(json, clazz);
            
            // 缓存结果（仅缓存小于1KB的JSON）
            if (json.length() < 1024) {
                deserializationCache.put(cacheKey, result);
            }
            
            return result;
        } catch (JsonProcessingException e) {
            log.error("JSON反序列化失败: json={}, class={}", 
                    json.length() > 100 ? json.substring(0, 100) + "..." : json, 
                    clazz.getSimpleName(), e);
            throw new RuntimeException("JSON反序列化失败", e);
        } finally {
            returnObjectMapper(mapper);
        }
    }

    public <T> T deserialize(String json, com.fasterxml.jackson.core.type.TypeReference<T> typeReference) {
        if (json == null || json.isEmpty()) {
            return null;
        }

        deserializationCount.increment();

        // 生成缓存键
        String cacheKey = json + ":" + typeReference.getType().getTypeName();

        // 检查缓存
        @SuppressWarnings("unchecked")
        T cached = (T) deserializationCache.getIfPresent(cacheKey);
        if (cached != null) {
            cacheHits.increment();
            return cached;
        }

        cacheMisses.increment();

        ObjectMapper mapper = borrowObjectMapper();
        try {
            T result = mapper.readValue(json, typeReference);

            // 缓存结果（仅缓存小于1KB的JSON）
            if (json.length() < 1024) {
                deserializationCache.put(cacheKey, result);
            }

            return result;
        } catch (JsonProcessingException e) {
            log.error("JSON反序列化失败: json={}, typeReference={}",
                    json.length() > 100 ? json.substring(0, 100) + "..." : json,
                    typeReference.getType().getTypeName(), e);
            throw new RuntimeException("JSON反序列化失败", e);
        } finally {
            returnObjectMapper(mapper);
        }
    }

    /**
     * 解析JSON字符串为JsonNode
     * 
     * @param json JSON字符串
     * @return JsonNode对象
     */
    public JsonNode parseToJsonNode(String json) {
        if (json == null || json.isEmpty()) {
            return null;
        }

        ObjectMapper mapper = borrowObjectMapper();
        try {
            return mapper.readTree(json);
        } catch (JsonProcessingException e) {
            log.error("JSON解析失败: json={}", 
                    json.length() > 100 ? json.substring(0, 100) + "..." : json, e);
            throw new RuntimeException("JSON解析失败", e);
        } finally {
            returnObjectMapper(mapper);
        }
    }

    /**
     * 从对象池借用ObjectMapper
     */
    private ObjectMapper borrowObjectMapper() {
        poolHits.increment();
        return objectMapperPool.borrow();
    }

    /**
     * 归还ObjectMapper到对象池
     */
    private void returnObjectMapper(ObjectMapper mapper) {
        objectMapperPool.returnObject(mapper);
    }

    /**
     * 从对象池借用StringBuilder
     */
    public StringBuilder borrowStringBuilder() {
        return stringBuilderPool.borrow();
    }

    /**
     * 归还StringBuilder到对象池
     */
    public void returnStringBuilder(StringBuilder sb) {
        stringBuilderPool.returnObject(sb);
    }

    /**
     * 清理缓存
     */
    public void clearCaches() {
        serializationCache.invalidateAll();
        deserializationCache.invalidateAll();
        smallObjectCache.clear();
        log.info("JSON处理器缓存已清理");
    }

    /**
     * 判断是否为小对象（适合缓存）
     */
    private boolean isSmallObject(Object obj) {
        Class<?> clazz = obj.getClass();
        return clazz == String.class ||
               clazz == Integer.class ||
               clazz == Long.class ||
               clazz == Boolean.class ||
               clazz == Double.class ||
               clazz == BigDecimal.class;
    }

    /**
     * 转义JSON字符串
     */
    private void escapeString(String str, StringBuilder sb) {
        for (int i = 0; i < str.length(); i++) {
            char c = str.charAt(i);
            switch (c) {
                case '"':
                    sb.append("\\\"");
                    break;
                case '\\':
                    sb.append("\\\\");
                    break;
                case '\b':
                    sb.append("\\b");
                    break;
                case '\f':
                    sb.append("\\f");
                    break;
                case '\n':
                    sb.append("\\n");
                    break;
                case '\r':
                    sb.append("\\r");
                    break;
                case '\t':
                    sb.append("\\t");
                    break;
                default:
                    if (c < ' ') {
                        sb.append(String.format("\\u%04x", (int) c));
                    } else {
                        sb.append(c);
                    }
            }
        }
    }

    /**
     * 获取BigDecimal常量池
     */
    public OptimizedCacheConfiguration.BigDecimalPool getBigDecimalPool() {
        return bigDecimalPool;
    }

    /**
     * 优化的BigDecimal序列化
     * 使用常量池避免重复创建
     */
    public String serializeBigDecimal(java.math.BigDecimal value) {
        if (value == null) {
            return "null";
        }

        // 对于常用值，直接返回字符串常量
        if (value.equals(java.math.BigDecimal.ZERO)) {
            return "0";
        } else if (value.equals(java.math.BigDecimal.ONE)) {
            return "1";
        } else if (value.equals(java.math.BigDecimal.TEN)) {
            return "10";
        }

        // 使用plain string避免科学计数法
        return value.toPlainString();
    }

    /**
     * 优化的BigDecimal反序列化
     * 使用常量池避免重复创建
     */
    public java.math.BigDecimal deserializeBigDecimal(String value) {
        if (value == null || "null".equals(value)) {
            return null;
        }

        return bigDecimalPool.valueOf(value);
    }

    /**
     * 批量序列化
     * 优化多个对象的序列化性能
     */
    public java.util.List<String> serializeBatch(java.util.List<Object> objects) {
        if (objects == null || objects.isEmpty()) {
            return java.util.Collections.emptyList();
        }

        java.util.List<String> results = new java.util.ArrayList<>(objects.size());
        ObjectMapper mapper = borrowObjectMapper();

        try {
            for (Object obj : objects) {
                if (obj == null) {
                    results.add(null);
                } else {
                    String json = mapper.writeValueAsString(obj);
                    results.add(json);
                    serializationCount.increment();
                }
            }
            return results;
        } catch (JsonProcessingException e) {
            log.error("批量JSON序列化失败", e);
            throw new RuntimeException("批量JSON序列化失败", e);
        } finally {
            returnObjectMapper(mapper);
        }
    }

    /**
     * 批量反序列化
     * 优化多个JSON字符串的反序列化性能
     */
    public <T> List<T> deserializeBatch(List<String> jsonList, Class<T> clazz) {
        if (jsonList == null || jsonList.isEmpty()) {
            return Collections.emptyList();
        }

        List<T> results = new ArrayList<>(jsonList.size());
        ObjectMapper mapper = borrowObjectMapper();

        try {
            for (String json : jsonList) {
                if (json == null) {
                    results.add(null);
                } else {
                    T obj = mapper.readValue(json, clazz);
                    results.add(obj);
                    deserializationCount.increment();
                }
            }
            return results;
        } catch (JsonProcessingException e) {
            log.error("批量JSON反序列化失败", e);
            throw new RuntimeException("批量JSON反序列化失败", e);
        } finally {
            returnObjectMapper(mapper);
        }
    }

    /**
     * 检查JSON字符串是否有效
     *
     * @param json JSON字符串
     * @return true表示有效，false表示无效
     */
    public boolean isValidJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return false;
        }

        try {
            parseToJsonNode(json);
            return true;
        } catch (RuntimeException e) {
            return false;
        }
    }

    /**
     * 格式化JSON字符串（美化输出）
     *
     * @param json 原始JSON字符串
     * @return 格式化后的JSON字符串
     */
    public String prettyPrint(String json) {
        if (json == null || json.isEmpty()) {
            return json;
        }

        ObjectMapper mapper = borrowObjectMapper();
        try {
            JsonNode jsonNode = mapper.readTree(json);
            return mapper.writerWithDefaultPrettyPrinter().writeValueAsString(jsonNode);
        } catch (JsonProcessingException e) {
            log.warn("JSON格式化失败: {}", e.getMessage());
            return json; // 返回原始字符串
        } finally {
            returnObjectMapper(mapper);
        }
    }

    /**
     * 将对象转换为JsonNode
     *
     * @param object 要转换的对象
     * @return JsonNode对象
     */
    public JsonNode convertToJsonNode(Object object) {
        if (object == null) {
            return null;
        }

        ObjectMapper mapper = borrowObjectMapper();
        try {
            return mapper.valueToTree(object);
        } finally {
            returnObjectMapper(mapper);
        }
    }

    /**
     * 将JsonNode转换为指定类型对象
     *
     * @param jsonNode JsonNode对象
     * @param clazz 目标类型
     * @param <T> 泛型类型
     * @return 转换后的对象
     */
    public <T> T convertFromJsonNode(JsonNode jsonNode, Class<T> clazz) {
        if (jsonNode == null) {
            return null;
        }

        ObjectMapper mapper = borrowObjectMapper();
        try {
            return mapper.treeToValue(jsonNode, clazz);
        } catch (JsonProcessingException e) {
            log.error("JsonNode转换失败: class={}", clazz.getSimpleName(), e);
            throw new RuntimeException("JsonNode转换失败", e);
        } finally {
            returnObjectMapper(mapper);
        }
    }

    /**
     * 深拷贝对象（通过JSON序列化/反序列化）
     *
     * @param object 要拷贝的对象
     * @param clazz 对象类型
     * @param <T> 泛型类型
     * @return 深拷贝后的对象
     */
    public <T> T deepCopy(T object, Class<T> clazz) {
        if (object == null) {
            return null;
        }

        String json = serialize(object);
        return deserialize(json, clazz);
    }

    /**
     * 获取性能统计信息
     */
    public JsonProcessorStatistics getStatistics() {
        return new JsonProcessorStatistics(
                serializationCount.sum(),
                deserializationCount.sum(),
                cacheHits.sum(),
                cacheMisses.sum(),
                poolHits.sum(),
                poolMisses.sum(),
                serializationCache.stats(),
                deserializationCache.stats(),
                objectMapperPool.getStatistics(),
                stringBuilderPool.getStatistics(),
                smallObjectCache.size(),
                serializerCache.size()
        );
    }

    /**
     * 获取详细的性能统计信息（Map格式）
     */
    public Map<String, Object> getPerformanceStats() {
        Map<String, Object> stats = new HashMap<>();

        long totalCount = serializationCount.sum();
        long hits = cacheHits.sum();
        long misses = cacheMisses.sum();
        double hitRate = totalCount > 0 ? (double) hits / totalCount * 100 : 0;

        stats.put("serializationCount", totalCount);
        stats.put("deserializationCount", deserializationCount.sum());
        stats.put("cacheHits", hits);
        stats.put("cacheMisses", misses);
        stats.put("cacheHitRate", String.format("%.2f%%", hitRate));
        stats.put("mapperPoolSize", objectMapperPool.getStatistics().getCurrentSize());
        stats.put("stringBuilderPoolSize", stringBuilderPool.getStatistics().getCurrentSize());
        stats.put("smallObjectCacheSize", smallObjectCache.size());
        stats.put("serializerCacheSize", serializerCache.size());

        return stats;
    }

    /**
     * JSON处理器统计信息
     */
    public static class JsonProcessorStatistics {
        private final long serializationCount;
        private final long deserializationCount;
        private final long cacheHits;
        private final long cacheMisses;
        private final long poolHits;
        private final long poolMisses;
        private final com.github.benmanes.caffeine.cache.stats.CacheStats serializationCacheStats;
        private final com.github.benmanes.caffeine.cache.stats.CacheStats deserializationCacheStats;
        private final HighPerformanceObjectPool.PoolStatistics objectMapperPoolStats;
        private final HighPerformanceObjectPool.PoolStatistics stringBuilderPoolStats;
        private final int smallObjectCacheSize;
        private final int serializerCacheSize;

        public JsonProcessorStatistics(long serializationCount, long deserializationCount,
                                     long cacheHits, long cacheMisses, long poolHits, long poolMisses,
                                     com.github.benmanes.caffeine.cache.stats.CacheStats serializationCacheStats,
                                     com.github.benmanes.caffeine.cache.stats.CacheStats deserializationCacheStats,
                                     HighPerformanceObjectPool.PoolStatistics objectMapperPoolStats,
                                     HighPerformanceObjectPool.PoolStatistics stringBuilderPoolStats,
                                     int smallObjectCacheSize, int serializerCacheSize) {
            this.serializationCount = serializationCount;
            this.deserializationCount = deserializationCount;
            this.cacheHits = cacheHits;
            this.cacheMisses = cacheMisses;
            this.poolHits = poolHits;
            this.poolMisses = poolMisses;
            this.serializationCacheStats = serializationCacheStats;
            this.deserializationCacheStats = deserializationCacheStats;
            this.objectMapperPoolStats = objectMapperPoolStats;
            this.stringBuilderPoolStats = stringBuilderPoolStats;
            this.smallObjectCacheSize = smallObjectCacheSize;
            this.serializerCacheSize = serializerCacheSize;
        }

        // Getters
        public long getSerializationCount() { return serializationCount; }
        public long getDeserializationCount() { return deserializationCount; }
        public long getCacheHits() { return cacheHits; }
        public long getCacheMisses() { return cacheMisses; }
        public long getPoolHits() { return poolHits; }
        public long getPoolMisses() { return poolMisses; }

        public double getCacheHitRate() {
            long total = cacheHits + cacheMisses;
            return total == 0 ? 0.0 : (double) cacheHits / total;
        }

        public double getPoolHitRate() {
            long total = poolHits + poolMisses;
            return total == 0 ? 0.0 : (double) poolHits / total;
        }

        public int getSmallObjectCacheSize() { return smallObjectCacheSize; }
        public int getSerializerCacheSize() { return serializerCacheSize; }

        @Override
        public String toString() {
            return String.format("JsonProcessor: serializations=%d, deserializations=%d, " +
                            "cacheHitRate=%.2f%%, poolHitRate=%.2f%%, smallCache=%d, serializers=%d",
                    serializationCount, deserializationCount,
                    getCacheHitRate() * 100, getPoolHitRate() * 100,
                    smallObjectCacheSize, serializerCacheSize);
        }
    }
}
