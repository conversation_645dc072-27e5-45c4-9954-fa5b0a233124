package com.trading.common.metrics;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * WebSocket性能监控指标
 * 收集和统计WebSocket连接的性能数据
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class WebSocketMetrics {
    
    private static final Logger log = LoggerFactory.getLogger(WebSocketMetrics.class);
    
    // 连接统计
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong activeConnections = new AtomicLong(0);
    private final AtomicLong failedConnections = new AtomicLong(0);
    private final AtomicLong reconnections = new AtomicLong(0);
    
    // 消息统计
    private final AtomicLong totalMessages = new AtomicLong(0);
    private final AtomicLong messagesPerSecond = new AtomicLong(0);
    private final AtomicLong lastMessageTime = new AtomicLong(System.currentTimeMillis());
    
    // 延迟统计
    private final AtomicLong totalLatency = new AtomicLong(0);
    private final AtomicLong minLatency = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong maxLatency = new AtomicLong(0);
    
    // 按订阅类型分类的统计
    private final ConcurrentHashMap<String, SubscriptionStats> subscriptionStats = new ConcurrentHashMap<>();
    
    // 错误统计
    private final ConcurrentHashMap<String, AtomicLong> errorCounts = new ConcurrentHashMap<>();
    
    // 启动时间
    private final Instant startTime = Instant.now();
    
    public WebSocketMetrics() {
        log.info("WebSocket性能监控初始化完成");
    }
    
    /**
     * 记录连接建立
     * 
     * @param subscriptionKey 订阅键
     */
    public void recordConnection(String subscriptionKey) {
        totalConnections.incrementAndGet();
        activeConnections.incrementAndGet();
        
        subscriptionStats.computeIfAbsent(subscriptionKey, k -> new SubscriptionStats())
                .incrementConnections();
        
        log.debug("WebSocket连接建立: {}", subscriptionKey);
    }
    
    /**
     * 记录连接断开
     * 
     * @param subscriptionKey 订阅键
     */
    public void recordDisconnection(String subscriptionKey) {
        activeConnections.decrementAndGet();
        
        SubscriptionStats stats = subscriptionStats.get(subscriptionKey);
        if (stats != null) {
            stats.incrementDisconnections();
        }
        
        log.debug("WebSocket连接断开: {}", subscriptionKey);
    }
    
    /**
     * 记录连接失败
     * 
     * @param subscriptionKey 订阅键
     * @param errorType 错误类型
     */
    public void recordConnectionFailure(String subscriptionKey, String errorType) {
        failedConnections.incrementAndGet();
        
        SubscriptionStats stats = subscriptionStats.get(subscriptionKey);
        if (stats != null) {
            stats.incrementFailures();
        }
        
        errorCounts.computeIfAbsent(errorType, k -> new AtomicLong(0)).incrementAndGet();
        
        log.warn("WebSocket连接失败: {}, 错误类型: {}", subscriptionKey, errorType);
    }
    
    /**
     * 记录重连
     * 
     * @param subscriptionKey 订阅键
     */
    public void recordReconnection(String subscriptionKey) {
        reconnections.incrementAndGet();
        
        SubscriptionStats stats = subscriptionStats.get(subscriptionKey);
        if (stats != null) {
            stats.incrementReconnections();
        }
        
        log.info("WebSocket重连: {}", subscriptionKey);
    }
    
    /**
     * 记录消息接收
     * 
     * @param subscriptionKey 订阅键
     * @param latency 延迟（毫秒）
     */
    public void recordMessage(String subscriptionKey, long latency) {
        totalMessages.incrementAndGet();
        lastMessageTime.set(System.currentTimeMillis());
        
        // 更新延迟统计
        totalLatency.addAndGet(latency);
        minLatency.updateAndGet(current -> Math.min(current, latency));
        maxLatency.updateAndGet(current -> Math.max(current, latency));
        
        // 更新订阅统计
        SubscriptionStats stats = subscriptionStats.get(subscriptionKey);
        if (stats != null) {
            stats.incrementMessages();
            stats.addLatency(latency);
        }
        
        log.debug("WebSocket消息接收: {}, 延迟: {}ms", subscriptionKey, latency);
    }
    
    /**
     * 获取总体统计信息
     * 
     * @return 总体统计信息
     */
    public WebSocketOverallStats getOverallStats() {
        long totalMsg = totalMessages.get();
        long avgLatency = totalMsg > 0 ? totalLatency.get() / totalMsg : 0;
        long minLat = minLatency.get();
        long maxLat = maxLatency.get();
        
        return WebSocketOverallStats.builder()
                .totalConnections(totalConnections.get())
                .activeConnections(activeConnections.get())
                .failedConnections(failedConnections.get())
                .reconnections(reconnections.get())
                .totalMessages(totalMsg)
                .averageLatency(avgLatency)
                .minLatency(minLat == Long.MAX_VALUE ? 0 : minLat)
                .maxLatency(maxLat)
                .uptime(Duration.between(startTime, Instant.now()))
                .messagesPerSecond(calculateMessagesPerSecond())
                .build();
    }
    
    /**
     * 获取订阅统计信息
     * 
     * @param subscriptionKey 订阅键
     * @return 订阅统计信息
     */
    public SubscriptionStats getSubscriptionStats(String subscriptionKey) {
        SubscriptionStats stats = subscriptionStats.get(subscriptionKey);
        return stats != null ? stats.copy() : new SubscriptionStats();
    }
    
    /**
     * 获取错误统计信息
     * 
     * @return 错误统计信息
     */
    public ConcurrentHashMap<String, Long> getErrorStats() {
        ConcurrentHashMap<String, Long> result = new ConcurrentHashMap<>();
        errorCounts.forEach((key, value) -> result.put(key, value.get()));
        return result;
    }
    
    /**
     * 计算每秒消息数
     * 
     * @return 每秒消息数
     */
    private double calculateMessagesPerSecond() {
        long uptimeSeconds = Duration.between(startTime, Instant.now()).getSeconds();
        if (uptimeSeconds == 0) {
            return 0.0;
        }
        return (double) totalMessages.get() / uptimeSeconds;
    }
    
    /**
     * 检查WebSocket是否健康
     * 
     * @return 是否健康
     */
    public boolean isHealthy() {
        // 检查是否有活跃连接
        if (activeConnections.get() == 0) {
            return false;
        }
        
        // 检查最近是否有消息
        long timeSinceLastMessage = System.currentTimeMillis() - lastMessageTime.get();
        if (timeSinceLastMessage > 60000) { // 超过1分钟没有消息
            return false;
        }
        
        // 检查连接成功率
        long total = totalConnections.get();
        long failed = failedConnections.get();
        if (total > 0 && (double) failed / total > 0.1) { // 失败率超过10%
            return false;
        }
        
        return true;
    }
    
    /**
     * 重置所有统计信息
     */
    public void reset() {
        totalConnections.set(0);
        activeConnections.set(0);
        failedConnections.set(0);
        reconnections.set(0);
        totalMessages.set(0);
        totalLatency.set(0);
        minLatency.set(Long.MAX_VALUE);
        maxLatency.set(0);
        
        subscriptionStats.clear();
        errorCounts.clear();
        
        log.info("WebSocket性能监控统计信息已重置");
    }
    
    /**
     * 获取性能报告
     * 
     * @return 性能报告字符串
     */
    public String getPerformanceReport() {
        WebSocketOverallStats overall = getOverallStats();
        
        StringBuilder report = new StringBuilder();
        report.append("=== WebSocket性能报告 ===\n");
        report.append(String.format("总连接数: %d\n", overall.getTotalConnections()));
        report.append(String.format("活跃连接数: %d\n", overall.getActiveConnections()));
        report.append(String.format("失败连接数: %d\n", overall.getFailedConnections()));
        report.append(String.format("重连次数: %d\n", overall.getReconnections()));
        report.append(String.format("总消息数: %d\n", overall.getTotalMessages()));
        report.append(String.format("平均延迟: %dms\n", overall.getAverageLatency()));
        report.append(String.format("最小延迟: %dms\n", overall.getMinLatency()));
        report.append(String.format("最大延迟: %dms\n", overall.getMaxLatency()));
        report.append(String.format("每秒消息数: %.2f\n", overall.getMessagesPerSecond()));
        report.append(String.format("运行时间: %s\n", formatDuration(overall.getUptime())));
        
        return report.toString();
    }
    
    /**
     * 格式化持续时间
     * 
     * @param duration 持续时间
     * @return 格式化字符串
     */
    private String formatDuration(Duration duration) {
        long hours = duration.toHours();
        long minutes = duration.toMinutesPart();
        long seconds = duration.toSecondsPart();
        
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }
}
