package com.trading.common.metrics;

import com.trading.common.enums.ApiRequestWeight;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.time.Instant;

/**
 * API请求上下文
 * 用于跟踪单个API请求的执行信息
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@AllArgsConstructor
public class RequestContext {
    
    /**
     * API端点
     */
    private String endpoint;
    
    /**
     * 请求权重
     */
    private ApiRequestWeight weight;
    
    /**
     * 请求开始时间
     */
    private Instant startTime;
    
    /**
     * 获取请求已执行时间（毫秒）
     * 
     * @return 已执行时间
     */
    public long getElapsedTime() {
        return java.time.Duration.between(startTime, Instant.now()).toMillis();
    }
    
    /**
     * 检查请求是否超时
     * 
     * @param timeoutMs 超时时间（毫秒）
     * @return 是否超时
     */
    public boolean isTimeout(long timeoutMs) {
        return getElapsedTime() > timeoutMs;
    }
}
