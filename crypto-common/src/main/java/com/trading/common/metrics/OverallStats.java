package com.trading.common.metrics;

import lombok.Builder;
import lombok.Data;

import java.time.Duration;

/**
 * 总体统计信息
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
public class OverallStats {
    
    /**
     * 总请求数
     */
    private long totalRequests;
    
    /**
     * 成功请求数
     */
    private long successfulRequests;
    
    /**
     * 失败请求数
     */
    private long failedRequests;
    
    /**
     * 成功率
     */
    private double successRate;
    
    /**
     * 平均响应时间（毫秒）
     */
    private long averageResponseTime;
    
    /**
     * 运行时间
     */
    private Duration uptime;
    
    /**
     * 每秒请求数
     */
    private double requestsPerSecond;
    
    /**
     * 获取失败率
     * 
     * @return 失败率
     */
    public double getFailureRate() {
        return 1.0 - successRate;
    }
    
    /**
     * 检查是否健康
     * 
     * @return 是否健康
     */
    public boolean isHealthy() {
        return successRate > 0.95 && averageResponseTime < 1000;
    }
    
    @Override
    public String toString() {
        return String.format(
                "OverallStats{total=%d, success=%d, failed=%d, successRate=%.2f%%, " +
                "avgResponseTime=%dms, uptime=%s, rps=%.2f}",
                totalRequests, successfulRequests, failedRequests,
                successRate * 100, averageResponseTime, formatUptime(), requestsPerSecond
        );
    }
    
    /**
     * 格式化运行时间
     * 
     * @return 格式化的运行时间
     */
    private String formatUptime() {
        if (uptime == null) {
            return "N/A";
        }
        
        long hours = uptime.toHours();
        long minutes = uptime.toMinutesPart();
        long seconds = uptime.toSecondsPart();
        
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }
}
