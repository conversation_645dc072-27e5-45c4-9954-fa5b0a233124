package com.trading.common.metrics;

import lombok.Data;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 请求统计信息
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
public class RequestStats {
    
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final LongAdder totalResponseTime = new LongAdder();
    private final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong maxResponseTime = new AtomicLong(0);
    
    /**
     * 增加请求数
     */
    public void incrementRequests() {
        totalRequests.incrementAndGet();
    }
    
    /**
     * 增加成功数
     */
    public void incrementSuccess() {
        successfulRequests.incrementAndGet();
    }
    
    /**
     * 增加失败数
     */
    public void incrementFailure() {
        failedRequests.incrementAndGet();
    }
    
    /**
     * 添加响应时间
     * 
     * @param responseTime 响应时间（毫秒）
     */
    public void addResponseTime(long responseTime) {
        totalResponseTime.add(responseTime);
        
        // 更新最小响应时间
        minResponseTime.updateAndGet(current -> Math.min(current, responseTime));
        
        // 更新最大响应时间
        maxResponseTime.updateAndGet(current -> Math.max(current, responseTime));
    }
    
    /**
     * 获取总请求数
     * 
     * @return 总请求数
     */
    public long getTotalRequests() {
        return totalRequests.get();
    }
    
    /**
     * 获取成功请求数
     * 
     * @return 成功请求数
     */
    public long getSuccessfulRequests() {
        return successfulRequests.get();
    }
    
    /**
     * 获取失败请求数
     * 
     * @return 失败请求数
     */
    public long getFailedRequests() {
        return failedRequests.get();
    }
    
    /**
     * 获取成功率
     * 
     * @return 成功率（0-1之间）
     */
    public double getSuccessRate() {
        long total = totalRequests.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) successfulRequests.get() / total;
    }
    
    /**
     * 获取失败率
     * 
     * @return 失败率（0-1之间）
     */
    public double getFailureRate() {
        return 1.0 - getSuccessRate();
    }
    
    /**
     * 获取平均响应时间
     * 
     * @return 平均响应时间（毫秒）
     */
    public long getAverageResponseTime() {
        long total = totalRequests.get();
        if (total == 0) {
            return 0;
        }
        return totalResponseTime.sum() / total;
    }
    
    /**
     * 获取最小响应时间
     * 
     * @return 最小响应时间（毫秒）
     */
    public long getMinResponseTime() {
        long min = minResponseTime.get();
        return min == Long.MAX_VALUE ? 0 : min;
    }
    
    /**
     * 获取最大响应时间
     * 
     * @return 最大响应时间（毫秒）
     */
    public long getMaxResponseTime() {
        return maxResponseTime.get();
    }
    
    /**
     * 重置统计信息
     */
    public void reset() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        totalResponseTime.reset();
        minResponseTime.set(Long.MAX_VALUE);
        maxResponseTime.set(0);
    }
    
    /**
     * 复制统计信息
     * 
     * @return 统计信息副本
     */
    public RequestStats copy() {
        RequestStats copy = new RequestStats();
        copy.totalRequests.set(this.totalRequests.get());
        copy.successfulRequests.set(this.successfulRequests.get());
        copy.failedRequests.set(this.failedRequests.get());
        copy.totalResponseTime.add(this.totalResponseTime.sum());
        copy.minResponseTime.set(this.minResponseTime.get());
        copy.maxResponseTime.set(this.maxResponseTime.get());
        return copy;
    }
    
    @Override
    public String toString() {
        return String.format(
                "RequestStats{total=%d, success=%d, failed=%d, successRate=%.2f%%, " +
                "avgResponseTime=%dms, minResponseTime=%dms, maxResponseTime=%dms}",
                getTotalRequests(), getSuccessfulRequests(), getFailedRequests(),
                getSuccessRate() * 100, getAverageResponseTime(),
                getMinResponseTime(), getMaxResponseTime()
        );
    }
}
