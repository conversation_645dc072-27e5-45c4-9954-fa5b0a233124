package com.trading.common.metrics;

import lombok.Builder;
import lombok.Data;

/**
 * 响应时间分布统计
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
public class ResponseTimeDistribution {
    
    /**
     * 0-100ms范围的请求数
     */
    private long range_0_100ms;
    
    /**
     * 100-500ms范围的请求数
     */
    private long range_100_500ms;
    
    /**
     * 500-1000ms范围的请求数
     */
    private long range_500_1000ms;
    
    /**
     * 1000-5000ms范围的请求数
     */
    private long range_1000_5000ms;
    
    /**
     * 超过5000ms的请求数
     */
    private long range_over_5000ms;
    
    /**
     * 获取总请求数
     * 
     * @return 总请求数
     */
    public long getTotalRequests() {
        return range_0_100ms + range_100_500ms + range_500_1000ms + 
               range_1000_5000ms + range_over_5000ms;
    }
    
    /**
     * 获取快速响应比例（<100ms）
     * 
     * @return 快速响应比例
     */
    public double getFastResponseRate() {
        long total = getTotalRequests();
        return total > 0 ? (double) range_0_100ms / total : 0.0;
    }
    
    /**
     * 获取慢响应比例（>1000ms）
     * 
     * @return 慢响应比例
     */
    public double getSlowResponseRate() {
        long total = getTotalRequests();
        long slowRequests = range_1000_5000ms + range_over_5000ms;
        return total > 0 ? (double) slowRequests / total : 0.0;
    }
    
    /**
     * 获取超时响应比例（>5000ms）
     * 
     * @return 超时响应比例
     */
    public double getTimeoutResponseRate() {
        long total = getTotalRequests();
        return total > 0 ? (double) range_over_5000ms / total : 0.0;
    }
    
    @Override
    public String toString() {
        long total = getTotalRequests();
        if (total == 0) {
            return "ResponseTimeDistribution{no data}";
        }
        
        return String.format(
                "ResponseTimeDistribution{0-100ms: %d(%.1f%%), 100-500ms: %d(%.1f%%), " +
                "500-1000ms: %d(%.1f%%), 1000-5000ms: %d(%.1f%%), >5000ms: %d(%.1f%%)}",
                range_0_100ms, (double) range_0_100ms / total * 100,
                range_100_500ms, (double) range_100_500ms / total * 100,
                range_500_1000ms, (double) range_500_1000ms / total * 100,
                range_1000_5000ms, (double) range_1000_5000ms / total * 100,
                range_over_5000ms, (double) range_over_5000ms / total * 100
        );
    }
}
