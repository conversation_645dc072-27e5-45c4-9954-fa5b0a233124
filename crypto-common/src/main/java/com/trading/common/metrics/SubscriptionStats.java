package com.trading.common.metrics;

import lombok.Data;

import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 订阅统计信息
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
public class SubscriptionStats {
    
    private final AtomicLong connections = new AtomicLong(0);
    private final AtomicLong disconnections = new AtomicLong(0);
    private final AtomicLong failures = new AtomicLong(0);
    private final AtomicLong reconnections = new AtomicLong(0);
    private final AtomicLong messages = new AtomicLong(0);
    private final LongAdder totalLatency = new LongAdder();
    private final AtomicLong minLatency = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong maxLatency = new AtomicLong(0);
    
    /**
     * 增加连接数
     */
    public void incrementConnections() {
        connections.incrementAndGet();
    }
    
    /**
     * 增加断开连接数
     */
    public void incrementDisconnections() {
        disconnections.incrementAndGet();
    }
    
    /**
     * 增加失败数
     */
    public void incrementFailures() {
        failures.incrementAndGet();
    }
    
    /**
     * 增加重连数
     */
    public void incrementReconnections() {
        reconnections.incrementAndGet();
    }
    
    /**
     * 增加消息数
     */
    public void incrementMessages() {
        messages.incrementAndGet();
    }
    
    /**
     * 添加延迟
     * 
     * @param latency 延迟（毫秒）
     */
    public void addLatency(long latency) {
        totalLatency.add(latency);
        minLatency.updateAndGet(current -> Math.min(current, latency));
        maxLatency.updateAndGet(current -> Math.max(current, latency));
    }
    
    /**
     * 获取连接数
     * 
     * @return 连接数
     */
    public long getConnections() {
        return connections.get();
    }
    
    /**
     * 获取断开连接数
     * 
     * @return 断开连接数
     */
    public long getDisconnections() {
        return disconnections.get();
    }
    
    /**
     * 获取失败数
     * 
     * @return 失败数
     */
    public long getFailures() {
        return failures.get();
    }
    
    /**
     * 获取重连数
     * 
     * @return 重连数
     */
    public long getReconnections() {
        return reconnections.get();
    }
    
    /**
     * 获取消息数
     * 
     * @return 消息数
     */
    public long getMessages() {
        return messages.get();
    }
    
    /**
     * 获取连接成功率
     * 
     * @return 连接成功率（0-1之间）
     */
    public double getConnectionSuccessRate() {
        long total = connections.get();
        if (total == 0) {
            return 0.0;
        }
        return (double) (total - failures.get()) / total;
    }
    
    /**
     * 获取平均延迟
     * 
     * @return 平均延迟（毫秒）
     */
    public long getAverageLatency() {
        long totalMsg = messages.get();
        if (totalMsg == 0) {
            return 0;
        }
        return totalLatency.sum() / totalMsg;
    }
    
    /**
     * 获取最小延迟
     * 
     * @return 最小延迟（毫秒）
     */
    public long getMinLatency() {
        long min = minLatency.get();
        return min == Long.MAX_VALUE ? 0 : min;
    }
    
    /**
     * 获取最大延迟
     * 
     * @return 最大延迟（毫秒）
     */
    public long getMaxLatency() {
        return maxLatency.get();
    }
    
    /**
     * 重置统计信息
     */
    public void reset() {
        connections.set(0);
        disconnections.set(0);
        failures.set(0);
        reconnections.set(0);
        messages.set(0);
        totalLatency.reset();
        minLatency.set(Long.MAX_VALUE);
        maxLatency.set(0);
    }
    
    /**
     * 复制统计信息
     * 
     * @return 统计信息副本
     */
    public SubscriptionStats copy() {
        SubscriptionStats copy = new SubscriptionStats();
        copy.connections.set(this.connections.get());
        copy.disconnections.set(this.disconnections.get());
        copy.failures.set(this.failures.get());
        copy.reconnections.set(this.reconnections.get());
        copy.messages.set(this.messages.get());
        copy.totalLatency.add(this.totalLatency.sum());
        copy.minLatency.set(this.minLatency.get());
        copy.maxLatency.set(this.maxLatency.get());
        return copy;
    }
    
    @Override
    public String toString() {
        return String.format(
                "SubscriptionStats{connections=%d, disconnections=%d, failures=%d, " +
                "reconnections=%d, messages=%d, successRate=%.2f%%, " +
                "avgLatency=%dms, minLatency=%dms, maxLatency=%dms}",
                getConnections(), getDisconnections(), getFailures(),
                getReconnections(), getMessages(), getConnectionSuccessRate() * 100,
                getAverageLatency(), getMinLatency(), getMaxLatency()
        );
    }
}
