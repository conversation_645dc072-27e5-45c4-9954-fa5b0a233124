package com.trading.common.metrics;

import com.trading.common.enums.ApiRequestWeight;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * API性能监控指标
 * 收集和统计API调用的性能数据
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class ApiMetrics {
    
    private static final Logger log = LoggerFactory.getLogger(ApiMetrics.class);
    
    // 全局统计
    private final AtomicLong totalRequests = new AtomicLong(0);
    private final AtomicLong successfulRequests = new AtomicLong(0);
    private final AtomicLong failedRequests = new AtomicLong(0);
    private final LongAdder totalResponseTime = new LongAdder();
    
    // 按权重分类的统计
    private final ConcurrentHashMap<ApiRequestWeight, RequestStats> weightStats = new ConcurrentHashMap<>();
    
    // 按端点分类的统计
    private final ConcurrentHashMap<String, RequestStats> endpointStats = new ConcurrentHashMap<>();
    
    // 错误统计
    private final ConcurrentHashMap<String, AtomicLong> errorCounts = new ConcurrentHashMap<>();
    
    // 响应时间分布
    private final AtomicLong responseTimes_0_100ms = new AtomicLong(0);
    private final AtomicLong responseTimes_100_500ms = new AtomicLong(0);
    private final AtomicLong responseTimes_500_1000ms = new AtomicLong(0);
    private final AtomicLong responseTimes_1000_5000ms = new AtomicLong(0);
    private final AtomicLong responseTimes_over_5000ms = new AtomicLong(0);
    
    // 启动时间
    private final Instant startTime = Instant.now();
    
    public ApiMetrics() {
        // 初始化权重统计
        for (ApiRequestWeight weight : ApiRequestWeight.values()) {
            weightStats.put(weight, new RequestStats());
        }
        
        log.info("API性能监控初始化完成");
    }
    
    /**
     * 记录API请求开始
     * 
     * @param endpoint API端点
     * @param weight 请求权重
     * @return 请求上下文
     */
    public RequestContext startRequest(String endpoint, ApiRequestWeight weight) {
        totalRequests.incrementAndGet();
        
        // 更新权重统计
        weightStats.get(weight).incrementRequests();
        
        // 更新端点统计
        endpointStats.computeIfAbsent(endpoint, k -> new RequestStats()).incrementRequests();
        
        return new RequestContext(endpoint, weight, Instant.now());
    }
    
    /**
     * 记录API请求成功
     * 
     * @param context 请求上下文
     */
    public void recordSuccess(RequestContext context) {
        long responseTime = Duration.between(context.getStartTime(), Instant.now()).toMillis();
        
        successfulRequests.incrementAndGet();
        totalResponseTime.add(responseTime);
        
        // 更新权重统计
        RequestStats weightStat = weightStats.get(context.getWeight());
        weightStat.incrementSuccess();
        weightStat.addResponseTime(responseTime);
        
        // 更新端点统计
        RequestStats endpointStat = endpointStats.get(context.getEndpoint());
        endpointStat.incrementSuccess();
        endpointStat.addResponseTime(responseTime);
        
        // 更新响应时间分布
        updateResponseTimeDistribution(responseTime);
        
        log.debug("API请求成功: endpoint={}, weight={}, responseTime={}ms", 
                context.getEndpoint(), context.getWeight(), responseTime);
    }
    
    /**
     * 记录API请求失败
     * 
     * @param context 请求上下文
     * @param errorType 错误类型
     */
    public void recordFailure(RequestContext context, String errorType) {
        long responseTime = Duration.between(context.getStartTime(), Instant.now()).toMillis();
        
        failedRequests.incrementAndGet();
        totalResponseTime.add(responseTime);
        
        // 更新权重统计
        RequestStats weightStat = weightStats.get(context.getWeight());
        weightStat.incrementFailure();
        weightStat.addResponseTime(responseTime);
        
        // 更新端点统计
        RequestStats endpointStat = endpointStats.get(context.getEndpoint());
        endpointStat.incrementFailure();
        endpointStat.addResponseTime(responseTime);
        
        // 更新错误统计
        errorCounts.computeIfAbsent(errorType, k -> new AtomicLong(0)).incrementAndGet();
        
        // 更新响应时间分布
        updateResponseTimeDistribution(responseTime);
        
        log.warn("API请求失败: endpoint={}, weight={}, errorType={}, responseTime={}ms", 
                context.getEndpoint(), context.getWeight(), errorType, responseTime);
    }
    
    /**
     * 更新响应时间分布
     * 
     * @param responseTime 响应时间（毫秒）
     */
    private void updateResponseTimeDistribution(long responseTime) {
        if (responseTime < 100) {
            responseTimes_0_100ms.incrementAndGet();
        } else if (responseTime < 500) {
            responseTimes_100_500ms.incrementAndGet();
        } else if (responseTime < 1000) {
            responseTimes_500_1000ms.incrementAndGet();
        } else if (responseTime < 5000) {
            responseTimes_1000_5000ms.incrementAndGet();
        } else {
            responseTimes_over_5000ms.incrementAndGet();
        }
    }
    
    /**
     * 获取总体统计信息
     * 
     * @return 总体统计信息
     */
    public OverallStats getOverallStats() {
        long total = totalRequests.get();
        long successful = successfulRequests.get();
        long failed = failedRequests.get();
        long avgResponseTime = total > 0 ? totalResponseTime.sum() / total : 0;
        
        return OverallStats.builder()
                .totalRequests(total)
                .successfulRequests(successful)
                .failedRequests(failed)
                .successRate(total > 0 ? (double) successful / total : 0.0)
                .averageResponseTime(avgResponseTime)
                .uptime(Duration.between(startTime, Instant.now()))
                .requestsPerSecond(calculateRequestsPerSecond())
                .build();
    }
    
    /**
     * 获取权重统计信息
     * 
     * @param weight 请求权重
     * @return 权重统计信息
     */
    public RequestStats getWeightStats(ApiRequestWeight weight) {
        return weightStats.get(weight).copy();
    }
    
    /**
     * 获取端点统计信息
     * 
     * @param endpoint API端点
     * @return 端点统计信息
     */
    public RequestStats getEndpointStats(String endpoint) {
        RequestStats stats = endpointStats.get(endpoint);
        return stats != null ? stats.copy() : new RequestStats();
    }
    
    /**
     * 获取错误统计信息
     * 
     * @return 错误统计信息
     */
    public ConcurrentHashMap<String, Long> getErrorStats() {
        ConcurrentHashMap<String, Long> result = new ConcurrentHashMap<>();
        errorCounts.forEach((key, value) -> result.put(key, value.get()));
        return result;
    }
    
    /**
     * 获取响应时间分布
     * 
     * @return 响应时间分布
     */
    public ResponseTimeDistribution getResponseTimeDistribution() {
        return ResponseTimeDistribution.builder()
                .range_0_100ms(responseTimes_0_100ms.get())
                .range_100_500ms(responseTimes_100_500ms.get())
                .range_500_1000ms(responseTimes_500_1000ms.get())
                .range_1000_5000ms(responseTimes_1000_5000ms.get())
                .range_over_5000ms(responseTimes_over_5000ms.get())
                .build();
    }
    
    /**
     * 计算每秒请求数
     * 
     * @return 每秒请求数
     */
    private double calculateRequestsPerSecond() {
        long uptimeSeconds = Duration.between(startTime, Instant.now()).getSeconds();
        if (uptimeSeconds == 0) {
            return 0.0;
        }
        return (double) totalRequests.get() / uptimeSeconds;
    }
    
    /**
     * 重置所有统计信息
     */
    public void reset() {
        totalRequests.set(0);
        successfulRequests.set(0);
        failedRequests.set(0);
        totalResponseTime.reset();
        
        weightStats.values().forEach(RequestStats::reset);
        endpointStats.clear();
        errorCounts.clear();
        
        responseTimes_0_100ms.set(0);
        responseTimes_100_500ms.set(0);
        responseTimes_500_1000ms.set(0);
        responseTimes_1000_5000ms.set(0);
        responseTimes_over_5000ms.set(0);
        
        log.info("API性能监控统计信息已重置");
    }
    
    /**
     * 检查性能是否健康
     * 
     * @return 是否健康
     */
    public boolean isHealthy() {
        OverallStats stats = getOverallStats();
        
        // 成功率大于95%且平均响应时间小于1000ms
        return stats.getSuccessRate() > 0.95 && stats.getAverageResponseTime() < 1000;
    }
    
    /**
     * 获取性能报告
     * 
     * @return 性能报告字符串
     */
    public String getPerformanceReport() {
        OverallStats overall = getOverallStats();
        ResponseTimeDistribution distribution = getResponseTimeDistribution();
        
        StringBuilder report = new StringBuilder();
        report.append("=== API性能报告 ===\n");
        report.append(String.format("总请求数: %d\n", overall.getTotalRequests()));
        report.append(String.format("成功请求数: %d\n", overall.getSuccessfulRequests()));
        report.append(String.format("失败请求数: %d\n", overall.getFailedRequests()));
        report.append(String.format("成功率: %.2f%%\n", overall.getSuccessRate() * 100));
        report.append(String.format("平均响应时间: %dms\n", overall.getAverageResponseTime()));
        report.append(String.format("每秒请求数: %.2f\n", overall.getRequestsPerSecond()));
        report.append(String.format("运行时间: %s\n", formatDuration(overall.getUptime())));
        
        report.append("\n=== 响应时间分布 ===\n");
        report.append(String.format("0-100ms: %d\n", distribution.getRange_0_100ms()));
        report.append(String.format("100-500ms: %d\n", distribution.getRange_100_500ms()));
        report.append(String.format("500-1000ms: %d\n", distribution.getRange_500_1000ms()));
        report.append(String.format("1000-5000ms: %d\n", distribution.getRange_1000_5000ms()));
        report.append(String.format(">5000ms: %d\n", distribution.getRange_over_5000ms()));
        
        return report.toString();
    }
    
    /**
     * 格式化持续时间
     * 
     * @param duration 持续时间
     * @return 格式化字符串
     */
    private String formatDuration(Duration duration) {
        long hours = duration.toHours();
        long minutes = duration.toMinutesPart();
        long seconds = duration.toSecondsPart();
        
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }
}
