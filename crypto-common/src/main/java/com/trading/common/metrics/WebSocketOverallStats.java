package com.trading.common.metrics;

import lombok.Builder;
import lombok.Data;

import java.time.Duration;

/**
 * WebSocket总体统计信息
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
public class WebSocketOverallStats {
    
    /**
     * 总连接数
     */
    private long totalConnections;
    
    /**
     * 活跃连接数
     */
    private long activeConnections;
    
    /**
     * 失败连接数
     */
    private long failedConnections;
    
    /**
     * 重连次数
     */
    private long reconnections;
    
    /**
     * 总消息数
     */
    private long totalMessages;
    
    /**
     * 平均延迟（毫秒）
     */
    private long averageLatency;
    
    /**
     * 最小延迟（毫秒）
     */
    private long minLatency;
    
    /**
     * 最大延迟（毫秒）
     */
    private long maxLatency;
    
    /**
     * 运行时间
     */
    private Duration uptime;
    
    /**
     * 每秒消息数
     */
    private double messagesPerSecond;
    
    /**
     * 获取连接成功率
     * 
     * @return 连接成功率
     */
    public double getConnectionSuccessRate() {
        if (totalConnections == 0) {
            return 0.0;
        }
        return (double) (totalConnections - failedConnections) / totalConnections;
    }
    
    /**
     * 获取连接失败率
     * 
     * @return 连接失败率
     */
    public double getConnectionFailureRate() {
        return 1.0 - getConnectionSuccessRate();
    }
    
    /**
     * 检查是否健康
     * 
     * @return 是否健康
     */
    public boolean isHealthy() {
        return activeConnections > 0 && 
               getConnectionSuccessRate() > 0.9 && 
               averageLatency < 1000;
    }
    
    @Override
    public String toString() {
        return String.format(
                "WebSocketOverallStats{total=%d, active=%d, failed=%d, reconnections=%d, " +
                "messages=%d, avgLatency=%dms, minLatency=%dms, maxLatency=%dms, " +
                "mps=%.2f, uptime=%s}",
                totalConnections, activeConnections, failedConnections, reconnections,
                totalMessages, averageLatency, minLatency, maxLatency,
                messagesPerSecond, formatUptime()
        );
    }
    
    /**
     * 格式化运行时间
     * 
     * @return 格式化的运行时间
     */
    private String formatUptime() {
        if (uptime == null) {
            return "N/A";
        }
        
        long hours = uptime.toHours();
        long minutes = uptime.toMinutesPart();
        long seconds = uptime.toSecondsPart();
        
        return String.format("%02d:%02d:%02d", hours, minutes, seconds);
    }
}
