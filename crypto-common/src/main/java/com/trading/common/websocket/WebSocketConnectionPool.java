package com.trading.common.websocket;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.LongAdder;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * 高性能WebSocket连接池
 * 实现连接复用、池化管理和自动健康检查
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class WebSocketConnectionPool {

    private static final Logger log = LoggerFactory.getLogger(WebSocketConnectionPool.class);

    // 连接池配置
    private static final int DEFAULT_POOL_SIZE = 10;
    private static final int MAX_POOL_SIZE = 50;
    private static final int CONNECTION_TIMEOUT_MS = 10000;
    private static final int HEALTH_CHECK_INTERVAL_MS = 30000;
    private static final int MAX_IDLE_TIME_MS = 300000; // 5分钟

    // 连接池管理
    private final Map<String, Queue<PooledWebSocketConnection>> connectionPools = new ConcurrentHashMap<>();
    private final Map<String, AtomicInteger> poolSizes = new ConcurrentHashMap<>();
    private final Map<String, PooledWebSocketConnection> activeConnections = new ConcurrentHashMap<>();
    
    // 健康检查
    private ScheduledExecutorService healthCheckExecutor;
    private final Map<String, Long> lastHealthCheck = new ConcurrentHashMap<>();
    
    // 性能统计
    private final LongAdder totalConnectionsCreated = new LongAdder();
    private final LongAdder totalConnectionsReused = new LongAdder();
    private final LongAdder totalConnectionsDestroyed = new LongAdder();
    private final LongAdder healthCheckCount = new LongAdder();
    private final LongAdder connectionFailures = new LongAdder();

    @PostConstruct
    public void initialize() {
        log.info("初始化WebSocket连接池...");
        
        // 启动健康检查
        healthCheckExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "websocket-pool-health-check");
            thread.setDaemon(true);
            return thread;
        });
        
        // 定期健康检查
        healthCheckExecutor.scheduleAtFixedRate(
                this::performHealthCheck, 
                HEALTH_CHECK_INTERVAL_MS, 
                HEALTH_CHECK_INTERVAL_MS, 
                TimeUnit.MILLISECONDS
        );
        
        // 定期清理空闲连接
        healthCheckExecutor.scheduleAtFixedRate(
                this::cleanupIdleConnections, 
                60000, // 1分钟后开始
                60000, // 每分钟执行一次
                TimeUnit.MILLISECONDS
        );
        
        log.info("WebSocket连接池初始化完成");
    }

    /**
     * 获取连接
     */
    public PooledWebSocketConnection getConnection(String endpoint) {
        Queue<PooledWebSocketConnection> pool = connectionPools.computeIfAbsent(
                endpoint, k -> new ConcurrentLinkedQueue<>());
        
        // 尝试从池中获取可用连接
        PooledWebSocketConnection connection = pool.poll();
        if (connection != null && connection.isHealthy()) {
            connection.markAsActive();
            activeConnections.put(connection.getId(), connection);
            totalConnectionsReused.increment();
            // 只在trace级别输出详细日志，避免测试时大量日志输出
            if (log.isTraceEnabled()) {
                log.trace("从连接池复用连接: endpoint={}, connectionId={}", endpoint, connection.getId());
            }
            return connection;
        }
        
        // 如果没有可用连接，创建新连接
        if (canCreateNewConnection(endpoint)) {
            connection = createNewConnection(endpoint);
            if (connection != null) {
                activeConnections.put(connection.getId(), connection);
                totalConnectionsCreated.increment();
                log.debug("创建新连接: endpoint={}, connectionId={}", endpoint, connection.getId());
                return connection;
            }
        }
        
        connectionFailures.increment();
        log.warn("无法获取连接: endpoint={}", endpoint);
        return null;
    }

    /**
     * 归还连接到池
     */
    public void returnConnection(PooledWebSocketConnection connection) {
        if (connection == null) {
            return;
        }
        
        activeConnections.remove(connection.getId());
        
        if (connection.isHealthy() && !connection.isExpired(MAX_IDLE_TIME_MS)) {
            connection.markAsIdle();
            Queue<PooledWebSocketConnection> pool = connectionPools.get(connection.getEndpoint());
            if (pool != null) {
                pool.offer(connection);
                // 只在trace级别输出详细日志，避免测试时大量日志输出
                if (log.isTraceEnabled()) {
                    log.trace("连接已归还到池: endpoint={}, connectionId={}",
                            connection.getEndpoint(), connection.getId());
                }
            }
        } else {
            // 连接不健康或已过期，销毁它
            destroyConnection(connection);
        }
    }

    /**
     * 强制关闭连接
     */
    public void closeConnection(PooledWebSocketConnection connection) {
        if (connection != null) {
            activeConnections.remove(connection.getId());
            destroyConnection(connection);
        }
    }

    /**
     * 获取连接池统计信息
     */
    public ConnectionPoolStatistics getStatistics() {
        int totalPooledConnections = connectionPools.values().stream()
                .mapToInt(Queue::size)
                .sum();
        
        return new ConnectionPoolStatistics(
                totalConnectionsCreated.sum(),
                totalConnectionsReused.sum(),
                totalConnectionsDestroyed.sum(),
                activeConnections.size(),
                totalPooledConnections,
                connectionFailures.sum(),
                healthCheckCount.sum(),
                calculateReuseRate()
        );
    }

    /**
     * 获取端点连接统计
     */
    public Map<String, Integer> getEndpointStatistics() {
        Map<String, Integer> stats = new ConcurrentHashMap<>();
        connectionPools.forEach((endpoint, pool) -> {
            stats.put(endpoint + "_pooled", pool.size());
        });
        
        // 统计活跃连接
        Map<String, Integer> activeStats = new ConcurrentHashMap<>();
        activeConnections.values().forEach(conn -> {
            activeStats.merge(conn.getEndpoint() + "_active", 1, Integer::sum);
        });
        stats.putAll(activeStats);
        
        return stats;
    }

    /**
     * 创建新连接
     */
    private PooledWebSocketConnection createNewConnection(String endpoint) {
        try {
            AtomicInteger poolSize = poolSizes.computeIfAbsent(endpoint, k -> new AtomicInteger(0));
            if (poolSize.get() >= MAX_POOL_SIZE) {
                log.warn("连接池已达到最大大小: endpoint={}, maxSize={}", endpoint, MAX_POOL_SIZE);
                return null;
            }
            
            poolSize.incrementAndGet();
            
            // 创建连接（这里是模拟实现，实际需要根据具体WebSocket库实现）
            PooledWebSocketConnection connection = new PooledWebSocketConnection(
                    generateConnectionId(), endpoint, System.currentTimeMillis());
            
            log.debug("创建新WebSocket连接: endpoint={}, connectionId={}", endpoint, connection.getId());
            return connection;
            
        } catch (Exception e) {
            log.error("创建WebSocket连接失败: endpoint={}", endpoint, e);
            connectionFailures.increment();
            return null;
        }
    }

    /**
     * 销毁连接
     */
    private void destroyConnection(PooledWebSocketConnection connection) {
        try {
            connection.close();
            
            AtomicInteger poolSize = poolSizes.get(connection.getEndpoint());
            if (poolSize != null) {
                poolSize.decrementAndGet();
            }
            
            totalConnectionsDestroyed.increment();
            log.debug("销毁WebSocket连接: endpoint={}, connectionId={}", 
                    connection.getEndpoint(), connection.getId());
            
        } catch (Exception e) {
            log.error("销毁WebSocket连接失败: connectionId={}", connection.getId(), e);
        }
    }

    /**
     * 检查是否可以创建新连接
     */
    private boolean canCreateNewConnection(String endpoint) {
        AtomicInteger poolSize = poolSizes.get(endpoint);
        return poolSize == null || poolSize.get() < MAX_POOL_SIZE;
    }

    /**
     * 执行健康检查
     */
    void performHealthCheck() {
        try {
            healthCheckCount.increment();
            long currentTime = System.currentTimeMillis();
            
            // 检查活跃连接
            activeConnections.values().forEach(connection -> {
                if (!connection.isHealthy()) {
                    log.warn("发现不健康的活跃连接: endpoint={}, connectionId={}", 
                            connection.getEndpoint(), connection.getId());
                    closeConnection(connection);
                }
            });
            
            // 检查池中的连接
            connectionPools.forEach((endpoint, pool) -> {
                lastHealthCheck.put(endpoint, currentTime);
                pool.removeIf(connection -> {
                    if (!connection.isHealthy() || connection.isExpired(MAX_IDLE_TIME_MS)) {
                        destroyConnection(connection);
                        return true;
                    }
                    return false;
                });
            });
            
            log.debug("健康检查完成: 活跃连接={}, 池化连接={}", 
                    activeConnections.size(), 
                    connectionPools.values().stream().mapToInt(Queue::size).sum());
            
        } catch (Exception e) {
            log.error("健康检查执行失败", e);
        }
    }

    /**
     * 清理空闲连接
     */
    private void cleanupIdleConnections() {
        try {
            long currentTime = System.currentTimeMillis();
            
            connectionPools.forEach((endpoint, pool) -> {
                int originalSize = pool.size();
                pool.removeIf(connection -> {
                    if (connection.isExpired(MAX_IDLE_TIME_MS)) {
                        destroyConnection(connection);
                        return true;
                    }
                    return false;
                });
                
                int removedCount = originalSize - pool.size();
                if (removedCount > 0) {
                    log.debug("清理空闲连接: endpoint={}, 清理数量={}", endpoint, removedCount);
                }
            });
            
        } catch (Exception e) {
            log.error("清理空闲连接失败", e);
        }
    }

    /**
     * 计算连接复用率
     */
    private double calculateReuseRate() {
        long total = totalConnectionsCreated.sum() + totalConnectionsReused.sum();
        return total == 0 ? 0.0 : (double) totalConnectionsReused.sum() / total * 100;
    }

    /**
     * 生成连接ID
     */
    private String generateConnectionId() {
        return "ws-conn-" + System.currentTimeMillis() + "-" + 
               Thread.currentThread().getId();
    }

    @PreDestroy
    public void shutdown() {
        log.info("关闭WebSocket连接池...");
        
        // 关闭健康检查
        if (healthCheckExecutor != null && !healthCheckExecutor.isShutdown()) {
            healthCheckExecutor.shutdown();
            try {
                if (!healthCheckExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    healthCheckExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                healthCheckExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 关闭所有连接
        activeConnections.values().forEach(this::destroyConnection);
        connectionPools.values().forEach(pool -> {
            while (!pool.isEmpty()) {
                PooledWebSocketConnection connection = pool.poll();
                if (connection != null) {
                    destroyConnection(connection);
                }
            }
        });
        
        activeConnections.clear();
        connectionPools.clear();
        poolSizes.clear();
        
        log.info("WebSocket连接池已关闭");
    }

    /**
     * 连接池统计信息
     */
    public static class ConnectionPoolStatistics {
        private final long totalCreated;
        private final long totalReused;
        private final long totalDestroyed;
        private final int activeConnections;
        private final int pooledConnections;
        private final long failures;
        private final long healthChecks;
        private final double reuseRate;

        public ConnectionPoolStatistics(long totalCreated, long totalReused, long totalDestroyed,
                                      int activeConnections, int pooledConnections, long failures,
                                      long healthChecks, double reuseRate) {
            this.totalCreated = totalCreated;
            this.totalReused = totalReused;
            this.totalDestroyed = totalDestroyed;
            this.activeConnections = activeConnections;
            this.pooledConnections = pooledConnections;
            this.failures = failures;
            this.healthChecks = healthChecks;
            this.reuseRate = reuseRate;
        }

        // Getters
        public long getTotalCreated() { return totalCreated; }
        public long getTotalReused() { return totalReused; }
        public long getTotalDestroyed() { return totalDestroyed; }
        public int getActiveConnections() { return activeConnections; }
        public int getPooledConnections() { return pooledConnections; }
        public long getFailures() { return failures; }
        public long getHealthChecks() { return healthChecks; }
        public double getReuseRate() { return reuseRate; }

        @Override
        public String toString() {
            return String.format("WebSocketPool: created=%d, reused=%d, destroyed=%d, " +
                            "active=%d, pooled=%d, failures=%d, healthChecks=%d, reuseRate=%.2f%%",
                    totalCreated, totalReused, totalDestroyed, activeConnections, 
                    pooledConnections, failures, healthChecks, reuseRate);
        }
    }
}
