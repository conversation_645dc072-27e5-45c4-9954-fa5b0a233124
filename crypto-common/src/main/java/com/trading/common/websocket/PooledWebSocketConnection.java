package com.trading.common.websocket;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 池化的WebSocket连接
 * 支持连接复用、健康检查和生命周期管理
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class PooledWebSocketConnection {

    private static final Logger log = LoggerFactory.getLogger(PooledWebSocketConnection.class);

    // 连接状态枚举
    public enum ConnectionState {
        IDLE,           // 空闲状态
        ACTIVE,         // 活跃状态
        CONNECTING,     // 连接中
        CONNECTED,      // 已连接
        DISCONNECTED,   // 已断开
        ERROR,          // 错误状态
        CLOSED          // 已关闭
    }

    // 连接基本信息
    private final String id;
    private final String endpoint;
    private final long createdTime;
    
    // 连接状态管理
    private final AtomicReference<ConnectionState> state = new AtomicReference<>(ConnectionState.IDLE);
    private final AtomicBoolean healthy = new AtomicBoolean(true);
    private final AtomicLong lastUsedTime = new AtomicLong(System.currentTimeMillis());
    private final AtomicLong lastPingTime = new AtomicLong(0);
    private final AtomicLong lastPongTime = new AtomicLong(0);
    
    // 使用统计
    private final AtomicLong useCount = new AtomicLong(0);
    private final AtomicLong messagesSent = new AtomicLong(0);
    private final AtomicLong messagesReceived = new AtomicLong(0);
    private final AtomicLong errorCount = new AtomicLong(0);
    
    // 实际的WebSocket连接对象（这里用Object代替，实际使用时替换为具体的WebSocket实现）
    private volatile Object actualConnection;

    public PooledWebSocketConnection(String id, String endpoint, long createdTime) {
        this.id = id;
        this.endpoint = endpoint;
        this.createdTime = createdTime;
        this.lastUsedTime.set(createdTime);
    }

    /**
     * 标记为活跃状态
     */
    public void markAsActive() {
        state.set(ConnectionState.ACTIVE);
        lastUsedTime.set(System.currentTimeMillis());
        useCount.incrementAndGet();
        // 只在trace级别输出详细日志，避免测试时大量日志输出
        if (log.isTraceEnabled()) {
            log.trace("连接标记为活跃: id={}, endpoint={}", id, endpoint);
        }
    }

    /**
     * 标记为空闲状态
     */
    public void markAsIdle() {
        state.set(ConnectionState.IDLE);
        lastUsedTime.set(System.currentTimeMillis());
        // 只在trace级别输出详细日志，避免测试时大量日志输出
        if (log.isTraceEnabled()) {
            log.trace("连接标记为空闲: id={}, endpoint={}", id, endpoint);
        }
    }

    /**
     * 检查连接是否健康
     */
    public boolean isHealthy() {
        if (!healthy.get()) {
            return false;
        }
        
        // 检查连接状态
        ConnectionState currentState = state.get();
        if (currentState == ConnectionState.ERROR || 
            currentState == ConnectionState.CLOSED ||
            currentState == ConnectionState.DISCONNECTED) {
            return false;
        }
        
        // 检查ping/pong时间差
        long lastPing = lastPingTime.get();
        long lastPong = lastPongTime.get();
        if (lastPing > 0 && lastPong > 0 && (lastPing - lastPong) > 60000) { // 1分钟超时
            log.warn("连接ping/pong超时: id={}, endpoint={}, pingTime={}, pongTime={}", 
                    id, endpoint, lastPing, lastPong);
            return false;
        }
        
        return true;
    }

    /**
     * 检查连接是否过期
     */
    public boolean isExpired(long maxIdleTimeMs) {
        long idleTime = System.currentTimeMillis() - lastUsedTime.get();
        return idleTime > maxIdleTimeMs;
    }

    /**
     * 发送消息
     */
    public boolean sendMessage(String message) {
        if (!isHealthy()) {
            log.warn("尝试在不健康的连接上发送消息: id={}, endpoint={}", id, endpoint);
            return false;
        }
        
        try {
            // 这里应该调用实际的WebSocket发送方法
            // actualConnection.send(message);
            
            messagesSent.incrementAndGet();
            lastUsedTime.set(System.currentTimeMillis());
            // 只在trace级别输出详细日志，避免测试时大量日志输出
            if (log.isTraceEnabled()) {
                log.trace("消息已发送: id={}, endpoint={}, messageLength={}", id, endpoint, message.length());
            }
            return true;
            
        } catch (Exception e) {
            log.error("发送消息失败: id={}, endpoint={}", id, endpoint, e);
            errorCount.incrementAndGet();
            markAsUnhealthy();
            return false;
        }
    }

    /**
     * 发送ping消息
     */
    public boolean sendPing() {
        try {
            // 这里应该调用实际的WebSocket ping方法
            // actualConnection.sendPing();
            
            lastPingTime.set(System.currentTimeMillis());
            log.debug("Ping已发送: id={}, endpoint={}", id, endpoint);
            return true;
            
        } catch (Exception e) {
            log.error("发送Ping失败: id={}, endpoint={}", id, endpoint, e);
            errorCount.incrementAndGet();
            markAsUnhealthy();
            return false;
        }
    }

    /**
     * 处理接收到的pong消息
     */
    public void onPongReceived() {
        lastPongTime.set(System.currentTimeMillis());
        log.debug("Pong已接收: id={}, endpoint={}", id, endpoint);
    }

    /**
     * 处理接收到的消息
     */
    public void onMessageReceived(String message) {
        messagesReceived.incrementAndGet();
        lastUsedTime.set(System.currentTimeMillis());
        log.debug("消息已接收: id={}, endpoint={}, messageLength={}", id, endpoint, message.length());
    }

    /**
     * 标记连接为不健康
     */
    public void markAsUnhealthy() {
        healthy.set(false);
        state.set(ConnectionState.ERROR);
        log.warn("连接标记为不健康: id={}, endpoint={}", id, endpoint);
    }

    /**
     * 关闭连接
     */
    public void close() {
        try {
            state.set(ConnectionState.CLOSED);
            healthy.set(false);
            
            // 这里应该调用实际的WebSocket关闭方法
            // if (actualConnection != null) {
            //     actualConnection.close();
            // }
            
            log.debug("连接已关闭: id={}, endpoint={}", id, endpoint);
            
        } catch (Exception e) {
            log.error("关闭连接失败: id={}, endpoint={}", id, endpoint, e);
        }
    }

    /**
     * 重置连接状态（用于连接复用）
     */
    public void reset() {
        state.set(ConnectionState.IDLE);
        healthy.set(true);
        lastUsedTime.set(System.currentTimeMillis());
        lastPingTime.set(0);
        lastPongTime.set(0);
        log.debug("连接状态已重置: id={}, endpoint={}", id, endpoint);
    }

    /**
     * 获取连接统计信息
     */
    public ConnectionStatistics getStatistics() {
        long currentTime = System.currentTimeMillis();
        return new ConnectionStatistics(
                id,
                endpoint,
                state.get(),
                healthy.get(),
                createdTime,
                lastUsedTime.get(),
                currentTime - createdTime,
                currentTime - lastUsedTime.get(),
                useCount.get(),
                messagesSent.get(),
                messagesReceived.get(),
                errorCount.get()
        );
    }

    // Getters
    public String getId() { return id; }
    public String getEndpoint() { return endpoint; }
    public ConnectionState getState() { return state.get(); }
    public long getCreatedTime() { return createdTime; }
    public long getLastUsedTime() { return lastUsedTime.get(); }
    public long getUseCount() { return useCount.get(); }
    public long getMessagesSent() { return messagesSent.get(); }
    public long getMessagesReceived() { return messagesReceived.get(); }
    public long getErrorCount() { return errorCount.get(); }

    /**
     * 设置实际的WebSocket连接对象
     */
    public void setActualConnection(Object connection) {
        this.actualConnection = connection;
    }

    /**
     * 获取实际的WebSocket连接对象
     */
    public Object getActualConnection() {
        return actualConnection;
    }

    @Override
    public String toString() {
        return String.format("PooledWebSocketConnection{id='%s', endpoint='%s', state=%s, healthy=%s, useCount=%d}",
                id, endpoint, state.get(), healthy.get(), useCount.get());
    }

    /**
     * 连接统计信息
     */
    public static class ConnectionStatistics {
        private final String id;
        private final String endpoint;
        private final ConnectionState state;
        private final boolean healthy;
        private final long createdTime;
        private final long lastUsedTime;
        private final long totalLifetime;
        private final long idleTime;
        private final long useCount;
        private final long messagesSent;
        private final long messagesReceived;
        private final long errorCount;

        public ConnectionStatistics(String id, String endpoint, ConnectionState state, boolean healthy,
                                  long createdTime, long lastUsedTime, long totalLifetime, long idleTime,
                                  long useCount, long messagesSent, long messagesReceived, long errorCount) {
            this.id = id;
            this.endpoint = endpoint;
            this.state = state;
            this.healthy = healthy;
            this.createdTime = createdTime;
            this.lastUsedTime = lastUsedTime;
            this.totalLifetime = totalLifetime;
            this.idleTime = idleTime;
            this.useCount = useCount;
            this.messagesSent = messagesSent;
            this.messagesReceived = messagesReceived;
            this.errorCount = errorCount;
        }

        // Getters
        public String getId() { return id; }
        public String getEndpoint() { return endpoint; }
        public ConnectionState getState() { return state; }
        public boolean isHealthy() { return healthy; }
        public long getCreatedTime() { return createdTime; }
        public long getLastUsedTime() { return lastUsedTime; }
        public long getTotalLifetime() { return totalLifetime; }
        public long getIdleTime() { return idleTime; }
        public long getUseCount() { return useCount; }
        public long getMessagesSent() { return messagesSent; }
        public long getMessagesReceived() { return messagesReceived; }
        public long getErrorCount() { return errorCount; }

        @Override
        public String toString() {
            return String.format("ConnectionStats{id='%s', endpoint='%s', state=%s, healthy=%s, " +
                            "lifetime=%dms, idle=%dms, useCount=%d, sent=%d, received=%d, errors=%d}",
                    id, endpoint, state, healthy, totalLifetime, idleTime, 
                    useCount, messagesSent, messagesReceived, errorCount);
        }
    }
}
