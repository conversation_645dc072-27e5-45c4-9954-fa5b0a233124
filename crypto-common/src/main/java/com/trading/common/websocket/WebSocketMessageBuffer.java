package com.trading.common.websocket;

import com.trading.common.thread.UnifiedThreadPoolManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.LongAdder;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * WebSocket消息缓冲和批处理器
 * 实现消息批处理、缓冲管理和异步处理
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class WebSocketMessageBuffer {

    private static final Logger log = LoggerFactory.getLogger(WebSocketMessageBuffer.class);

    // 缓冲配置
    private static final int DEFAULT_BATCH_SIZE = 50;
    private static final int MAX_BATCH_SIZE = 200;
    private static final int FLUSH_INTERVAL_MS = 100;
    private static final int MAX_BUFFER_SIZE = 10000;

    @Autowired
    private UnifiedThreadPoolManager threadPoolManager;

    // 消息缓冲区
    private final Map<String, ConcurrentLinkedQueue<BufferedMessage>> messageBuffers = new ConcurrentHashMap<>();
    private final Map<String, AtomicInteger> bufferSizes = new ConcurrentHashMap<>();
    private final Map<String, Consumer<List<BufferedMessage>>> messageHandlers = new ConcurrentHashMap<>();
    
    // 批处理管理
    private ScheduledFuture<?> flushTask;
    private final Map<String, Long> lastFlushTime = new ConcurrentHashMap<>();
    
    // 性能统计
    private final LongAdder totalMessagesBuffered = new LongAdder();
    private final LongAdder totalBatchesProcessed = new LongAdder();
    private final LongAdder totalMessagesDropped = new LongAdder();
    private final LongAdder flushCount = new LongAdder();

    @PostConstruct
    public void initialize() {
        log.info("初始化WebSocket消息缓冲器...");
        
        // 启动定期刷新任务
        flushTask = threadPoolManager.scheduleAtFixedRate(
                this::flushAllBuffers,
                FLUSH_INTERVAL_MS,
                FLUSH_INTERVAL_MS,
                TimeUnit.MILLISECONDS
        );
        
        log.info("WebSocket消息缓冲器初始化完成");
    }

    /**
     * 注册消息处理器
     */
    public void registerHandler(String topic, Consumer<List<BufferedMessage>> handler) {
        messageHandlers.put(topic, handler);
        messageBuffers.computeIfAbsent(topic, k -> new ConcurrentLinkedQueue<>());
        bufferSizes.computeIfAbsent(topic, k -> new AtomicInteger(0));
        lastFlushTime.put(topic, System.currentTimeMillis());
        
        log.debug("注册消息处理器: topic={}", topic);
    }

    /**
     * 缓冲消息
     */
    public boolean bufferMessage(String topic, String message, String messageType) {
        return bufferMessage(topic, message, messageType, System.currentTimeMillis());
    }

    /**
     * 缓冲消息（带时间戳）
     */
    public boolean bufferMessage(String topic, String message, String messageType, long timestamp) {
        ConcurrentLinkedQueue<BufferedMessage> buffer = messageBuffers.get(topic);
        if (buffer == null) {
            log.warn("未找到主题的缓冲区: topic={}", topic);
            return false;
        }

        AtomicInteger bufferSize = bufferSizes.get(topic);
        if (bufferSize.get() >= MAX_BUFFER_SIZE) {
            totalMessagesDropped.increment();
            log.warn("缓冲区已满，丢弃消息: topic={}, bufferSize={}", topic, bufferSize.get());
            return false;
        }

        BufferedMessage bufferedMessage = new BufferedMessage(message, messageType, timestamp, topic);
        buffer.offer(bufferedMessage);
        bufferSize.incrementAndGet();
        totalMessagesBuffered.increment();

        // 检查是否需要立即刷新
        if (bufferSize.get() >= DEFAULT_BATCH_SIZE) {
            threadPoolManager.submitToVirtualThread(() -> flushBuffer(topic));
        }

        return true;
    }

    /**
     * 立即刷新指定主题的缓冲区
     */
    public void flushBuffer(String topic) {
        ConcurrentLinkedQueue<BufferedMessage> buffer = messageBuffers.get(topic);
        AtomicInteger bufferSize = bufferSizes.get(topic);
        Consumer<List<BufferedMessage>> handler = messageHandlers.get(topic);

        if (buffer == null || handler == null || bufferSize.get() == 0) {
            return;
        }

        List<BufferedMessage> batch = new ArrayList<>();
        int batchSize = Math.min(bufferSize.get(), MAX_BATCH_SIZE);

        // 从缓冲区取出消息
        for (int i = 0; i < batchSize; i++) {
            BufferedMessage message = buffer.poll();
            if (message != null) {
                batch.add(message);
                bufferSize.decrementAndGet();
            } else {
                break;
            }
        }

        if (!batch.isEmpty()) {
            try {
                handler.accept(batch);
                totalBatchesProcessed.increment();
                lastFlushTime.put(topic, System.currentTimeMillis());
                
                log.debug("批处理完成: topic={}, batchSize={}", topic, batch.size());
                
            } catch (Exception e) {
                log.error("批处理失败: topic={}, batchSize={}", topic, batch.size(), e);
                
                // 处理失败，将消息重新放回缓冲区（如果空间允许）
                for (BufferedMessage message : batch) {
                    if (bufferSize.get() < MAX_BUFFER_SIZE) {
                        buffer.offer(message);
                        bufferSize.incrementAndGet();
                    } else {
                        totalMessagesDropped.increment();
                    }
                }
            }
        }
    }

    /**
     * 刷新所有缓冲区
     */
    private void flushAllBuffers() {
        try {
            flushCount.increment();
            long currentTime = System.currentTimeMillis();
            
            messageBuffers.keySet().forEach(topic -> {
                Long lastFlush = lastFlushTime.get(topic);
                AtomicInteger bufferSize = bufferSizes.get(topic);
                
                // 检查是否需要刷新（有消息且超过刷新间隔）
                if (bufferSize != null && bufferSize.get() > 0 && 
                    (lastFlush == null || (currentTime - lastFlush) >= FLUSH_INTERVAL_MS)) {
                    
                    threadPoolManager.submitToVirtualThread(() -> flushBuffer(topic));
                }
            });
            
        } catch (Exception e) {
            log.error("刷新所有缓冲区失败", e);
        }
    }

    /**
     * 获取缓冲区统计信息
     */
    public BufferStatistics getStatistics() {
        int totalBufferedMessages = bufferSizes.values().stream()
                .mapToInt(AtomicInteger::get)
                .sum();
        
        Map<String, Integer> topicBufferSizes = new ConcurrentHashMap<>();
        bufferSizes.forEach((topic, size) -> topicBufferSizes.put(topic, size.get()));
        
        return new BufferStatistics(
                totalMessagesBuffered.sum(),
                totalBatchesProcessed.sum(),
                totalMessagesDropped.sum(),
                flushCount.sum(),
                totalBufferedMessages,
                messageHandlers.size(),
                topicBufferSizes
        );
    }

    /**
     * 清空指定主题的缓冲区
     */
    public void clearBuffer(String topic) {
        ConcurrentLinkedQueue<BufferedMessage> buffer = messageBuffers.get(topic);
        AtomicInteger bufferSize = bufferSizes.get(topic);
        
        if (buffer != null && bufferSize != null) {
            int clearedCount = bufferSize.getAndSet(0);
            buffer.clear();
            log.info("清空缓冲区: topic={}, clearedCount={}", topic, clearedCount);
        }
    }

    /**
     * 清空所有缓冲区
     */
    public void clearAllBuffers() {
        messageBuffers.forEach((topic, buffer) -> {
            AtomicInteger bufferSize = bufferSizes.get(topic);
            if (bufferSize != null) {
                int clearedCount = bufferSize.getAndSet(0);
                buffer.clear();
                log.debug("清空缓冲区: topic={}, clearedCount={}", topic, clearedCount);
            }
        });
        log.info("所有缓冲区已清空");
    }

    @PreDestroy
    public void shutdown() {
        log.info("关闭WebSocket消息缓冲器...");
        
        // 停止刷新任务
        if (flushTask != null && !flushTask.isCancelled()) {
            flushTask.cancel(false);
        }
        
        // 最后一次刷新所有缓冲区
        messageBuffers.keySet().forEach(this::flushBuffer);
        
        // 清空所有缓冲区
        clearAllBuffers();
        
        log.info("WebSocket消息缓冲器已关闭");
    }

    /**
     * 缓冲的消息
     */
    public static class BufferedMessage {
        private final String content;
        private final String messageType;
        private final long timestamp;
        private final String topic;
        private final long bufferedTime;

        public BufferedMessage(String content, String messageType, long timestamp, String topic) {
            this.content = content;
            this.messageType = messageType;
            this.timestamp = timestamp;
            this.topic = topic;
            this.bufferedTime = System.currentTimeMillis();
        }

        // Getters
        public String getContent() { return content; }
        public String getMessageType() { return messageType; }
        public long getTimestamp() { return timestamp; }
        public String getTopic() { return topic; }
        public long getBufferedTime() { return bufferedTime; }
        public long getBufferDelay() { return System.currentTimeMillis() - bufferedTime; }

        @Override
        public String toString() {
            return String.format("BufferedMessage{type='%s', topic='%s', timestamp=%d, delay=%dms}",
                    messageType, topic, timestamp, getBufferDelay());
        }
    }

    /**
     * 缓冲区统计信息
     */
    public static class BufferStatistics {
        private final long totalBuffered;
        private final long totalBatchesProcessed;
        private final long totalDropped;
        private final long flushCount;
        private final int currentBufferedMessages;
        private final int registeredHandlers;
        private final Map<String, Integer> topicBufferSizes;

        public BufferStatistics(long totalBuffered, long totalBatchesProcessed, long totalDropped,
                              long flushCount, int currentBufferedMessages, int registeredHandlers,
                              Map<String, Integer> topicBufferSizes) {
            this.totalBuffered = totalBuffered;
            this.totalBatchesProcessed = totalBatchesProcessed;
            this.totalDropped = totalDropped;
            this.flushCount = flushCount;
            this.currentBufferedMessages = currentBufferedMessages;
            this.registeredHandlers = registeredHandlers;
            this.topicBufferSizes = topicBufferSizes;
        }

        // Getters
        public long getTotalBuffered() { return totalBuffered; }
        public long getTotalBatchesProcessed() { return totalBatchesProcessed; }
        public long getTotalDropped() { return totalDropped; }
        public long getFlushCount() { return flushCount; }
        public int getCurrentBufferedMessages() { return currentBufferedMessages; }
        public int getRegisteredHandlers() { return registeredHandlers; }
        public Map<String, Integer> getTopicBufferSizes() { return topicBufferSizes; }

        public double getDropRate() {
            return totalBuffered == 0 ? 0.0 : (double) totalDropped / totalBuffered * 100;
        }

        public double getAverageBatchSize() {
            return totalBatchesProcessed == 0 ? 0.0 : (double) totalBuffered / totalBatchesProcessed;
        }

        @Override
        public String toString() {
            return String.format("BufferStats: buffered=%d, batches=%d, dropped=%d, " +
                            "current=%d, handlers=%d, dropRate=%.2f%%, avgBatchSize=%.2f",
                    totalBuffered, totalBatchesProcessed, totalDropped, currentBufferedMessages,
                    registeredHandlers, getDropRate(), getAverageBatchSize());
        }
    }
}
