package com.trading.common.circuit;

import com.trading.common.exception.SdkException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 熔断器实现
 * 基于失败率和响应时间的智能熔断机制
 * 支持半开状态的自动恢复
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class CircuitBreaker {
    
    private static final Logger log = LoggerFactory.getLogger(CircuitBreaker.class);
    
    /**
     * 熔断器状态枚举
     */
    public enum State {
        /**
         * 关闭状态 - 正常工作
         */
        CLOSED,
        
        /**
         * 开启状态 - 熔断中
         */
        OPEN,
        
        /**
         * 半开状态 - 尝试恢复
         */
        HALF_OPEN
    }
    
    // 熔断器配置
    private final String name;
    private final int failureThreshold;           // 失败阈值
    private final int successThreshold;           // 成功阈值（半开状态）
    private final Duration timeout;               // 熔断超时时间
    private final Duration slowCallDuration;     // 慢调用阈值
    private final double slowCallRateThreshold;  // 慢调用率阈值
    
    // 状态管理
    private final AtomicReference<State> state = new AtomicReference<>(State.CLOSED);
    private final AtomicLong lastFailureTime = new AtomicLong(0);
    private final AtomicInteger failureCount = new AtomicInteger(0);
    private final AtomicInteger successCount = new AtomicInteger(0);
    private final AtomicInteger totalCalls = new AtomicInteger(0);
    private final AtomicInteger slowCalls = new AtomicInteger(0);
    
    /**
     * 构造函数
     * 
     * @param name 熔断器名称
     * @param failureThreshold 失败阈值
     * @param successThreshold 成功阈值
     * @param timeout 熔断超时时间
     * @param slowCallDuration 慢调用阈值
     * @param slowCallRateThreshold 慢调用率阈值
     */
    public CircuitBreaker(String name, int failureThreshold, int successThreshold, 
                         Duration timeout, Duration slowCallDuration, double slowCallRateThreshold) {
        this.name = name;
        this.failureThreshold = failureThreshold;
        this.successThreshold = successThreshold;
        this.timeout = timeout;
        this.slowCallDuration = slowCallDuration;
        this.slowCallRateThreshold = slowCallRateThreshold;
        
        log.info("熔断器已创建: name={}, failureThreshold={}, successThreshold={}, timeout={}ms, slowCallDuration={}ms, slowCallRateThreshold={}", 
                name, failureThreshold, successThreshold, timeout.toMillis(), slowCallDuration.toMillis(), slowCallRateThreshold);
    }
    
    /**
     * 执行受保护的操作
     * 
     * @param operation 要执行的操作
     * @param <T> 返回类型
     * @return 操作结果
     * @throws Exception 当操作失败或熔断器开启时抛出
     */
    public <T> T execute(java.util.concurrent.Callable<T> operation) throws Exception {
        // 检查熔断器状态
        if (!allowRequest()) {
            throw new SdkException("CIRCUIT_BREAKER_OPEN", 
                    String.format("熔断器 [%s] 处于开启状态，拒绝请求", name));
        }
        
        Instant startTime = Instant.now();
        boolean success = false;
        
        try {
            // 执行操作
            T result = operation.call();
            success = true;
            
            // 记录成功
            onSuccess(Duration.between(startTime, Instant.now()));
            
            return result;
            
        } catch (Exception e) {
            // 记录失败
            onFailure();
            throw e;
            
        } finally {
            // 更新总调用次数
            totalCalls.incrementAndGet();
        }
    }
    
    /**
     * 检查是否允许请求
     * 
     * @return 如果允许请求返回true，否则返回false
     */
    private boolean allowRequest() {
        State currentState = state.get();
        
        switch (currentState) {
            case CLOSED:
                return true;
                
            case OPEN:
                // 检查是否可以转换到半开状态
                if (shouldAttemptReset()) {
                    if (state.compareAndSet(State.OPEN, State.HALF_OPEN)) {
                        log.info("熔断器 [{}] 从开启状态转换到半开状态", name);
                        resetCounts();
                    }
                    return true;
                }
                return false;
                
            case HALF_OPEN:
                return true;
                
            default:
                return false;
        }
    }
    
    /**
     * 处理成功调用
     * 
     * @param duration 调用持续时间
     */
    private void onSuccess(Duration duration) {
        State currentState = state.get();
        
        // 检查是否为慢调用
        if (duration.compareTo(slowCallDuration) > 0) {
            slowCalls.incrementAndGet();
            log.debug("熔断器 [{}] 检测到慢调用: duration={}ms", name, duration.toMillis());
        }
        
        if (currentState == State.HALF_OPEN) {
            int currentSuccessCount = successCount.incrementAndGet();
            log.debug("熔断器 [{}] 半开状态成功调用: count={}/{}", name, currentSuccessCount, successThreshold);
            
            if (currentSuccessCount >= successThreshold) {
                if (state.compareAndSet(State.HALF_OPEN, State.CLOSED)) {
                    log.info("熔断器 [{}] 从半开状态恢复到关闭状态", name);
                    resetCounts();
                }
            }
        } else {
            // 重置失败计数
            failureCount.set(0);
        }
    }
    
    /**
     * 处理失败调用
     */
    private void onFailure() {
        State currentState = state.get();
        int currentFailureCount = failureCount.incrementAndGet();
        lastFailureTime.set(System.currentTimeMillis());
        
        log.debug("熔断器 [{}] 检测到失败调用: count={}/{}, state={}", 
                name, currentFailureCount, failureThreshold, currentState);
        
        if (currentState == State.HALF_OPEN) {
            // 半开状态下的失败直接转换到开启状态
            if (state.compareAndSet(State.HALF_OPEN, State.OPEN)) {
                log.warn("熔断器 [{}] 从半开状态转换到开启状态", name);
            }
        } else if (currentState == State.CLOSED) {
            // 检查是否达到失败阈值或慢调用率阈值
            if (shouldOpenCircuit()) {
                if (state.compareAndSet(State.CLOSED, State.OPEN)) {
                    log.warn("熔断器 [{}] 从关闭状态转换到开启状态: failureCount={}, slowCallRate={}", 
                            name, currentFailureCount, getSlowCallRate());
                }
            }
        }
    }
    
    /**
     * 检查是否应该开启熔断器
     * 
     * @return 如果应该开启返回true，否则返回false
     */
    private boolean shouldOpenCircuit() {
        int currentFailureCount = failureCount.get();
        int currentTotalCalls = totalCalls.get();
        
        // 检查失败次数阈值
        if (currentFailureCount >= failureThreshold) {
            return true;
        }
        
        // 检查慢调用率阈值
        if (currentTotalCalls >= 10) { // 至少需要10次调用才计算慢调用率
            double slowCallRate = getSlowCallRate();
            if (slowCallRate >= slowCallRateThreshold) {
                log.debug("熔断器 [{}] 慢调用率超过阈值: rate={}, threshold={}", 
                        name, slowCallRate, slowCallRateThreshold);
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * 检查是否应该尝试重置熔断器
     * 
     * @return 如果应该尝试重置返回true，否则返回false
     */
    private boolean shouldAttemptReset() {
        long currentTime = System.currentTimeMillis();
        long lastFailure = lastFailureTime.get();
        
        return (currentTime - lastFailure) >= timeout.toMillis();
    }
    
    /**
     * 重置计数器
     */
    private void resetCounts() {
        failureCount.set(0);
        successCount.set(0);
        totalCalls.set(0);
        slowCalls.set(0);
    }
    
    /**
     * 获取慢调用率
     * 
     * @return 慢调用率
     */
    private double getSlowCallRate() {
        int currentTotalCalls = totalCalls.get();
        int currentSlowCalls = slowCalls.get();
        
        if (currentTotalCalls == 0) {
            return 0.0;
        }
        
        return (double) currentSlowCalls / currentTotalCalls;
    }
    
    /**
     * 获取熔断器状态
     * 
     * @return 当前状态
     */
    public State getState() {
        return state.get();
    }
    
    /**
     * 获取失败次数
     * 
     * @return 失败次数
     */
    public int getFailureCount() {
        return failureCount.get();
    }
    
    /**
     * 获取成功次数
     * 
     * @return 成功次数
     */
    public int getSuccessCount() {
        return successCount.get();
    }
    
    /**
     * 获取总调用次数
     * 
     * @return 总调用次数
     */
    public int getTotalCalls() {
        return totalCalls.get();
    }
    
    /**
     * 获取熔断器名称
     * 
     * @return 熔断器名称
     */
    public String getName() {
        return name;
    }
    
    /**
     * 强制开启熔断器
     */
    public void forceOpen() {
        state.set(State.OPEN);
        log.warn("熔断器 [{}] 被强制开启", name);
    }
    
    /**
     * 强制关闭熔断器
     */
    public void forceClose() {
        state.set(State.CLOSED);
        resetCounts();
        log.info("熔断器 [{}] 被强制关闭", name);
    }
}
