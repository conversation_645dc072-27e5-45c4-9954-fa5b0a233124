package com.trading.common.circuit;

import com.trading.common.config.SdkConfigurationProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

/**
 * 熔断器管理器
 * 负责创建、管理和监控熔断器实例
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class CircuitBreakerManager {
    
    private static final Logger log = LoggerFactory.getLogger(CircuitBreakerManager.class);
    
    // 熔断器实例缓存
    private final ConcurrentMap<String, CircuitBreaker> circuitBreakers = new ConcurrentHashMap<>();
    
    // 默认配置
    private static final int DEFAULT_FAILURE_THRESHOLD = 5;
    private static final int DEFAULT_SUCCESS_THRESHOLD = 3;
    private static final Duration DEFAULT_TIMEOUT = Duration.ofSeconds(60);
    private static final Duration DEFAULT_SLOW_CALL_DURATION = Duration.ofSeconds(5);
    private static final double DEFAULT_SLOW_CALL_RATE_THRESHOLD = 0.5;
    
    private final SdkConfigurationProvider sdkConfigurationProvider;
    
    /**
     * 构造函数
     * 
     * @param sdkConfigurationProvider SDK配置
     */
    public CircuitBreakerManager(@Qualifier("sdkConfiguration") SdkConfigurationProvider sdkConfigurationProvider) {
        this.sdkConfigurationProvider = sdkConfigurationProvider;
        log.info("熔断器管理器已初始化");
    }
    
    /**
     * 获取或创建熔断器
     * 
     * @param name 熔断器名称
     * @return 熔断器实例
     */
    public CircuitBreaker getOrCreateCircuitBreaker(String name) {
        return circuitBreakers.computeIfAbsent(name, this::createCircuitBreaker);
    }
    
    /**
     * 获取API熔断器
     * 
     * @param apiPath API路径
     * @return 熔断器实例
     */
    public CircuitBreaker getApiCircuitBreaker(String apiPath) {
        String name = "api:" + apiPath;
        return getOrCreateCircuitBreaker(name);
    }
    
    /**
     * 获取WebSocket熔断器
     * 
     * @param streamName 流名称
     * @return 熔断器实例
     */
    public CircuitBreaker getWebSocketCircuitBreaker(String streamName) {
        String name = "websocket:" + streamName;
        return getOrCreateCircuitBreaker(name);
    }
    
    /**
     * 获取客户端熔断器
     * 
     * @param clientType 客户端类型
     * @return 熔断器实例
     */
    public CircuitBreaker getClientCircuitBreaker(String clientType) {
        String name = "client:" + clientType;
        return getOrCreateCircuitBreaker(name);
    }
    
    /**
     * 创建熔断器实例
     * 
     * @param name 熔断器名称
     * @return 熔断器实例
     */
    private CircuitBreaker createCircuitBreaker(String name) {
        log.info("创建新的熔断器: {}", name);
        
        // 根据名称类型使用不同的配置
        if (name.startsWith("api:")) {
            return createApiCircuitBreaker(name);
        } else if (name.startsWith("websocket:")) {
            return createWebSocketCircuitBreaker(name);
        } else if (name.startsWith("client:")) {
            return createClientCircuitBreaker(name);
        } else {
            return createDefaultCircuitBreaker(name);
        }
    }
    
    /**
     * 创建API熔断器
     * 
     * @param name 熔断器名称
     * @return 熔断器实例
     */
    private CircuitBreaker createApiCircuitBreaker(String name) {
        return new CircuitBreaker(
                name,
                getConfigValue("api.failure-threshold", DEFAULT_FAILURE_THRESHOLD),
                getConfigValue("api.success-threshold", DEFAULT_SUCCESS_THRESHOLD),
                Duration.ofMillis(getConfigValue("api.timeout-ms", (int) DEFAULT_TIMEOUT.toMillis())),
                Duration.ofMillis(getConfigValue("api.slow-call-duration-ms", (int) DEFAULT_SLOW_CALL_DURATION.toMillis())),
                getConfigValue("api.slow-call-rate-threshold", DEFAULT_SLOW_CALL_RATE_THRESHOLD)
        );
    }
    
    /**
     * 创建WebSocket熔断器
     * 
     * @param name 熔断器名称
     * @return 熔断器实例
     */
    private CircuitBreaker createWebSocketCircuitBreaker(String name) {
        return new CircuitBreaker(
                name,
                getConfigValue("websocket.failure-threshold", 3),
                getConfigValue("websocket.success-threshold", 2),
                Duration.ofMillis(getConfigValue("websocket.timeout-ms", 30000)),
                Duration.ofMillis(getConfigValue("websocket.slow-call-duration-ms", 3000)),
                getConfigValue("websocket.slow-call-rate-threshold", 0.3)
        );
    }
    
    /**
     * 创建客户端熔断器
     * 
     * @param name 熔断器名称
     * @return 熔断器实例
     */
    private CircuitBreaker createClientCircuitBreaker(String name) {
        return new CircuitBreaker(
                name,
                getConfigValue("client.failure-threshold", 10),
                getConfigValue("client.success-threshold", 5),
                Duration.ofMillis(getConfigValue("client.timeout-ms", 120000)),
                Duration.ofMillis(getConfigValue("client.slow-call-duration-ms", 10000)),
                getConfigValue("client.slow-call-rate-threshold", 0.6)
        );
    }
    
    /**
     * 创建默认熔断器
     * 
     * @param name 熔断器名称
     * @return 熔断器实例
     */
    private CircuitBreaker createDefaultCircuitBreaker(String name) {
        return new CircuitBreaker(
                name,
                DEFAULT_FAILURE_THRESHOLD,
                DEFAULT_SUCCESS_THRESHOLD,
                DEFAULT_TIMEOUT,
                DEFAULT_SLOW_CALL_DURATION,
                DEFAULT_SLOW_CALL_RATE_THRESHOLD
        );
    }
    
    /**
     * 获取配置值
     * 
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    private int getConfigValue(String key, int defaultValue) {
        try {
            // 这里可以从配置中读取值，暂时返回默认值
            return defaultValue;
        } catch (Exception e) {
            log.warn("获取配置值失败，使用默认值: key={}, defaultValue={}", key, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取配置值
     * 
     * @param key 配置键
     * @param defaultValue 默认值
     * @return 配置值
     */
    private double getConfigValue(String key, double defaultValue) {
        try {
            // 这里可以从配置中读取值，暂时返回默认值
            return defaultValue;
        } catch (Exception e) {
            log.warn("获取配置值失败，使用默认值: key={}, defaultValue={}", key, defaultValue);
            return defaultValue;
        }
    }
    
    /**
     * 获取所有熔断器状态
     * 
     * @return 熔断器状态信息
     */
    public String getCircuitBreakerStatus() {
        StringBuilder status = new StringBuilder();
        status.append("熔断器状态报告:\n");
        
        circuitBreakers.forEach((name, circuitBreaker) -> {
            status.append(String.format("  %s: state=%s, failures=%d, successes=%d, total=%d\n",
                    name,
                    circuitBreaker.getState(),
                    circuitBreaker.getFailureCount(),
                    circuitBreaker.getSuccessCount(),
                    circuitBreaker.getTotalCalls()));
        });
        
        return status.toString();
    }
    
    /**
     * 重置所有熔断器
     */
    public void resetAllCircuitBreakers() {
        log.info("重置所有熔断器");
        circuitBreakers.values().forEach(CircuitBreaker::forceClose);
    }
    
    /**
     * 强制开启所有熔断器
     */
    public void openAllCircuitBreakers() {
        log.warn("强制开启所有熔断器");
        circuitBreakers.values().forEach(CircuitBreaker::forceOpen);
    }
    
    /**
     * 移除熔断器
     * 
     * @param name 熔断器名称
     */
    public void removeCircuitBreaker(String name) {
        CircuitBreaker removed = circuitBreakers.remove(name);
        if (removed != null) {
            log.info("移除熔断器: {}", name);
        }
    }
    
    /**
     * 获取熔断器数量
     * 
     * @return 熔断器数量
     */
    public int getCircuitBreakerCount() {
        return circuitBreakers.size();
    }
    
    /**
     * 检查是否存在熔断器
     * 
     * @param name 熔断器名称
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasCircuitBreaker(String name) {
        return circuitBreakers.containsKey(name);
    }
}
