package com.trading.common.startup;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationStartingEvent;
import org.springframework.boot.context.event.ApplicationEnvironmentPreparedEvent;
import org.springframework.boot.context.event.ApplicationPreparedEvent;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.RuntimeMXBean;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 高性能启动优化器
 * 优化Spring Boot应用启动过程，提升启动速度和运行时性能
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
@Order(Integer.MIN_VALUE) // 最高优先级
public class HighPerformanceStartupOptimizer {
    
    private static final Logger log = LoggerFactory.getLogger(HighPerformanceStartupOptimizer.class);
    
    private final AtomicLong startupStartTime = new AtomicLong();
    private final AtomicLong environmentPreparedTime = new AtomicLong();
    private final AtomicLong applicationPreparedTime = new AtomicLong();
    private final AtomicLong applicationReadyTime = new AtomicLong();
    
    // 启动优化执行器
    private final ExecutorService startupOptimizationExecutor = 
        Executors.newVirtualThreadPerTaskExecutor();
    
    // JVM优化参数
    private volatile boolean jvmOptimized = false;
    
    @PostConstruct
    public void init() {
        log.info("高性能启动优化器初始化完成");
    }
    
    /**
     * 应用启动开始事件
     */
    @EventListener
    @Order(Integer.MIN_VALUE)
    public void onApplicationStarting(ApplicationStartingEvent event) {
        startupStartTime.set(System.currentTimeMillis());
        log.info("🚀 应用启动开始，启动高性能优化...");
        
        // 异步执行JVM优化
        startupOptimizationExecutor.submit(this::optimizeJVM);
        
        // 异步预热关键组件
        startupOptimizationExecutor.submit(this::prewarmCriticalComponents);
    }
    
    /**
     * 环境准备完成事件
     */
    @EventListener
    @Order(Integer.MIN_VALUE)
    public void onApplicationEnvironmentPrepared(ApplicationEnvironmentPreparedEvent event) {
        environmentPreparedTime.set(System.currentTimeMillis());
        long elapsed = environmentPreparedTime.get() - startupStartTime.get();
        log.info("📋 环境准备完成，耗时: {}ms", elapsed);
        
        // 异步优化环境配置
        startupOptimizationExecutor.submit(this::optimizeEnvironment);
    }
    
    /**
     * 应用准备完成事件
     */
    @EventListener
    @Order(Integer.MIN_VALUE)
    public void onApplicationPrepared(ApplicationPreparedEvent event) {
        applicationPreparedTime.set(System.currentTimeMillis());
        long elapsed = applicationPreparedTime.get() - environmentPreparedTime.get();
        log.info("🔧 应用准备完成，耗时: {}ms", elapsed);
        
        // 异步优化Bean加载
        startupOptimizationExecutor.submit(this::optimizeBeanLoading);
    }
    
    /**
     * 应用就绪事件
     */
    @EventListener
    @Order(Integer.MIN_VALUE)
    public void onApplicationReady(ApplicationReadyEvent event) {
        applicationReadyTime.set(System.currentTimeMillis());
        
        long totalStartupTime = applicationReadyTime.get() - startupStartTime.get();
        long environmentTime = environmentPreparedTime.get() - startupStartTime.get();
        long preparationTime = applicationPreparedTime.get() - environmentPreparedTime.get();
        long readyTime = applicationReadyTime.get() - applicationPreparedTime.get();
        
        log.info("✅ 应用启动完成！");
        log.info("📊 启动性能统计:");
        log.info("   总启动时间: {}ms", totalStartupTime);
        log.info("   环境准备: {}ms", environmentTime);
        log.info("   应用准备: {}ms", preparationTime);
        log.info("   就绪时间: {}ms", readyTime);
        
        // 异步执行启动后优化
        startupOptimizationExecutor.submit(this::postStartupOptimization);
        
        // 输出系统信息
        logSystemInfo();
    }
    
    /**
     * JVM优化
     */
    private void optimizeJVM() {
        if (jvmOptimized) {
            return;
        }
        
        try {
            log.info("🔧 开始JVM优化...");
            
            // 设置系统属性优化
            System.setProperty("java.awt.headless", "true");
            System.setProperty("file.encoding", "UTF-8");
            System.setProperty("sun.net.useExclusiveBind", "false");
            
            // 网络优化
            System.setProperty("java.net.preferIPv4Stack", "true");
            System.setProperty("networkaddress.cache.ttl", "60");
            System.setProperty("networkaddress.cache.negative.ttl", "10");
            
            // 安全优化
            System.setProperty("java.security.egd", "file:/dev/./urandom");
            
            // DNS缓存优化
            java.security.Security.setProperty("networkaddress.cache.ttl", "60");
            java.security.Security.setProperty("networkaddress.cache.negative.ttl", "10");
            
            jvmOptimized = true;
            log.info("✅ JVM优化完成");
            
        } catch (Exception e) {
            log.warn("JVM优化失败: {}", e.getMessage());
        }
    }
    
    /**
     * 预热关键组件
     */
    private void prewarmCriticalComponents() {
        try {
            log.info("🔥 开始预热关键组件...");
            
            // 预热线程池
            prewarmThreadPools();
            
            // 预热序列化器
            prewarmSerializers();
            
            // 预热加密组件
            prewarmCryptographicComponents();
            
            log.info("✅ 关键组件预热完成");
            
        } catch (Exception e) {
            log.warn("关键组件预热失败: {}", e.getMessage());
        }
    }
    
    /**
     * 预热线程池
     */
    private void prewarmThreadPools() {
        // 预热ForkJoinPool
        ForkJoinPool.commonPool().submit(() -> {
            // 简单任务预热
            return "prewarm";
        }).join();
        
        // 预热虚拟线程
        try (var executor = Executors.newVirtualThreadPerTaskExecutor()) {
            var future = executor.submit(() -> "virtual-thread-prewarm");
            future.get(1, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.debug("虚拟线程预热失败: {}", e.getMessage());
        }
    }
    
    /**
     * 预热序列化器
     */
    private void prewarmSerializers() {
        try {
            // 预热Jackson
            com.fasterxml.jackson.databind.ObjectMapper mapper = 
                new com.fasterxml.jackson.databind.ObjectMapper();
            
            // 简单对象序列化预热
            String json = mapper.writeValueAsString(java.util.Map.of("test", "prewarm"));
            mapper.readValue(json, java.util.Map.class);
            
        } catch (Exception e) {
            log.debug("序列化器预热失败: {}", e.getMessage());
        }
    }
    
    /**
     * 预热加密组件
     */
    private void prewarmCryptographicComponents() {
        try {
            // 预热MessageDigest
            java.security.MessageDigest.getInstance("SHA-256")
                .digest("prewarm".getBytes());
            
            // 预热SecureRandom
            java.security.SecureRandom.getInstanceStrong().nextBytes(new byte[16]);
            
        } catch (Exception e) {
            log.debug("加密组件预热失败: {}", e.getMessage());
        }
    }
    
    /**
     * 优化环境配置
     */
    private void optimizeEnvironment() {
        try {
            log.info("🌍 开始环境配置优化...");
            
            // 优化时区设置
            System.setProperty("user.timezone", "UTC");
            
            // 优化临时目录
            String tmpDir = System.getProperty("java.io.tmpdir");
            if (tmpDir != null && !tmpDir.endsWith("/")) {
                System.setProperty("java.io.tmpdir", tmpDir + "/");
            }
            
            log.info("✅ 环境配置优化完成");
            
        } catch (Exception e) {
            log.warn("环境配置优化失败: {}", e.getMessage());
        }
    }
    
    /**
     * 优化Bean加载
     */
    private void optimizeBeanLoading() {
        try {
            log.info("🫘 开始Bean加载优化...");
            
            // 这里可以添加Bean加载优化逻辑
            // 例如：预加载关键Bean、优化依赖注入等
            
            log.info("✅ Bean加载优化完成");
            
        } catch (Exception e) {
            log.warn("Bean加载优化失败: {}", e.getMessage());
        }
    }
    
    /**
     * 启动后优化
     */
    private void postStartupOptimization() {
        try {
            log.info("🚀 开始启动后优化...");
            
            // 触发JIT编译优化
            triggerJITOptimization();
            
            // 内存优化
            optimizeMemory();
            
            // 垃圾回收优化
            optimizeGarbageCollection();
            
            log.info("✅ 启动后优化完成");
            
        } catch (Exception e) {
            log.warn("启动后优化失败: {}", e.getMessage());
        }
    }
    
    /**
     * 触发JIT编译优化
     */
    private void triggerJITOptimization() {
        // 执行一些热点代码路径，触发JIT编译
        for (int i = 0; i < 10000; i++) {
            String.valueOf(i).hashCode();
        }
    }
    
    /**
     * 内存优化
     */
    private void optimizeMemory() {
        // 建议垃圾回收
        System.gc();
    }
    
    /**
     * 垃圾回收优化
     */
    private void optimizeGarbageCollection() {
        try {
            // 获取内存信息
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            long heapUsed = memoryBean.getHeapMemoryUsage().getUsed();
            long heapMax = memoryBean.getHeapMemoryUsage().getMax();
            
            double heapUsagePercent = (double) heapUsed / heapMax * 100;
            
            if (heapUsagePercent > 70) {
                log.info("堆内存使用率较高 ({:.1f}%)，建议垃圾回收", heapUsagePercent);
                System.gc();
            }
            
        } catch (Exception e) {
            log.debug("垃圾回收优化失败: {}", e.getMessage());
        }
    }
    
    /**
     * 输出系统信息
     */
    private void logSystemInfo() {
        try {
            RuntimeMXBean runtimeBean = ManagementFactory.getRuntimeMXBean();
            MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
            
            log.info("💻 系统信息:");
            log.info("   JVM版本: {}", runtimeBean.getVmVersion());
            log.info("   启动时间: {}ms", runtimeBean.getUptime());
            log.info("   堆内存: {}MB / {}MB", 
                memoryBean.getHeapMemoryUsage().getUsed() / 1024 / 1024,
                memoryBean.getHeapMemoryUsage().getMax() / 1024 / 1024);
            log.info("   非堆内存: {}MB / {}MB",
                memoryBean.getNonHeapMemoryUsage().getUsed() / 1024 / 1024,
                memoryBean.getNonHeapMemoryUsage().getMax() / 1024 / 1024);
            log.info("   可用处理器: {}", Runtime.getRuntime().availableProcessors());
            
        } catch (Exception e) {
            log.debug("系统信息输出失败: {}", e.getMessage());
        }
    }
    
    /**
     * 关闭资源
     */
    public void shutdown() {
        if (startupOptimizationExecutor != null && !startupOptimizationExecutor.isShutdown()) {
            startupOptimizationExecutor.shutdown();
            try {
                if (!startupOptimizationExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    startupOptimizationExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                startupOptimizationExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
