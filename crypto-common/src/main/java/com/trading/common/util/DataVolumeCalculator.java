package com.trading.common.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * 数据量计算工具
 * 用于计算指定天数的数据量需求
 */
@Slf4j
@Component
public class DataVolumeCalculator {

    // 每条记录的平均大小（字节）
    private static final Map<String, Integer> RECORD_SIZES = new HashMap<>();
    
    static {
        // K线数据：包含开高低收量等字段，约200字节
        RECORD_SIZES.put("KLINE", 200);
        // 深度数据：包含买卖盘口数据，约500字节（20档）
        RECORD_SIZES.put("DEPTH", 500);
        // 交易数据：包含价格、数量、时间等，约100字节
        RECORD_SIZES.put("TRADE", 100);
        // 统计数据：包含24小时统计信息，约150字节
        RECORD_SIZES.put("TICKER", 150);
    }

    /**
     * 计算90天K线数据量
     */
    public DataVolumeResult calculateKlineDataVolume(int symbolCount, int days) {
        log.info("开始计算{}天K线数据量: 交易对数量={}", days, symbolCount);
        
        DataVolumeResult result = new DataVolumeResult();
        result.setDataType("K线数据");
        result.setDays(days);
        result.setSymbolCount(symbolCount);
        
        // 不同时间间隔的数据量计算
        Map<String, Long> intervalCounts = new HashMap<>();
        
        // 1分钟：90天 * 24小时 * 60分钟 = 129,600条/交易对
        long count1m = (long) days * 24 * 60;
        intervalCounts.put("1m", count1m);
        
        // 5分钟：90天 * 24小时 * 12次 = 25,920条/交易对
        long count5m = (long) days * 24 * 12;
        intervalCounts.put("5m", count5m);
        
        // 15分钟：90天 * 24小时 * 4次 = 8,640条/交易对
        long count15m = (long) days * 24 * 4;
        intervalCounts.put("15m", count15m);
        
        // 1小时：90天 * 24小时 = 2,160条/交易对
        long count1h = (long) days * 24;
        intervalCounts.put("1h", count1h);
        
        // 4小时：90天 * 6次 = 540条/交易对
        long count4h = (long) days * 6;
        intervalCounts.put("4h", count4h);
        
        // 1天：90天 = 90条/交易对
        long count1d = (long) days;
        intervalCounts.put("1d", count1d);
        
        result.setIntervalCounts(intervalCounts);
        
        // 计算总记录数
        long totalRecords = (count1m + count5m + count15m + count1h + count4h + count1d) * symbolCount;
        result.setTotalRecords(totalRecords);
        
        // 计算存储大小
        long totalSizeBytes = totalRecords * RECORD_SIZES.get("KLINE");
        result.setTotalSizeBytes(totalSizeBytes);
        result.setTotalSizeMB(BigDecimal.valueOf(totalSizeBytes).divide(BigDecimal.valueOf(1024 * 1024), 2, RoundingMode.HALF_UP));
        result.setTotalSizeGB(BigDecimal.valueOf(totalSizeBytes).divide(BigDecimal.valueOf(1024 * 1024 * 1024), 3, RoundingMode.HALF_UP));
        
        log.info("K线数据量计算完成: 总记录数={}, 总大小={}MB", totalRecords, result.getTotalSizeMB());
        return result;
    }

    /**
     * 计算90天深度数据量
     */
    public DataVolumeResult calculateDepthDataVolume(int symbolCount, int days) {
        log.info("开始计算{}天深度数据量: 交易对数量={}", days, symbolCount);
        
        DataVolumeResult result = new DataVolumeResult();
        result.setDataType("深度数据");
        result.setDays(days);
        result.setSymbolCount(symbolCount);
        
        // 深度数据更新频率：每100ms一次
        // 90天 * 24小时 * 60分钟 * 60秒 * 10次/秒 = 77,760,000条/交易对/档位
        long updatesPerSymbolPerLevel = (long) days * 24 * 60 * 60 * 10;
        
        // 3个档位（5档、10档、20档）
        int levels = 3;
        long totalRecords = updatesPerSymbolPerLevel * levels * symbolCount;
        result.setTotalRecords(totalRecords);
        
        // 计算存储大小
        long totalSizeBytes = totalRecords * RECORD_SIZES.get("DEPTH");
        result.setTotalSizeBytes(totalSizeBytes);
        result.setTotalSizeMB(BigDecimal.valueOf(totalSizeBytes).divide(BigDecimal.valueOf(1024 * 1024), 2, RoundingMode.HALF_UP));
        result.setTotalSizeGB(BigDecimal.valueOf(totalSizeBytes).divide(BigDecimal.valueOf(1024 * 1024 * 1024), 3, RoundingMode.HALF_UP));
        
        log.info("深度数据量计算完成: 总记录数={}, 总大小={}GB", totalRecords, result.getTotalSizeGB());
        return result;
    }

    /**
     * 计算90天交易数据量
     */
    public DataVolumeResult calculateTradeDataVolume(int symbolCount, int days) {
        log.info("开始计算{}天交易数据量: 交易对数量={}", days, symbolCount);
        
        DataVolumeResult result = new DataVolumeResult();
        result.setDataType("交易数据");
        result.setDays(days);
        result.setSymbolCount(symbolCount);
        
        // 假设平均每秒10笔交易
        // 90天 * 24小时 * 60分钟 * 60秒 * 10笔/秒 = 77,760,000条/交易对
        long tradesPerSymbol = (long) days * 24 * 60 * 60 * 10;
        long totalRecords = tradesPerSymbol * symbolCount;
        result.setTotalRecords(totalRecords);
        
        // 计算存储大小
        long totalSizeBytes = totalRecords * RECORD_SIZES.get("TRADE");
        result.setTotalSizeBytes(totalSizeBytes);
        result.setTotalSizeMB(BigDecimal.valueOf(totalSizeBytes).divide(BigDecimal.valueOf(1024 * 1024), 2, RoundingMode.HALF_UP));
        result.setTotalSizeGB(BigDecimal.valueOf(totalSizeBytes).divide(BigDecimal.valueOf(1024 * 1024 * 1024), 3, RoundingMode.HALF_UP));
        
        log.info("交易数据量计算完成: 总记录数={}, 总大小={}GB", totalRecords, result.getTotalSizeGB());
        return result;
    }

    /**
     * 计算总数据量
     */
    public DataVolumeSummary calculateTotalDataVolume(int symbolCount, int klineDays, int depthDays, int tradeDays) {
        log.info("开始计算总数据量: 交易对数量={}, K线天数={}, 深度天数={}, 交易天数={}", 
            symbolCount, klineDays, depthDays, tradeDays);
        
        DataVolumeResult klineResult = calculateKlineDataVolume(symbolCount, klineDays);
        DataVolumeResult depthResult = calculateDepthDataVolume(symbolCount, depthDays);
        DataVolumeResult tradeResult = calculateTradeDataVolume(symbolCount, tradeDays);
        
        DataVolumeSummary summary = new DataVolumeSummary();
        summary.setSymbolCount(symbolCount);
        // Note: 'days' in summary is now less meaningful, maybe we should represent the range.
        // For now, we'll set it to the max days for simplicity.
        summary.setDays(Math.max(klineDays, Math.max(depthDays, tradeDays)));
        summary.setKlineData(klineResult);
        summary.setDepthData(depthResult);
        summary.setTradeData(tradeResult);
        
        // 计算总计
        long totalRecords = klineResult.getTotalRecords() + depthResult.getTotalRecords() + tradeResult.getTotalRecords();
        long totalSizeBytes = klineResult.getTotalSizeBytes() + depthResult.getTotalSizeBytes() + tradeResult.getTotalSizeBytes();
        
        summary.setTotalRecords(totalRecords);
        summary.setTotalSizeBytes(totalSizeBytes);
        summary.setTotalSizeMB(BigDecimal.valueOf(totalSizeBytes).divide(BigDecimal.valueOf(1024 * 1024), 2, RoundingMode.HALF_UP));
        summary.setTotalSizeGB(BigDecimal.valueOf(totalSizeBytes).divide(BigDecimal.valueOf(1024 * 1024 * 1024), 3, RoundingMode.HALF_UP));
        summary.setTotalSizeTB(BigDecimal.valueOf(totalSizeBytes).divide(BigDecimal.valueOf(1024L * 1024 * 1024 * 1024), 4, RoundingMode.HALF_UP));
        
        log.info("总数据量计算完成: 总记录数={}, 总大小={}GB", totalRecords, summary.getTotalSizeGB());
        return summary;
    }

    /**
     * 数据量计算结果
     */
    public static class DataVolumeResult {
        private String dataType;
        private int days;
        private int symbolCount;
        private Map<String, Long> intervalCounts;
        private long totalRecords;
        private long totalSizeBytes;
        private BigDecimal totalSizeMB;
        private BigDecimal totalSizeGB;

        // Getters and Setters
        public String getDataType() { return dataType; }
        public void setDataType(String dataType) { this.dataType = dataType; }
        public int getDays() { return days; }
        public void setDays(int days) { this.days = days; }
        public int getSymbolCount() { return symbolCount; }
        public void setSymbolCount(int symbolCount) { this.symbolCount = symbolCount; }
        public Map<String, Long> getIntervalCounts() { return intervalCounts; }
        public void setIntervalCounts(Map<String, Long> intervalCounts) { this.intervalCounts = intervalCounts; }
        public long getTotalRecords() { return totalRecords; }
        public void setTotalRecords(long totalRecords) { this.totalRecords = totalRecords; }
        public long getTotalSizeBytes() { return totalSizeBytes; }
        public void setTotalSizeBytes(long totalSizeBytes) { this.totalSizeBytes = totalSizeBytes; }
        public BigDecimal getTotalSizeMB() { return totalSizeMB; }
        public void setTotalSizeMB(BigDecimal totalSizeMB) { this.totalSizeMB = totalSizeMB; }
        public BigDecimal getTotalSizeGB() { return totalSizeGB; }
        public void setTotalSizeGB(BigDecimal totalSizeGB) { this.totalSizeGB = totalSizeGB; }
    }

    /**
     * 数据量汇总
     */
    public static class DataVolumeSummary {
        private int symbolCount;
        private int days;
        private DataVolumeResult klineData;
        private DataVolumeResult depthData;
        private DataVolumeResult tradeData;
        private long totalRecords;
        private long totalSizeBytes;
        private BigDecimal totalSizeMB;
        private BigDecimal totalSizeGB;
        private BigDecimal totalSizeTB;

        // Getters and Setters
        public int getSymbolCount() { return symbolCount; }
        public void setSymbolCount(int symbolCount) { this.symbolCount = symbolCount; }
        public int getDays() { return days; }
        public void setDays(int days) { this.days = days; }
        public DataVolumeResult getKlineData() { return klineData; }
        public void setKlineData(DataVolumeResult klineData) { this.klineData = klineData; }
        public DataVolumeResult getDepthData() { return depthData; }
        public void setDepthData(DataVolumeResult depthData) { this.depthData = depthData; }
        public DataVolumeResult getTradeData() { return tradeData; }
        public void setTradeData(DataVolumeResult tradeData) { this.tradeData = tradeData; }
        public long getTotalRecords() { return totalRecords; }
        public void setTotalRecords(long totalRecords) { this.totalRecords = totalRecords; }
        public long getTotalSizeBytes() { return totalSizeBytes; }
        public void setTotalSizeBytes(long totalSizeBytes) { this.totalSizeBytes = totalSizeBytes; }
        public BigDecimal getTotalSizeMB() { return totalSizeMB; }
        public void setTotalSizeMB(BigDecimal totalSizeMB) { this.totalSizeMB = totalSizeMB; }
        public BigDecimal getTotalSizeGB() { return totalSizeGB; }
        public void setTotalSizeGB(BigDecimal totalSizeGB) { this.totalSizeGB = totalSizeGB; }
        public BigDecimal getTotalSizeTB() { return totalSizeTB; }
        public void setTotalSizeTB(BigDecimal totalSizeTB) { this.totalSizeTB = totalSizeTB; }
    }
}
