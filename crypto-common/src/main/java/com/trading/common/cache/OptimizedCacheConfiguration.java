package com.trading.common.cache;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.fasterxml.jackson.module.afterburner.AfterburnerModule;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.concurrent.TimeUnit;

/**
 * 优化的缓存配置
 * 针对高性能场景的缓存优化配置
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Configuration
public class OptimizedCacheConfiguration {

    private static final Logger log = LoggerFactory.getLogger(OptimizedCacheConfiguration.class);
    
    /**
     * 高性能ObjectMapper配置
     * 针对JSON序列化/反序列化性能优化
     */
    @Bean
    @Primary
    public ObjectMapper optimizedObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        
        // 启用Afterburner模块，提升序列化性能30-40%
        mapper.registerModule(new AfterburnerModule());
        
        // 注册Java时间模块
        mapper.registerModule(new JavaTimeModule());
        
        // 性能优化配置
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
        
        // 禁用不必要的特性以提升性能
        mapper.configure(DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS, true);
        mapper.configure(DeserializationFeature.USE_BIG_INTEGER_FOR_INTS, false);
        
        log.info("高性能ObjectMapper配置完成，已启用Afterburner模块");
        return mapper;
    }
    
    /**
     * 优化的L1缓存配置
     * 针对不同业务场景的缓存配置
     */
    @Bean("marketDataCache")
    public Cache<String, Object> marketDataCache() {
        return Caffeine.newBuilder()
                .maximumSize(50000)  // 增大市场数据缓存
                .expireAfterWrite(30, TimeUnit.SECONDS)  // 市场数据30秒过期
                .expireAfterAccess(15, TimeUnit.SECONDS)  // 15秒未访问过期
                .recordStats()
                .removalListener((RemovalListener<String, Object>) (key, value, cause) -> {
                    if (log.isDebugEnabled()) {
                        log.debug("市场数据缓存移除: key={}, cause={}", key, cause);
                    }
                })
                .build();
    }
    
    @Bean("userDataCache")
    public Cache<String, Object> userDataCache() {
        return Caffeine.newBuilder()
                .maximumSize(10000)  // 用户数据缓存
                .expireAfterWrite(5, TimeUnit.MINUTES)  // 5分钟过期
                .expireAfterAccess(2, TimeUnit.MINUTES)  // 2分钟未访问过期
                .recordStats()
                .build();
    }
    
    @Bean("configCache")
    public Cache<String, Object> configCache() {
        return Caffeine.newBuilder()
                .maximumSize(1000)  // 配置缓存
                .expireAfterWrite(1, TimeUnit.HOURS)  // 1小时过期
                .recordStats()
                .build();
    }
    
    /**
     * BigDecimal常量池
     * 避免重复创建BigDecimal对象
     */
    @Bean
    public BigDecimalPool bigDecimalPool() {
        return new BigDecimalPool();
    }
    
    /**
     * BigDecimal常量池实现
     */
    public static class BigDecimalPool {
        
        // 常用的BigDecimal常量
        public static final BigDecimal ZERO = BigDecimal.ZERO;
        public static final BigDecimal ONE = BigDecimal.ONE;
        public static final BigDecimal TEN = BigDecimal.TEN;
        public static final BigDecimal ONE_HUNDRED = new BigDecimal("100");
        public static final BigDecimal ONE_THOUSAND = new BigDecimal("1000");
        public static final BigDecimal ONE_MILLION = new BigDecimal("1000000");
        
        // 常用小数
        public static final BigDecimal HALF = new BigDecimal("0.5");
        public static final BigDecimal QUARTER = new BigDecimal("0.25");
        public static final BigDecimal THREE_QUARTERS = new BigDecimal("0.75");
        
        // 百分比常量
        public static final BigDecimal PERCENT_1 = new BigDecimal("0.01");
        public static final BigDecimal PERCENT_5 = new BigDecimal("0.05");
        public static final BigDecimal PERCENT_10 = new BigDecimal("0.10");
        public static final BigDecimal PERCENT_50 = new BigDecimal("0.50");
        
        // 缓存常用的BigDecimal值
        private final Cache<String, BigDecimal> decimalCache = Caffeine.newBuilder()
                .maximumSize(1000)
                .expireAfterAccess(1, TimeUnit.HOURS)
                .build();
        
        /**
         * 获取或创建BigDecimal
         * 对于常用值使用缓存，避免重复创建
         */
        public BigDecimal valueOf(String value) {
            // 检查常量
            switch (value) {
                case "0": return ZERO;
                case "1": return ONE;
                case "10": return TEN;
                case "100": return ONE_HUNDRED;
                case "1000": return ONE_THOUSAND;
                case "1000000": return ONE_MILLION;
                case "0.5": return HALF;
                case "0.25": return QUARTER;
                case "0.75": return THREE_QUARTERS;
                case "0.01": return PERCENT_1;
                case "0.05": return PERCENT_5;
                case "0.10": return PERCENT_10;
                case "0.50": return PERCENT_50;
                default:
                    return decimalCache.get(value, k -> new BigDecimal(k));
            }
        }
        
        /**
         * 获取或创建BigDecimal（double值）
         */
        public BigDecimal valueOf(double value) {
            return valueOf(String.valueOf(value));
        }
        
        /**
         * 获取缓存统计
         */
        public String getCacheStats() {
            var stats = decimalCache.stats();
            return String.format("BigDecimal缓存统计: 命中率=%.2f%%, 大小=%d", 
                    stats.hitRate() * 100, decimalCache.estimatedSize());
        }
    }
    
    /**
     * 缓存键生成器
     * 提供高效的缓存键生成策略
     */
    @Bean
    public CacheKeyGenerator cacheKeyGenerator() {
        return new CacheKeyGenerator();
    }
    
    public static class CacheKeyGenerator {
        
        private static final String SEPARATOR = ":";
        
        /**
         * 生成市场数据缓存键
         */
        public String marketDataKey(String symbol, String dataType) {
            return "market" + SEPARATOR + symbol + SEPARATOR + dataType;
        }
        
        /**
         * 生成K线数据缓存键
         */
        public String klineKey(String symbol, String interval, long timestamp) {
            return "kline" + SEPARATOR + symbol + SEPARATOR + interval + SEPARATOR + timestamp;
        }
        
        /**
         * 生成深度数据缓存键
         */
        public String depthKey(String symbol, int level) {
            return "depth" + SEPARATOR + symbol + SEPARATOR + level;
        }
        
        /**
         * 生成用户数据缓存键
         */
        public String userDataKey(String userId, String dataType) {
            return "user" + SEPARATOR + userId + SEPARATOR + dataType;
        }
        
        /**
         * 生成配置缓存键
         */
        public String configKey(String module, String key) {
            return "config" + SEPARATOR + module + SEPARATOR + key;
        }
        
        /**
         * 生成通用缓存键
         */
        public String genericKey(String prefix, String... parts) {
            StringBuilder sb = new StringBuilder(prefix);
            for (String part : parts) {
                sb.append(SEPARATOR).append(part);
            }
            return sb.toString();
        }
    }
    

}
