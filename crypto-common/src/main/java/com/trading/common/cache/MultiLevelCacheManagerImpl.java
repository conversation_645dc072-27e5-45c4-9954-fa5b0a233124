package com.trading.common.cache;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.trading.common.utils.AsyncDelayUtils;
import com.trading.common.retry.UnifiedRetryService;
import com.trading.common.monitoring.UnifiedMetricsService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PreDestroy;

import java.time.Duration;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 统一的多级缓存管理器实现类
 * 整合了分片缓存和多级缓存功能
 * L1: Caffeine本地缓存 (快速访问，内存有限，支持分片)
 * L2: Redis分布式缓存 (持久化，可共享)
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@ConditionalOnProperty(name = "trading.market-data.storage.redis.enabled", havingValue = "true", matchIfMissing = true)
@Component
public class MultiLevelCacheManagerImpl implements MultiLevelCacheManager {
    
    private static final Logger log = LoggerFactory.getLogger(MultiLevelCacheManagerImpl.class);
    
    private final Cache<String, Object>[] l1CacheShards;
    private final int shardCount;
    private final RedisTemplate<String, String> redisTemplate;
    private final ObjectMapper objectMapper;
    private final CacheStatsImpl stats;
    private final Executor virtualThreadExecutor;
    private final UnifiedRetryService retryService;
    private final UnifiedMetricsService metricsService;

    @Autowired(required = false)
    @Lazy
    private CachePerformanceMonitor performanceMonitor;

    private volatile boolean isShuttingDown = false;
    
    @SuppressWarnings("unchecked")
    public MultiLevelCacheManagerImpl(RedisTemplate<String, String> redisTemplate,
                                    ObjectMapper objectMapper,
                                    UnifiedRetryService retryService,
                                    UnifiedMetricsService metricsService) {
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
        this.retryService = retryService;
        this.metricsService = metricsService;
        this.stats = new CacheStatsImpl();

        // 配置分片数量
        this.shardCount = 16; // 默认16个分片
        this.l1CacheShards = new Cache[shardCount];

        // 初始化L1本地缓存分片 (Caffeine) - 优化配置
        for (int i = 0; i < shardCount; i++) {
            final int shardIndex = i;
            l1CacheShards[i] = Caffeine.newBuilder()
                    .maximumSize(2000)  // 每个分片2000条目，总共32000
                    .expireAfterWrite(10, TimeUnit.MINUTES)  // 延长写入过期时间
                    .expireAfterAccess(5, TimeUnit.MINUTES)  // 延长访问过期时间
                    .recordStats()  // 启用统计
                    .removalListener((key, value, cause) -> {
                        // 记录缓存移除事件
                        if (log.isDebugEnabled()) {
                            log.debug("L1缓存分片{}移除: key={}, cause={}", shardIndex, key, cause);
                        }
                        if (metricsService != null) {
                            metricsService.incrementCounter("cache.evictions", "cache", "l1", "shard", String.valueOf(shardIndex), "cause", cause.toString());
                        }
                    })
                    .build();
        }

        // 虚拟线程执行器
        this.virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();

        log.info("统一多级缓存管理器初始化完成: L1=Caffeine分片({}个分片,总容量={}), L2=Redis", shardCount, shardCount * 2000);
    }
    
    @Override
    public <T> Optional<T> get(String key, Class<T> valueType) {
        long startTime = System.currentTimeMillis();
        boolean hit = false;

        try {
            // 1. 先从L1本地缓存获取
            Object l1Value = getShard(key).getIfPresent(key);
            if (l1Value != null) {
                stats.recordL1Hit();
                metricsService.incrementCounter("cache.requests", "cache", "l1", "result", "hit");
                hit = true;
                log.debug("L1缓存命中: key={}", key);

                // 安全的类型转换
                try {
                    if (valueType.isInstance(l1Value)) {
                        return Optional.of(valueType.cast(l1Value));
                    } else {
                        // 类型不匹配，可能是序列化字符串，尝试反序列化
                        if (l1Value instanceof String) {
                            T deserializedValue = objectMapper.readValue((String) l1Value, valueType);
                            // 更新L1缓存为正确的对象类型
                            getShard(key).put(key, deserializedValue);
                            return Optional.of(deserializedValue);
                        } else {
                            // 类型不匹配且不是字符串，从缓存中移除
                            log.warn("L1缓存类型不匹配，移除缓存: key={}, expected={}, actual={}",
                                    key, valueType.getSimpleName(), l1Value.getClass().getSimpleName());
                            getShard(key).invalidate(key);
                        }
                    }
                } catch (Exception castException) {
                    log.warn("L1缓存类型转换失败，移除缓存: key={}", key, castException);
                    getShard(key).invalidate(key);
                }
            }

            stats.recordL1Miss();
            metricsService.incrementCounter("cache.requests", "cache", "l1", "result", "miss");

            // 2. 从L2 Redis缓存获取 - 使用统一重试服务
            try {
                String l2Value = retryService.executeRedisOperation("cache-read-" + key, () ->
                    redisTemplate.opsForValue().get(key));

                if (l2Value != null) {
                    stats.recordL2Hit();
                    metricsService.incrementCounter("cache.requests", "cache", "l2", "result", "hit");
                    hit = true;
                    log.debug("L2缓存命中: key={}", key);

                    // 反序列化并回写到L1缓存
                    T deserializedValue = objectMapper.readValue(l2Value, valueType);
                    getShard(key).put(key, deserializedValue);

                    return Optional.of(deserializedValue);
                }
            } catch (Exception e) {
                // 检查是否是连接工厂停止相关的异常
                Throwable rootCause = getRootCause(e);
                if (rootCause instanceof IllegalStateException &&
                    rootCause.getMessage() != null &&
                    (rootCause.getMessage().contains("STOPPING") || rootCause.getMessage().contains("STOPPED"))) {
                    log.debug("Redis连接工厂正在停止或已停止，跳过缓存读取: key={}", key);
                } else {
                    log.warn("Redis缓存读取失败: key={}", key, e);
                }
            }

            stats.recordL2Miss();
            metricsService.incrementCounter("cache.requests", "cache", "l2", "result", "miss");
            log.debug("缓存未命中: key={}", key);
            return Optional.empty();

        } catch (Exception e) {
            log.error("获取缓存失败: key={}", key, e);
            return Optional.empty();
        } finally {
            // 记录性能监控
            if (performanceMonitor != null && !isShuttingDown) {
                try {
                    long responseTime = System.currentTimeMillis() - startTime;
                    performanceMonitor.recordCacheAccess(key, hit, responseTime);
                } catch (Exception e) {
                    // 忽略关闭时的监控错误
                    log.debug("缓存性能监控记录失败（可能正在关闭）: {}", e.getMessage());
                }
            }
        }
    }
    
    @Override
    public <T> CompletableFuture<Optional<T>> getAsync(String key, Class<T> valueType) {
        return CompletableFuture.supplyAsync(() -> get(key, valueType), virtualThreadExecutor);
    }
    
    @Override
    public void put(String key, Object value, Duration ttl) {
        long startTime = System.currentTimeMillis();

        try {
            // 确保value不为null
            if (value == null) {
                log.warn("尝试缓存null值，跳过: key={}", key);
                return;
            }

            // 1. 写入L1本地缓存 - 直接存储对象
            getShard(key).put(key, value);

            // 2. 安全写入L2 Redis缓存 - 序列化为JSON字符串
            try {
                retryService.executeRedisOperation("cache-write-" + key, () -> {
                    try {
                        String serializedValue = objectMapper.writeValueAsString(value);
                        redisTemplate.opsForValue().set(key, serializedValue, ttl);
                        log.debug("缓存写入成功: key={}, ttl={}, valueType={}", key, ttl, value.getClass().getSimpleName());
                        return null;
                    } catch (IllegalStateException e) {
                        // 检查是否是连接工厂停止异常
                        if (e.getMessage() != null &&
                            (e.getMessage().contains("STOPPING") || e.getMessage().contains("STOPPED"))) {
                            log.debug("Redis连接工厂正在停止或已停止，跳过缓存写入: key={}", key);
                            return null; // 优雅处理，不抛出异常
                        }
                        throw new RuntimeException("Redis缓存写入失败", e);
                    } catch (Exception e) {
                        throw new RuntimeException("Redis缓存写入失败", e);
                    }
                });
            } catch (Exception e) {
                // 检查是否是连接工厂停止相关的异常
                Throwable rootCause = getRootCause(e);
                if (rootCause instanceof IllegalStateException &&
                    rootCause.getMessage() != null &&
                    (rootCause.getMessage().contains("STOPPING") || rootCause.getMessage().contains("STOPPED"))) {
                    log.debug("Redis连接工厂正在停止或已停止，跳过缓存写入: key={}", key);
                } else {
                    log.warn("Redis缓存写入最终失败: key={}", key, e);
                }
            }

            // 更新统计
            long cacheSize = getL1EstimatedSize();
            stats.updateCacheSize(cacheSize);
            metricsService.setGauge("cache.size", cacheSize, "cache", "l1");

        } catch (Exception e) {
            log.error("缓存写入失败: key={}, value={}", key, value, e);
            // 如果写入失败，从L1缓存中移除，保持一致性
            getShard(key).invalidate(key);
        } finally {
            // 记录性能监控
            if (performanceMonitor != null && !isShuttingDown) {
                try {
                    long responseTime = System.currentTimeMillis() - startTime;
                    performanceMonitor.recordCacheWrite(key, responseTime);
                } catch (Exception e) {
                    // 忽略关闭时的监控错误
                    log.debug("缓存性能监控记录失败（可能正在关闭）: {}", e.getMessage());
                }
            }
        }
    }
    
    @Override
    public CompletableFuture<Void> putAsync(String key, Object value, Duration ttl) {
        return CompletableFuture.runAsync(() -> put(key, value, ttl), virtualThreadExecutor);
    }
    
    @Override
    public void evict(String key) {
        try {
            // 从L1删除
            getShard(key).invalidate(key);
            
            // 从L2安全删除
            try {
                retryService.executeRedisOperation("cache-delete-" + key, () -> {
                    redisTemplate.delete(key);
                    return null;
                });
            } catch (Exception e) {
                log.warn("Redis缓存删除失败: key={}", key, e);
            }
            
            // 更新统计
            stats.updateCacheSize(getL1EstimatedSize());
            
            log.debug("缓存删除成功: key={}", key);
            
        } catch (Exception e) {
            log.error("缓存删除失败: key={}", key, e);
        }
    }
    
    @Override
    public CompletableFuture<Void> evictAsync(String key) {
        return CompletableFuture.runAsync(() -> evict(key), virtualThreadExecutor);
    }

    /**
     * 批量获取缓存值
     * 优化多个键的获取性能
     */
    public <T> CompletableFuture<java.util.Map<String, T>> getBatch(java.util.Set<String> keys, Class<T> valueType) {
        return CompletableFuture.supplyAsync(() -> {
            java.util.Map<String, T> result = new java.util.concurrent.ConcurrentHashMap<>();

            // 先从L1缓存批量获取
            java.util.Set<String> missedKeys = new java.util.HashSet<>();
            for (String key : keys) {
                Object l1Value = getShard(key).getIfPresent(key);
                if (l1Value != null && valueType.isInstance(l1Value)) {
                    result.put(key, valueType.cast(l1Value));
                    metricsService.incrementCounter("cache.requests", "cache", "l1", "result", "hit");
                } else {
                    missedKeys.add(key);
                    metricsService.incrementCounter("cache.requests", "cache", "l1", "result", "miss");
                }
            }

            // 从L2缓存批量获取未命中的键
            if (!missedKeys.isEmpty()) {
                try {
                    java.util.List<String> l2Values = retryService.executeRedisOperation("cache-batch-read", () ->
                        redisTemplate.opsForValue().multiGet(missedKeys));

                    int index = 0;
                    for (String key : missedKeys) {
                        if (index < l2Values.size() && l2Values.get(index) != null) {
                            try {
                                T deserializedValue = objectMapper.readValue(l2Values.get(index), valueType);
                                result.put(key, deserializedValue);
                                // 回写到L1缓存
                                getShard(key).put(key, deserializedValue);
                                metricsService.incrementCounter("cache.requests", "cache", "l2", "result", "hit");
                            } catch (Exception e) {
                                log.warn("批量反序列化失败: key={}", key, e);
                                metricsService.incrementCounter("cache.requests", "cache", "l2", "result", "miss");
                            }
                        } else {
                            metricsService.incrementCounter("cache.requests", "cache", "l2", "result", "miss");
                        }
                        index++;
                    }
                } catch (Exception e) {
                    log.warn("批量Redis读取失败: keys={}", missedKeys, e);
                }
            }

            return result;
        }, virtualThreadExecutor);
    }

    /**
     * 批量设置缓存值
     * 优化多个键值对的写入性能
     */
    public CompletableFuture<Void> putBatch(java.util.Map<String, Object> keyValues, Duration ttl) {
        return CompletableFuture.runAsync(() -> {
            // 批量写入L1缓存
            keyValues.forEach((k, v) -> getShard(k).put(k, v));

            // 批量写入L2缓存
            try {
                java.util.Map<String, String> serializedValues = new java.util.HashMap<>();
                for (java.util.Map.Entry<String, Object> entry : keyValues.entrySet()) {
                    try {
                        String serializedValue = objectMapper.writeValueAsString(entry.getValue());
                        serializedValues.put(entry.getKey(), serializedValue);
                    } catch (Exception e) {
                        log.warn("批量序列化失败: key={}", entry.getKey(), e);
                    }
                }

                if (!serializedValues.isEmpty()) {
                    retryService.executeRedisOperation("cache-batch-write", () -> {
                        redisTemplate.opsForValue().multiSet(serializedValues);
                        // 设置过期时间（Redis不支持批量设置TTL，需要逐个设置）
                        for (String key : serializedValues.keySet()) {
                            redisTemplate.expire(key, ttl);
                        }
                        return null;
                    });
                }
            } catch (Exception e) {
                log.warn("批量Redis写入失败: keys={}", keyValues.keySet(), e);
            }

            // 更新统计
            long cacheSize = getL1EstimatedSize();
            stats.updateCacheSize(cacheSize);
            metricsService.setGauge("cache.size", cacheSize, "cache", "l1");

        }, virtualThreadExecutor);
    }
    
    @Override
    public void clear() {
        try {
            for (Cache<String, Object> shard : l1CacheShards) {
                shard.invalidateAll();
            }
            // 注意：这里不清空整个Redis，只清空相关键
            log.info("L1缓存已清空");

            // 更新统计
            stats.updateCacheSize(0);

        } catch (Exception e) {
            log.error("清空缓存失败", e);
        }
    }

    /**
     * 清理错误的占位符缓存数据
     */
    public void clearPlaceholderData() {
        try {
            log.info("开始清理占位符缓存数据...");

            // 清理L1缓存中的占位符数据
            for (Cache<String, Object> shard : l1CacheShards) {
                shard.asMap().entrySet().removeIf(entry -> {
                    Object value = entry.getValue();
                    if (value instanceof String) {
                        String strValue = (String) value;
                        return strValue.startsWith("depth_data_") ||
                               strValue.startsWith("kline_data_") ||
                               strValue.startsWith("price_data_") ||
                               strValue.startsWith("stats_data_");
                    }
                    return false;
                });
            }

            // 清理Redis中的占位符数据（通过模式匹配）
            try {
                retryService.executeRedisOperation("cache-cleanup", () -> {
                    // 获取所有可能包含占位符数据的键
                    Set<String> depthKeys = redisTemplate.keys("depth:*");
                    Set<String> klineKeys = redisTemplate.keys("kline:*");
                    Set<String> priceKeys = redisTemplate.keys("latest_price:*");
                    Set<String> statsKeys = redisTemplate.keys("stats:*");

                    // 清理深度数据占位符
                    if (depthKeys != null) {
                        for (String key : depthKeys) {
                            String value = redisTemplate.opsForValue().get(key);
                            if (value != null && value.startsWith("\"depth_data_")) {
                                redisTemplate.delete(key);
                                log.debug("清理Redis深度占位符数据: key={}", key);
                            }
                        }
                    }

                    // 清理K线数据占位符
                    if (klineKeys != null) {
                        for (String key : klineKeys) {
                            String value = redisTemplate.opsForValue().get(key);
                            if (value != null && value.startsWith("\"kline_data_")) {
                                redisTemplate.delete(key);
                                log.debug("清理Redis K线占位符数据: key={}", key);
                            }
                        }
                    }

                    // 清理价格数据占位符
                    if (priceKeys != null) {
                        for (String key : priceKeys) {
                            String value = redisTemplate.opsForValue().get(key);
                            if (value != null && value.startsWith("\"price_data_")) {
                                redisTemplate.delete(key);
                                log.debug("清理Redis价格占位符数据: key={}", key);
                            }
                        }
                    }

                    // 清理统计数据占位符
                    if (statsKeys != null) {
                        for (String key : statsKeys) {
                            String value = redisTemplate.opsForValue().get(key);
                            if (value != null && value.startsWith("\"stats_data_")) {
                                redisTemplate.delete(key);
                                log.debug("清理Redis统计占位符数据: key={}", key);
                            }
                        }
                    }

                    return null;
                });
            } catch (Exception e) {
                log.warn("清理Redis占位符数据失败", e);
            }

            log.info("占位符缓存数据清理完成");

        } catch (Exception e) {
            log.error("清理占位符缓存数据失败", e);
        }
    }
    
    @Override
    public boolean exists(String key) {
        try {
            // 检查L1缓存
            if (getShard(key).getIfPresent(key) != null) {
                return true;
            }
            
            // 检查L2缓存 - 使用重试服务
            Boolean redisExists = retryService.executeRedisOperation("cache-exists-" + key,
                    () -> redisTemplate.hasKey(key));
            return Boolean.TRUE.equals(redisExists);
            
        } catch (Exception e) {
            log.error("检查缓存存在性失败: key={}", key, e);
            return false;
        }
    }
    
    @Override
    public CacheStats getStats() {
        // 更新L1缓存大小统计
        stats.updateCacheSize(getL1EstimatedSize());
        return stats;
    }
    
    @Override
    public CompletableFuture<Void> warmUp(String... keys) {
        return CompletableFuture.runAsync(() -> {
            log.info("开始缓存预热: keys={}", (Object) keys);
            
            for (String key : keys) {
                try {
                    // 这里可以根据业务需求从数据源加载数据
                    // 暂时只是检查缓存是否存在
                    exists(key);
                } catch (Exception e) {
                    log.error("预热缓存失败: key={}", key, e);
                }
            }
            
            log.info("缓存预热完成: 处理了{}个键", keys.length);
        }, virtualThreadExecutor);
    }

    /**
     * 获取异常的根本原因
     */
    private Throwable getRootCause(Throwable throwable) {
        Throwable rootCause = throwable;
        while (rootCause.getCause() != null && rootCause.getCause() != rootCause) {
            rootCause = rootCause.getCause();
        }
        return rootCause;
    }

    /**
     * 检查Redis连接工厂是否可用（不执行实际连接操作）
     */
    private boolean isRedisConnectionFactoryAvailable() {
        try {
            // 检查是否正在关闭
            if (isShuttingDown) {
                return false;
            }

            // 检查连接工厂状态
            if (redisTemplate == null || redisTemplate.getConnectionFactory() == null) {
                return false;
            }

            // 检查LettuceConnectionFactory的状态
            RedisConnectionFactory factory = redisTemplate.getConnectionFactory();
            if (factory instanceof org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory) {
                org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory lettuceFactory =
                    (org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory) factory;

                // 使用反射检查内部状态，避免触发连接操作
                try {
                    // 通过反射获取内部状态字段
                    java.lang.reflect.Field stateField = lettuceFactory.getClass().getDeclaredField("state");
                    stateField.setAccessible(true);
                    Object state = stateField.get(lettuceFactory);

                    // 检查状态是否为STOPPED或STOPPING
                    if (state != null) {
                        String stateStr = state.toString();
                        if ("STOPPED".equals(stateStr) || "STOPPING".equals(stateStr)) {
                            log.debug("Redis连接工厂状态为: {}", stateStr);
                            return false;
                        }
                    }

                    return true;

                } catch (NoSuchFieldException | IllegalAccessException e) {
                    // 如果反射失败，回退到连接测试方式
                    log.debug("无法通过反射检查连接工厂状态，回退到连接测试: {}", e.getMessage());

                    try {
                        // 尝试获取连接来检查状态，如果抛出STOPPING异常则说明正在关闭
                        RedisConnection connection = lettuceFactory.getConnection();
                        connection.close();
                        return true;
                    } catch (IllegalStateException ex) {
                        if (ex.getMessage() != null &&
                            (ex.getMessage().contains("STOPPING") || ex.getMessage().contains("STOPPED"))) {
                            log.debug("Redis连接工厂正在停止或已停止: {}", ex.getMessage());
                            return false;
                        }
                        // 其他异常可能是临时的，返回true继续尝试
                        return true;
                    } catch (Exception ex) {
                        log.debug("Redis连接工厂状态检查异常: {}", ex.getMessage());
                        return false;
                    }
                }
            }

            return true;

        } catch (Exception e) {
            log.debug("Redis连接工厂可用性检查失败: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查Redis连接是否有效
     */
    private boolean isRedisConnectionValid() {
        try {
            // 首先检查连接工厂是否可用
            if (!isRedisConnectionFactoryAvailable()) {
                return false;
            }

            // 使用更可靠的连接检查方式
            String testResult = redisTemplate.execute((RedisCallback<String>) connection -> {
                try {
                    return connection.ping();
                } catch (Exception e) {
                    log.debug("Redis ping失败: {}", e.getMessage());
                    return null;
                }
            });

            boolean isValid = "PONG".equals(testResult);
            if (!isValid) {
                log.debug("Redis连接检查失败，ping结果: {}", testResult);
            }
            return isValid;

        } catch (Exception e) {
            log.debug("Redis连接检查异常: {} - {}", e.getClass().getSimpleName(), e.getMessage());
            return false;
        }
    }

    /**
     * 轻量级Redis连接健康检查 - 减少连接检查频率，避免连接泄漏
     */
    private boolean isRedisConnectionHealthy() {
        try {
            // 检查是否正在关闭
            if (isShuttingDown) {
                return false;
            }

            // 检查连接工厂状态
            if (redisTemplate == null || redisTemplate.getConnectionFactory() == null) {
                return false;
            }

            // 简单检查，不执行实际的ping操作，减少连接使用
            return true;

        } catch (Exception e) {
            log.debug("Redis连接健康检查异常: {}", e.getMessage());
            return false;
        }
    }

    // 旧的safeRedisOperation方法已被UnifiedRetryService替换

    // 旧的safeRedisOperationAsync方法已被UnifiedRetryService替换

    /**
     * Redis操作接口
     */
    @FunctionalInterface
    private interface RedisOperation<T> {
        T execute() throws Exception;
    }

    /**
     * 获取分片索引
     */
    /**
     * 获取L1缓存总大小
     */
    private long getL1EstimatedSize() {
        long totalSize = 0;
        if (l1CacheShards != null) {
            for (Cache<String, Object> shard : l1CacheShards) {
                totalSize += shard.estimatedSize();
            }
        }
        return totalSize;
    }

    /**
     * 获取分片索引
     */
    public int getShardIndex(String key) {
        return Math.abs(key.hashCode()) % shardCount;
    }

    /**
     * 获取指定的分片缓存
     */
    public Cache<String, Object> getShard(String key) {
        return l1CacheShards[getShardIndex(key)];
    }

    /**
     * 获取指定索引的分片缓存
     */
    public Cache<String, Object> getShard(int shardIndex) {
        if (shardIndex < 0 || shardIndex >= shardCount) {
            throw new IllegalArgumentException("分片索引超出范围: " + shardIndex);
        }
        return l1CacheShards[shardIndex];
    }

    /**
     * 获取分片数量
     */
    public int getShardCount() {
        return shardCount;
    }

    @PreDestroy
    public void shutdown() {
        log.info("关闭多级缓存管理器...");
        isShuttingDown = true;

        try {
            // 清空缓存
            clear();
        } catch (Exception e) {
            log.warn("清空缓存时出现异常: {}", e.getMessage());
        }

        log.info("多级缓存管理器已关闭");
    }
}
