package com.trading.common.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 缓存性能监控器
 * 监控缓存命中率、响应时间、内存使用等关键指标
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class CachePerformanceMonitor {
    
    private static final Logger log = LoggerFactory.getLogger(CachePerformanceMonitor.class);
    
    @Autowired
    @Lazy
    private MultiLevelCacheManager cacheManager;
    
    private ScheduledExecutorService monitorExecutor;
    private final java.util.concurrent.atomic.AtomicBoolean isMonitoring = new java.util.concurrent.atomic.AtomicBoolean(false);
    
    // 性能统计
    private final LongAdder totalRequests = new LongAdder();
    private final LongAdder totalHits = new LongAdder();
    private final LongAdder totalMisses = new LongAdder();
    private final LongAdder totalResponseTime = new LongAdder();
    private final AtomicLong maxResponseTime = new AtomicLong(0);
    private final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
    
    // 按业务类型统计
    private final ConcurrentHashMap<String, BusinessTypeStats> businessStats = new ConcurrentHashMap<>();
    
    // 热点数据统计
    private final ConcurrentHashMap<String, HotKeyStats> hotKeyStats = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void initialize() {
        if (isMonitoring.compareAndSet(false, true)) {
            log.info("初始化缓存性能监控器...");
            
            monitorExecutor = Executors.newScheduledThreadPool(2);
            
            // 定期报告性能指标
            monitorExecutor.scheduleAtFixedRate(
                    this::reportPerformanceMetrics,
                    60, 60, TimeUnit.SECONDS);
            
            // 定期清理过期统计数据
            monitorExecutor.scheduleAtFixedRate(
                    this::cleanupExpiredStats,
                    300, 300, TimeUnit.SECONDS);
            
            log.info("缓存性能监控器初始化完成");
        }
    }
    
    /**
     * 记录缓存访问
     */
    public void recordCacheAccess(String key, boolean hit, long responseTimeMs) {
        totalRequests.increment();
        totalResponseTime.add(responseTimeMs);
        
        // 更新最大最小响应时间
        updateResponseTimeStats(responseTimeMs);
        
        if (hit) {
            totalHits.increment();
        } else {
            totalMisses.increment();
        }
        
        // 按业务类型统计
        String businessType = extractBusinessType(key);
        BusinessTypeStats stats = businessStats.computeIfAbsent(businessType, k -> new BusinessTypeStats());
        stats.recordAccess(hit, responseTimeMs);
        
        // 热点数据统计
        HotKeyStats hotStats = hotKeyStats.computeIfAbsent(key, k -> new HotKeyStats());
        hotStats.recordAccess(hit);
    }
    
    /**
     * 记录缓存写入
     */
    public void recordCacheWrite(String key, long responseTimeMs) {
        String businessType = extractBusinessType(key);
        BusinessTypeStats stats = businessStats.computeIfAbsent(businessType, k -> new BusinessTypeStats());
        stats.recordWrite(responseTimeMs);
    }
    
    /**
     * 获取整体性能统计
     */
    public OverallPerformanceStats getOverallStats() {
        long requests = totalRequests.sum();
        long hits = totalHits.sum();
        long misses = totalMisses.sum();
        long totalTime = totalResponseTime.sum();
        
        return OverallPerformanceStats.builder()
                .totalRequests(requests)
                .totalHits(hits)
                .totalMisses(misses)
                .hitRate(requests > 0 ? (double) hits / requests : 0.0)
                .averageResponseTime(requests > 0 ? (double) totalTime / requests : 0.0)
                .maxResponseTime(maxResponseTime.get())
                .minResponseTime(minResponseTime.get() == Long.MAX_VALUE ? 0 : minResponseTime.get())
                .cacheStats(cacheManager.getStats())
                .build();
    }
    
    /**
     * 获取业务类型统计
     */
    public ConcurrentHashMap<String, BusinessTypeStats> getBusinessTypeStats() {
        return new ConcurrentHashMap<>(businessStats);
    }
    
    /**
     * 获取热点数据统计
     */
    public java.util.List<HotKeyInfo> getHotKeys(int topN) {
        return hotKeyStats.entrySet().stream()
                .map(entry -> HotKeyInfo.builder()
                        .key(entry.getKey())
                        .accessCount(entry.getValue().getAccessCount())
                        .hitCount(entry.getValue().getHitCount())
                        .hitRate(entry.getValue().getHitRate())
                        .lastAccessTime(entry.getValue().getLastAccessTime())
                        .build())
                .sorted((a, b) -> Long.compare(b.getAccessCount(), a.getAccessCount()))
                .limit(topN)
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 更新响应时间统计
     */
    private void updateResponseTimeStats(long responseTimeMs) {
        maxResponseTime.updateAndGet(current -> Math.max(current, responseTimeMs));
        minResponseTime.updateAndGet(current -> Math.min(current, responseTimeMs));
    }
    
    /**
     * 提取业务类型
     */
    private String extractBusinessType(String key) {
        if (key.startsWith("latest_price:")) return "latest_price";
        if (key.startsWith("kline:")) return "kline";
        if (key.startsWith("depth:")) return "depth";
        if (key.startsWith("trade:")) return "trade";
        if (key.startsWith("stats:")) return "stats";
        return "unknown";
    }
    
    /**
     * 定期报告性能指标
     */
    private void reportPerformanceMetrics() {
        if (!isMonitoring.get()) {
            log.warn("缓存性能监控已停止，跳过报告。");
            return;
        }
        try {
            OverallPerformanceStats stats = getOverallStats();
            
            log.info("=== 缓存性能监控报告 ===");
            log.info("总请求数: {}", stats.getTotalRequests());
            log.info("缓存命中数: {}", stats.getTotalHits());
            log.info("缓存未命中数: {}", stats.getTotalMisses());
            log.info("命中率: {:.2f}%", stats.getHitRate() * 100);
            log.info("平均响应时间: {:.2f}ms", stats.getAverageResponseTime());
            log.info("最大响应时间: {}ms", stats.getMaxResponseTime());
            log.info("最小响应时间: {}ms", stats.getMinResponseTime());
            
            // 按业务类型报告
            businessStats.forEach((type, typeStats) -> {
                log.info("业务类型[{}] - 请求: {}, 命中率: {:.2f}%, 平均响应: {:.2f}ms", 
                        type, typeStats.getRequestCount(), 
                        typeStats.getHitRate() * 100, typeStats.getAverageResponseTime());
            });
            
            // 报告热点数据
            java.util.List<HotKeyInfo> hotKeys = getHotKeys(5);
            if (!hotKeys.isEmpty()) {
                log.info("=== 热点数据TOP5 ===");
                hotKeys.forEach(hotKey -> 
                    log.info("热点键[{}] - 访问: {}, 命中率: {:.2f}%", 
                            hotKey.getKey(), hotKey.getAccessCount(), hotKey.getHitRate() * 100));
            }
            
        } catch (Exception e) {
            log.error("缓存性能监控报告失败", e);
        }
    }
    
    /**
     * 清理过期统计数据
     */
    private void cleanupExpiredStats() {
        if (!isMonitoring.get()) {
            log.warn("缓存性能监控已停止，跳过清理。");
            return;
        }
        try {
            Instant cutoff = Instant.now().minus(Duration.ofHours(1));
            
            hotKeyStats.entrySet().removeIf(entry -> 
                    entry.getValue().getLastAccessTime().isBefore(cutoff));
            
            log.debug("清理过期缓存统计数据完成");
        } catch (Exception e) {
            log.error("清理过期缓存统计数据失败", e);
        }
    }
    
    @PreDestroy
    public void shutdown() {
        if (isMonitoring.compareAndSet(true, false)) {
            log.info("开始关闭缓存性能监控器...");
            if (monitorExecutor != null) {
                monitorExecutor.shutdownNow();
                try {
                    if (!monitorExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        log.warn("缓存监控执行器在5秒内未能完全终止。");
                    }
                } catch (InterruptedException e) {
                    log.error("关闭缓存监控时发生中断异常。", e);
                    Thread.currentThread().interrupt();
                }
            }
            log.info("缓存性能监控器已确认关闭。");
        }
    }
    
    // 内部类定义
    
    @lombok.Data
    @lombok.Builder
    public static class OverallPerformanceStats {
        private long totalRequests;
        private long totalHits;
        private long totalMisses;
        private double hitRate;
        private double averageResponseTime;
        private long maxResponseTime;
        private long minResponseTime;
        private Object cacheStats;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class HotKeyInfo {
        private String key;
        private long accessCount;
        private long hitCount;
        private double hitRate;
        private Instant lastAccessTime;
    }
    
    public static class BusinessTypeStats {
        private final AtomicLong requestCount = new AtomicLong(0);
        private final AtomicLong hitCount = new AtomicLong(0);
        private final AtomicLong writeCount = new AtomicLong(0);
        private final AtomicLong totalResponseTime = new AtomicLong(0);
        
        public void recordAccess(boolean hit, long responseTimeMs) {
            requestCount.incrementAndGet();
            totalResponseTime.addAndGet(responseTimeMs);
            if (hit) {
                hitCount.incrementAndGet();
            }
        }
        
        public void recordWrite(long responseTimeMs) {
            writeCount.incrementAndGet();
            totalResponseTime.addAndGet(responseTimeMs);
        }
        
        public long getRequestCount() { return requestCount.get(); }
        public long getHitCount() { return hitCount.get(); }
        public long getWriteCount() { return writeCount.get(); }
        public double getHitRate() { 
            long requests = requestCount.get();
            return requests > 0 ? (double) hitCount.get() / requests : 0.0; 
        }
        public double getAverageResponseTime() { 
            long requests = requestCount.get() + writeCount.get();
            return requests > 0 ? (double) totalResponseTime.get() / requests : 0.0; 
        }
    }
    
    public static class HotKeyStats {
        private final AtomicLong accessCount = new AtomicLong(0);
        private final AtomicLong hitCount = new AtomicLong(0);
        private volatile Instant lastAccessTime = Instant.now();
        
        public void recordAccess(boolean hit) {
            accessCount.incrementAndGet();
            if (hit) {
                hitCount.incrementAndGet();
            }
            lastAccessTime = Instant.now();
        }
        
        public long getAccessCount() { return accessCount.get(); }
        public long getHitCount() { return hitCount.get(); }
        public double getHitRate() { 
            long accesses = accessCount.get();
            return accesses > 0 ? (double) hitCount.get() / accesses : 0.0; 
        }
        public Instant getLastAccessTime() { return lastAccessTime; }
    }
}
