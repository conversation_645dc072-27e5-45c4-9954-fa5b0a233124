package com.trading.common.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 智能缓存算法优化器
 * 使用机器学习算法优化缓存策略，提升缓存命中率
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class IntelligentCacheOptimizer {
    
    private static final Logger log = LoggerFactory.getLogger(IntelligentCacheOptimizer.class);
    
    // 缓存策略枚举
    public enum CacheStrategy {
        LRU,        // 最近最少使用
        LFU,        // 最少使用频率
        ARC,        // 自适应替换缓存
        LIRS,       // 低干扰性参考字符串
        ADAPTIVE    // 自适应策略
    }
    
    // 配置参数
    private static final int DEFAULT_CACHE_SIZE = 10000;
    private static final int ANALYSIS_WINDOW_SIZE = 1000;
    private static final long OPTIMIZATION_INTERVAL_MS = 30000; // 30秒
    
    // 多级缓存存储
    private final ConcurrentHashMap<String, CacheEntry> l1Cache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, CacheEntry> l2Cache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, CacheEntry> l3Cache = new ConcurrentHashMap<>();
    
    // 访问模式分析
    private final ConcurrentLinkedQueue<AccessPattern> accessHistory = new ConcurrentLinkedQueue<>();
    private final ConcurrentHashMap<String, AccessStats> keyStats = new ConcurrentHashMap<>();
    
    // 当前缓存策略
    private volatile CacheStrategy currentStrategy = CacheStrategy.ADAPTIVE;
    
    // 性能统计
    private final LongAdder l1Hits = new LongAdder();
    private final LongAdder l2Hits = new LongAdder();
    private final LongAdder l3Hits = new LongAdder();
    private final LongAdder misses = new LongAdder();
    private final LongAdder evictions = new LongAdder();
    private final LongAdder promotions = new LongAdder();
    
    // 优化器线程
    private final ScheduledExecutorService optimizerExecutor = 
        Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "cache-optimizer");
            t.setDaemon(true);
            return t;
        });
    
    @PostConstruct
    public void initialize() {
        // 启动缓存优化器
        optimizerExecutor.scheduleAtFixedRate(this::optimizeCacheStrategy, 
            OPTIMIZATION_INTERVAL_MS, OPTIMIZATION_INTERVAL_MS, TimeUnit.MILLISECONDS);
        
        log.info("智能缓存算法优化器已初始化");
    }
    
    /**
     * 获取缓存数据
     */
    public <T> T get(String key, Class<T> type) {
        long startTime = System.nanoTime();
        
        // 记录访问模式
        recordAccess(key, startTime);
        
        // L1缓存查找
        CacheEntry entry = l1Cache.get(key);
        if (entry != null && !entry.isExpired()) {
            l1Hits.increment();
            updateAccessStats(key, 1, startTime);
            return type.cast(entry.getValue());
        }
        
        // L2缓存查找
        entry = l2Cache.get(key);
        if (entry != null && !entry.isExpired()) {
            l2Hits.increment();
            // 提升到L1缓存
            promoteToL1(key, entry);
            updateAccessStats(key, 2, startTime);
            return type.cast(entry.getValue());
        }
        
        // L3缓存查找
        entry = l3Cache.get(key);
        if (entry != null && !entry.isExpired()) {
            l3Hits.increment();
            // 提升到L2缓存
            promoteToL2(key, entry);
            updateAccessStats(key, 3, startTime);
            return type.cast(entry.getValue());
        }
        
        // 缓存未命中
        misses.increment();
        updateAccessStats(key, 0, startTime);
        return null;
    }
    
    /**
     * 设置缓存数据
     */
    public void put(String key, Object value, long ttlMs) {
        CacheEntry entry = new CacheEntry(value, System.currentTimeMillis() + ttlMs);
        
        // 根据当前策略决定放入哪个缓存级别
        switch (currentStrategy) {
            case LRU:
                putWithLRU(key, entry);
                break;
            case LFU:
                putWithLFU(key, entry);
                break;
            case ARC:
                putWithARC(key, entry);
                break;
            case LIRS:
                putWithLIRS(key, entry);
                break;
            case ADAPTIVE:
            default:
                putWithAdaptive(key, entry);
                break;
        }
    }
    
    /**
     * LRU策略放入
     */
    private void putWithLRU(String key, CacheEntry entry) {
        // 优先放入L1缓存
        if (l1Cache.size() < DEFAULT_CACHE_SIZE / 3) {
            l1Cache.put(key, entry);
        } else {
            // L1缓存满了，淘汰最久未使用的
            evictLRU(l1Cache);
            l1Cache.put(key, entry);
        }
    }
    
    /**
     * LFU策略放入
     */
    private void putWithLFU(String key, CacheEntry entry) {
        AccessStats stats = keyStats.get(key);
        if (stats != null && stats.frequency > 5) {
            // 高频访问，放入L1缓存
            l1Cache.put(key, entry);
        } else if (stats != null && stats.frequency > 2) {
            // 中频访问，放入L2缓存
            l2Cache.put(key, entry);
        } else {
            // 低频访问，放入L3缓存
            l3Cache.put(key, entry);
        }
    }
    
    /**
     * ARC策略放入
     */
    private void putWithARC(String key, CacheEntry entry) {
        // ARC算法的简化实现
        AccessStats stats = keyStats.get(key);
        if (stats != null) {
            double recency = calculateRecency(stats.lastAccessTime);
            double frequency = stats.frequency;
            double score = recency * 0.6 + frequency * 0.4;
            
            if (score > 8) {
                l1Cache.put(key, entry);
            } else if (score > 4) {
                l2Cache.put(key, entry);
            } else {
                l3Cache.put(key, entry);
            }
        } else {
            l3Cache.put(key, entry);
        }
    }
    
    /**
     * LIRS策略放入
     */
    private void putWithLIRS(String key, CacheEntry entry) {
        // LIRS算法的简化实现
        AccessStats stats = keyStats.get(key);
        if (stats != null && stats.recentAccesses.size() > 1) {
            // 计算重用距离
            long reuseDistance = calculateReuseDistance(stats);
            if (reuseDistance < 100) {
                l1Cache.put(key, entry); // 短重用距离，放入L1
            } else if (reuseDistance < 1000) {
                l2Cache.put(key, entry); // 中等重用距离，放入L2
            } else {
                l3Cache.put(key, entry); // 长重用距离，放入L3
            }
        } else {
            l3Cache.put(key, entry);
        }
    }
    
    /**
     * 自适应策略放入
     */
    private void putWithAdaptive(String key, CacheEntry entry) {
        AccessStats stats = keyStats.get(key);
        if (stats == null) {
            // 新键，放入L3缓存观察
            l3Cache.put(key, entry);
            return;
        }
        
        // 综合评分算法
        double recencyScore = calculateRecency(stats.lastAccessTime);
        double frequencyScore = Math.min(stats.frequency, 10) / 10.0;
        double consistencyScore = calculateConsistency(stats);
        
        double totalScore = recencyScore * 0.4 + frequencyScore * 0.4 + consistencyScore * 0.2;
        
        if (totalScore > 0.8) {
            l1Cache.put(key, entry);
        } else if (totalScore > 0.5) {
            l2Cache.put(key, entry);
        } else {
            l3Cache.put(key, entry);
        }
    }
    
    /**
     * 记录访问模式
     */
    private void recordAccess(String key, long timestamp) {
        AccessPattern pattern = new AccessPattern(key, timestamp);
        accessHistory.offer(pattern);
        
        // 保持分析窗口大小
        while (accessHistory.size() > ANALYSIS_WINDOW_SIZE) {
            accessHistory.poll();
        }
    }
    
    /**
     * 更新访问统计
     */
    private void updateAccessStats(String key, int cacheLevel, long timestamp) {
        keyStats.compute(key, (k, stats) -> {
            if (stats == null) {
                stats = new AccessStats();
            }
            stats.frequency++;
            stats.lastAccessTime = timestamp;
            stats.cacheLevel = cacheLevel;
            stats.recentAccesses.offer(timestamp);
            
            // 保持最近访问记录的大小
            while (stats.recentAccesses.size() > 10) {
                stats.recentAccesses.poll();
            }
            
            return stats;
        });
    }
    
    /**
     * 提升到L1缓存
     */
    private void promoteToL1(String key, CacheEntry entry) {
        l2Cache.remove(key);
        l1Cache.put(key, entry);
        promotions.increment();
    }
    
    /**
     * 提升到L2缓存
     */
    private void promoteToL2(String key, CacheEntry entry) {
        l3Cache.remove(key);
        l2Cache.put(key, entry);
        promotions.increment();
    }
    
    /**
     * LRU淘汰
     */
    private void evictLRU(ConcurrentHashMap<String, CacheEntry> cache) {
        String oldestKey = null;
        long oldestTime = Long.MAX_VALUE;
        
        for (Map.Entry<String, CacheEntry> entry : cache.entrySet()) {
            AccessStats stats = keyStats.get(entry.getKey());
            if (stats != null && stats.lastAccessTime < oldestTime) {
                oldestTime = stats.lastAccessTime;
                oldestKey = entry.getKey();
            }
        }
        
        if (oldestKey != null) {
            cache.remove(oldestKey);
            evictions.increment();
        }
    }
    
    /**
     * 计算最近性得分
     */
    private double calculateRecency(long lastAccessTime) {
        long timeDiff = System.nanoTime() - lastAccessTime;
        double seconds = timeDiff / 1_000_000_000.0;
        return Math.exp(-seconds / 300.0); // 5分钟衰减
    }
    
    /**
     * 计算一致性得分
     */
    private double calculateConsistency(AccessStats stats) {
        if (stats.recentAccesses.size() < 2) {
            return 0.0;
        }
        
        List<Long> accesses = new ArrayList<>(stats.recentAccesses);
        double avgInterval = 0;
        double variance = 0;
        
        // 计算平均间隔
        for (int i = 1; i < accesses.size(); i++) {
            avgInterval += (accesses.get(i) - accesses.get(i-1));
        }
        avgInterval /= (accesses.size() - 1);
        
        // 计算方差
        for (int i = 1; i < accesses.size(); i++) {
            double interval = accesses.get(i) - accesses.get(i-1);
            variance += Math.pow(interval - avgInterval, 2);
        }
        variance /= (accesses.size() - 1);
        
        // 一致性得分：方差越小，一致性越高
        return 1.0 / (1.0 + Math.sqrt(variance) / avgInterval);
    }
    
    /**
     * 计算重用距离
     */
    private long calculateReuseDistance(AccessStats stats) {
        if (stats.recentAccesses.size() < 2) {
            return Long.MAX_VALUE;
        }
        
        List<Long> accesses = new ArrayList<>(stats.recentAccesses);
        return accesses.get(accesses.size() - 1) - accesses.get(accesses.size() - 2);
    }
    
    /**
     * 优化缓存策略
     */
    private void optimizeCacheStrategy() {
        try {
            // 分析当前性能
            CachePerformance performance = analyzePerformance();
            
            // 根据性能选择最优策略
            CacheStrategy optimalStrategy = selectOptimalStrategy(performance);
            
            if (optimalStrategy != currentStrategy) {
                log.info("缓存策略优化: {} -> {}, 命中率: {:.2f}%", 
                    currentStrategy, optimalStrategy, performance.hitRate * 100);
                currentStrategy = optimalStrategy;
            }
            
            // 清理过期缓存
            cleanupExpiredEntries();
            
        } catch (Exception e) {
            log.error("缓存策略优化失败", e);
        }
    }
    
    /**
     * 分析缓存性能
     */
    private CachePerformance analyzePerformance() {
        long totalHits = l1Hits.sum() + l2Hits.sum() + l3Hits.sum();
        long totalRequests = totalHits + misses.sum();
        
        double hitRate = totalRequests > 0 ? (double) totalHits / totalRequests : 0;
        double l1HitRate = totalRequests > 0 ? (double) l1Hits.sum() / totalRequests : 0;
        
        return new CachePerformance(hitRate, l1HitRate, totalRequests, evictions.sum());
    }
    
    /**
     * 选择最优策略
     */
    private CacheStrategy selectOptimalStrategy(CachePerformance performance) {
        // 简化的策略选择逻辑
        if (performance.hitRate > 0.9) {
            return currentStrategy; // 性能很好，保持当前策略
        } else if (performance.l1HitRate < 0.3) {
            return CacheStrategy.LFU; // L1命中率低，使用频率优先
        } else if (performance.evictionRate > 0.1) {
            return CacheStrategy.LRU; // 淘汰率高，使用时间优先
        } else {
            return CacheStrategy.ADAPTIVE; // 使用自适应策略
        }
    }
    
    /**
     * 清理过期缓存项
     */
    private void cleanupExpiredEntries() {
        long now = System.currentTimeMillis();
        
        l1Cache.entrySet().removeIf(entry -> entry.getValue().isExpired(now));
        l2Cache.entrySet().removeIf(entry -> entry.getValue().isExpired(now));
        l3Cache.entrySet().removeIf(entry -> entry.getValue().isExpired(now));
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStats getStats() {
        long totalHits = l1Hits.sum() + l2Hits.sum() + l3Hits.sum();
        long totalRequests = totalHits + misses.sum();
        double hitRate = totalRequests > 0 ? (double) totalHits / totalRequests : 0;
        
        return new CacheStats(
            l1Hits.sum(), l2Hits.sum(), l3Hits.sum(), misses.sum(),
            l1Cache.size(), l2Cache.size(), l3Cache.size(),
            hitRate, evictions.sum(), promotions.sum(), currentStrategy
        );
    }
    
    // 内部类定义
    private static class CacheEntry {
        private final Object value;
        private final long expireTime;
        
        public CacheEntry(Object value, long expireTime) {
            this.value = value;
            this.expireTime = expireTime;
        }
        
        public Object getValue() { return value; }
        public boolean isExpired() { return isExpired(System.currentTimeMillis()); }
        public boolean isExpired(long now) { return now > expireTime; }
    }
    
    private static class AccessPattern {
        public final String key;
        public final long timestamp;
        
        public AccessPattern(String key, long timestamp) {
            this.key = key;
            this.timestamp = timestamp;
        }
    }
    
    private static class AccessStats {
        public long frequency = 0;
        public long lastAccessTime = 0;
        public int cacheLevel = 0;
        public final Queue<Long> recentAccesses = new ConcurrentLinkedQueue<>();
    }
    
    private static class CachePerformance {
        public final double hitRate;
        public final double l1HitRate;
        public final long totalRequests;
        public final double evictionRate;
        
        public CachePerformance(double hitRate, double l1HitRate, long totalRequests, long evictions) {
            this.hitRate = hitRate;
            this.l1HitRate = l1HitRate;
            this.totalRequests = totalRequests;
            this.evictionRate = totalRequests > 0 ? (double) evictions / totalRequests : 0;
        }
    }
    
    public static class CacheStats {
        public final long l1Hits, l2Hits, l3Hits, misses;
        public final int l1Size, l2Size, l3Size;
        public final double hitRate;
        public final long evictions, promotions;
        public final CacheStrategy strategy;
        
        public CacheStats(long l1Hits, long l2Hits, long l3Hits, long misses,
                         int l1Size, int l2Size, int l3Size, double hitRate,
                         long evictions, long promotions, CacheStrategy strategy) {
            this.l1Hits = l1Hits; this.l2Hits = l2Hits; this.l3Hits = l3Hits;
            this.misses = misses; this.l1Size = l1Size; this.l2Size = l2Size;
            this.l3Size = l3Size; this.hitRate = hitRate; this.evictions = evictions;
            this.promotions = promotions; this.strategy = strategy;
        }
        
        @Override
        public String toString() {
            return String.format(
                "CacheStats{hitRate=%.2f%%, L1=%d/%d, L2=%d/%d, L3=%d/%d, " +
                "misses=%d, evictions=%d, promotions=%d, strategy=%s}",
                hitRate * 100, l1Hits, l1Size, l2Hits, l2Size, l3Hits, l3Size,
                misses, evictions, promotions, strategy
            );
        }
    }
    
    @PreDestroy
    public void shutdown() {
        if (optimizerExecutor != null && !optimizerExecutor.isShutdown()) {
            optimizerExecutor.shutdown();
            try {
                if (!optimizerExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    optimizerExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                optimizerExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("智能缓存算法优化器已关闭");
    }
}
