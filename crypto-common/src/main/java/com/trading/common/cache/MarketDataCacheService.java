package com.trading.common.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Duration;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * 市场数据缓存服务
 * 提供业务级别的缓存操作，封装多级缓存管理器
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
public class MarketDataCacheService {
    
    private static final Logger log = LoggerFactory.getLogger(MarketDataCacheService.class);
    
    @Autowired
    private MultiLevelCacheManager cacheManager;
    
    // 缓存键前缀
    private static final String LATEST_PRICE_PREFIX = "latest_price:";
    private static final String KLINE_PREFIX = "kline:";
    private static final String DEPTH_PREFIX = "depth:";
    private static final String TRADE_PREFIX = "trade:";
    private static final String STATS_PREFIX = "stats:";
    
    // 缓存TTL配置
    private static final Duration LATEST_PRICE_TTL = Duration.ofSeconds(30);  // 最新价格30秒
    private static final Duration KLINE_TTL = Duration.ofMinutes(5);  // K线数据5分钟
    private static final Duration DEPTH_TTL = Duration.ofSeconds(10);  // 深度数据10秒
    private static final Duration TRADE_TTL = Duration.ofMinutes(1);  // 交易数据1分钟
    private static final Duration STATS_TTL = Duration.ofMinutes(10);  // 统计数据10分钟
    
    /**
     * 缓存最新价格
     */
    public void cacheLatestPrice(String symbol, BigDecimal price) {
        String key = LATEST_PRICE_PREFIX + symbol;
        cacheManager.put(key, price, LATEST_PRICE_TTL);
        log.debug("缓存最新价格: symbol={}, price={}", symbol, price);
    }
    
    /**
     * 获取最新价格
     */
    public Optional<BigDecimal> getLatestPrice(String symbol) {
        String key = LATEST_PRICE_PREFIX + symbol;
        return cacheManager.get(key, BigDecimal.class);
    }
    
    /**
     * 异步获取最新价格
     */
    public CompletableFuture<Optional<BigDecimal>> getLatestPriceAsync(String symbol) {
        String key = LATEST_PRICE_PREFIX + symbol;
        return cacheManager.getAsync(key, BigDecimal.class);
    }
    
    /**
     * 缓存K线数据
     */
    public void cacheKlineData(String symbol, String interval, Object klineData) {
        String key = KLINE_PREFIX + symbol + ":" + interval;
        cacheManager.put(key, klineData, KLINE_TTL);
        log.debug("缓存K线数据: symbol={}, interval={}", symbol, interval);
    }
    
    /**
     * 获取K线数据
     */
    public <T> Optional<T> getKlineData(String symbol, String interval, Class<T> dataType) {
        String key = KLINE_PREFIX + symbol + ":" + interval;
        return cacheManager.get(key, dataType);
    }

    /**
     * 获取K线数据列表
     */
    @SuppressWarnings("unchecked")
    public <T> Optional<java.util.List<T>> getKlineDataList(String symbol, String interval) {
        String key = KLINE_PREFIX + "list:" + symbol + ":" + interval;
        Optional<java.util.List> result = cacheManager.get(key, java.util.List.class);
        return result.map(list -> (java.util.List<T>) list);
    }

    /**
     * 缓存K线数据列表
     */
    public void cacheKlineDataList(String symbol, String interval, java.util.List<?> klineDataList) {
        String key = KLINE_PREFIX + "list:" + symbol + ":" + interval;
        cacheManager.put(key, klineDataList, KLINE_TTL);
        log.debug("缓存K线数据列表: symbol={}, interval={}, size={}", symbol, interval, klineDataList.size());
    }
    
    /**
     * 缓存深度数据
     */
    public void cacheDepthData(String symbol, int levels, Object depthData) {
        String key = DEPTH_PREFIX + symbol + ":" + levels;
        cacheManager.put(key, depthData, DEPTH_TTL);
        log.debug("缓存深度数据: symbol={}, levels={}", symbol, levels);
    }

    /**
     * 缓存深度数据（重载方法，支持Integer类型）
     */
    public void cacheDepthData(String symbol, Integer levels, Object depthData) {
        String key = DEPTH_PREFIX + symbol + ":" + levels;
        cacheManager.put(key, depthData, DEPTH_TTL);
        log.debug("缓存深度数据: symbol={}, levels={}", symbol, levels);
    }

    /**
     * 获取深度数据
     */
    public <T> Optional<T> getDepthData(String symbol, int levels, Class<T> dataType) {
        String key = DEPTH_PREFIX + symbol + ":" + levels;
        return cacheManager.get(key, dataType);
    }

    /**
     * 获取深度数据（重载方法，支持Integer类型）
     */
    public <T> Optional<T> getDepthData(String symbol, Integer levels, Class<T> dataType) {
        String key = DEPTH_PREFIX + symbol + ":" + levels;
        return cacheManager.get(key, dataType);
    }
    
    /**
     * 缓存交易数据
     */
    public void cacheTradeData(String symbol, Object tradeData) {
        String key = TRADE_PREFIX + symbol;
        cacheManager.put(key, tradeData, TRADE_TTL);
        log.debug("缓存交易数据: symbol={}", symbol);
    }
    
    /**
     * 获取交易数据
     */
    public <T> Optional<T> getTradeData(String symbol, Class<T> dataType) {
        String key = TRADE_PREFIX + symbol;
        return cacheManager.get(key, dataType);
    }
    
    /**
     * 缓存统计数据
     */
    public void cacheStatsData(String symbol, Object statsData) {
        String key = STATS_PREFIX + symbol;
        cacheManager.put(key, statsData, STATS_TTL);
        log.debug("缓存统计数据: symbol={}", symbol);
    }
    
    /**
     * 获取统计数据
     */
    public <T> Optional<T> getStatsData(String symbol, Class<T> dataType) {
        String key = STATS_PREFIX + symbol;
        return cacheManager.get(key, dataType);
    }
    
    /**
     * 删除指定交易对的所有缓存
     */
    public void evictSymbolCache(String symbol) {
        cacheManager.evict(LATEST_PRICE_PREFIX + symbol);
        // 这里可以扩展删除更多相关缓存
        log.info("删除交易对缓存: symbol={}", symbol);
    }

    /**
     * 清除指定交易对的所有缓存
     */
    public void clearSymbolCache(String symbol) {
        // 清除最新价格缓存
        cacheManager.evict(LATEST_PRICE_PREFIX + symbol);

        // 清除K线数据缓存（所有时间间隔）
        String[] intervals = {"1m", "5m", "15m", "1h", "4h", "1d"};
        for (String interval : intervals) {
            cacheManager.evict(KLINE_PREFIX + symbol + ":" + interval);
        }

        // 清除深度数据缓存（所有档位）
        Integer[] levels = {5, 10, 20};
        for (Integer level : levels) {
            cacheManager.evict(DEPTH_PREFIX + symbol + ":" + level);
        }

        // 清除交易数据缓存
        cacheManager.evict(TRADE_PREFIX + symbol);

        // 清除统计数据缓存
        cacheManager.evict(STATS_PREFIX + symbol);

        log.info("清除交易对所有缓存: symbol={}", symbol);
    }
    
    /**
     * 预热缓存
     * 预加载热门交易对的数据
     */
    public CompletableFuture<Void> warmUpCache(String... symbols) {
        String[] keys = new String[symbols.length];
        for (int i = 0; i < symbols.length; i++) {
            keys[i] = LATEST_PRICE_PREFIX + symbols[i];
        }
        
        log.info("开始预热缓存: symbols={}", (Object) symbols);
        return cacheManager.warmUp(keys);
    }
    
    /**
     * 获取缓存统计信息
     */
    public Object getCacheStats() {
        return cacheManager.getStats();
    }
    
    /**
     * 清空所有缓存
     */
    public void clearAllCache() {
        cacheManager.clear();
        log.info("已清空所有缓存");
    }
}
