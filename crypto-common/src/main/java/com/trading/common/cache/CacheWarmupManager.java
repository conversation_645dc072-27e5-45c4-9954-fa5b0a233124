package com.trading.common.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.Set;
import java.util.HashSet;
import java.util.concurrent.ConcurrentHashMap;
import java.time.Instant;
import java.time.Duration;

/**
 * 智能缓存预热管理器
 * 实现智能缓存预热策略，提升缓存命中率
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class CacheWarmupManager {

    private static final Logger log = LoggerFactory.getLogger(CacheWarmupManager.class);

    private static final Duration DEFAULT_WARMUP_TTL = Duration.ofMinutes(10);

    // 预热配置
    @Value("${app.performance.cache.warmup.enabled:true}")
    private boolean warmupEnabled;
    
    @Value("${app.performance.cache.warmup.batch-size:100}")
    private int warmupBatchSize;
    
    @Value("${app.performance.cache.warmup.thread-count:4}")
    private int warmupThreadCount;
    
    @Value("${app.performance.cache.warmup.timeout-seconds:300}")
    private int warmupTimeoutSeconds;

    @Autowired
    private MultiLevelCacheManager shardedCacheManager;
    
    @Autowired
    private CacheKeyOptimizer cacheKeyOptimizer;

    // 预热执行器
    private ExecutorService warmupExecutor;
    private ScheduledExecutorService scheduledExecutor;
    
    // 预热策略
    private final Map<String, WarmupStrategy> warmupStrategies = new ConcurrentHashMap<>();
    
    // 性能统计
    private final LongAdder totalWarmupTasks = new LongAdder();
    private final LongAdder successfulWarmups = new LongAdder();
    private final LongAdder failedWarmups = new LongAdder();
    private final AtomicLong totalWarmupTime = new AtomicLong(0);
    private final AtomicLong totalItemsWarmedUp = new AtomicLong(0);

    @PostConstruct
    public void initialize() {
        if (!warmupEnabled) {
            log.info("缓存预热已禁用");
            return;
        }
        
        log.info("初始化智能缓存预热管理器...");
        
        // 创建预热执行器
        warmupExecutor = Executors.newFixedThreadPool(warmupThreadCount, r -> {
            Thread thread = new Thread(r, "cache-warmup");
            thread.setDaemon(true);
            return thread;
        });
        
        // 创建调度执行器
        scheduledExecutor = Executors.newScheduledThreadPool(2, r -> {
            Thread thread = new Thread(r, "cache-warmup-scheduler");
            thread.setDaemon(true);
            return thread;
        });
        
        // 初始化预热策略
        initializeWarmupStrategies();
        
        log.info("智能缓存预热管理器初始化完成 - 线程数: {}, 批处理大小: {}", 
                warmupThreadCount, warmupBatchSize);
    }

    /**
     * 初始化预热策略
     */
    private void initializeWarmupStrategies() {
        // 市场数据预热策略
        warmupStrategies.put("market-data", new MarketDataWarmupStrategy());
        
        // K线数据预热策略
        warmupStrategies.put("kline-data", new KlineDataWarmupStrategy());
        
        // 深度数据预热策略
        warmupStrategies.put("depth-data", new DepthDataWarmupStrategy());
        
        // 价格数据预热策略
        warmupStrategies.put("price-data", new PriceDataWarmupStrategy());
        
        log.info("预热策略初始化完成: {}", warmupStrategies.keySet());
    }

    /**
     * 执行智能预热
     */
    public CompletableFuture<WarmupResult> executeSmartWarmup(String[] symbols) {
        if (!warmupEnabled) {
            return CompletableFuture.completedFuture(
                    new WarmupResult(false, "缓存预热已禁用", 0, 0));
        }
        
        log.info("开始智能缓存预热: symbols={}", java.util.Arrays.toString(symbols));
        
        long startTime = System.currentTimeMillis();
        totalWarmupTasks.increment();
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return performWarmup(symbols, startTime);
            } catch (Exception e) {
                failedWarmups.increment();
                log.error("智能缓存预热失败", e);
                return new WarmupResult(false, "预热失败: " + e.getMessage(), 0, 
                        System.currentTimeMillis() - startTime);
            }
        }, warmupExecutor);
    }

    /**
     * 执行预热操作
     */
    private WarmupResult performWarmup(String[] symbols, long startTime) {
        List<CompletableFuture<Integer>> warmupFutures = new ArrayList<>();
        
        // 为每种数据类型执行预热
        for (Map.Entry<String, WarmupStrategy> entry : warmupStrategies.entrySet()) {
            String dataType = entry.getKey();
            WarmupStrategy strategy = entry.getValue();
            
            CompletableFuture<Integer> future = CompletableFuture.supplyAsync(() -> {
                try {
                    return strategy.warmup(symbols);
                } catch (Exception e) {
                    log.error("预热策略执行失败: dataType={}", dataType, e);
                    return 0;
                }
            }, warmupExecutor);
            
            warmupFutures.add(future);
        }
        
        // 等待所有预热任务完成
        try {
            CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                    warmupFutures.toArray(new CompletableFuture[0]));
            
            allFutures.get(warmupTimeoutSeconds, TimeUnit.SECONDS);
            
            // 统计预热结果
            int totalWarmedItems = warmupFutures.stream()
                    .mapToInt(future -> {
                        try {
                            return future.get();
                        } catch (Exception e) {
                            return 0;
                        }
                    })
                    .sum();
            
            long duration = System.currentTimeMillis() - startTime;
            totalWarmupTime.addAndGet(duration);
            totalItemsWarmedUp.addAndGet(totalWarmedItems);
            successfulWarmups.increment();
            
            log.info("智能缓存预热完成: 预热项目数={}, 耗时={}ms", totalWarmedItems, duration);
            
            return new WarmupResult(true, "预热成功", totalWarmedItems, duration);
            
        } catch (TimeoutException e) {
            log.warn("缓存预热超时: timeout={}s", warmupTimeoutSeconds);
            return new WarmupResult(false, "预热超时", 0, System.currentTimeMillis() - startTime);
        } catch (Exception e) {
            log.error("缓存预热异常", e);
            return new WarmupResult(false, "预热异常: " + e.getMessage(), 0, 
                    System.currentTimeMillis() - startTime);
        }
    }

    /**
     * 调度定期预热
     */
    public void schedulePeriodicWarmup(String[] symbols, Duration interval) {
        if (!warmupEnabled) {
            return;
        }
        
        log.info("调度定期缓存预热: interval={}", interval);
        
        scheduledExecutor.scheduleAtFixedRate(() -> {
            try {
                executeSmartWarmup(symbols).get(warmupTimeoutSeconds, TimeUnit.SECONDS);
            } catch (Exception e) {
                log.error("定期缓存预热失败", e);
            }
        }, interval.toSeconds(), interval.toSeconds(), TimeUnit.SECONDS);
    }

    /**
     * 基于访问模式的自适应预热
     */
    public CompletableFuture<WarmupResult> executeAdaptiveWarmup(Map<String, Integer> accessPatterns) {
        if (!warmupEnabled) {
            return CompletableFuture.completedFuture(
                    new WarmupResult(false, "缓存预热已禁用", 0, 0));
        }
        
        log.info("开始自适应缓存预热: patterns={}", accessPatterns.size());
        
        // 根据访问频率排序
        List<Map.Entry<String, Integer>> sortedPatterns = accessPatterns.entrySet()
                .stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .toList();
        
        // 选择高频访问的数据进行预热
        String[] highFrequencySymbols = sortedPatterns.stream()
                .limit(20) // 只预热前20个高频符号
                .map(Map.Entry::getKey)
                .toArray(String[]::new);
        
        return executeSmartWarmup(highFrequencySymbols);
    }

    /**
     * 获取预热统计信息
     */
    public WarmupStatistics getStatistics() {
        long totalTasks = totalWarmupTasks.sum();
        double successRate = totalTasks > 0 ? (double) successfulWarmups.sum() / totalTasks : 0.0;
        double avgWarmupTime = successfulWarmups.sum() > 0 ? 
                (double) totalWarmupTime.get() / successfulWarmups.sum() : 0.0;
        
        return new WarmupStatistics(
                totalTasks,
                successfulWarmups.sum(),
                failedWarmups.sum(),
                successRate,
                avgWarmupTime,
                totalItemsWarmedUp.get(),
                warmupStrategies.size()
        );
    }

    /**
     * 预热策略接口
     */
    private interface WarmupStrategy {
        int warmup(String[] symbols);
    }

    /**
     * 市场数据预热策略
     */
    private class MarketDataWarmupStrategy implements WarmupStrategy {
        @Override
        public int warmup(String[] symbols) {
            int warmedCount = 0;
            
            for (String symbol : symbols) {
                try {
                    // 预缓存键，避免首次访问延迟
                    String priceKey = cacheKeyOptimizer.createPriceKey(symbol);
                    String statsKey = cacheKeyOptimizer.createStatsKey(symbol);
                    
                    // 预创建缓存键但不加载实际数据，等待首次同步
                    if (!shardedCacheManager.exists(priceKey)) {
                        shardedCacheManager.put(priceKey, new CachePlaceholder("price"), DEFAULT_WARMUP_TTL);
                    }
                    
                    if (!shardedCacheManager.exists(statsKey)) {
                        shardedCacheManager.put(statsKey, new CachePlaceholder("stats"), DEFAULT_WARMUP_TTL);
                    }
                    
                    warmedCount += 2;
                    
                } catch (Exception e) {
                    log.debug("市场数据预热失败: symbol={}", symbol, e);
                }
            }
            
            return warmedCount;
        }
    }

    /**
     * K线数据预热策略
     */
    private class KlineDataWarmupStrategy implements WarmupStrategy {
        @Override
        public int warmup(String[] symbols) {
            int warmedCount = 0;
            String[] intervals = {"1m", "5m", "15m", "1h"};
            
            for (String symbol : symbols) {
                for (String interval : intervals) {
                    try {
                        String klineKey = cacheKeyOptimizer.createKlineKey(symbol, interval);
                        // 预留K线数据缓存空间，等待真实数据填充
                        shardedCacheManager.put(klineKey, new CachePlaceholder("kline"), DEFAULT_WARMUP_TTL);
                        warmedCount++;
                    } catch (Exception e) {
                        log.debug("K线数据预热失败: symbol={}, interval={}", symbol, interval, e);
                    }
                }
            }
            
            return warmedCount;
        }
    }

    /**
     * 深度数据预热策略
     */
    private class DepthDataWarmupStrategy implements WarmupStrategy {
        @Override
        public int warmup(String[] symbols) {
            int warmedCount = 0;
            int[] levels = {5, 10, 20};
            
            for (String symbol : symbols) {
                for (int level : levels) {
                    try {
                        String depthKey = cacheKeyOptimizer.createDepthKey(symbol, level);
                        // 预留深度数据缓存空间，等待真实数据填充
                        shardedCacheManager.put(depthKey, new CachePlaceholder("depth"), DEFAULT_WARMUP_TTL);
                        warmedCount++;
                    } catch (Exception e) {
                        log.debug("深度数据预热失败: symbol={}, levels={}", symbol, level, e);
                    }
                }
            }
            
            return warmedCount;
        }
    }

    /**
     * 价格数据预热策略
     */
    /**
     * 缓存占位符类 - 简化预热逻辑
     */
    private static class CachePlaceholder {
        private final String type;
        private final Instant createTime;
        
        public CachePlaceholder(String type) {
            this.type = type;
            this.createTime = Instant.now();
        }
        
        public String getType() {
            return type;
        }
        
        public Instant getCreateTime() {
            return createTime;
        }
    }

    private class PriceDataWarmupStrategy implements WarmupStrategy {
        @Override
        public int warmup(String[] symbols) {
            int warmedCount = 0;
            
            for (String symbol : symbols) {
                try {
                    String priceKey = cacheKeyOptimizer.createPriceKey(symbol);
                    // 预留价格数据缓存空间，等待真实数据填充
                    shardedCacheManager.put(priceKey, new CachePlaceholder("price"), DEFAULT_WARMUP_TTL);
                    warmedCount++;
                } catch (Exception e) {
                    log.debug("价格数据预热失败: symbol={}", symbol, e);
                }
            }
            
            return warmedCount;
        }
    }

    /**
     * 预热结果
     */
    public static class WarmupResult {
        private final boolean success;
        private final String message;
        private final int itemsWarmedUp;
        private final long duration;

        public WarmupResult(boolean success, String message, int itemsWarmedUp, long duration) {
            this.success = success;
            this.message = message;
            this.itemsWarmedUp = itemsWarmedUp;
            this.duration = duration;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public int getItemsWarmedUp() { return itemsWarmedUp; }
        public long getDuration() { return duration; }

        @Override
        public String toString() {
            return String.format("WarmupResult: success=%s, items=%d, duration=%dms, message=%s",
                    success, itemsWarmedUp, duration, message);
        }
    }

    /**
     * 预热统计信息
     */
    public static class WarmupStatistics {
        private final long totalTasks;
        private final long successfulTasks;
        private final long failedTasks;
        private final double successRate;
        private final double averageWarmupTime;
        private final long totalItemsWarmedUp;
        private final int strategiesCount;

        public WarmupStatistics(long totalTasks, long successfulTasks, long failedTasks,
                              double successRate, double averageWarmupTime, long totalItemsWarmedUp,
                              int strategiesCount) {
            this.totalTasks = totalTasks;
            this.successfulTasks = successfulTasks;
            this.failedTasks = failedTasks;
            this.successRate = successRate;
            this.averageWarmupTime = averageWarmupTime;
            this.totalItemsWarmedUp = totalItemsWarmedUp;
            this.strategiesCount = strategiesCount;
        }

        // Getters
        public long getTotalTasks() { return totalTasks; }
        public long getSuccessfulTasks() { return successfulTasks; }
        public long getFailedTasks() { return failedTasks; }
        public double getSuccessRate() { return successRate; }
        public double getAverageWarmupTime() { return averageWarmupTime; }
        public long getTotalItemsWarmedUp() { return totalItemsWarmedUp; }
        public int getStrategiesCount() { return strategiesCount; }

        @Override
        public String toString() {
            return String.format("WarmupStats: tasks=%d, success=%.1f%%, avgTime=%.2fms, " +
                            "items=%d, strategies=%d",
                    totalTasks, successRate * 100, averageWarmupTime,
                    totalItemsWarmedUp, strategiesCount);
        }
    }
}
