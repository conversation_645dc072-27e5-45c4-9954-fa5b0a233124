package com.trading.common.cache;

import java.time.Duration;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;

/**
 * 多级缓存管理器接口
 * 实现本地缓存(L1) + Redis缓存(L2)的多级缓存架构
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public interface MultiLevelCacheManager {
    
    /**
     * 获取缓存值
     * 优先从L1本地缓存获取，如果不存在则从L2 Redis获取
     * 
     * @param key 缓存键
     * @param valueType 值类型
     * @return 缓存值，如果不存在返回空
     */
    <T> Optional<T> get(String key, Class<T> valueType);
    
    /**
     * 异步获取缓存值
     * 
     * @param key 缓存键
     * @param valueType 值类型
     * @return 异步缓存值
     */
    <T> CompletableFuture<Optional<T>> getAsync(String key, Class<T> valueType);
    
    /**
     * 设置缓存值
     * 同时写入L1本地缓存和L2 Redis缓存
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param ttl 过期时间
     */
    void put(String key, Object value, Duration ttl);
    
    /**
     * 异步设置缓存值
     * 
     * @param key 缓存键
     * @param value 缓存值
     * @param ttl 过期时间
     * @return 异步操作结果
     */
    CompletableFuture<Void> putAsync(String key, Object value, Duration ttl);
    
    /**
     * 删除缓存
     * 同时从L1和L2删除
     * 
     * @param key 缓存键
     */
    void evict(String key);
    
    /**
     * 异步删除缓存
     * 
     * @param key 缓存键
     * @return 异步操作结果
     */
    CompletableFuture<Void> evictAsync(String key);
    
    /**
     * 清空所有缓存
     */
    void clear();
    
    /**
     * 检查缓存是否存在
     * 
     * @param key 缓存键
     * @return 是否存在
     */
    boolean exists(String key);
    
    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计
     */
    CacheStats getStats();
    
    /**
     * 预热缓存
     * 从数据源加载数据到缓存
     * 
     * @param keys 需要预热的键列表
     * @return 异步预热结果
     */
    CompletableFuture<Void> warmUp(String... keys);
    
    /**
     * 缓存统计信息
     */
    interface CacheStats {
        /**
         * L1缓存命中次数
         */
        long getL1HitCount();
        
        /**
         * L1缓存未命中次数
         */
        long getL1MissCount();
        
        /**
         * L2缓存命中次数
         */
        long getL2HitCount();
        
        /**
         * L2缓存未命中次数
         */
        long getL2MissCount();
        
        /**
         * 总命中率
         */
        double getHitRate();
        
        /**
         * L1命中率
         */
        double getL1HitRate();
        
        /**
         * L2命中率
         */
        double getL2HitRate();
        
        /**
         * 缓存大小
         */
        long getCacheSize();
        
        /**
         * 重置统计
         */
        void reset();
    }
}
