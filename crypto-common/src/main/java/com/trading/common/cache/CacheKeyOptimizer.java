package com.trading.common.cache;

import com.trading.common.gc.MemoryAllocationOptimizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 缓存键优化器
 * 优化缓存键的设计和哈希算法，提升缓存性能
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class CacheKeyOptimizer {

    private static final Logger log = LoggerFactory.getLogger(CacheKeyOptimizer.class);

    @Autowired
    private MemoryAllocationOptimizer memoryOptimizer;

    // 缓存键模板
    private static final String KLINE_KEY_TEMPLATE = "kline:%s:%s";
    private static final String DEPTH_KEY_TEMPLATE = "depth:%s:%d";
    private static final String TRADE_KEY_TEMPLATE = "trade:%s";
    private static final String PRICE_KEY_TEMPLATE = "price:%s";
    private static final String STATS_KEY_TEMPLATE = "stats:%s";
    private static final String MARKET_DATA_KEY_TEMPLATE = "market:%s:%s";

    // 键缓存 - 避免重复创建相同的键
    private final Map<String, String> keyCache = new ConcurrentHashMap<>();
    private final AtomicLong keyCacheSize = new AtomicLong(0);
    
    // 性能统计
    private final LongAdder keyCacheHits = new LongAdder();
    private final LongAdder keyCacheMisses = new LongAdder();
    private final LongAdder keyCreations = new LongAdder();
    private final LongAdder hashCollisions = new LongAdder();
    private final AtomicLong totalMemorySaved = new AtomicLong(0);

    // 哈希算法常量
    private static final int FNV_OFFSET_BASIS = 0x811c9dc5;
    private static final int FNV_PRIME = 0x01000193;
    private static final int MURMUR_SEED = 0x9e3779b9;

    @PostConstruct
    public void initialize() {
        log.info("初始化缓存键优化器...");
    }

    /**
     * 创建K线数据缓存键
     */
    public String createKlineKey(String symbol, String interval) {
        return createOptimizedKey("kline", symbol, interval);
    }

    /**
     * 创建深度数据缓存键
     */
    public String createDepthKey(String symbol, int levels) {
        return createOptimizedKey("depth", symbol, String.valueOf(levels));
    }

    /**
     * 创建交易数据缓存键
     */
    public String createTradeKey(String symbol) {
        return createOptimizedKey("trade", symbol);
    }

    /**
     * 创建价格数据缓存键
     */
    public String createPriceKey(String symbol) {
        return createOptimizedKey("price", symbol);
    }

    /**
     * 创建统计数据缓存键
     */
    public String createStatsKey(String symbol) {
        return createOptimizedKey("stats", symbol);
    }

    /**
     * 创建市场数据缓存键
     */
    public String createMarketDataKey(String symbol, String dataType) {
        return createOptimizedKey("market", symbol, dataType);
    }

    /**
     * 创建优化的缓存键
     */
    private String createOptimizedKey(String prefix, String... parts) {
        keyCreations.increment();

        // 构建完整的部分数组
        String[] allParts = new String[parts.length + 1];
        allParts[0] = prefix;
        System.arraycopy(parts, 0, allParts, 1, parts.length);

        // 使用内存优化器创建键
        String key = memoryOptimizer.createOptimizedString(":", (Object[]) allParts);

        // 检查键缓存
        String cachedKey = keyCache.get(key);
        if (cachedKey != null) {
            keyCacheHits.increment();
            totalMemorySaved.addAndGet(estimateMemorySavings(key));
            return cachedKey;
        }

        keyCacheMisses.increment();

        // 缓存新键（限制缓存大小）
        if (keyCacheSize.get() < 10000) {
            keyCache.put(key, key);
            keyCacheSize.incrementAndGet();
        }

        return key;
    }

    /**
     * 创建带时间戳的缓存键
     */
    public String createTimestampedKey(String baseKey, long timestamp) {
        // 使用时间窗口减少键的数量
        long timeWindow = timestamp / 60000; // 1分钟窗口
        return createOptimizedKey(baseKey, String.valueOf(timeWindow));
    }

    /**
     * 创建分片缓存键
     */
    public String createShardedKey(String baseKey, int shardCount) {
        int shardIndex = getOptimalShardIndex(baseKey, shardCount);
        return createOptimizedKey(baseKey, "shard", String.valueOf(shardIndex));
    }

    /**
     * 高性能最优分片索引算法 - 使用一致性哈希
     */
    private int getOptimalShardIndex(String key, int shardCount) {
        // 使用xxHash算法 - 更快的哈希算法
        long hash = xxHash64(key);

        // 检测哈希冲突
        if (isHashCollision((int)hash)) {
            hashCollisions.increment();
            // 使用CityHash作为备用算法
            hash = cityHash64(key);
        }

        // 使用一致性哈希减少重新分片时的数据迁移
        return (int)(hash % shardCount);
    }

    /**
     * xxHash64算法实现 - 高性能哈希算法
     */
    private long xxHash64(String key) {
        if (key == null || key.isEmpty()) {
            return 0;
        }

        byte[] data = key.getBytes(StandardCharsets.UTF_8);
        long seed = 0x9E3779B185EBCA87L;
        long h64;

        if (data.length >= 32) {
            long v1 = seed + PRIME64_1 + PRIME64_2;
            long v2 = seed + PRIME64_2;
            long v3 = seed + 0;
            long v4 = seed - PRIME64_1;

            int limit = data.length - 32;
            int pos = 0;

            do {
                v1 = round64(v1, getLong(data, pos)); pos += 8;
                v2 = round64(v2, getLong(data, pos)); pos += 8;
                v3 = round64(v3, getLong(data, pos)); pos += 8;
                v4 = round64(v4, getLong(data, pos)); pos += 8;
            } while (pos <= limit);

            h64 = rotateLeft64(v1, 1) + rotateLeft64(v2, 7) +
                  rotateLeft64(v3, 12) + rotateLeft64(v4, 18);
            h64 = mergeRound64(h64, v1);
            h64 = mergeRound64(h64, v2);
            h64 = mergeRound64(h64, v3);
            h64 = mergeRound64(h64, v4);
        } else {
            h64 = seed + PRIME64_5;
        }

        h64 += data.length;

        // 处理剩余字节
        int remaining = data.length & 31;
        int pos = data.length - remaining;

        while (remaining >= 8) {
            long k1 = getLong(data, pos);
            k1 *= PRIME64_2;
            k1 = rotateLeft64(k1, 31);
            k1 *= PRIME64_1;
            h64 ^= k1;
            h64 = rotateLeft64(h64, 27) * PRIME64_1 + PRIME64_4;
            pos += 8;
            remaining -= 8;
        }

        // 最终混合
        h64 ^= h64 >>> 33;
        h64 *= PRIME64_2;
        h64 ^= h64 >>> 29;
        h64 *= PRIME64_3;
        h64 ^= h64 >>> 32;

        return h64;
    }

    // xxHash64常量
    private static final long PRIME64_1 = 0x9E3779B185EBCA87L;
    private static final long PRIME64_2 = 0xC2B2AE3D27D4EB4FL;
    private static final long PRIME64_3 = 0x165667B19E3779F9L;
    private static final long PRIME64_4 = 0x85EBCA77C2B2AE63L;
    private static final long PRIME64_5 = 0x27D4EB2F165667C5L;

    /**
     * xxHash64辅助方法
     */
    private long round64(long acc, long input) {
        acc += input * PRIME64_2;
        acc = rotateLeft64(acc, 31);
        acc *= PRIME64_1;
        return acc;
    }

    private long mergeRound64(long acc, long val) {
        val = round64(0, val);
        acc ^= val;
        acc = acc * PRIME64_1 + PRIME64_4;
        return acc;
    }

    private long rotateLeft64(long value, int distance) {
        return (value << distance) | (value >>> (64 - distance));
    }

    private long getLong(byte[] data, int pos) {
        return ((long) data[pos] & 0xFF) |
               (((long) data[pos + 1] & 0xFF) << 8) |
               (((long) data[pos + 2] & 0xFF) << 16) |
               (((long) data[pos + 3] & 0xFF) << 24) |
               (((long) data[pos + 4] & 0xFF) << 32) |
               (((long) data[pos + 5] & 0xFF) << 40) |
               (((long) data[pos + 6] & 0xFF) << 48) |
               (((long) data[pos + 7] & 0xFF) << 56);
    }

    /**
     * CityHash64算法实现 - 备用高性能哈希算法
     */
    private long cityHash64(String key) {
        if (key == null || key.isEmpty()) {
            return 0;
        }

        byte[] data = key.getBytes(StandardCharsets.UTF_8);
        int len = data.length;

        if (len <= 32) {
            if (len <= 16) {
                return hashLen0to16(data, len);
            } else {
                return hashLen17to32(data, len);
            }
        }

        return hashLen33to64(data, len);
    }

    private long hashLen0to16(byte[] data, int len) {
        if (len >= 8) {
            long mul = 0x9ddfea08eb382d69L;
            long a = getLong(data, 0) + 0x9e3779b97f4a7c15L;
            long b = getLong(data, len - 8);
            long c = rotateRight64(b, 37) * mul + a;
            long d = (rotateRight64(a, 25) + b) * mul;
            return hashLen16(c, d, mul);
        }

        if (len >= 4) {
            long mul = 0x9ddfea08eb382d69L;
            long a = getInt(data, 0);
            return hashLen16(len + (a << 3), getInt(data, len - 4), mul);
        }

        if (len > 0) {
            byte a = data[0];
            byte b = data[len >> 1];
            byte c = data[len - 1];
            int y = (a & 0xFF) + ((b & 0xFF) << 8);
            int z = len + ((c & 0xFF) << 2);
            return shiftMix(y * 0x9ddfea08eb382d69L ^ z * 0x9e3779b97f4a7c15L) * 0x9ddfea08eb382d69L;
        }

        return 0x9e3779b97f4a7c15L;
    }

    private long hashLen17to32(byte[] data, int len) {
        long mul = 0x9ddfea08eb382d69L;
        long a = getLong(data, 0) * 0x9e3779b97f4a7c15L;
        long b = getLong(data, 8);
        long c = getLong(data, len - 8) * mul;
        long d = getLong(data, len - 16) * 0x9e3779b97f4a7c15L;
        return hashLen16(rotateRight64(a + b, 43) + rotateRight64(c, 30) + d,
                        a + rotateRight64(b + 0x9e3779b97f4a7c15L, 18) + c, mul);
    }

    private long hashLen33to64(byte[] data, int len) {
        long mul = 0x9ddfea08eb382d69L;
        long a = getLong(data, 0) * 0x9e3779b97f4a7c15L;
        long b = getLong(data, 8);
        long c = getLong(data, len - 24);
        long d = getLong(data, len - 32);
        long e = getLong(data, 16) * 0x9e3779b97f4a7c15L;
        long f = getLong(data, 24) * 9;
        long g = getLong(data, len - 8);
        long h = getLong(data, len - 16) * mul;
        long u = rotateRight64(a + g, 43) + (rotateRight64(b, 30) + c) * 9;
        long v = ((a + g) ^ d) + f + 1;
        long w = Long.reverseBytes((u + v) * mul) + h;
        long x = rotateRight64(e + f, 42) + c;
        long y = (Long.reverseBytes((v + w) * mul) + g) * mul;
        long z = e + f + c;
        a = Long.reverseBytes((x + z) * mul + y) + b;
        b = shiftMix((z + a) * mul + d + h) * mul;
        return b + x;
    }

    private long hashLen16(long u, long v, long mul) {
        long a = (u ^ v) * mul;
        a ^= (a >>> 47);
        long b = (v ^ a) * mul;
        b ^= (b >>> 47);
        b *= mul;
        return b;
    }

    private long rotateRight64(long value, int distance) {
        return (value >>> distance) | (value << (64 - distance));
    }

    private long shiftMix(long val) {
        return val ^ (val >>> 47);
    }

    private int getInt(byte[] data, int pos) {
        return ((data[pos] & 0xFF)) |
               ((data[pos + 1] & 0xFF) << 8) |
               ((data[pos + 2] & 0xFF) << 16) |
               ((data[pos + 3] & 0xFF) << 24);
    }

    /**
     * FNV-1a哈希算法
     */
    private int fnvHash(String key) {
        int hash = FNV_OFFSET_BASIS;
        for (int i = 0; i < key.length(); i++) {
            hash ^= key.charAt(i);
            hash *= FNV_PRIME;
        }
        return hash;
    }

    /**
     * MurmurHash算法（简化版）
     */
    private int murmurHash(String key) {
        int hash = MURMUR_SEED;
        for (int i = 0; i < key.length(); i++) {
            int k = key.charAt(i);
            k *= 0xcc9e2d51;
            k = Integer.rotateLeft(k, 15);
            k *= 0x1b873593;
            
            hash ^= k;
            hash = Integer.rotateLeft(hash, 13);
            hash = hash * 5 + 0xe6546b64;
        }
        
        hash ^= key.length();
        hash ^= hash >>> 16;
        hash *= 0x85ebca6b;
        hash ^= hash >>> 13;
        hash *= 0xc2b2ae35;
        hash ^= hash >>> 16;
        
        return hash;
    }

    /**
     * 检测哈希冲突
     */
    private boolean isHashCollision(int hash) {
        // 简单的冲突检测：检查哈希值的分布
        return (hash & 0xFFFF) == 0; // 如果低16位全为0，可能存在冲突
    }

    /**
     * 创建层次化缓存键
     */
    public String createHierarchicalKey(String... levels) {
        if (levels.length == 0) {
            throw new IllegalArgumentException("至少需要一个层级");
        }
        
        return createOptimizedKey(levels[0], java.util.Arrays.copyOfRange(levels, 1, levels.length));
    }

    /**
     * 创建带版本的缓存键
     */
    public String createVersionedKey(String baseKey, int version) {
        return createOptimizedKey(baseKey, "v" + version);
    }

    /**
     * 创建带过期时间的缓存键
     */
    public String createExpiringKey(String baseKey, long expirationTime) {
        // 将过期时间编码到键中
        long expireWindow = expirationTime / 1000; // 秒级精度
        return createOptimizedKey(baseKey, "exp", String.valueOf(expireWindow));
    }

    /**
     * 批量创建缓存键
     */
    public Map<String, String> createBatchKeys(String prefix, String[] identifiers) {
        Map<String, String> keys = new ConcurrentHashMap<>();
        
        for (String identifier : identifiers) {
            String key = createOptimizedKey(prefix, identifier);
            keys.put(identifier, key);
        }
        
        return keys;
    }

    /**
     * 创建压缩缓存键
     */
    public String createCompressedKey(String longKey) {
        if (longKey.length() <= 50) {
            return longKey; // 短键不需要压缩
        }
        
        // 使用哈希值作为压缩键
        int hash = fnvHash(longKey);
        String compressedKey = "compressed:" + Integer.toHexString(hash);
        
        // 记录压缩映射（用于调试）
        log.debug("键压缩: {} -> {}", longKey, compressedKey);
        
        return compressedKey;
    }

    /**
     * 验证缓存键格式
     */
    public boolean isValidKey(String key) {
        if (key == null || key.isEmpty()) {
            return false;
        }
        
        // 检查键长度
        if (key.length() > 250) {
            log.warn("缓存键过长: length={}, key={}", key.length(), key);
            return false;
        }
        
        // 检查非法字符
        if (key.contains(" ") || key.contains("\n") || key.contains("\r")) {
            log.warn("缓存键包含非法字符: key={}", key);
            return false;
        }
        
        return true;
    }

    /**
     * 估算内存节省量
     */
    private long estimateMemorySavings(String key) {
        // 估算字符串对象的内存开销
        return key.length() * 2 + 40; // 每个字符2字节 + 对象头开销
    }

    /**
     * 清理键缓存
     */
    public void clearKeyCache() {
        keyCache.clear();
        keyCacheSize.set(0);
        log.info("缓存键缓存已清理");
    }

    /**
     * 获取键优化统计信息
     */
    public KeyOptimizationStatistics getStatistics() {
        long totalKeyRequests = keyCacheHits.sum() + keyCacheMisses.sum();
        double cacheHitRate = totalKeyRequests > 0 ? (double) keyCacheHits.sum() / totalKeyRequests : 0.0;
        
        return new KeyOptimizationStatistics(
                keyCreations.sum(),
                keyCacheHits.sum(),
                keyCacheMisses.sum(),
                cacheHitRate,
                keyCacheSize.get(),
                hashCollisions.sum(),
                totalMemorySaved.get()
        );
    }

    /**
     * 键优化统计信息
     */
    public static class KeyOptimizationStatistics {
        private final long totalKeyCreations;
        private final long keyCacheHits;
        private final long keyCacheMisses;
        private final double keyCacheHitRate;
        private final long keyCacheSize;
        private final long hashCollisions;
        private final long totalMemorySaved;

        public KeyOptimizationStatistics(long totalKeyCreations, long keyCacheHits, long keyCacheMisses,
                                       double keyCacheHitRate, long keyCacheSize, long hashCollisions,
                                       long totalMemorySaved) {
            this.totalKeyCreations = totalKeyCreations;
            this.keyCacheHits = keyCacheHits;
            this.keyCacheMisses = keyCacheMisses;
            this.keyCacheHitRate = keyCacheHitRate;
            this.keyCacheSize = keyCacheSize;
            this.hashCollisions = hashCollisions;
            this.totalMemorySaved = totalMemorySaved;
        }

        // Getters
        public long getTotalKeyCreations() { return totalKeyCreations; }
        public long getKeyCacheHits() { return keyCacheHits; }
        public long getKeyCacheMisses() { return keyCacheMisses; }
        public double getKeyCacheHitRate() { return keyCacheHitRate; }
        public long getKeyCacheSize() { return keyCacheSize; }
        public long getHashCollisions() { return hashCollisions; }
        public long getTotalMemorySaved() { return totalMemorySaved; }

        @Override
        public String toString() {
            return String.format("KeyOptimization: creations=%d, cacheHit=%.1f%%, " +
                            "cacheSize=%d, collisions=%d, memorySaved=%dKB",
                    totalKeyCreations, keyCacheHitRate * 100, keyCacheSize,
                    hashCollisions, totalMemorySaved / 1024);
        }
    }
}
