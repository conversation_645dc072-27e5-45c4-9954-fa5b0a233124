package com.trading.common.cache;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.concurrent.atomic.AtomicLong;
import java.util.Map;
import java.util.HashMap;

/**
 * 缓存统计信息实现类
 * 支持JSON序列化和详细统计信息展示
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class CacheStatsImpl implements MultiLevelCacheManager.CacheStats {
    
    private final AtomicLong l1HitCount = new AtomicLong(0);
    private final AtomicLong l1MissCount = new AtomicLong(0);
    private final AtomicLong l2HitCount = new AtomicLong(0);
    private final AtomicLong l2MissCount = new AtomicLong(0);
    private final AtomicLong cacheSize = new AtomicLong(0);
    
    @Override
    @JsonProperty("l1HitCount")
    public long getL1HitCount() {
        return l1HitCount.get();
    }

    @Override
    @JsonProperty("l1MissCount")
    public long getL1MissCount() {
        return l1MissCount.get();
    }

    @Override
    @JsonProperty("l2HitCount")
    public long getL2HitCount() {
        return l2HitCount.get();
    }

    @Override
    @JsonProperty("l2MissCount")
    public long getL2MissCount() {
        return l2MissCount.get();
    }
    
    @Override
    @JsonProperty("hitRate")
    public double getHitRate() {
        long totalHits = l1HitCount.get() + l2HitCount.get();
        long totalRequests = totalHits + l1MissCount.get() + l2MissCount.get();
        return totalRequests == 0 ? 0.0 : (double) totalHits / totalRequests;
    }

    @Override
    @JsonProperty("l1HitRate")
    public double getL1HitRate() {
        long totalL1Requests = l1HitCount.get() + l1MissCount.get();
        return totalL1Requests == 0 ? 0.0 : (double) l1HitCount.get() / totalL1Requests;
    }

    @Override
    @JsonProperty("l2HitRate")
    public double getL2HitRate() {
        long totalL2Requests = l2HitCount.get() + l2MissCount.get();
        return totalL2Requests == 0 ? 0.0 : (double) l2HitCount.get() / totalL2Requests;
    }

    @Override
    @JsonProperty("cacheSize")
    public long getCacheSize() {
        return cacheSize.get();
    }
    
    @Override
    public void reset() {
        l1HitCount.set(0);
        l1MissCount.set(0);
        l2HitCount.set(0);
        l2MissCount.set(0);
        cacheSize.set(0);
    }
    
    /**
     * 记录L1缓存命中
     */
    public void recordL1Hit() {
        l1HitCount.incrementAndGet();
    }
    
    /**
     * 记录L1缓存未命中
     */
    public void recordL1Miss() {
        l1MissCount.incrementAndGet();
    }
    
    /**
     * 记录L2缓存命中
     */
    public void recordL2Hit() {
        l2HitCount.incrementAndGet();
    }
    
    /**
     * 记录L2缓存未命中
     */
    public void recordL2Miss() {
        l2MissCount.incrementAndGet();
    }
    
    /**
     * 更新缓存大小
     */
    public void updateCacheSize(long size) {
        cacheSize.set(size);
    }
    
    /**
     * 获取统计摘要
     */
    public String getSummary() {
        return String.format(
            "CacheStats{L1Hit=%d, L1Miss=%d, L2Hit=%d, L2Miss=%d, HitRate=%.2f%%, L1HitRate=%.2f%%, L2HitRate=%.2f%%, Size=%d}",
            getL1HitCount(), getL1MissCount(), getL2HitCount(), getL2MissCount(),
            getHitRate() * 100, getL1HitRate() * 100, getL2HitRate() * 100, getCacheSize()
        );
    }

    /**
     * 转换为Map格式，便于JSON序列化
     */
    @JsonProperty("statsMap")
    public Map<String, Object> toMap() {
        Map<String, Object> map = new HashMap<>();
        map.put("l1HitCount", getL1HitCount());
        map.put("l1MissCount", getL1MissCount());
        map.put("l2HitCount", getL2HitCount());
        map.put("l2MissCount", getL2MissCount());
        map.put("hitRate", getHitRate());
        map.put("l1HitRate", getL1HitRate());
        map.put("l2HitRate", getL2HitRate());
        map.put("cacheSize", getCacheSize());
        map.put("totalRequests", getL1HitCount() + getL1MissCount() + getL2HitCount() + getL2MissCount());
        map.put("totalHits", getL1HitCount() + getL2HitCount());
        map.put("totalMisses", getL1MissCount() + getL2MissCount());
        return map;
    }

    /**
     * 重写toString方法，提供可读的统计信息
     */
    @Override
    public String toString() {
        return getSummary();
    }
}
