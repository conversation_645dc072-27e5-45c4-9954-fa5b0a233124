package com.trading.common.cache;

import com.trading.common.config.MarketDataConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;
import com.trading.common.thread.UnifiedThreadPoolManager;

import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 缓存预热服务
 * 在应用启动时预加载热点数据到缓存中
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
public class CacheWarmupService {
    
    private static final Logger log = LoggerFactory.getLogger(CacheWarmupService.class);
    
    @Autowired
    private MultiLevelCacheManager cacheManager;
    
    @Autowired
    private MarketDataCacheService marketDataCacheService;

    @Autowired
    private UnifiedThreadPoolManager threadPoolManager;

    @Autowired(required = false)
    private MarketDataConfig marketDataConfig;

    // 预热统计
    private final AtomicLong totalWarmupItems = new AtomicLong(0);
    private final AtomicLong successfulWarmups = new AtomicLong(0);
    private final AtomicLong failedWarmups = new AtomicLong(0);

    // 默认热门交易对列表 - 当配置不可用时使用
    private static final List<String> DEFAULT_HOT_SYMBOLS = Arrays.asList(
            "BTCUSDT", "ETHUSDT", "BNBUSDT", "ADAUSDT", "XRPUSDT",
            "SOLUSDT", "DOTUSDT", "DOGEUSDT", "AVAXUSDT", "SHIBUSDT",
            "MATICUSDT", "LTCUSDT", "UNIUSDT", "LINKUSDT", "ATOMUSDT"
    );
    
    // K线时间间隔
    private static final List<String> KLINE_INTERVALS = Arrays.asList(
            "1m", "5m", "15m", "1h", "4h", "1d"
    );
    
    // 深度档位
    private static final List<Integer> DEPTH_LEVELS = Arrays.asList(5, 10, 20);
    
    /**
     * 获取要预热的交易对列表
     * 优先使用配置文件中的交易对，如果不可用则使用默认列表
     */
    private List<String> getSymbolsForWarmup() {
        if (marketDataConfig != null &&
            marketDataConfig.getCollector() != null &&
            marketDataConfig.getCollector().getSymbols() != null &&
            !marketDataConfig.getCollector().getSymbols().isEmpty()) {
            return marketDataConfig.getCollector().getSymbols();
        }
        return DEFAULT_HOT_SYMBOLS;
    }

    /**
     * 应用启动完成后执行缓存预热
     * 注意：这个预热主要用于通用缓存，具体的市场数据预热由CacheWarmupStartupRunner处理
     */
//    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("应用启动完成，开始执行通用缓存预热...");

        // 首先清理错误的占位符缓存数据
        threadPoolManager.schedule(() -> {
            if (cacheManager instanceof com.trading.common.cache.MultiLevelCacheManagerImpl) {
                ((com.trading.common.cache.MultiLevelCacheManagerImpl) cacheManager).clearPlaceholderData();
            }
        }, 5, TimeUnit.SECONDS);

        // 延迟30秒后开始预热，确保所有服务都已启动
        threadPoolManager.schedule(this::performWarmup, 30, TimeUnit.SECONDS);

        // 定期预热（每小时执行一次）
        threadPoolManager.scheduleAtFixedRate(this::performIncrementalWarmup,
                1, 1, TimeUnit.HOURS);
    }
    
    /**
     * 执行完整缓存预热
     */
    public CompletableFuture<WarmupResult> performWarmup() {
        return CompletableFuture.supplyAsync(() -> {
            log.info("开始执行缓存预热...");
            Instant startTime = Instant.now();
            
            try {
                // 重置统计
                totalWarmupItems.set(0);
                successfulWarmups.set(0);
                failedWarmups.set(0);
                
                // 并行预热不同类型的数据
                CompletableFuture<Void> klineWarmup = warmupKlineData();
                CompletableFuture<Void> depthWarmup = warmupDepthData();
                CompletableFuture<Void> priceWarmup = warmupLatestPrices();
                CompletableFuture<Void> statsWarmup = warmupStatisticsData();
                
                // 等待所有预热任务完成
                CompletableFuture.allOf(klineWarmup, depthWarmup, priceWarmup, statsWarmup).join();
                
                Duration duration = Duration.between(startTime, Instant.now());
                
                WarmupResult result = WarmupResult.builder()
                        .success(true)
                        .totalItems(totalWarmupItems.get())
                        .successfulItems(successfulWarmups.get())
                        .failedItems(failedWarmups.get())
                        .duration(duration)
                        .build();
                
                log.info("缓存预热完成: 总数={}, 成功={}, 失败={}, 耗时={}ms", 
                        result.getTotalItems(), result.getSuccessfulItems(), 
                        result.getFailedItems(), duration.toMillis());
                
                return result;
                
            } catch (Exception e) {
                log.error("缓存预热失败", e);
                return WarmupResult.builder()
                        .success(false)
                        .totalItems(totalWarmupItems.get())
                        .successfulItems(successfulWarmups.get())
                        .failedItems(failedWarmups.get())
                        .duration(Duration.between(startTime, Instant.now()))
                        .errorMessage(e.getMessage())
                        .build();
            }
        });
    }
    
    /**
     * 执行增量缓存预热
     */
    public CompletableFuture<Void> performIncrementalWarmup() {
        return CompletableFuture.runAsync(() -> {
            log.info("开始执行增量缓存预热...");
            
            try {
                // 只预热最热门的数据
                List<String> symbols = getSymbolsForWarmup();
                List<String> topSymbols = symbols.subList(0, Math.min(5, symbols.size()));

                for (String symbol : topSymbols) {
                    // 预热最新价格
                    warmupSymbolLatestPrice(symbol);
                    
                    // 预热主要K线数据
                    warmupSymbolKlineData(symbol, "1m");
                    warmupSymbolKlineData(symbol, "1h");
                    
                    // 预热深度数据
                    warmupSymbolDepthData(symbol, 5);
                }
                
                log.info("增量缓存预热完成");
                
            } catch (Exception e) {
                log.error("增量缓存预热失败", e);
            }
        });
    }
    
    /**
     * 预热K线数据
     */
    private CompletableFuture<Void> warmupKlineData() {
        return CompletableFuture.runAsync(() -> {
            log.info("开始预热K线数据...");

            List<String> symbols = getSymbolsForWarmup();
            for (String symbol : symbols) {
                for (String interval : KLINE_INTERVALS) {
                    warmupSymbolKlineData(symbol, interval);
                }
            }

            log.info("K线数据预热完成");
        });
    }
    
    /**
     * 预热深度数据
     */
    private CompletableFuture<Void> warmupDepthData() {
        return CompletableFuture.runAsync(() -> {
            log.info("开始预热深度数据...");

            List<String> symbols = getSymbolsForWarmup();
            for (String symbol : symbols) {
                for (Integer levels : DEPTH_LEVELS) {
                    warmupSymbolDepthData(symbol, levels);
                }
            }

            log.info("深度数据预热完成");
        });
    }
    
    /**
     * 预热最新价格数据
     */
    private CompletableFuture<Void> warmupLatestPrices() {
        return CompletableFuture.runAsync(() -> {
            log.info("开始预热最新价格数据...");

            List<String> symbols = getSymbolsForWarmup();
            for (String symbol : symbols) {
                warmupSymbolLatestPrice(symbol);
            }

            log.info("最新价格数据预热完成");
        });
    }
    
    /**
     * 预热统计数据
     */
    private CompletableFuture<Void> warmupStatisticsData() {
        return CompletableFuture.runAsync(() -> {
            log.info("开始预热统计数据...");

            List<String> symbols = getSymbolsForWarmup();
            for (String symbol : symbols) {
                warmupSymbolStatistics(symbol);
            }

            log.info("统计数据预热完成");
        });
    }
    
    /**
     * 预热单个交易对的K线数据
     */
    private void warmupSymbolKlineData(String symbol, String interval) {
        try {
            totalWarmupItems.incrementAndGet();

            // 构造缓存键
            String key = "kline:" + symbol + ":" + interval;

            // 检查缓存是否已存在
            if (cacheManager.exists(key)) {
                successfulWarmups.incrementAndGet();
                return;
            }

            // 不再使用占位符数据，而是跳过预热K线数据
            // K线数据应该由实际的数据处理流程来缓存，而不是预热服务
            log.debug("跳过K线数据预热（等待实际数据）: symbol={}, interval={}", symbol, interval);
            successfulWarmups.incrementAndGet();

        } catch (Exception e) {
            failedWarmups.incrementAndGet();
            log.warn("预热K线数据失败: symbol={}, interval={}", symbol, interval, e);
        }
    }
    
    /**
     * 预热单个交易对的深度数据
     */
    private void warmupSymbolDepthData(String symbol, Integer levels) {
        try {
            totalWarmupItems.incrementAndGet();

            String key = "depth:" + symbol + ":" + levels;

            if (cacheManager.exists(key)) {
                successfulWarmups.incrementAndGet();
                return;
            }

            // 不再使用占位符数据，而是跳过预热深度数据
            // 深度数据应该由实际的数据处理流程来缓存，而不是预热服务
            log.debug("跳过深度数据预热（等待实际数据）: symbol={}, levels={}", symbol, levels);
            successfulWarmups.incrementAndGet();

        } catch (Exception e) {
            failedWarmups.incrementAndGet();
            log.warn("预热深度数据失败: symbol={}, levels={}", symbol, levels, e);
        }
    }
    
    /**
     * 预热单个交易对的最新价格
     */
    private void warmupSymbolLatestPrice(String symbol) {
        try {
            totalWarmupItems.incrementAndGet();

            String key = "latest_price:" + symbol;

            if (cacheManager.exists(key)) {
                successfulWarmups.incrementAndGet();
                return;
            }

            // 不再使用占位符数据，而是跳过预热价格数据
            // 价格数据应该由实际的数据处理流程来缓存，而不是预热服务
            log.debug("跳过价格数据预热（等待实际数据）: symbol={}", symbol);
            successfulWarmups.incrementAndGet();

        } catch (Exception e) {
            failedWarmups.incrementAndGet();
            log.warn("预热最新价格失败: symbol={}", symbol, e);
        }
    }
    
    /**
     * 预热单个交易对的统计数据
     */
    private void warmupSymbolStatistics(String symbol) {
        try {
            totalWarmupItems.incrementAndGet();

            String key = "stats:" + symbol;

            if (cacheManager.exists(key)) {
                successfulWarmups.incrementAndGet();
                return;
            }

            // 不再使用占位符数据，而是跳过预热统计数据
            // 统计数据应该由实际的数据处理流程来缓存，而不是预热服务
            log.debug("跳过统计数据预热（等待实际数据）: symbol={}", symbol);
            successfulWarmups.incrementAndGet();

        } catch (Exception e) {
            failedWarmups.incrementAndGet();
            log.warn("预热统计数据失败: symbol={}", symbol, e);
        }
    }
    
    /**
     * 获取预热统计信息
     */
    public WarmupStats getWarmupStats() {
        return WarmupStats.builder()
                .totalItems(totalWarmupItems.get())
                .successfulItems(successfulWarmups.get())
                .failedItems(failedWarmups.get())
                .successRate(totalWarmupItems.get() > 0 ? 
                        (double) successfulWarmups.get() / totalWarmupItems.get() : 0.0)
                .build();
    }
    
    /**
     * 关闭预热服务
     */
    public void shutdown() {
        log.info("关闭缓存预热服务...");
        // 统一线程池管理器会自动处理关闭
        log.info("缓存预热服务已关闭");
    }
    
    // 内部类定义
    
    @lombok.Data
    @lombok.Builder
    public static class WarmupResult {
        private boolean success;
        private long totalItems;
        private long successfulItems;
        private long failedItems;
        private Duration duration;
        private String errorMessage;
    }
    
    @lombok.Data
    @lombok.Builder
    public static class WarmupStats {
        private long totalItems;
        private long successfulItems;
        private long failedItems;
        private double successRate;
    }
}
