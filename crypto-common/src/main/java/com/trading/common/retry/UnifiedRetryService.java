package com.trading.common.retry;

import com.trading.common.utils.AsyncDelayUtils;
import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.ratelimiter.RateLimiter;
import io.github.resilience4j.ratelimiter.RateLimiterConfig;
import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryConfig;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.function.Predicate;
import java.util.function.Supplier;

/**
 * 统一重试服务
 * 基于Resilience4j实现的高性能重试、熔断和限流机制
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
public class UnifiedRetryService {

    private static final Logger log = LoggerFactory.getLogger(UnifiedRetryService.class);
    private final Executor virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();

    // 缓存重试、熔断器和限流器实例
    private final ConcurrentHashMap<String, Retry> retryCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, CircuitBreaker> circuitBreakerCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, RateLimiter> rateLimiterCache = new ConcurrentHashMap<>();

    // 预定义的重试配置 - 使用实例方法而不是静态字段
    private io.github.resilience4j.retry.RetryConfig redisRetryConfig;
    private io.github.resilience4j.retry.RetryConfig apiRetryConfig;
    private io.github.resilience4j.retry.RetryConfig databaseRetryConfig;

    // 预定义的熔断器配置
    private CircuitBreakerConfig redisCircuitBreakerConfig;
    private CircuitBreakerConfig apiCircuitBreakerConfig;

    // 预定义的限流器配置
    private RateLimiterConfig apiRateLimiterConfig;
    private RateLimiterConfig redisRateLimiterConfig;

    /**
     * 构造函数 - 初始化配置
     */
    public UnifiedRetryService() {
        try {
            log.info("开始初始化UnifiedRetryService...");
            initializeConfigs();
            log.info("UnifiedRetryService初始化成功");
        } catch (Exception e) {
            log.error("UnifiedRetryService初始化失败", e);
            throw new RuntimeException("UnifiedRetryService初始化失败", e);
        }
    }

    /**
     * 初始化配置
     */
    private void initializeConfigs() {
        try {
            log.info("开始初始化重试配置...");
            // 初始化重试配置
            redisRetryConfig = io.github.resilience4j.retry.RetryConfig.custom()
                    .maxAttempts(3)
                    .intervalFunction(attempt -> 100L * (1L << (attempt - 1)))
                    .retryOnException(throwable ->
                        throwable instanceof java.net.ConnectException ||
                        throwable instanceof java.net.SocketTimeoutException ||
                        (throwable.getMessage() != null &&
                         (throwable.getMessage().contains("Connection") ||
                          throwable.getMessage().contains("timeout") ||
                          throwable.getMessage().contains("LOADING"))))
                    .build();
            log.info("Redis重试配置初始化完成");

            apiRetryConfig = io.github.resilience4j.retry.RetryConfig.custom()
                    .maxAttempts(3)
                    .intervalFunction(attempt -> 1000L * (1L << (attempt - 1)))
                    .retryOnException(throwable -> {
                        // HTTP 5xx错误和429限流错误可重试
                        if (throwable.getMessage() != null) {
                            String msg = throwable.getMessage().toLowerCase();
                            return msg.contains("5") || msg.contains("429") ||
                                   msg.contains("timeout") || msg.contains("connection");
                        }
                        return throwable instanceof java.net.SocketTimeoutException ||
                               throwable instanceof java.net.ConnectException ||
                               throwable instanceof java.io.IOException;
                    })
                    .build();

            databaseRetryConfig = io.github.resilience4j.retry.RetryConfig.custom()
                    .maxAttempts(2)
                    .intervalFunction(attempt -> 200L * attempt)
                    .retryOnException(throwable ->
                        throwable instanceof java.net.ConnectException ||
                        (throwable.getMessage() != null &&
                         throwable.getMessage().contains("deadlock")))
                    .build();

            // 初始化熔断器配置 - 优化Redis熔断器，降低敏感度
            redisCircuitBreakerConfig = CircuitBreakerConfig.custom()
                    .failureRateThreshold(70) // 提高失败率阈值从50%到70%
                    .waitDurationInOpenState(Duration.ofSeconds(60)) // 增加等待时间从30秒到60秒
                    .slidingWindowSize(20) // 增加滑动窗口大小从10到20
                    .minimumNumberOfCalls(10) // 增加最小调用次数从5到10
                    .slowCallRateThreshold(80) // 设置慢调用率阈值为80%
                    .slowCallDurationThreshold(Duration.ofSeconds(5)) // 设置慢调用时间阈值为5秒
                    .permittedNumberOfCallsInHalfOpenState(5) // 关键修复：限制HALF_OPEN状态下的并发请求数量
                    .build();

            apiCircuitBreakerConfig = CircuitBreakerConfig.custom()
                    .failureRateThreshold(60)
                    .waitDurationInOpenState(Duration.ofSeconds(60))
                    .slidingWindowSize(20)
                    .minimumNumberOfCalls(10)
                    .permittedNumberOfCallsInHalfOpenState(3) // 限制API HALF_OPEN状态下的并发请求数量
                    .build();

            // 初始化限流器配置
            apiRateLimiterConfig = RateLimiterConfig.custom()
                    .limitForPeriod(100)
                    .limitRefreshPeriod(Duration.ofSeconds(1))
                    .timeoutDuration(Duration.ofSeconds(5))
                    .build();

            redisRateLimiterConfig = RateLimiterConfig.custom()
                    .limitForPeriod(1000)
                    .limitRefreshPeriod(Duration.ofSeconds(1))
                    .timeoutDuration(Duration.ofMillis(100))
                    .build();

            log.info("UnifiedRetryService配置初始化完成");
        } catch (Exception e) {
            log.error("UnifiedRetryService配置初始化失败", e);
            // 使用默认配置
            redisRetryConfig = io.github.resilience4j.retry.RetryConfig.ofDefaults();
            apiRetryConfig = io.github.resilience4j.retry.RetryConfig.ofDefaults();
            databaseRetryConfig = io.github.resilience4j.retry.RetryConfig.ofDefaults();
            redisCircuitBreakerConfig = CircuitBreakerConfig.ofDefaults();
            apiCircuitBreakerConfig = CircuitBreakerConfig.ofDefaults();
            apiRateLimiterConfig = RateLimiterConfig.ofDefaults();
            redisRateLimiterConfig = RateLimiterConfig.ofDefaults();
        }
    }
    

    
    /**
     * 执行带重试的操作
     */
    public <T> T executeWithRetry(String operationName, Supplier<T> operation, io.github.resilience4j.retry.RetryConfig config) {
        Retry retry = getOrCreateRetry(operationName, config);
        Supplier<T> decoratedSupplier = Retry.decorateSupplier(retry, operation);
        
        try {
            return decoratedSupplier.get();
        } catch (Exception e) {
            log.error("重试操作最终失败: operationName={}", operationName, e);
            throw e;
        }
    }
    
    /**
     * 异步执行带重试的操作
     */
    public <T> CompletableFuture<T> executeWithRetryAsync(String operationName, Supplier<T> operation, io.github.resilience4j.retry.RetryConfig config) {
        return CompletableFuture.supplyAsync(() -> executeWithRetry(operationName, operation, config), virtualThreadExecutor);
    }
    
    /**
     * 执行带重试和熔断的操作
     */
    public <T> T executeWithRetryAndCircuitBreaker(String operationName, Supplier<T> operation,
                                                   io.github.resilience4j.retry.RetryConfig retryConfig, CircuitBreakerConfig circuitBreakerConfig) {
        Retry retry = getOrCreateRetry(operationName, retryConfig);
        CircuitBreaker circuitBreaker = getOrCreateCircuitBreaker(operationName, circuitBreakerConfig);
        
        Supplier<T> decoratedSupplier = Retry.decorateSupplier(retry, 
                CircuitBreaker.decorateSupplier(circuitBreaker, operation));
        
        try {
            return decoratedSupplier.get();
        } catch (Exception e) {
            log.error("重试和熔断操作最终失败: operationName={}, circuitBreakerState={}", 
                    operationName, circuitBreaker.getState(), e);
            throw e;
        }
    }
    
    /**
     * 执行带重试、熔断和限流的操作
     */
    public <T> T executeWithFullProtection(String operationName, Supplier<T> operation,
                                          io.github.resilience4j.retry.RetryConfig retryConfig, CircuitBreakerConfig circuitBreakerConfig,
                                          RateLimiterConfig rateLimiterConfig) {
        Retry retry = getOrCreateRetry(operationName, retryConfig);
        CircuitBreaker circuitBreaker = getOrCreateCircuitBreaker(operationName, circuitBreakerConfig);
        RateLimiter rateLimiter = getOrCreateRateLimiter(operationName, rateLimiterConfig);
        
        Supplier<T> decoratedSupplier = Retry.decorateSupplier(retry,
                CircuitBreaker.decorateSupplier(circuitBreaker,
                        RateLimiter.decorateSupplier(rateLimiter, operation)));
        
        try {
            return decoratedSupplier.get();
        } catch (Exception e) {
            log.error("全保护操作最终失败: operationName={}, circuitBreakerState={}", 
                    operationName, circuitBreaker.getState(), e);
            throw e;
        }
    }
    
    /**
     * 获取异常的根本原因
     */
    private Throwable getRootCause(Throwable throwable) {
        Throwable rootCause = throwable;
        while (rootCause.getCause() != null && rootCause.getCause() != rootCause) {
            rootCause = rootCause.getCause();
        }
        return rootCause;
    }

    /**
     * Redis操作的便捷方法
     */
    public <T> T executeRedisOperation(String operationName, Supplier<T> operation) {
        try {
            return executeWithRetryAndCircuitBreaker(
                    "redis-" + operationName,
                    operation,
                    redisRetryConfig,
                    redisCircuitBreakerConfig);
        } catch (Exception e) {
            // 检查是否是Redis连接工厂停止异常
            Throwable rootCause = getRootCause(e);
            if (rootCause instanceof IllegalStateException &&
                rootCause.getMessage() != null &&
                (rootCause.getMessage().contains("STOPPING") || rootCause.getMessage().contains("STOPPED"))) {
                log.debug("Redis连接工厂正在停止或已停止，跳过Redis操作: {}", operationName);
                return null; // 返回null而不是抛出异常
            }
            throw e; // 重新抛出其他异常
        }
    }

    /**
     * API操作的便捷方法
     */
    public <T> T executeApiOperation(String operationName, Supplier<T> operation) {
        return executeWithFullProtection(
                "api-" + operationName,
                operation,
                apiRetryConfig,
                apiCircuitBreakerConfig,
                apiRateLimiterConfig);
    }

    /**
     * 数据库操作的便捷方法
     */
    public <T> T executeDatabaseOperation(String operationName, Supplier<T> operation) {
        return executeWithRetry(
                "db-" + operationName,
                operation,
                databaseRetryConfig);
    }
    
    // 私有方法：获取或创建重试实例
    private Retry getOrCreateRetry(String name, io.github.resilience4j.retry.RetryConfig config) {
        return retryCache.computeIfAbsent(name, k -> Retry.of(k, config));
    }
    
    // 私有方法：获取或创建熔断器实例
    private CircuitBreaker getOrCreateCircuitBreaker(String name, CircuitBreakerConfig config) {
        return circuitBreakerCache.computeIfAbsent(name, k -> CircuitBreaker.of(k, config));
    }
    
    // 私有方法：获取或创建限流器实例
    private RateLimiter getOrCreateRateLimiter(String name, RateLimiterConfig config) {
        return rateLimiterCache.computeIfAbsent(name, k -> RateLimiter.of(k, config));
    }
    
    /**
     * 获取重试统计信息
     */
    public String getRetryStats(String operationName) {
        Retry retry = retryCache.get(operationName);
        if (retry != null) {
            return String.format("Retry[%s]: success_with_retry=%d, success_without_retry=%d, failed_with_retry=%d, failed_without_retry=%d",
                    operationName,
                    retry.getMetrics().getNumberOfSuccessfulCallsWithRetryAttempt(),
                    retry.getMetrics().getNumberOfSuccessfulCallsWithoutRetryAttempt(),
                    retry.getMetrics().getNumberOfFailedCallsWithRetryAttempt(),
                    retry.getMetrics().getNumberOfFailedCallsWithoutRetryAttempt());
        }
        return "No retry stats for: " + operationName;
    }
    
    /**
     * 获取熔断器统计信息
     */
    public String getCircuitBreakerStats(String operationName) {
        CircuitBreaker circuitBreaker = circuitBreakerCache.get(operationName);
        if (circuitBreaker != null) {
            return String.format("CircuitBreaker[%s]: state=%s, failure_rate=%.2f%%, successful_calls=%d, failed_calls=%d",
                    operationName,
                    circuitBreaker.getState(),
                    circuitBreaker.getMetrics().getFailureRate(),
                    circuitBreaker.getMetrics().getNumberOfSuccessfulCalls(),
                    circuitBreaker.getMetrics().getNumberOfFailedCalls());
        }
        return "No circuit breaker stats for: " + operationName;
    }
}
