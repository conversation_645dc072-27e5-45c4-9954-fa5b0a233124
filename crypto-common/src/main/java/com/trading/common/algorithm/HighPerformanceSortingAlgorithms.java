package com.trading.common.algorithm;

import java.util.*;
import java.util.concurrent.ForkJoinPool;
import java.util.concurrent.RecursiveAction;
import java.math.BigDecimal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 高性能排序和搜索算法
 * 针对加密货币交易数据优化的排序和搜索算法
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class HighPerformanceSortingAlgorithms {
    
    private static final Logger log = LoggerFactory.getLogger(HighPerformanceSortingAlgorithms.class);
    private static final int PARALLEL_THRESHOLD = 1000; // 并行处理阈值
    private static final int INSERTION_SORT_THRESHOLD = 47; // 插入排序阈值
    
    /**
     * 高性能并行快速排序 - 针对大数据集优化
     */
    public static <T extends Comparable<? super T>> void parallelQuickSort(T[] array) {
        if (array == null || array.length <= 1) {
            return;
        }
        
        ForkJoinPool.commonPool().invoke(new ParallelQuickSortTask<>(array, 0, array.length - 1));
    }
    
    private static class ParallelQuickSortTask<T extends Comparable<? super T>> extends RecursiveAction {
        private final T[] array;
        private final int low;
        private final int high;
        
        ParallelQuickSortTask(T[] array, int low, int high) {
            this.array = array;
            this.low = low;
            this.high = high;
        }
        
        @Override
        protected void compute() {
            if (high - low < PARALLEL_THRESHOLD) {
                // 小数组使用优化的快速排序
                optimizedQuickSort(array, low, high);
            } else {
                // 大数组使用并行处理
                int pivotIndex = partition(array, low, high);
                
                ParallelQuickSortTask<T> leftTask = new ParallelQuickSortTask<>(array, low, pivotIndex - 1);
                ParallelQuickSortTask<T> rightTask = new ParallelQuickSortTask<>(array, pivotIndex + 1, high);
                
                invokeAll(leftTask, rightTask);
            }
        }
    }
    
    /**
     * 优化的快速排序 - 使用三路划分和插入排序优化
     */
    private static <T extends Comparable<? super T>> void optimizedQuickSort(T[] array, int low, int high) {
        if (low >= high) {
            return;
        }
        
        // 小数组使用插入排序
        if (high - low < INSERTION_SORT_THRESHOLD) {
            insertionSort(array, low, high);
            return;
        }
        
        // 三数取中选择枢轴
        int pivotIndex = medianOfThree(array, low, high);
        swap(array, pivotIndex, high);
        
        // 三路划分
        int[] partitionResult = threeWayPartition(array, low, high);
        int lt = partitionResult[0];
        int gt = partitionResult[1];
        
        optimizedQuickSort(array, low, lt - 1);
        optimizedQuickSort(array, gt + 1, high);
    }
    
    /**
     * 三路划分算法 - 处理重复元素
     */
    private static <T extends Comparable<? super T>> int[] threeWayPartition(T[] array, int low, int high) {
        T pivot = array[high];
        int lt = low;      // array[low..lt-1] < pivot
        int i = low;       // array[lt..i-1] == pivot
        int gt = high;     // array[gt+1..high] > pivot
        
        while (i < gt) {
            int cmp = array[i].compareTo(pivot);
            if (cmp < 0) {
                swap(array, lt++, i++);
            } else if (cmp > 0) {
                swap(array, i, --gt);
            } else {
                i++;
            }
        }
        swap(array, i, high);
        
        return new int[]{lt, gt};
    }
    
    /**
     * 插入排序 - 小数组优化
     */
    private static <T extends Comparable<? super T>> void insertionSort(T[] array, int low, int high) {
        for (int i = low + 1; i <= high; i++) {
            T key = array[i];
            int j = i - 1;
            
            while (j >= low && array[j].compareTo(key) > 0) {
                array[j + 1] = array[j];
                j--;
            }
            array[j + 1] = key;
        }
    }
    
    /**
     * 三数取中选择枢轴
     */
    private static <T extends Comparable<? super T>> int medianOfThree(T[] array, int low, int high) {
        int mid = low + (high - low) / 2;
        
        if (array[mid].compareTo(array[low]) < 0) {
            swap(array, low, mid);
        }
        if (array[high].compareTo(array[low]) < 0) {
            swap(array, low, high);
        }
        if (array[high].compareTo(array[mid]) < 0) {
            swap(array, mid, high);
        }
        
        return mid;
    }
    
    /**
     * 标准划分算法
     */
    private static <T extends Comparable<? super T>> int partition(T[] array, int low, int high) {
        // 三数取中选择枢轴
        int pivotIndex = medianOfThree(array, low, high);
        swap(array, pivotIndex, high);
        
        T pivot = array[high];
        int i = low - 1;
        
        for (int j = low; j < high; j++) {
            if (array[j].compareTo(pivot) <= 0) {
                i++;
                swap(array, i, j);
            }
        }
        swap(array, i + 1, high);
        return i + 1;
    }
    
    /**
     * 交换数组元素
     */
    private static <T> void swap(T[] array, int i, int j) {
        T temp = array[i];
        array[i] = array[j];
        array[j] = temp;
    }
    
    /**
     * 高性能二分搜索 - 针对BigDecimal优化
     */
    public static class BigDecimalBinarySearch {
        
        /**
         * 在排序的BigDecimal数组中搜索目标值
         */
        public static int search(BigDecimal[] array, BigDecimal target) {
            if (array == null || array.length == 0 || target == null) {
                return -1;
            }
            
            int left = 0;
            int right = array.length - 1;
            
            while (left <= right) {
                int mid = left + (right - left) / 2;
                int cmp = array[mid].compareTo(target);
                
                if (cmp == 0) {
                    return mid;
                } else if (cmp < 0) {
                    left = mid + 1;
                } else {
                    right = mid - 1;
                }
            }
            
            return -(left + 1); // 返回插入位置
        }
        
        /**
         * 查找第一个大于等于目标值的位置
         */
        public static int lowerBound(BigDecimal[] array, BigDecimal target) {
            if (array == null || array.length == 0 || target == null) {
                return 0;
            }
            
            int left = 0;
            int right = array.length;
            
            while (left < right) {
                int mid = left + (right - left) / 2;
                if (array[mid].compareTo(target) < 0) {
                    left = mid + 1;
                } else {
                    right = mid;
                }
            }
            
            return left;
        }
        
        /**
         * 查找第一个大于目标值的位置
         */
        public static int upperBound(BigDecimal[] array, BigDecimal target) {
            if (array == null || array.length == 0 || target == null) {
                return 0;
            }
            
            int left = 0;
            int right = array.length;
            
            while (left < right) {
                int mid = left + (right - left) / 2;
                if (array[mid].compareTo(target) <= 0) {
                    left = mid + 1;
                } else {
                    right = mid;
                }
            }
            
            return left;
        }
    }
    
    /**
     * 高性能时间序列数据搜索
     */
    public static class TimeSeriesSearch {
        
        /**
         * 在时间戳排序的数组中查找指定时间范围的数据
         */
        public static int[] findTimeRange(long[] timestamps, long startTime, long endTime) {
            if (timestamps == null || timestamps.length == 0) {
                return new int[]{-1, -1};
            }
            
            int startIndex = findFirstGreaterOrEqual(timestamps, startTime);
            if (startIndex == timestamps.length) {
                return new int[]{-1, -1}; // 没有找到
            }
            
            int endIndex = findLastLessOrEqual(timestamps, endTime);
            if (endIndex == -1) {
                return new int[]{-1, -1}; // 没有找到
            }
            
            return new int[]{startIndex, endIndex};
        }
        
        private static int findFirstGreaterOrEqual(long[] array, long target) {
            int left = 0;
            int right = array.length;
            
            while (left < right) {
                int mid = left + (right - left) / 2;
                if (array[mid] < target) {
                    left = mid + 1;
                } else {
                    right = mid;
                }
            }
            
            return left;
        }
        
        private static int findLastLessOrEqual(long[] array, long target) {
            int left = -1;
            int right = array.length - 1;
            
            while (left < right) {
                int mid = left + (right - left + 1) / 2;
                if (array[mid] <= target) {
                    left = mid;
                } else {
                    right = mid - 1;
                }
            }
            
            return left;
        }
    }
    
    /**
     * 高性能Top-K算法 - 使用快速选择
     */
    public static <T extends Comparable<? super T>> List<T> findTopK(T[] array, int k) {
        if (array == null || array.length == 0 || k <= 0) {
            return new ArrayList<>();
        }
        
        k = Math.min(k, array.length);
        T[] copy = Arrays.copyOf(array, array.length);
        
        quickSelect(copy, 0, copy.length - 1, copy.length - k);
        
        List<T> result = new ArrayList<>(k);
        for (int i = copy.length - k; i < copy.length; i++) {
            result.add(copy[i]);
        }
        
        return result;
    }
    
    /**
     * 快速选择算法
     */
    private static <T extends Comparable<? super T>> void quickSelect(T[] array, int low, int high, int k) {
        if (low >= high) {
            return;
        }
        
        int pivotIndex = partition(array, low, high);
        
        if (pivotIndex == k) {
            return;
        } else if (pivotIndex < k) {
            quickSelect(array, pivotIndex + 1, high, k);
        } else {
            quickSelect(array, low, pivotIndex - 1, k);
        }
    }
}
