package com.trading.common.algorithm;

import java.util.concurrent.*;
import java.util.concurrent.atomic.*;
import java.lang.ref.SoftReference;
import java.util.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 高性能内存管理算法
 * 提供对象池、内存预分配、垃圾回收优化等功能
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class HighPerformanceMemoryManager {
    
    private static final Logger log = LoggerFactory.getLogger(HighPerformanceMemoryManager.class);
    
    /**
     * 高性能对象池 - 使用无锁算法
     */
    public static class LockFreeObjectPool<T> {
        private final AtomicReference<Node<T>> head = new AtomicReference<>();
        private final AtomicInteger size = new AtomicInteger(0);
        private final int maxSize;
        private final ObjectFactory<T> factory;
        private final ObjectResetter<T> resetter;
        
        private static class Node<T> {
            volatile T item;
            volatile Node<T> next;
            
            Node(T item, Node<T> next) {
                this.item = item;
                this.next = next;
            }
        }
        
        public interface ObjectFactory<T> {
            T create();
        }
        
        public interface ObjectResetter<T> {
            void reset(T object);
        }
        
        public LockFreeObjectPool(int maxSize, ObjectFactory<T> factory, ObjectResetter<T> resetter) {
            this.maxSize = maxSize;
            this.factory = factory;
            this.resetter = resetter;
        }
        
        public T acquire() {
            Node<T> head = this.head.get();
            if (head != null && this.head.compareAndSet(head, head.next)) {
                size.decrementAndGet();
                T item = head.item;
                head.item = null; // 清理引用
                return item;
            }
            
            // 池中没有对象，创建新对象
            return factory.create();
        }
        
        public void release(T object) {
            if (object == null || size.get() >= maxSize) {
                return; // 池已满或对象为空
            }
            
            // 重置对象状态
            if (resetter != null) {
                resetter.reset(object);
            }
            
            Node<T> newHead = new Node<>(object, head.get());
            while (!head.compareAndSet(newHead.next, newHead)) {
                newHead.next = head.get();
            }
            size.incrementAndGet();
        }
        
        public int size() {
            return size.get();
        }
    }
    
    /**
     * 内存预分配管理器 - 减少GC压力
     */
    public static class MemoryPreAllocator {
        private final Map<Class<?>, Queue<Object>> preAllocatedObjects = new ConcurrentHashMap<>();
        private final Map<Class<?>, Integer> allocationSizes = new ConcurrentHashMap<>();
        private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
        
        public MemoryPreAllocator() {
            // 定期清理未使用的对象
            cleanupExecutor.scheduleAtFixedRate(this::cleanup, 60, 60, TimeUnit.SECONDS);
        }
        
        public <T> void preAllocate(Class<T> clazz, int count, LockFreeObjectPool.ObjectFactory<T> factory) {
            Queue<Object> queue = preAllocatedObjects.computeIfAbsent(clazz, k -> new ConcurrentLinkedQueue<>());
            allocationSizes.put(clazz, count);
            
            for (int i = 0; i < count; i++) {
                queue.offer(factory.create());
            }
            
            log.info("预分配对象完成: {} x {}", clazz.getSimpleName(), count);
        }
        
        @SuppressWarnings("unchecked")
        public <T> T acquire(Class<T> clazz, LockFreeObjectPool.ObjectFactory<T> factory) {
            Queue<Object> queue = preAllocatedObjects.get(clazz);
            if (queue != null) {
                Object obj = queue.poll();
                if (obj != null) {
                    return (T) obj;
                }
            }
            
            // 预分配池中没有对象，创建新对象
            return factory.create();
        }
        
        public <T> void release(Class<T> clazz, T object) {
            if (object == null) {
                return;
            }
            
            Queue<Object> queue = preAllocatedObjects.get(clazz);
            if (queue != null) {
                Integer maxSize = allocationSizes.get(clazz);
                if (maxSize == null || queue.size() < maxSize) {
                    queue.offer(object);
                }
            }
        }
        
        private void cleanup() {
            for (Map.Entry<Class<?>, Queue<Object>> entry : preAllocatedObjects.entrySet()) {
                Queue<Object> queue = entry.getValue();
                Integer maxSize = allocationSizes.get(entry.getKey());
                
                if (maxSize != null && queue.size() > maxSize) {
                    // 移除多余的对象
                    while (queue.size() > maxSize) {
                        queue.poll();
                    }
                }
            }
        }
        
        public void shutdown() {
            cleanupExecutor.shutdown();
        }
    }
    
    /**
     * 软引用缓存 - 自动内存管理
     */
    public static class SoftReferenceCache<K, V> {
        private final ConcurrentHashMap<K, SoftReference<V>> cache = new ConcurrentHashMap<>();
        private final ScheduledExecutorService cleanupExecutor = Executors.newSingleThreadScheduledExecutor();
        
        public SoftReferenceCache() {
            // 定期清理失效的软引用
            cleanupExecutor.scheduleAtFixedRate(this::cleanupStaleReferences, 30, 30, TimeUnit.SECONDS);
        }
        
        public void put(K key, V value) {
            cache.put(key, new SoftReference<>(value));
        }
        
        public V get(K key) {
            SoftReference<V> ref = cache.get(key);
            if (ref != null) {
                V value = ref.get();
                if (value == null) {
                    // 软引用已被回收，移除键
                    cache.remove(key);
                }
                return value;
            }
            return null;
        }
        
        public V remove(K key) {
            SoftReference<V> ref = cache.remove(key);
            return ref != null ? ref.get() : null;
        }
        
        public void clear() {
            cache.clear();
        }
        
        public int size() {
            return cache.size();
        }
        
        private void cleanupStaleReferences() {
            Iterator<Map.Entry<K, SoftReference<V>>> iterator = cache.entrySet().iterator();
            int cleaned = 0;
            
            while (iterator.hasNext()) {
                Map.Entry<K, SoftReference<V>> entry = iterator.next();
                if (entry.getValue().get() == null) {
                    iterator.remove();
                    cleaned++;
                }
            }
            
            if (cleaned > 0) {
                log.debug("清理失效软引用: {} 个", cleaned);
            }
        }
        
        public void shutdown() {
            cleanupExecutor.shutdown();
        }
    }
    
    /**
     * 内存使用监控器
     */
    public static class MemoryMonitor {
        private final Runtime runtime = Runtime.getRuntime();
        private final AtomicLong maxMemoryUsed = new AtomicLong(0);
        private final AtomicLong gcCount = new AtomicLong(0);
        
        public MemoryStats getMemoryStats() {
            long totalMemory = runtime.totalMemory();
            long freeMemory = runtime.freeMemory();
            long usedMemory = totalMemory - freeMemory;
            long maxMemory = runtime.maxMemory();
            
            // 更新最大内存使用量
            maxMemoryUsed.updateAndGet(current -> Math.max(current, usedMemory));
            
            return new MemoryStats(
                totalMemory,
                freeMemory,
                usedMemory,
                maxMemory,
                maxMemoryUsed.get(),
                (double) usedMemory / maxMemory * 100
            );
        }
        
        public void forceGC() {
            long before = getMemoryStats().usedMemory;
            System.gc();
            gcCount.incrementAndGet();
            long after = getMemoryStats().usedMemory;
            
            log.info("强制GC完成: 释放内存 {} MB", (before - after) / 1024 / 1024);
        }
        
        public long getGCCount() {
            return gcCount.get();
        }
        
        public static class MemoryStats {
            public final long totalMemory;
            public final long freeMemory;
            public final long usedMemory;
            public final long maxMemory;
            public final long maxMemoryUsed;
            public final double usagePercentage;
            
            MemoryStats(long totalMemory, long freeMemory, long usedMemory, 
                       long maxMemory, long maxMemoryUsed, double usagePercentage) {
                this.totalMemory = totalMemory;
                this.freeMemory = freeMemory;
                this.usedMemory = usedMemory;
                this.maxMemory = maxMemory;
                this.maxMemoryUsed = maxMemoryUsed;
                this.usagePercentage = usagePercentage;
            }
            
            @Override
            public String toString() {
                return String.format("Memory[used=%dMB, free=%dMB, total=%dMB, max=%dMB, usage=%.2f%%]",
                    usedMemory / 1024 / 1024,
                    freeMemory / 1024 / 1024,
                    totalMemory / 1024 / 1024,
                    maxMemory / 1024 / 1024,
                    usagePercentage);
            }
        }
    }
    
    /**
     * 批量对象创建器 - 减少内存分配开销
     */
    public static class BatchObjectCreator<T> {
        private final LockFreeObjectPool.ObjectFactory<T> factory;
        private final int batchSize;

        public BatchObjectCreator(LockFreeObjectPool.ObjectFactory<T> factory, int batchSize) {
            this.factory = factory;
            this.batchSize = batchSize;
        }
        
        public List<T> createBatch() {
            List<T> batch = new ArrayList<>(batchSize);
            for (int i = 0; i < batchSize; i++) {
                batch.add(factory.create());
            }
            return batch;
        }
        
        public T[] createArray(Class<T> clazz) {
            @SuppressWarnings("unchecked")
            T[] array = (T[]) java.lang.reflect.Array.newInstance(clazz, batchSize);
            for (int i = 0; i < batchSize; i++) {
                array[i] = factory.create();
            }
            return array;
        }
    }
}
