package com.trading.common.algorithm;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.concurrent.locks.StampedLock;
import java.util.function.Function;
import java.util.BitSet;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 超高性能数据去重算法
 * 使用多种去重策略和布隆过滤器优化
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class UltraFastDeduplicator {
    
    private static final Logger log = LoggerFactory.getLogger(UltraFastDeduplicator.class);
    
    // 去重策略枚举
    public enum DeduplicationStrategy {
        HASH_SET,           // 基于HashSet的精确去重
        BLOOM_FILTER,       // 基于布隆过滤器的概率去重
        LRU_CACHE,          // 基于LRU缓存的去重
        TIME_WINDOW,        // 基于时间窗口的去重
        HYBRID              // 混合策略
    }
    
    // 布隆过滤器参数
    private static final int BLOOM_FILTER_SIZE = 1_000_000;
    private static final int HASH_FUNCTIONS = 3;
    
    // 时间窗口参数
    private static final long TIME_WINDOW_MS = 60_000; // 1分钟
    private static final int MAX_CACHE_SIZE = 10_000;
    
    // 数据结构
    private final ConcurrentHashMap<String, Long> exactDeduplicationCache = new ConcurrentHashMap<>();
    private final BitSet bloomFilter = new BitSet(BLOOM_FILTER_SIZE);
    private final StampedLock bloomFilterLock = new StampedLock();
    
    // 时间窗口去重
    private final ConcurrentHashMap<String, TimeWindowEntry> timeWindowCache = new ConcurrentHashMap<>();
    
    // 性能统计
    private final LongAdder totalChecks = new LongAdder();
    private final LongAdder duplicatesFound = new LongAdder();
    private final LongAdder bloomFilterHits = new LongAdder();
    private final LongAdder exactCacheHits = new LongAdder();
    private final LongAdder timeWindowHits = new LongAdder();
    private final AtomicLong totalProcessingTime = new AtomicLong(0);
    
    @PostConstruct
    public void initialize() {
        log.info("超高性能数据去重算法已初始化");
    }
    
    /**
     * 检查数据是否重复
     */
    public boolean isDuplicate(String key) {
        return isDuplicate(key, DeduplicationStrategy.HYBRID);
    }
    
    /**
     * 检查数据是否重复（指定策略）
     */
    public boolean isDuplicate(String key, DeduplicationStrategy strategy) {
        if (key == null || key.isEmpty()) {
            return false;
        }
        
        long startTime = System.nanoTime();
        totalChecks.increment();
        
        try {
            boolean isDuplicate = false;
            
            switch (strategy) {
                case HASH_SET:
                    isDuplicate = checkWithHashSet(key);
                    break;
                case BLOOM_FILTER:
                    isDuplicate = checkWithBloomFilter(key);
                    break;
                case LRU_CACHE:
                    isDuplicate = checkWithLRUCache(key);
                    break;
                case TIME_WINDOW:
                    isDuplicate = checkWithTimeWindow(key);
                    break;
                case HYBRID:
                default:
                    isDuplicate = checkWithHybridStrategy(key);
                    break;
            }
            
            if (isDuplicate) {
                duplicatesFound.increment();
            }
            
            return isDuplicate;
            
        } finally {
            long endTime = System.nanoTime();
            totalProcessingTime.addAndGet(endTime - startTime);
        }
    }
    
    /**
     * 标记数据已处理
     */
    public void markAsProcessed(String key) {
        markAsProcessed(key, DeduplicationStrategy.HYBRID);
    }
    
    /**
     * 标记数据已处理（指定策略）
     */
    public void markAsProcessed(String key, DeduplicationStrategy strategy) {
        if (key == null || key.isEmpty()) {
            return;
        }
        
        switch (strategy) {
            case HASH_SET:
                markInHashSet(key);
                break;
            case BLOOM_FILTER:
                markInBloomFilter(key);
                break;
            case LRU_CACHE:
                markInLRUCache(key);
                break;
            case TIME_WINDOW:
                markInTimeWindow(key);
                break;
            case HYBRID:
            default:
                markWithHybridStrategy(key);
                break;
        }
    }
    
    /**
     * HashSet策略检查
     */
    private boolean checkWithHashSet(String key) {
        boolean exists = exactDeduplicationCache.containsKey(key);
        if (exists) {
            exactCacheHits.increment();
        }
        return exists;
    }
    
    /**
     * HashSet策略标记
     */
    private void markInHashSet(String key) {
        exactDeduplicationCache.put(key, System.currentTimeMillis());
        
        // 清理过期数据
        if (exactDeduplicationCache.size() > MAX_CACHE_SIZE) {
            cleanupExactCache();
        }
    }
    
    /**
     * 布隆过滤器策略检查
     */
    private boolean checkWithBloomFilter(String key) {
        long stamp = bloomFilterLock.readLock();
        try {
            boolean mightExist = true;
            for (int i = 0; i < HASH_FUNCTIONS; i++) {
                int hash = hash(key, i) % BLOOM_FILTER_SIZE;
                if (hash < 0) hash = -hash;
                if (!bloomFilter.get(hash)) {
                    mightExist = false;
                    break;
                }
            }
            
            if (mightExist) {
                bloomFilterHits.increment();
            }
            
            return mightExist;
        } finally {
            bloomFilterLock.unlockRead(stamp);
        }
    }
    
    /**
     * 布隆过滤器策略标记
     */
    private void markInBloomFilter(String key) {
        long stamp = bloomFilterLock.writeLock();
        try {
            for (int i = 0; i < HASH_FUNCTIONS; i++) {
                int hash = hash(key, i) % BLOOM_FILTER_SIZE;
                if (hash < 0) hash = -hash;
                bloomFilter.set(hash);
            }
        } finally {
            bloomFilterLock.unlockWrite(stamp);
        }
    }
    
    /**
     * LRU缓存策略检查
     */
    private boolean checkWithLRUCache(String key) {
        // 简化的LRU实现，使用时间戳
        Long lastAccess = exactDeduplicationCache.get(key);
        if (lastAccess != null) {
            // 更新访问时间
            exactDeduplicationCache.put(key, System.currentTimeMillis());
            exactCacheHits.increment();
            return true;
        }
        return false;
    }
    
    /**
     * LRU缓存策略标记
     */
    private void markInLRUCache(String key) {
        exactDeduplicationCache.put(key, System.currentTimeMillis());
        
        // LRU清理
        if (exactDeduplicationCache.size() > MAX_CACHE_SIZE) {
            cleanupLRUCache();
        }
    }
    
    /**
     * 时间窗口策略检查
     */
    private boolean checkWithTimeWindow(String key) {
        TimeWindowEntry entry = timeWindowCache.get(key);
        if (entry != null) {
            long currentTime = System.currentTimeMillis();
            if (currentTime - entry.timestamp <= TIME_WINDOW_MS) {
                timeWindowHits.increment();
                return true;
            } else {
                // 过期，移除
                timeWindowCache.remove(key);
            }
        }
        return false;
    }
    
    /**
     * 时间窗口策略标记
     */
    private void markInTimeWindow(String key) {
        timeWindowCache.put(key, new TimeWindowEntry(System.currentTimeMillis()));
        
        // 定期清理过期数据
        if (ThreadLocalRandom.current().nextInt(100) == 0) {
            cleanupTimeWindowCache();
        }
    }
    
    /**
     * 混合策略检查
     */
    private boolean checkWithHybridStrategy(String key) {
        // 1. 首先检查布隆过滤器（快速排除）
        if (!checkWithBloomFilter(key)) {
            return false; // 肯定不存在
        }
        
        // 2. 布隆过滤器可能存在，检查精确缓存
        if (checkWithHashSet(key)) {
            return true; // 确实存在
        }
        
        // 3. 检查时间窗口
        return checkWithTimeWindow(key);
    }
    
    /**
     * 混合策略标记
     */
    private void markWithHybridStrategy(String key) {
        // 同时在多个结构中标记
        markInBloomFilter(key);
        markInHashSet(key);
        markInTimeWindow(key);
    }
    
    /**
     * 哈希函数
     */
    private int hash(String key, int seed) {
        int hash = key.hashCode();
        hash ^= (hash >>> 16);
        hash *= 0x85ebca6b;
        hash ^= (hash >>> 13);
        hash *= 0xc2b2ae35;
        hash ^= (hash >>> 16);
        return hash ^ seed;
    }
    
    /**
     * 清理精确缓存
     */
    private void cleanupExactCache() {
        if (exactDeduplicationCache.size() <= MAX_CACHE_SIZE) {
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        long expireTime = currentTime - TIME_WINDOW_MS;
        
        exactDeduplicationCache.entrySet().removeIf(entry -> 
            entry.getValue() < expireTime);
        
        // 如果还是太大，移除最老的一半
        if (exactDeduplicationCache.size() > MAX_CACHE_SIZE) {
            exactDeduplicationCache.entrySet().stream()
                .sorted((e1, e2) -> Long.compare(e1.getValue(), e2.getValue()))
                .limit(exactDeduplicationCache.size() / 2)
                .forEach(entry -> exactDeduplicationCache.remove(entry.getKey()));
        }
    }
    
    /**
     * 清理LRU缓存
     */
    private void cleanupLRUCache() {
        // 移除最少使用的条目
        exactDeduplicationCache.entrySet().stream()
            .sorted((e1, e2) -> Long.compare(e1.getValue(), e2.getValue()))
            .limit(exactDeduplicationCache.size() - MAX_CACHE_SIZE + 1000)
            .forEach(entry -> exactDeduplicationCache.remove(entry.getKey()));
    }
    
    /**
     * 清理时间窗口缓存
     */
    private void cleanupTimeWindowCache() {
        long currentTime = System.currentTimeMillis();
        long expireTime = currentTime - TIME_WINDOW_MS;
        
        timeWindowCache.entrySet().removeIf(entry -> 
            entry.getValue().timestamp < expireTime);
    }
    
    /**
     * 获取去重统计信息
     */
    public DeduplicationStats getStats() {
        long checks = totalChecks.sum();
        long duplicates = duplicatesFound.sum();
        long bloomHits = bloomFilterHits.sum();
        long exactHits = exactCacheHits.sum();
        long timeHits = timeWindowHits.sum();
        
        double duplicateRate = checks > 0 ? (double) duplicates / checks : 0;
        double avgProcessingTime = checks > 0 ? 
            (double) totalProcessingTime.get() / checks / 1_000_000 : 0; // ms
        
        return new DeduplicationStats(
            checks, duplicates, duplicateRate, avgProcessingTime,
            bloomHits, exactHits, timeHits,
            exactDeduplicationCache.size(), timeWindowCache.size()
        );
    }
    
    /**
     * 清理所有缓存
     */
    public void clearAll() {
        exactDeduplicationCache.clear();
        timeWindowCache.clear();
        
        long stamp = bloomFilterLock.writeLock();
        try {
            bloomFilter.clear();
        } finally {
            bloomFilterLock.unlockWrite(stamp);
        }
        
        log.info("所有去重缓存已清理");
    }
    
    // 内部类定义
    private static class TimeWindowEntry {
        public final long timestamp;
        
        public TimeWindowEntry(long timestamp) {
            this.timestamp = timestamp;
        }
    }
    
    public static class DeduplicationStats {
        public final long totalChecks;
        public final long duplicatesFound;
        public final double duplicateRate;
        public final double avgProcessingTimeMs;
        public final long bloomFilterHits;
        public final long exactCacheHits;
        public final long timeWindowHits;
        public final int exactCacheSize;
        public final int timeWindowCacheSize;
        
        public DeduplicationStats(long totalChecks, long duplicatesFound, double duplicateRate,
                                double avgProcessingTimeMs, long bloomFilterHits, long exactCacheHits,
                                long timeWindowHits, int exactCacheSize, int timeWindowCacheSize) {
            this.totalChecks = totalChecks;
            this.duplicatesFound = duplicatesFound;
            this.duplicateRate = duplicateRate;
            this.avgProcessingTimeMs = avgProcessingTimeMs;
            this.bloomFilterHits = bloomFilterHits;
            this.exactCacheHits = exactCacheHits;
            this.timeWindowHits = timeWindowHits;
            this.exactCacheSize = exactCacheSize;
            this.timeWindowCacheSize = timeWindowCacheSize;
        }
        
        @Override
        public String toString() {
            return String.format(
                "DeduplicationStats{checks=%d, duplicates=%d, rate=%.2f%%, " +
                "avgTime=%.3fms, bloomHits=%d, exactHits=%d, timeHits=%d, " +
                "exactCache=%d, timeCache=%d}",
                totalChecks, duplicatesFound, duplicateRate * 100,
                avgProcessingTimeMs, bloomFilterHits, exactCacheHits, timeWindowHits,
                exactCacheSize, timeWindowCacheSize
            );
        }
    }
}
