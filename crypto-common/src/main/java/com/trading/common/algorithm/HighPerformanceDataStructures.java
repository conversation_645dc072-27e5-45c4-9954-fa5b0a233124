package com.trading.common.algorithm;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.*;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import java.util.function.Function;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 高性能数据结构优化器
 * 提供针对加密货币交易系统优化的数据结构和算法
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class HighPerformanceDataStructures {
    
    private static final Logger log = LoggerFactory.getLogger(HighPerformanceDataStructures.class);
    
    /**
     * 高性能环形缓冲区 - 用于高频数据处理
     */
    public static class RingBuffer<T> {
        private final Object[] buffer;
        private final int capacity;
        private final AtomicLong writeIndex = new AtomicLong(0);
        private final AtomicLong readIndex = new AtomicLong(0);
        private final int mask;
        
        public RingBuffer(int capacity) {
            // 确保容量是2的幂，便于位运算优化
            this.capacity = Integer.highestOneBit(capacity - 1) << 1;
            this.buffer = new Object[this.capacity];
            this.mask = this.capacity - 1;
        }
        
        public boolean offer(T item) {
            long currentWrite = writeIndex.get();
            long nextWrite = currentWrite + 1;
            
            // 检查是否会覆盖未读数据
            if (nextWrite - readIndex.get() > capacity) {
                return false; // 缓冲区满
            }
            
            buffer[(int)(currentWrite & mask)] = item;
            writeIndex.lazySet(nextWrite); // 使用lazySet提高性能
            return true;
        }
        
        @SuppressWarnings("unchecked")
        public T poll() {
            long currentRead = readIndex.get();
            if (currentRead >= writeIndex.get()) {
                return null; // 缓冲区空
            }
            
            T item = (T) buffer[(int)(currentRead & mask)];
            buffer[(int)(currentRead & mask)] = null; // 清理引用
            readIndex.lazySet(currentRead + 1);
            return item;
        }
        
        public int size() {
            return (int)(writeIndex.get() - readIndex.get());
        }
        
        public boolean isEmpty() {
            return readIndex.get() >= writeIndex.get();
        }
    }
    
    /**
     * 高性能LRU缓存 - 使用双向链表和哈希表
     */
    public static class FastLRUCache<K, V> {
        private final int capacity;
        private final ConcurrentHashMap<K, Node<K, V>> map;
        private final Node<K, V> head;
        private final Node<K, V> tail;
        private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
        
        private static class Node<K, V> {
            K key;
            V value;
            Node<K, V> prev;
            Node<K, V> next;
            
            Node(K key, V value) {
                this.key = key;
                this.value = value;
            }
        }
        
        public FastLRUCache(int capacity) {
            this.capacity = capacity;
            this.map = new ConcurrentHashMap<>(capacity);
            this.head = new Node<>(null, null);
            this.tail = new Node<>(null, null);
            head.next = tail;
            tail.prev = head;
        }
        
        public V get(K key) {
            Node<K, V> node = map.get(key);
            if (node == null) {
                return null;
            }
            
            // 移动到头部
            lock.writeLock().lock();
            try {
                moveToHead(node);
            } finally {
                lock.writeLock().unlock();
            }
            
            return node.value;
        }
        
        public void put(K key, V value) {
            Node<K, V> existing = map.get(key);
            
            lock.writeLock().lock();
            try {
                if (existing != null) {
                    // 更新现有节点
                    existing.value = value;
                    moveToHead(existing);
                } else {
                    // 添加新节点
                    Node<K, V> newNode = new Node<>(key, value);
                    map.put(key, newNode);
                    addToHead(newNode);
                    
                    if (map.size() > capacity) {
                        // 移除尾部节点
                        Node<K, V> tail = removeTail();
                        map.remove(tail.key);
                    }
                }
            } finally {
                lock.writeLock().unlock();
            }
        }
        
        private void addToHead(Node<K, V> node) {
            node.prev = head;
            node.next = head.next;
            head.next.prev = node;
            head.next = node;
        }
        
        private void removeNode(Node<K, V> node) {
            node.prev.next = node.next;
            node.next.prev = node.prev;
        }
        
        private void moveToHead(Node<K, V> node) {
            removeNode(node);
            addToHead(node);
        }
        
        private Node<K, V> removeTail() {
            Node<K, V> lastNode = tail.prev;
            removeNode(lastNode);
            return lastNode;
        }
    }
    
    /**
     * 高性能优先级队列 - 使用二叉堆优化
     */
    public static class FastPriorityQueue<T> {
        private Object[] heap;
        private int size;
        private final Comparator<? super T> comparator;
        private static final int DEFAULT_CAPACITY = 11;
        
        public FastPriorityQueue() {
            this(DEFAULT_CAPACITY, null);
        }
        
        public FastPriorityQueue(Comparator<? super T> comparator) {
            this(DEFAULT_CAPACITY, comparator);
        }
        
        public FastPriorityQueue(int initialCapacity, Comparator<? super T> comparator) {
            this.heap = new Object[initialCapacity];
            this.size = 0;
            this.comparator = comparator;
        }
        
        public boolean offer(T item) {
            if (size >= heap.length) {
                grow();
            }
            
            if (size == 0) {
                heap[0] = item;
            } else {
                siftUp(size, item);
            }
            size++;
            return true;
        }
        
        @SuppressWarnings("unchecked")
        public T poll() {
            if (size == 0) {
                return null;
            }
            
            int s = --size;
            T result = (T) heap[0];
            T x = (T) heap[s];
            heap[s] = null;
            
            if (s != 0) {
                siftDown(0, x);
            }
            
            return result;
        }
        
        @SuppressWarnings("unchecked")
        public T peek() {
            return size == 0 ? null : (T) heap[0];
        }
        
        public int size() {
            return size;
        }
        
        public boolean isEmpty() {
            return size == 0;
        }
        
        private void grow() {
            int oldCapacity = heap.length;
            int newCapacity = oldCapacity + (oldCapacity < 64 ? oldCapacity + 2 : oldCapacity >> 1);
            heap = Arrays.copyOf(heap, newCapacity);
        }
        
        @SuppressWarnings("unchecked")
        private void siftUp(int k, T x) {
            if (comparator != null) {
                siftUpUsingComparator(k, x);
            } else {
                siftUpComparable(k, x);
            }
        }
        
        @SuppressWarnings("unchecked")
        private void siftUpComparable(int k, T x) {
            Comparable<? super T> key = (Comparable<? super T>) x;
            while (k > 0) {
                int parent = (k - 1) >>> 1;
                Object e = heap[parent];
                if (key.compareTo((T) e) >= 0) {
                    break;
                }
                heap[k] = e;
                k = parent;
            }
            heap[k] = key;
        }
        
        @SuppressWarnings("unchecked")
        private void siftUpUsingComparator(int k, T x) {
            while (k > 0) {
                int parent = (k - 1) >>> 1;
                Object e = heap[parent];
                if (comparator.compare(x, (T) e) >= 0) {
                    break;
                }
                heap[k] = e;
                k = parent;
            }
            heap[k] = x;
        }
        
        @SuppressWarnings("unchecked")
        private void siftDown(int k, T x) {
            if (comparator != null) {
                siftDownUsingComparator(k, x);
            } else {
                siftDownComparable(k, x);
            }
        }
        
        @SuppressWarnings("unchecked")
        private void siftDownComparable(int k, T x) {
            Comparable<? super T> key = (Comparable<? super T>) x;
            int half = size >>> 1;
            while (k < half) {
                int child = (k << 1) + 1;
                Object c = heap[child];
                int right = child + 1;
                if (right < size && ((Comparable<? super T>) c).compareTo((T) heap[right]) > 0) {
                    c = heap[child = right];
                }
                if (key.compareTo((T) c) <= 0) {
                    break;
                }
                heap[k] = c;
                k = child;
            }
            heap[k] = key;
        }
        
        @SuppressWarnings("unchecked")
        private void siftDownUsingComparator(int k, T x) {
            int half = size >>> 1;
            while (k < half) {
                int child = (k << 1) + 1;
                Object c = heap[child];
                int right = child + 1;
                if (right < size && comparator.compare((T) c, (T) heap[right]) > 0) {
                    c = heap[child = right];
                }
                if (comparator.compare(x, (T) c) <= 0) {
                    break;
                }
                heap[k] = c;
                k = child;
            }
            heap[k] = x;
        }
    }
}
