package com.trading.common.batch;

import com.trading.common.memory.UltraFastMemoryManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.LongAdder;
import java.util.function.Consumer;

/**
 * 超高性能批处理器
 * 使用多种优化技术实现高吞吐量的批处理
 *
 * @param <T> 处理数据类型
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class UltraFastBatchProcessor<T> {
    
    private static final Logger log = LoggerFactory.getLogger(UltraFastBatchProcessor.class);
    
    // 默认配置
    private static final int DEFAULT_BATCH_SIZE = 500;
    private static final int DEFAULT_FLUSH_INTERVAL_MS = 100;
    private static final int DEFAULT_MAX_QUEUE_SIZE = 10000;
    private static final int DEFAULT_WORKER_THREADS = Runtime.getRuntime().availableProcessors();
    
    @Autowired
    private UltraFastMemoryManager memoryManager;
    
    // 配置参数
    private final int batchSize;
    private final int flushIntervalMs;
    private final int maxQueueSize;
    private final int workerThreads;
    
    // 多队列分片，减少锁竞争
    private final ConcurrentLinkedQueue<T>[] shardedQueues;
    private final AtomicInteger[] queueSizes;
    private final int shardCount;
    
    // 批处理器
    private final Consumer<List<T>> batchProcessor;
    
    // 线程池
    private final ExecutorService workerExecutor;
    private final ScheduledExecutorService flushExecutor;
    
    // 状态控制
    private final AtomicBoolean running = new AtomicBoolean(false);
    private final AtomicBoolean shutdown = new AtomicBoolean(false);
    
    // 性能统计
    private final LongAdder itemsProcessed = new LongAdder();
    private final LongAdder batchesProcessed = new LongAdder();
    private final LongAdder queueOverflows = new LongAdder();
    private final LongAdder processingErrors = new LongAdder();
    private final LongAdder totalProcessingTime = new LongAdder();
    
    // 负载均衡
    private final AtomicInteger roundRobinCounter = new AtomicInteger(0);
    
    /**
     * 创建超高性能批处理器
     */
    @SuppressWarnings("unchecked")
    public UltraFastBatchProcessor(Consumer<List<T>> batchProcessor) {
        this(batchProcessor, DEFAULT_BATCH_SIZE, DEFAULT_FLUSH_INTERVAL_MS, 
             DEFAULT_MAX_QUEUE_SIZE, DEFAULT_WORKER_THREADS);
    }
    
    /**
     * 创建超高性能批处理器
     */
    @SuppressWarnings("unchecked")
    public UltraFastBatchProcessor(Consumer<List<T>> batchProcessor, 
                                  int batchSize, int flushIntervalMs, 
                                  int maxQueueSize, int workerThreads) {
        this.batchProcessor = batchProcessor;
        this.batchSize = batchSize;
        this.flushIntervalMs = flushIntervalMs;
        this.maxQueueSize = maxQueueSize;
        this.workerThreads = workerThreads;
        
        // 创建分片队列，减少锁竞争
        this.shardCount = Math.max(4, workerThreads);
        this.shardedQueues = new ConcurrentLinkedQueue[shardCount];
        this.queueSizes = new AtomicInteger[shardCount];
        
        for (int i = 0; i < shardCount; i++) {
            shardedQueues[i] = new ConcurrentLinkedQueue<>();
            queueSizes[i] = new AtomicInteger(0);
        }
        
        // 创建线程池
        this.workerExecutor = Executors.newFixedThreadPool(workerThreads, r -> {
            Thread t = new Thread(r, "ultra-fast-batch-worker");
            t.setDaemon(true);
            return t;
        });
        
        this.flushExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "ultra-fast-batch-flush");
            t.setDaemon(true);
            return t;
        });
    }
    
    @PostConstruct
    public void start() {
        if (running.compareAndSet(false, true)) {
            // 启动工作线程
            for (int i = 0; i < workerThreads; i++) {
                final int shardIndex = i % shardCount;
                workerExecutor.submit(() -> workerLoop(shardIndex));
            }
            
            // 启动定时刷新任务
            flushExecutor.scheduleAtFixedRate(this::flushAll, 
                flushIntervalMs, flushIntervalMs, TimeUnit.MILLISECONDS);
            
            log.info("超高性能批处理器已启动: batchSize={}, flushInterval={}ms, workers={}, shards={}", 
                batchSize, flushIntervalMs, workerThreads, shardCount);
        }
    }
    
    /**
     * 添加数据到批处理队列
     */
    public boolean add(T item) {
        if (item == null || shutdown.get()) {
            return false;
        }
        
        // 选择分片队列（负载均衡）
        int shardIndex = selectShard(item);
        ConcurrentLinkedQueue<T> queue = shardedQueues[shardIndex];
        AtomicInteger queueSize = queueSizes[shardIndex];
        
        // 检查队列大小限制
        if (queueSize.get() >= maxQueueSize / shardCount) {
            queueOverflows.increment();
            return false;
        }
        
        // 添加到队列
        if (queue.offer(item)) {
            queueSize.incrementAndGet();
            return true;
        }
        
        return false;
    }
    
    /**
     * 选择分片队列
     */
    private int selectShard(T item) {
        // 使用轮询算法进行负载均衡
        return Math.abs(roundRobinCounter.getAndIncrement()) % shardCount;
    }
    
    /**
     * 工作线程循环
     */
    private void workerLoop(int shardIndex) {
        ConcurrentLinkedQueue<T> queue = shardedQueues[shardIndex];
        AtomicInteger queueSize = queueSizes[shardIndex];
        List<T> batch = new ArrayList<>(batchSize);
        
        while (running.get() || !queue.isEmpty()) {
            try {
                // 收集批次数据
                collectBatch(queue, queueSize, batch);
                
                if (!batch.isEmpty()) {
                    // 处理批次
                    processBatch(batch);
                    batch.clear();
                } else {
                    // 没有数据时短暂休眠
                    Thread.sleep(1);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            } catch (Exception e) {
                log.error("批处理工作线程异常: shard={}", shardIndex, e);
                processingErrors.increment();
            }
        }
        
        log.debug("批处理工作线程退出: shard={}", shardIndex);
    }
    
    /**
     * 收集批次数据
     */
    private void collectBatch(ConcurrentLinkedQueue<T> queue, AtomicInteger queueSize, List<T> batch) {
        int collected = 0;
        
        while (collected < batchSize && !queue.isEmpty()) {
            T item = queue.poll();
            if (item != null) {
                batch.add(item);
                queueSize.decrementAndGet();
                collected++;
            } else {
                break;
            }
        }
    }
    
    /**
     * 处理批次数据
     */
    private void processBatch(List<T> batch) {
        if (batch.isEmpty()) {
            return;
        }
        
        long startTime = System.nanoTime();
        
        try {
            batchProcessor.accept(batch);
            itemsProcessed.add(batch.size());
            batchesProcessed.increment();
        } catch (Exception e) {
            log.error("批处理失败: batchSize={}", batch.size(), e);
            processingErrors.increment();
        } finally {
            long endTime = System.nanoTime();
            totalProcessingTime.add(endTime - startTime);
        }
    }
    
    /**
     * 刷新所有队列
     */
    private void flushAll() {
        if (shutdown.get()) {
            return;
        }
        
        for (int i = 0; i < shardCount; i++) {
            ConcurrentLinkedQueue<T> queue = shardedQueues[i];
            AtomicInteger queueSize = queueSizes[i];
            
            if (!queue.isEmpty()) {
                List<T> batch = new ArrayList<>();
                collectBatch(queue, queueSize, batch);
                
                if (!batch.isEmpty()) {
                    processBatch(batch);
                }
            }
        }
    }
    
    /**
     * 获取队列总大小
     */
    public int getTotalQueueSize() {
        int total = 0;
        for (AtomicInteger queueSize : queueSizes) {
            total += queueSize.get();
        }
        return total;
    }
    
    /**
     * 获取性能统计信息
     */
    public BatchProcessorStats getStats() {
        long processed = itemsProcessed.sum();
        long batches = batchesProcessed.sum();
        long errors = processingErrors.sum();
        long overflows = queueOverflows.sum();
        long totalTime = totalProcessingTime.sum();
        
        double avgBatchSize = batches > 0 ? (double) processed / batches : 0;
        double avgProcessingTime = batches > 0 ? (double) totalTime / batches / 1_000_000 : 0; // ms
        double errorRate = processed > 0 ? (double) errors / processed * 100 : 0;
        
        return new BatchProcessorStats(
            processed, batches, errors, overflows,
            getTotalQueueSize(), avgBatchSize, avgProcessingTime, errorRate
        );
    }
    
    /**
     * 批处理器统计信息
     */
    public static class BatchProcessorStats {
        public final long itemsProcessed;
        public final long batchesProcessed;
        public final long processingErrors;
        public final long queueOverflows;
        public final int currentQueueSize;
        public final double avgBatchSize;
        public final double avgProcessingTimeMs;
        public final double errorRate;
        
        public BatchProcessorStats(long itemsProcessed, long batchesProcessed,
                                 long processingErrors, long queueOverflows,
                                 int currentQueueSize, double avgBatchSize,
                                 double avgProcessingTimeMs, double errorRate) {
            this.itemsProcessed = itemsProcessed;
            this.batchesProcessed = batchesProcessed;
            this.processingErrors = processingErrors;
            this.queueOverflows = queueOverflows;
            this.currentQueueSize = currentQueueSize;
            this.avgBatchSize = avgBatchSize;
            this.avgProcessingTimeMs = avgProcessingTimeMs;
            this.errorRate = errorRate;
        }
        
        @Override
        public String toString() {
            return String.format(
                "BatchProcessorStats{items=%d, batches=%d, errors=%d(%.2f%%), " +
                "overflows=%d, queueSize=%d, avgBatchSize=%.1f, avgTime=%.2fms}",
                itemsProcessed, batchesProcessed, processingErrors, errorRate,
                queueOverflows, currentQueueSize, avgBatchSize, avgProcessingTimeMs
            );
        }
    }
    
    @PreDestroy
    public void shutdown() {
        if (shutdown.compareAndSet(false, true)) {
            running.set(false);
            
            // 处理剩余数据
            flushAll();
            
            // 关闭线程池
            if (workerExecutor != null) {
                workerExecutor.shutdown();
                try {
                    if (!workerExecutor.awaitTermination(10, TimeUnit.SECONDS)) {
                        workerExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    workerExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
            
            if (flushExecutor != null) {
                flushExecutor.shutdown();
                try {
                    if (!flushExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                        flushExecutor.shutdownNow();
                    }
                } catch (InterruptedException e) {
                    flushExecutor.shutdownNow();
                    Thread.currentThread().interrupt();
                }
            }
            
            log.info("超高性能批处理器已关闭");
        }
    }
}
