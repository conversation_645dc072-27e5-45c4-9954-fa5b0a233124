package com.trading.common.database;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 高性能数据库连接池优化器
 * 提供数据库连接池的高级优化功能
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class HighPerformanceConnectionPoolOptimizer {
    
    private static final Logger log = LoggerFactory.getLogger(HighPerformanceConnectionPoolOptimizer.class);
    
    @Value("${spring.datasource.hikari.maximum-pool-size:20}")
    private int maxPoolSize;
    
    @Value("${spring.datasource.hikari.minimum-idle:5}")
    private int minIdle;
    
    @Value("${spring.datasource.hikari.connection-timeout:30000}")
    private long connectionTimeout;
    
    @Value("${spring.datasource.hikari.idle-timeout:600000}")
    private long idleTimeout;
    
    @Value("${spring.datasource.hikari.max-lifetime:1800000}")
    private long maxLifetime;
    
    // 连接池监控指标
    private final AtomicInteger activeConnections = new AtomicInteger(0);
    private final AtomicInteger idleConnections = new AtomicInteger(0);
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong connectionWaitTime = new AtomicLong(0);
    
    // 性能监控执行器
    private final ScheduledExecutorService monitoringExecutor = 
        Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "connection-pool-monitor");
            t.setDaemon(true);
            return t;
        });
    
    /**
     * 创建优化的HikariCP数据源
     */
    public HikariDataSource createOptimizedDataSource(String jdbcUrl, String username, String password) {
        HikariConfig config = new HikariConfig();
        
        // 基本连接配置
        config.setJdbcUrl(jdbcUrl);
        config.setUsername(username);
        config.setPassword(password);
        config.setDriverClassName("com.mysql.cj.jdbc.Driver");
        
        // 连接池大小优化
        config.setMaximumPoolSize(maxPoolSize);
        config.setMinimumIdle(minIdle);
        
        // 超时配置优化
        config.setConnectionTimeout(connectionTimeout);
        config.setIdleTimeout(idleTimeout);
        config.setMaxLifetime(maxLifetime);
        config.setLeakDetectionThreshold(60000); // 1分钟泄漏检测
        
        // 连接验证优化
        config.setConnectionTestQuery("SELECT 1");
        config.setValidationTimeout(5000);
        
        // 性能优化配置
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        config.addDataSourceProperty("useServerPrepStmts", "true");
        config.addDataSourceProperty("useLocalSessionState", "true");
        config.addDataSourceProperty("rewriteBatchedStatements", "true");
        config.addDataSourceProperty("cacheResultSetMetadata", "true");
        config.addDataSourceProperty("cacheServerConfiguration", "true");
        config.addDataSourceProperty("elideSetAutoCommits", "true");
        config.addDataSourceProperty("maintainTimeStats", "false");
        
        // 字符集和时区配置
        config.addDataSourceProperty("characterEncoding", "utf8mb4");
        config.addDataSourceProperty("useUnicode", "true");
        config.addDataSourceProperty("serverTimezone", "UTC");
        
        // SSL配置
        config.addDataSourceProperty("useSSL", "false");
        config.addDataSourceProperty("allowPublicKeyRetrieval", "true");
        
        // 连接池名称
        config.setPoolName("CryptoTradingPool");
        
        // 注册JMX监控
        config.setRegisterMbeans(true);
        
        HikariDataSource dataSource = new HikariDataSource(config);
        
        // 启动监控
        startMonitoring(dataSource);
        
        log.info("高性能数据库连接池创建完成: maxPoolSize={}, minIdle={}", maxPoolSize, minIdle);
        
        return dataSource;
    }
    
    /**
     * 启动连接池监控
     */
    private void startMonitoring(HikariDataSource dataSource) {
        monitoringExecutor.scheduleAtFixedRate(() -> {
            try {
                monitorConnectionPool(dataSource);
            } catch (Exception e) {
                log.warn("连接池监控失败: {}", e.getMessage());
            }
        }, 30, 30, TimeUnit.SECONDS);
    }
    
    /**
     * 监控连接池状态
     */
    private void monitorConnectionPool(HikariDataSource dataSource) {
        try {
            var poolMXBean = dataSource.getHikariPoolMXBean();
            
            int active = poolMXBean.getActiveConnections();
            int idle = poolMXBean.getIdleConnections();
            int total = poolMXBean.getTotalConnections();
            int waiting = poolMXBean.getThreadsAwaitingConnection();
            
            activeConnections.set(active);
            idleConnections.set(idle);
            totalConnections.set(total);
            
            // 计算连接池使用率
            double utilizationRate = (double) active / maxPoolSize * 100;
            
            log.debug("连接池状态: 活跃={}, 空闲={}, 总计={}, 等待={}, 使用率={:.1f}%", 
                active, idle, total, waiting, utilizationRate);
            
            // 性能告警
            if (utilizationRate > 80) {
                log.warn("连接池使用率过高: {:.1f}%，建议增加连接池大小", utilizationRate);
            }
            
            if (waiting > 0) {
                log.warn("有{}个线程正在等待数据库连接", waiting);
            }
            
        } catch (Exception e) {
            log.debug("连接池监控异常: {}", e.getMessage());
        }
    }
    
    /**
     * 连接池预热
     */
    public void warmupConnectionPool(DataSource dataSource) {
        log.info("开始连接池预热...");
        
        CompletableFuture<Void>[] warmupTasks = new CompletableFuture[minIdle];
        
        for (int i = 0; i < minIdle; i++) {
            warmupTasks[i] = CompletableFuture.runAsync(() -> {
                try (Connection conn = dataSource.getConnection();
                     PreparedStatement stmt = conn.prepareStatement("SELECT 1");
                     ResultSet rs = stmt.executeQuery()) {
                    
                    if (rs.next()) {
                        log.debug("连接预热成功");
                    }
                    
                } catch (Exception e) {
                    log.warn("连接预热失败: {}", e.getMessage());
                }
            });
        }
        
        try {
            CompletableFuture.allOf(warmupTasks).get(30, TimeUnit.SECONDS);
            log.info("连接池预热完成，预热连接数: {}", minIdle);
        } catch (Exception e) {
            log.warn("连接池预热超时: {}", e.getMessage());
        }
    }
    
    /**
     * 连接池健康检查
     */
    public boolean healthCheck(DataSource dataSource) {
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement("SELECT 1");
             ResultSet rs = stmt.executeQuery()) {
            
            return rs.next() && rs.getInt(1) == 1;
            
        } catch (Exception e) {
            log.error("连接池健康检查失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 获取连接池统计信息
     */
    public ConnectionPoolStats getConnectionPoolStats() {
        return new ConnectionPoolStats(
            activeConnections.get(),
            idleConnections.get(),
            (int) totalConnections.get(),
            maxPoolSize,
            connectionWaitTime.get()
        );
    }
    
    /**
     * 连接池统计信息
     */
    public static class ConnectionPoolStats {
        private final int activeConnections;
        private final int idleConnections;
        private final int totalConnections;
        private final int maxPoolSize;
        private final long averageWaitTime;
        
        public ConnectionPoolStats(int activeConnections, int idleConnections, 
                                 int totalConnections, int maxPoolSize, long averageWaitTime) {
            this.activeConnections = activeConnections;
            this.idleConnections = idleConnections;
            this.totalConnections = totalConnections;
            this.maxPoolSize = maxPoolSize;
            this.averageWaitTime = averageWaitTime;
        }
        
        public int getActiveConnections() { return activeConnections; }
        public int getIdleConnections() { return idleConnections; }
        public int getTotalConnections() { return totalConnections; }
        public int getMaxPoolSize() { return maxPoolSize; }
        public long getAverageWaitTime() { return averageWaitTime; }
        
        public double getUtilizationRate() {
            return (double) activeConnections / maxPoolSize * 100;
        }
        
        @Override
        public String toString() {
            return String.format("ConnectionPoolStats{active=%d, idle=%d, total=%d, max=%d, utilization=%.1f%%, waitTime=%dms}",
                activeConnections, idleConnections, totalConnections, maxPoolSize, getUtilizationRate(), averageWaitTime);
        }
    }
    
    /**
     * 优化数据库连接参数
     */
    public void optimizeDatabaseConnection(Connection connection) {
        try {
            // 设置事务隔离级别
            connection.setTransactionIsolation(Connection.TRANSACTION_READ_COMMITTED);
            
            // 设置自动提交
            connection.setAutoCommit(true);
            
            // 设置只读模式（如果适用）
            // connection.setReadOnly(true);
            
            log.debug("数据库连接参数优化完成");
            
        } catch (Exception e) {
            log.warn("数据库连接参数优化失败: {}", e.getMessage());
        }
    }
    
    /**
     * 批量操作优化
     */
    public void executeBatchOptimized(Connection connection, String sql, 
                                    java.util.List<Object[]> parameters) throws Exception {
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            
            // 设置批处理大小
            int batchSize = 1000;
            int count = 0;
            
            for (Object[] params : parameters) {
                for (int i = 0; i < params.length; i++) {
                    stmt.setObject(i + 1, params[i]);
                }
                stmt.addBatch();
                count++;
                
                // 执行批处理
                if (count % batchSize == 0) {
                    stmt.executeBatch();
                    stmt.clearBatch();
                }
            }
            
            // 执行剩余的批处理
            if (count % batchSize != 0) {
                stmt.executeBatch();
            }
            
            log.debug("批量操作完成，处理记录数: {}", parameters.size());
        }
    }
    
    /**
     * 关闭监控
     */
    public void shutdown() {
        if (monitoringExecutor != null && !monitoringExecutor.isShutdown()) {
            monitoringExecutor.shutdown();
            try {
                if (!monitoringExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    monitoringExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                monitoringExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}
