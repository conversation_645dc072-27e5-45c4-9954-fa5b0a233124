package com.trading.common.gc;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.lang.management.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.List;
import java.util.ArrayList;

/**
 * GC优化管理器
 * 监控和优化垃圾回收性能，减少GC压力
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class GCOptimizationManager {

    private static final Logger log = LoggerFactory.getLogger(GCOptimizationManager.class);

    // GC监控配置 - 优化阈值，减少误报
    @Value("${app.performance.gc.monitoring.enabled:true}")
    private boolean gcMonitoringEnabled;

    @Value("${app.performance.gc.monitoring.interval-seconds:60}")
    private int monitoringIntervalSeconds;

    @Value("${app.performance.gc.young-gc-threshold:30}")
    private int youngGcThresholdPerMinute;

    @Value("${app.performance.gc.full-gc-threshold:5}")
    private int fullGcThresholdPerMinute;

    // 内存预分配配置 - 禁用预分配，减少内存压力
    @Value("${app.performance.memory.preallocation.enabled:false}")
    private boolean memoryPreallocationEnabled;

    @Value("${app.performance.memory.preallocation.size-mb:10}")
    private int preallocationSizeMB;

    // GC监控
    private ScheduledExecutorService gcMonitorExecutor;
    private final Map<String, GarbageCollectorMXBean> gcBeans = new ConcurrentHashMap<>();
    private final Map<String, Long> lastGcCollectionCount = new ConcurrentHashMap<>();
    private final Map<String, Long> lastGcCollectionTime = new ConcurrentHashMap<>();
    
    // 内存管理
    private MemoryMXBean memoryBean;
    private final List<MemoryPoolMXBean> memoryPoolBeans = new ArrayList<>();
    
    // 预分配内存区域
    private volatile byte[] preAllocatedMemory;
    private final AtomicLong preAllocatedSize = new AtomicLong(0);
    
    // 性能统计
    private final LongAdder totalYoungGcCount = new LongAdder();
    private final LongAdder totalFullGcCount = new LongAdder();
    private final LongAdder totalGcTime = new LongAdder();
    private final AtomicLong maxHeapUsed = new AtomicLong(0);
    private final AtomicLong maxNonHeapUsed = new AtomicLong(0);

    // GC优化控制 - 防止过度优化
    private volatile long lastOptimizationTime = 0;
    private static final long MIN_OPTIMIZATION_INTERVAL_MS = 300000; // 5分钟
    private final AtomicLong optimizationCount = new AtomicLong(0);

    @PostConstruct
    public void initialize() {
        log.info("初始化GC优化管理器...");
        
        // 初始化内存监控
        initializeMemoryMonitoring();
        
        // 初始化GC监控
        initializeGCMonitoring();
        
        // 初始化内存预分配
        initializeMemoryPreallocation();
        
        // 启动监控
        startMonitoring();
        
        log.info("GC优化管理器初始化完成 - 监控间隔: {}秒, 预分配: {}MB", 
                monitoringIntervalSeconds, preallocationSizeMB);
    }

    /**
     * 初始化内存监控
     */
    private void initializeMemoryMonitoring() {
        memoryBean = ManagementFactory.getMemoryMXBean();
        memoryPoolBeans.addAll(ManagementFactory.getMemoryPoolMXBeans());
        
        log.debug("内存监控初始化完成 - 内存池数量: {}", memoryPoolBeans.size());
    }

    /**
     * 初始化GC监控
     */
    private void initializeGCMonitoring() {
        List<GarbageCollectorMXBean> gcMXBeans = ManagementFactory.getGarbageCollectorMXBeans();
        
        for (GarbageCollectorMXBean gcBean : gcMXBeans) {
            String gcName = gcBean.getName();
            gcBeans.put(gcName, gcBean);
            lastGcCollectionCount.put(gcName, gcBean.getCollectionCount());
            lastGcCollectionTime.put(gcName, gcBean.getCollectionTime());
            
            log.debug("注册GC监控: {}", gcName);
        }
        
        log.debug("GC监控初始化完成 - GC收集器数量: {}", gcBeans.size());
    }

    /**
     * 初始化内存预分配
     */
    private void initializeMemoryPreallocation() {
        if (memoryPreallocationEnabled && preallocationSizeMB > 0) {
            try {
                int sizeBytes = preallocationSizeMB * 1024 * 1024;
                preAllocatedMemory = new byte[sizeBytes];
                preAllocatedSize.set(sizeBytes);
                
                log.info("内存预分配完成: {}MB", preallocationSizeMB);
                
                // 立即释放预分配内存，让GC知道这块内存的存在
                preAllocatedMemory = null;
                // 移除强制GC调用，让JVM自行决定GC时机
                
            } catch (OutOfMemoryError e) {
                log.warn("内存预分配失败，内存不足: {}MB", preallocationSizeMB);
                preAllocatedSize.set(0);
            }
        }
    }

    /**
     * 启动监控
     */
    private void startMonitoring() {
        if (gcMonitoringEnabled) {
            gcMonitorExecutor = Executors.newScheduledThreadPool(1, r -> {
                Thread thread = new Thread(r, "gc-optimization-monitor");
                thread.setDaemon(true);
                return thread;
            });
            
            gcMonitorExecutor.scheduleAtFixedRate(
                    this::performGCMonitoring,
                    monitoringIntervalSeconds,
                    monitoringIntervalSeconds,
                    TimeUnit.SECONDS
            );
            
            log.debug("GC监控已启动");
        }
    }

    /**
     * 执行GC监控
     */
    private void performGCMonitoring() {
        try {
            // 监控内存使用
            monitorMemoryUsage();
            
            // 监控GC活动
            monitorGCActivity();
            
            // 检查GC性能
            checkGCPerformance();
            
        } catch (Exception e) {
            log.error("GC监控执行失败", e);
        }
    }

    /**
     * 监控内存使用
     */
    private void monitorMemoryUsage() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        long heapUsed = heapUsage.getUsed();
        long nonHeapUsed = nonHeapUsage.getUsed();
        
        // 更新最大使用量
        maxHeapUsed.updateAndGet(current -> Math.max(current, heapUsed));
        maxNonHeapUsed.updateAndGet(current -> Math.max(current, nonHeapUsed));
        
        // 计算内存使用率
        double heapUsagePercent = (double) heapUsed / heapUsage.getMax() * 100;
        double nonHeapUsagePercent = (double) nonHeapUsed / nonHeapUsage.getMax() * 100;
        
        log.debug("内存使用 - 堆: {:.1f}% ({} MB), 非堆: {:.1f}% ({} MB)",
                heapUsagePercent, heapUsed / 1024 / 1024,
                nonHeapUsagePercent, nonHeapUsed / 1024 / 1024);
        
        // 检查内存压力 - 提高阈值，减少误报
        if (heapUsagePercent > 90) {
            log.warn("堆内存使用率过高: {:.1f}%", heapUsagePercent);
            // 移除自动GC建议，让JVM自行管理
        }
    }

    /**
     * 监控GC活动
     */
    private void monitorGCActivity() {
        for (Map.Entry<String, GarbageCollectorMXBean> entry : gcBeans.entrySet()) {
            String gcName = entry.getKey();
            GarbageCollectorMXBean gcBean = entry.getValue();
            
            long currentCount = gcBean.getCollectionCount();
            long currentTime = gcBean.getCollectionTime();
            
            Long lastCount = lastGcCollectionCount.get(gcName);
            Long lastTime = lastGcCollectionTime.get(gcName);
            
            if (lastCount != null && lastTime != null) {
                long countDiff = currentCount - lastCount;
                long timeDiff = currentTime - lastTime;
                
                if (countDiff > 0) {
                    // 分类GC类型
                    if (isYoungGC(gcName)) {
                        totalYoungGcCount.add(countDiff);
                    } else if (isFullGC(gcName)) {
                        totalFullGcCount.add(countDiff);
                    }
                    
                    totalGcTime.add(timeDiff);
                    
                    log.debug("GC活动 - {}: 次数={}, 时间={}ms", gcName, countDiff, timeDiff);
                }
            }
            
            lastGcCollectionCount.put(gcName, currentCount);
            lastGcCollectionTime.put(gcName, currentTime);
        }
    }

    /**
     * 检查GC性能 - 优化检查逻辑，减少误报
     */
    private void checkGCPerformance() {
        long currentTime = System.currentTimeMillis();

        // 防止过度优化
        if (currentTime - lastOptimizationTime < MIN_OPTIMIZATION_INTERVAL_MS) {
            return;
        }

        long intervalMs = monitoringIntervalSeconds * 1000L;

        // 计算每分钟的GC次数 - 使用滑动窗口平均
        double youngGcPerMinute = (double) totalYoungGcCount.sum() * 60000 / intervalMs;
        double fullGcPerMinute = (double) totalFullGcCount.sum() * 60000 / intervalMs;

        log.debug("GC性能 - Young GC: {:.1f}/分钟, Full GC: {:.1f}/分钟",
                youngGcPerMinute, fullGcPerMinute);

        // 检查是否超过阈值 - 只记录警告，不执行激进优化
        if (youngGcPerMinute > youngGcThresholdPerMinute) {
            log.warn("Young GC频率过高: {:.1f}/分钟 (阈值: {})",
                    youngGcPerMinute, youngGcThresholdPerMinute);
            // 移除自动优化，只记录统计
        }

        if (fullGcPerMinute > fullGcThresholdPerMinute) {
            log.warn("Full GC频率过高: {:.1f}/分钟 (阈值: {})",
                    fullGcPerMinute, fullGcThresholdPerMinute);
            // 移除自动优化，只记录统计
        }
    }

    /**
     * 判断是否为Young GC
     */
    private boolean isYoungGC(String gcName) {
        return gcName.contains("PS Scavenge") || 
               gcName.contains("ParNew") || 
               gcName.contains("G1 Young") ||
               gcName.contains("Copy") ||
               gcName.contains("Serial Young");
    }

    /**
     * 判断是否为Full GC
     */
    private boolean isFullGC(String gcName) {
        return gcName.contains("PS MarkSweep") || 
               gcName.contains("ConcurrentMarkSweep") || 
               gcName.contains("G1 Old") ||
               gcName.contains("MarkSweepCompact") ||
               gcName.contains("Serial Old");
    }

    /**
     * 建议执行GC - 改为被动监控，不主动触发
     */
    public void suggestGC() {
        log.debug("检测到内存压力，建议关注GC状态");
        // 移除System.gc()调用，让JVM自行决定GC时机
    }

    /**
     * 执行高级GC优化策略
     */
    public void executeAdvancedGCOptimization() {
        log.info("执行高级GC优化策略...");

        try {
            // 记录优化前状态
            MemoryUsage heapBefore = memoryBean.getHeapMemoryUsage();
            long usedBefore = heapBefore.getUsed();

            // 1. 智能内存清理策略
            executeIntelligentMemoryCleanup();

            // 2. 分代GC优化
            executeGenerationalGCOptimization();

            // 3. 大对象处理优化
            executeLargeObjectOptimization();

            // 4. 建议进行垃圾回收
            System.gc();

            // 5. 等待GC完成
            Thread.sleep(200);

            // 6. 验证优化效果
            MemoryUsage heapAfter = memoryBean.getHeapMemoryUsage();
            long usedAfter = heapAfter.getUsed();
            long maxMemory = heapAfter.getMax();
            double usagePercent = (double) usedAfter / maxMemory * 100;
            long memoryFreed = usedBefore - usedAfter;

            log.info("高级GC优化完成 - 内存使用率: {:.1f}%, 释放内存: {}MB",
                usagePercent, memoryFreed / 1024 / 1024);

            // 7. 更新优化统计
            updateOptimizationStats(memoryFreed);

        } catch (Exception e) {
            log.error("高级GC优化失败", e);
        }
    }

    /**
     * 智能内存清理策略
     */
    private void executeIntelligentMemoryCleanup() {
        // 1. 清理弱引用和软引用 - 移除已废弃的 runFinalization
        // System.runFinalization() 已在 Java 18+ 中被标记为废弃

        // 2. 清理线程本地变量
        cleanupThreadLocalVariables();

        // 3. 清理临时缓存
        cleanupTemporaryCaches();

        // 4. 清理过期对象
        cleanupExpiredObjects();
    }

    /**
     * 分代GC优化
     */
    private void executeGenerationalGCOptimization() {
        try {
            // 获取年轻代内存使用情况
            MemoryPoolMXBean youngGenPool = getYoungGenerationPool();
            if (youngGenPool != null) {
                MemoryUsage youngGenUsage = youngGenPool.getUsage();
                double youngGenUsagePercent = (double) youngGenUsage.getUsed() / youngGenUsage.getMax() * 100;

                if (youngGenUsagePercent > 80) {
                    log.debug("年轻代内存使用率过高: {:.1f}%, 触发Minor GC优化", youngGenUsagePercent);
                    // 触发Minor GC
                    System.gc();
                }
            }

            // 获取老年代内存使用情况
            MemoryPoolMXBean oldGenPool = getOldGenerationPool();
            if (oldGenPool != null) {
                MemoryUsage oldGenUsage = oldGenPool.getUsage();
                double oldGenUsagePercent = (double) oldGenUsage.getUsed() / oldGenUsage.getMax() * 100;

                if (oldGenUsagePercent > 70) {
                    log.debug("老年代内存使用率过高: {:.1f}%, 执行深度清理", oldGenUsagePercent);
                    executeDeepCleanup();
                }
            }

        } catch (Exception e) {
            log.warn("分代GC优化失败", e);
        }
    }

    /**
     * 大对象处理优化
     */
    private void executeLargeObjectOptimization() {
        try {
            // 检查是否有大对象池
            MemoryPoolMXBean largeObjectPool = getLargeObjectPool();
            if (largeObjectPool != null) {
                MemoryUsage usage = largeObjectPool.getUsage();
                double usagePercent = (double) usage.getUsed() / usage.getMax() * 100;

                if (usagePercent > 60) {
                    log.debug("大对象空间使用率过高: {:.1f}%, 执行大对象清理", usagePercent);
                    cleanupLargeObjects();
                }
            }
        } catch (Exception e) {
            log.warn("大对象处理优化失败", e);
        }
    }

    /**
     * 清理线程本地变量
     */
    private void cleanupThreadLocalVariables() {
        try {
            // 获取当前线程组中的所有线程
            ThreadGroup rootGroup = Thread.currentThread().getThreadGroup();
            ThreadGroup parentGroup;
            while ((parentGroup = rootGroup.getParent()) != null) {
                rootGroup = parentGroup;
            }

            Thread[] threads = new Thread[rootGroup.activeCount()];
            int count = rootGroup.enumerate(threads);

            // 清理已结束线程的ThreadLocal变量
            for (int i = 0; i < count; i++) {
                Thread thread = threads[i];
                if (thread != null && !thread.isAlive()) {
                    // 线程已结束，其ThreadLocal变量应该被清理
                    log.trace("清理已结束线程的ThreadLocal变量: {}", thread.getName());
                }
            }
        } catch (Exception e) {
            log.debug("清理线程本地变量失败", e);
        }
    }

    /**
     * 清理临时缓存
     */
    private void cleanupTemporaryCaches() {
        // 这里可以添加应用特定的临时缓存清理逻辑
        log.trace("执行临时缓存清理");
    }

    /**
     * 清理过期对象
     */
    private void cleanupExpiredObjects() {
        // 这里可以添加应用特定的过期对象清理逻辑
        log.trace("执行过期对象清理");
    }

    /**
     * 执行深度清理
     */
    private void executeDeepCleanup() {
        // 深度清理策略 - 移除已废弃的 runFinalization
        // System.runFinalization() 已在 Java 18+ 中被标记为废弃
        System.gc();

        try {
            Thread.sleep(50);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 清理大对象
     */
    private void cleanupLargeObjects() {
        // 大对象清理策略
        log.trace("执行大对象清理");
    }

    /**
     * 获取年轻代内存池
     */
    private MemoryPoolMXBean getYoungGenerationPool() {
        for (MemoryPoolMXBean pool : ManagementFactory.getMemoryPoolMXBeans()) {
            String name = pool.getName().toLowerCase();
            if (name.contains("young") || name.contains("eden") || name.contains("survivor")) {
                return pool;
            }
        }
        return null;
    }

    /**
     * 获取老年代内存池
     */
    private MemoryPoolMXBean getOldGenerationPool() {
        for (MemoryPoolMXBean pool : ManagementFactory.getMemoryPoolMXBeans()) {
            String name = pool.getName().toLowerCase();
            if (name.contains("old") || name.contains("tenured")) {
                return pool;
            }
        }
        return null;
    }

    /**
     * 获取大对象内存池
     */
    private MemoryPoolMXBean getLargeObjectPool() {
        for (MemoryPoolMXBean pool : ManagementFactory.getMemoryPoolMXBeans()) {
            String name = pool.getName().toLowerCase();
            if (name.contains("large") || name.contains("humongous")) {
                return pool;
            }
        }
        return null;
    }

    /**
     * 更新优化统计
     */
    private void updateOptimizationStats(long memoryFreed) {
        // 更新优化统计信息
        log.debug("GC优化统计更新: 释放内存={}MB", memoryFreed / 1024 / 1024);
    }

    /**
     * 优化Young GC
     */
    private void optimizeYoungGC() {
        log.info("执行Young GC优化策略...");
        
        // 清理可能的内存泄漏
        clearTemporaryObjects();
        
        // 建议执行一次GC
        suggestGC();
    }

    /**
     * 优化Full GC
     */
    private void optimizeFullGC() {
        log.warn("执行Full GC优化策略...");
        
        // 清理大对象
        clearLargeObjects();
        
        // 释放预分配内存
        releasePreallocatedMemory();
        
        // 建议执行一次GC
        suggestGC();
    }

    /**
     * 清理临时对象
     */
    private void clearTemporaryObjects() {
        // 这里可以添加清理临时对象的逻辑
        log.debug("清理临时对象");
    }

    /**
     * 清理大对象
     */
    private void clearLargeObjects() {
        // 这里可以添加清理大对象的逻辑
        log.debug("清理大对象");
    }

    /**
     * 释放预分配内存
     */
    private void releasePreallocatedMemory() {
        if (preAllocatedMemory != null) {
            preAllocatedMemory = null;
            preAllocatedSize.set(0);
            log.info("释放预分配内存: {}MB", preallocationSizeMB);
        }
    }

    /**
     * 获取GC统计信息
     */
    public GCStatistics getStatistics() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        return new GCStatistics(
                totalYoungGcCount.sum(),
                totalFullGcCount.sum(),
                totalGcTime.sum(),
                heapUsage.getUsed(),
                heapUsage.getMax(),
                nonHeapUsage.getUsed(),
                nonHeapUsage.getMax(),
                maxHeapUsed.get(),
                maxNonHeapUsed.get(),
                preAllocatedSize.get()
        );
    }

    @PreDestroy
    public void shutdown() {
        log.info("关闭GC优化管理器...");
        
        if (gcMonitorExecutor != null && !gcMonitorExecutor.isShutdown()) {
            gcMonitorExecutor.shutdown();
            try {
                if (!gcMonitorExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    gcMonitorExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                gcMonitorExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 释放预分配内存
        releasePreallocatedMemory();
        
        log.info("GC优化管理器已关闭");
    }

    /**
     * GC统计信息
     */
    public static class GCStatistics {
        private final long totalYoungGcCount;
        private final long totalFullGcCount;
        private final long totalGcTime;
        private final long currentHeapUsed;
        private final long maxHeapSize;
        private final long currentNonHeapUsed;
        private final long maxNonHeapSize;
        private final long maxHeapUsed;
        private final long maxNonHeapUsed;
        private final long preAllocatedSize;

        public GCStatistics(long totalYoungGcCount, long totalFullGcCount, long totalGcTime,
                           long currentHeapUsed, long maxHeapSize, long currentNonHeapUsed,
                           long maxNonHeapSize, long maxHeapUsed, long maxNonHeapUsed,
                           long preAllocatedSize) {
            this.totalYoungGcCount = totalYoungGcCount;
            this.totalFullGcCount = totalFullGcCount;
            this.totalGcTime = totalGcTime;
            this.currentHeapUsed = currentHeapUsed;
            this.maxHeapSize = maxHeapSize;
            this.currentNonHeapUsed = currentNonHeapUsed;
            this.maxNonHeapSize = maxNonHeapSize;
            this.maxHeapUsed = maxHeapUsed;
            this.maxNonHeapUsed = maxNonHeapUsed;
            this.preAllocatedSize = preAllocatedSize;
        }

        // Getters
        public long getTotalYoungGcCount() { return totalYoungGcCount; }
        public long getTotalFullGcCount() { return totalFullGcCount; }
        public long getTotalGcTime() { return totalGcTime; }
        public long getCurrentHeapUsed() { return currentHeapUsed; }
        public long getMaxHeapSize() { return maxHeapSize; }
        public long getCurrentNonHeapUsed() { return currentNonHeapUsed; }
        public long getMaxNonHeapSize() { return maxNonHeapSize; }
        public long getMaxHeapUsed() { return maxHeapUsed; }
        public long getMaxNonHeapUsed() { return maxNonHeapUsed; }
        public long getPreAllocatedSize() { return preAllocatedSize; }

        public double getHeapUsagePercent() {
            return maxHeapSize == 0 ? 0.0 : (double) currentHeapUsed / maxHeapSize * 100;
        }

        public double getNonHeapUsagePercent() {
            return maxNonHeapSize == 0 ? 0.0 : (double) currentNonHeapUsed / maxNonHeapSize * 100;
        }

        @Override
        public String toString() {
            return String.format("GCStats: youngGC=%d, fullGC=%d, gcTime=%dms, " +
                            "heap=%.1f%% (%dMB/%dMB), nonHeap=%.1f%% (%dMB/%dMB), preAlloc=%dMB",
                    totalYoungGcCount, totalFullGcCount, totalGcTime,
                    getHeapUsagePercent(), currentHeapUsed / 1024 / 1024, maxHeapSize / 1024 / 1024,
                    getNonHeapUsagePercent(), currentNonHeapUsed / 1024 / 1024, maxNonHeapSize / 1024 / 1024,
                    preAllocatedSize / 1024 / 1024);
        }
    }
}
