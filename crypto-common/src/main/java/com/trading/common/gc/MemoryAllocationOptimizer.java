package com.trading.common.gc;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;
import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.lang.ref.SoftReference;
import java.util.List;
import java.util.ArrayList;

/**
 * 内存分配优化器
 * 减少临时对象创建，优化内存分配模式
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class MemoryAllocationOptimizer {

    private static final Logger log = LoggerFactory.getLogger(MemoryAllocationOptimizer.class);

    // 字符串构建器池
    private final Queue<StringBuilder> stringBuilderPool = new ConcurrentLinkedQueue<>();
    private static final int MAX_STRING_BUILDER_POOL_SIZE = 100;
    private static final int STRING_BUILDER_INITIAL_CAPACITY = 256;

    // 字节数组池
    private final Map<Integer, Queue<SoftReference<byte[]>>> byteArrayPools = new ConcurrentHashMap<>();
    private static final int MAX_BYTE_ARRAY_POOL_SIZE = 50;
    
    // 集合对象池
    private final Queue<List<Object>> listPool = new ConcurrentLinkedQueue<>();
    private final Queue<Map<String, Object>> mapPool = new ConcurrentLinkedQueue<>();
    private static final int MAX_COLLECTION_POOL_SIZE = 50;

    // 性能统计
    private final LongAdder stringBuilderReused = new LongAdder();
    private final LongAdder stringBuilderCreated = new LongAdder();
    private final LongAdder byteArrayReused = new LongAdder();
    private final LongAdder byteArrayCreated = new LongAdder();
    private final LongAdder collectionReused = new LongAdder();
    private final LongAdder collectionCreated = new LongAdder();
    private final AtomicLong totalMemorySaved = new AtomicLong(0);

    @PostConstruct
    public void initialize() {
        log.info("初始化内存分配优化器...");
        
        // 预热对象池
        warmupPools();
        
        log.info("内存分配优化器初始化完成");
    }

    /**
     * 预热对象池
     */
    private void warmupPools() {
        // 预热StringBuilder池
        for (int i = 0; i < 20; i++) {
            stringBuilderPool.offer(new StringBuilder(STRING_BUILDER_INITIAL_CAPACITY));
        }
        
        // 预热集合池
        for (int i = 0; i < 10; i++) {
            listPool.offer(new ArrayList<>());
            mapPool.offer(new ConcurrentHashMap<>());
        }
        
        // 预热常用大小的字节数组池
        int[] commonSizes = {1024, 4096, 8192, 16384, 32768};
        for (int size : commonSizes) {
            Queue<SoftReference<byte[]>> pool = new ConcurrentLinkedQueue<>();
            for (int i = 0; i < 5; i++) {
                pool.offer(new SoftReference<>(new byte[size]));
            }
            byteArrayPools.put(size, pool);
        }
        
        log.debug("对象池预热完成");
    }

    /**
     * 借用StringBuilder
     */
    public StringBuilder borrowStringBuilder() {
        StringBuilder sb = stringBuilderPool.poll();
        if (sb != null) {
            sb.setLength(0); // 重置长度
            stringBuilderReused.increment();
            return sb;
        } else {
            stringBuilderCreated.increment();
            return new StringBuilder(STRING_BUILDER_INITIAL_CAPACITY);
        }
    }

    /**
     * 归还StringBuilder
     */
    public void returnStringBuilder(StringBuilder sb) {
        if (sb != null && stringBuilderPool.size() < MAX_STRING_BUILDER_POOL_SIZE) {
            // 如果StringBuilder太大，不放回池中
            if (sb.capacity() <= STRING_BUILDER_INITIAL_CAPACITY * 4) {
                sb.setLength(0); // 清空内容
                stringBuilderPool.offer(sb);
                
                // 估算节省的内存
                totalMemorySaved.addAndGet(sb.capacity() * 2); // 每个字符2字节
            }
        }
    }

    /**
     * 借用字节数组
     */
    public byte[] borrowByteArray(int size) {
        // 找到最接近的池大小
        Integer poolSize = findClosestPoolSize(size);
        if (poolSize != null) {
            Queue<SoftReference<byte[]>> pool = byteArrayPools.get(poolSize);
            if (pool != null) {
                SoftReference<byte[]> ref = pool.poll();
                if (ref != null) {
                    byte[] array = ref.get();
                    if (array != null && array.length >= size) {
                        byteArrayReused.increment();
                        return array;
                    }
                }
            }
        }
        
        byteArrayCreated.increment();
        return new byte[size];
    }

    /**
     * 归还字节数组
     */
    public void returnByteArray(byte[] array) {
        if (array != null) {
            int size = array.length;
            Integer poolSize = findClosestPoolSize(size);
            
            if (poolSize != null && poolSize.equals(size)) {
                Queue<SoftReference<byte[]>> pool = byteArrayPools.computeIfAbsent(
                        poolSize, k -> new ConcurrentLinkedQueue<>());
                
                if (pool.size() < MAX_BYTE_ARRAY_POOL_SIZE) {
                    pool.offer(new SoftReference<>(array));
                    totalMemorySaved.addAndGet(array.length);
                }
            }
        }
    }

    /**
     * 借用List
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> borrowList() {
        List<Object> list = listPool.poll();
        if (list != null) {
            list.clear();
            collectionReused.increment();
            return (List<T>) list;
        } else {
            collectionCreated.increment();
            return new ArrayList<>();
        }
    }

    /**
     * 归还List
     */
    public void returnList(List<?> list) {
        if (list != null && listPool.size() < MAX_COLLECTION_POOL_SIZE) {
            list.clear();
            listPool.offer((List<Object>) list);
            
            // 估算节省的内存（假设每个元素引用8字节）
            totalMemorySaved.addAndGet(list.size() * 8L);
        }
    }

    /**
     * 借用Map
     */
    @SuppressWarnings("unchecked")
    public <K, V> Map<K, V> borrowMap() {
        Map<String, Object> map = mapPool.poll();
        if (map != null) {
            map.clear();
            collectionReused.increment();
            return (Map<K, V>) map;
        } else {
            collectionCreated.increment();
            return new ConcurrentHashMap<>();
        }
    }

    /**
     * 归还Map
     */
    public void returnMap(Map<?, ?> map) {
        if (map != null && mapPool.size() < MAX_COLLECTION_POOL_SIZE) {
            map.clear();
            mapPool.offer((Map<String, Object>) map);
            
            // 估算节省的内存（假设每个键值对16字节）
            totalMemorySaved.addAndGet(map.size() * 16L);
        }
    }

    /**
     * 创建优化的字符串
     */
    public String createOptimizedString(Object... parts) {
        StringBuilder sb = borrowStringBuilder();
        try {
            for (Object part : parts) {
                if (part != null) {
                    sb.append(part);
                }
            }
            return sb.toString();
        } finally {
            returnStringBuilder(sb);
        }
    }

    /**
     * 创建优化的字符串（带分隔符）
     */
    public String createOptimizedString(String separator, Object... parts) {
        StringBuilder sb = borrowStringBuilder();
        try {
            for (int i = 0; i < parts.length; i++) {
                if (i > 0 && separator != null) {
                    sb.append(separator);
                }
                if (parts[i] != null) {
                    sb.append(parts[i]);
                }
            }
            return sb.toString();
        } finally {
            returnStringBuilder(sb);
        }
    }

    /**
     * 批量处理优化
     */
    public <T> void processBatch(List<T> items, BatchProcessor<T> processor) {
        if (items == null || items.isEmpty()) {
            return;
        }
        
        // 使用池化的临时集合
        List<T> batch = borrowList();
        try {
            int batchSize = Math.min(100, items.size()); // 限制批次大小
            
            for (int i = 0; i < items.size(); i += batchSize) {
                batch.clear();
                int endIndex = Math.min(i + batchSize, items.size());
                
                for (int j = i; j < endIndex; j++) {
                    batch.add(items.get(j));
                }
                
                processor.process(batch);
            }
        } finally {
            returnList(batch);
        }
    }

    /**
     * 找到最接近的池大小
     */
    private Integer findClosestPoolSize(int size) {
        Integer closest = null;
        int minDiff = Integer.MAX_VALUE;
        
        for (Integer poolSize : byteArrayPools.keySet()) {
            if (poolSize >= size) {
                int diff = poolSize - size;
                if (diff < minDiff) {
                    minDiff = diff;
                    closest = poolSize;
                }
            }
        }
        
        return closest;
    }

    /**
     * 清理过期的软引用
     */
    public void cleanupExpiredReferences() {
        for (Queue<SoftReference<byte[]>> pool : byteArrayPools.values()) {
            pool.removeIf(ref -> ref.get() == null);
        }
        log.debug("清理过期软引用完成");
    }

    /**
     * 获取内存优化统计信息
     */
    public MemoryOptimizationStatistics getStatistics() {
        return new MemoryOptimizationStatistics(
                stringBuilderReused.sum(),
                stringBuilderCreated.sum(),
                byteArrayReused.sum(),
                byteArrayCreated.sum(),
                collectionReused.sum(),
                collectionCreated.sum(),
                totalMemorySaved.get(),
                stringBuilderPool.size(),
                byteArrayPools.values().stream().mapToInt(Queue::size).sum(),
                listPool.size() + mapPool.size()
        );
    }

    /**
     * 批处理器接口
     */
    @FunctionalInterface
    public interface BatchProcessor<T> {
        void process(List<T> batch);
    }

    /**
     * 内存优化统计信息
     */
    public static class MemoryOptimizationStatistics {
        private final long stringBuilderReused;
        private final long stringBuilderCreated;
        private final long byteArrayReused;
        private final long byteArrayCreated;
        private final long collectionReused;
        private final long collectionCreated;
        private final long totalMemorySaved;
        private final int currentStringBuilderPoolSize;
        private final int currentByteArrayPoolSize;
        private final int currentCollectionPoolSize;

        public MemoryOptimizationStatistics(long stringBuilderReused, long stringBuilderCreated,
                                          long byteArrayReused, long byteArrayCreated,
                                          long collectionReused, long collectionCreated,
                                          long totalMemorySaved, int currentStringBuilderPoolSize,
                                          int currentByteArrayPoolSize, int currentCollectionPoolSize) {
            this.stringBuilderReused = stringBuilderReused;
            this.stringBuilderCreated = stringBuilderCreated;
            this.byteArrayReused = byteArrayReused;
            this.byteArrayCreated = byteArrayCreated;
            this.collectionReused = collectionReused;
            this.collectionCreated = collectionCreated;
            this.totalMemorySaved = totalMemorySaved;
            this.currentStringBuilderPoolSize = currentStringBuilderPoolSize;
            this.currentByteArrayPoolSize = currentByteArrayPoolSize;
            this.currentCollectionPoolSize = currentCollectionPoolSize;
        }

        // Getters
        public long getStringBuilderReused() { return stringBuilderReused; }
        public long getStringBuilderCreated() { return stringBuilderCreated; }
        public long getByteArrayReused() { return byteArrayReused; }
        public long getByteArrayCreated() { return byteArrayCreated; }
        public long getCollectionReused() { return collectionReused; }
        public long getCollectionCreated() { return collectionCreated; }
        public long getTotalMemorySaved() { return totalMemorySaved; }
        public int getCurrentStringBuilderPoolSize() { return currentStringBuilderPoolSize; }
        public int getCurrentByteArrayPoolSize() { return currentByteArrayPoolSize; }
        public int getCurrentCollectionPoolSize() { return currentCollectionPoolSize; }

        public double getStringBuilderReuseRate() {
            long total = stringBuilderReused + stringBuilderCreated;
            return total == 0 ? 0.0 : (double) stringBuilderReused / total * 100;
        }

        public double getByteArrayReuseRate() {
            long total = byteArrayReused + byteArrayCreated;
            return total == 0 ? 0.0 : (double) byteArrayReused / total * 100;
        }

        public double getCollectionReuseRate() {
            long total = collectionReused + collectionCreated;
            return total == 0 ? 0.0 : (double) collectionReused / total * 100;
        }

        @Override
        public String toString() {
            return String.format("MemoryOptimization: sb=%.1f%% (%d/%d), bytes=%.1f%% (%d/%d), " +
                            "collections=%.1f%% (%d/%d), saved=%dKB, pools=[sb=%d,bytes=%d,coll=%d]",
                    getStringBuilderReuseRate(), stringBuilderReused, stringBuilderReused + stringBuilderCreated,
                    getByteArrayReuseRate(), byteArrayReused, byteArrayReused + byteArrayCreated,
                    getCollectionReuseRate(), collectionReused, collectionReused + collectionCreated,
                    totalMemorySaved / 1024,
                    currentStringBuilderPoolSize, currentByteArrayPoolSize, currentCollectionPoolSize);
        }
    }
}
