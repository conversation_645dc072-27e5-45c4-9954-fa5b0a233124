package com.trading.common.network;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.net.*;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.SocketChannel;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 高性能网络I/O优化器
 * 提供网络连接、HTTP客户端、WebSocket等网络组件的性能优化
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class HighPerformanceNetworkOptimizer {
    
    private static final Logger log = LoggerFactory.getLogger(HighPerformanceNetworkOptimizer.class);
    
    // 网络性能监控指标
    private final AtomicInteger activeConnections = new AtomicInteger(0);
    private final AtomicLong totalBytesReceived = new AtomicLong(0);
    private final AtomicLong totalBytesSent = new AtomicLong(0);
    private final AtomicLong connectionCount = new AtomicLong(0);
    
    // 连接池
    private final ConcurrentHashMap<String, ConnectionPool> connectionPools = new ConcurrentHashMap<>();
    
    // NIO选择器
    private volatile Selector selector;
    private final ExecutorService networkExecutor = Executors.newVirtualThreadPerTaskExecutor();
    
    /**
     * 初始化网络优化器
     */
    public void initialize() {
        try {
            // 创建NIO选择器
            selector = Selector.open();
            
            // 优化系统网络参数
            optimizeSystemNetworkParameters();
            
            log.info("高性能网络I/O优化器初始化完成");
            
        } catch (IOException e) {
            log.error("网络优化器初始化失败", e);
        }
    }
    
    /**
     * 优化系统网络参数
     */
    private void optimizeSystemNetworkParameters() {
        try {
            // TCP优化参数
            System.setProperty("java.net.preferIPv4Stack", "true");
            System.setProperty("java.net.useSystemProxies", "false");
            
            // Socket缓冲区优化
            System.setProperty("sun.net.useExclusiveBind", "false");
            
            // DNS缓存优化
            System.setProperty("networkaddress.cache.ttl", "60");
            System.setProperty("networkaddress.cache.negative.ttl", "10");
            
            // 连接超时优化
            System.setProperty("sun.net.client.defaultConnectTimeout", "10000");
            System.setProperty("sun.net.client.defaultReadTimeout", "30000");
            
            log.info("系统网络参数优化完成");
            
        } catch (Exception e) {
            log.warn("系统网络参数优化失败: {}", e.getMessage());
        }
    }
    
    /**
     * 创建优化的Socket连接
     */
    public Socket createOptimizedSocket(String host, int port) throws IOException {
        Socket socket = new Socket();
        
        // Socket优化配置
        socket.setTcpNoDelay(true);  // 禁用Nagle算法
        socket.setKeepAlive(true);   // 启用Keep-Alive
        socket.setReuseAddress(true); // 允许地址重用
        
        // 缓冲区大小优化
        socket.setSendBufferSize(64 * 1024);    // 64KB发送缓冲区
        socket.setReceiveBufferSize(64 * 1024); // 64KB接收缓冲区
        
        // 连接超时设置
        socket.connect(new InetSocketAddress(host, port), 10000);
        
        // 读取超时设置
        socket.setSoTimeout(30000);
        
        activeConnections.incrementAndGet();
        connectionCount.incrementAndGet();
        
        log.debug("创建优化Socket连接: {}:{}", host, port);
        
        return socket;
    }
    
    /**
     * 创建高性能HTTP连接池
     */
    public ConnectionPool createHttpConnectionPool(String baseUrl, int maxConnections) {
        return connectionPools.computeIfAbsent(baseUrl, url -> {
            ConnectionPool pool = new ConnectionPool(url, maxConnections);
            log.info("创建HTTP连接池: {} (最大连接数: {})", url, maxConnections);
            return pool;
        });
    }
    
    /**
     * HTTP连接池实现
     */
    public static class ConnectionPool {
        private final String baseUrl;
        private final int maxConnections;
        private final BlockingQueue<HttpURLConnection> availableConnections;
        private final AtomicInteger currentConnections = new AtomicInteger(0);
        
        public ConnectionPool(String baseUrl, int maxConnections) {
            this.baseUrl = baseUrl;
            this.maxConnections = maxConnections;
            this.availableConnections = new LinkedBlockingQueue<>();
        }
        
        public HttpURLConnection getConnection() throws IOException {
            HttpURLConnection connection = availableConnections.poll();
            
            if (connection == null && currentConnections.get() < maxConnections) {
                connection = createNewConnection();
                currentConnections.incrementAndGet();
            }
            
            if (connection == null) {
                try {
                    connection = availableConnections.poll(5, TimeUnit.SECONDS);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    throw new IOException("获取连接超时", e);
                }
            }
            
            if (connection == null) {
                throw new IOException("无法获取HTTP连接");
            }
            
            return connection;
        }
        
        public void returnConnection(HttpURLConnection connection) {
            if (connection != null && currentConnections.get() <= maxConnections) {
                availableConnections.offer(connection);
            } else if (connection != null) {
                connection.disconnect();
                currentConnections.decrementAndGet();
            }
        }
        
        private HttpURLConnection createNewConnection() throws IOException {
            URL url = new URL(baseUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // HTTP连接优化
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(30000);
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            
            // 启用HTTP/1.1持久连接
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setRequestProperty("Keep-Alive", "timeout=60, max=100");
            
            // 压缩支持
            connection.setRequestProperty("Accept-Encoding", "gzip, deflate");
            
            // User-Agent
            connection.setRequestProperty("User-Agent", "CryptoTradingSystem/1.0");
            
            return connection;
        }
        
        public int getActiveConnections() {
            return currentConnections.get();
        }
        
        public int getAvailableConnections() {
            return availableConnections.size();
        }
    }
    
    /**
     * WebSocket连接优化
     */
    public void optimizeWebSocketConnection(Object webSocketConnection) {
        try {
            // 这里可以添加WebSocket连接的具体优化逻辑
            // 例如：设置缓冲区大小、心跳间隔、重连策略等
            
            log.debug("WebSocket连接优化完成");
            
        } catch (Exception e) {
            log.warn("WebSocket连接优化失败: {}", e.getMessage());
        }
    }
    
    /**
     * 网络延迟测试
     */
    public long measureNetworkLatency(String host, int port) {
        long startTime = System.nanoTime();
        
        try (Socket socket = new Socket()) {
            socket.connect(new InetSocketAddress(host, port), 5000);
            long endTime = System.nanoTime();
            
            long latencyMs = (endTime - startTime) / 1_000_000;
            log.debug("网络延迟测试: {}:{} = {}ms", host, port, latencyMs);
            
            return latencyMs;
            
        } catch (IOException e) {
            log.warn("网络延迟测试失败: {}:{} - {}", host, port, e.getMessage());
            return -1;
        }
    }
    
    /**
     * 带宽测试
     */
    public double measureBandwidth(String url) {
        long startTime = System.currentTimeMillis();
        long bytesTransferred = 0;
        
        try {
            HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
            connection.setConnectTimeout(10000);
            connection.setReadTimeout(30000);
            
            try (var inputStream = connection.getInputStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    bytesTransferred += bytesRead;
                }
            }
            
            long endTime = System.currentTimeMillis();
            long durationMs = endTime - startTime;
            
            if (durationMs > 0) {
                double bandwidthMbps = (bytesTransferred * 8.0) / (durationMs * 1000.0);
                log.debug("带宽测试: {} = {:.2f} Mbps", url, bandwidthMbps);
                return bandwidthMbps;
            }
            
        } catch (IOException e) {
            log.warn("带宽测试失败: {} - {}", url, e.getMessage());
        }
        
        return 0.0;
    }
    
    /**
     * 获取网络统计信息
     */
    public NetworkStats getNetworkStats() {
        return new NetworkStats(
            activeConnections.get(),
            totalBytesReceived.get(),
            totalBytesSent.get(),
            connectionCount.get(),
            connectionPools.size()
        );
    }
    
    /**
     * 网络统计信息
     */
    public static class NetworkStats {
        private final int activeConnections;
        private final long totalBytesReceived;
        private final long totalBytesSent;
        private final long totalConnections;
        private final int connectionPools;
        
        public NetworkStats(int activeConnections, long totalBytesReceived, 
                          long totalBytesSent, long totalConnections, int connectionPools) {
            this.activeConnections = activeConnections;
            this.totalBytesReceived = totalBytesReceived;
            this.totalBytesSent = totalBytesSent;
            this.totalConnections = totalConnections;
            this.connectionPools = connectionPools;
        }
        
        public int getActiveConnections() { return activeConnections; }
        public long getTotalBytesReceived() { return totalBytesReceived; }
        public long getTotalBytesSent() { return totalBytesSent; }
        public long getTotalConnections() { return totalConnections; }
        public int getConnectionPools() { return connectionPools; }
        
        @Override
        public String toString() {
            return String.format("NetworkStats{active=%d, received=%dMB, sent=%dMB, total=%d, pools=%d}",
                activeConnections, totalBytesReceived / 1024 / 1024, totalBytesSent / 1024 / 1024, 
                totalConnections, connectionPools);
        }
    }
    
    /**
     * 异步网络操作
     */
    public CompletableFuture<String> performAsyncNetworkOperation(String url) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                HttpURLConnection connection = (HttpURLConnection) new URL(url).openConnection();
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(30000);
                
                try (var inputStream = connection.getInputStream();
                     var reader = new java.io.BufferedReader(new java.io.InputStreamReader(inputStream))) {
                    
                    StringBuilder response = new StringBuilder();
                    String line;
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    
                    totalBytesReceived.addAndGet(response.length());
                    return response.toString();
                }
                
            } catch (IOException e) {
                log.error("异步网络操作失败: {}", url, e);
                throw new RuntimeException(e);
            }
        }, networkExecutor);
    }
    
    /**
     * 关闭网络优化器
     */
    public void shutdown() {
        try {
            if (selector != null && selector.isOpen()) {
                selector.close();
            }
            
            if (networkExecutor != null && !networkExecutor.isShutdown()) {
                networkExecutor.shutdown();
                if (!networkExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    networkExecutor.shutdownNow();
                }
            }
            
            // 关闭所有连接池
            connectionPools.values().forEach(pool -> {
                // 这里可以添加连接池关闭逻辑
            });
            
            log.info("网络优化器关闭完成");
            
        } catch (Exception e) {
            log.error("网络优化器关闭失败", e);
        }
    }
}
