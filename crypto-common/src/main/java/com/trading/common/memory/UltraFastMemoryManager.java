package com.trading.common.memory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.nio.ByteBuffer;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 超高性能内存管理器
 * 提供内存预分配、对象池、直接内存管理等功能
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class UltraFastMemoryManager {
    
    private static final Logger log = LoggerFactory.getLogger(UltraFastMemoryManager.class);
    
    // 内存池配置
    private static final int SMALL_BUFFER_SIZE = 1024;      // 1KB
    private static final int MEDIUM_BUFFER_SIZE = 8192;     // 8KB
    private static final int LARGE_BUFFER_SIZE = 65536;     // 64KB
    private static final int POOL_SIZE = 100;
    
    // 直接内存池
    private final ConcurrentLinkedQueue<ByteBuffer> smallBufferPool = new ConcurrentLinkedQueue<>();
    private final ConcurrentLinkedQueue<ByteBuffer> mediumBufferPool = new ConcurrentLinkedQueue<>();
    private final ConcurrentLinkedQueue<ByteBuffer> largeBufferPool = new ConcurrentLinkedQueue<>();
    
    // 堆内存池
    private final ConcurrentLinkedQueue<byte[]> smallByteArrayPool = new ConcurrentLinkedQueue<>();
    private final ConcurrentLinkedQueue<byte[]> mediumByteArrayPool = new ConcurrentLinkedQueue<>();
    private final ConcurrentLinkedQueue<byte[]> largeByteArrayPool = new ConcurrentLinkedQueue<>();
    
    // 性能统计
    private final LongAdder directMemoryAllocated = new LongAdder();
    private final LongAdder directMemoryDeallocated = new LongAdder();
    private final LongAdder heapMemoryAllocated = new LongAdder();
    private final LongAdder heapMemoryDeallocated = new LongAdder();
    private final LongAdder poolHits = new LongAdder();
    private final LongAdder poolMisses = new LongAdder();
    
    // 内存监控
    private final MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
    private final ScheduledExecutorService monitorExecutor = 
        Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "memory-monitor");
            t.setDaemon(true);
            return t;
        });
    
    // 内存阈值
    private volatile long heapMemoryThreshold = 0;
    private volatile long directMemoryThreshold = 0;
    
    @PostConstruct
    public void init() {
        // 初始化内存池
        initializeMemoryPools();
        
        // 设置内存阈值
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        heapMemoryThreshold = (long) (heapUsage.getMax() * 0.8); // 80%阈值
        
        // 启动内存监控
        startMemoryMonitoring();
        
        log.info("超高性能内存管理器初始化完成");
    }
    
    /**
     * 初始化内存池
     */
    private void initializeMemoryPools() {
        // 预分配直接内存缓冲区
        for (int i = 0; i < POOL_SIZE; i++) {
            smallBufferPool.offer(ByteBuffer.allocateDirect(SMALL_BUFFER_SIZE));
            mediumBufferPool.offer(ByteBuffer.allocateDirect(MEDIUM_BUFFER_SIZE));
            largeBufferPool.offer(ByteBuffer.allocateDirect(LARGE_BUFFER_SIZE));
        }
        
        // 预分配堆内存数组
        for (int i = 0; i < POOL_SIZE; i++) {
            smallByteArrayPool.offer(new byte[SMALL_BUFFER_SIZE]);
            mediumByteArrayPool.offer(new byte[MEDIUM_BUFFER_SIZE]);
            largeByteArrayPool.offer(new byte[LARGE_BUFFER_SIZE]);
        }
        
        log.info("内存池初始化完成: 直接内存={}KB, 堆内存={}KB", 
            (SMALL_BUFFER_SIZE + MEDIUM_BUFFER_SIZE + LARGE_BUFFER_SIZE) * POOL_SIZE / 1024,
            (SMALL_BUFFER_SIZE + MEDIUM_BUFFER_SIZE + LARGE_BUFFER_SIZE) * POOL_SIZE / 1024);
    }
    
    /**
     * 启动内存监控
     */
    private void startMemoryMonitoring() {
        monitorExecutor.scheduleAtFixedRate(() -> {
            try {
                monitorMemoryUsage();
            } catch (Exception e) {
                log.warn("内存监控失败", e);
            }
        }, 30, 30, TimeUnit.SECONDS);
    }
    
    /**
     * 监控内存使用情况
     */
    private void monitorMemoryUsage() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        long heapUsed = heapUsage.getUsed();
        long heapMax = heapUsage.getMax();
        double heapUsagePercent = (double) heapUsed / heapMax * 100;
        
        log.debug("内存使用情况: 堆内存={:.1f}% ({}MB/{}MB), 非堆内存={}MB", 
            heapUsagePercent, 
            heapUsed / 1024 / 1024, 
            heapMax / 1024 / 1024,
            nonHeapUsage.getUsed() / 1024 / 1024);
        
        // 内存使用率过高时触发优化
        if (heapUsed > heapMemoryThreshold) {
            log.warn("堆内存使用率过高: {:.1f}%，触发内存优化", heapUsagePercent);
            optimizeMemoryUsage();
        }
    }
    
    /**
     * 获取直接内存缓冲区
     */
    public ByteBuffer borrowDirectBuffer(int size) {
        ByteBuffer buffer = null;
        
        if (size <= SMALL_BUFFER_SIZE) {
            buffer = smallBufferPool.poll();
        } else if (size <= MEDIUM_BUFFER_SIZE) {
            buffer = mediumBufferPool.poll();
        } else if (size <= LARGE_BUFFER_SIZE) {
            buffer = largeBufferPool.poll();
        }
        
        if (buffer != null) {
            buffer.clear();
            poolHits.increment();
            return buffer;
        }
        
        // 池中没有可用缓冲区，创建新的
        poolMisses.increment();
        buffer = ByteBuffer.allocateDirect(size);
        directMemoryAllocated.add(size);
        
        return buffer;
    }
    
    /**
     * 归还直接内存缓冲区
     */
    public void returnDirectBuffer(ByteBuffer buffer) {
        if (buffer == null || !buffer.isDirect()) {
            return;
        }
        
        int capacity = buffer.capacity();
        buffer.clear();
        
        boolean returned = false;
        if (capacity == SMALL_BUFFER_SIZE && smallBufferPool.size() < POOL_SIZE) {
            returned = smallBufferPool.offer(buffer);
        } else if (capacity == MEDIUM_BUFFER_SIZE && mediumBufferPool.size() < POOL_SIZE) {
            returned = mediumBufferPool.offer(buffer);
        } else if (capacity == LARGE_BUFFER_SIZE && largeBufferPool.size() < POOL_SIZE) {
            returned = largeBufferPool.offer(buffer);
        }
        
        if (!returned) {
            // 无法归还到池中，记录释放的内存
            directMemoryDeallocated.add(capacity);
        }
    }
    
    /**
     * 获取堆内存字节数组
     */
    public byte[] borrowByteArray(int size) {
        byte[] array = null;
        
        if (size <= SMALL_BUFFER_SIZE) {
            array = smallByteArrayPool.poll();
        } else if (size <= MEDIUM_BUFFER_SIZE) {
            array = mediumByteArrayPool.poll();
        } else if (size <= LARGE_BUFFER_SIZE) {
            array = largeByteArrayPool.poll();
        }
        
        if (array != null && array.length >= size) {
            poolHits.increment();
            return array;
        }
        
        // 池中没有合适的数组，创建新的
        poolMisses.increment();
        array = new byte[size];
        heapMemoryAllocated.add(size);
        
        return array;
    }
    
    /**
     * 归还堆内存字节数组
     */
    public void returnByteArray(byte[] array) {
        if (array == null) {
            return;
        }
        
        int length = array.length;
        boolean returned = false;
        
        if (length == SMALL_BUFFER_SIZE && smallByteArrayPool.size() < POOL_SIZE) {
            returned = smallByteArrayPool.offer(array);
        } else if (length == MEDIUM_BUFFER_SIZE && mediumByteArrayPool.size() < POOL_SIZE) {
            returned = mediumByteArrayPool.offer(array);
        } else if (length == LARGE_BUFFER_SIZE && largeByteArrayPool.size() < POOL_SIZE) {
            returned = largeByteArrayPool.offer(array);
        }
        
        if (!returned) {
            // 无法归还到池中，记录释放的内存
            heapMemoryDeallocated.add(length);
        }
    }
    
    /**
     * 内存优化
     */
    private void optimizeMemoryUsage() {
        // 清理部分池中的对象
        clearPool(smallBufferPool, POOL_SIZE / 2);
        clearPool(mediumBufferPool, POOL_SIZE / 2);
        clearPool(largeBufferPool, POOL_SIZE / 2);
        
        clearPool(smallByteArrayPool, POOL_SIZE / 2);
        clearPool(mediumByteArrayPool, POOL_SIZE / 2);
        clearPool(largeByteArrayPool, POOL_SIZE / 2);
        
        // 建议垃圾回收
        System.gc();
        
        log.info("内存优化完成");
    }
    
    /**
     * 清理池中的对象
     */
    private <T> void clearPool(ConcurrentLinkedQueue<T> pool, int keepCount) {
        int currentSize = pool.size();
        int toRemove = Math.max(0, currentSize - keepCount);
        
        for (int i = 0; i < toRemove; i++) {
            pool.poll();
        }
    }
    
    /**
     * 获取内存统计信息
     */
    public MemoryStats getMemoryStats() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        MemoryUsage nonHeapUsage = memoryBean.getNonHeapMemoryUsage();
        
        return new MemoryStats(
            heapUsage.getUsed(),
            heapUsage.getMax(),
            nonHeapUsage.getUsed(),
            directMemoryAllocated.sum(),
            directMemoryDeallocated.sum(),
            heapMemoryAllocated.sum(),
            heapMemoryDeallocated.sum(),
            poolHits.sum(),
            poolMisses.sum(),
            smallBufferPool.size() + mediumBufferPool.size() + largeBufferPool.size(),
            smallByteArrayPool.size() + mediumByteArrayPool.size() + largeByteArrayPool.size()
        );
    }
    
    /**
     * 内存统计信息
     */
    public static class MemoryStats {
        public final long heapUsed;
        public final long heapMax;
        public final long nonHeapUsed;
        public final long directMemoryAllocated;
        public final long directMemoryDeallocated;
        public final long heapMemoryAllocated;
        public final long heapMemoryDeallocated;
        public final long poolHits;
        public final long poolMisses;
        public final int directBufferPoolSize;
        public final int byteArrayPoolSize;
        
        public MemoryStats(long heapUsed, long heapMax, long nonHeapUsed,
                          long directMemoryAllocated, long directMemoryDeallocated,
                          long heapMemoryAllocated, long heapMemoryDeallocated,
                          long poolHits, long poolMisses,
                          int directBufferPoolSize, int byteArrayPoolSize) {
            this.heapUsed = heapUsed;
            this.heapMax = heapMax;
            this.nonHeapUsed = nonHeapUsed;
            this.directMemoryAllocated = directMemoryAllocated;
            this.directMemoryDeallocated = directMemoryDeallocated;
            this.heapMemoryAllocated = heapMemoryAllocated;
            this.heapMemoryDeallocated = heapMemoryDeallocated;
            this.poolHits = poolHits;
            this.poolMisses = poolMisses;
            this.directBufferPoolSize = directBufferPoolSize;
            this.byteArrayPoolSize = byteArrayPoolSize;
        }
        
        @Override
        public String toString() {
            double heapUsagePercent = (double) heapUsed / heapMax * 100;
            double poolHitRate = poolHits + poolMisses > 0 ? 
                (double) poolHits / (poolHits + poolMisses) * 100 : 0;
            
            return String.format(
                "MemoryStats{heap=%.1f%% (%dMB/%dMB), nonHeap=%dMB, " +
                "directAlloc=%dMB, heapAlloc=%dMB, poolHitRate=%.1f%%, " +
                "directBuffers=%d, byteArrays=%d}",
                heapUsagePercent, heapUsed / 1024 / 1024, heapMax / 1024 / 1024,
                nonHeapUsed / 1024 / 1024,
                directMemoryAllocated / 1024 / 1024,
                heapMemoryAllocated / 1024 / 1024,
                poolHitRate,
                directBufferPoolSize, byteArrayPoolSize
            );
        }
    }
    
    @PreDestroy
    public void shutdown() {
        if (monitorExecutor != null && !monitorExecutor.isShutdown()) {
            monitorExecutor.shutdown();
            try {
                if (!monitorExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    monitorExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                monitorExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        log.info("超高性能内存管理器已关闭");
    }
}
