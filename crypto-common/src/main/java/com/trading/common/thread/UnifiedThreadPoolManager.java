package com.trading.common.thread;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;

import java.util.HashMap;
import java.util.concurrent.*;
import java.util.concurrent.atomic.LongAdder;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 统一线程池管理器
 * 管理所有线程池，提供统一的线程池创建、监控和销毁
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class UnifiedThreadPoolManager {

    private static final Logger log = LoggerFactory.getLogger(UnifiedThreadPoolManager.class);

    // 线程池配置 - 根据CPU核心数动态调整
    @Value("${app.performance.core-pool-size:#{T(java.lang.Runtime).getRuntime().availableProcessors()}}")
    private int corePoolSize;

    @Value("${app.performance.max-pool-size:#{T(java.lang.Runtime).getRuntime().availableProcessors() * 4}}")
    private int maxPoolSize;

    @Value("${app.performance.queue-capacity:1000}")
    private int queueCapacity;

    @Value("${app.performance.keep-alive-seconds:60}")
    private long keepAliveSeconds;

    // 虚拟线程配置 - 优化虚拟线程数量
    @Value("${market-data.processor.virtual-threads.enabled:true}")
    private boolean virtualThreadsEnabled;

    @Value("${market-data.processor.virtual-threads.max-threads:1000}")
    private int maxVirtualThreads;

    // 线程池注册表
    private final Map<String, ExecutorService> executorRegistry = new ConcurrentHashMap<>();
    private final Map<String, ScheduledExecutorService> scheduledExecutorRegistry = new ConcurrentHashMap<>();
    
    // 统一的虚拟线程执行器
    private ExecutorService virtualThreadExecutor;
    
    // 统一的调度执行器
    private ScheduledExecutorService unifiedScheduledExecutor;
    
    // 统一的数据处理执行器
    private ExecutorService dataProcessingExecutor;
    
    // 统一的监控执行器
    private ScheduledExecutorService monitoringExecutor;
    
    // 统一的WebSocket执行器
    private ExecutorService webSocketExecutor;

    // 性能统计
    private final LongAdder totalTasksSubmitted = new LongAdder();
    private final LongAdder totalTasksCompleted = new LongAdder();
    private final LongAdder totalTasksFailed = new LongAdder();

    @PostConstruct
    public void initialize() {
        log.info("初始化统一线程池管理器...");

        // 加载配置（支持系统属性覆盖）
        loadConfiguration();

        // 检查JDK版本和虚拟线程支持
        checkVirtualThreadSupport();

        // 创建虚拟线程执行器
        initializeVirtualThreadExecutor();

        // 创建统一调度执行器
        initializeScheduledExecutor();

        // 创建数据处理执行器
        initializeDataProcessingExecutor();

        // 创建监控执行器
        initializeMonitoringExecutor();

        // 创建WebSocket执行器
        initializeWebSocketExecutor();

        // 启动监控
        startMonitoring();

        log.info("统一线程池管理器初始化完成 - 虚拟线程: {}, 核心池大小: {}, 最大池大小: {}",
                virtualThreadsEnabled, corePoolSize, maxPoolSize);
    }

    /**
     * 加载配置（支持系统属性覆盖）
     */
    private void loadConfiguration() {
        // 从系统属性加载配置，如果没有则使用默认值
        corePoolSize = Integer.parseInt(System.getProperty("app.performance.core-pool-size", String.valueOf(corePoolSize)));
        maxPoolSize = Integer.parseInt(System.getProperty("app.performance.max-pool-size", String.valueOf(maxPoolSize)));
        queueCapacity = Integer.parseInt(System.getProperty("app.performance.queue-capacity", String.valueOf(queueCapacity)));
        keepAliveSeconds = Long.parseLong(System.getProperty("app.performance.keep-alive-seconds", String.valueOf(keepAliveSeconds)));
        virtualThreadsEnabled = Boolean.parseBoolean(System.getProperty("market-data.processor.virtual-threads.enabled", String.valueOf(virtualThreadsEnabled)));
        maxVirtualThreads = Integer.parseInt(System.getProperty("market-data.processor.virtual-threads.max-threads", String.valueOf(maxVirtualThreads)));

        log.debug("配置加载完成 - 核心池大小: {}, 最大池大小: {}, 队列容量: {}, 虚拟线程启用: {}",
                corePoolSize, maxPoolSize, queueCapacity, virtualThreadsEnabled);
    }

    /**
     * 检查虚拟线程支持
     */
    private void checkVirtualThreadSupport() {
        try {
            // 检查JDK版本
            String javaVersion = System.getProperty("java.version");
            log.info("当前JDK版本: {}", javaVersion);

            // 尝试访问虚拟线程API
            Thread.ofVirtual();
            log.info("虚拟线程API可用");
        } catch (Exception e) {
            log.warn("虚拟线程API不可用，将使用传统线程池: {}", e.getMessage());
            virtualThreadsEnabled = false;
        }
    }

    /**
     * 初始化虚拟线程执行器
     */
    private void initializeVirtualThreadExecutor() {
        if (virtualThreadsEnabled) {
            try {
                // 尝试创建虚拟线程执行器
                // 显式创建ThreadFactory以绕过可能的编译器/IDE问题
                ThreadFactory virtualThreadFactory = r -> {
                    Thread thread = Thread.ofVirtual().name("unified-vt-", 0).unstarted(r);
                    thread.setDaemon(true); // 将其设置为守护线程
                    return thread;
                };
                virtualThreadExecutor = Executors.newThreadPerTaskExecutor(virtualThreadFactory);
                log.info("虚拟线程执行器已创建，最大线程数: {}", maxVirtualThreads);
            } catch (Exception e) {
                log.warn("虚拟线程创建失败，降级到传统线程池: {}", e.getMessage());
                virtualThreadExecutor = createOptimizedThreadPool("virtual-fallback", corePoolSize);
                log.info("已降级到传统线程池，核心线程数: {}", corePoolSize);
            }
        } else {
            virtualThreadExecutor = createOptimizedThreadPool("virtual-fallback", corePoolSize);
            log.info("虚拟线程未启用，使用固定线程池替代");
        }
        executorRegistry.put("virtual", virtualThreadExecutor);
    }

    /**
     * 初始化统一调度执行器
     */
    private void initializeScheduledExecutor() {
        unifiedScheduledExecutor = Executors.newScheduledThreadPool(
                Math.max(2, corePoolSize / 5), // 调度线程数为核心线程数的1/5，最少2个
                r -> {
                    Thread thread = new Thread(r, "unified-scheduler-" + System.currentTimeMillis());
                    thread.setDaemon(true);
                    return thread;
                }
        );
        scheduledExecutorRegistry.put("unified", unifiedScheduledExecutor);
        log.info("统一调度执行器已创建，线程数: {}", Math.max(2, corePoolSize / 5));
    }

    /**
     * 初始化数据处理执行器
     */
    private void initializeDataProcessingExecutor() {
        dataProcessingExecutor = createOptimizedThreadPool("data-processing", corePoolSize);
        executorRegistry.put("data-processing", dataProcessingExecutor);
        log.info("数据处理执行器已创建");
    }

    /**
     * 初始化监控执行器
     */
    private void initializeMonitoringExecutor() {
        monitoringExecutor = Executors.newScheduledThreadPool(
                2, // 监控只需要2个线程
                r -> {
                    Thread thread = new Thread(r, "unified-monitor-" + System.currentTimeMillis());
                    thread.setDaemon(true);
                    return thread;
                }
        );
        scheduledExecutorRegistry.put("monitoring", monitoringExecutor);
        log.info("监控执行器已创建");
    }

    /**
     * 初始化WebSocket执行器
     */
    private void initializeWebSocketExecutor() {
        webSocketExecutor = createOptimizedThreadPool("websocket", Math.max(4, corePoolSize / 2));
        executorRegistry.put("websocket", webSocketExecutor);
        log.info("WebSocket执行器已创建");
    }

    /**
     * 创建优化的线程池 - 改进资源利用率和性能
     */
    private ThreadPoolExecutor createOptimizedThreadPool(String namePrefix, int coreSize) {
        // 动态计算线程池参数
        int actualCoreSize = Math.max(1, coreSize);
        int maxThreads = Math.max(actualCoreSize * 2, actualCoreSize + 4);
        int queueSize = Math.max(50, queueCapacity / 2);

        log.debug("创建线程池 {} - 核心线程数: {}, 最大线程数: {}, 队列大小: {}",
                namePrefix, actualCoreSize, maxThreads, queueSize);

        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                actualCoreSize,
                maxThreads,
                keepAliveSeconds,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueSize),
                r -> {
                    Thread thread = new Thread(r, namePrefix + "-" + System.nanoTime());
                    thread.setDaemon(true);
                    thread.setUncaughtExceptionHandler((t, e) ->
                        log.error("线程 {} 发生未捕获异常", t.getName(), e));
                    return thread;
                },
                new ThreadPoolExecutor.CallerRunsPolicy() // 使用调用者运行策略防止任务丢失
        );

        // 允许核心线程超时，提高资源利用率
        executor.allowCoreThreadTimeOut(true);

        return executor;
    }

    /**
     * 获取虚拟线程执行器
     */
    public ExecutorService getVirtualThreadExecutor() {
        return virtualThreadExecutor;
    }

    /**
     * 获取统一调度执行器
     */
    public ScheduledExecutorService getUnifiedScheduledExecutor() {
        return unifiedScheduledExecutor;
    }

    /**
     * 获取数据处理执行器
     */
    public ExecutorService getDataProcessingExecutor() {
        return dataProcessingExecutor;
    }

    /**
     * 获取监控执行器
     */
    public ScheduledExecutorService getMonitoringExecutor() {
        return monitoringExecutor;
    }

    /**
     * 获取WebSocket执行器
     */
    public ExecutorService getWebSocketExecutor() {
        return webSocketExecutor;
    }

    /**
     * 提交任务到虚拟线程执行器
     */
    public CompletableFuture<Void> submitToVirtualThread(Runnable task) {
        totalTasksSubmitted.increment();
        return CompletableFuture.runAsync(task, virtualThreadExecutor)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        totalTasksFailed.increment();
                        log.warn("虚拟线程任务执行失败", throwable);
                    } else {
                        totalTasksCompleted.increment();
                    }
                });
    }

    /**
     * 提交任务到数据处理执行器
     */
    public CompletableFuture<Void> submitToDataProcessing(Runnable task) {
        totalTasksSubmitted.increment();
        return CompletableFuture.runAsync(task, dataProcessingExecutor)
                .whenComplete((result, throwable) -> {
                    if (throwable != null) {
                        totalTasksFailed.increment();
                        log.warn("数据处理任务执行失败", throwable);
                    } else {
                        totalTasksCompleted.increment();
                    }
                });
    }

    /**
     * 调度任务
     */
    public ScheduledFuture<?> schedule(Runnable task, long delay, TimeUnit unit) {
        totalTasksSubmitted.increment();
        return unifiedScheduledExecutor.schedule(() -> {
            try {
                task.run();
                totalTasksCompleted.increment();
            } catch (Exception e) {
                totalTasksFailed.increment();
                log.warn("调度任务执行失败", e);
            }
        }, delay, unit);
    }

    /**
     * 定期调度任务
     */
    public ScheduledFuture<?> scheduleAtFixedRate(Runnable task, long initialDelay, long period, TimeUnit unit) {
        return unifiedScheduledExecutor.scheduleAtFixedRate(() -> {
            try {
                task.run();
                totalTasksCompleted.increment();
            } catch (Exception e) {
                totalTasksFailed.increment();
                log.warn("定期调度任务执行失败", e);
            }
        }, initialDelay, period, unit);
    }

    /**
     * 启动监控
     */
    private void startMonitoring() {
        monitoringExecutor.scheduleAtFixedRate(this::logThreadPoolStatistics, 5, 5, TimeUnit.MINUTES);
    }

    /**
     * 输出线程池统计信息
     */
    private void logThreadPoolStatistics() {
        log.info("=== 统一线程池统计信息 ===");
        log.info("总提交任务数: {}", totalTasksSubmitted.sum());
        log.info("总完成任务数: {}", totalTasksCompleted.sum());
        log.info("总失败任务数: {}", totalTasksFailed.sum());
        
        executorRegistry.forEach((name, executor) -> {
            if (executor instanceof ThreadPoolExecutor) {
                ThreadPoolExecutor tpe = (ThreadPoolExecutor) executor;
                log.info("线程池[{}]: 活跃线程={}, 池大小={}, 队列大小={}, 完成任务={}",
                        name, tpe.getActiveCount(), tpe.getPoolSize(), 
                        tpe.getQueue().size(), tpe.getCompletedTaskCount());
            }
        });
        
        scheduledExecutorRegistry.forEach((name, executor) -> {
            if (executor instanceof ThreadPoolExecutor) {
                ThreadPoolExecutor tpe = (ThreadPoolExecutor) executor;
                log.info("调度池[{}]: 活跃线程={}, 池大小={}, 队列大小={}, 完成任务={}",
                        name, tpe.getActiveCount(), tpe.getPoolSize(), 
                        tpe.getQueue().size(), tpe.getCompletedTaskCount());
            }
        });
        log.info("=== 统计信息结束 ===");
    }

    /**
     * 获取线程池统计信息
     */
    public ThreadPoolStatistics getStatistics() {
        return new ThreadPoolStatistics(
                totalTasksSubmitted.sum(),
                totalTasksCompleted.sum(),
                totalTasksFailed.sum(),
                executorRegistry.size(),
                scheduledExecutorRegistry.size(),
                virtualThreadsEnabled
        );
    }

    /**
     * 获取详细的线程池状态信息（用于监控）
     */
    public Map<String, Map<String, Object>> getThreadPoolStats() {
        Map<String, Map<String, Object>> stats = new HashMap<>();

        // 虚拟线程统计
        stats.put("virtualThreads", Map.of(
                "enabled", virtualThreadsEnabled,
                "maxThreads", maxVirtualThreads,
                "tasksSubmitted", totalTasksSubmitted.sum(),
                "tasksCompleted", totalTasksCompleted.sum(),
                "tasksFailed", totalTasksFailed.sum()
        ));

        // 添加各个线程池的详细统计
        for (Map.Entry<String, ExecutorService> entry : executorRegistry.entrySet()) {
            String poolName = entry.getKey();
            ExecutorService executor = entry.getValue();

            Map<String, Object> poolStats = new HashMap<>();
            poolStats.put("executor", executor);
            poolStats.put("type", executor.getClass().getSimpleName());

            if (executor instanceof ThreadPoolExecutor) {
                ThreadPoolExecutor tpe = (ThreadPoolExecutor) executor;
                poolStats.put("activeCount", tpe.getActiveCount());
                poolStats.put("corePoolSize", tpe.getCorePoolSize());
                poolStats.put("maximumPoolSize", tpe.getMaximumPoolSize());
                poolStats.put("taskCount", tpe.getTaskCount());
                poolStats.put("completedTaskCount", tpe.getCompletedTaskCount());
                poolStats.put("queueSize", tpe.getQueue().size());
            }

            stats.put(poolName, poolStats);
        }

        return stats;
    }

    @PreDestroy
    public void shutdown() {
        log.info("关闭统一线程池管理器...");
        
        try {
            // 关闭所有普通执行器
            log.info("关闭{}个普通执行器...", executorRegistry.size());
            executorRegistry.values().forEach(this::shutdownExecutor);
            
            // 关闭所有调度执行器
            log.info("关闭{}个调度执行器...", scheduledExecutorRegistry.size());
            scheduledExecutorRegistry.values().forEach(this::shutdownExecutor);
            
            executorRegistry.clear();
            scheduledExecutorRegistry.clear();
            
            log.info("统一线程池管理器已关闭");
        } catch (Exception e) {
            log.error("关闭统一线程池管理器时发生异常", e);
        }
    }

    private void shutdownExecutor(ExecutorService executor) {
        try {
            log.debug("开始关闭执行器: {}", executor.getClass().getSimpleName());
            
            // 优雅关闭
            executor.shutdown();
            
            // 等待正在执行的任务完成，最多等待15秒
            if (!executor.awaitTermination(15, TimeUnit.SECONDS)) {
                log.warn("执行器未在15秒内完成，强制关闭: {}", executor.getClass().getSimpleName());
                executor.shutdownNow();
                
                // 再等待5秒确保强制关闭完成
                if (!executor.awaitTermination(5, TimeUnit.SECONDS)) {
                    log.error("执行器强制关闭失败: {}", executor.getClass().getSimpleName());
                }
            } else {
                log.debug("执行器已优雅关闭: {}", executor.getClass().getSimpleName());
            }
        } catch (InterruptedException e) {
            log.warn("关闭执行器时被中断，强制关闭: {}", executor.getClass().getSimpleName());
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.error("关闭执行器时发生异常: {}", executor.getClass().getSimpleName(), e);
            executor.shutdownNow();
        }
    }

    /**
     * 线程池统计信息
     */
    public static class ThreadPoolStatistics {
        private final long totalTasksSubmitted;
        private final long totalTasksCompleted;
        private final long totalTasksFailed;
        private final int executorCount;
        private final int scheduledExecutorCount;
        private final boolean virtualThreadsEnabled;

        public ThreadPoolStatistics(long totalTasksSubmitted, long totalTasksCompleted, 
                                   long totalTasksFailed, int executorCount, 
                                   int scheduledExecutorCount, boolean virtualThreadsEnabled) {
            this.totalTasksSubmitted = totalTasksSubmitted;
            this.totalTasksCompleted = totalTasksCompleted;
            this.totalTasksFailed = totalTasksFailed;
            this.executorCount = executorCount;
            this.scheduledExecutorCount = scheduledExecutorCount;
            this.virtualThreadsEnabled = virtualThreadsEnabled;
        }

        // Getters
        public long getTotalTasksSubmitted() { return totalTasksSubmitted; }
        public long getTotalTasksCompleted() { return totalTasksCompleted; }
        public long getTotalTasksFailed() { return totalTasksFailed; }
        public int getExecutorCount() { return executorCount; }
        public int getScheduledExecutorCount() { return scheduledExecutorCount; }
        public boolean isVirtualThreadsEnabled() { return virtualThreadsEnabled; }

        public double getSuccessRate() {
            return totalTasksSubmitted == 0 ? 0.0 : 
                   (double) totalTasksCompleted / totalTasksSubmitted * 100;
        }

        @Override
        public String toString() {
            return String.format("ThreadPool: submitted=%d, completed=%d, failed=%d, " +
                            "executors=%d, schedulers=%d, virtualThreads=%s, successRate=%.2f%%",
                    totalTasksSubmitted, totalTasksCompleted, totalTasksFailed,
                    executorCount, scheduledExecutorCount, virtualThreadsEnabled, getSuccessRate());
        }
    }
}
