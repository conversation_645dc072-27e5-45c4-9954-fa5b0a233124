<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 测试环境日志配置 -->
    
    <!-- 控制台输出配置 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{HH:mm:ss.SSS} [%thread] %highlight(%-5level) [%cyan(%logger{36})] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
    </appender>
    
    <!-- 测试日志文件配置 -->
    <appender name="TEST_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>target/test-logs/crypto-common-test.log</file>
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>target/test-logs/crypto-common-test.%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
            <maxFileSize>10MB</maxFileSize>
            <maxHistory>3</maxHistory>
            <totalSizeCap>50MB</totalSizeCap>
        </rollingPolicy>
    </appender>
    
    <!-- 特定包的日志级别配置 -->
    
    <!-- 测试相关日志 -->
    <logger name="com.trading.common" level="DEBUG" additivity="false">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="TEST_FILE"/>
    </logger>
    
    <!-- Spring框架日志 -->
    <logger name="org.springframework" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- Hibernate日志 -->
    <logger name="org.hibernate" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- H2数据库日志 -->
    <logger name="org.h2" level="WARN" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- JUnit日志 -->
    <logger name="org.junit" level="INFO" additivity="false">
        <appender-ref ref="CONSOLE"/>
    </logger>
    
    <!-- 根日志配置 -->
    <root level="WARN">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="TEST_FILE"/>
    </root>
    
</configuration>
