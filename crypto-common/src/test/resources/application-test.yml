# 测试环境配置文件
spring:
  # 数据源配置（使用MySQL数据库）
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${MYSQL_HOST:localhost}:${MYSQL_PORT:3306}/crypto_trading?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
    username: crypto_user
    password: crypto_user_2024!

  # Redis配置（使用嵌入式Redis）
  data:
    redis:
      host: localhost
      port: 16379
      database: 15
      password: root
      timeout: 1000ms

# MyBatis-Plus测试配置
mybatis-plus:
  # 配置扫描路径
  mapper-locations: classpath*:mapper/*.xml
  # 实体扫描
  type-aliases-package: com.trading.common.model,com.trading.market.model,com.trading.sdk.model
  # 配置
  configuration:
    # 是否开启自动驼峰命名规则映射
    map-underscore-to-camel-case: true
    # 关闭二级缓存（测试环境）
    cache-enabled: false
    # 配置JdbcTypeForNull
    jdbc-type-for-null: null
    # 配置默认的执行器
    default-executor-type: simple
    # 配置默认超时时间
    default-statement-timeout: 10
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  # 全局配置
  global-config:
    # 数据库相关配置
    db-config:
      # 主键类型
      id-type: auto
      # 表名下划线命名
      table-underline: true
      # 字段名下划线命名
      column-underline: true
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# 应用配置
app:
  name: crypto-common-test
  version: 1.0.0-test
  
  # 性能配置
  performance:
    virtual-threads:
      enabled: false  # 测试环境关闭虚拟线程
    timeout:
      default: 5000
      api: 3000
      database: 2000
      cache: 500
      
# 测试专用配置
test:
  # 模拟数据配置
  mock:
    enabled: true
    data-size: 100
    
  # 性能测试配置
  performance:
    enabled: false
    thread-count: 10
    duration-seconds: 30
    
  # 集成测试配置
  integration:
    enabled: false
    external-api: false
    database: true
    cache: true
    
# 日志配置
logging:
  level:
    root: WARN
    com.trading: INFO  # 提高日志级别，避免大量debug输出
    com.trading.common.cache: WARN  # 缓存组件使用WARN级别
    org.springframework: WARN
    
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level [%logger{36}] - %msg%n"
    
# Binance API测试配置
binance:
  spot:
    base-url: "https://testnet.binance.vision"  # 测试网
    api-key: "${BINANCE_TEST_API_KEY:}"  # 从环境变量获取测试密钥
    secret-key: "${BINANCE_TEST_SECRET_KEY:}"  # 从环境变量获取测试密钥

  futures:
    base-url: "https://testnet.binancefuture.com"
    api-key: "${BINANCE_FUTURES_TEST_API_KEY:}"  # 从环境变量获取测试密钥
    secret-key: "${BINANCE_FUTURES_TEST_SECRET_KEY:}"  # 从环境变量获取测试密钥
    
  websocket:
    spot-url: "wss://testnet.binance.vision/ws/"
    futures-url: "wss://stream.binancefuture.com/ws/"
    
  
# 数据库测试配置
database:
  mysql:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://${MYSQL_HOST:localhost}:${MYSQL_PORT:3306}/crypto_trading?useUnicode=true&characterEncoding=utf8&useSSL=false&serverTimezone=UTC&allowPublicKeyRetrieval=true
    username: crypto_user
    password: crypto_user_2024!
    
# Redis测试配置
redis:
  host: localhost
  port: 6370
  database: 15

# Kafka测试配置（使用嵌入式Kafka）
kafka:
  bootstrap-servers: localhost:9093
  
# 错误码测试配置
error:
  test-mode: true
  mock-errors: true
