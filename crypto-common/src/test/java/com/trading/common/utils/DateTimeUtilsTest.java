package com.trading.common.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;

/**
 * DateTimeUtils 单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@DisplayName("日期时间工具类测试")
class DateTimeUtilsTest {
    
    @Test
    @DisplayName("测试获取当前UTC时间")
    void testGetCurrentUtcTime() {
        LocalDateTime utcTime = DateTimeUtils.getCurrentUtcTime();
        assertNotNull(utcTime);
        
        // 验证时间是合理的（不能是未来时间，不能太久以前）
        LocalDateTime now = LocalDateTime.now(ZoneId.of("UTC"));
        assertTrue(utcTime.isBefore(now.plusMinutes(1)));
        assertTrue(utcTime.isAfter(now.minusMinutes(1)));
    }
    
    @Test
    @DisplayName("测试获取当前中国时间")
    void testGetCurrentChinaTime() {
        LocalDateTime chinaTime = DateTimeUtils.getCurrentChinaTime();
        assertNotNull(chinaTime);
        
        LocalDateTime now = LocalDateTime.now(ZoneId.of("Asia/Shanghai"));
        assertTrue(chinaTime.isBefore(now.plusMinutes(1)));
        assertTrue(chinaTime.isAfter(now.minusMinutes(1)));
    }
    
    @Test
    @DisplayName("测试时区转换")
    void testConvertTimezone() {
        LocalDateTime utcTime = LocalDateTime.of(2024, 1, 1, 12, 0, 0);
        
        // UTC转中国时间（+8小时）
        LocalDateTime chinaTime = DateTimeUtils.convertTimezone(utcTime, "UTC", "Asia/Shanghai");
        assertEquals(LocalDateTime.of(2024, 1, 1, 20, 0, 0), chinaTime);
        
        // 中国时间转UTC（-8小时）
        LocalDateTime backToUtc = DateTimeUtils.convertTimezone(chinaTime, "Asia/Shanghai", "UTC");
        assertEquals(utcTime, backToUtc);
    }
    
    @Test
    @DisplayName("测试格式化日期时间")
    void testFormatDateTime() {
        LocalDateTime dateTime = LocalDateTime.of(2024, 1, 1, 12, 30, 45);
        
        // 测试默认格式
        String formatted = DateTimeUtils.formatDateTime(dateTime);
        assertEquals("2024-01-01 12:30:45", formatted);
        
        // 测试自定义格式
        String customFormatted = DateTimeUtils.formatDateTime(dateTime, "yyyy/MM/dd HH:mm");
        assertEquals("2024/01/01 12:30", customFormatted);
    }
    
    @Test
    @DisplayName("测试解析日期时间字符串")
    void testParseDateTime() {
        // 测试默认格式解析
        LocalDateTime parsed = DateTimeUtils.parseDateTime("2024-01-01 12:30:45");
        assertEquals(LocalDateTime.of(2024, 1, 1, 12, 30, 45), parsed);
        
        // 测试自定义格式解析
        LocalDateTime customParsed = DateTimeUtils.parseDateTime("2024/01/01 12:30", "yyyy/MM/dd HH:mm");
        assertEquals(LocalDateTime.of(2024, 1, 1, 12, 30, 0), customParsed);
        
        // 测试无效格式
        assertThrows(IllegalArgumentException.class, () -> {
            DateTimeUtils.parseDateTime("invalid-date");
        });
    }
    
    @Test
    @DisplayName("测试时间戳转换")
    void testTimestampConversion() {
        LocalDateTime dateTime = LocalDateTime.of(2024, 1, 1, 12, 0, 0);
        
        // 转换为时间戳
        long timestamp = DateTimeUtils.toTimestamp(dateTime);
        assertTrue(timestamp > 0);
        
        // 从时间戳转回
        LocalDateTime converted = DateTimeUtils.fromTimestamp(timestamp);
        assertEquals(dateTime, converted);
        
        // 测试毫秒时间戳
        long millis = DateTimeUtils.toMillisTimestamp(dateTime);
        LocalDateTime fromMillis = DateTimeUtils.fromMillisTimestamp(millis);
        assertEquals(dateTime, fromMillis);
    }
    
    @Test
    @DisplayName("测试时间比较")
    void testTimeComparison() {
        LocalDateTime time1 = LocalDateTime.of(2024, 1, 1, 12, 0, 0);
        LocalDateTime time2 = LocalDateTime.of(2024, 1, 1, 13, 0, 0);
        
        assertTrue(DateTimeUtils.isBefore(time1, time2));
        assertFalse(DateTimeUtils.isBefore(time2, time1));
        
        assertTrue(DateTimeUtils.isAfter(time2, time1));
        assertFalse(DateTimeUtils.isAfter(time1, time2));
        
        assertTrue(DateTimeUtils.isEqual(time1, time1));
        assertFalse(DateTimeUtils.isEqual(time1, time2));
    }
    
    @Test
    @DisplayName("测试时间计算")
    void testTimeCalculation() {
        LocalDateTime baseTime = LocalDateTime.of(2024, 1, 1, 12, 0, 0);
        
        // 加时间
        LocalDateTime plusHours = DateTimeUtils.plusHours(baseTime, 2);
        assertEquals(LocalDateTime.of(2024, 1, 1, 14, 0, 0), plusHours);
        
        LocalDateTime plusMinutes = DateTimeUtils.plusMinutes(baseTime, 30);
        assertEquals(LocalDateTime.of(2024, 1, 1, 12, 30, 0), plusMinutes);
        
        LocalDateTime plusSeconds = DateTimeUtils.plusSeconds(baseTime, 45);
        assertEquals(LocalDateTime.of(2024, 1, 1, 12, 0, 45), plusSeconds);
        
        // 减时间
        LocalDateTime minusHours = DateTimeUtils.minusHours(baseTime, 1);
        assertEquals(LocalDateTime.of(2024, 1, 1, 11, 0, 0), minusHours);
        
        LocalDateTime minusMinutes = DateTimeUtils.minusMinutes(baseTime, 15);
        assertEquals(LocalDateTime.of(2024, 1, 1, 11, 45, 0), minusMinutes);
        
        LocalDateTime minusSeconds = DateTimeUtils.minusSeconds(baseTime, 30);
        assertEquals(LocalDateTime.of(2024, 1, 1, 11, 59, 30), minusSeconds);
    }
    
    @Test
    @DisplayName("测试时间差计算")
    void testTimeDifference() {
        LocalDateTime start = LocalDateTime.of(2024, 1, 1, 12, 0, 0);
        LocalDateTime end = LocalDateTime.of(2024, 1, 1, 14, 30, 45);
        
        // 计算小时差
        long hoursDiff = DateTimeUtils.getHoursBetween(start, end);
        assertEquals(2, hoursDiff);
        
        // 计算分钟差
        long minutesDiff = DateTimeUtils.getMinutesBetween(start, end);
        assertEquals(150, minutesDiff); // 2小时30分钟 = 150分钟
        
        // 计算秒差
        long secondsDiff = DateTimeUtils.getSecondsBetween(start, end);
        assertEquals(9045, secondsDiff); // 2小时30分钟45秒 = 9045秒
    }
    
    @Test
    @DisplayName("测试时间范围检查")
    void testTimeRangeCheck() {
        LocalDateTime start = LocalDateTime.of(2024, 1, 1, 12, 0, 0);
        LocalDateTime end = LocalDateTime.of(2024, 1, 1, 14, 0, 0);
        LocalDateTime middle = LocalDateTime.of(2024, 1, 1, 13, 0, 0);
        LocalDateTime outside = LocalDateTime.of(2024, 1, 1, 15, 0, 0);
        
        assertTrue(DateTimeUtils.isBetween(middle, start, end));
        assertFalse(DateTimeUtils.isBetween(outside, start, end));
        
        // 边界值测试
        assertTrue(DateTimeUtils.isBetween(start, start, end));
        assertTrue(DateTimeUtils.isBetween(end, start, end));
    }
    
    @Test
    @DisplayName("测试空值处理")
    void testNullHandling() {
        // 测试空值输入
        assertNull(DateTimeUtils.formatDateTime(null));
        assertEquals(0L, DateTimeUtils.toTimestamp(null));
        assertNull(DateTimeUtils.convertTimezone(null, "UTC", "Asia/Shanghai"));
        
        // 测试空字符串解析
        assertNull(DateTimeUtils.parseDateTime(""));
        assertNull(DateTimeUtils.parseDateTime(null));
    }
    
    @Test
    @DisplayName("测试边界值")
    void testBoundaryValues() {
        // 测试最小时间戳
        LocalDateTime minTime = DateTimeUtils.fromTimestamp(0);
        assertNotNull(minTime);
        
        // 测试当前时间戳
        long currentTimestamp = System.currentTimeMillis() / 1000;
        LocalDateTime currentTime = DateTimeUtils.fromTimestamp(currentTimestamp);
        assertNotNull(currentTime);
        
        // 验证往返转换的一致性
        long backToTimestamp = DateTimeUtils.toTimestamp(currentTime);
        assertEquals(currentTimestamp, backToTimestamp);
    }
}
