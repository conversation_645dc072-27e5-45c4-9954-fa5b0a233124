package com.trading.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.trading.common.dto.ResponseResult;
import com.trading.common.exception.JsonOperationException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * JsonUtils 单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@DisplayName("JSON工具类测试")
class JsonUtilsTest {
    
    /**
     * 测试用的简单对象
     */
    static class TestObject {
        private String name;
        private Integer age;
        private BigDecimal amount;
        private LocalDateTime createTime;
        
        public TestObject() {}
        
        public TestObject(String name, Integer age, BigDecimal amount, LocalDateTime createTime) {
            this.name = name;
            this.age = age;
            this.amount = amount;
            this.createTime = createTime;
        }
        
        // Getters and Setters
        public String getName() { return name; }
        public void setName(String name) { this.name = name; }
        public Integer getAge() { return age; }
        public void setAge(Integer age) { this.age = age; }
        public BigDecimal getAmount() { return amount; }
        public void setAmount(BigDecimal amount) { this.amount = amount; }
        public LocalDateTime getCreateTime() { return createTime; }
        public void setCreateTime(LocalDateTime createTime) { this.createTime = createTime; }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TestObject that = (TestObject) obj;
            return Objects.equals(name, that.name) &&
                   Objects.equals(age, that.age) &&
                   Objects.equals(amount, that.amount) &&
                   Objects.equals(createTime, that.createTime);
        }
        
        @Override
        public int hashCode() {
            return Objects.hash(name, age, amount, createTime);
        }
    }
    
    @Test
    @DisplayName("测试对象转JSON字符串")
    void testToJsonString() {
        TestObject obj = new TestObject("test", 25, new BigDecimal("100.50"), 
                                       LocalDateTime.of(2024, 1, 1, 12, 0, 0));
        
        String json = JsonUtils.toJsonString(obj);
        assertNotNull(json);
        assertTrue(json.contains("\"name\":\"test\""));
        assertTrue(json.contains("\"age\":25"));
        assertTrue(json.contains("\"amount\":100.50"));
    }
    
    @Test
    @DisplayName("测试JSON字符串转对象")
    void testFromJsonString() {
        String json = "{\"name\":\"test\",\"age\":25,\"amount\":100.50}";
        
        TestObject obj = JsonUtils.fromJsonString(json, TestObject.class);
        assertNotNull(obj);
        assertEquals("test", obj.getName());
        assertEquals(25, obj.getAge());
        assertEquals(new BigDecimal("100.50"), obj.getAmount());
    }
    
    @Test
    @DisplayName("测试JSON字符串转泛型对象")
    void testFromJsonStringWithTypeReference() {
        String json = "[{\"name\":\"test1\",\"age\":25},{\"name\":\"test2\",\"age\":30}]";
        
        List<TestObject> list = JsonUtils.fromJsonString(json, new TypeReference<List<TestObject>>() {});
        assertNotNull(list);
        assertEquals(2, list.size());
        assertEquals("test1", list.get(0).getName());
        assertEquals("test2", list.get(1).getName());
    }
    
    @Test
    @DisplayName("测试Map转JSON")
    void testMapToJson() {
        Map<String, Object> map = new HashMap<>();
        map.put("name", "test");
        map.put("age", 25);
        map.put("amount", new BigDecimal("100.50"));
        map.put("active", true);
        
        String json = JsonUtils.toJsonString(map);
        assertNotNull(json);
        assertTrue(json.contains("\"name\":\"test\""));
        assertTrue(json.contains("\"age\":25"));
        assertTrue(json.contains("\"active\":true"));
    }
    
    @Test
    @DisplayName("测试JSON转Map")
    void testJsonToMap() {
        String json = "{\"name\":\"test\",\"age\":25,\"amount\":100.50,\"active\":true}";
        
        Map<String, Object> map = JsonUtils.fromJsonString(json, new TypeReference<Map<String, Object>>() {});
        assertNotNull(map);
        assertEquals("test", map.get("name"));
        assertEquals(25, map.get("age"));
        assertEquals(100.50, map.get("amount"));
        assertEquals(true, map.get("active"));
    }
    
    @Test
    @DisplayName("测试List转JSON")
    void testListToJson() {
        List<TestObject> list = Arrays.asList(
            new TestObject("test1", 25, new BigDecimal("100.50"), null),
            new TestObject("test2", 30, new BigDecimal("200.75"), null)
        );
        
        String json = JsonUtils.toJsonString(list);
        assertNotNull(json);
        assertTrue(json.startsWith("["));
        assertTrue(json.endsWith("]"));
        assertTrue(json.contains("test1"));
        assertTrue(json.contains("test2"));
    }
    
    @Test
    @DisplayName("测试JSON转List")
    void testJsonToList() {
        String json = "[{\"name\":\"test1\",\"age\":25},{\"name\":\"test2\",\"age\":30}]";
        
        List<TestObject> list = JsonUtils.fromJsonString(json, new TypeReference<List<TestObject>>() {});
        assertNotNull(list);
        assertEquals(2, list.size());
        assertEquals("test1", list.get(0).getName());
        assertEquals(25, list.get(0).getAge());
        assertEquals("test2", list.get(1).getName());
        assertEquals(30, list.get(1).getAge());
    }
    
    @Test
    @DisplayName("测试格式化JSON")
    void testPrettyPrint() {
        TestObject obj = new TestObject("test", 25, new BigDecimal("100.50"), null);
        
        String prettyJson = JsonUtils.toPrettyJsonString(obj);
        assertNotNull(prettyJson);
        assertTrue(prettyJson.contains("\n")); // 包含换行符
        assertTrue(prettyJson.contains("  ")); // 包含缩进
    }
    
    @Test
    @DisplayName("测试复杂对象序列化")
    void testComplexObjectSerialization() {
        ResponseResult<TestObject> response = ResponseResult.success(
            new TestObject("test", 25, new BigDecimal("100.50"), 
                          LocalDateTime.of(2024, 1, 1, 12, 0, 0))
        );
        
        String json = JsonUtils.toJsonString(response);
        assertNotNull(json);
        assertTrue(json.contains("\"success\":true"));
        assertTrue(json.contains("\"data\""));
        
        // 反序列化
        ResponseResult<TestObject> deserialized = JsonUtils.fromJsonString(json, 
            new TypeReference<ResponseResult<TestObject>>() {});
        assertNotNull(deserialized);
        assertTrue(deserialized.isSuccess());
        assertNotNull(deserialized.getData());
        assertEquals("test", deserialized.getData().getName());
    }
    
    @Test
    @DisplayName("测试日期时间序列化")
    void testDateTimeSerialization() {
        TestObject obj = new TestObject("test", 25, new BigDecimal("100.50"), 
                                       LocalDateTime.of(2024, 1, 1, 12, 30, 45));
        
        String json = JsonUtils.toJsonString(obj);
        assertNotNull(json);
        
        // 反序列化
        TestObject deserialized = JsonUtils.fromJsonString(json, TestObject.class);
        assertNotNull(deserialized);
        assertEquals(obj.getCreateTime(), deserialized.getCreateTime());
    }
    
    @Test
    @DisplayName("测试BigDecimal序列化")
    void testBigDecimalSerialization() {
        TestObject obj = new TestObject("test", 25, new BigDecimal("123.456789"), null);
        
        String json = JsonUtils.toJsonString(obj);
        assertNotNull(json);
        assertTrue(json.contains("123.456789")); // 应该保持原始精度
        
        // 反序列化
        TestObject deserialized = JsonUtils.fromJsonString(json, TestObject.class);
        assertNotNull(deserialized);
        assertEquals(obj.getAmount(), deserialized.getAmount());
    }
    
    @Test
    @DisplayName("测试空值处理")
    void testNullHandling() {
        // 测试null对象
        String nullJson = JsonUtils.toJsonString(null);
        assertNull(nullJson);
        
        // 测试包含null字段的对象
        TestObject objWithNull = new TestObject("test", null, null, null);
        String json = JsonUtils.toJsonString(objWithNull);
        assertNotNull(json);
        // 默认配置应该忽略null字段
        assertFalse(json.contains("\"age\":null"));
        
        // 反序列化
        TestObject deserialized = JsonUtils.fromJsonString(json, TestObject.class);
        assertNotNull(deserialized);
        assertEquals("test", deserialized.getName());
        assertNull(deserialized.getAge());
    }
    
    @Test
    @DisplayName("测试无效JSON处理")
    void testInvalidJsonHandling() {
        // 测试无效JSON字符串
        assertThrows(JsonOperationException.class, () -> {
            JsonUtils.fromJsonString("invalid json", TestObject.class);
        });
        
        // 测试空字符串
        assertThrows(JsonOperationException.class, () -> {
            JsonUtils.fromJsonString("", TestObject.class);
        });
        
        // 测试null字符串
        assertThrows(JsonOperationException.class, () -> {
            JsonUtils.fromJsonString(null, TestObject.class);
        });
    }
    
    @Test
    @DisplayName("测试类型转换错误")
    void testTypeConversionError() {
        String json = "{\"name\":\"test\",\"age\":\"not_a_number\"}";
        
        // 应该抛出异常，因为age字段不是数字
        assertThrows(JsonOperationException.class, () -> {
            JsonUtils.fromJsonString(json, TestObject.class);
        });
    }
    
    @Test
    @DisplayName("测试嵌套对象")
    void testNestedObjects() {
        Map<String, Object> nested = new HashMap<>();
        nested.put("user", new TestObject("test", 25, new BigDecimal("100.50"), null));
        nested.put("metadata", Map.of("version", "1.0", "timestamp", System.currentTimeMillis()));
        
        String json = JsonUtils.toJsonString(nested);
        assertNotNull(json);
        assertTrue(json.contains("\"user\""));
        assertTrue(json.contains("\"metadata\""));
        
        // 反序列化
        Map<String, Object> deserialized = JsonUtils.fromJsonString(json, 
            new TypeReference<Map<String, Object>>() {});
        assertNotNull(deserialized);
        assertTrue(deserialized.containsKey("user"));
        assertTrue(deserialized.containsKey("metadata"));
    }
    
    @Test
    @DisplayName("测试数组处理")
    void testArrayHandling() {
        String[] stringArray = {"test1", "test2", "test3"};
        
        String json = JsonUtils.toJsonString(stringArray);
        assertNotNull(json);
        assertTrue(json.startsWith("["));
        assertTrue(json.endsWith("]"));
        
        // 反序列化
        String[] deserialized = JsonUtils.fromJsonString(json, String[].class);
        assertNotNull(deserialized);
        assertArrayEquals(stringArray, deserialized);
    }
    
    @Test
    @DisplayName("测试特殊字符处理")
    void testSpecialCharacterHandling() {
        TestObject obj = new TestObject("test\"with\\quotes\nand\tspecial\rchars", 25, null, null);
        
        String json = JsonUtils.toJsonString(obj);
        assertNotNull(json);
        assertTrue(json.contains("\\\""));  // 引号应该被转义
        assertTrue(json.contains("\\\\"));  // 反斜杠应该被转义
        assertTrue(json.contains("\\n"));   // 换行符应该被转义
        
        // 反序列化
        TestObject deserialized = JsonUtils.fromJsonString(json, TestObject.class);
        assertNotNull(deserialized);
        assertEquals(obj.getName(), deserialized.getName());
    }
}
