package com.trading.common.utils;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * NumberUtils 单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@DisplayName("数字工具类测试")
class NumberUtilsTest {
    
    @Test
    @DisplayName("测试BigDecimal创建")
    void testCreateBigDecimal() {
        // 测试字符串创建
        BigDecimal fromString = NumberUtils.toBigDecimal("123.456");
        assertEquals(new BigDecimal("123.456"), fromString);
        
        // 测试double创建
        BigDecimal fromDouble = NumberUtils.toBigDecimal(123.456);
        assertNotNull(fromDouble);
        
        // 测试int创建
        BigDecimal fromInt = NumberUtils.toBigDecimal(123);
        assertEquals(new BigDecimal("123"), fromInt);
        
        // 测试long创建
        BigDecimal fromLong = NumberUtils.toBigDecimal(123L);
        assertEquals(new BigDecimal("123"), fromLong);
    }
    
    @Test
    @DisplayName("测试数字比较")
    void testNumberComparison() {
        BigDecimal num1 = new BigDecimal("10.5");
        BigDecimal num2 = new BigDecimal("20.3");
        BigDecimal num3 = new BigDecimal("10.5");
        
        // 测试相等比较
        assertTrue(NumberUtils.isEqual(num1, num3));
        assertFalse(NumberUtils.isEqual(num1, num2));
        
        // 测试大于比较
        assertTrue(NumberUtils.isGreaterThan(num2, num1));
        assertFalse(NumberUtils.isGreaterThan(num1, num2));
        
        // 测试小于比较
        assertTrue(NumberUtils.isLessThan(num1, num2));
        assertFalse(NumberUtils.isLessThan(num2, num1));
        
        // 测试大于等于比较
        assertTrue(NumberUtils.isGreaterThanOrEqual(num2, num1));
        assertTrue(NumberUtils.isGreaterThanOrEqual(num1, num3));
        
        // 测试小于等于比较
        assertTrue(NumberUtils.isLessThanOrEqual(num1, num2));
        assertTrue(NumberUtils.isLessThanOrEqual(num1, num3));
    }
    
    @Test
    @DisplayName("测试零值和正负数判断")
    void testZeroAndSignCheck() {
        BigDecimal zero = BigDecimal.ZERO;
        BigDecimal positive = new BigDecimal("10.5");
        BigDecimal negative = new BigDecimal("-5.2");
        
        // 测试零值判断
        assertTrue(NumberUtils.isZero(zero));
        assertFalse(NumberUtils.isZero(positive));
        assertFalse(NumberUtils.isZero(negative));
        
        // 测试正数判断
        assertTrue(NumberUtils.isPositive(positive));
        assertFalse(NumberUtils.isPositive(zero));
        assertFalse(NumberUtils.isPositive(negative));
        
        // 测试负数判断
        assertTrue(NumberUtils.isNegative(negative));
        assertFalse(NumberUtils.isNegative(zero));
        assertFalse(NumberUtils.isNegative(positive));
    }
    
    @Test
    @DisplayName("测试数学运算")
    void testMathOperations() {
        BigDecimal num1 = new BigDecimal("10.5");
        BigDecimal num2 = new BigDecimal("3.2");
        
        // 测试加法
        BigDecimal sum = NumberUtils.add(num1, num2);
        assertEquals(new BigDecimal("13.7"), sum);
        
        // 测试减法
        BigDecimal diff = NumberUtils.subtract(num1, num2);
        assertEquals(new BigDecimal("7.3"), diff);
        
        // 测试乘法
        BigDecimal product = NumberUtils.multiply(num1, num2);
        assertEquals(new BigDecimal("33.60"), product);
        
        // 测试除法
        BigDecimal quotient = NumberUtils.divide(num1, num2, 2, RoundingMode.HALF_UP);
        assertEquals(new BigDecimal("3.28"), quotient);
    }
    
    @Test
    @DisplayName("测试精度处理")
    void testPrecisionHandling() {
        BigDecimal number = new BigDecimal("123.456789");
        
        // 测试四舍五入
        BigDecimal rounded2 = NumberUtils.setScale(number, 2, RoundingMode.HALF_UP);
        assertEquals(new BigDecimal("123.46"), rounded2);
        
        BigDecimal rounded4 = NumberUtils.setScale(number, 4, RoundingMode.HALF_UP);
        assertEquals(new BigDecimal("123.4568"), rounded4);
        
        // 测试向下取整
        BigDecimal floor = NumberUtils.setScale(number, 2, RoundingMode.DOWN);
        assertEquals(new BigDecimal("123.45"), floor);
        
        // 测试向上取整
        BigDecimal ceil = NumberUtils.setScale(number, 2, RoundingMode.UP);
        assertEquals(new BigDecimal("123.46"), ceil);
    }
    
    @Test
    @DisplayName("测试价格精度处理")
    void testPricePrecision() {
        BigDecimal price = new BigDecimal("1234.56789123");

        // 测试价格精度（8位小数）
        String pricePrecision = NumberUtils.formatPrice(price);
        assertEquals("1234.56789123", pricePrecision);

        // 测试超出精度的价格
        BigDecimal longPrice = new BigDecimal("1234.567891234567");
        String formattedPrice = NumberUtils.formatPrice(longPrice);
        assertEquals("1234.56789123", formattedPrice);
    }
    
    @Test
    @DisplayName("测试数量精度处理")
    void testQuantityPrecision() {
        BigDecimal quantity = new BigDecimal("123.456789");

        // 测试数量精度（8位小数）
        String quantityPrecision = NumberUtils.formatQuantity(quantity);
        assertEquals("123.45678900", quantityPrecision);

        // 测试超出精度的数量
        BigDecimal longQuantity = new BigDecimal("123.4567891234");
        String formattedQuantity = NumberUtils.formatQuantity(longQuantity);
        assertEquals("123.45678912", formattedQuantity);
    }
    
    @Test
    @DisplayName("测试百分比计算")
    void testPercentageCalculation() {
        BigDecimal value = new BigDecimal("25");
        BigDecimal total = new BigDecimal("100");
        
        // 测试百分比计算
        BigDecimal percentage = NumberUtils.calculatePercentage(value, total);
        assertEquals(new BigDecimal("0.25000000"), percentage);
        
        // 测试变化百分比
        BigDecimal oldValue = new BigDecimal("100");
        BigDecimal newValue = new BigDecimal("120");
        BigDecimal changePercent = NumberUtils.calculateChangePercentage(oldValue, newValue);
        assertEquals(new BigDecimal("20.00000000"), changePercent);

        // 测试负变化
        BigDecimal decreasePercent = NumberUtils.calculateChangePercentage(newValue, oldValue);
        assertEquals(new BigDecimal("-16.66666700"), decreasePercent);
    }
    
    @Test
    @DisplayName("测试最大最小值")
    void testMinMax() {
        BigDecimal num1 = new BigDecimal("10.5");
        BigDecimal num2 = new BigDecimal("20.3");
        BigDecimal num3 = new BigDecimal("5.7");
        
        // 测试最大值
        BigDecimal max = NumberUtils.max(num1, num2);
        assertEquals(num2, max);
        
        // 测试最小值
        BigDecimal min = NumberUtils.min(num1, num3);
        assertEquals(num3, min);
        
        // 测试多个数的最大值
        BigDecimal maxOfThree = NumberUtils.max(num1, num2, num3);
        assertEquals(num2, maxOfThree);
        
        // 测试多个数的最小值
        BigDecimal minOfThree = NumberUtils.min(num1, num2, num3);
        assertEquals(num3, minOfThree);
    }
    
    @Test
    @DisplayName("测试绝对值")
    void testAbsoluteValue() {
        BigDecimal positive = new BigDecimal("10.5");
        BigDecimal negative = new BigDecimal("-10.5");
        BigDecimal zero = BigDecimal.ZERO;
        
        assertEquals(positive, NumberUtils.abs(positive));
        assertEquals(positive, NumberUtils.abs(negative));
        assertEquals(zero, NumberUtils.abs(zero));
    }
    
    @Test
    @DisplayName("测试字符串转换")
    void testStringConversion() {
        BigDecimal number = new BigDecimal("123.456");
        
        // 测试普通字符串转换
        String str = NumberUtils.toPlainString(number);
        assertEquals("123.456", str);

        // 测试格式化字符串转换
        String formatted = NumberUtils.toFormattedString(number, 2);
        assertEquals("123.46", formatted);

        // 测试科学计数法
        BigDecimal largeNumber = new BigDecimal("1234567890.123456");
        String plain = NumberUtils.toPlainString(largeNumber);
        assertEquals("1234567890.123456", plain);
    }
    
    @Test
    @DisplayName("测试空值处理")
    void testNullHandling() {
        // 测试空值输入
        assertNull(NumberUtils.toBigDecimal((String) null));
        assertEquals("0", NumberUtils.toPlainString(null));
        assertEquals(BigDecimal.ZERO, NumberUtils.abs(null));
        
        // 测试空字符串
        assertNull(NumberUtils.toBigDecimal(""));
        assertNull(NumberUtils.toBigDecimal("   "));
        
        // 测试无效数字字符串
        assertThrows(IllegalArgumentException.class, () -> {
            NumberUtils.toBigDecimal("invalid");
        });
    }
    
    @Test
    @DisplayName("测试除零处理")
    void testDivisionByZero() {
        BigDecimal dividend = new BigDecimal("10");
        BigDecimal zero = BigDecimal.ZERO;
        
        // 测试除零异常
        assertThrows(ArithmeticException.class, () -> {
            NumberUtils.divide(dividend, zero, 2, RoundingMode.HALF_UP);
        });
        
        // 测试百分比计算中的除零
        assertThrows(ArithmeticException.class, () -> {
            NumberUtils.calculatePercentage(dividend, zero);
        });
    }
    
    @Test
    @DisplayName("测试边界值")
    void testBoundaryValues() {
        // 测试非常大的数
        BigDecimal veryLarge = new BigDecimal("999999999999999999.99999999");
        assertNotNull(NumberUtils.formatPrice(veryLarge));
        
        // 测试非常小的数
        BigDecimal verySmall = new BigDecimal("0.00000001");
        assertNotNull(NumberUtils.formatQuantity(verySmall));
        
        // 测试精度边界
        BigDecimal precisionTest = new BigDecimal("1.123456789012345");
        BigDecimal formatted = NumberUtils.setScale(precisionTest, 8, RoundingMode.HALF_UP);
        assertEquals(8, formatted.scale());
    }
}
