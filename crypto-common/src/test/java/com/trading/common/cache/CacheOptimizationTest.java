package com.trading.common.cache;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.trading.common.gc.MemoryAllocationOptimizer;
import com.trading.common.monitoring.UnifiedMetricsService;
import com.trading.common.retry.UnifiedRetryService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;

import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CacheOptimizationTest {

    private static final Logger log = LoggerFactory.getLogger(CacheOptimizationTest.class);

    private MultiLevelCacheManagerImpl multiLevelCacheManager;
    private CacheKeyOptimizer cacheKeyOptimizer;
    private MemoryAllocationOptimizer memoryAllocationOptimizer;

    @Mock
    private RedisTemplate<String, String> redisTemplate;
    @Mock
    private ValueOperations<String, String> valueOperations;
    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private UnifiedRetryService retryService;
    @Mock
    private UnifiedMetricsService metricsService;

    @BeforeEach
    public void setUp() {
        when(redisTemplate.opsForValue()).thenReturn(valueOperations);
        when(retryService.executeRedisOperation(anyString(), any(java.util.function.Supplier.class))).thenAnswer(invocation -> {
            java.util.function.Supplier<?> op = invocation.getArgument(1);
            return op.get();
        });

        multiLevelCacheManager = new MultiLevelCacheManagerImpl(redisTemplate, objectMapper, retryService, metricsService);
        
        memoryAllocationOptimizer = new MemoryAllocationOptimizer();
        memoryAllocationOptimizer.initialize();

        cacheKeyOptimizer = new CacheKeyOptimizer();
        try {
            java.lang.reflect.Field memoryField = CacheKeyOptimizer.class.getDeclaredField("memoryOptimizer");
            memoryField.setAccessible(true);
            memoryField.set(cacheKeyOptimizer, memoryAllocationOptimizer);
        } catch (Exception e) {
            throw new RuntimeException("Failed to setup CacheKeyOptimizer", e);
        }
    }

    @Test
    public void testMultiLevelCachePerformance() {
        log.info("Starting multi-level cache performance test...");
        int operationCount = 500;
        String[] symbols = {"BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT"};
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < operationCount; i++) {
            String symbol = symbols[i % symbols.length];
            String key = cacheKeyOptimizer.createKlineKey(symbol, "1m");
            String value = "test-data-" + i;
            multiLevelCacheManager.put(key, value, Duration.ofMinutes(10));
        }

        long writeTime = System.currentTimeMillis() - startTime;
        startTime = System.currentTimeMillis();
        int hits = 0;

        for (int i = 0; i < operationCount; i++) {
            String symbol = symbols[i % symbols.length];
            String key = cacheKeyOptimizer.createKlineKey(symbol, "1m");
            if (multiLevelCacheManager.get(key, String.class).isPresent()) {
                hits++;
            }
        }

        long readTime = System.currentTimeMillis() - startTime;
        MultiLevelCacheManager.CacheStats stats = (MultiLevelCacheManager.CacheStats) multiLevelCacheManager.getStats();

        log.info("Multi-level cache performance test results: Operation count={}, Write time={}ms, Read time={}ms, Hits={}, Rate={:.1f}%, Stats={}",
                operationCount, writeTime, readTime, hits, (double) hits / operationCount * 100, stats);
    }

    @Test
    public void testConcurrentCachePerformance() throws InterruptedException {
        log.info("Starting concurrent cache performance test...");
        int threadCount = 5;
        int operationsPerThread = 100;
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger totalOperations = new AtomicInteger(0);
        AtomicLong totalTime = new AtomicLong(0);
        String[] symbols = {"BTCUSDT", "ETHUSDT", "ADAUSDT", "DOTUSDT", "LINKUSDT"};
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            new Thread(() -> {
                try {
                    long threadStart = System.currentTimeMillis();
                    for (int j = 0; j < operationsPerThread; j++) {
                        String symbol = symbols[j % symbols.length];
                        String key = cacheKeyOptimizer.createKlineKey(symbol, "1m");
                        String value = "thread-" + threadId + "-data-" + j;
                        multiLevelCacheManager.put(key, value, Duration.ofMinutes(10));
                        multiLevelCacheManager.get(key, String.class);
                        totalOperations.incrementAndGet();
                    }
                    totalTime.addAndGet(System.currentTimeMillis() - threadStart);
                } finally {
                    latch.countDown();
                }
            }).start();
        }

        boolean completed = latch.await(30, TimeUnit.SECONDS);
        long overallTime = System.currentTimeMillis() - startTime;
        MultiLevelCacheManager.CacheStats stats = (MultiLevelCacheManager.CacheStats) multiLevelCacheManager.getStats();

        log.info("Concurrent cache performance test results: Completed={}, Operations={}, Time={}ms, Throughput={:.2f} ops/sec, Stats={}",
                completed, totalOperations.get(), overallTime, (double) totalOperations.get() * 1000 / overallTime, stats);
    }
    
    @Test
    public void testBatchOperationPerformance() {
        log.info("Starting batch operation performance test...");
        int batchSize = 200;
        Map<String, Object> batchData = new HashMap<>();
        for (int i = 0; i < batchSize; i++) {
            String key = cacheKeyOptimizer.createKlineKey("BTCUSDT", "1m") + "_" + i;
            batchData.put(key, "batch-data-" + i);
        }

        long startTime = System.currentTimeMillis();
        CompletableFuture<Void> putFuture = multiLevelCacheManager.putBatch(batchData, Duration.ofMinutes(10));

        try {
            putFuture.get(10, TimeUnit.SECONDS);
            long writeTime = System.currentTimeMillis() - startTime;

            startTime = System.currentTimeMillis();
            Set<String> keys = new HashSet<>(batchData.keySet());
            CompletableFuture<Map<String, String>> getFuture = multiLevelCacheManager.getBatch(keys, String.class);
            Map<String, String> results = getFuture.get(10, TimeUnit.SECONDS);
            long readTime = System.currentTimeMillis() - startTime;

            log.info("Batch operation performance test results: Size={}, Write time={}ms, Read time={}ms, Hits={}",
                    batchSize, writeTime, readTime, results.size());

        } catch (Exception e) {
            log.error("Batch operation performance test exception", e);
        }
    }
}