package com.trading.common.gc;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.management.GarbageCollectorMXBean;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * GC优化性能测试
 * 验证垃圾回收优化效果和内存分配优化
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class GCOptimizationTest {

    private static final Logger log = LoggerFactory.getLogger(GCOptimizationTest.class);

    private GCOptimizationManager gcOptimizationManager;
    private MemoryAllocationOptimizer memoryOptimizer;

    @BeforeEach
    public void setUp() {
        // 手动设置配置属性
        System.setProperty("app.performance.gc.monitoring.enabled", "true");
        System.setProperty("app.performance.gc.monitoring.interval-seconds", "5");
        System.setProperty("app.performance.gc.young-gc-threshold", "10");
        System.setProperty("app.performance.gc.full-gc-threshold", "0");
        System.setProperty("app.performance.memory.preallocation.enabled", "true");
        System.setProperty("app.performance.memory.preallocation.size-mb", "20");

        gcOptimizationManager = new GCOptimizationManager();
        gcOptimizationManager.initialize();

        memoryOptimizer = new MemoryAllocationOptimizer();
        memoryOptimizer.initialize();
    }

    @AfterEach
    public void tearDown() {
        if (gcOptimizationManager != null) {
            gcOptimizationManager.shutdown();
        }
    }

    /**
     * 测试GC监控功能
     */
    @Test
    public void testGCMonitoring() {
        log.info("开始GC监控测试...");

        // 获取初始GC统计
        GCOptimizationManager.GCStatistics initialStats = gcOptimizationManager.getStatistics();
        log.info("初始GC统计: {}", initialStats);

        // 创建一些对象触发GC
        createMemoryPressure(1000);

        // 等待GC监控执行
        try {
            Thread.sleep(6000); // 等待超过监控间隔
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 获取最终GC统计
        GCOptimizationManager.GCStatistics finalStats = gcOptimizationManager.getStatistics();
        log.info("最终GC统计: {}", finalStats);

        log.info("GC监控测试结果:");
        log.info("Young GC增量: {}", finalStats.getTotalYoungGcCount() - initialStats.getTotalYoungGcCount());
        log.info("Full GC增量: {}", finalStats.getTotalFullGcCount() - initialStats.getTotalFullGcCount());
        log.info("GC时间增量: {}ms", finalStats.getTotalGcTime() - initialStats.getTotalGcTime());
        log.info("堆内存使用率: {:.1f}%", finalStats.getHeapUsagePercent());

        if (finalStats.getTotalFullGcCount() == initialStats.getTotalFullGcCount()) {
            log.info("✅ GC监控测试通过 - 无Full GC发生");
        } else {
            log.warn("⚠️ 发生了Full GC，需要进一步优化");
        }
    }

    /**
     * 测试内存分配优化
     */
    @Test
    public void testMemoryAllocationOptimization() {
        log.info("开始内存分配优化测试...");

        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();

        // 强制垃圾回收
        System.gc();
        Thread.yield();

        long startMemory = memoryBean.getHeapMemoryUsage().getUsed();
        MemoryAllocationOptimizer.MemoryOptimizationStatistics initialStats = memoryOptimizer.getStatistics();

        // 执行优化的内存分配操作
        performOptimizedMemoryOperations(5000);

        long endMemory = memoryBean.getHeapMemoryUsage().getUsed();
        MemoryAllocationOptimizer.MemoryOptimizationStatistics finalStats = memoryOptimizer.getStatistics();

        long memoryIncrease = endMemory - startMemory;

        log.info("内存分配优化测试结果:");
        log.info("内存增长: {} MB", memoryIncrease / 1024 / 1024);
        log.info("优化统计: {}", finalStats);
        log.info("StringBuilder复用率: {:.1f}%", finalStats.getStringBuilderReuseRate());
        log.info("集合复用率: {:.1f}%", finalStats.getCollectionReuseRate());
        log.info("节省内存: {} KB", finalStats.getTotalMemorySaved() / 1024);

        if (finalStats.getStringBuilderReuseRate() >= 80.0) {
            log.info("✅ 内存分配优化测试通过 - StringBuilder复用率: {:.1f}%", 
                    finalStats.getStringBuilderReuseRate());
        } else {
            log.warn("⚠️ StringBuilder复用率较低: {:.1f}%", finalStats.getStringBuilderReuseRate());
        }
    }

    /**
     * 测试对象池性能
     */
    @Test
    public void testObjectPoolPerformance() {
        log.info("开始对象池性能测试...");

        int iterations = 10000;
        long startTime = System.currentTimeMillis();

        // 测试StringBuilder池
        for (int i = 0; i < iterations; i++) {
            StringBuilder sb = memoryOptimizer.borrowStringBuilder();
            sb.append("Test").append(i).append("_").append(System.currentTimeMillis());
            String result = sb.toString();
            memoryOptimizer.returnStringBuilder(sb);
            
            // 防止编译器优化
            if (result.length() < 0) {
                System.out.println("Unexpected result");
            }
        }

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        MemoryAllocationOptimizer.MemoryOptimizationStatistics stats = memoryOptimizer.getStatistics();

        log.info("对象池性能测试结果:");
        log.info("总耗时: {}ms", totalTime);
        log.info("平均每次操作: {:.3f}ms", (double) totalTime / iterations);
        log.info("StringBuilder复用率: {:.1f}%", stats.getStringBuilderReuseRate());
        log.info("当前池大小: {}", stats.getCurrentStringBuilderPoolSize());

        if (totalTime < iterations / 2) { // 平均每次操作少于0.5ms
            log.info("✅ 对象池性能测试通过 - 平均耗时: {:.3f}ms", (double) totalTime / iterations);
        } else {
            log.warn("⚠️ 对象池性能需要优化 - 平均耗时: {:.3f}ms", (double) totalTime / iterations);
        }
    }

    /**
     * 测试并发内存分配优化
     */
    @Test
    public void testConcurrentMemoryOptimization() throws InterruptedException {
        log.info("开始并发内存分配优化测试...");

        int threadCount = 10;
        int operationsPerThread = 1000;
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicLong totalOperations = new AtomicLong(0);

        long startTime = System.currentTimeMillis();

        // 启动多个线程并发执行内存分配操作
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            Thread thread = new Thread(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        // 使用优化的字符串创建
                        String result = memoryOptimizer.createOptimizedString("_", 
                                "thread", String.valueOf(threadId), 
                                "operation", String.valueOf(j),
                                "timestamp", String.valueOf(System.currentTimeMillis()));
                        
                        // 使用集合池
                        List<String> list = memoryOptimizer.borrowList();
                        list.add(result);
                        list.add("additional_data");
                        memoryOptimizer.returnList(list);
                        
                        totalOperations.incrementAndGet();
                    }
                } finally {
                    latch.countDown();
                }
            }, "memory-test-" + i);
            thread.start();
        }

        // 等待所有线程完成
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        MemoryAllocationOptimizer.MemoryOptimizationStatistics stats = memoryOptimizer.getStatistics();

        log.info("并发内存分配优化测试结果:");
        log.info("测试是否完成: {}", completed);
        log.info("总耗时: {}ms", totalTime);
        log.info("总操作数: {}", totalOperations.get());
        log.info("吞吐量: {:.2f} 操作/秒", (double) totalOperations.get() * 1000 / totalTime);
        log.info("优化统计: {}", stats);

        if (completed && stats.getStringBuilderReuseRate() >= 70.0) {
            log.info("✅ 并发内存分配优化测试通过 - 复用率: {:.1f}%", stats.getStringBuilderReuseRate());
        } else {
            log.warn("⚠️ 并发内存分配优化测试未完全通过");
        }
    }

    /**
     * 测试GC压力下的性能
     */
    @Test
    public void testPerformanceUnderGCPressure() {
        log.info("开始GC压力下的性能测试...");

        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        List<GarbageCollectorMXBean> gcBeans = ManagementFactory.getGarbageCollectorMXBeans();

        // 记录初始GC状态
        long initialGcCount = gcBeans.stream().mapToLong(GarbageCollectorMXBean::getCollectionCount).sum();
        long initialGcTime = gcBeans.stream().mapToLong(GarbageCollectorMXBean::getCollectionTime).sum();
        long startMemory = memoryBean.getHeapMemoryUsage().getUsed();

        long startTime = System.currentTimeMillis();

        // 在GC压力下执行操作
        for (int i = 0; i < 1000; i++) {
            // 创建内存压力
            createMemoryPressure(100);
            
            // 执行优化的内存操作
            performOptimizedMemoryOperations(50);
            
            // 定期建议GC
            if (i % 100 == 0) {
                gcOptimizationManager.suggestGC();
            }
        }

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        // 记录最终GC状态
        long finalGcCount = gcBeans.stream().mapToLong(GarbageCollectorMXBean::getCollectionCount).sum();
        long finalGcTime = gcBeans.stream().mapToLong(GarbageCollectorMXBean::getCollectionTime).sum();
        long endMemory = memoryBean.getHeapMemoryUsage().getUsed();

        long gcCountIncrease = finalGcCount - initialGcCount;
        long gcTimeIncrease = finalGcTime - initialGcTime;
        long memoryIncrease = endMemory - startMemory;

        log.info("GC压力下的性能测试结果:");
        log.info("总耗时: {}ms", totalTime);
        log.info("GC次数增加: {}", gcCountIncrease);
        log.info("GC时间增加: {}ms", gcTimeIncrease);
        log.info("内存增长: {} MB", memoryIncrease / 1024 / 1024);
        log.info("GC时间占比: {:.2f}%", (double) gcTimeIncrease / totalTime * 100);

        GCOptimizationManager.GCStatistics gcStats = gcOptimizationManager.getStatistics();
        log.info("GC优化统计: {}", gcStats);

        if (gcTimeIncrease < totalTime * 0.1) { // GC时间少于总时间的10%
            log.info("✅ GC压力下的性能测试通过 - GC时间占比: {:.2f}%", 
                    (double) gcTimeIncrease / totalTime * 100);
        } else {
            log.warn("⚠️ GC时间占比过高: {:.2f}%", (double) gcTimeIncrease / totalTime * 100);
        }
    }

    /**
     * 创建内存压力
     */
    private void createMemoryPressure(int objectCount) {
        List<Object> objects = new ArrayList<>();
        for (int i = 0; i < objectCount; i++) {
            // 创建各种大小的对象
            objects.add(new byte[1024 + i % 1000]);
            objects.add(new StringBuilder(256).append("pressure_test_").append(i));
            objects.add(new ArrayList<>(i % 100));
        }
        // 让对象超出作用域，成为垃圾回收的候选
    }

    /**
     * 执行优化的内存操作
     */
    private void performOptimizedMemoryOperations(int operationCount) {
        for (int i = 0; i < operationCount; i++) {
            // 使用优化的字符串创建
            String optimizedString = memoryOptimizer.createOptimizedString("_", 
                    "operation", String.valueOf(i), "timestamp", String.valueOf(System.currentTimeMillis()));
            
            // 使用StringBuilder池
            StringBuilder sb = memoryOptimizer.borrowStringBuilder();
            sb.append("optimized_").append(i).append("_").append(optimizedString.hashCode());
            String result = sb.toString();
            memoryOptimizer.returnStringBuilder(sb);
            
            // 使用集合池
            List<String> list = memoryOptimizer.borrowList();
            list.add(result);
            list.add(optimizedString);
            memoryOptimizer.returnList(list);
            
            // 防止编译器优化
            if (result.length() < 0) {
                System.out.println("Unexpected result");
            }
        }
    }
}
