package com.trading.common;

import org.junit.platform.suite.api.SelectPackages;
import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;

/**
 * crypto-common模块测试套件
 * 运行所有单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@Suite
@SuiteDisplayName("Crypto Common Module Test Suite")
@SelectPackages({
    "com.trading.common.utils",
    "com.trading.common.exception", 
    "com.trading.common.dto",
    "com.trading.common.constant",
    "com.trading.common.enums",
    "com.trading.common.config"
})
public class CryptoCommonTestSuite {
    // 测试套件类，无需实现内容
}
