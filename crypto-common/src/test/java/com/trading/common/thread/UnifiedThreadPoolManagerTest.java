package com.trading.common.thread;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.List;
import java.util.ArrayList;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

/**
 * 统一线程池管理器性能测试
 * 验证线程池整合和虚拟线程优化效果
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class UnifiedThreadPoolManagerTest {

    private static final Logger log = LoggerFactory.getLogger(UnifiedThreadPoolManagerTest.class);

    private UnifiedThreadPoolManager threadPoolManager;

    @BeforeEach
    public void setUp() {
        // 手动设置配置属性
        System.setProperty("app.performance.core-pool-size", "10");
        System.setProperty("app.performance.max-pool-size", "50");
        System.setProperty("app.performance.queue-capacity", "1000");
        System.setProperty("app.performance.keep-alive-seconds", "60");
        System.setProperty("market-data.processor.virtual-threads.enabled", "true");
        System.setProperty("market-data.processor.virtual-threads.max-threads", "200");

        threadPoolManager = new UnifiedThreadPoolManager();
        threadPoolManager.initialize();
    }

    @AfterEach
    public void tearDown() {
        if (threadPoolManager != null) {
            threadPoolManager.shutdown();
        }
    }

    /**
     * 测试虚拟线程性能
     */
    @Test
    public void testVirtualThreadPerformance() {
        log.info("开始虚拟线程性能测试...");

        int taskCount = 1000;
        CountDownLatch latch = new CountDownLatch(taskCount);
        AtomicInteger completedTasks = new AtomicInteger(0);
        AtomicLong totalExecutionTime = new AtomicLong(0);

        long startTime = System.currentTimeMillis();

        // 提交大量虚拟线程任务
        for (int i = 0; i < taskCount; i++) {
            final int taskId = i;
            threadPoolManager.submitToVirtualThread(() -> {
                long taskStart = System.currentTimeMillis();
                
                try {
                    // 模拟I/O密集型任务 - 使用真实计算替代Thread.sleep
                    double result = 0;
                    for (int k = 0; k < 10000; k++) {
                        result += Math.sqrt(taskId * 1000 + k);
                        result += Math.sin(result);
                    }

                    long taskEnd = System.currentTimeMillis();
                    totalExecutionTime.addAndGet(taskEnd - taskStart);
                    completedTasks.incrementAndGet();

                } catch (Exception e) {
                    log.error("任务执行异常", e);
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            // 等待所有任务完成
            boolean completed = latch.await(30, TimeUnit.SECONDS);
            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;

            log.info("虚拟线程性能测试结果:");
            log.info("任务总数: {}", taskCount);
            log.info("完成任务数: {}", completedTasks.get());
            log.info("总耗时: {}ms", totalTime);
            log.info("平均任务执行时间: {}ms", totalExecutionTime.get() / completedTasks.get());
            log.info("任务吞吐量: {:.2f} 任务/秒", (double) completedTasks.get() * 1000 / totalTime);
            log.info("所有任务是否完成: {}", completed);

            if (completed && completedTasks.get() == taskCount) {
                log.info("✅ 虚拟线程性能测试通过");
            } else {
                log.warn("⚠️ 虚拟线程性能测试未完全通过");
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("虚拟线程性能测试被中断", e);
        }
    }

    /**
     * 测试数据处理线程池性能
     */
    @Test
    public void testDataProcessingThreadPoolPerformance() {
        log.info("开始数据处理线程池性能测试...");

        int taskCount = 500;
        CountDownLatch latch = new CountDownLatch(taskCount);
        AtomicInteger completedTasks = new AtomicInteger(0);

        long startTime = System.currentTimeMillis();

        // 提交数据处理任务
        for (int i = 0; i < taskCount; i++) {
            final int taskId = i;
            threadPoolManager.submitToDataProcessing(() -> {
                try {
                    // 模拟数据处理任务
                    processMarketData(taskId);
                    completedTasks.incrementAndGet();
                } catch (Exception e) {
                    log.error("数据处理任务失败: taskId={}", taskId, e);
                } finally {
                    latch.countDown();
                }
            });
        }

        try {
            boolean completed = latch.await(20, TimeUnit.SECONDS);
            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;

            log.info("数据处理线程池性能测试结果:");
            log.info("任务总数: {}", taskCount);
            log.info("完成任务数: {}", completedTasks.get());
            log.info("总耗时: {}ms", totalTime);
            log.info("任务吞吐量: {:.2f} 任务/秒", (double) completedTasks.get() * 1000 / totalTime);

            if (completed && completedTasks.get() == taskCount) {
                log.info("✅ 数据处理线程池性能测试通过");
            } else {
                log.warn("⚠️ 数据处理线程池性能测试未完全通过");
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("数据处理线程池性能测试被中断", e);
        }
    }

    /**
     * 测试调度任务性能
     */
    @Test
    public void testScheduledTaskPerformance() {
        log.info("开始调度任务性能测试...");

        AtomicInteger executionCount = new AtomicInteger(0);
        CountDownLatch latch = new CountDownLatch(10);

        // 调度定期任务
        threadPoolManager.scheduleAtFixedRate(() -> {
            int count = executionCount.incrementAndGet();
            log.debug("调度任务执行: count={}", count);
            latch.countDown();
        }, 100, 100, TimeUnit.MILLISECONDS);

        try {
            boolean completed = latch.await(5, TimeUnit.SECONDS);
            
            log.info("调度任务性能测试结果:");
            log.info("预期执行次数: 10");
            log.info("实际执行次数: {}", executionCount.get());
            log.info("任务是否按时完成: {}", completed);

            if (completed && executionCount.get() >= 10) {
                log.info("✅ 调度任务性能测试通过");
            } else {
                log.warn("⚠️ 调度任务性能测试未完全通过");
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("调度任务性能测试被中断", e);
        }
    }

    /**
     * 测试混合负载性能
     */
    @Test
    public void testMixedWorkloadPerformance() {
        log.info("开始混合负载性能测试...");

        int virtualThreadTasks = 200;
        int dataProcessingTasks = 100;
        int scheduledTasks = 5;

        CountDownLatch virtualLatch = new CountDownLatch(virtualThreadTasks);
        CountDownLatch dataLatch = new CountDownLatch(dataProcessingTasks);
        CountDownLatch scheduledLatch = new CountDownLatch(scheduledTasks);

        AtomicInteger virtualCompleted = new AtomicInteger(0);
        AtomicInteger dataCompleted = new AtomicInteger(0);
        AtomicInteger scheduledCompleted = new AtomicInteger(0);

        long startTime = System.currentTimeMillis();

        // 提交虚拟线程任务
        for (int i = 0; i < virtualThreadTasks; i++) {
            final int taskId = i;
            threadPoolManager.submitToVirtualThread(() -> {
                try {
                    // 使用真实计算替代Thread.sleep
                    double result = 0;
                    for (int k = 0; k < 5000; k++) {
                        result += Math.sqrt(taskId + k);
                    }
                    virtualCompleted.incrementAndGet();
                } catch (Exception e) {
                    log.error("虚拟线程任务异常", e);
                } finally {
                    virtualLatch.countDown();
                }
            });
        }

        // 提交数据处理任务
        for (int i = 0; i < dataProcessingTasks; i++) {
            final int taskId = i;
            threadPoolManager.submitToDataProcessing(() -> {
                try {
                    processMarketData(taskId);
                    dataCompleted.incrementAndGet();
                } finally {
                    dataLatch.countDown();
                }
            });
        }

        // 提交调度任务
        threadPoolManager.scheduleAtFixedRate(() -> {
            scheduledCompleted.incrementAndGet();
            scheduledLatch.countDown();
        }, 50, 200, TimeUnit.MILLISECONDS);

        try {
            boolean virtualDone = virtualLatch.await(15, TimeUnit.SECONDS);
            boolean dataDone = dataLatch.await(15, TimeUnit.SECONDS);
            boolean scheduledDone = scheduledLatch.await(5, TimeUnit.SECONDS);

            long endTime = System.currentTimeMillis();
            long totalTime = endTime - startTime;

            log.info("混合负载性能测试结果:");
            log.info("总耗时: {}ms", totalTime);
            log.info("虚拟线程任务: {}/{} ({})", virtualCompleted.get(), virtualThreadTasks, virtualDone ? "完成" : "未完成");
            log.info("数据处理任务: {}/{} ({})", dataCompleted.get(), dataProcessingTasks, dataDone ? "完成" : "未完成");
            log.info("调度任务: {}/{} ({})", scheduledCompleted.get(), scheduledTasks, scheduledDone ? "完成" : "未完成");

            // 输出线程池统计信息
            UnifiedThreadPoolManager.ThreadPoolStatistics stats = threadPoolManager.getStatistics();
            log.info("线程池统计: {}", stats);

            if (virtualDone && dataDone && scheduledDone) {
                log.info("✅ 混合负载性能测试通过");
            } else {
                log.warn("⚠️ 混合负载性能测试未完全通过");
            }

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("混合负载性能测试被中断", e);
        }
    }

    /**
     * 测试线程池核心指标
     */
    @Test
    public void testThreadPoolMetrics() throws Exception {
        log.info("开始线程池指标测试...");

        int taskCount = 10;
        
        List<CompletableFuture<Void>> futures = new ArrayList<>();

        // 提交10个任务到数据处理线程池
        for (int i = 0; i < taskCount; i++) {
            futures.add(threadPoolManager.submitToDataProcessing(() -> {
                try {
                    // 模拟短时间工作
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }));
        }

        // 等待所有任务及其whenComplete回调完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get(5, TimeUnit.SECONDS);

        // 获取并验证统计信息
        UnifiedThreadPoolManager.ThreadPoolStatistics stats = threadPoolManager.getStatistics();
        log.info("线程池统计信息: {}", stats);

        assertEquals(taskCount, stats.getTotalTasksSubmitted(), "提交的任务总数应该正确");
        assertEquals(taskCount, stats.getTotalTasksCompleted(), "完成的任务总数应该正确");
        assertEquals(0, stats.getTotalTasksFailed(), "失败的任务总数应该为0");
    }

    /**
     * 模拟市场数据处理
     */
    private void processMarketData(int taskId) {
        try {
            // 模拟JSON解析
            Thread.sleep(2);
            
            // 模拟数据验证
            if (taskId % 100 == 0) {
                Thread.sleep(1);
            }
            
            // 模拟数据转换
            double price = 50000 + (taskId % 1000);
            double volume = 1.0 + (taskId % 10);
            
            // 模拟计算
            double value = price * volume;
            
            // 防止编译器优化
            if (value < 0) {
                System.out.println("Unexpected value: " + value);
            }
            
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
    }
}
