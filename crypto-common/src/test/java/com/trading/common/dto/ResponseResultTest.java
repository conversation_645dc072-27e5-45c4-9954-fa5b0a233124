package com.trading.common.dto;

import com.trading.common.constant.ErrorCodes;
import com.trading.common.constant.SystemConstants;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * ResponseResult 单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@DisplayName("响应结果封装测试")
class ResponseResultTest {
    
    @Test
    @DisplayName("测试成功响应创建")
    void testSuccessResponse() {
        // 测试无数据成功响应
        ResponseResult<Void> voidResult = ResponseResult.success();
        assertTrue(voidResult.isSuccess());
        assertEquals(ErrorCodes.SUCCESS, voidResult.getCode());
        assertEquals(SystemConstants.SUCCESS_MESSAGE, voidResult.getMessage());
        assertNull(voidResult.getData());
        assertNotNull(voidResult.getTimestamp());

        // 测试带数据成功响应
        String testData = "test data";
        ResponseResult<String> dataResult = ResponseResult.success(testData);
        assertTrue(dataResult.isSuccess());
        assertEquals(ErrorCodes.SUCCESS, dataResult.getCode());
        assertEquals(SystemConstants.SUCCESS_MESSAGE, dataResult.getMessage());
        assertEquals(testData, dataResult.getData());

        // 测试带自定义消息的成功响应
        ResponseResult<String> customResult = ResponseResult.success("自定义成功消息", testData);
        assertTrue(customResult.isSuccess());
        assertEquals(ErrorCodes.SUCCESS, customResult.getCode());
        assertEquals("自定义成功消息", customResult.getMessage());
        assertEquals(testData, customResult.getData());
    }
    
    @Test
    @DisplayName("测试失败响应创建")
    void testFailureResponse() {
        // 测试基本失败响应
        ResponseResult<Void> basicFailure = ResponseResult.fail("操作失败");
        assertFalse(basicFailure.isSuccess());
        assertTrue(basicFailure.isFail());
        assertEquals(SystemConstants.FAIL_CODE, basicFailure.getCode());
        assertEquals("操作失败", basicFailure.getMessage());
        assertNull(basicFailure.getData());

        // 测试带错误码的失败响应
        ResponseResult<Void> codeFailure = ResponseResult.fail(ErrorCodes.INVALID_PARAMETER, "参数无效");
        assertFalse(codeFailure.isSuccess());
        assertEquals(ErrorCodes.INVALID_PARAMETER, codeFailure.getCode());
        assertEquals("参数无效", codeFailure.getMessage());

        // 测试带错误详情的失败响应
        String errorData = "error details";
        ResponseResult<Void> dataFailure = ResponseResult.fail(ErrorCodes.SYSTEM_ERROR, "系统错误", errorData);
        assertFalse(dataFailure.isSuccess());
        assertEquals(ErrorCodes.SYSTEM_ERROR, dataFailure.getCode());
        assertEquals("系统错误", dataFailure.getMessage());
        assertEquals(errorData, dataFailure.getErrorDetails());
    }
    
    @Test
    @DisplayName("测试不同数据类型的响应")
    void testDifferentDataTypes() {
        // 测试字符串数据
        ResponseResult<String> stringResult = ResponseResult.success("test string");
        assertEquals("test string", stringResult.getData());
        assertTrue(stringResult.getData() instanceof String);
        
        // 测试数字数据
        ResponseResult<Integer> intResult = ResponseResult.success(12345);
        assertEquals(Integer.valueOf(12345), intResult.getData());
        assertTrue(intResult.getData() instanceof Integer);
        
        // 测试列表数据
        List<String> testList = Arrays.asList("item1", "item2", "item3");
        ResponseResult<List<String>> listResult = ResponseResult.success(testList);
        assertEquals(testList, listResult.getData());
        assertTrue(listResult.getData() instanceof List);
        assertEquals(3, listResult.getData().size());
        
        // 测试复杂对象数据
        TestObject testObject = new TestObject("test", 25);
        ResponseResult<TestObject> objectResult = ResponseResult.success(testObject);
        assertEquals(testObject, objectResult.getData());
        assertTrue(objectResult.getData() instanceof TestObject);
        assertEquals("test", objectResult.getData().getName());
    }
    
    @Test
    @DisplayName("测试响应状态判断")
    void testResponseStatusCheck() {
        ResponseResult<String> successResult = ResponseResult.success("success");
        ResponseResult<String> failureResult = ResponseResult.fail("failure");

        // 测试成功状态判断
        assertTrue(successResult.isSuccess());
        assertFalse(successResult.isFail());

        // 测试失败状态判断
        assertFalse(failureResult.isSuccess());
        assertTrue(failureResult.isFail());
    }
    
    @Test
    @DisplayName("测试时间戳生成")
    void testTimestampGeneration() {
        LocalDateTime beforeCreate = LocalDateTime.now();
        ResponseResult<String> result = ResponseResult.success("test");
        LocalDateTime afterCreate = LocalDateTime.now();

        assertNotNull(result.getTimestamp());
        assertTrue(result.getTimestamp().isAfter(beforeCreate) || result.getTimestamp().isEqual(beforeCreate));
        assertTrue(result.getTimestamp().isBefore(afterCreate) || result.getTimestamp().isEqual(afterCreate));
        
        // 测试不同实例的时间戳不同
        try {
            Thread.sleep(1); // 确保时间差
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        ResponseResult<String> result2 = ResponseResult.success("test2");
        assertNotEquals(result.getTimestamp(), result2.getTimestamp());
    }
    
    @Test
    @DisplayName("测试空值处理")
    void testNullHandling() {
        // 测试null数据
        ResponseResult<String> nullDataResult = ResponseResult.success(null);
        assertTrue(nullDataResult.isSuccess());
        assertNull(nullDataResult.getData());
        assertFalse(nullDataResult.hasData());

        // 测试null消息
        ResponseResult<String> nullMessageResult = ResponseResult.success(null, "data");
        assertTrue(nullMessageResult.isSuccess());
        assertNull(nullMessageResult.getMessage());
        assertEquals("data", nullMessageResult.getData());

        // 测试失败响应的null消息
        ResponseResult<Void> nullFailureMessage = ResponseResult.fail(ErrorCodes.BUSINESS_ERROR, null);
        assertFalse(nullFailureMessage.isSuccess());
        assertNull(nullFailureMessage.getMessage());
    }
    
    @Test
    @DisplayName("测试错误码处理")
    void testErrorCodeHandling() {
        // 测试各种错误码
        ResponseResult<Void> systemError = ResponseResult.failure(ErrorCodes.SYSTEM_ERROR, "系统错误");
        assertEquals(ErrorCodes.SYSTEM_ERROR, systemError.getCode());
        
        ResponseResult<Void> businessError = ResponseResult.failure(ErrorCodes.BUSINESS_ERROR, "业务错误");
        assertEquals(ErrorCodes.BUSINESS_ERROR, businessError.getCode());
        
        ResponseResult<Void> tradeError = ResponseResult.failure(ErrorCodes.TRADE_ERROR, "交易错误");
        assertEquals(ErrorCodes.TRADE_ERROR, tradeError.getCode());
        
        ResponseResult<Void> apiError = ResponseResult.failure(ErrorCodes.API_ERROR, "API错误");
        assertEquals(ErrorCodes.API_ERROR, apiError.getCode());
    }
    
    @Test
    @DisplayName("测试响应工具方法")
    void testResponseUtilityMethods() {
        // 测试数据获取方法
        ResponseResult<String> dataResult = ResponseResult.success("test data");
        assertEquals("test data", dataResult.getDataOrDefault("default"));
        assertTrue(dataResult.hasData());

        ResponseResult<String> nullDataResult = ResponseResult.success(null);
        assertEquals("default", nullDataResult.getDataOrDefault("default"));
        assertFalse(nullDataResult.hasData());

        // 测试错误详情方法
        ResponseResult<Void> errorResult = ResponseResult.fail(ErrorCodes.SYSTEM_ERROR, "error", "error details");
        assertTrue(errorResult.hasErrorDetails());
        assertEquals("error details", errorResult.getErrorDetails());

        ResponseResult<Void> noErrorResult = ResponseResult.success();
        assertFalse(noErrorResult.hasErrorDetails());
        assertNull(noErrorResult.getErrorDetails());
    }

    @Test
    @DisplayName("测试响应链式设置")
    void testResponseChainingSetting() {
        // 测试链式设置追踪ID
        ResponseResult<String> result = ResponseResult.success("test")
            .withTraceId("trace-123")
            .withErrorDetails("some details");

        assertTrue(result.isSuccess());
        assertEquals("trace-123", result.getTraceId());
        assertEquals("some details", result.getErrorDetails());
        assertTrue(result.hasErrorDetails());
    }
    
    /**
     * 测试用的简单对象
     */
    static class TestObject {
        private String name;
        private Integer age;
        
        public TestObject(String name, Integer age) {
            this.name = name;
            this.age = age;
        }
        
        public String getName() { return name; }
        public Integer getAge() { return age; }
        
        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            TestObject that = (TestObject) obj;
            return java.util.Objects.equals(name, that.name) &&
                   java.util.Objects.equals(age, that.age);
        }
        
        @Override
        public int hashCode() {
            return java.util.Objects.hash(name, age);
        }
    }
}
