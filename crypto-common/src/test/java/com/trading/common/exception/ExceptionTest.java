package com.trading.common.exception;

import com.trading.common.constant.ErrorCodes;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 异常类单元测试
 * 
 * <AUTHOR>
 * @since 1.0.0
 */
@DisplayName("异常类测试")
class ExceptionTest {
    
    @Test
    @DisplayName("测试BaseException基础功能")
    void testBaseException() {
        // 测试基本构造函数
        BusinessException exception1 = new BusinessException("Test message");
        assertEquals("Test message", exception1.getMessage());
        assertEquals("BUSINESS_ERROR", exception1.getErrorCode());
        assertNull(exception1.getData());

        // 测试带错误码的构造函数
        BusinessException exception2 = new BusinessException(ErrorCodes.BUSINESS_ERROR, "Business error");
        assertEquals("Business error", exception2.getMessage());
        assertEquals(ErrorCodes.BUSINESS_ERROR, exception2.getErrorCode());

        // 测试带数据的构造函数
        String testData = "test data";
        TradeException exception3 = new TradeException(ErrorCodes.TRADE_ERROR, "Trade error", testData);
        assertEquals("Trade error", exception3.getMessage());
        assertEquals(ErrorCodes.TRADE_ERROR, exception3.getErrorCode());
        assertEquals(testData, exception3.getData());

        // 测试带原因的构造函数
        RuntimeException cause = new RuntimeException("Root cause");
        SystemException exception4 = new SystemException(ErrorCodes.API_ERROR, "API error", cause);
        assertEquals("API error", exception4.getMessage());
        assertEquals(ErrorCodes.API_ERROR, exception4.getErrorCode());
        assertEquals(cause, exception4.getCause());
    }
    
    @Test
    @DisplayName("测试BusinessException业务异常")
    void testBusinessException() {
        // 测试基本构造函数
        BusinessException exception1 = new BusinessException("Business logic error");
        assertEquals("Business logic error", exception1.getMessage());
        assertEquals("BUSINESS_ERROR", exception1.getErrorCode());

        // 测试带错误码的构造函数
        BusinessException exception2 = new BusinessException(ErrorCodes.DATA_NOT_FOUND, "Data not found");
        assertEquals("Data not found", exception2.getMessage());
        assertEquals(ErrorCodes.DATA_NOT_FOUND, exception2.getErrorCode());

        // 测试带数据的构造函数
        Object businessData = new Object();
        BusinessException exception3 = new BusinessException(ErrorCodes.INVALID_PARAMETER, "Invalid param", businessData);
        assertEquals("Invalid param", exception3.getMessage());
        assertEquals(ErrorCodes.INVALID_PARAMETER, exception3.getErrorCode());
        assertEquals(businessData, exception3.getData());

        // 测试带原因的构造函数
        Exception cause = new IllegalArgumentException("Invalid argument");
        BusinessException exception4 = new BusinessException(ErrorCodes.OPERATION_FAILED, "Operation failed", cause);
        assertEquals("Operation failed", exception4.getMessage());
        assertEquals(ErrorCodes.OPERATION_FAILED, exception4.getErrorCode());
        assertEquals(cause, exception4.getCause());
    }
    
    @Test
    @DisplayName("测试SystemException系统异常")
    void testSystemException() {
        // 测试基本构造函数
        SystemException exception1 = new SystemException("System error occurred");
        assertEquals("System error occurred", exception1.getMessage());
        assertEquals("SYSTEM_ERROR", exception1.getErrorCode());

        // 测试带错误码的构造函数
        SystemException exception2 = new SystemException(ErrorCodes.NETWORK_ERROR, "Network connection failed");
        assertEquals("Network connection failed", exception2.getMessage());
        assertEquals(ErrorCodes.NETWORK_ERROR, exception2.getErrorCode());

        // 测试带原因的构造函数
        Exception cause = new java.net.ConnectException("Connection refused");
        SystemException exception3 = new SystemException(ErrorCodes.TIMEOUT_ERROR, "Request timeout", cause);
        assertEquals("Request timeout", exception3.getMessage());
        assertEquals(ErrorCodes.TIMEOUT_ERROR, exception3.getErrorCode());
        assertEquals(cause, exception3.getCause());
    }
    
    @Test
    @DisplayName("测试TradeException交易异常")
    void testTradeException() {
        // 测试基本构造函数
        TradeException exception1 = new TradeException("Trade execution failed");
        assertEquals("Trade execution failed", exception1.getMessage());
        assertEquals("TRADE_ERROR", exception1.getErrorCode());

        // 测试带错误码的构造函数
        TradeException exception2 = new TradeException(ErrorCodes.INSUFFICIENT_BALANCE, "Insufficient balance");
        assertEquals("Insufficient balance", exception2.getMessage());
        assertEquals(ErrorCodes.INSUFFICIENT_BALANCE, exception2.getErrorCode());

        // 测试带交易数据的构造函数
        Object tradeData = new Object();
        TradeException exception3 = new TradeException(ErrorCodes.INVALID_SYMBOL, "Invalid trading pair", tradeData);
        assertEquals("Invalid trading pair", exception3.getMessage());
        assertEquals(ErrorCodes.INVALID_SYMBOL, exception3.getErrorCode());
        assertEquals(tradeData, exception3.getData());

        // 测试带原因的构造函数
        Exception cause = new IllegalStateException("Invalid order state");
        TradeException exception4 = new TradeException(ErrorCodes.ORDER_FAILED, "Order placement failed", cause);
        assertEquals("Order placement failed", exception4.getMessage());
        assertEquals(ErrorCodes.ORDER_FAILED, exception4.getErrorCode());
        assertEquals(cause, exception4.getCause());
    }
    
    @Test
    @DisplayName("测试ApiException API异常")
    void testApiException() {
        // 测试基本构造函数
        ApiException exception1 = new ApiException("API call failed");
        assertEquals("API call failed", exception1.getMessage());
        assertEquals("API_ERROR", exception1.getErrorCode());

        // 测试带错误码的构造函数
        ApiException exception2 = new ApiException(ErrorCodes.RATE_LIMIT_EXCEEDED, "Rate limit exceeded");
        assertEquals("Rate limit exceeded", exception2.getMessage());
        assertEquals(ErrorCodes.RATE_LIMIT_EXCEEDED, exception2.getErrorCode());

        // 测试带API响应数据的构造函数
        String apiResponse = "{\"error\":\"Invalid API key\"}";
        ApiException exception3 = new ApiException(ErrorCodes.UNAUTHORIZED, "Unauthorized access", 401, apiResponse);
        assertEquals("Unauthorized access", exception3.getMessage());
        assertEquals(ErrorCodes.UNAUTHORIZED, exception3.getErrorCode());
        assertEquals(Integer.valueOf(401), exception3.getHttpStatusCode());

        // 测试带原因的构造函数
        Exception cause = new java.net.SocketTimeoutException("Read timeout");
        ApiException exception4 = new ApiException(ErrorCodes.API_TIMEOUT, "API request timeout", 408, null, cause);
        assertEquals("API request timeout", exception4.getMessage());
        assertEquals(ErrorCodes.API_TIMEOUT, exception4.getErrorCode());
        assertEquals(cause, exception4.getCause());
    }
    
    @Test
    @DisplayName("测试异常继承关系")
    void testExceptionHierarchy() {
        BusinessException businessException = new BusinessException("Business error");
        SystemException systemException = new SystemException("System error");
        TradeException tradeException = new TradeException("Trade error");
        ApiException apiException = new ApiException("API error");
        
        // 验证继承关系
        assertTrue(businessException instanceof BaseException);
        assertTrue(systemException instanceof BaseException);
        assertTrue(tradeException instanceof BaseException);
        assertTrue(apiException instanceof BaseException);
        
        assertTrue(businessException instanceof RuntimeException);
        assertTrue(systemException instanceof RuntimeException);
        assertTrue(tradeException instanceof RuntimeException);
        assertTrue(apiException instanceof RuntimeException);
    }
    
    @Test
    @DisplayName("测试异常链传播")
    void testExceptionChaining() {
        // 创建异常链
        Exception rootCause = new IllegalArgumentException("Root cause");
        BusinessException businessException = new BusinessException(ErrorCodes.INVALID_PARAMETER, "Business error", rootCause);
        SystemException systemException = new SystemException(ErrorCodes.SYSTEM_ERROR, "System error", businessException);
        
        // 验证异常链
        assertEquals(businessException, systemException.getCause());
        assertEquals(rootCause, businessException.getCause());
        assertEquals(rootCause, systemException.getCause().getCause());
        
        // 验证消息传播
        assertEquals("System error", systemException.getMessage());
        assertEquals("Business error", businessException.getMessage());
        assertEquals("Root cause", rootCause.getMessage());
    }
    
    @Test
    @DisplayName("测试异常数据携带")
    void testExceptionDataCarrying() {
        // 测试不同类型的数据
        String stringData = "error details";
        Integer intData = 12345;
        Object complexData = new Object() {
            public String toString() { return "complex data"; }
        };
        
        BusinessException exception1 = new BusinessException(ErrorCodes.BUSINESS_ERROR, "Error with string data", stringData);
        BusinessException exception2 = new BusinessException(ErrorCodes.BUSINESS_ERROR, "Error with int data", intData);
        BusinessException exception3 = new BusinessException(ErrorCodes.BUSINESS_ERROR, "Error with complex data", complexData);
        
        assertEquals(stringData, exception1.getData());
        assertEquals(intData, exception2.getData());
        assertEquals(complexData, exception3.getData());
        
        // 验证数据类型
        assertTrue(exception1.getData() instanceof String);
        assertTrue(exception2.getData() instanceof Integer);
        assertEquals("complex data", exception3.getData().toString());
    }
    
    @Test
    @DisplayName("测试异常消息格式化")
    void testExceptionMessageFormatting() {
        // 测试带参数的消息
        String template = "User %s attempted to access resource %d";
        String formattedMessage = String.format(template, "john", 123);
        
        BusinessException exception = new BusinessException(ErrorCodes.UNAUTHORIZED, formattedMessage);
        assertEquals("User john attempted to access resource 123", exception.getMessage());

        // 测试空消息处理
        BusinessException emptyException = new BusinessException(ErrorCodes.BUSINESS_ERROR, "");
        assertEquals("", emptyException.getMessage());

        BusinessException nullException = new BusinessException(ErrorCodes.BUSINESS_ERROR, null);
        assertNull(nullException.getMessage());
    }
    
    @Test
    @DisplayName("测试异常创建工厂方法")
    void testExceptionFactoryMethods() {
        // 测试快速创建方法
        BusinessException businessError = BusinessException.of("Business operation failed");
        assertEquals("Business operation failed", businessError.getMessage());
        assertEquals("BUSINESS_ERROR", businessError.getErrorCode());

        SystemException systemError = SystemException.of("System malfunction");
        assertEquals("System malfunction", systemError.getMessage());
        assertEquals("SYSTEM_ERROR", systemError.getErrorCode());

        TradeException tradeError = TradeException.of("Trade execution error");
        assertEquals("Trade execution error", tradeError.getMessage());
        assertEquals("TRADE_ERROR", tradeError.getErrorCode());

        ApiException apiError = ApiException.of("API communication error");
        assertEquals("API communication error", apiError.getMessage());
        assertEquals("API_ERROR", apiError.getErrorCode());
    }
}
