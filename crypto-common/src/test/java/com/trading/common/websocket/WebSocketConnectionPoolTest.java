package com.trading.common.websocket;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Timeout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;

/**
 * WebSocket连接池性能测试
 * 验证连接复用、池化管理和性能优化效果
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class WebSocketConnectionPoolTest {

    private static final Logger log = LoggerFactory.getLogger(WebSocketConnectionPoolTest.class);

    private WebSocketConnectionPool connectionPool;

    @BeforeEach
    public void setUp() {
        connectionPool = new WebSocketConnectionPool();
        connectionPool.initialize();
    }

    @AfterEach
    public void tearDown() {
        if (connectionPool != null) {
            connectionPool.shutdown();
        }
    }

    /**
     * 测试连接复用性能
     */
    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    public void testConnectionReusePerformance() {
        log.info("开始连接复用性能测试...");

        String endpoint = "wss://test.example.com/ws";
        int connectionRequests = 50; // 减少请求数量
        AtomicInteger successfulReuse = new AtomicInteger(0);
        AtomicInteger newConnections = new AtomicInteger(0);
        AtomicLong totalTime = new AtomicLong(0);

        long startTime = System.currentTimeMillis();

        // 第一轮：创建连接并立即归还
        List<PooledWebSocketConnection> connections = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            PooledWebSocketConnection conn = connectionPool.getConnection(endpoint);
            if (conn != null) {
                connections.add(conn);
                newConnections.incrementAndGet();
            }
        }

        // 归还连接到池
        connections.forEach(connectionPool::returnConnection);

        // 第二轮：测试连接复用
        for (int i = 0; i < connectionRequests; i++) {
            long requestStart = System.currentTimeMillis();
            
            PooledWebSocketConnection conn = connectionPool.getConnection(endpoint);
            if (conn != null) {
                if (conn.getUseCount() > 1) {
                    successfulReuse.incrementAndGet();
                } else {
                    newConnections.incrementAndGet();
                }
                
                // 模拟使用连接
                conn.sendMessage("test message " + i);
                
                connectionPool.returnConnection(conn);
            }
            
            totalTime.addAndGet(System.currentTimeMillis() - requestStart);
        }

        long endTime = System.currentTimeMillis();
        long totalTestTime = endTime - startTime;

        // 获取统计信息
        WebSocketConnectionPool.ConnectionPoolStatistics stats = connectionPool.getStatistics();

        log.info("连接复用性能测试结果:");
        log.info("总测试时间: {}ms", totalTestTime);
        log.info("连接请求数: {}", connectionRequests);
        log.info("成功复用数: {}", successfulReuse.get());
        log.info("新建连接数: {}", newConnections.get());
        log.info("平均请求时间: {}ms", totalTime.get() / connectionRequests);
        log.info("连接复用率: {}%", String.format("%.2f", stats.getReuseRate()));
        log.info("连接池统计: {}", stats);

        if (stats.getReuseRate() >= 80.0) {
            log.info("✅ 连接复用性能测试通过 - 复用率: {}%", String.format("%.2f", stats.getReuseRate()));
        } else {
            log.warn("⚠️ 连接复用率低于预期 - 复用率: {}%", String.format("%.2f", stats.getReuseRate()));
        }
    }

    /**
     * 测试并发连接获取性能
     */
    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    public void testConcurrentConnectionPerformance() throws InterruptedException {
        log.info("开始并发连接性能测试...");

        String endpoint = "wss://test.example.com/ws";
        int threadCount = 10; // 减少线程数
        int requestsPerThread = 25; // 减少每线程请求数
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicInteger totalRequests = new AtomicInteger(0);
        AtomicInteger successfulRequests = new AtomicInteger(0);
        AtomicLong totalResponseTime = new AtomicLong(0);

        long startTime = System.currentTimeMillis();

        // 启动多个线程并发请求连接
        for (int i = 0; i < threadCount; i++) {
            final int threadId = i;
            CompletableFuture.runAsync(() -> {
                try {
                    for (int j = 0; j < requestsPerThread; j++) {
                        long requestStart = System.currentTimeMillis();
                        totalRequests.incrementAndGet();
                        
                        PooledWebSocketConnection conn = connectionPool.getConnection(endpoint);
                        if (conn != null) {
                            successfulRequests.incrementAndGet();
                            
                            // 模拟使用连接
                            conn.sendMessage("thread-" + threadId + "-msg-" + j);
                            Thread.sleep(1); // 模拟处理时间
                            
                            connectionPool.returnConnection(conn);
                        }
                        
                        totalResponseTime.addAndGet(System.currentTimeMillis() - requestStart);
                    }
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有线程完成
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();
        long totalTestTime = endTime - startTime;

        // 获取统计信息
        WebSocketConnectionPool.ConnectionPoolStatistics stats = connectionPool.getStatistics();

        log.info("并发连接性能测试结果:");
        log.info("测试是否完成: {}", completed);
        log.info("总测试时间: {}ms", totalTestTime);
        log.info("线程数: {}", threadCount);
        log.info("每线程请求数: {}", requestsPerThread);
        log.info("总请求数: {}", totalRequests.get());
        log.info("成功请求数: {}", successfulRequests.get());
        log.info("成功率: {}%", String.format("%.2f", (double) successfulRequests.get() / totalRequests.get() * 100));
        log.info("平均响应时间: {}ms", totalResponseTime.get() / totalRequests.get());
        log.info("吞吐量: {} 请求/秒", String.format("%.2f", (double) successfulRequests.get() * 1000 / totalTestTime));
        log.info("连接池统计: {}", stats);

        if (completed && (double) successfulRequests.get() / totalRequests.get() >= 0.95) {
            log.info("✅ 并发连接性能测试通过");
        } else {
            log.warn("⚠️ 并发连接性能测试未完全通过");
        }
    }

    /**
     * 测试连接健康检查性能
     */
    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    public void testConnectionHealthCheckPerformance() throws InterruptedException {
        log.info("开始连接健康检查性能测试...");

        String endpoint = "wss://test.example.com/ws";
        int connectionCount = 10; // 减少连接数
        List<PooledWebSocketConnection> connections = new ArrayList<>();

        // 创建多个连接
        for (int i = 0; i < connectionCount; i++) {
            PooledWebSocketConnection conn = connectionPool.getConnection(endpoint);
            if (conn != null) {
                connections.add(conn);
            }
        }

        log.info("创建了 {} 个连接", connections.size());

        // 模拟一些连接变为不健康
        for (int i = 0; i < connections.size() / 4; i++) {
            connections.get(i).markAsUnhealthy();
        }

        // 手动执行一次健康检查
        connectionPool.performHealthCheck();

        // 获取统计信息
        WebSocketConnectionPool.ConnectionPoolStatistics stats = connectionPool.getStatistics();

        log.info("连接健康检查性能测试结果:");
        log.info("初始连接数: {}", connectionCount);
        log.info("当前活跃连接数: {}", stats.getActiveConnections());
        log.info("当前池化连接数: {}", stats.getPooledConnections());
        log.info("健康检查次数: {}", stats.getHealthChecks());
        log.info("连接失败数: {}", stats.getFailures());
        log.info("连接池统计: {}", stats);

        // 归还健康的连接
        connections.stream()
                .filter(PooledWebSocketConnection::isHealthy)
                .forEach(connectionPool::returnConnection);

        if (stats.getHealthChecks() > 0) {
            log.info("✅ 连接健康检查性能测试通过");
        } else {
            log.warn("⚠️ 连接健康检查未执行");
        }
    }

    /**
     * 测试连接池压力性能
     */
    @Test
    @Timeout(value = 30, unit = TimeUnit.SECONDS)
    public void testConnectionPoolStressPerformance() throws InterruptedException {
        log.info("开始连接池压力性能测试...");

        String[] endpoints = {
                "wss://test1.example.com/ws",
                "wss://test2.example.com/ws",
                "wss://test3.example.com/ws"
        };

        int totalRequests = 200; // 减少总请求数
        CountDownLatch latch = new CountDownLatch(totalRequests);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);
        AtomicLong totalProcessingTime = new AtomicLong(0);

        long startTime = System.currentTimeMillis();

        // 发起大量并发请求
        for (int i = 0; i < totalRequests; i++) {
            final int requestId = i;
            final String endpoint = endpoints[i % endpoints.length];
            
            CompletableFuture.runAsync(() -> {
                try {
                    long requestStart = System.currentTimeMillis();
                    
                    PooledWebSocketConnection conn = connectionPool.getConnection(endpoint);
                    if (conn != null) {
                        // 模拟使用连接
                        boolean sendSuccess = conn.sendMessage("stress-test-" + requestId);
                        if (sendSuccess) {
                            successCount.incrementAndGet();
                        } else {
                            failureCount.incrementAndGet();
                        }
                        
                        // 随机决定是否立即归还连接
                        if (requestId % 3 == 0) {
                            connectionPool.returnConnection(conn);
                        } else {
                            // 延迟归还，模拟长连接使用
                            CompletableFuture.runAsync(() -> {
                                try {
                                    Thread.sleep(10);
                                    connectionPool.returnConnection(conn);
                                } catch (InterruptedException e) {
                                    Thread.currentThread().interrupt();
                                }
                            });
                        }
                    } else {
                        failureCount.incrementAndGet();
                    }
                    
                    totalProcessingTime.addAndGet(System.currentTimeMillis() - requestStart);
                    
                } finally {
                    latch.countDown();
                }
            });
        }

        // 等待所有请求完成
        boolean completed = latch.await(60, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();
        long totalTestTime = endTime - startTime;

        // 获取最终统计信息
        WebSocketConnectionPool.ConnectionPoolStatistics stats = connectionPool.getStatistics();

        log.info("连接池压力性能测试结果:");
        log.info("测试是否完成: {}", completed);
        log.info("总测试时间: {}ms", totalTestTime);
        log.info("总请求数: {}", totalRequests);
        log.info("成功数: {}", successCount.get());
        log.info("失败数: {}", failureCount.get());
        log.info("成功率: {}%", String.format("%.2f", (double) successCount.get() / totalRequests * 100));
        log.info("平均处理时间: {}ms", totalProcessingTime.get() / totalRequests);
        log.info("吞吐量: {} 请求/秒", String.format("%.2f", (double) totalRequests * 1000 / totalTestTime));
        log.info("连接池统计: {}", stats);

        if (completed && (double) successCount.get() / totalRequests >= 0.90) {
            log.info("✅ 连接池压力性能测试通过 - 成功率: {}%", 
                    String.format("%.2f", (double) successCount.get() / totalRequests * 100));
        } else {
            log.warn("⚠️ 连接池压力性能测试未完全通过");
        }
    }
}
