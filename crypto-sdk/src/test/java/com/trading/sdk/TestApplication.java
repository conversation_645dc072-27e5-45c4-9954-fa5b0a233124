package com.trading.sdk;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisRepositoriesAutoConfiguration;
import org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;

/**
 * 测试应用程序主类
 * 用于Spring Boot测试上下文，限制扫描范围避免Bean冲突
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@SpringBootApplication(
    scanBasePackages = {
        "com.trading.sdk.config",
        "com.trading.sdk.client",
        "com.trading.sdk.ratelimit"
    },
    exclude = {
        RedisAutoConfiguration.class,
        RedisRepositoriesAutoConfiguration.class,
        KafkaAutoConfiguration.class,
        DataSourceAutoConfiguration.class
    }
)
public class TestApplication {

    public static void main(String[] args) {
        SpringApplication.run(TestApplication.class, args);
    }
}
