package com.trading.sdk.client;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ClientConfiguration单元测试
 */
class ClientConfigurationTest {

    @Test
    void testDefaultConfiguration() {
        // 创建默认配置
        ClientConfiguration config = ClientConfiguration.builder()
                .name("test")
                .baseUrl("https://fapi.binance.com")
                .build();

        // 验证默认值
        assertEquals("test", config.getName());
        assertEquals("https://fapi.binance.com", config.getBaseUrl());
        assertFalse(config.isTestnet());
        assertTrue(config.isShowLimitUsage());
        assertEquals(10000, config.getConnectionTimeout());
        assertEquals(30000, config.getReadTimeout());
        assertEquals(10000, config.getWriteTimeout());
        assertEquals(3, config.getMaxRetries());
        assertEquals(1000, config.getRetryDelay());
        assertTrue(config.isEnableConnectionPool());
        assertEquals(50, config.getMaxConnections()); // 修复：更新为优化后的默认值
        assertEquals(20, config.getMaxIdleConnections()); // 修复：更新为优化后的默认值
        assertEquals(600000, config.getKeepAliveDuration()); // 修复：更新为优化后的默认值
    }

    @Test
    void testAuthenticatedConfiguration() {
        // 创建认证配置
        ClientConfiguration config = ClientConfiguration.builder()
                .name("authenticated")
                .baseUrl("https://fapi.binance.com")
                .apiKey("test-api-key")
                .secretKey("test-secret-key")
                .build();

        // 验证认证状态
        assertTrue(config.isAuthenticated());
        assertEquals("test-api-key", config.getApiKey());
        assertEquals("test-secret-key", config.getSecretKey());
    }

    @Test
    void testUnauthenticatedConfiguration() {
        // 创建未认证配置
        ClientConfiguration config = ClientConfiguration.builder()
                .name("public")
                .baseUrl("https://fapi.binance.com")
                .build();

        // 验证认证状态
        assertFalse(config.isAuthenticated());
        assertNull(config.getApiKey());
        assertNull(config.getSecretKey());
    }

    @Test
    void testConfigurationWithProxy() {
        // 创建代理配置
        ClientConfiguration.ProxyConfiguration proxyConfig = ClientConfiguration.ProxyConfiguration.builder()
                .type(ClientConfiguration.ProxyType.HTTP)
                .host("proxy.example.com")
                .port(8080)
                .build();

        ClientConfiguration config = ClientConfiguration.builder()
                .name("with-proxy")
                .baseUrl("https://fapi.binance.com")
                .proxy(proxyConfig)
                .build();

        // 验证代理配置
        assertTrue(config.hasProxy());
        assertNotNull(config.getProxy());
        assertEquals("proxy.example.com", config.getProxy().getHost());
        assertEquals(8080, config.getProxy().getPort());
        assertEquals(ClientConfiguration.ProxyType.HTTP, config.getProxy().getType());
        assertFalse(config.getProxy().requiresAuth());
    }

    @Test
    void testConfigurationWithAuthProxy() {
        // 创建认证代理配置
        ClientConfiguration.ProxyConfiguration proxyConfig = ClientConfiguration.ProxyConfiguration.builder()
                .type(ClientConfiguration.ProxyType.HTTP)
                .host("proxy.example.com")
                .port(8080)
                .username("proxy-user")
                .password("proxy-pass")
                .build();

        ClientConfiguration config = ClientConfiguration.builder()
                .name("with-auth-proxy")
                .baseUrl("https://fapi.binance.com")
                .proxy(proxyConfig)
                .build();

        // 验证认证代理配置
        assertTrue(config.hasProxy());
        assertTrue(config.getProxy().requiresAuth());
        assertEquals("proxy-user", config.getProxy().getUsername());
        assertEquals("proxy-pass", config.getProxy().getPassword());
    }

    @Test
    void testGetFullBaseUrl() {
        // 测试带斜杠的URL
        ClientConfiguration config1 = ClientConfiguration.builder()
                .name("test1")
                .baseUrl("https://fapi.binance.com/")
                .build();
        assertEquals("https://fapi.binance.com", config1.getFullBaseUrl());

        // 测试不带斜杠的URL
        ClientConfiguration config2 = ClientConfiguration.builder()
                .name("test2")
                .baseUrl("https://fapi.binance.com")
                .build();
        assertEquals("https://fapi.binance.com", config2.getFullBaseUrl());
    }

    @Test
    void testCreateProductionConfig() {
        // 创建生产环境配置
        ClientConfiguration config = ClientConfiguration.createProductionConfig("api-key", "secret-key");

        // 验证
        assertEquals("production", config.getName());
        assertEquals("https://fapi.binance.com", config.getBaseUrl());
        assertEquals("api-key", config.getApiKey());
        assertEquals("secret-key", config.getSecretKey());
        assertFalse(config.isTestnet());
        assertTrue(config.isAuthenticated());
    }

    @Test
    void testCreateTestnetConfig() {
        // 创建测试网配置
        ClientConfiguration config = ClientConfiguration.createTestnetConfig("test-api-key", "test-secret-key");

        // 验证
        assertEquals("testnet", config.getName());
        assertEquals("https://testnet.binancefuture.com", config.getBaseUrl());
        assertEquals("test-api-key", config.getApiKey());
        assertEquals("test-secret-key", config.getSecretKey());
        assertTrue(config.isTestnet());
        assertTrue(config.isAuthenticated());
    }

    @Test
    void testCreatePublicConfig() {
        // 创建公开配置
        ClientConfiguration config = ClientConfiguration.createPublicConfig();

        // 验证
        assertEquals("public", config.getName());
        assertEquals("https://fapi.binance.com", config.getBaseUrl());
        assertFalse(config.isTestnet());
        assertFalse(config.isAuthenticated());
    }

    @Test
    void testCreateConfigWithProxy() {
        // 创建带代理的配置
        ClientConfiguration config = ClientConfiguration.createConfigWithProxy(
                "api-key", "secret-key", "proxy.example.com", 8080
        );

        // 验证
        assertEquals("with-proxy", config.getName());
        assertTrue(config.isAuthenticated());
        assertTrue(config.hasProxy());
        assertEquals("proxy.example.com", config.getProxy().getHost());
        assertEquals(8080, config.getProxy().getPort());
        assertFalse(config.getProxy().requiresAuth());
    }

    @Test
    void testCreateConfigWithAuthProxy() {
        // 创建带认证代理的配置
        ClientConfiguration config = ClientConfiguration.createConfigWithAuthProxy(
                "api-key", "secret-key", "proxy.example.com", 8080, "proxy-user", "proxy-pass"
        );

        // 验证
        assertEquals("with-auth-proxy", config.getName());
        assertTrue(config.isAuthenticated());
        assertTrue(config.hasProxy());
        assertTrue(config.getProxy().requiresAuth());
        assertEquals("proxy-user", config.getProxy().getUsername());
        assertEquals("proxy-pass", config.getProxy().getPassword());
    }

    @Test
    void testCopyWithName() {
        // 创建原始配置
        ClientConfiguration original = ClientConfiguration.createProductionConfig("api-key", "secret-key");

        // 复制并修改名称
        ClientConfiguration copied = original.copyWithName("new-name");

        // 验证
        assertEquals("new-name", copied.getName());
        assertEquals(original.getBaseUrl(), copied.getBaseUrl());
        assertEquals(original.getApiKey(), copied.getApiKey());
        assertEquals(original.getSecretKey(), copied.getSecretKey());
        assertNotSame(original, copied);
    }

    @Test
    void testCopyWithBaseUrl() {
        // 创建原始配置
        ClientConfiguration original = ClientConfiguration.createProductionConfig("api-key", "secret-key");

        // 复制并修改基础URL
        ClientConfiguration copied = original.copyWithBaseUrl("https://new.binance.com");

        // 验证
        assertEquals(original.getName(), copied.getName());
        assertEquals("https://new.binance.com", copied.getBaseUrl());
        assertEquals(original.getApiKey(), copied.getApiKey());
        assertEquals(original.getSecretKey(), copied.getSecretKey());
        assertNotSame(original, copied);
    }

    @Test
    void testCopyWithCredentials() {
        // 创建原始配置
        ClientConfiguration original = ClientConfiguration.createProductionConfig("api-key", "secret-key");

        // 复制并修改认证信息
        ClientConfiguration copied = original.copyWithCredentials("new-api-key", "new-secret-key");

        // 验证
        assertEquals(original.getName(), copied.getName());
        assertEquals(original.getBaseUrl(), copied.getBaseUrl());
        assertEquals("new-api-key", copied.getApiKey());
        assertEquals("new-secret-key", copied.getSecretKey());
        assertNotSame(original, copied);
    }

    @Test
    void testValidateValidConfiguration() {
        // 创建有效配置
        ClientConfiguration config = ClientConfiguration.createProductionConfig("api-key", "secret-key");

        // 验证不抛出异常
        assertDoesNotThrow(config::validate);
    }

    @Test
    void testValidateInvalidName() {
        // 创建无效名称的配置
        ClientConfiguration config = ClientConfiguration.builder()
                .name("")
                .baseUrl("https://fapi.binance.com")
                .build();

        // 验证抛出异常
        assertThrows(IllegalArgumentException.class, config::validate);
    }

    @Test
    void testValidateInvalidBaseUrl() {
        // 创建无效基础URL的配置
        ClientConfiguration config = ClientConfiguration.builder()
                .name("test")
                .baseUrl("invalid-url")
                .build();

        // 验证抛出异常
        assertThrows(IllegalArgumentException.class, config::validate);
    }

    @Test
    void testValidateInvalidTimeout() {
        // 创建无效超时的配置
        ClientConfiguration config = ClientConfiguration.builder()
                .name("test")
                .baseUrl("https://fapi.binance.com")
                .connectionTimeout(-1)
                .build();

        // 验证抛出异常
        assertThrows(IllegalArgumentException.class, config::validate);
    }

    @Test
    void testProxyConfigurationValidation() {
        // 创建无效代理配置
        ClientConfiguration.ProxyConfiguration invalidProxy = ClientConfiguration.ProxyConfiguration.builder()
                .host("")
                .port(8080)
                .build();

        // 验证抛出异常
        assertThrows(IllegalArgumentException.class, () -> 
                ClientConfiguration.ProxyConfigurationValidator.validate(invalidProxy));
    }

    @Test
    void testToString() {
        // 创建配置
        ClientConfiguration config = ClientConfiguration.createProductionConfig("api-key", "secret-key");

        // 验证toString方法
        String configString = config.toString();
        assertNotNull(configString);
        assertTrue(configString.contains("ClientConfiguration"));
        assertTrue(configString.contains("production"));
        assertTrue(configString.contains("https://fapi.binance.com"));
        assertTrue(configString.contains("authenticated=true"));
    }

    @Test
    void testProxyTypeEnum() {
        // 验证代理类型枚举
        assertEquals(2, ClientConfiguration.ProxyType.values().length);
        assertNotNull(ClientConfiguration.ProxyType.HTTP);
        assertNotNull(ClientConfiguration.ProxyType.SOCKS);
    }

    @Test
    void testBuilderPattern() {
        // 测试建造者模式
        ClientConfiguration config = ClientConfiguration.builder()
                .name("builder-test")
                .baseUrl("https://test.binance.com")
                .apiKey("test-key")
                .secretKey("test-secret")
                .testnet(true)
                .showLimitUsage(false)
                .connectionTimeout(5000)
                .readTimeout(15000)
                .writeTimeout(5000)
                .maxRetries(5)
                .retryDelay(2000)
                .enableConnectionPool(false)
                .maxConnections(50)
                .maxIdleConnections(25)
                .keepAliveDuration(600000)
                .build();

        // 验证所有设置的值
        assertEquals("builder-test", config.getName());
        assertEquals("https://test.binance.com", config.getBaseUrl());
        assertEquals("test-key", config.getApiKey());
        assertEquals("test-secret", config.getSecretKey());
        assertTrue(config.isTestnet());
        assertFalse(config.isShowLimitUsage());
        assertEquals(5000, config.getConnectionTimeout());
        assertEquals(15000, config.getReadTimeout());
        assertEquals(5000, config.getWriteTimeout());
        assertEquals(5, config.getMaxRetries());
        assertEquals(2000, config.getRetryDelay());
        assertFalse(config.isEnableConnectionPool());
        assertEquals(50, config.getMaxConnections());
        assertEquals(25, config.getMaxIdleConnections());
        assertEquals(600000, config.getKeepAliveDuration());
    }
}
