package com.trading.sdk.client;

import com.binance.connector.futures.client.impl.CMFuturesClientImpl;
import com.binance.connector.futures.client.impl.UMFuturesClientImpl;
import com.trading.sdk.config.SdkConfiguration;
import com.trading.common.exception.SdkException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BinanceClientFactoryTest {

    @Mock
    private SdkConfiguration sdkConfiguration;

    @Mock
    private SdkConfiguration.BinanceConfig binanceConfig;

    private BinanceClientFactory clientFactory;

    @BeforeEach
    void setUp() {
        lenient().when(sdkConfiguration.getBinance()).thenReturn(binanceConfig);
        lenient().when(binanceConfig.isTestnet()).thenReturn(false);
        lenient().when(binanceConfig.getApiKey()).thenReturn("test-api-key");
        lenient().when(binanceConfig.getSecretKey()).thenReturn("test-secret-key");
        clientFactory = new BinanceClientFactory(sdkConfiguration);
    }

    @Test
    void testGetUMFuturesClient_Success() {
        UMFuturesClientImpl client = clientFactory.getUMFuturesClient();
        assertNotNull(client);
    }

    @Test
    void testGetCMFuturesClient_Success() {
        CMFuturesClientImpl client = clientFactory.getCMFuturesClient();
        assertNotNull(client);
    }

    @Test
    void testGetUMFuturesClient_ReturnsCachedInstance() {
        UMFuturesClientImpl client1 = clientFactory.getUMFuturesClient();
        UMFuturesClientImpl client2 = clientFactory.getUMFuturesClient();
        assertSame(client1, client2, "Should return the same cached instance for UM client");
    }

    @Test
    void testGetCMFuturesClient_ReturnsCachedInstance() {
        CMFuturesClientImpl client1 = clientFactory.getCMFuturesClient();
        CMFuturesClientImpl client2 = clientFactory.getCMFuturesClient();
        assertSame(client1, client2, "Should return the same cached instance for CM client");
    }

    @Test
    void testClientCreationForTestnet() {
        when(binanceConfig.isTestnet()).thenReturn(true);
        // Re-initialize to pick up new mock config
        clientFactory = new BinanceClientFactory(sdkConfiguration); 

        assertNotNull(clientFactory.getUMFuturesClient());
        assertNotNull(clientFactory.getCMFuturesClient());
        // Verification of URL is tricky as it's private, but we can trust the implementation for now.
    }
    
    @Test
    void testClientCreationWithNoApiKey() {
        when(binanceConfig.getApiKey()).thenReturn(null);
        clientFactory = new BinanceClientFactory(sdkConfiguration);

        assertNotNull(clientFactory.getUMFuturesClient());
        assertNotNull(clientFactory.getCMFuturesClient());
    }

    @Test
    void testValidateClientConnection_UMClient_Success() {
        // This test is limited as we can't easily mock the final client's `market()` method chain
        UMFuturesClientImpl mockClient = mock(UMFuturesClientImpl.class, RETURNS_DEEP_STUBS);
        when(mockClient.market().time()).thenReturn("{\"serverTime\": 123456789}");
        assertTrue(clientFactory.validateClientConnection(mockClient));
    }

    @Test
    void testValidateClientConnection_CMClient_Success() {
        CMFuturesClientImpl mockClient = mock(CMFuturesClientImpl.class, RETURNS_DEEP_STUBS);
        when(mockClient.market().time()).thenReturn("{\"serverTime\": 123456789}");
        assertTrue(clientFactory.validateClientConnection(mockClient));
    }

    @Test
    void testValidateClientConnection_Failure() {
        UMFuturesClientImpl mockClient = mock(UMFuturesClientImpl.class, RETURNS_DEEP_STUBS);
        when(mockClient.market().time()).thenThrow(new RuntimeException("Connection failed"));
        assertFalse(clientFactory.validateClientConnection(mockClient));
    }

    @Test
    void testValidateClientConnection_UnknownObject() {
        assertFalse(clientFactory.validateClientConnection(new Object()));
    }
    
    @Test
    void testConcurrentClientCreation() throws InterruptedException {
        int threadCount = 10;
        Thread[] threads = new Thread[threadCount];
        final UMFuturesClientImpl[] umClients = new UMFuturesClientImpl[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int index = i;
            threads[i] = new Thread(() -> umClients[index] = clientFactory.getUMFuturesClient());
        }

        for (Thread thread : threads) {
            thread.start();
        }

        for (Thread thread : threads) {
            thread.join();
        }

        for (int i = 1; i < threadCount; i++) {
            assertNotNull(umClients[i]);
            assertSame(umClients[0], umClients[i], "All threads should get the same client instance");
        }
    }
}
