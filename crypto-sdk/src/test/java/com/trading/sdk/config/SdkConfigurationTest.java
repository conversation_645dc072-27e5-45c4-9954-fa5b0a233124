package com.trading.sdk.config;

import com.trading.sdk.config.SdkConfiguration;
import com.trading.sdk.TestApplication;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.TestPropertySource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SDK配置测试
 */
class SdkConfigurationTest {

    @Test
    void testDefaultConfiguration() {
        SdkConfiguration config = new SdkConfiguration();
        
        // 测试默认值
        assertNotNull(config.getBinance());
        assertEquals("https://fapi.binance.com", config.getBinance().getBaseUrl());
        assertEquals("wss://fstream.binance.com", config.getBinance().getUmFuturesWebSocketBaseUrl());
        assertFalse(config.getBinance().isTestnet());
        assertEquals(10000, config.getBinance().getTimeout());
        assertEquals(3, config.getBinance().getMaxRetries());
        
        // 测试连接池配置
        assertNotNull(config.getPool());
        assertEquals(20, config.getPool().getMaxTotal());
        assertEquals(10, config.getPool().getMaxIdle());
        assertEquals(2, config.getPool().getMinIdle());
        
        // 测试重试配置
        assertNotNull(config.getRetry());
        assertEquals(3, config.getRetry().getMaxAttempts());
        assertEquals(1000, config.getRetry().getBackoffDelay());
        assertEquals(2.0, config.getRetry().getBackoffMultiplier());
        
        // 测试WebSocket配置
        assertNotNull(config.getWebsocket());
        assertEquals(10000, config.getWebsocket().getConnectionTimeout());
        assertEquals(60000, config.getWebsocket().getReadTimeout());
        assertTrue(config.getWebsocket().isAutoReconnect());
    }

    @Test
    void testBinanceConfigSetters() {
        SdkConfiguration.BinanceConfig binanceConfig = new SdkConfiguration.BinanceConfig();
        
        binanceConfig.setApiKey("test-api-key");
        binanceConfig.setSecretKey("test-secret-key");
        binanceConfig.setTestnet(true);
        binanceConfig.setTimeout(5000);
        
        assertEquals("test-api-key", binanceConfig.getApiKey());
        assertEquals("test-secret-key", binanceConfig.getSecretKey());
        assertTrue(binanceConfig.isTestnet());
        assertEquals(5000, binanceConfig.getTimeout());
    }

    @Test
    void testPoolConfigSetters() {
        SdkConfiguration.PoolConfig poolConfig = new SdkConfiguration.PoolConfig();
        
        poolConfig.setMaxTotal(50);
        poolConfig.setMaxIdle(25);
        poolConfig.setMinIdle(5);
        poolConfig.setMaxWaitMillis(10000);
        
        assertEquals(50, poolConfig.getMaxTotal());
        assertEquals(25, poolConfig.getMaxIdle());
        assertEquals(5, poolConfig.getMinIdle());
        assertEquals(10000, poolConfig.getMaxWaitMillis());
    }

    @Test
    void testRetryConfigSetters() {
        SdkConfiguration.RetryConfig retryConfig = new SdkConfiguration.RetryConfig();
        
        retryConfig.setMaxAttempts(5);
        retryConfig.setBackoffDelay(2000);
        retryConfig.setBackoffMultiplier(1.5);
        retryConfig.setMaxDelay(60000);
        
        assertEquals(5, retryConfig.getMaxAttempts());
        assertEquals(2000, retryConfig.getBackoffDelay());
        assertEquals(1.5, retryConfig.getBackoffMultiplier());
        assertEquals(60000, retryConfig.getMaxDelay());
    }

    @Test
    void testWebSocketConfigSetters() {
        SdkConfiguration.WebSocketConfig wsConfig = new SdkConfiguration.WebSocketConfig();
        
        wsConfig.setConnectionTimeout(15000);
        wsConfig.setReadTimeout(120000);
        wsConfig.setPingInterval(45000);
        wsConfig.setAutoReconnect(false);
        wsConfig.setMaxReconnectAttempts(5);
        wsConfig.setReconnectDelay(10000);
        
        assertEquals(15000, wsConfig.getConnectionTimeout());
        assertEquals(120000, wsConfig.getReadTimeout());
        assertEquals(45000, wsConfig.getPingInterval());
        assertFalse(wsConfig.isAutoReconnect());
        assertEquals(5, wsConfig.getMaxReconnectAttempts());
        assertEquals(10000, wsConfig.getReconnectDelay());
    }
}
