package com.trading.sdk.config;

import com.trading.sdk.config.SdkConfiguration;
import com.trading.common.dto.Symbol;
import com.trading.sdk.websocket.callback.MessageHandler;
import com.influxdb.client.InfluxDBClient;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.mockito.Mockito;
import javax.sql.DataSource;

/**
 * 测试配置类
 * 提供测试所需的Mock Bean，避免Bean定义冲突
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@TestConfiguration
public class TestConfig {

    /**
     * 测试用MessageHandler Bean
     */
    @Bean("testMessageHandler")
    @Primary
    public MessageHandler messageHandler() {
        return new MessageHandler() {
            @Override
            public void handleKlineMessage(String subscriptionKey, Symbol symbol, String interval, String message) {
                // 测试用空实现
            }

            @Override
            public void handleDepthMessage(String subscriptionKey, Symbol symbol, int levels, int speed, String message) {
                // 测试用空实现
            }

            @Override
            public void handleTradeMessage(String subscriptionKey, Symbol symbol, String message) {
                // 测试用空实现
            }

            @Override
            public void handleTickerMessage(String subscriptionKey, Symbol symbol, String message) {
                // 测试用空实现
            }

            @Override
            public void handleBookTickerMessage(String subscriptionKey, Symbol symbol, String message) {
                // 测试用空实现
            }

            @Override
            public void handleMarkPriceMessage(String subscriptionKey, Symbol symbol, String message) {
                // 测试用空实现
            }

            @Override
            public void handleAggTradeMessage(String subscriptionKey, Symbol symbol, String message) {
                // 测试用空实现
            }

            @Override
            public void handleUserDataMessage(String subscriptionKey, String listenKey, String message) {
                // 测试用空实现
            }

            @Override
            public void handleForceOrderMessage(String subscriptionKey, Symbol symbol, String message) {
                // 测试用空实现
            }

            @Override
            public void handleConnectionOpen(String subscriptionKey) {
                // 测试用空实现
            }

            @Override
            public void handleConnectionClose(String subscriptionKey, String reason) {
                // 测试用空实现
            }

            @Override
            public void handleConnectionError(String subscriptionKey, Throwable error) {
                // 测试用空实现
            }

            @Override
            public void handleReconnect(String subscriptionKey, int attempt) {
                // 测试用空实现
            }
        };
    }

    /**
     * 测试用RedisTemplate<String, Object> Bean
     * 避免与其他RedisTemplate Bean冲突
     */
    @Bean("testRedisTemplate")
    @Primary
    @SuppressWarnings("unchecked")
    public RedisTemplate<String, Object> redisTemplate() {
        RedisTemplate<String, Object> template = Mockito.mock(RedisTemplate.class);
        // 配置基本的Mock行为，避免NullPointerException
        Mockito.when(template.hasKey(Mockito.anyString())).thenReturn(false);
        return template;
    }

    /**
     * 测试用RedisTemplate<String, String> Bean
     * 用于MultiLevelCacheManagerImpl，避免Bean冲突
     */
    @Bean("testStringRedisTemplate")
    @SuppressWarnings("unchecked")
    public RedisTemplate<String, String> stringRedisTemplate() {
        RedisTemplate<String, String> template = Mockito.mock(RedisTemplate.class);
        // 配置基本的Mock行为，避免NullPointerException
        Mockito.when(template.hasKey(Mockito.anyString())).thenReturn(false);
        return template;
    }

    /**
     * 测试用DataSource Bean
     */
    @Bean("testDataSource")
    @Primary
    public DataSource dataSource() {
        return Mockito.mock(DataSource.class);
    }

    /**
     * 测试用RedisConnectionFactory Bean
     */
    @Bean
    @Primary
    public RedisConnectionFactory redisConnectionFactory() {
        return Mockito.mock(RedisConnectionFactory.class);
    }

    /**
     * 测试用InfluxDBClient Bean
     * 避免InfluxDB配置问题导致的测试失败
     */
    @Bean("testInfluxDBClient")
    @Primary
    public InfluxDBClient influxDBClient() {
        return Mockito.mock(InfluxDBClient.class);
    }

    /**
     * 测试用BinanceConfig Bean
     * 避免WebSocketManager依赖注入失败
     */
    @Bean("testBinanceConfig")
    @Primary
    public SdkConfiguration.BinanceConfig binanceConfig() {
        SdkConfiguration.BinanceConfig config = new SdkConfiguration.BinanceConfig();
        config.setApiKey("test-key");
        config.setSecretKey("test-secret");
        config.setTestnet(true);
        return config;
    }
}
