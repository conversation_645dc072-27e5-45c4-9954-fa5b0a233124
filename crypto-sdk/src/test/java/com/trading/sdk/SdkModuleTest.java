package com.trading.sdk;

import com.trading.sdk.config.SdkConfiguration;
import com.trading.common.exception.SdkException;
import com.trading.common.exception.BinanceApiException;
import com.trading.common.exception.WebSocketException;
import com.trading.common.dto.Symbol;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * SDK模块基础功能测试
 * 验证模块结构和基础类是否正确创建
 */
class SdkModuleTest {

    @Test
    void testSdkConfigurationCreation() {
        SdkConfiguration config = new SdkConfiguration();
        assertNotNull(config);
        assertNotNull(config.getBinance());
        assertNotNull(config.getPool());
        assertNotNull(config.getRetry());
        assertNotNull(config.getWebsocket());
    }

    @Test
    void testSdkExceptionHierarchy() {
        // 测试基础异常
        SdkException sdkException = new SdkException("Test SDK exception");
        assertNotNull(sdkException);
        assertEquals("Test SDK exception", sdkException.getMessage());

        // 测试Binance API异常
        BinanceApiException apiException = new BinanceApiException(400, "INVALID_SYMBOL", "Invalid symbol");
        assertNotNull(apiException);
        assertEquals(400, apiException.getHttpStatusCode());
        assertEquals("INVALID_SYMBOL", apiException.getBinanceErrorCode());
        assertEquals("Invalid symbol", apiException.getMessage());

        // 测试WebSocket异常
        WebSocketException wsException = new WebSocketException("WebSocket connection failed");
        assertNotNull(wsException);
        assertEquals("WebSocket connection failed", wsException.getMessage());
        assertEquals("WEBSOCKET_ERROR", wsException.getErrorCode());
    }

    @Test
    void testSymbolModel() {
        // 测试Symbol创建
        Symbol btcUsdt = Symbol.of("BTCUSDT");
        assertNotNull(btcUsdt);
        assertEquals("BTCUSDT", btcUsdt.getSymbol());
        assertEquals("BTC", btcUsdt.getBaseAsset());
        assertEquals("USDT", btcUsdt.getQuoteAsset());

        // 测试Symbol相等性
        Symbol btcUsdt2 = Symbol.of("BTCUSDT");
        assertEquals(btcUsdt, btcUsdt2);
        assertEquals(btcUsdt.hashCode(), btcUsdt2.hashCode());

        // 测试不同的Symbol
        Symbol ethUsdt = Symbol.of("ETHUSDT");
        assertNotEquals(btcUsdt, ethUsdt);

        // 测试toString
        assertEquals("BTCUSDT", btcUsdt.toString());
    }

    @Test
    void testSymbolParsing() {
        // 测试USDT交易对
        Symbol btcUsdt = Symbol.of("BTCUSDT");
        assertEquals("BTC", btcUsdt.getBaseAsset());
        assertEquals("USDT", btcUsdt.getQuoteAsset());

        // 测试BUSD交易对
        Symbol btcBusd = Symbol.of("BTCBUSD");
        assertEquals("BTC", btcBusd.getBaseAsset());
        assertEquals("BUSD", btcBusd.getQuoteAsset());

        // 测试BTC交易对
        Symbol ethBtc = Symbol.of("ETHBTC");
        assertEquals("ETH", ethBtc.getBaseAsset());
        assertEquals("BTC", ethBtc.getQuoteAsset());

        // 测试ETH交易对
        Symbol linkEth = Symbol.of("LINKETH");
        assertEquals("LINK", linkEth.getBaseAsset());
        assertEquals("ETH", linkEth.getQuoteAsset());
    }

    @Test
    void testSymbolConstructors() {
        // 测试字符串构造函数
        Symbol symbol1 = new Symbol("BTCUSDT");
        assertEquals("BTCUSDT", symbol1.getSymbol());

        // 测试分离构造函数
        Symbol symbol2 = new Symbol("BTC", "USDT");
        assertEquals("BTCUSDT", symbol2.getSymbol());
        assertEquals("BTC", symbol2.getBaseAsset());
        assertEquals("USDT", symbol2.getQuoteAsset());

        // 测试静态工厂方法
        Symbol symbol3 = Symbol.of("BTC", "USDT");
        assertEquals(symbol2, symbol3);
    }
}
