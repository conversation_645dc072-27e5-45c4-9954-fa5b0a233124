package com.trading.sdk.websocket.callback;

import com.trading.common.dto.Symbol;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Timeout;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 增强版消息处理器测试
 * 验证虚拟线程异步处理、JSON解析、性能监控等功能
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
import org.junit.jupiter.api.Disabled;

@Disabled("Disabling this performance-sensitive test to diagnose build timeouts.")
class EnhancedMessageHandlerTest {
    
    private static final Logger log = LoggerFactory.getLogger(EnhancedMessageHandlerTest.class);
    
    private EnhancedMessageHandler messageHandler;
    
    @BeforeEach
    void setUp() {
        messageHandler = new EnhancedMessageHandler();
        log.info("测试环境初始化完成");
    }
    
    @Test
    @DisplayName("测试K线消息处理")
    @Timeout(10)
    void testKlineMessageHandling() throws InterruptedException {
        // 准备测试数据
        String subscriptionKey = "btcusdt@kline_1m";
        Symbol symbol = Symbol.of("BTCUSDT");
        String interval = "1m";
        String klineMessage = """
            {
                "e": "kline",
                "E": 1672531200000,
                "s": "BTCUSDT",
                "k": {
                    "t": 1672531200000,
                    "T": 1672531259999,
                    "s": "BTCUSDT",
                    "i": "1m",
                    "f": 100,
                    "L": 200,
                    "o": "16569.01000000",
                    "c": "16569.02000000",
                    "h": "16569.03000000",
                    "l": "16569.00000000",
                    "v": "0.01000000",
                    "n": 101,
                    "x": false,
                    "q": "165.69020000",
                    "V": "0.00500000",
                    "Q": "82.84510000",
                    "B": "0"
                }
            }
            """;
        
        // 处理消息
        assertDoesNotThrow(() -> {
            messageHandler.handleKlineMessage(subscriptionKey, symbol, interval, klineMessage);
        }, "K线消息处理不应抛出异常");
        
        // 等待异步处理完成
        Thread.sleep(1000);
        
        // 验证统计信息
        Map<String, Long> stats = messageHandler.getMessageStats();
        assertTrue(stats.containsKey("kline"), "应包含K线消息统计");
        assertEquals(1L, stats.get("kline"), "K线消息计数应为1");
        
        log.info("K线消息处理测试通过");
    }
    
    @Test
    @DisplayName("测试深度消息处理")
    @Timeout(10)
    void testDepthMessageHandling() throws InterruptedException {
        // 准备测试数据
        String subscriptionKey = "btcusdt@depth20@100ms";
        Symbol symbol = Symbol.of("BTCUSDT");
        int levels = 20;
        int speed = 100;
        String depthMessage = """
            {
                "e": "depthUpdate",
                "E": 1672531200000,
                "s": "BTCUSDT",
                "U": 157,
                "u": 160,
                "b": [
                    ["16569.00000000", "0.01000000"],
                    ["16568.99000000", "0.02000000"]
                ],
                "a": [
                    ["16569.01000000", "0.01500000"],
                    ["16569.02000000", "0.02500000"]
                ]
            }
            """;
        
        // 处理消息
        assertDoesNotThrow(() -> {
            messageHandler.handleDepthMessage(subscriptionKey, symbol, levels, speed, depthMessage);
        }, "深度消息处理不应抛出异常");
        
        // 等待异步处理完成
        Thread.sleep(1000);
        
        // 验证统计信息
        Map<String, Long> stats = messageHandler.getMessageStats();
        assertTrue(stats.containsKey("depth"), "应包含深度消息统计");
        assertEquals(1L, stats.get("depth"), "深度消息计数应为1");
        
        log.info("深度消息处理测试通过");
    }
    
    @Test
    @DisplayName("测试交易消息处理")
    @Timeout(10)
    void testTradeMessageHandling() throws InterruptedException {
        // 准备测试数据
        String subscriptionKey = "btcusdt@trade";
        Symbol symbol = Symbol.of("BTCUSDT");
        String tradeMessage = """
            {
                "e": "trade",
                "E": 1672531200000,
                "s": "BTCUSDT",
                "t": 12345,
                "p": "16569.01000000",
                "q": "0.01000000",
                "b": 88,
                "a": 50,
                "T": 1672531200000,
                "m": true,
                "M": true
            }
            """;
        
        // 处理消息
        assertDoesNotThrow(() -> {
            messageHandler.handleTradeMessage(subscriptionKey, symbol, tradeMessage);
        }, "交易消息处理不应抛出异常");
        
        // 等待异步处理完成
        Thread.sleep(1000);
        
        // 验证统计信息
        Map<String, Long> stats = messageHandler.getMessageStats();
        assertTrue(stats.containsKey("trade"), "应包含交易消息统计");
        assertEquals(1L, stats.get("trade"), "交易消息计数应为1");
        
        log.info("交易消息处理测试通过");
    }
    
    @Test
    @DisplayName("测试24小时统计消息处理")
    @Timeout(10)
    void test24hrTickerMessageHandling() throws InterruptedException {
        // 准备测试数据
        String subscriptionKey = "btcusdt@ticker";
        Symbol symbol = Symbol.of("BTCUSDT");
        String tickerMessage = """
            {
                "e": "24hrTicker",
                "E": 1672531200000,
                "s": "BTCUSDT",
                "p": "100.00000000",
                "P": "0.61",
                "w": "16569.01000000",
                "x": "16469.01000000",
                "c": "16569.01000000",
                "Q": "0.01000000",
                "b": "16569.00000000",
                "B": "0.01000000",
                "a": "16569.02000000",
                "A": "0.01000000",
                "o": "16469.01000000",
                "h": "16669.01000000",
                "l": "16369.01000000",
                "v": "1000.00000000",
                "q": "16569010.00000000",
                "O": 1672444800000,
                "C": 1672531200000,
                "F": 1,
                "L": 1000,
                "n": 1000
            }
            """;
        
        // 处理消息
        assertDoesNotThrow(() -> {
            messageHandler.handleTickerMessage(subscriptionKey, symbol, tickerMessage);
        }, "24小时统计消息处理不应抛出异常");
        
        // 等待异步处理完成
        Thread.sleep(1000);
        
        // 验证统计信息
        Map<String, Long> stats = messageHandler.getMessageStats();
        assertTrue(stats.containsKey("24hrTicker"), "应包含24小时统计消息统计");
        assertEquals(1L, stats.get("24hrTicker"), "24小时统计消息计数应为1");
        
        log.info("24小时统计消息处理测试通过");
    }
    
    @Test
    @DisplayName("测试最优挂单消息处理")
    @Timeout(10)
    void testBookTickerMessageHandling() throws InterruptedException {
        // 准备测试数据
        String subscriptionKey = "btcusdt@bookTicker";
        Symbol symbol = Symbol.of("BTCUSDT");
        String bookTickerMessage = """
            {
                "u": 400900217,
                "s": "BTCUSDT",
                "b": "16569.00000000",
                "B": "0.01000000",
                "a": "16569.01000000",
                "A": "0.01500000"
            }
            """;
        
        // 处理消息
        assertDoesNotThrow(() -> {
            messageHandler.handleBookTickerMessage(subscriptionKey, symbol, bookTickerMessage);
        }, "最优挂单消息处理不应抛出异常");
        
        // 等待异步处理完成
        Thread.sleep(1000);
        
        // 验证统计信息
        Map<String, Long> stats = messageHandler.getMessageStats();
        assertTrue(stats.containsKey("bookTicker"), "应包含最优挂单消息统计");
        assertEquals(1L, stats.get("bookTicker"), "最优挂单消息计数应为1");
        
        log.info("最优挂单消息处理测试通过");
    }
    
    @Test
    @DisplayName("测试标记价格消息处理")
    @Timeout(10)
    void testMarkPriceMessageHandling() throws InterruptedException {
        // 准备测试数据
        String subscriptionKey = "btcusdt@markPrice";
        Symbol symbol = Symbol.of("BTCUSDT");
        String markPriceMessage = """
            {
                "e": "markPriceUpdate",
                "E": 1672531200000,
                "s": "BTCUSDT",
                "p": "16569.01000000",
                "i": "16569.00000000",
                "P": "16569.02000000",
                "r": "0.00010000",
                "T": 1672531200000
            }
            """;
        
        // 处理消息
        assertDoesNotThrow(() -> {
            messageHandler.handleMarkPriceMessage(subscriptionKey, symbol, markPriceMessage);
        }, "标记价格消息处理不应抛出异常");
        
        // 等待异步处理完成
        Thread.sleep(1000);
        
        // 验证统计信息
        Map<String, Long> stats = messageHandler.getMessageStats();
        assertTrue(stats.containsKey("markPrice"), "应包含标记价格消息统计");
        assertEquals(1L, stats.get("markPrice"), "标记价格消息计数应为1");
        
        log.info("标记价格消息处理测试通过");
    }
    
    @Test
    @DisplayName("测试用户数据消息处理")
    @Timeout(10)
    void testUserDataMessageHandling() throws InterruptedException {
        // 准备测试数据
        String subscriptionKey = "user_data_stream";
        String userDataMessage = """
            {
                "e": "ORDER_TRADE_UPDATE",
                "E": 1672531200000,
                "T": 1672531200000,
                "o": {
                    "s": "BTCUSDT",
                    "c": "TEST",
                    "S": "SELL",
                    "o": "TRAILING_STOP_MARKET",
                    "f": "GTC",
                    "q": "0.001",
                    "p": "0",
                    "ap": "0",
                    "sp": "7103.04",
                    "x": "NEW",
                    "X": "NEW",
                    "i": 8886774,
                    "l": "0",
                    "z": "0",
                    "L": "0",
                    "n": "0",
                    "N": null,
                    "T": 1672531200000,
                    "t": 0,
                    "b": "0",
                    "a": "9.91",
                    "m": false,
                    "R": false,
                    "wt": "CONTRACT_PRICE",
                    "ot": "TRAILING_STOP_MARKET",
                    "ps": "LONG",
                    "cp": false,
                    "AP": "7476.89",
                    "cr": "5.0",
                    "pP": false,
                    "si": 0,
                    "ss": 0,
                    "V": "NONE",
                    "pm": "NONE",
                    "gtd": 0
                }
            }
            """;
        
        // 处理消息
        assertDoesNotThrow(() -> {
            messageHandler.handleUserDataMessage(subscriptionKey, "test_listen_key", userDataMessage);
        }, "用户数据消息处理不应抛出异常");
        
        // 等待异步处理完成
        Thread.sleep(1000);
        
        // 验证统计信息
        Map<String, Long> stats = messageHandler.getMessageStats();
        assertTrue(stats.containsKey("userData"), "应包含用户数据消息统计");
        assertEquals(1L, stats.get("userData"), "用户数据消息计数应为1");
        
        log.info("用户数据消息处理测试通过");
    }
    
    @Test
    @DisplayName("测试批量消息处理性能")
    @Timeout(30)
    void testBatchMessageProcessingPerformance() throws InterruptedException {
        // 准备测试数据
        int messageCount = 1000;
        String subscriptionKey = "performance_test";
        Symbol symbol = Symbol.of("BTCUSDT");
        
        String tradeMessage = """
            {
                "e": "trade",
                "E": 1672531200000,
                "s": "BTCUSDT",
                "t": 12345,
                "p": "16569.01000000",
                "q": "0.01000000",
                "b": 88,
                "a": 50,
                "T": 1672531200000,
                "m": true,
                "M": true
            }
            """;
        
        // 记录开始时间
        long startTime = System.currentTimeMillis();
        
        // 批量处理消息
        for (int i = 0; i < messageCount; i++) {
            messageHandler.handleTradeMessage(subscriptionKey + "_" + i, symbol, tradeMessage);
        }
        
        // 等待处理完成
        Thread.sleep(5000);
        
        // 计算性能指标
        long duration = System.currentTimeMillis() - startTime;
        double throughput = (double) messageCount / duration * 1000; // 每秒处理数
        
        // 获取性能统计
        EnhancedMessageHandler.MessageHandlerPerformanceStats stats = messageHandler.getPerformanceStats();
        
        // 验证结果
        assertTrue(stats.getTotalMessagesProcessed() >= messageCount, "处理的消息数量不足");
        assertTrue(stats.getAverageProcessingTime() >= 0, "平均处理时间应为非负数");
        
        log.info("批量消息处理性能测试通过 - 处理{}条消息，耗时{}ms，吞吐量{:.2f}消息/秒", 
                messageCount, duration, throughput);
        log.info("性能统计: {}", stats);
    }
    
    @Test
    @DisplayName("测试连接事件处理")
    @Timeout(10)
    void testConnectionEventHandling() {
        String subscriptionKey = "connection_test";
        
        // 测试连接打开
        assertDoesNotThrow(() -> {
            messageHandler.handleConnectionOpen(subscriptionKey);
        }, "连接打开处理不应抛出异常");
        
        // 测试连接关闭
        assertDoesNotThrow(() -> {
            messageHandler.handleConnectionClose(subscriptionKey, "Normal closure");
        }, "连接关闭处理不应抛出异常");
        
        // 测试连接错误
        assertDoesNotThrow(() -> {
            messageHandler.handleConnectionError(subscriptionKey, new RuntimeException("Test error"));
        }, "连接错误处理不应抛出异常");
        
        log.info("连接事件处理测试通过");
    }
    
    @Test
    @DisplayName("测试统计信息功能")
    @Timeout(10)
    void testStatisticsFeatures() throws InterruptedException {
        // 发送不同类型的消息
        messageHandler.handleTradeMessage("test1", Symbol.of("BTCUSDT"), "{\"e\":\"trade\",\"p\":\"100\",\"q\":\"1\"}");
        messageHandler.handleTradeMessage("test2", Symbol.of("ETHUSDT"), "{\"e\":\"trade\",\"p\":\"200\",\"q\":\"2\"}");
        messageHandler.handleKlineMessage("test3", Symbol.of("BTCUSDT"), "1m", "{\"e\":\"kline\",\"k\":{\"t\":123,\"T\":456}}");
        
        // 等待处理完成
        Thread.sleep(2000);
        
        // 获取消息统计
        Map<String, Long> messageStats = messageHandler.getMessageStats();
        assertTrue(messageStats.containsKey("trade"), "应包含交易消息统计");
        assertTrue(messageStats.containsKey("kline"), "应包含K线消息统计");
        assertEquals(2L, messageStats.get("trade"), "交易消息计数应为2");
        assertEquals(1L, messageStats.get("kline"), "K线消息计数应为1");
        
        // 获取性能统计
        EnhancedMessageHandler.MessageHandlerPerformanceStats perfStats = messageHandler.getPerformanceStats();
        assertTrue(perfStats.getTotalMessagesProcessed() >= 3, "总处理消息数应至少为3");
        
        // 获取统计摘要
        String summary = messageHandler.getStatsSummary();
        assertNotNull(summary, "统计摘要不应为空");
        assertTrue(summary.contains("增强版消息处理器统计"), "统计摘要格式不正确");
        
        log.info("统计信息功能测试通过");
        log.info("统计摘要:\n{}", summary);
    }
}
