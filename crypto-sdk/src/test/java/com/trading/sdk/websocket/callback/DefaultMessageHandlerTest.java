package com.trading.sdk.websocket.callback;

import com.trading.common.dto.Symbol;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 默认消息处理器测试
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class DefaultMessageHandlerTest {
    
    private DefaultMessageHandler messageHandler;
    
    @BeforeEach
    void setUp() {
        messageHandler = new DefaultMessageHandler();
    }
    
    @Test
    void testHandleKlineMessage_ValidMessage() {
        String subscriptionKey = "test-kline-subscription";
        Symbol symbol = Symbol.of("BTCUSDT");
        String interval = "1m";
        String message = """
                {
                    "e": "kline",
                    "E": *************,
                    "s": "BTCUSDT",
                    "k": {
                        "t": 1672515780000,
                        "T": 1672515839999,
                        "s": "BTCUSDT",
                        "i": "1m",
                        "f": 100,
                        "L": 200,
                        "o": "16569.********",
                        "c": "16569.********",
                        "h": "16569.********",
                        "l": "16569.********",
                        "v": "0.********",
                        "n": 0,
                        "x": false,
                        "q": "0.********",
                        "V": "0.********",
                        "Q": "0.********",
                        "B": "0"
                    }
                }
                """;
        
        // 验证处理不抛出异常
        assertDoesNotThrow(() -> 
                messageHandler.handleKlineMessage(subscriptionKey, symbol, interval, message));
    }
    
    @Test
    void testHandleKlineMessage_InvalidMessage() {
        String subscriptionKey = "test-kline-subscription";
        Symbol symbol = Symbol.of("BTCUSDT");
        String interval = "1m";
        String invalidMessage = "invalid json";
        
        // 验证处理无效消息不抛出异常（应该记录错误日志）
        assertDoesNotThrow(() -> 
                messageHandler.handleKlineMessage(subscriptionKey, symbol, interval, invalidMessage));
    }
    
    @Test
    void testHandleKlineMessage_MissingKlineData() {
        String subscriptionKey = "test-kline-subscription";
        Symbol symbol = Symbol.of("BTCUSDT");
        String interval = "1m";
        String message = """
                {
                    "e": "kline",
                    "E": *************,
                    "s": "BTCUSDT"
                }
                """;
        
        // 验证处理缺少K线数据的消息不抛出异常
        assertDoesNotThrow(() -> 
                messageHandler.handleKlineMessage(subscriptionKey, symbol, interval, message));
    }
    
    @Test
    void testHandleDepthMessage_ValidMessage() {
        String subscriptionKey = "test-depth-subscription";
        Symbol symbol = Symbol.of("BTCUSDT");
        int levels = 20;
        int speed = 100;
        String message = """
                {
                    "e": "depthUpdate",
                    "E": *************,
                    "T": *************,
                    "s": "BTCUSDT",
                    "U": 157,
                    "u": 160,
                    "pu": 149,
                    "b": [
                        ["16569.********", "0.********"],
                        ["16569.********", "1.********"]
                    ],
                    "a": [
                        ["16569.********", "0.********"],
                        ["16569.03000000", "1.********"]
                    ]
                }
                """;
        
        // 验证处理不抛出异常
        assertDoesNotThrow(() -> 
                messageHandler.handleDepthMessage(subscriptionKey, symbol, levels, speed, message));
    }
    
    @Test
    void testHandleDepthMessage_PartialDepthMessage() {
        String subscriptionKey = "test-depth-subscription";
        Symbol symbol = Symbol.of("BTCUSDT");
        int levels = 20;
        int speed = 100;
        String message = """
                {
                    "lastUpdateId": 160,
                    "bids": [
                        ["16569.********", "1.********"],
                        ["16569.********", "2.********"]
                    ],
                    "asks": [
                        ["16569.********", "1.********"],
                        ["16569.03000000", "2.********"]
                    ]
                }
                """;
        
        // 验证处理不抛出异常
        assertDoesNotThrow(() -> 
                messageHandler.handleDepthMessage(subscriptionKey, symbol, levels, speed, message));
    }
    
    @Test
    void testHandleTradeMessage_ValidMessage() {
        String subscriptionKey = "test-trade-subscription";
        Symbol symbol = Symbol.of("BTCUSDT");
        String message = """
                {
                    "e": "trade",
                    "E": *************,
                    "s": "BTCUSDT",
                    "t": 12345,
                    "p": "16569.********",
                    "q": "1.********",
                    "b": 88,
                    "a": 50,
                    "T": *************,
                    "m": true,
                    "M": true
                }
                """;
        
        // 验证处理不抛出异常
        assertDoesNotThrow(() -> 
                messageHandler.handleTradeMessage(subscriptionKey, symbol, message));
    }
    
    @Test
    void testHandleAggTradeMessage_ValidMessage() {
        String subscriptionKey = "test-aggtrade-subscription";
        Symbol symbol = Symbol.of("BTCUSDT");
        String message = """
                {
                    "e": "aggTrade",
                    "E": *************,
                    "s": "BTCUSDT",
                    "a": 26129,
                    "p": "16569.********",
                    "q": "1.********",
                    "f": 100,
                    "l": 105,
                    "T": *************,
                    "m": true,
                    "M": true
                }
                """;
        
        // 验证处理不抛出异常
        assertDoesNotThrow(() -> 
                messageHandler.handleAggTradeMessage(subscriptionKey, symbol, message));
    }
    
    @Test
    void testHandleTickerMessage_ValidMessage() {
        String subscriptionKey = "test-ticker-subscription";
        Symbol symbol = Symbol.of("BTCUSDT");
        String message = """
                {
                    "e": "24hrTicker",
                    "E": *************,
                    "s": "BTCUSDT",
                    "p": "0.0015",
                    "P": "0.18",
                    "w": "16569.********",
                    "x": "16569.********",
                    "c": "16569.********",
                    "Q": "1.********",
                    "b": "16569.********",
                    "B": "1.********",
                    "a": "16569.********",
                    "A": "1.********",
                    "o": "16569.********",
                    "h": "16569.********",
                    "l": "16569.********",
                    "v": "1000.********",
                    "q": "16569010.********",
                    "O": 1672429382136,
                    "C": *************,
                    "F": 0,
                    "L": 18150,
                    "n": 18151
                }
                """;
        
        // 验证处理不抛出异常
        assertDoesNotThrow(() -> 
                messageHandler.handleTickerMessage(subscriptionKey, symbol, message));
    }
    
    @Test
    void testHandleBookTickerMessage_ValidMessage() {
        String subscriptionKey = "test-bookticker-subscription";
        Symbol symbol = Symbol.of("BTCUSDT");
        String message = """
                {
                    "e": "bookTicker",
                    "u": 400900217,
                    "s": "BTCUSDT",
                    "b": "16569.********",
                    "B": "1.********",
                    "a": "16569.********",
                    "A": "1.********",
                    "T": *************,
                    "E": *************
                }
                """;
        
        // 验证处理不抛出异常
        assertDoesNotThrow(() -> 
                messageHandler.handleBookTickerMessage(subscriptionKey, symbol, message));
    }
    
    @Test
    void testHandleUserDataMessage_AccountUpdate() {
        String subscriptionKey = "test-userdata-subscription";
        String listenKey = "test-listen-key";
        String message = """
                {
                    "e": "ACCOUNT_UPDATE",
                    "E": *************,
                    "T": *************,
                    "a": {
                        "m": "ORDER",
                        "B": [
                            {
                                "a": "USDT",
                                "wb": "122624.********",
                                "cw": "100.********",
                                "bc": "50.********"
                            }
                        ],
                        "P": [
                            {
                                "s": "BTCUSDT",
                                "pa": "0",
                                "ep": "0.00000",
                                "cr": "200",
                                "up": "0",
                                "mt": "isolated",
                                "iw": "0.********",
                                "ps": "BOTH"
                            }
                        ]
                    }
                }
                """;
        
        // 验证处理不抛出异常
        assertDoesNotThrow(() -> 
                messageHandler.handleUserDataMessage(subscriptionKey, listenKey, message));
    }
    
    @Test
    void testHandleUserDataMessage_OrderUpdate() {
        String subscriptionKey = "test-userdata-subscription";
        String listenKey = "test-listen-key";
        String message = """
                {
                    "e": "ORDER_TRADE_UPDATE",
                    "E": *************,
                    "T": *************,
                    "o": {
                        "s": "BTCUSDT",
                        "c": "TEST",
                        "S": "SELL",
                        "o": "TRAILING_STOP_MARKET",
                        "f": "GTC",
                        "q": "0.001",
                        "p": "0",
                        "ap": "0",
                        "sp": "7103.04",
                        "x": "NEW",
                        "X": "NEW",
                        "i": 8886774,
                        "l": "0",
                        "z": "0",
                        "L": "0",
                        "n": "0",
                        "N": null,
                        "T": *************,
                        "t": 0,
                        "b": "0",
                        "a": "9.91",
                        "m": false,
                        "R": false,
                        "wt": "CONTRACT_PRICE",
                        "ot": "TRAILING_STOP_MARKET",
                        "ps": "LONG",
                        "cp": false,
                        "AP": "7476.89",
                        "cr": "5.0",
                        "pP": false,
                        "si": 0,
                        "ss": 0,
                        "rp": "0",
                        "V": "NONE",
                        "pm": "NONE",
                        "gtd": 0
                    }
                }
                """;
        
        // 验证处理不抛出异常
        assertDoesNotThrow(() -> 
                messageHandler.handleUserDataMessage(subscriptionKey, listenKey, message));
    }
    
    @Test
    void testHandleForceOrderMessage_ValidMessage() {
        String subscriptionKey = "test-forceorder-subscription";
        Symbol symbol = Symbol.of("BTCUSDT");
        String message = """
                {
                    "e": "forceOrder",
                    "E": *************,
                    "o": {
                        "s": "BTCUSDT",
                        "S": "SELL",
                        "o": "LIMIT",
                        "f": "IOC",
                        "q": "0.014",
                        "p": "4327.78",
                        "ap": "4327.78",
                        "X": "FILLED",
                        "l": "0.014",
                        "z": "0.014",
                        "T": *************
                    }
                }
                """;
        
        // 验证处理不抛出异常
        assertDoesNotThrow(() -> 
                messageHandler.handleForceOrderMessage(subscriptionKey, symbol, message));
    }
    
    @Test
    void testConnectionEventHandlers() {
        String subscriptionKey = "test-subscription";
        
        // 验证连接事件处理不抛出异常
        assertDoesNotThrow(() -> {
            messageHandler.handleConnectionOpen(subscriptionKey);
            messageHandler.handleConnectionClose(subscriptionKey, "Normal closure");
            messageHandler.handleConnectionError(subscriptionKey, new RuntimeException("Test error"));
            messageHandler.handleReconnect(subscriptionKey, 1);
        });
    }
}
