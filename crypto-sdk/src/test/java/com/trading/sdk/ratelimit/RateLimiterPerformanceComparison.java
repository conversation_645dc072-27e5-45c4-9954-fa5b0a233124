package com.trading.sdk.ratelimit;

import com.trading.common.ratelimit.EnhancedRateLimiter;
import com.trading.common.ratelimit.RateLimiter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.IntStream;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RateLimiter性能对比测试
 * 对比原始RateLimiter和EnhancedRateLimiter的性能差异
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@SpringBootTest
public class RateLimiterPerformanceComparison {

    private static final Logger log = LoggerFactory.getLogger(RateLimiterPerformanceComparison.class);

    private RateLimiter originalRateLimiter;
    private EnhancedRateLimiter enhancedRateLimiter;
    private ExecutorService executorService;

    @BeforeEach
    void setUp() {
        originalRateLimiter = new RateLimiter();
        enhancedRateLimiter = new EnhancedRateLimiter();
        executorService = Executors.newVirtualThreadPerTaskExecutor();
        log.info("性能对比测试环境初始化完成");
    }

    @Test
    @DisplayName("单线程性能对比测试")
    void testSingleThreadPerformanceComparison() {
        log.info("开始单线程性能对比测试...");

        int iterations = 50000;

        // 测试原始RateLimiter
        long originalStartTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            originalRateLimiter.tryAcquireUSDM(1);
        }
        long originalEndTime = System.nanoTime();
        double originalAvgLatencyMs = (originalEndTime - originalStartTime) / 1_000_000.0 / iterations;
        double originalThroughput = iterations / ((originalEndTime - originalStartTime) / 1_000_000_000.0);

        // 重置并测试增强型RateLimiter
        enhancedRateLimiter.resetAllStats();
        long enhancedStartTime = System.nanoTime();
        for (int i = 0; i < iterations; i++) {
            enhancedRateLimiter.tryAcquireUSDM(1);
        }
        long enhancedEndTime = System.nanoTime();
        double enhancedAvgLatencyMs = (enhancedEndTime - enhancedStartTime) / 1_000_000.0 / iterations;
        double enhancedThroughput = iterations / ((enhancedEndTime - enhancedStartTime) / 1_000_000_000.0);

        // 计算性能提升
        double latencyImprovement = ((originalAvgLatencyMs - enhancedAvgLatencyMs) / originalAvgLatencyMs) * 100;
        double throughputImprovement = ((enhancedThroughput - originalThroughput) / originalThroughput) * 100;

        log.info("单线程性能对比结果:");
        log.info("  原始RateLimiter:");
        log.info("    平均延迟: {:.6f}ms", originalAvgLatencyMs);
        log.info("    吞吐量: {:.0f} 操作/秒", originalThroughput);
        log.info("  增强型RateLimiter:");
        log.info("    平均延迟: {:.6f}ms", enhancedAvgLatencyMs);
        log.info("    吞吐量: {:.0f} 操作/秒", enhancedThroughput);
        log.info("  性能提升:");
        log.info("    延迟改善: {:.2f}%", latencyImprovement);
        log.info("    吞吐量提升: {:.2f}%", throughputImprovement);

        // 验证性能目标（考虑增强型限流器的监控开销）
        assertTrue(enhancedAvgLatencyMs < 0.1, "增强型限流器延迟应小于0.1ms");
        // 增强型限流器有额外的统计开销，吞吐量可能略低，但应在合理范围内
        assertTrue(enhancedThroughput >= originalThroughput * 0.8,
                String.format("增强型限流器吞吐量(%.0f)应不低于原始版本的80%% (%.0f)",
                        enhancedThroughput, originalThroughput * 0.8));

        log.info("单线程性能对比测试通过 ✓");
    }

    @Test
    @DisplayName("高并发性能对比测试")
    void testHighConcurrencyPerformanceComparison() throws InterruptedException {
        log.info("开始高并发性能对比测试...");

        int threadCount = 50;
        int requestsPerThread = 1000;
        int totalRequests = threadCount * requestsPerThread;

        // 测试原始RateLimiter
        PerformanceResult originalResult = runConcurrencyTest(
            "原始RateLimiter", 
            threadCount, 
            requestsPerThread, 
            () -> originalRateLimiter.tryAcquireUSDM(1, 10, TimeUnit.MILLISECONDS)
        );

        // 重置并测试增强型RateLimiter
        enhancedRateLimiter.resetAllStats();
        PerformanceResult enhancedResult = runConcurrencyTest(
            "增强型RateLimiter", 
            threadCount, 
            requestsPerThread, 
            () -> enhancedRateLimiter.tryAcquireUSDM(1, 10, TimeUnit.MILLISECONDS)
        );

        // 计算性能提升
        double latencyImprovement = ((originalResult.avgLatencyMs - enhancedResult.avgLatencyMs) / originalResult.avgLatencyMs) * 100;
        double throughputImprovement = ((enhancedResult.throughput - originalResult.throughput) / originalResult.throughput) * 100;
        double successRateImprovement = enhancedResult.successRate - originalResult.successRate;

        log.info("高并发性能对比结果:");
        log.info("  性能提升:");
        log.info("    延迟改善: {:.2f}%", latencyImprovement);
        log.info("    吞吐量提升: {:.2f}%", throughputImprovement);
        log.info("    成功率提升: {:.2f}%", successRateImprovement);

        // 验证性能目标
        assertTrue(enhancedResult.avgLatencyMs < originalResult.avgLatencyMs, "增强型限流器延迟应优于原始版本");
        assertTrue(enhancedResult.throughput >= originalResult.throughput, "增强型限流器吞吐量应不低于原始版本");

        log.info("高并发性能对比测试通过 ✓");
    }

    @Test
    @DisplayName("内存使用对比测试")
    void testMemoryUsageComparison() {
        log.info("开始内存使用对比测试...");

        Runtime runtime = Runtime.getRuntime();
        
        // 测试原始RateLimiter内存使用
        System.gc();
        long originalInitialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        for (int i = 0; i < 100000; i++) {
            originalRateLimiter.tryAcquireUSDM(1);
        }
        
        System.gc();
        long originalFinalMemory = runtime.totalMemory() - runtime.freeMemory();
        long originalMemoryIncrease = originalFinalMemory - originalInitialMemory;

        // 测试增强型RateLimiter内存使用
        enhancedRateLimiter.resetAllStats();
        System.gc();
        long enhancedInitialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        for (int i = 0; i < 100000; i++) {
            enhancedRateLimiter.tryAcquireUSDM(1);
        }
        
        System.gc();
        long enhancedFinalMemory = runtime.totalMemory() - runtime.freeMemory();
        long enhancedMemoryIncrease = enhancedFinalMemory - enhancedInitialMemory;

        // 计算内存优化
        double memoryImprovement = ((double)(originalMemoryIncrease - enhancedMemoryIncrease) / originalMemoryIncrease) * 100;

        log.info("内存使用对比结果:");
        log.info("  原始RateLimiter内存增长: {} KB", originalMemoryIncrease / 1024);
        log.info("  增强型RateLimiter内存增长: {} KB", enhancedMemoryIncrease / 1024);
        log.info("  内存优化: {:.2f}%", memoryImprovement);

        // 验证内存使用合理
        assertTrue(enhancedMemoryIncrease <= originalMemoryIncrease * 1.2, "增强型限流器内存使用应不超过原始版本的120%");

        log.info("内存使用对比测试通过 ✓");
    }

    @Test
    @DisplayName("延迟分布对比测试")
    void testLatencyDistributionComparison() {
        log.info("开始延迟分布对比测试...");

        int samples = 10000;
        
        // 测试原始RateLimiter延迟分布
        long[] originalLatencies = new long[samples];
        for (int i = 0; i < samples; i++) {
            long startTime = System.nanoTime();
            originalRateLimiter.tryAcquireUSDM(1);
            originalLatencies[i] = System.nanoTime() - startTime;
        }

        // 测试增强型RateLimiter延迟分布
        enhancedRateLimiter.resetAllStats();
        long[] enhancedLatencies = new long[samples];
        for (int i = 0; i < samples; i++) {
            long startTime = System.nanoTime();
            enhancedRateLimiter.tryAcquireUSDM(1);
            enhancedLatencies[i] = System.nanoTime() - startTime;
        }

        // 计算统计指标
        LatencyStats originalStats = calculateLatencyStats(originalLatencies);
        LatencyStats enhancedStats = calculateLatencyStats(enhancedLatencies);

        log.info("延迟分布对比结果:");
        log.info("  原始RateLimiter:");
        log.info("    平均延迟: {:.3f}ms", originalStats.avgMs);
        log.info("    P50延迟: {:.3f}ms", originalStats.p50Ms);
        log.info("    P95延迟: {:.3f}ms", originalStats.p95Ms);
        log.info("    P99延迟: {:.3f}ms", originalStats.p99Ms);
        log.info("  增强型RateLimiter:");
        log.info("    平均延迟: {:.3f}ms", enhancedStats.avgMs);
        log.info("    P50延迟: {:.3f}ms", enhancedStats.p50Ms);
        log.info("    P95延迟: {:.3f}ms", enhancedStats.p95Ms);
        log.info("    P99延迟: {:.3f}ms", enhancedStats.p99Ms);

        // 验证延迟改善（允许合理的性能波动）
        // P95延迟允许有10%的波动，因为增强型限流器有额外的统计开销
        assertTrue(enhancedStats.p95Ms <= originalStats.p95Ms * 1.1,
                String.format("增强型限流器P95延迟(%.3fms)应不超过原始版本的110%% (%.3fms)",
                        enhancedStats.p95Ms, originalStats.p95Ms * 1.1));
        assertTrue(enhancedStats.p99Ms <= originalStats.p99Ms * 1.2,
                String.format("增强型限流器P99延迟(%.3fms)应不超过原始版本的120%% (%.3fms)",
                        enhancedStats.p99Ms, originalStats.p99Ms * 1.2));

        log.info("延迟分布对比测试通过 ✓");
    }

    /**
     * 运行并发测试
     */
    private PerformanceResult runConcurrencyTest(String name, int threadCount, int requestsPerThread, 
                                                Callable<Boolean> operation) throws InterruptedException {
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(threadCount);
        AtomicLong totalLatency = new AtomicLong(0);
        AtomicLong successCount = new AtomicLong(0);
        AtomicLong totalCount = new AtomicLong(0);

        for (int i = 0; i < threadCount; i++) {
            executorService.submit(() -> {
                try {
                    startLatch.await();
                    
                    for (int j = 0; j < requestsPerThread; j++) {
                        long startTime = System.nanoTime();
                        boolean success = operation.call();
                        long endTime = System.nanoTime();
                        
                        totalLatency.addAndGet(endTime - startTime);
                        totalCount.incrementAndGet();
                        if (success) {
                            successCount.incrementAndGet();
                        }
                    }
                } catch (Exception e) {
                    log.error("并发测试执行错误", e);
                } finally {
                    finishLatch.countDown();
                }
            });
        }

        long testStartTime = System.currentTimeMillis();
        startLatch.countDown();
        finishLatch.await();
        long testEndTime = System.currentTimeMillis();

        double avgLatencyMs = totalLatency.get() / 1_000_000.0 / totalCount.get();
        double successRate = (double) successCount.get() / totalCount.get() * 100;
        double throughput = (double) totalCount.get() / (testEndTime - testStartTime) * 1000;

        log.info("{}并发测试结果:", name);
        log.info("  总请求数: {}", totalCount.get());
        log.info("  成功请求数: {}", successCount.get());
        log.info("  成功率: {:.2f}%", successRate);
        log.info("  平均延迟: {:.3f}ms", avgLatencyMs);
        log.info("  吞吐量: {:.2f} 请求/秒", throughput);

        return new PerformanceResult(avgLatencyMs, successRate, throughput);
    }

    /**
     * 计算延迟统计
     */
    private LatencyStats calculateLatencyStats(long[] latencies) {
        java.util.Arrays.sort(latencies);
        
        double avgNs = java.util.Arrays.stream(latencies).average().orElse(0);
        long p50Ns = latencies[latencies.length / 2];
        long p95Ns = latencies[(int) (latencies.length * 0.95)];
        long p99Ns = latencies[(int) (latencies.length * 0.99)];

        return new LatencyStats(
            avgNs / 1_000_000.0,
            p50Ns / 1_000_000.0,
            p95Ns / 1_000_000.0,
            p99Ns / 1_000_000.0
        );
    }

    /**
     * 性能结果数据类
     */
    private static class PerformanceResult {
        final double avgLatencyMs;
        final double successRate;
        final double throughput;

        PerformanceResult(double avgLatencyMs, double successRate, double throughput) {
            this.avgLatencyMs = avgLatencyMs;
            this.successRate = successRate;
            this.throughput = throughput;
        }
    }

    /**
     * 延迟统计数据类
     */
    private static class LatencyStats {
        final double avgMs;
        final double p50Ms;
        final double p95Ms;
        final double p99Ms;

        LatencyStats(double avgMs, double p50Ms, double p95Ms, double p99Ms) {
            this.avgMs = avgMs;
            this.p50Ms = p50Ms;
            this.p95Ms = p95Ms;
            this.p99Ms = p99Ms;
        }
    }
}
