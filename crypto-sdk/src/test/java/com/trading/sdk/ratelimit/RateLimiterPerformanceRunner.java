package com.trading.sdk.ratelimit;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import com.trading.common.ratelimit.EnhancedRateLimiter;
import com.trading.common.ratelimit.RateLimiter;

/**
 * 限流器性能测试运行器
 * 用于验证原始RateLimiter和EnhancedRateLimiter的性能差异
 */
public class RateLimiterPerformanceRunner {
    
    public static void main(String[] args) {
        RateLimiterPerformanceRunner runner = new RateLimiterPerformanceRunner();
        
        System.out.println("=== 币安量化交易系统 - 限流器性能对比测试 ===");

        // 单线程性能测试
        runner.testSingleThreadPerformance();

        // 高并发性能测试
        runner.testHighConcurrencyPerformance();

        // 内存使用测试
        runner.testMemoryUsage();

        System.out.println("=== 性能测试完成 ===");
    }
    
    /**
     * 单线程性能测试
     */
    private void testSingleThreadPerformance() {
        System.out.println("\n--- 单线程性能测试 ---");
        
        RateLimiter originalLimiter = new RateLimiter();
        EnhancedRateLimiter enhancedLimiter = new EnhancedRateLimiter();
        
        int testCount = 10000;
        
        // 测试原始限流器
        long startTime = System.nanoTime();
        int originalSuccess = 0;
        for (int i = 0; i < testCount; i++) {
            if (originalLimiter.tryAcquireUSDM(1, 1, TimeUnit.MILLISECONDS)) {
                originalSuccess++;
            }
        }
        long originalTime = System.nanoTime() - startTime;
        
        // 测试增强限流器
        startTime = System.nanoTime();
        int enhancedSuccess = 0;
        for (int i = 0; i < testCount; i++) {
            if (enhancedLimiter.tryAcquireUSDM(1, 1, TimeUnit.MILLISECONDS)) {
                enhancedSuccess++;
            }
        }
        long enhancedTime = System.nanoTime() - startTime;
        
        System.out.printf("原始限流器: %d次请求, %d次成功, 耗时: %.2fms, 平均延迟: %.2fμs%n",
                testCount, originalSuccess, originalTime / 1_000_000.0, originalTime / testCount / 1000.0);
        System.out.printf("增强限流器: %d次请求, %d次成功, 耗时: %.2fms, 平均延迟: %.2fμs%n",
                testCount, enhancedSuccess, enhancedTime / 1_000_000.0, enhancedTime / testCount / 1000.0);
        System.out.printf("性能提升: %.2f%%%n", ((double)(originalTime - enhancedTime) / originalTime) * 100);
    }
    
    /**
     * 高并发性能测试
     */
    private void testHighConcurrencyPerformance() {
        System.out.println("\n--- 高并发性能测试 ---");
        
        RateLimiter originalLimiter = new RateLimiter();
        EnhancedRateLimiter enhancedLimiter = new EnhancedRateLimiter();
        
        int threadCount = 100;
        int requestsPerThread = 100;
        
        // 测试原始限流器
        testConcurrentPerformance("原始限流器", originalLimiter, threadCount, requestsPerThread);
        
        // 测试增强限流器
        testConcurrentPerformance("增强限流器", enhancedLimiter, threadCount, requestsPerThread);
    }
    
    private void testConcurrentPerformance(String name, Object limiter, int threadCount, int requestsPerThread) {
        ExecutorService executor = Executors.newVirtualThreadPerTaskExecutor();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicLong totalLatency = new AtomicLong(0);
        
        long startTime = System.nanoTime();
        
        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                for (int j = 0; j < requestsPerThread; j++) {
                    long requestStart = System.nanoTime();
                    boolean success = false;
                    
                    if (limiter instanceof RateLimiter) {
                        success = ((RateLimiter) limiter).tryAcquireUSDM(1, 1, TimeUnit.MILLISECONDS);
                    } else if (limiter instanceof EnhancedRateLimiter) {
                        success = ((EnhancedRateLimiter) limiter).tryAcquireUSDM(1, 1, TimeUnit.MILLISECONDS);
                    }
                    
                    if (success) {
                        successCount.incrementAndGet();
                        totalLatency.addAndGet(System.nanoTime() - requestStart);
                    }
                }
            });
        }
        
        executor.shutdown();
        try {
            executor.awaitTermination(30, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        long totalTime = System.nanoTime() - startTime;
        int totalRequests = threadCount * requestsPerThread;
        int successRequests = successCount.get();
        
        System.out.printf("%s: %d个线程, 每线程%d请求, 总请求%d次%n", name, threadCount, requestsPerThread, totalRequests);
        System.out.printf("  成功: %d次, 成功率: %.2f%%%n", successRequests, (double)successRequests / totalRequests * 100);
        System.out.printf("  总耗时: %.2fms, 吞吐量: %.2f请求/秒%n", totalTime / 1_000_000.0, (double)successRequests / (totalTime / 1_000_000_000.0));
        if (successRequests > 0) {
            System.out.printf("  平均延迟: %.2fμs%n", (double)totalLatency.get() / successRequests / 1000.0);
        }
    }
    
    /**
     * 内存使用测试
     */
    private void testMemoryUsage() {
        System.out.println("\n--- 内存使用测试 ---");
        
        Runtime runtime = Runtime.getRuntime();
        
        // 测试原始限流器内存使用
        runtime.gc();
        long beforeOriginal = runtime.totalMemory() - runtime.freeMemory();
        
        RateLimiter[] originalLimiters = new RateLimiter[1000];
        for (int i = 0; i < originalLimiters.length; i++) {
            originalLimiters[i] = new RateLimiter();
        }
        
        long afterOriginal = runtime.totalMemory() - runtime.freeMemory();
        long originalMemory = afterOriginal - beforeOriginal;
        
        // 测试增强限流器内存使用
        runtime.gc();
        long beforeEnhanced = runtime.totalMemory() - runtime.freeMemory();
        
        EnhancedRateLimiter[] enhancedLimiters = new EnhancedRateLimiter[1000];
        for (int i = 0; i < enhancedLimiters.length; i++) {
            enhancedLimiters[i] = new EnhancedRateLimiter();
        }
        
        long afterEnhanced = runtime.totalMemory() - runtime.freeMemory();
        long enhancedMemory = afterEnhanced - beforeEnhanced;
        
        System.out.printf("原始限流器内存使用: %d KB (每个实例约 %d bytes)%n", originalMemory / 1024, originalMemory / 1000);
        System.out.printf("增强限流器内存使用: %d KB (每个实例约 %d bytes)%n", enhancedMemory / 1024, enhancedMemory / 1000);
        System.out.printf("内存优化: %.2f%%%n", ((double)(originalMemory - enhancedMemory) / originalMemory) * 100);
        
        // 防止被GC回收
        System.out.println("创建的限流器实例数: " + (originalLimiters.length + enhancedLimiters.length));
    }
}
