# 测试环境配置
spring:
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  profiles:
    active: test
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration
      - org.springframework.boot.autoconfigure.kafka.KafkaAutoConfiguration
      - org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration

# 测试用配置
trading:
  sdk:
    binance:
      api-key: test-key
      secret-key: test-secret
      testnet: true
    pool:
      core-size: 2
      max-size: 4
      queue-capacity: 100
    retry:
      max-attempts: 3
      delay: 1000
    websocket:
      connection-timeout: 5000
      read-timeout: 10000
      max-connections: 5

# Binance配置（兼容性）
binance:
  api-key: test-key
  secret-key: test-secret
  testnet: true

# InfluxDB配置（测试环境启用）
influxdb:
  enabled: true
  url: http://localhost:8086
  token: test-token
  org: test-org
  bucket: test-bucket

# MySQL配置（测试环境）
mysql:
  enabled: false

# 日志配置
logging:
  level:
    com.trading: WARN
    org.springframework: WARN
    root: WARN
    org.springframework.test: WARN
