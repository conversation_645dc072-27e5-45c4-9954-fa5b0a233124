package com.trading.sdk.api.impl;

import com.trading.sdk.api.MarketDataApi;
import com.trading.sdk.api.RestApiClient;
import com.trading.common.api.ApiRequest;
import com.trading.common.api.ApiResponse;
import com.trading.common.enums.HttpMethod;
import com.trading.common.exception.SdkException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Binance市场数据API实现
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class BinanceMarketDataApi implements MarketDataApi {

    private static final Logger log = LoggerFactory.getLogger(BinanceMarketDataApi.class);
    
    private final RestApiClient restApiClient;
    
    public BinanceMarketDataApi(RestApiClient restApiClient) {
        this.restApiClient = restApiClient;
    }
    
    @Override
    public ApiResponse<Map<String, Object>> getServerTime() throws SdkException {
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/time")
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> getServerTimeAsync() {
        return restApiClient.executeAsync(ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/time")
            .build());
    }
    
    @Override
    public ApiResponse<Map<String, Object>> getExchangeInfo() throws SdkException {
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/exchangeInfo")
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> getExchangeInfoAsync() {
        return restApiClient.executeAsync(ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/exchangeInfo")
            .build());
    }
    
    @Override
    public ApiResponse<Map<String, Object>> getOrderBook(String symbol, Integer limit) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        if (limit != null) {
            parameters.put("limit", limit);
        }
        
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/depth")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> getOrderBookAsync(String symbol, Integer limit) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getOrderBook(symbol, limit);
            } catch (SdkException e) {
                return ApiResponse.error("ORDER_BOOK_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<List<Map<String, Object>>> getRecentTrades(String symbol, Integer limit) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        if (limit != null) {
            parameters.put("limit", limit);
        }
        
        ApiRequest<List<Map<String, Object>>> request = ApiRequest.<List<Map<String, Object>>>builder()
            .responseType((Class<List<Map<String, Object>>>)(Class<?>)List.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/trades")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<Map<String, Object>>>> getRecentTradesAsync(String symbol, Integer limit) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getRecentTrades(symbol, limit);
            } catch (SdkException e) {
                return ApiResponse.error("RECENT_TRADES_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<List<Map<String, Object>>> getHistoricalTrades(String symbol, Integer limit, Long fromId) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        if (limit != null) {
            parameters.put("limit", limit);
        }
        if (fromId != null) {
            parameters.put("fromId", fromId);
        }
        
        ApiRequest<List<Map<String, Object>>> request = ApiRequest.<List<Map<String, Object>>>builder()
            .responseType((Class<List<Map<String, Object>>>)(Class<?>)List.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/aggTrades")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<Map<String, Object>>>> getHistoricalTradesAsync(String symbol, Integer limit, Long fromId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getHistoricalTrades(symbol, limit, fromId);
            } catch (SdkException e) {
                return ApiResponse.error("HISTORICAL_TRADES_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<List<Map<String, Object>>> getAggTrades(String symbol, Long fromId, Long startTime, Long endTime, Integer limit) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        if (fromId != null) {
            parameters.put("fromId", fromId);
        }
        if (startTime != null) {
            parameters.put("startTime", startTime);
        }
        if (endTime != null) {
            parameters.put("endTime", endTime);
        }
        if (limit != null) {
            parameters.put("limit", limit);
        }
        
        ApiRequest<List<Map<String, Object>>> request = ApiRequest.<List<Map<String, Object>>>builder()
            .responseType((Class<List<Map<String, Object>>>)(Class<?>)List.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/aggTrades")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<Map<String, Object>>>> getAggTradesAsync(String symbol, Long fromId, Long startTime, Long endTime, Integer limit) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getAggTrades(symbol, fromId, startTime, endTime, limit);
            } catch (SdkException e) {
                return ApiResponse.error("AGG_TRADES_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<List<List<Object>>> getKlines(String symbol, String interval, Long startTime, Long endTime, Integer limit) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        parameters.put("interval", interval);
        if (startTime != null) {
            parameters.put("startTime", startTime);
        }
        if (endTime != null) {
            parameters.put("endTime", endTime);
        }
        if (limit != null) {
            parameters.put("limit", limit);
        }

        ApiRequest<List<List<Object>>> request = ApiRequest.<List<List<Object>>>builder()
                .responseType((Class<List<List<Object>>>)(Class<?>)List.class)
                .path("/fapi/v1/klines")
                .method(HttpMethod.GET)
                .parameters(parameters)
                .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<List<Object>>>> getKlinesAsync(String symbol, String interval, Long startTime, Long endTime, Integer limit) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getKlines(symbol, interval, startTime, endTime, limit);
            } catch (SdkException e) {
                return ApiResponse.error("KLINES_ERROR", e.getMessage());
            }
        });
    }

    @Override
    public ApiResponse<Object> get24hrTicker(String symbol) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        if (symbol != null) {
            parameters.put("symbol", symbol);
        }

        ApiRequest<Object> request = ApiRequest.<Object>builder()
            .responseType(Object.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/ticker/24hr")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }

    @Override
    public CompletableFuture<ApiResponse<Object>> get24hrTickerAsync(String symbol) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return get24hrTicker(symbol);
            } catch (SdkException e) {
                return ApiResponse.error("24HR_TICKER_ERROR", e.getMessage());
            }
        });
    }

    @Override
    public ApiResponse<Object> getTickerPrice(String symbol) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        if (symbol != null) {
            parameters.put("symbol", symbol);
        }

        ApiRequest<Object> request = ApiRequest.<Object>builder()
            .responseType(Object.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/ticker/price")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }

    @Override
    public CompletableFuture<ApiResponse<Object>> getTickerPriceAsync(String symbol) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getTickerPrice(symbol);
            } catch (SdkException e) {
                return ApiResponse.error("TICKER_PRICE_ERROR", e.getMessage());
            }
        });
    }

    @Override
    public ApiResponse<Object> getBookTicker(String symbol) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        if (symbol != null) {
            parameters.put("symbol", symbol);
        }

        ApiRequest<Object> request = ApiRequest.<Object>builder()
            .responseType(Object.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/ticker/bookTicker")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }

    @Override
    public CompletableFuture<ApiResponse<Object>> getBookTickerAsync(String symbol) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getBookTicker(symbol);
            } catch (SdkException e) {
                return ApiResponse.error("BOOK_TICKER_ERROR", e.getMessage());
            }
        });
    }
}
