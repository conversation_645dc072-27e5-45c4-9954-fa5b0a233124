package com.trading.sdk.api;

import com.trading.common.api.ApiResponse;
import com.trading.common.exception.SdkException;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 账户API接口
 * 提供账户信息、余额、持仓等功能
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public interface AccountApi {
    
    /**
     * 获取账户信息
     * 
     * @return 账户信息响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<Map<String, Object>> getAccountInfo() throws SdkException;
    
    /**
     * 异步获取账户信息
     * 
     * @return 异步账户信息响应
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> getAccountInfoAsync();
    
    /**
     * 获取期货账户余额
     * 
     * @return 期货账户余额响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<List<Map<String, Object>>> getFuturesAccountBalance() throws SdkException;
    
    /**
     * 异步获取期货账户余额
     * 
     * @return 异步期货账户余额响应
     */
    CompletableFuture<ApiResponse<List<Map<String, Object>>>> getFuturesAccountBalanceAsync();
    
    /**
     * 获取持仓信息
     * 
     * @param symbol 交易对符号（可选）
     * @return 持仓信息响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<List<Map<String, Object>>> getPositionInfo(String symbol) throws SdkException;
    
    /**
     * 异步获取持仓信息
     * 
     * @param symbol 交易对符号（可选）
     * @return 异步持仓信息响应
     */
    CompletableFuture<ApiResponse<List<Map<String, Object>>>> getPositionInfoAsync(String symbol);
    
    /**
     * 获取账户交易状态
     * 
     * @return 账户交易状态响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<Map<String, Object>> getTradingStatus() throws SdkException;
    
    /**
     * 异步获取账户交易状态
     * 
     * @return 异步账户交易状态响应
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> getTradingStatusAsync();
    
    /**
     * 获取API交易状态
     * 
     * @return API交易状态响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<Map<String, Object>> getApiTradingStatus() throws SdkException;
    
    /**
     * 异步获取API交易状态
     * 
     * @return 异步API交易状态响应
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> getApiTradingStatusAsync();
    
    /**
     * 获取佣金费率
     * 
     * @param symbol 交易对符号
     * @return 佣金费率响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<Map<String, Object>> getCommissionRate(String symbol) throws SdkException;
    
    /**
     * 异步获取佣金费率
     * 
     * @param symbol 交易对符号
     * @return 异步佣金费率响应
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> getCommissionRateAsync(String symbol);
    
    /**
     * 获取收入历史
     * 
     * @param symbol 交易对符号（可选）
     * @param incomeType 收入类型（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 返回数量限制（可选）
     * @return 收入历史响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<List<Map<String, Object>>> getIncomeHistory(String symbol, String incomeType, 
                                                            Long startTime, Long endTime, Integer limit) throws SdkException;
    
    /**
     * 异步获取收入历史
     * 
     * @param symbol 交易对符号（可选）
     * @param incomeType 收入类型（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 返回数量限制（可选）
     * @return 异步收入历史响应
     */
    CompletableFuture<ApiResponse<List<Map<String, Object>>>> getIncomeHistoryAsync(String symbol, String incomeType, 
                                                                                    Long startTime, Long endTime, Integer limit);
    
    /**
     * 调整杠杆倍数
     * 
     * @param symbol 交易对符号
     * @param leverage 杠杆倍数
     * @return 调整杠杆倍数响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<Map<String, Object>> changeInitialLeverage(String symbol, int leverage) throws SdkException;
    
    /**
     * 异步调整杠杆倍数
     * 
     * @param symbol 交易对符号
     * @param leverage 杠杆倍数
     * @return 异步调整杠杆倍数响应
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> changeInitialLeverageAsync(String symbol, int leverage);
    
    /**
     * 变更保证金模式
     * 
     * @param symbol 交易对符号
     * @param marginType 保证金模式（ISOLATED/CROSSED）
     * @return 变更保证金模式响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<Map<String, Object>> changeMarginType(String symbol, String marginType) throws SdkException;
    
    /**
     * 异步变更保证金模式
     * 
     * @param symbol 交易对符号
     * @param marginType 保证金模式（ISOLATED/CROSSED）
     * @return 异步变更保证金模式响应
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> changeMarginTypeAsync(String symbol, String marginType);
    
    /**
     * 调整逐仓保证金
     * 
     * @param symbol 交易对符号
     * @param positionSide 持仓方向（BOTH/LONG/SHORT）
     * @param amount 调整数量
     * @param type 调整类型（1增加，2减少）
     * @return 调整逐仓保证金响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<Map<String, Object>> modifyIsolatedPositionMargin(String symbol, String positionSide, 
                                                                  String amount, int type) throws SdkException;
    
    /**
     * 异步调整逐仓保证金
     * 
     * @param symbol 交易对符号
     * @param positionSide 持仓方向（BOTH/LONG/SHORT）
     * @param amount 调整数量
     * @param type 调整类型（1增加，2减少）
     * @return 异步调整逐仓保证金响应
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> modifyIsolatedPositionMarginAsync(String symbol, String positionSide, 
                                                                                          String amount, int type);
    
    /**
     * 获取逐仓保证金变动历史
     * 
     * @param symbol 交易对符号
     * @param type 调整类型（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 返回数量限制（可选）
     * @return 逐仓保证金变动历史响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<List<Map<String, Object>>> getPositionMarginHistory(String symbol, Integer type, 
                                                                    Long startTime, Long endTime, Integer limit) throws SdkException;
    
    /**
     * 异步获取逐仓保证金变动历史
     * 
     * @param symbol 交易对符号
     * @param type 调整类型（可选）
     * @param startTime 开始时间（可选）
     * @param endTime 结束时间（可选）
     * @param limit 返回数量限制（可选）
     * @return 异步逐仓保证金变动历史响应
     */
    CompletableFuture<ApiResponse<List<Map<String, Object>>>> getPositionMarginHistoryAsync(String symbol, Integer type, 
                                                                                            Long startTime, Long endTime, Integer limit);
}
