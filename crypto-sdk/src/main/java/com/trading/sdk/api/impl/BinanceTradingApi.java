package com.trading.sdk.api.impl;

import com.trading.sdk.api.TradingApi;
import com.trading.sdk.api.RestApiClient;
import com.trading.common.api.ApiRequest;
import com.trading.common.api.ApiResponse;
import com.trading.common.enums.HttpMethod;
import com.trading.common.exception.SdkException;
import com.trading.common.utils.JsonUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Binance交易API实现
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class BinanceTradingApi implements TradingApi {

    private static final Logger log = LoggerFactory.getLogger(BinanceTradingApi.class);
    
    private final RestApiClient restApiClient;
    
    public BinanceTradingApi(RestApiClient restApiClient) {
        this.restApiClient = restApiClient;
    }
    
    @Override
    public ApiResponse<Map<String, Object>> testNewOrder(String symbol, String side, String type, 
                                                         BigDecimal quantity, BigDecimal price, String timeInForce) throws SdkException {
        // Binance Futures API does not support test orders via /fapi/v1/order/test
        // This is a simulated response for parameter validation.
        Map<String, Object> mockResponse = new HashMap<>();
        mockResponse.put("symbol", symbol);
        mockResponse.put("side", side);
        mockResponse.put("type", type);
        mockResponse.put("quantity", quantity.toString());
        if (price != null) {
            mockResponse.put("price", price.toString());
        }
        if (timeInForce != null) {
            mockResponse.put("timeInForce", timeInForce);
        }
        mockResponse.put("status", "TEST_SUCCESS");
        mockResponse.put("message", "Order parameters validated successfully (simulation).");

        return ApiResponse.success(mockResponse, "SIMULATED_TEST_ORDER_SUCCESS");
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> testNewOrderAsync(String symbol, String side, String type, 
                                                                                 BigDecimal quantity, BigDecimal price, String timeInForce) {
        return CompletableFuture.supplyAsync(() -> testNewOrder(symbol, side, type, quantity, price, timeInForce));
    }
    
    @Override
    public ApiResponse<Map<String, Object>> newOrder(String symbol, String side, String type, 
                                                     BigDecimal quantity, BigDecimal price, String timeInForce) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        parameters.put("side", side);
        parameters.put("type", type);
        parameters.put("quantity", quantity.toString());
        if (price != null) {
            parameters.put("price", price.toString());
        }
        if (timeInForce != null) {
            parameters.put("timeInForce", timeInForce);
        }
        
        return newOrder(parameters);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> newOrderAsync(String symbol, String side, String type, 
                                                                             BigDecimal quantity, BigDecimal price, String timeInForce) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return newOrder(symbol, side, type, quantity, price, timeInForce);
            } catch (SdkException e) {
                return ApiResponse.error("NEW_ORDER_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<Map<String, Object>> newOrder(Map<String, Object> orderParams) throws SdkException {
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.POST)
            .path("/fapi/v1/order")
            .parameters(orderParams)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> newOrderAsync(Map<String, Object> orderParams) {
        return restApiClient.executeAsync(ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.POST)
            .path("/fapi/v1/order")
            .parameters(orderParams)
            .build());
    }
    
    @Override
    public ApiResponse<Map<String, Object>> cancelOrder(String symbol, Long orderId, String origClientOrderId) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        if (orderId != null) {
            parameters.put("orderId", orderId);
        }
        if (origClientOrderId != null) {
            parameters.put("origClientOrderId", origClientOrderId);
        }
        
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.DELETE)
            .path("/fapi/v1/order")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> cancelOrderAsync(String symbol, Long orderId, String origClientOrderId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return cancelOrder(symbol, orderId, origClientOrderId);
            } catch (SdkException e) {
                return ApiResponse.error("CANCEL_ORDER_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<List<Map<String, Object>>> cancelAllOrders(String symbol) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        
        ApiRequest<List<Map<String, Object>>> request = ApiRequest.<List<Map<String, Object>>>builder()
            .responseType((Class<List<Map<String, Object>>>)(Class<?>)List.class)
            .method(HttpMethod.DELETE)
            .path("/fapi/v1/allOpenOrders")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<Map<String, Object>>>> cancelAllOrdersAsync(String symbol) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return cancelAllOrders(symbol);
            } catch (SdkException e) {
                return ApiResponse.error("CANCEL_ALL_ORDERS_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<Map<String, Object>> queryOrder(String symbol, Long orderId, String origClientOrderId) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        if (orderId != null) {
            parameters.put("orderId", orderId);
        }
        if (origClientOrderId != null) {
            parameters.put("origClientOrderId", origClientOrderId);
        }
        
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/order")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> queryOrderAsync(String symbol, Long orderId, String origClientOrderId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return queryOrder(symbol, orderId, origClientOrderId);
            } catch (SdkException e) {
                return ApiResponse.error("QUERY_ORDER_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<List<Map<String, Object>>> getCurrentOrders(String symbol) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        if (symbol != null) {
            parameters.put("symbol", symbol);
        }
        
        ApiRequest<List<Map<String, Object>>> request = ApiRequest.<List<Map<String, Object>>>builder()
            .responseType((Class<List<Map<String, Object>>>)(Class<?>)List.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/openOrders")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<Map<String, Object>>>> getCurrentOrdersAsync(String symbol) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getCurrentOrders(symbol);
            } catch (SdkException e) {
                return ApiResponse.error("CURRENT_ORDERS_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<List<Map<String, Object>>> getAllOrders(String symbol, Long orderId, Long startTime, Long endTime, Integer limit) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        if (orderId != null) {
            parameters.put("orderId", orderId);
        }
        if (startTime != null) {
            parameters.put("startTime", startTime);
        }
        if (endTime != null) {
            parameters.put("endTime", endTime);
        }
        if (limit != null) {
            parameters.put("limit", limit);
        }
        
        ApiRequest<List<Map<String, Object>>> request = ApiRequest.<List<Map<String, Object>>>builder()
            .responseType((Class<List<Map<String, Object>>>)(Class<?>)List.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/allOrders")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<Map<String, Object>>>> getAllOrdersAsync(String symbol, Long orderId, Long startTime, Long endTime, Integer limit) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getAllOrders(symbol, orderId, startTime, endTime, limit);
            } catch (SdkException e) {
                return ApiResponse.error("ALL_ORDERS_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<List<Map<String, Object>>> getMyTrades(String symbol, Long orderId, Long startTime, Long endTime, Long fromId, Integer limit) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        if (orderId != null) {
            parameters.put("orderId", orderId);
        }
        if (startTime != null) {
            parameters.put("startTime", startTime);
        }
        if (endTime != null) {
            parameters.put("endTime", endTime);
        }
        if (fromId != null) {
            parameters.put("fromId", fromId);
        }
        if (limit != null) {
            parameters.put("limit", limit);
        }
        
        ApiRequest<List<Map<String, Object>>> request = ApiRequest.<List<Map<String, Object>>>builder()
            .responseType((Class<List<Map<String, Object>>>)(Class<?>)List.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/userTrades")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<Map<String, Object>>>> getMyTradesAsync(String symbol, Long orderId, Long startTime, Long endTime, Long fromId, Integer limit) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getMyTrades(symbol, orderId, startTime, endTime, fromId, limit);
            } catch (SdkException e) {
                return ApiResponse.error("MY_TRADES_ERROR", e.getMessage());
            }
        });
    }
}
