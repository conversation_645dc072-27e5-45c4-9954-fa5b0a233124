package com.trading.sdk.api;

import com.trading.common.dto.KlineData;
import com.trading.common.dto.DepthData;
import com.trading.common.dto.TickerData;
import com.trading.common.dto.TradeData;
import com.trading.common.api.ApiResponse;
import com.trading.common.exception.SdkException;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 强类型市场数据API接口
 * 提供类型安全的市场数据访问方法
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public interface StrongTypedMarketDataApi {
    
    /**
     * 获取K线数据
     * 
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param limit 限制数量
     * @return K线数据列表
     * @throws SdkException API异常
     */
    ApiResponse<List<KlineData>> getKlines(String symbol, String interval, Long startTime, Long endTime, Integer limit) throws SdkException;
    
    /**
     * 异步获取K线数据
     * 
     * @param symbol 交易对符号
     * @param interval 时间间隔
     * @param startTime 开始时间戳
     * @param endTime 结束时间戳
     * @param limit 限制数量
     * @return K线数据列表的CompletableFuture
     */
    CompletableFuture<ApiResponse<List<KlineData>>> getKlinesAsync(String symbol, String interval, Long startTime, Long endTime, Integer limit);
    
    /**
     * 获取订单簿深度数据
     * 
     * @param symbol 交易对符号
     * @param limit 限制数量
     * @return 深度数据
     * @throws SdkException API异常
     */
    ApiResponse<DepthData> getOrderBook(String symbol, Integer limit) throws SdkException;
    
    /**
     * 异步获取订单簿深度数据
     * 
     * @param symbol 交易对符号
     * @param limit 限制数量
     * @return 深度数据的CompletableFuture
     */
    CompletableFuture<ApiResponse<DepthData>> getOrderBookAsync(String symbol, Integer limit);
    
    /**
     * 获取24小时价格变动统计
     * 
     * @param symbol 交易对符号
     * @return 价格统计数据
     * @throws SdkException API异常
     */
    ApiResponse<TickerData> get24hrTicker(String symbol) throws SdkException;
    
    /**
     * 异步获取24小时价格变动统计
     * 
     * @param symbol 交易对符号
     * @return 价格统计数据的CompletableFuture
     */
    CompletableFuture<ApiResponse<TickerData>> get24hrTickerAsync(String symbol);
    
    /**
     * 获取最近成交记录
     * 
     * @param symbol 交易对符号
     * @param limit 限制数量
     * @return 交易数据列表
     * @throws SdkException API异常
     */
    ApiResponse<List<TradeData>> getRecentTrades(String symbol, Integer limit) throws SdkException;
    
    /**
     * 异步获取最近成交记录
     * 
     * @param symbol 交易对符号
     * @param limit 限制数量
     * @return 交易数据列表的CompletableFuture
     */
    CompletableFuture<ApiResponse<List<TradeData>>> getRecentTradesAsync(String symbol, Integer limit);
}