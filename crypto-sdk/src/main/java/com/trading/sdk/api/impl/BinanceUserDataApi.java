package com.trading.sdk.api.impl;

import com.trading.sdk.api.UserDataApi;
import com.trading.sdk.api.RestApiClient;
import com.trading.common.api.ApiRequest;
import com.trading.common.api.ApiResponse;
import com.trading.common.enums.HttpMethod;
import com.trading.common.exception.SdkException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Binance用户数据API实现
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class BinanceUserDataApi implements UserDataApi {

    private static final Logger log = LoggerFactory.getLogger(BinanceUserDataApi.class);
    
    private final RestApiClient restApiClient;
    
    public BinanceUserDataApi(RestApiClient restApiClient) {
        this.restApiClient = restApiClient;
    }
    
    @Override
    public ApiResponse<Map<String, Object>> createListenKey() throws SdkException {
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.POST)
            .path("/fapi/v1/listenKey")
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> createListenKeyAsync() {
        return restApiClient.executeAsync(ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.POST)
            .path("/fapi/v1/listenKey")
            .build());
    }
    
    @Override
    public ApiResponse<Map<String, Object>> keepAliveListenKey(String listenKey) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("listenKey", listenKey);
        
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.PUT)
            .path("/fapi/v1/listenKey")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> keepAliveListenKeyAsync(String listenKey) {
       return CompletableFuture.supplyAsync(() -> {
           try {
               return keepAliveListenKey(listenKey);
           } catch (SdkException e) {
               return ApiResponse.error("KEEP_ALIVE_LISTEN_KEY_ERROR", e.getMessage());
           }
       });
    }
    
    @Override
    public ApiResponse<Map<String, Object>> closeListenKey(String listenKey) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("listenKey", listenKey);

        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.DELETE)
            .path("/fapi/v1/listenKey")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> closeListenKeyAsync(String listenKey) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return closeListenKey(listenKey);
            } catch (SdkException e) {
                return ApiResponse.error("CLOSE_LISTEN_KEY_ERROR", e.getMessage());
            }
        });
    }
}
