package com.trading.sdk.api;

import com.trading.common.api.ApiResponse;
import com.trading.common.exception.SdkException;

import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 用户数据API接口
 * 提供用户数据流相关功能
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public interface UserDataApi {
    
    /**
     * 创建用户数据流监听密钥
     * 
     * @return 监听密钥响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<Map<String, Object>> createListenKey() throws SdkException;
    
    /**
     * 异步创建用户数据流监听密钥
     * 
     * @return 异步监听密钥响应
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> createListenKeyAsync();
    
    /**
     * 延长用户数据流监听密钥有效期
     * 
     * @param listenKey 监听密钥
     * @return 延长有效期响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<Map<String, Object>> keepAliveListenKey(String listenKey) throws SdkException;
    
    /**
     * 异步延长用户数据流监听密钥有效期
     * 
     * @param listenKey 监听密钥
     * @return 异步延长有效期响应
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> keepAliveListenKeyAsync(String listenKey);
    
    /**
     * 关闭用户数据流
     * 
     * @param listenKey 监听密钥
     * @return 关闭数据流响应
     * @throws SdkException 当请求失败时抛出
     */
    ApiResponse<Map<String, Object>> closeListenKey(String listenKey) throws SdkException;
    
    /**
     * 异步关闭用户数据流
     * 
     * @param listenKey 监听密钥
     * @return 异步关闭数据流响应
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> closeListenKeyAsync(String listenKey);
}
