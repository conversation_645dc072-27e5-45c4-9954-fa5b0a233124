package com.trading.sdk.api;

import com.trading.common.api.ApiRequest;
import com.trading.common.api.ApiResponse;
import com.trading.common.exception.SdkException;
import java.util.concurrent.CompletableFuture;

/**
 * 统一 REST API 客户端的核心接口。
 * <p>
 * 此接口定义了与交易所进行 RESTful 通信的通用契约。它充当了所有特定功能API（如市场、交易、账户）的门户，
 * 并提供了执行请求的同步和异步方法。实现此接口的客户端负责处理认证、请求签名、速率限制和错误处理等底层细节。
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public interface RestApiClient {
    
    /**
     * 以阻塞方式同步执行一个API请求。
     * <p>
     * 此方法会发送请求并等待，直到收到交易所的响应。适用于需要立即获取结果的场景。
     *
     * @param request 封装了端点、参数和期望响应类型的 {@link ApiRequest} 对象。
     * @param <T>     响应数据负载的期望类型。
     * @return 一个 {@link ApiResponse} 对象，其中包含了反序列化后的数据负载和响应元数据。
     * @throws SdkException 如果请求过程中发生网络错误、API错误或反序列化错误。
     */
    <T> ApiResponse<T> execute(ApiRequest<T> request) throws SdkException;
    
    /**
     * 以非阻塞方式异步执行一个API请求。
     * <p>
     * 此方法会立即返回一个 {@link CompletableFuture}，允许调用者在不阻塞当前线程的情况下处理未来的结果。
     * 适用于需要高并发和高性能的场景。
     *
     * @param request 封装了端点、参数和期望响应类型的 {@link ApiRequest} 对象。
     * @param <T>     响应数据负载的期望类型。
     * @return 一个 {@link CompletableFuture<ApiResponse<T>>}，在请求完成时它将包含最终的响应。
     *         如果请求失败，该 Future 将以 {@link SdkException} 异常结束。
     */
    <T> CompletableFuture<ApiResponse<T>> executeAsync(ApiRequest<T> request);
    
    /**
     * 获取用于访问市场数据的专用API客户端。
     *
     * @return {@link MarketDataApi} 接口的实例。
     */
    MarketDataApi getMarketDataApi();
    
    /**
     * 获取用于执行交易操作的专用API客户端。
     *
     * @return {@link TradingApi} 接口的实例。
     */
    TradingApi getTradingApi();
    
    /**
     * 获取用于访问账户信息的专用API客户端。
     *
     * @return {@link AccountApi} 接口的实例。
     */
    AccountApi getAccountApi();
    
    /**
     * 获取用于访问用户数据流（如Listen Key）的专用API客户端。
     *
     * @return {@link UserDataApi} 接口的实例。
     */
    UserDataApi getUserDataApi();
    
    /**
     * 对客户端的连接和服务状态进行一次快速健康检查。
     * <p>
     * 通常通过向一个轻量级的端点（如 `ping` 或 `getServerTime`）发送请求来实现。
     *
     * @return 如果客户端能够成功与交易所通信，则返回 {@code true}，否则返回 {@code false}。
     */
    boolean isHealthy();
    
    /**
     * 关闭客户端并释放所有相关资源。
     * <p>
     * 这包括关闭底层的HTTP连接池和清理任何活动的资源。
     * 在应用程序关闭时应调用此方法以防止资源泄露。
     */
    void close();
}
