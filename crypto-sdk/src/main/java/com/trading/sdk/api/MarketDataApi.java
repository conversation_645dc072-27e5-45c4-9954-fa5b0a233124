package com.trading.sdk.api;

import com.trading.common.api.ApiResponse;
import com.trading.common.exception.SdkException;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 提供对交易所市场数据端点访问的专用接口。
 * <p>
 * 此接口定义了获取各类市场数据的标准方法，例如：
 * <ul>
 *     <li>服务器状态和交易所信息。</li>
 *     <li>订单簿深度。</li>
 *     <li>近期和历史成交记录。</li>
 *     <li>K-line (蜡烛图) 数据。</li>
 *     <li>24小时价格统计 (Ticker)。</li>
 * </ul>
 * 所有方法都提供了同步和异步两种调用方式，以适应不同的应用场景。
 * 返回的数据通常是通用的 {@code Map<String, Object>} 或 {@code List<Map<String, Object>>}，
 * 以便与具体交易所的响应格式解耦。对于需要强类型安全性的场景，应使用 {@link StrongTypedMarketDataApi}。
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public interface MarketDataApi {
    
    /**
     * 同步获取交易所服务器时间。用于校准本地时间。
     *
     * @return 包含服务器时间戳的 {@link ApiResponse}。
     * @throws SdkException 如果请求失败。
     */
    ApiResponse<Map<String, Object>> getServerTime() throws SdkException;
    
    /**
     * 异步获取交易所服务器时间。
     *
     * @return 一个 {@link CompletableFuture}，完成后会包含服务器时间的响应。
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> getServerTimeAsync();
    
    /**
     * 同步获取交易所的交易规则和交易对信息。
     *
     * @return 包含详细交易所信息的 {@link ApiResponse}。
     * @throws SdkException 如果请求失败。
     */
    ApiResponse<Map<String, Object>> getExchangeInfo() throws SdkException;
    
    /**
     * 异步获取交易所的交易规则和交易对信息。
     *
     * @return 一个 {@link CompletableFuture}，完成后会包含交易所信息的响应。
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> getExchangeInfoAsync();
    
    /**
     * 同步获取指定交易对的订单簿深度。
     *
     * @param symbol 交易对符号 (例如 "BTCUSDT")。
     * @param limit  返回的深度档位数量。有效值依赖于具体交易所的规定。
     * @return 包含订单簿深度数据的 {@link ApiResponse}。
     * @throws SdkException 如果请求失败。
     */
    ApiResponse<Map<String, Object>> getOrderBook(String symbol, Integer limit) throws SdkException;
    
    /**
     * 异步获取指定交易对的订单簿深度。
     *
     * @param symbol 交易对符号 (例如 "BTCUSDT")。
     * @param limit  返回的深度档位数量。
     * @return 一个 {@link CompletableFuture}，完成后会包含订单簿深度的响应。
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> getOrderBookAsync(String symbol, Integer limit);
    
    /**
     * 同步获取指定交易对的最近成交记录。
     *
     * @param symbol 交易对符号 (例如 "BTCUSDT")。
     * @param limit  返回的记录数量（最大值依赖于交易所）。
     * @return 包含最近成交列表的 {@link ApiResponse}。
     * @throws SdkException 如果请求失败。
     */
    ApiResponse<List<Map<String, Object>>> getRecentTrades(String symbol, Integer limit) throws SdkException;
    
    /**
     * 异步获取指定交易对的最近成交记录。
     *
     * @param symbol 交易对符号 (例如 "BTCUSDT")。
     * @param limit  返回的记录数量。
     * @return 一个 {@link CompletableFuture}，完成后会包含最近成交列表的响应。
     */
    CompletableFuture<ApiResponse<List<Map<String, Object>>>> getRecentTradesAsync(String symbol, Integer limit);
    
    /**
     * 同步获取指定交易对的历史成交记录。
     *
     * @param symbol 交易对符号 (例如 "BTCUSDT")。
     * @param limit  返回的记录数量。
     * @param fromId 从哪个成交ID开始查找。
     * @return 包含历史成交列表的 {@link ApiResponse}。
     * @throws SdkException 如果请求失败。
     */
    ApiResponse<List<Map<String, Object>>> getHistoricalTrades(String symbol, Integer limit, Long fromId) throws SdkException;
    
    /**
     * 异步获取指定交易对的历史成交记录。
     *
     * @param symbol 交易对符号 (例如 "BTCUSDT")。
     * @param limit  返回的记录数量。
     * @param fromId 从哪个成交ID开始查找。
     * @return 一个 {@link CompletableFuture}，完成后会包含历史成交列表的响应。
     */
    CompletableFuture<ApiResponse<List<Map<String, Object>>>> getHistoricalTradesAsync(String symbol, Integer limit, Long fromId);
    
    /**
     * 同步获取指定交易对的聚合交易记录（将多笔小额交易聚合成一笔）。
     *
     * @param symbol    交易对符号 (例如 "BTCUSDT")。
     * @param fromId    从哪个成交ID开始查找。
     * @param startTime 查询的开始时间戳。
     * @param endTime   查询的结束时间戳。
     * @param limit     返回的记录数量。
     * @return 包含聚合成交列表的 {@link ApiResponse}。
     * @throws SdkException 如果请求失败。
     */
    ApiResponse<List<Map<String, Object>>> getAggTrades(String symbol, Long fromId, Long startTime, Long endTime, Integer limit) throws SdkException;
    
    /**
     * 异步获取指定交易对的聚合交易记录。
     *
     * @param symbol    交易对符号 (例如 "BTCUSDT")。
     * @param fromId    从哪个成交ID开始查找。
     * @param startTime 查询的开始时间戳。
     * @param endTime   查询的结束时间戳。
     * @param limit     返回的记录数量。
     * @return 一个 {@link CompletableFuture}，完成后会包含聚合成交列表的响应。
     */
    CompletableFuture<ApiResponse<List<Map<String, Object>>>> getAggTradesAsync(String symbol, Long fromId, Long startTime, Long endTime, Integer limit);
    
    /**
     * 同步获取指定交易对的K-line（蜡烛图）数据。
     *
     * @param symbol    交易对符号 (例如 "BTCUSDT")。
     * @param interval  K线的时间间隔 (例如 "1m", "1h", "1d")。
     * @param startTime 查询的开始时间戳。
     * @param endTime   查询的结束时间戳。
     * @param limit     返回的记录数量。
     * @return 包含K-line数据列表的 {@link ApiResponse}，每条K-line是一个Object列表。
     * @throws SdkException 如果请求失败。
     */
    ApiResponse<List<List<Object>>> getKlines(String symbol, String interval, Long startTime, Long endTime, Integer limit) throws SdkException;
    
    /**
     * 异步获取指定交易对的K-line（蜡烛图）数据。
     *
     * @param symbol    交易对符号 (例如 "BTCUSDT")。
     * @param interval  K线的时间间隔 (例如 "1m", "1h", "1d")。
     * @param startTime 查询的开始时间戳。
     * @param endTime   查询的结束时间戳。
     * @param limit     返回的记录数量。
     * @return 一个 {@link CompletableFuture}，完成后会包含K-line数据列表的响应。
     */
    CompletableFuture<ApiResponse<List<List<Object>>>> getKlinesAsync(String symbol, String interval, Long startTime, Long endTime, Integer limit);
    
    /**
     * 同步获取24小时价格变动统计。
     *
     * @param symbol 交易对符号 (例如 "BTCUSDT")。如果为null，则返回所有交易对的统计信息。
     * @return 包含价格统计的 {@link ApiResponse}。单个交易对时返回Map，所有交易对时返回List of Maps。
     * @throws SdkException 如果请求失败。
     */
    ApiResponse<Object> get24hrTicker(String symbol) throws SdkException;
    
    /**
     * 异步获取24小时价格变动统计。
     *
     * @param symbol 交易对符号 (例如 "BTCUSDT")。如果为null，则返回所有交易对的统计信息。
     * @return 一个 {@link CompletableFuture}，完成后会包含价格统计的响应。
     */
    CompletableFuture<ApiResponse<Object>> get24hrTickerAsync(String symbol);
    
    /**
     * 同步获取最新价格。
     *
     * @param symbol 交易对符号 (例如 "BTCUSDT")。如果为null，则返回所有交易对的最新价格。
     * @return 包含最新价格的 {@link ApiResponse}。
     * @throws SdkException 如果请求失败。
     */
    ApiResponse<Object> getTickerPrice(String symbol) throws SdkException;
    
    /**
     * 异步获取最新价格。
     *
     * @param symbol 交易对符号 (例如 "BTCUSDT")。如果为null，则返回所有交易对的最新价格。
     * @return 一个 {@link CompletableFuture}，完成后会包含最新价格的响应。
     */
    CompletableFuture<ApiResponse<Object>> getTickerPriceAsync(String symbol);
    
    /**
     * 同步获取当前最优的买卖挂单价格。
     *
     * @param symbol 交易对符号 (例如 "BTCUSDT")。如果为null，则返回所有交易对的信息。
     * @return 包含最优挂单价的 {@link ApiResponse}。
     * @throws SdkException 如果请求失败。
     */
    ApiResponse<Object> getBookTicker(String symbol) throws SdkException;
    
    /**
     * 异步获取当前最优的买卖挂单价格。
     *
     * @param symbol 交易对符号 (例如 "BTCUSDT")。如果为null，则返回所有交易对的信息。
     * @return 一个 {@link CompletableFuture}，完成后会包含最优挂单价的响应。
     */
    CompletableFuture<ApiResponse<Object>> getBookTickerAsync(String symbol);
}
