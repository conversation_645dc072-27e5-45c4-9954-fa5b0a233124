package com.trading.sdk.api.impl;

import com.trading.sdk.api.RestApiClient;
import com.trading.sdk.api.AccountApi;
import com.trading.common.api.ApiRequest;
import com.trading.common.api.ApiResponse;
import com.trading.common.enums.HttpMethod;
import com.trading.common.exception.SdkException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * Binance账户API实现
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public class BinanceAccountApi implements AccountApi {

    private static final Logger log = LoggerFactory.getLogger(BinanceAccountApi.class);
    
    private final RestApiClient restApiClient;
    
    public BinanceAccountApi(RestApiClient restApiClient) {
        this.restApiClient = restApiClient;
    }
    
    @Override
    public ApiResponse<Map<String, Object>> getAccountInfo() throws SdkException {
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.GET)
            .path("/fapi/v2/account")
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> getAccountInfoAsync() {
        return restApiClient.executeAsync(ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.GET)
            .path("/fapi/v2/account")
            .build());
    }
    
    @Override
    public ApiResponse<List<Map<String, Object>>> getFuturesAccountBalance() throws SdkException {
        ApiRequest<List<Map<String, Object>>> request = ApiRequest.<List<Map<String, Object>>>builder()
            .responseType((Class<List<Map<String, Object>>>)(Class<?>)List.class)
            .method(HttpMethod.GET)
            .path("/fapi/v2/balance")
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<Map<String, Object>>>> getFuturesAccountBalanceAsync() {
        return restApiClient.executeAsync(ApiRequest.<List<Map<String, Object>>>builder()
            .responseType((Class<List<Map<String, Object>>>)(Class<?>)List.class)
            .method(HttpMethod.GET)
            .path("/fapi/v2/balance")
            .build());
    }
    
    @Override
    public ApiResponse<List<Map<String, Object>>> getPositionInfo(String symbol) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        if (symbol != null) {
            parameters.put("symbol", symbol);
        }
        
        ApiRequest<List<Map<String, Object>>> request = ApiRequest.<List<Map<String, Object>>>builder()
            .responseType((Class<List<Map<String, Object>>>)(Class<?>)List.class)
            .method(HttpMethod.GET)
            .path("/fapi/v2/positionRisk")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<Map<String, Object>>>> getPositionInfoAsync(String symbol) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getPositionInfo(symbol);
            } catch (SdkException e) {
                return ApiResponse.error("POSITION_INFO_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<Map<String, Object>> getTradingStatus() throws SdkException {
         return getApiTradingStatus();
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> getTradingStatusAsync() {
        return getApiTradingStatusAsync();
    }
    
    @Override
    public ApiResponse<Map<String, Object>> getApiTradingStatus() throws SdkException {
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/apiTradingStatus")
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> getApiTradingStatusAsync() {
        return restApiClient.executeAsync(ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/apiTradingStatus")
            .build());
    }
    
    @Override
    public ApiResponse<Map<String, Object>> getCommissionRate(String symbol) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/commissionRate")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> getCommissionRateAsync(String symbol) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getCommissionRate(symbol);
            } catch (SdkException e) {
                return ApiResponse.error("COMMISSION_RATE_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<List<Map<String, Object>>> getIncomeHistory(String symbol, String incomeType, 
                                                                   Long startTime, Long endTime, Integer limit) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        if (symbol != null) {
            parameters.put("symbol", symbol);
        }
        if (incomeType != null) {
            parameters.put("incomeType", incomeType);
        }
        if (startTime != null) {
            parameters.put("startTime", startTime);
        }
        if (endTime != null) {
            parameters.put("endTime", endTime);
        }
        if (limit != null) {
            parameters.put("limit", limit);
        }
        
        ApiRequest<List<Map<String, Object>>> request = ApiRequest.<List<Map<String, Object>>>builder()
            .responseType((Class<List<Map<String, Object>>>)(Class<?>)List.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/income")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<Map<String, Object>>>> getIncomeHistoryAsync(String symbol, String incomeType, 
                                                                                           Long startTime, Long endTime, Integer limit) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getIncomeHistory(symbol, incomeType, startTime, endTime, limit);
            } catch (SdkException e) {
                return ApiResponse.error("INCOME_HISTORY_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<Map<String, Object>> changeInitialLeverage(String symbol, int leverage) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        parameters.put("leverage", leverage);
        
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.POST)
            .path("/fapi/v1/leverage")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> changeInitialLeverageAsync(String symbol, int leverage) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return changeInitialLeverage(symbol, leverage);
            } catch (SdkException e) {
                return ApiResponse.error("CHANGE_LEVERAGE_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<Map<String, Object>> changeMarginType(String symbol, String marginType) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        parameters.put("marginType", marginType);
        
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.POST)
            .path("/fapi/v1/marginType")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> changeMarginTypeAsync(String symbol, String marginType) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return changeMarginType(symbol, marginType);
            } catch (SdkException e) {
                return ApiResponse.error("CHANGE_MARGIN_TYPE_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<Map<String, Object>> modifyIsolatedPositionMargin(String symbol, String positionSide, 
                                                                         String amount, int type) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        parameters.put("positionSide", positionSide);
        parameters.put("amount", amount);
        parameters.put("type", type);
        
        ApiRequest<Map<String, Object>> request = ApiRequest.<Map<String, Object>>builder()
            .responseType((Class<Map<String, Object>>)(Class<?>)Map.class)
            .method(HttpMethod.POST)
            .path("/fapi/v1/positionMargin")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<Map<String, Object>>> modifyIsolatedPositionMarginAsync(String symbol, String positionSide, 
                                                                                                 String amount, int type) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return modifyIsolatedPositionMargin(symbol, positionSide, amount, type);
            } catch (SdkException e) {
                return ApiResponse.error("MODIFY_POSITION_MARGIN_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<List<Map<String, Object>>> getPositionMarginHistory(String symbol, Integer type, 
                                                                           Long startTime, Long endTime, Integer limit) throws SdkException {
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>();
        parameters.put("symbol", symbol);
        if (type != null) {
            parameters.put("type", type);
        }
        if (startTime != null) {
            parameters.put("startTime", startTime);
        }
        if (endTime != null) {
            parameters.put("endTime", endTime);
        }
        if (limit != null) {
            parameters.put("limit", limit);
        }
        
        ApiRequest<List<Map<String, Object>>> request = ApiRequest.<List<Map<String, Object>>>builder()
            .responseType((Class<List<Map<String, Object>>>)(Class<?>)List.class)
            .method(HttpMethod.GET)
            .path("/fapi/v1/positionMargin/history")
            .parameters(parameters)
            .build();
        return restApiClient.execute(request);
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<Map<String, Object>>>> getPositionMarginHistoryAsync(String symbol, Integer type, 
                                                                                                   Long startTime, Long endTime, Integer limit) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getPositionMarginHistory(symbol, type, startTime, endTime, limit);
            } catch (SdkException e) {
                return ApiResponse.error("POSITION_MARGIN_HISTORY_ERROR", e.getMessage());
            }
        });
    }
}
