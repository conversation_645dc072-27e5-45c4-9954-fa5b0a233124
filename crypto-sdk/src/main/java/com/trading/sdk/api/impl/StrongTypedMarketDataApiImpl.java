package com.trading.sdk.api.impl;

import com.trading.common.dto.KlineData;
import com.trading.common.dto.DepthData;
import com.trading.common.dto.TickerData;
import com.trading.common.dto.TradeData;
import com.trading.common.dto.PriceLevel;
import com.trading.sdk.api.StrongTypedMarketDataApi;
import com.trading.sdk.api.MarketDataApi;
import com.trading.common.api.ApiResponse;
import com.trading.common.exception.SdkException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 强类型市场数据API实现
 * 基于原始API，提供强类型的数据转换
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
public class StrongTypedMarketDataApiImpl implements StrongTypedMarketDataApi {
    
    private static final Logger log = LoggerFactory.getLogger(StrongTypedMarketDataApiImpl.class);
    
    @Autowired
    private MarketDataApi marketDataApi;
    
    @Override
    public ApiResponse<List<KlineData>> getKlines(String symbol, String interval, Long startTime, Long endTime, Integer limit) throws SdkException {
        try {
            // 调用原始API获取数据
            ApiResponse<List<List<Object>>> rawResponse = marketDataApi.getKlines(symbol, interval, startTime, endTime, limit);
            
            if (!rawResponse.isSuccess()) {
                return ApiResponse.error(rawResponse.getErrorCode(), rawResponse.getErrorMessage());
            }
            
            // 转换为强类型
            List<KlineData> klineDataList = rawResponse.getData().stream()
                    .map(this::convertToKlineData)
                    .collect(Collectors.toList());
            
            return ApiResponse.success(klineDataList, rawResponse.getRawResponse());
            
        } catch (Exception e) {
            log.error("获取K线数据失败: symbol={}, interval={}", symbol, interval, e);
            throw new SdkException("获取K线数据失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<KlineData>>> getKlinesAsync(String symbol, String interval, Long startTime, Long endTime, Integer limit) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getKlines(symbol, interval, startTime, endTime, limit);
            } catch (SdkException e) {
                return ApiResponse.error("KLINES_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<DepthData> getOrderBook(String symbol, Integer limit) throws SdkException {
        try {
            // 调用原始API获取数据
            ApiResponse<Map<String, Object>> rawResponse = marketDataApi.getOrderBook(symbol, limit);
            
            if (!rawResponse.isSuccess()) {
                return ApiResponse.error(rawResponse.getErrorCode(), rawResponse.getErrorMessage());
            }
            
            // 转换为强类型
            DepthData depthData = convertToDepthData(rawResponse.getData(), symbol);
            
            return ApiResponse.success(depthData, rawResponse.getRawResponse());
            
        } catch (Exception e) {
            log.error("获取订单簿深度失败: symbol={}, limit={}", symbol, limit, e);
            throw new SdkException("获取订单簿深度失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public CompletableFuture<ApiResponse<DepthData>> getOrderBookAsync(String symbol, Integer limit) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getOrderBook(symbol, limit);
            } catch (SdkException e) {
                return ApiResponse.error("ORDER_BOOK_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<TickerData> get24hrTicker(String symbol) throws SdkException {
        try {
            // 调用原始API获取数据
            ApiResponse<Object> rawResponse = marketDataApi.get24hrTicker(symbol);
            
            if (!rawResponse.isSuccess()) {
                return ApiResponse.error(rawResponse.getErrorCode(), rawResponse.getErrorMessage());
            }
            
            // 转换为强类型
            TickerData tickerData = convertToTickerData(rawResponse.getData(), symbol);
            
            return ApiResponse.success(tickerData, rawResponse.getRawResponse());
            
        } catch (Exception e) {
            log.error("获取24小时价格变动统计失败: symbol={}", symbol, e);
            throw new SdkException("获取24小时价格变动统计失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public CompletableFuture<ApiResponse<TickerData>> get24hrTickerAsync(String symbol) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return get24hrTicker(symbol);
            } catch (SdkException e) {
                return ApiResponse.error("TICKER_ERROR", e.getMessage());
            }
        });
    }
    
    @Override
    public ApiResponse<List<TradeData>> getRecentTrades(String symbol, Integer limit) throws SdkException {
        try {
            // 调用原始API获取数据
            ApiResponse<List<Map<String, Object>>> rawResponse = marketDataApi.getRecentTrades(symbol, limit);
            
            if (!rawResponse.isSuccess()) {
                return ApiResponse.error(rawResponse.getErrorCode(), rawResponse.getErrorMessage());
            }
            
            // 转换为强类型
            List<TradeData> tradeDataList = rawResponse.getData().stream()
                    .map(this::convertToTradeData)
                    .collect(Collectors.toList());
            
            return ApiResponse.success(tradeDataList, rawResponse.getRawResponse());
            
        } catch (Exception e) {
            log.error("获取最近成交记录失败: symbol={}, limit={}", symbol, limit, e);
            throw new SdkException("获取最近成交记录失败: " + e.getMessage(), e);
        }
    }
    
    @Override
    public CompletableFuture<ApiResponse<List<TradeData>>> getRecentTradesAsync(String symbol, Integer limit) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return getRecentTrades(symbol, limit);
            } catch (SdkException e) {
                return ApiResponse.error("TRADES_ERROR", e.getMessage());
            }
        });
    }
    
    /**
     * 转换原始K线数据为KlineData对象
     */
    private KlineData convertToKlineData(List<Object> rawKline) {
        KlineData klineData = new KlineData();
        
        // Binance K线数据格式：
        // [开盘时间, 开盘价, 最高价, 最低价, 收盘价, 成交量, 收盘时间, 成交额, 成交笔数, 主动买入成交量, 主动买入成交额, 忽略]
        klineData.setOpenTime(convertTimestamp((Long) rawKline.get(0)));
        klineData.setOpenPrice(new BigDecimal(rawKline.get(1).toString()));
        klineData.setHighPrice(new BigDecimal(rawKline.get(2).toString()));
        klineData.setLowPrice(new BigDecimal(rawKline.get(3).toString()));
        klineData.setClosePrice(new BigDecimal(rawKline.get(4).toString()));
        klineData.setVolume(new BigDecimal(rawKline.get(5).toString()));
        klineData.setCloseTime(convertTimestamp((Long) rawKline.get(6)));
        klineData.setQuoteVolume(new BigDecimal(rawKline.get(7).toString()));
        klineData.setTradeCount((Long) rawKline.get(8));
        klineData.setTakerBuyVolume(new BigDecimal(rawKline.get(9).toString()));
        klineData.setTakerBuyQuoteVolume(new BigDecimal(rawKline.get(10).toString()));
        klineData.setCreatedAt(LocalDateTime.now());
        
        return klineData;
    }
    
    /**
     * 转换原始深度数据为DepthData对象
     */
    private DepthData convertToDepthData(Map<String, Object> rawDepth, String symbol) {
        DepthData depthData = new DepthData();
        depthData.setSymbol(symbol);
        depthData.setLastUpdateId((Long) rawDepth.get("lastUpdateId"));
        depthData.setTimestamp(LocalDateTime.now());
        depthData.setCreatedAt(LocalDateTime.now());

        // 转换买单和卖单
        List<List<String>> rawBids = (List<List<String>>) rawDepth.get("bids");
        List<List<String>> rawAsks = (List<List<String>>) rawDepth.get("asks");

        List<DepthData.PriceLevel> bids = rawBids.stream()
                .map(bid -> new DepthData.PriceLevel(new BigDecimal(bid.get(0)), new BigDecimal(bid.get(1))))
                .collect(Collectors.toList());

        List<DepthData.PriceLevel> asks = rawAsks.stream()
                .map(ask -> new DepthData.PriceLevel(new BigDecimal(ask.get(0)), new BigDecimal(ask.get(1))))
                .collect(Collectors.toList());

        depthData.setBids(bids);
        depthData.setAsks(asks);

        // 设置数据源和深度级别
        depthData.setSource("binance");
        depthData.setLevels(Math.max(bids.size(), asks.size()));

        return depthData;
    }
    
    /**
     * 转换原始Ticker数据为TickerData对象
     */
    private TickerData convertToTickerData(Object rawTicker, String symbol) {
        Map<String, Object> tickerMap = (Map<String, Object>) rawTicker;
        
        TickerData tickerData = new TickerData();
        tickerData.setSymbol(symbol);
        tickerData.setPriceChange(new BigDecimal(tickerMap.get("priceChange").toString()));
        tickerData.setPriceChangePercent(new BigDecimal(tickerMap.get("priceChangePercent").toString()));
        tickerData.setWeightedAvgPrice(new BigDecimal(tickerMap.get("weightedAvgPrice").toString()));
        tickerData.setLastPrice(new BigDecimal(tickerMap.get("lastPrice").toString()));
        tickerData.setLastQty(new BigDecimal(tickerMap.get("lastQty").toString()));
        tickerData.setOpenPrice(new BigDecimal(tickerMap.get("openPrice").toString()));
        tickerData.setHighPrice(new BigDecimal(tickerMap.get("highPrice").toString()));
        tickerData.setLowPrice(new BigDecimal(tickerMap.get("lowPrice").toString()));
        tickerData.setVolume(new BigDecimal(tickerMap.get("volume").toString()));
        tickerData.setQuoteVolume(new BigDecimal(tickerMap.get("quoteVolume").toString()));
        tickerData.setOpenTime(convertTimestamp((Long) tickerMap.get("openTime")));
        tickerData.setCloseTime(convertTimestamp((Long) tickerMap.get("closeTime")));
        tickerData.setCount((Long) tickerMap.get("count"));
        tickerData.setCreatedAt(LocalDateTime.now());
        
        return tickerData;
    }
    
    /**
     * 转换原始交易数据为TradeData对象
     */
    private TradeData convertToTradeData(Map<String, Object> rawTrade) {
        TradeData tradeData = new TradeData();
        tradeData.setTradeId((Long) rawTrade.get("id"));
        tradeData.setPrice(new BigDecimal(rawTrade.get("price").toString()));
        tradeData.setQuantity(new BigDecimal(rawTrade.get("qty").toString()));
        tradeData.setQuoteQuantity(new BigDecimal(rawTrade.get("quoteQty").toString()));
        tradeData.setTradeTime(convertTimestamp((Long) rawTrade.get("time")));
        tradeData.setIsBuyerMaker((Boolean) rawTrade.get("isBuyerMaker"));
        tradeData.setCreatedAt(LocalDateTime.now());
        
        return tradeData;
    }
    
    /**
     * 转换时间戳为LocalDateTime
     */
    private LocalDateTime convertTimestamp(Long timestamp) {
        return LocalDateTime.ofInstant(Instant.ofEpochMilli(timestamp), ZoneOffset.UTC);
    }
}
