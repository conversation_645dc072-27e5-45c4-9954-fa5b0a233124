package com.trading.sdk.api;

import com.trading.common.api.ApiResponse;
import com.trading.common.exception.SdkException;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * 提供对交易所交易功能端点访问的专用接口。
 * <p>
 * 此接口封装了所有与订单生命周期相关的操作，包括：
 * <ul>
 *     <li>创建新订单（包括测试订单）。</li>
 *     <li>取消单个或多个订单。</li>
 *     <li>查询订单状态。</li>
 *     <li>获取历史成交记录。</li>
 * </ul>
 * 所有方法均提供同步和异步两种调用方式。
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public interface TradingApi {
    
    /**
     * 同步发送一个测试订单。该订单仅用于验证请求的正确性，不会在撮合引擎中匹配。
     *
     * @param symbol      交易对符号 (例如 "BTCUSDT")。
     * @param side        买卖方向 ("BUY" 或 "SELL")。
     * @param type        订单类型 (例如 "LIMIT", "MARKET")。
     * @param quantity    订单数量。
     * @param price       价格（对于限价单 (LIMIT) 是必需的）。
     * @param timeInForce 订单的有效时间策略 (例如 "GTC", "IOC", "FOK")。
     * @return 一个空的 {@link ApiResponse}，如果请求验证成功则不包含数据。
     * @throws SdkException 如果请求验证失败。
     */
    ApiResponse<Map<String, Object>> testNewOrder(String symbol, String side, String type, 
                                                  BigDecimal quantity, BigDecimal price, String timeInForce) throws SdkException;
    
    /**
     * 异步发送一个测试订单。
     *
     * @param symbol      交易对符号。
     * @param side        买卖方向。
     * @param type        订单类型。
     * @param quantity    数量。
     * @param price       价格。
     * @param timeInForce 有效时间策略。
     * @return 一个 {@link CompletableFuture}，完成后会包含测试下单的响应。
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> testNewOrderAsync(String symbol, String side, String type, 
                                                                          BigDecimal quantity, BigDecimal price, String timeInForce);
    
    /**
     * 同步发送一个新订单到交易所。
     *
     * @param symbol      交易对符号。
     * @param side        买卖方向。
     * @param type        订单类型。
     * @param quantity    数量。
     * @param price       价格（对于限价单是必需的）。
     * @param timeInForce 有效时间策略。
     * @return 包含新订单确认信息的 {@link ApiResponse}。
     * @throws SdkException 如果下单失败。
     */
    ApiResponse<Map<String, Object>> newOrder(String symbol, String side, String type, 
                                              BigDecimal quantity, BigDecimal price, String timeInForce) throws SdkException;
    
    /**
     * 异步发送一个新订单。
     *
     * @param symbol      交易对符号。
     * @param side        买卖方向。
     * @param type        订单类型。
     * @param quantity    数量。
     * @param price       价格。
     * @param timeInForce 有效时间策略。
     * @return 一个 {@link CompletableFuture}，完成后会包含新订单的确认信息。
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> newOrderAsync(String symbol, String side, String type, 
                                                                      BigDecimal quantity, BigDecimal price, String timeInForce);
    
    /**
     * 同步发送一个新订单，使用 Map 传递所有参数。
     * <p>
     * 这种方式更灵活，允许传递非标准或交易所特定的参数。
     *
     * @param orderParams 包含所有订单参数的 {@link Map}。必须包含 "symbol", "side", "type", "quantity" 等关键参数。
     * @return 包含新订单确认信息的 {@link ApiResponse}。
     * @throws SdkException 如果下单失败。
     */
    ApiResponse<Map<String, Object>> newOrder(Map<String, Object> orderParams) throws SdkException;
    
    /**
     * 异步发送一个新订单，使用 Map 传递所有参数。
     *
     * @param orderParams 包含所有订单参数的 {@link Map}。
     * @return 一个 {@link CompletableFuture}，完成后会包含新订单的确认信息。
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> newOrderAsync(Map<String, Object> orderParams);
    
    /**
     * 同步取消一个活跃的订单。
     *
     * @param symbol            交易对符号。
     * @param orderId           交易所分配的订单ID。
     * @param origClientOrderId 用户自定义的订单ID。
     *                          (orderId 和 origClientOrderId 至少需要一个)。
     * @return 包含已取消订单信息的 {@link ApiResponse}。
     * @throws SdkException 如果撤单失败。
     */
    ApiResponse<Map<String, Object>> cancelOrder(String symbol, Long orderId, String origClientOrderId) throws SdkException;
    
    /**
     * 异步取消一个活跃的订单。
     *
     * @param symbol            交易对符号。
     * @param orderId           交易所分配的订单ID。
     * @param origClientOrderId 用户自定义的订单ID。
     * @return 一个 {@link CompletableFuture}，完成后会包含已取消订单的信息。
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> cancelOrderAsync(String symbol, Long orderId, String origClientOrderId);
    
    /**
     * 同步取消指定交易对上的所有活跃订单。
     *
     * @param symbol 要取消所有订单的交易对符号。
     * @return 包含所有已取消订单信息的 {@link ApiResponse}。
     * @throws SdkException 如果撤单失败。
     */
    ApiResponse<List<Map<String, Object>>> cancelAllOrders(String symbol) throws SdkException;
    
    /**
     * 异步取消指定交易对上的所有活跃订单。
     *
     * @param symbol 要取消所有订单的交易对符号。
     * @return 一个 {@link CompletableFuture}，完成后会包含所有已取消订单的信息。
     */
    CompletableFuture<ApiResponse<List<Map<String, Object>>>> cancelAllOrdersAsync(String symbol);
    
    /**
     * 同步查询一个订单的状态。
     *
     * @param symbol            交易对符号。
     * @param orderId           交易所分配的订单ID。
     * @param origClientOrderId 用户自定义的订单ID。
     *                          (orderId 和 origClientOrderId 至少需要一个)。
     * @return 包含订单详细状态的 {@link ApiResponse}。
     * @throws SdkException 如果查询失败。
     */
    ApiResponse<Map<String, Object>> queryOrder(String symbol, Long orderId, String origClientOrderId) throws SdkException;
    
    /**
     * 异步查询一个订单的状态。
     *
     * @param symbol            交易对符号。
     * @param orderId           交易所分配的订单ID。
     * @param origClientOrderId 用户自定义的订单ID。
     * @return 一个 {@link CompletableFuture}，完成后会包含订单的详细状态。
     */
    CompletableFuture<ApiResponse<Map<String, Object>>> queryOrderAsync(String symbol, Long orderId, String origClientOrderId);
    
    /**
     * 同步查询当前所有的挂单 (Open Orders)。
     *
     * @param symbol 如果提供，则只查询该交易对的挂单。如果为null，则查询所有交易对的挂单。
     * @return 包含所有当前挂单列表的 {@link ApiResponse}。
     * @throws SdkException 如果查询失败。
     */
    ApiResponse<List<Map<String, Object>>> getCurrentOrders(String symbol) throws SdkException;
    
    /**
     * 异步查询当前所有的挂单。
     *
     * @param symbol 如果提供，则只查询该交易对的挂单。如果为null，则查询所有交易对的挂单。
     * @return 一个 {@link CompletableFuture}，完成后会包含当前挂单的列表。
     */
    CompletableFuture<ApiResponse<List<Map<String, Object>>>> getCurrentOrdersAsync(String symbol);
    
    /**
     * 同步查询所有订单（包括历史订单）。
     *
     * @param symbol    交易对符号。
     * @param orderId   如果设置，则从此订单ID开始查询。
     * @param startTime 查询的开始时间戳。
     * @param endTime   查询的结束时间戳。
     * @param limit     返回的记录数量。
     * @return 包含订单历史列表的 {@link ApiResponse}。
     * @throws SdkException 如果查询失败。
     */
    ApiResponse<List<Map<String, Object>>> getAllOrders(String symbol, Long orderId, Long startTime, Long endTime, Integer limit) throws SdkException;
    
    /**
     * 异步查询所有订单（包括历史订单）。
     *
     * @param symbol    交易对符号。
     * @param orderId   如果设置，则从此订单ID开始查询。
     * @param startTime 查询的开始时间戳。
     * @param endTime   查询的结束时间戳。
     * @param limit     返回的记录数量。
     * @return 一个 {@link CompletableFuture}，完成后会包含订单历史的列表。
     */
    CompletableFuture<ApiResponse<List<Map<String, Object>>>> getAllOrdersAsync(String symbol, Long orderId, Long startTime, Long endTime, Integer limit);
    
    /**
     * 同步查询账户的成交历史。
     *
     * @param symbol    交易对符号。
     * @param orderId   只查询与此订单ID相关的成交。
     * @param startTime 查询的开始时间戳。
     * @param endTime   查询的结束时间戳。
     * @param fromId    从此成交ID开始查询。
     * @param limit     返回的记录数量。
     * @return 包含成交历史列表的 {@link ApiResponse}。
     * @throws SdkException 如果查询失败。
     */
    ApiResponse<List<Map<String, Object>>> getMyTrades(String symbol, Long orderId, Long startTime, Long endTime, Long fromId, Integer limit) throws SdkException;
    
    /**
     * 异步查询账户的成交历史。
     *
     * @param symbol    交易对符号。
     * @param orderId   只查询与此订单ID相关的成交。
     * @param startTime 查询的开始时间戳。
     * @param endTime   查询的结束时间戳。
     * @param fromId    从此成交ID开始查询。
     * @param limit     返回的记录数量。
     * @return 一个 {@link CompletableFuture}，完成后会包含成交历史的列表。
     */
    CompletableFuture<ApiResponse<List<Map<String, Object>>>> getMyTradesAsync(String symbol, Long orderId, Long startTime, Long endTime, Long fromId, Integer limit);
}
