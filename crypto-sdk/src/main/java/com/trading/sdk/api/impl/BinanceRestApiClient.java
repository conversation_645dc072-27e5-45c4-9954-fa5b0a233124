package com.trading.sdk.api.impl;

import com.binance.connector.futures.client.impl.CMFuturesClientImpl;
import com.binance.connector.futures.client.impl.UMFuturesClientImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trading.common.utils.JsonUtils;
import com.trading.sdk.api.*;
import com.trading.common.api.ApiRequest;
import com.trading.common.api.ApiResponse;
import com.trading.sdk.client.BinanceClientPool;
import com.trading.sdk.config.SdkConfiguration;
import com.trading.common.exception.SdkException;
import com.trading.common.ratelimit.RateLimiter;
import com.trading.common.retry.UnifiedRetryService;
import com.trading.common.metrics.ApiMetrics;
import com.trading.common.metrics.RequestContext;
import com.trading.common.circuit.CircuitBreaker;
import com.trading.common.circuit.CircuitBreakerManager;
import com.trading.common.enums.ApiRequestWeight;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.LinkedHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;


@Component
public class BinanceRestApiClient implements RestApiClient, InitializingBean {

    private static final Logger log = LoggerFactory.getLogger(BinanceRestApiClient.class);
    
    private final BinanceClientPool clientPool;
    private final SdkConfiguration sdkConfiguration;
    private final ObjectMapper objectMapper;
    private final ExecutorService executorService;
    private final RateLimiter rateLimiter;
    private final UnifiedRetryService retryService;
    private final ApiMetrics apiMetrics;
    private final CircuitBreakerManager circuitBreakerManager;

    private MarketDataApi marketDataApi;
    private TradingApi tradingApi;
    private AccountApi accountApi;
    private UserDataApi userDataApi;
    
    public BinanceRestApiClient(BinanceClientPool clientPool,
                               SdkConfiguration sdkConfiguration,
                               RateLimiter rateLimiter,
                               UnifiedRetryService retryService,
                               ApiMetrics apiMetrics,
                               CircuitBreakerManager circuitBreakerManager) {
        this.clientPool = clientPool;
        this.sdkConfiguration = sdkConfiguration;
        this.objectMapper = JsonUtils.getObjectMapper();
        this.executorService = Executors.newVirtualThreadPerTaskExecutor();
        this.rateLimiter = rateLimiter;
        this.retryService = retryService;
        this.apiMetrics = apiMetrics;
        this.circuitBreakerManager = circuitBreakerManager;
    }
    
    @Override
    public void afterPropertiesSet() throws Exception {
        log.info("初始化BinanceRestApiClient");
        
        this.marketDataApi = new BinanceMarketDataApi(this);
        this.tradingApi = new BinanceTradingApi(this);
        this.accountApi = new BinanceAccountApi(this);
        this.userDataApi = new BinanceUserDataApi(this);
        
        log.info("BinanceRestApiClient初始化完成");
    }
    
    @Override
    public <T> ApiResponse<T> execute(ApiRequest<T> request) throws SdkException {
        ApiRequestWeight weight = determineRequestWeight(request);
        RequestContext context = apiMetrics.startRequest(request.getPath(), weight);
        CircuitBreaker circuitBreaker = circuitBreakerManager.getApiCircuitBreaker(request.getPath());

        try {
            return circuitBreaker.execute(() ->
                retryService.executeApiOperation(request.getPath(), () -> {
                    try {
                        return executeWithRateLimit(request, weight, context);
                    } catch (Exception e) {
                        // 将受检异常包装为非受检异常，以便上层统一处理
                        throw new SdkException("Exception occurred inside retry operation: " + e.getMessage(), e);
                    }
                })
            );

        } catch (Exception e) {
            apiMetrics.recordFailure(context, e.getClass().getSimpleName());
            log.error("API请求执行失败: {} {}", request.getMethod(), request.getPath(), e);

            if (e instanceof SdkException) {
                throw (SdkException) e;
            } else {
                throw new SdkException("API请求执行失败: " + e.getMessage(), e);
            }
        }
    }
    
    @Override
    public <T> CompletableFuture<ApiResponse<T>> executeAsync(ApiRequest<T> request) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                return execute(request);
            } catch (SdkException e) {
                log.error("异步API请求执行失败: {} {}", request.getMethod(), request.getPath(), e);
                return ApiResponse.error(e.getMessage());
            }
        }, executorService);
    }

    private <T> ApiResponse<T> executeWithRateLimit(ApiRequest<T> request,
                                                   ApiRequestWeight weight,
                                                   RequestContext context) throws Exception {
        int weightValue = getWeightValue(weight);
        String limitType = getLimitType(request);

        if (!tryAcquireByType(limitType, weightValue, sdkConfiguration.getBinance().getTimeout())) {
            throw new SdkException("获取API限流许可超时");
        }

        Object client = null;
        try {
            log.debug("执行API请求: {} {}, weight={}, limitType={}",
                request.getMethod(), request.getPath(), weightValue, limitType);

            client = getClient(request);
            String rawResponse = executeRequest(client, request);
            T data = parseResponse(rawResponse, request.getResponseType());
            apiMetrics.recordSuccess(context);

            return ApiResponse.success(data, rawResponse);

        } finally {
            rateLimiter.release(limitType, weightValue);
            returnClient(client);
        }
    }
    
    @Override
    public MarketDataApi getMarketDataApi() {
        return marketDataApi;
    }
    
    @Override
    public TradingApi getTradingApi() {
        return tradingApi;
    }
    
    @Override
    public AccountApi getAccountApi() {
        return accountApi;
    }
    
    @Override
    public UserDataApi getUserDataApi() {
        return userDataApi;
    }
    
    @Override
    public boolean isHealthy() {
        try {
            if (!apiMetrics.isHealthy()) {
                log.warn("API性能指标不健康");
                return false;
            }

            if (rateLimiter.isNearLimit("usdm")) {
                log.warn("API限流接近阈值");
                return false;
            }

            getMarketDataApi().getServerTime();
            return true;
        } catch (Exception e) {
            log.warn("健康检查失败", e);
            return false;
        }
    }
    
    @Override
    public void close() {
        log.info("关闭BinanceRestApiClient");
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
    
    private ApiRequestWeight determineRequestWeight(ApiRequest<?> request) {
        String path = request.getPath();

        if (path.contains("/time") || path.contains("/ping")) {
            return ApiRequestWeight.WEIGHT_1;
        } else if (path.contains("/exchangeInfo") || path.contains("/depth")) {
            return ApiRequestWeight.WEIGHT_5;
        } else if (path.contains("/klines") || path.contains("/ticker")) {
            return ApiRequestWeight.WEIGHT_1;
        } else if (path.contains("/account") || path.contains("/balance")) {
            return ApiRequestWeight.WEIGHT_5;
        } else if (path.contains("/order")) {
            return ApiRequestWeight.WEIGHT_1;
        } else if (path.contains("/allOrders") || path.contains("/openOrders")) {
            return ApiRequestWeight.WEIGHT_5;
        } else {
            return ApiRequestWeight.WEIGHT_1;
        }
    }

    private Object getClient(ApiRequest<?> request) {
        String path = request.getPath();
        if (path.contains("/fapi/")) {
            return clientPool.borrowUMClient();
        } else if (path.contains("/dapi/")) {
            return clientPool.borrowCMClient();
        } else {
            return clientPool.borrowUMClient();
        }
    }
    
    private void returnClient(Object client) {
        if (client != null) {
            clientPool.returnClient(client);
        }
    }
    
    private String executeRequest(Object client, ApiRequest<?> request) throws Exception {
        try {
            log.debug("开始执行API请求: {} {}", request.getMethod(), request.getPath());

            if (client instanceof UMFuturesClientImpl) {
                return executeUMFuturesRequest((UMFuturesClientImpl) client, request);
            } else if (client instanceof CMFuturesClientImpl) {
                return executeCMFuturesRequest((CMFuturesClientImpl) client, request);
            } else {
                throw new SdkException("不支持的客户端类型: " + client.getClass().getName());
            }

        } catch (com.binance.connector.futures.client.exceptions.BinanceClientException e) {
            throw handleBinanceClientException(e, request);
        } catch (com.binance.connector.futures.client.exceptions.BinanceServerException e) {
            throw handleBinanceServerException(e, request);
        } catch (Exception e) {
            log.error("API请求执行失败: {} {}", request.getMethod(), request.getPath(), e);
            throw new SdkException("API请求执行失败: " + e.getMessage(), e);
        }
    }

    private String executeUMFuturesRequest(UMFuturesClientImpl client, ApiRequest<?> request) throws Exception {
        String path = request.getPath();
        String method = request.getMethod().toString();
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>(request.getParameters());

        if ("GET".equals(method)) {
            if (path.contains("/fapi/v1/klines")) {
                return client.market().klines(parameters);
            } else if (path.contains("/fapi/v1/depth")) {
                return client.market().depth(parameters);
            } else if (path.contains("/fapi/v1/ticker/24hr")) {
                return client.market().ticker24H(parameters);
            } else if (path.contains("/fapi/v1/ticker/price")) {
                return client.market().tickerSymbol(parameters);
            } else if (path.contains("/fapi/v1/account")) {
                return client.account().accountInformation(parameters);
            } else if (path.contains("/fapi/v1/balance")) {
                return client.account().futuresAccountBalance(parameters);
            } else if (path.contains("/fapi/v1/positionRisk")) {
                return client.account().positionInformation(parameters);
            } else if (path.contains("/fapi/v1/ping")) {
                return client.market().ping();
            } else if (path.contains("/fapi/v1/time")) {
                return client.market().time();
            }
        } else if ("POST".equals(method)) {
            if (path.contains("/fapi/v1/order")) {
                return client.account().newOrder(parameters);
            } else if (path.contains("/fapi/v1/listenKey")) {
                return client.userData().createListenKey();
            }
        } else if ("PUT".equals(method)) {
            if (path.contains("/fapi/v1/listenKey")) {
                return client.userData().extendListenKey();
            }
        } else if ("DELETE".equals(method)) {
            if (path.contains("/fapi/v1/order")) {
                return client.account().cancelOrder(parameters);
            } else if (path.contains("/fapi/v1/listenKey")) {
                return client.userData().closeListenKey();
            }
        }

        throw new SdkException("不支持的API端点: " + method + " " + path);
    }

    private String executeCMFuturesRequest(CMFuturesClientImpl client, ApiRequest<?> request) throws Exception {
        String path = request.getPath();
        String method = request.getMethod().toString();
        LinkedHashMap<String, Object> parameters = new LinkedHashMap<>(request.getParameters());

        if ("GET".equals(method)) {
            if (path.contains("/dapi/v1/klines")) {
                return client.market().klines(parameters);
            } else if (path.contains("/dapi/v1/depth")) {
                return client.market().depth(parameters);
            } else if (path.contains("/dapi/v1/ticker/24hr")) {
                return client.market().ticker24H(parameters);
            } else if (path.contains("/dapi/v1/account")) {
                return client.account().accountInformation(parameters);
            } else if (path.contains("/dapi/v1/ping")) {
                return client.market().ping();
            } else if (path.contains("/dapi/v1/time")) {
                return client.market().time();
            }
        } else if ("POST".equals(method)) {
            if (path.contains("/dapi/v1/order")) {
                return client.account().newOrder(parameters);
            }
        }

        throw new SdkException("不支持的CM期货API端点: " + method + " " + path);
    }

    private SdkException handleBinanceClientException(com.binance.connector.futures.client.exceptions.BinanceClientException e, ApiRequest<?> request) {
        log.error("币安客户端异常: {} {}, httpStatusCode={}, errorCode={}, errMsg={}",
                request.getMethod(), request.getPath(), e.getHttpStatusCode(), e.getErrorCode(), e.getErrMsg());

        int httpStatusCode = e.getHttpStatusCode();
        int errorCode = e.getErrorCode();
        String errorMessage = e.getErrMsg();

        String detailedMessage = String.format("币安API客户端错误 [%s %s]: HTTP %d, 错误码: %d, 错误信息: %s",
                request.getMethod(), request.getPath(), httpStatusCode, errorCode, errorMessage);

        if (httpStatusCode == 400) {
            return new SdkException("INVALID_REQUEST", detailedMessage, e);
        } else if (httpStatusCode == 401) {
            return new SdkException("AUTHENTICATION_FAILED", detailedMessage, e);
        } else if (httpStatusCode == 403) {
            return new SdkException("PERMISSION_DENIED", detailedMessage, e);
        } else if (httpStatusCode == 429) {
            return new SdkException("RATE_LIMIT_EXCEEDED", detailedMessage, e);
        } else if (httpStatusCode >= 400 && httpStatusCode < 500) {
            return new SdkException("CLIENT_ERROR", detailedMessage, e);
        } else {
            return new SdkException("UNKNOWN_CLIENT_ERROR", detailedMessage, e);
        }
    }

    private SdkException handleBinanceServerException(com.binance.connector.futures.client.exceptions.BinanceServerException e, ApiRequest<?> request) {
        log.error("币安服务器异常: {} {}, httpStatusCode={}, errorMsg={}",
                request.getMethod(), request.getPath(), e.getHttpStatusCode(), e.getMessage());

        int httpStatusCode = e.getHttpStatusCode();
        String errorMessage = e.getMessage();
        String detailedMessage = String.format("币安API服务器错误 [%s %s]: HTTP %d, 错误信息: %s",
                request.getMethod(), request.getPath(), httpStatusCode, errorMessage);

        if (httpStatusCode == 500) {
            return new SdkException("INTERNAL_SERVER_ERROR", detailedMessage, e);
        } else if (httpStatusCode == 502) {
            return new SdkException("BAD_GATEWAY", detailedMessage, e);
        } else if (httpStatusCode == 503) {
            return new SdkException("SERVICE_UNAVAILABLE", detailedMessage, e);
        } else if (httpStatusCode == 504) {
            return new SdkException("GATEWAY_TIMEOUT", detailedMessage, e);
        } else if (httpStatusCode >= 500) {
            return new SdkException("SERVER_ERROR", detailedMessage, e);
        } else {
            return new SdkException("UNKNOWN_SERVER_ERROR", detailedMessage, e);
        }
    }
    
    private static class BinanceApiError {
        public int code;
        public String msg;
    }

    private <T> T parseResponse(String rawResponse, Class<T> responseType) throws Exception {
        if (rawResponse == null || rawResponse.trim().isEmpty()) {
            return null;
        }

        if (responseType == String.class) {
            return responseType.cast(rawResponse);
        }

        try {
            // 检查是否为币安错误响应
            if (rawResponse.contains("\"code\":") && rawResponse.contains("\"msg\":")) {
                try {
                    BinanceApiError error = objectMapper.readValue(rawResponse, BinanceApiError.class);
                    if (error.code != 0) {
                        log.warn("收到币安API错误响应: code={}, msg={}", error.code, error.msg);
                        throw new SdkException("API_ERROR", String.format("币安返回错误: code=%d, msg=%s", error.code, error.msg));
                    }
                } catch (Exception e) {
                    // 忽略解析错误，继续尝试作为正常响应解析
                }
            }
            // First, attempt to parse directly into the target type. This will work for standard array responses.
            return objectMapper.readValue(rawResponse, responseType);
        } catch (com.fasterxml.jackson.databind.exc.MismatchedInputException e) {
            // This exception suggests a type mismatch, like getting an object when expecting an array.
            // This is the specific scenario we need to handle for wrapped K-line data.
            log.warn("类型不匹配，可能为包装后的响应，尝试特殊解析: {}", e.getMessage());

            if (rawResponse.trim().startsWith("{")) {
                try {
                    // Try parsing as a Map with a "data" field.
                    com.fasterxml.jackson.core.type.TypeReference<java.util.Map<String, Object>> mapType = new com.fasterxml.jackson.core.type.TypeReference<>() {};
                    java.util.Map<String, Object> dataMap = objectMapper.readValue(rawResponse, mapType);

                    // Handle standard Binance error format
                    if (dataMap.containsKey("code") && dataMap.get("code") != null && Integer.parseInt(dataMap.get("code").toString()) != 0) {
                         log.warn("收到币安API错误响应: code={}, msg={}", dataMap.get("code"), dataMap.get("msg"));
                         throw new SdkException("API_ERROR", String.format("币安返回错误: code=%s, msg=%s", dataMap.get("code"), dataMap.get("msg")));
                    }

                    // 新增处理：当"data"字段是数组类型时直接返回
                    if (dataMap.containsKey("data") && dataMap.get("data") instanceof java.util.Collection) {
                        return objectMapper.convertValue(dataMap.get("data"), responseType);
                    }

                    // Handle the case where the actual data is nested inside a "data" field as a String
                    if (dataMap.containsKey("data") && dataMap.get("data") instanceof String) {
                        String nestedData = (String) dataMap.get("data");
                        if (nestedData.trim().isEmpty()) {
                            log.warn("嵌套的 'data' 字段为空字符串。");
                            // Assuming an empty list is the correct representation for an empty data string.
                            return objectMapper.readValue("[]", responseType);
                        }
                        log.debug("检测到嵌套的'data'字段，将解析其内容。");
                        // Now parse the *nested* string into the target type
                        return objectMapper.readValue(nestedData, responseType);
                    }
                } catch (Exception parseException) {
                    log.error("特殊解析失败，原始异常将被抛出", parseException);
                    // If special parsing also fails, re-throw the original exception to not hide the root cause.
                    throw e;
                }
            }

            // If it wasn't a mismatched input we can handle, rethrow it.
            throw e;
        }
    }

    public SdkConfiguration getSdkConfiguration() {
        return sdkConfiguration;
    }
    
    public ObjectMapper getObjectMapper() {
        return objectMapper;
    }

    public ApiMetrics getApiMetrics() {
        return apiMetrics;
    }

    public RateLimiter getRateLimiter() {
        return rateLimiter;
    }

    public UnifiedRetryService getRetryService() {
        return retryService;
    }

    private int getWeightValue(ApiRequestWeight weight) {
        switch (weight) {
            case WEIGHT_1:
                return 1;
            case WEIGHT_5:
                return 5;
            case WEIGHT_10:
                return 10;
            case WEIGHT_20:
                return 20;
            case WEIGHT_50:
                return 50;
            default:
                return 1;
        }
    }

    private String getLimitType(ApiRequest<?> request) {
        String path = request.getPath();

        if (path.contains("/order") || path.contains("/batchOrders")) {
            return "order_10s";
        }

        if (path.contains("fapi") || path.contains("usdm")) {
            return "usdm";
        } else if (path.contains("dapi") || path.contains("coinm")) {
            return "coinm";
        }

        return "usdm";
    }

    private boolean tryAcquireByType(String limitType, int weight, long timeoutMs) {
        switch (limitType) {
            case "usdm":
                return rateLimiter.tryAcquireUSDM(weight, timeoutMs, java.util.concurrent.TimeUnit.MILLISECONDS);
            case "coinm":
                return rateLimiter.tryAcquireCOINM(weight, timeoutMs, java.util.concurrent.TimeUnit.MILLISECONDS);
            case "order_10s":
                return rateLimiter.tryAcquireOrder10s(weight, timeoutMs, java.util.concurrent.TimeUnit.MILLISECONDS);
            case "order_1m":
                return rateLimiter.tryAcquireOrder1m(weight, timeoutMs, java.util.concurrent.TimeUnit.MILLISECONDS);
            default:
                return rateLimiter.tryAcquireUSDM(weight, timeoutMs, java.util.concurrent.TimeUnit.MILLISECONDS);
        }
    }
}
