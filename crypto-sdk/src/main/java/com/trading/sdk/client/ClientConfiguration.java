package com.trading.sdk.client;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 客户端配置类
 * 用于配置不同的币安客户端实例
 */
@Data
@Builder(toBuilder = true)
@NoArgsConstructor
@AllArgsConstructor
public class ClientConfiguration {

    /**
     * 配置名称
     */
    @NotBlank(message = "Configuration name cannot be blank")
    private String name;

    /**
     * API密钥
     */
    private String apiKey;

    /**
     * 密钥
     */
    private String secretKey;

    /**
     * 基础URL
     */
    @NotBlank(message = "Base URL cannot be blank")
    private String baseUrl;

    /**
     * 是否为测试网
     */
    @Builder.Default
    private boolean testnet = false;

    /**
     * 是否显示限流信息
     */
    @Builder.Default
    private boolean showLimitUsage = true;

    /**
     * 连接超时时间（毫秒）
     */
    @Min(value = 1000, message = "Connection timeout must be at least 1000ms")
    @Max(value = 60000, message = "Connection timeout cannot exceed 60000ms")
    @Builder.Default
    private int connectionTimeout = 10000;

    /**
     * 读取超时时间（毫秒）
     */
    @Min(value = 1000, message = "Read timeout must be at least 1000ms")
    @Max(value = 300000, message = "Read timeout cannot exceed 300000ms")
    @Builder.Default
    private int readTimeout = 30000;

    /**
     * 写入超时时间（毫秒）
     */
    @Min(value = 1000, message = "Write timeout must be at least 1000ms")
    @Max(value = 60000, message = "Write timeout cannot exceed 60000ms")
    @Builder.Default
    private int writeTimeout = 10000;

    /**
     * 最大重试次数
     */
    @Min(value = 0, message = "Max retries cannot be negative")
    @Max(value = 10, message = "Max retries cannot exceed 10")
    @Builder.Default
    private int maxRetries = 3;

    /**
     * 重试延迟（毫秒）
     */
    @Min(value = 100, message = "Retry delay must be at least 100ms")
    @Max(value = 30000, message = "Retry delay cannot exceed 30000ms")
    @Builder.Default
    private int retryDelay = 1000;

    /**
     * 代理配置
     */
    private ProxyConfiguration proxy;

    /**
     * 是否启用连接池
     */
    @Builder.Default
    private boolean enableConnectionPool = true;

    /**
     * 连接池最大连接数 - 优化为支持更高并发
     */
    @Min(value = 1, message = "Max connections must be at least 1")
    @Max(value = 200, message = "Max connections cannot exceed 200")
    @Builder.Default
    private int maxConnections = 50; // 增加最大连接数

    /**
     * 连接池最大空闲连接数 - 优化空闲连接管理
     */
    @Min(value = 1, message = "Max idle connections must be at least 1")
    @Max(value = 100, message = "Max idle connections cannot exceed 100")
    @Builder.Default
    private int maxIdleConnections = 20; // 增加最大空闲连接数

    /**
     * 连接保活时间（毫秒） - 优化连接复用
     */
    @Min(value = 60000, message = "Keep alive duration must be at least 60000ms")
    @Max(value = 3600000, message = "Keep alive duration cannot exceed 3600000ms")
    @Builder.Default
    private long keepAliveDuration = 600000; // 增加保活时间到10分钟

    /**
     * 代理配置内部类
     */
    @Data
    @Builder(toBuilder = true)
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ProxyConfiguration {

        /**
         * 代理类型
         */
        @Builder.Default
        private ProxyType type = ProxyType.HTTP;

        /**
         * 代理主机
         */
        @NotBlank(message = "Proxy host cannot be blank")
        private String host;

        /**
         * 代理端口
         */
        @Min(value = 1, message = "Proxy port must be at least 1")
        @Max(value = 65535, message = "Proxy port cannot exceed 65535")
        private int port;

        /**
         * 代理用户名（可选）
         */
        private String username;

        /**
         * 代理密码（可选）
         */
        private String password;

        /**
         * 是否需要认证
         */
        public boolean requiresAuth() {
            return username != null && !username.trim().isEmpty() &&
                   password != null && !password.trim().isEmpty();
        }

        /**
         * 验证代理配置
         */
        public void validate() {
            if (host == null || host.trim().isEmpty()) {
                throw new IllegalArgumentException("Proxy host cannot be null or empty");
            }

            if (port <= 0 || port > 65535) {
                throw new IllegalArgumentException("Proxy port must be between 1 and 65535");
            }

            if (type == null) {
                throw new IllegalArgumentException("Proxy type cannot be null");
            }
        }
    }

    /**
     * 代理类型枚举
     */
    public enum ProxyType {
        HTTP,
        SOCKS
    }

    /**
     * 是否为认证客户端
     */
    public boolean isAuthenticated() {
        return apiKey != null && !apiKey.trim().isEmpty() &&
               secretKey != null && !secretKey.trim().isEmpty();
    }

    /**
     * 是否配置了代理
     */
    public boolean hasProxy() {
        return proxy != null && proxy.getHost() != null && !proxy.getHost().trim().isEmpty();
    }

    /**
     * 获取完整的基础URL
     */
    public String getFullBaseUrl() {
        if (baseUrl.endsWith("/")) {
            return baseUrl.substring(0, baseUrl.length() - 1);
        }
        return baseUrl;
    }

    /**
     * 创建默认的生产环境配置
     */
    public static ClientConfiguration createProductionConfig(String apiKey, String secretKey) {
        return ClientConfiguration.builder()
                .name("production")
                .apiKey(apiKey)
                .secretKey(secretKey)
                .baseUrl("https://fapi.binance.com")
                .testnet(false)
                .build();
    }

    /**
     * 创建默认的测试网配置
     */
    public static ClientConfiguration createTestnetConfig(String apiKey, String secretKey) {
        return ClientConfiguration.builder()
                .name("testnet")
                .apiKey(apiKey)
                .secretKey(secretKey)
                .baseUrl("https://testnet.binancefuture.com")
                .testnet(true)
                .build();
    }

    /**
     * 创建公开API配置（无需认证）
     */
    public static ClientConfiguration createPublicConfig() {
        return ClientConfiguration.builder()
                .name("public")
                .baseUrl("https://fapi.binance.com")
                .testnet(false)
                .build();
    }

    /**
     * 创建带代理的配置
     */
    public static ClientConfiguration createConfigWithProxy(String apiKey, String secretKey, 
                                                           String proxyHost, int proxyPort) {
        ProxyConfiguration proxyConfig = ProxyConfiguration.builder()
                .type(ProxyType.HTTP)
                .host(proxyHost)
                .port(proxyPort)
                .build();

        return ClientConfiguration.builder()
                .name("with-proxy")
                .apiKey(apiKey)
                .secretKey(secretKey)
                .baseUrl("https://fapi.binance.com")
                .proxy(proxyConfig)
                .build();
    }

    /**
     * 创建带认证代理的配置
     */
    public static ClientConfiguration createConfigWithAuthProxy(String apiKey, String secretKey,
                                                               String proxyHost, int proxyPort,
                                                               String proxyUsername, String proxyPassword) {
        ProxyConfiguration proxyConfig = ProxyConfiguration.builder()
                .type(ProxyType.HTTP)
                .host(proxyHost)
                .port(proxyPort)
                .username(proxyUsername)
                .password(proxyPassword)
                .build();

        return ClientConfiguration.builder()
                .name("with-auth-proxy")
                .apiKey(apiKey)
                .secretKey(secretKey)
                .baseUrl("https://fapi.binance.com")
                .proxy(proxyConfig)
                .build();
    }

    /**
     * 复制配置并修改名称
     */
    public ClientConfiguration copyWithName(String newName) {
        return this.toBuilder()
                .name(newName)
                .build();
    }

    /**
     * 复制配置并修改基础URL
     */
    public ClientConfiguration copyWithBaseUrl(String newBaseUrl) {
        return this.toBuilder()
                .baseUrl(newBaseUrl)
                .build();
    }

    /**
     * 复制配置并修改认证信息
     */
    public ClientConfiguration copyWithCredentials(String newApiKey, String newSecretKey) {
        return this.toBuilder()
                .apiKey(newApiKey)
                .secretKey(newSecretKey)
                .build();
    }

    /**
     * 验证配置的有效性
     */
    public void validate() {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Configuration name cannot be null or empty");
        }

        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("Base URL cannot be null or empty");
        }

        if (!baseUrl.startsWith("http://") && !baseUrl.startsWith("https://")) {
            throw new IllegalArgumentException("Base URL must start with http:// or https://");
        }

        if (connectionTimeout <= 0) {
            throw new IllegalArgumentException("Connection timeout must be positive");
        }

        if (readTimeout <= 0) {
            throw new IllegalArgumentException("Read timeout must be positive");
        }

        if (writeTimeout <= 0) {
            throw new IllegalArgumentException("Write timeout must be positive");
        }

        if (maxRetries < 0) {
            throw new IllegalArgumentException("Max retries cannot be negative");
        }

        if (retryDelay <= 0) {
            throw new IllegalArgumentException("Retry delay must be positive");
        }

        if (hasProxy()) {
            proxy.validate();
        }
    }

    /**
     * ProxyConfiguration的验证方法
     */
    public static class ProxyConfigurationValidator {
        public static void validate(ProxyConfiguration proxy) {
            if (proxy.getHost() == null || proxy.getHost().trim().isEmpty()) {
                throw new IllegalArgumentException("Proxy host cannot be null or empty");
            }

            if (proxy.getPort() <= 0 || proxy.getPort() > 65535) {
                throw new IllegalArgumentException("Proxy port must be between 1 and 65535");
            }

            if (proxy.getType() == null) {
                throw new IllegalArgumentException("Proxy type cannot be null");
            }
        }
    }

    @Override
    public String toString() {
        return String.format("ClientConfiguration{name='%s', baseUrl='%s', testnet=%s, authenticated=%s, hasProxy=%s}",
                name, baseUrl, testnet, isAuthenticated(), hasProxy());
    }
}
