package com.trading.sdk.client;

import com.binance.connector.futures.client.impl.CMFuturesClientImpl;
import com.binance.connector.futures.client.impl.UMFuturesClientImpl;
import com.trading.sdk.config.SdkConfiguration;
import com.trading.common.exception.SdkException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.pool2.BasePooledObjectFactory;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.time.Duration;

import com.trading.common.client.BinanceClientPoolProvider;

@Slf4j
@Component
public class BinanceClient<PERSON>ool implements InitializingBean, DisposableBean, BinanceClientPoolProvider {

    private final SdkConfiguration sdkConfiguration;
    private final BinanceClientFactory clientFactory;
    
    private GenericObjectPool<UMFuturesClientImpl> umFuturesClientPool;
    private GenericObjectPool<CMFuturesClientImpl> cmFuturesClientPool;
    
    private volatile boolean initialized = false;

    public BinanceClientPool(SdkConfiguration sdkConfiguration, BinanceClientFactory clientFactory) {
        this.sdkConfiguration = sdkConfiguration;
        this.clientFactory = clientFactory;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        initialize();
    }

    @Override
    public void destroy() throws Exception {
        shutdown();
    }

    private void initialize() {
        if (initialized) {
            return;
        }
        
        log.info("初始化Binance客户端连接池...");
        
        try {
            createDefaultPools();
            initialized = true;
            log.info("Binance客户端连接池初始化完成");
        } catch (Exception e) {
            log.error("初始化Binance客户端连接池失败", e);
            throw new SdkException("Failed to initialize BinanceClientPool", e);
        }
    }

    private void createDefaultPools() {
        SdkConfiguration.PoolConfig poolConfig = sdkConfiguration.getPool();
        
        umFuturesClientPool = new GenericObjectPool<>(new UMFuturesClientFactory(), createPoolConfig(poolConfig));
        cmFuturesClientPool = new GenericObjectPool<>(new CMFuturesClientFactory(), createPoolConfig(poolConfig));
        
        log.debug("默认合约客户端连接池创建完成: maxTotal={}, maxIdle={}",
                poolConfig.getMaxTotal(), poolConfig.getMaxIdle());
    }

    private <T> GenericObjectPoolConfig<T> createPoolConfig(SdkConfiguration.PoolConfig poolConfig) {
        GenericObjectPoolConfig<T> config = new GenericObjectPoolConfig<>();
        config.setMaxTotal(poolConfig.getMaxTotal());
        config.setMaxIdle(poolConfig.getMaxIdle());
        config.setMinIdle(poolConfig.getMinIdle());
        config.setMaxWait(Duration.ofMillis(poolConfig.getMaxWaitMillis()));
        config.setTestOnBorrow(poolConfig.isTestOnBorrow());
        config.setTestOnReturn(poolConfig.isTestOnReturn());
        config.setTestWhileIdle(poolConfig.isTestWhileIdle());
        config.setTimeBetweenEvictionRuns(Duration.ofMinutes(1));
        config.setMinEvictableIdleTime(Duration.ofMinutes(5));
        config.setNumTestsPerEvictionRun(3);
        config.setBlockWhenExhausted(true);
        config.setLifo(true);
        return config;
    }

    public UMFuturesClientImpl borrowUMClient() {
        checkInitialized();
        try {
            UMFuturesClientImpl client = umFuturesClientPool.borrowObject();
            log.debug("借用UM期货客户端: active={}, idle={}",
                    umFuturesClientPool.getNumActive(), umFuturesClientPool.getNumIdle());
            return client;
        } catch (Exception e) {
            log.error("获取UM期货客户端失败", e);
            throw new SdkException("Failed to borrow UM futures client", e);
        }
    }

    public CMFuturesClientImpl borrowCMClient() {
        checkInitialized();
        try {
            CMFuturesClientImpl client = cmFuturesClientPool.borrowObject();
            log.debug("借用CM期货客户端: active={}, idle={}",
                    cmFuturesClientPool.getNumActive(), cmFuturesClientPool.getNumIdle());
            return client;
        } catch (Exception e) {
            log.error("获取CM期货客户端失败", e);
            throw new SdkException("Failed to borrow CM futures client", e);
        }
    }

    public void returnClient(Object client) {
        if (client == null) {
            return;
        }
        if (client instanceof UMFuturesClientImpl) {
            returnUMClient((UMFuturesClientImpl) client);
        } else if (client instanceof CMFuturesClientImpl) {
            returnCMClient((CMFuturesClientImpl) client);
        }
    }
    
    private void returnUMClient(UMFuturesClientImpl client) {
        if (umFuturesClientPool != null) {
            try {
                // 添加状态检查防止重复归还
                if (client != null) {
                    umFuturesClientPool.returnObject(client);
                    log.debug("归还UM期货客户端: active={}, idle={}",
                            umFuturesClientPool.getNumActive(), umFuturesClientPool.getNumIdle());
                }
            } catch (IllegalStateException e) {
                log.warn("UM期货客户端已被归还: {}", e.getMessage());
            } catch (Exception e) {
                log.error("归还UM期货客户端失败", e);
            }
        }
    }
    
    private void returnCMClient(CMFuturesClientImpl client) {
        if (cmFuturesClientPool != null) {
            try {
                // 添加状态检查防止重复归还
                if (client != null) {
                    cmFuturesClientPool.returnObject(client);
                    log.debug("归还CM期货客户端: active={}, idle={}",
                            cmFuturesClientPool.getNumActive(), cmFuturesClientPool.getNumIdle());
                }
            } catch (IllegalStateException e) {
                log.warn("CM期货客户端已被归还: {}", e.getMessage());
            } catch (Exception e) {
                log.error("归还CM期货客户端失败", e);
            }
        }
    }

    private void checkInitialized() {
        if (!initialized) {
            throw new SdkException("BinanceClientPool is not initialized");
        }
    }

    private void shutdown() {
        if (!initialized) {
            return;
        }
        
        log.info("关闭Binance客户端连接池...");
        
        try {
            if (umFuturesClientPool != null) {
                umFuturesClientPool.close();
            }
            if (cmFuturesClientPool != null) {
                cmFuturesClientPool.close();
            }
            
            initialized = false;
            log.info("Binance客户端连接池已关闭");
            
        } catch (Exception e) {
            log.error("关闭Binance客户端连接池失败", e);
        }
    }

    public void refreshUMClientPool() {
        checkInitialized();
        log.info("Refreshing UM-Futures client pool...");
        if (umFuturesClientPool != null) {
            umFuturesClientPool.clear();
        }
        log.info("UM-Futures client pool refreshed.");
    }

    public void refreshCMClientPool() {
        checkInitialized();
        log.info("Refreshing COIN-M client pool...");
        if (cmFuturesClientPool != null) {
            cmFuturesClientPool.clear();
        }
        log.info("COIN-M client pool refreshed.");
    }

    private class UMFuturesClientFactory extends BasePooledObjectFactory<UMFuturesClientImpl> {
        @Override
        public UMFuturesClientImpl create() throws Exception {
            log.debug("创建新的UM合约客户端实例");
            return clientFactory.getUMFuturesClient();
        }

        @Override
        public PooledObject<UMFuturesClientImpl> wrap(UMFuturesClientImpl client) {
            return new DefaultPooledObject<>(client);
        }

        @Override
        public boolean validateObject(PooledObject<UMFuturesClientImpl> p) {
            return clientFactory.validateClientConnection(p.getObject());
        }
    }

    private class CMFuturesClientFactory extends BasePooledObjectFactory<CMFuturesClientImpl> {
        @Override
        public CMFuturesClientImpl create() throws Exception {
            log.debug("创建新的CM合约客户端实例");
            return clientFactory.getCMFuturesClient();
        }

        @Override
        public PooledObject<CMFuturesClientImpl> wrap(CMFuturesClientImpl client) {
            return new DefaultPooledObject<>(client);
        }
        
        @Override
        public boolean validateObject(PooledObject<CMFuturesClientImpl> p) {
            return clientFactory.validateClientConnection(p.getObject());
        }
    }
}
