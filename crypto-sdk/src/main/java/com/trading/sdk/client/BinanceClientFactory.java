package com.trading.sdk.client;

import com.binance.connector.futures.client.impl.CMFuturesClientImpl;
import com.binance.connector.futures.client.impl.UMFuturesClientImpl;
import com.binance.connector.futures.client.utils.ProxyAuth;
import com.trading.sdk.config.SdkConfiguration;
import com.trading.common.enums.DefaultUrls;
import com.trading.common.enums.ErrorCode;
import com.trading.common.exception.SdkException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;

import java.net.InetSocketAddress;
import java.net.Proxy;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

@Component
public class BinanceClientFactory {

    private static final Logger log = LoggerFactory.getLogger(BinanceClientFactory.class);

    private final SdkConfiguration sdkConfiguration;

    private final ConcurrentHashMap<String, UMFuturesClientImpl> umClientCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, CMFuturesClientImpl> cmClientCache = new ConcurrentHashMap<>();

    private final ReentrantLock createLock = new ReentrantLock();

    public BinanceClientFactory(SdkConfiguration sdkConfiguration) {
        this.sdkConfiguration = sdkConfiguration;
        log.info("币安客户端工厂初始化完成，支持URL配置: {}",
            sdkConfiguration.getBinance().isTestnet() ? "测试网环境" : "生产环境");
    }

    private String getApiUrl(boolean isUSDM) {
        boolean isTestnet = sdkConfiguration.getBinance().isTestnet();

        if (isUSDM) {
            return isTestnet ? DefaultUrls.USDM_TEST_URL.getUrl() : DefaultUrls.USDM_PROD_URL.getUrl();
        } else {
            return isTestnet ? DefaultUrls.COINM_TEST_URL.getUrl() : DefaultUrls.COINM_PROD_URL.getUrl();
        }
    }

    public UMFuturesClientImpl getUMFuturesClient() throws SdkException {
        return getClient(true, "default_um", this::createUMFuturesClient);
    }

    public CMFuturesClientImpl getCMFuturesClient() throws SdkException {
        return getClient(false, "default_cm", this::createCMFuturesClient);
    }

    private <T> T getClient(boolean isUSDM, String cacheKey, ClientSupplier<T> supplier) {
        ConcurrentHashMap<String, T> cache = (ConcurrentHashMap<String, T>) (isUSDM ? umClientCache : cmClientCache);
        String clientTypeName = isUSDM ? "USD-M" : "COIN-M";

        T client = cache.get(cacheKey);
        if (client != null) {
            log.debug("从缓存获取{}合约客户端实例", clientTypeName);
            return client;
        }

        createLock.lock();
        try {
            client = cache.get(cacheKey);
            if (client != null) {
                return client;
            }

            log.info("创建新的{}合约客户端实例，环境: {}",
                clientTypeName, sdkConfiguration.getBinance().isTestnet() ? "测试网" : "生产环境");

            client = supplier.get(null, null);
            cache.put(cacheKey, client);
            return client;
        } finally {
            createLock.unlock();
        }
    }


    @FunctionalInterface
    private interface ClientSupplier<T> {
        T get(String apiKey, String secretKey);
    }

    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    private UMFuturesClientImpl createUMFuturesClient(String apiKey, String secretKey) {
        return createClient(true, apiKey, secretKey, UMFuturesClientImpl.class);
    }

    @Retryable(value = {Exception.class}, maxAttempts = 3, backoff = @Backoff(delay = 1000))
    private CMFuturesClientImpl createCMFuturesClient(String apiKey, String secretKey) {
        return createClient(false, apiKey, secretKey, CMFuturesClientImpl.class);
    }
    
    private <T> T createClient(boolean isUSDM, String apiKey, String secretKey, Class<T> clientClass) {
        try {
            SdkConfiguration.BinanceConfig config = sdkConfiguration.getBinance();
            String finalApiKey = apiKey != null ? apiKey : config.getApiKey();
            String finalSecretKey = secretKey != null ? secretKey : config.getSecretKey();
            String baseUrl = getApiUrl(isUSDM);

            T client;
            if (finalApiKey != null && !finalApiKey.trim().isEmpty()) {
                client = clientClass.getConstructor(String.class, String.class, String.class)
                                    .newInstance(finalApiKey, finalSecretKey, baseUrl);
            } else {
                client = clientClass.getConstructor(String.class).newInstance(baseUrl);
            }
            
            if (client instanceof UMFuturesClientImpl) {
                 ((UMFuturesClientImpl) client).setShowLimitUsage(true);
            } else if (client instanceof CMFuturesClientImpl) {
                 ((CMFuturesClientImpl) client).setShowLimitUsage(true);
            }

            return client;

        } catch (Exception e) {
            log.error("创建合约客户端失败", e);
            throw new SdkException("Failed to create futures client", e);
        }
    }

    public boolean validateClientConnection(Object client) {
        try {
            if (client instanceof UMFuturesClientImpl) {
                String result = ((UMFuturesClientImpl) client).market().time();
                 return result != null && !result.trim().isEmpty();
            } else if (client instanceof CMFuturesClientImpl) {
                String result = ((CMFuturesClientImpl) client).market().time();
                return result != null && !result.trim().isEmpty();
            }
           return false;
        } catch (Exception e) {
            log.warn("客户端连接验证失败: {}", e.getMessage());
            return false;
        }
    }
}
