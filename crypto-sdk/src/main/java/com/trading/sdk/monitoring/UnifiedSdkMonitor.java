package com.trading.sdk.monitoring;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Timer;
import io.micrometer.core.instrument.Gauge;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.actuate.health.HealthIndicator;
import org.springframework.boot.actuate.health.Health;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 统一的SDK监控服务
 * 完整的SDK监控解决方案
 * 监控API调用、WebSocket连接、限流器性能等
 *
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Service
public class UnifiedSdkMonitor implements HealthIndicator {

    private static final Logger log = LoggerFactory.getLogger(UnifiedSdkMonitor.class);

    @Autowired
    private MeterRegistry meterRegistry;

    // API监控指标
    private final AtomicLong totalApiCalls = new AtomicLong(0);
    private final AtomicLong successfulApiCalls = new AtomicLong(0);
    private final AtomicLong failedApiCalls = new AtomicLong(0);
    
    // WebSocket监控指标
    private final AtomicLong activeConnections = new AtomicLong(0);
    private final AtomicLong totalConnections = new AtomicLong(0);
    private final AtomicLong messagesReceived = new AtomicLong(0);
    private final AtomicLong messagesSent = new AtomicLong(0);
    
    // 性能指标
    private final AtomicReference<Double> avgResponseTime = new AtomicReference<>(0.0);
    private final AtomicReference<Double> apiCallRate = new AtomicReference<>(0.0);
    private final AtomicReference<Double> messageRate = new AtomicReference<>(0.0);
    
    // 健康状态
    private final AtomicReference<Health> cachedHealth = new AtomicReference<>();
    private volatile long lastHealthCheck = 0;
    private static final long HEALTH_CACHE_DURATION = 30000; // 30秒缓存

    /**
     * 记录API调用开始
     */
    public Timer.Sample startApiCall(String endpoint, String method) {
        totalApiCalls.incrementAndGet();
        
        Counter.builder("sdk.api.calls.total")
                .tag("endpoint", endpoint)
                .tag("method", method)
                .register(meterRegistry)
                .increment();
        
        return Timer.start(meterRegistry);
    }

    /**
     * 记录API调用成功
     */
    public void recordApiSuccess(Timer.Sample sample, String endpoint) {
        successfulApiCalls.incrementAndGet();
        
        sample.stop(Timer.builder("sdk.api.response.duration")
                .tag("endpoint", endpoint)
                .tag("status", "success")
                .register(meterRegistry));
        
        Counter.builder("sdk.api.success.total")
                .tag("endpoint", endpoint)
                .register(meterRegistry)
                .increment();
        
        log.debug("API调用成功: endpoint={}", endpoint);
    }

    /**
     * 记录API调用失败
     */
    public void recordApiFailure(Timer.Sample sample, String endpoint, String errorType) {
        failedApiCalls.incrementAndGet();
        
        if (sample != null) {
            sample.stop(Timer.builder("sdk.api.response.duration")
                    .tag("endpoint", endpoint)
                    .tag("status", "failure")
                    .register(meterRegistry));
        }
        
        Counter.builder("sdk.api.failures.total")
                .tag("endpoint", endpoint)
                .tag("error", errorType)
                .register(meterRegistry)
                .increment();
        
        log.warn("API调用失败: endpoint={}, error={}", endpoint, errorType);
    }

    /**
     * 记录WebSocket连接建立
     */
    public void recordWebSocketConnected(String endpoint) {
        totalConnections.incrementAndGet();
        activeConnections.incrementAndGet();
        
        Counter.builder("sdk.websocket.connections.total")
                .tag("endpoint", endpoint)
                .tag("type", "connected")
                .register(meterRegistry)
                .increment();
        
        log.info("WebSocket连接建立: endpoint={}, 活跃连接数: {}", endpoint, activeConnections.get());
    }

    /**
     * 记录WebSocket连接断开
     */
    public void recordWebSocketDisconnected(String endpoint, String reason) {
        activeConnections.decrementAndGet();
        
        Counter.builder("sdk.websocket.connections.total")
                .tag("endpoint", endpoint)
                .tag("type", "disconnected")
                .tag("reason", reason)
                .register(meterRegistry)
                .increment();
        
        log.info("WebSocket连接断开: endpoint={}, reason={}, 活跃连接数: {}", 
                endpoint, reason, activeConnections.get());
    }

    /**
     * 记录WebSocket消息接收
     */
    public void recordWebSocketMessageReceived(String messageType, long size) {
        messagesReceived.incrementAndGet();
        
        Counter.builder("sdk.websocket.messages.total")
                .tag("direction", "received")
                .tag("type", messageType)
                .register(meterRegistry)
                .increment();
        
        Gauge.builder("sdk.websocket.message.size", size, Double::valueOf)
                .tag("direction", "received")
                .tag("type", messageType)
                .register(meterRegistry);
    }

    /**
     * 记录WebSocket消息发送
     */
    public void recordWebSocketMessageSent(String messageType, long size) {
        messagesSent.incrementAndGet();
        
        Counter.builder("sdk.websocket.messages.total")
                .tag("direction", "sent")
                .tag("type", messageType)
                .register(meterRegistry)
                .increment();
        
        Gauge.builder("sdk.websocket.message.size", size, Double::valueOf)
                .tag("direction", "sent")
                .tag("type", messageType)
                .register(meterRegistry);
    }

    /**
     * 记录限流器事件
     */
    public void recordRateLimiterEvent(String limiterName, String eventType) {
        Counter.builder("sdk.ratelimiter.events.total")
                .tag("limiter", limiterName)
                .tag("event", eventType)
                .register(meterRegistry)
                .increment();
        
        log.debug("限流器事件: limiter={}, event={}", limiterName, eventType);
    }

    /**
     * 定期计算性能指标 - 每分钟执行一次
     */
    @Scheduled(fixedRate = 60000)
    public void calculatePerformanceMetrics() {
        try {
            long totalCalls = totalApiCalls.get();
            long successCalls = successfulApiCalls.get();
            long failedCalls = failedApiCalls.get();
            long totalMessages = messagesReceived.get() + messagesSent.get();
            
            // 计算API调用速率（每分钟）
            double callRate = totalCalls / 60.0;
            apiCallRate.set(callRate);
            
            // 计算消息速率（每分钟）
            double msgRate = totalMessages / 60.0;
            messageRate.set(msgRate);
            
            // 注册Gauge指标
            Gauge.builder("sdk.api.call.rate", apiCallRate, AtomicReference::get)
                    .description("API调用速率")
                    .register(meterRegistry);
            
            Gauge.builder("sdk.websocket.message.rate", messageRate, AtomicReference::get)
                    .description("WebSocket消息速率")
                    .register(meterRegistry);
            
            Gauge.builder("sdk.websocket.connections.active", activeConnections, AtomicLong::doubleValue)
                    .description("活跃WebSocket连接数")
                    .register(meterRegistry);
            
            log.info("SDK监控指标 - API调用: {}, 成功: {}, 失败: {}, 活跃连接: {}, 消息总数: {}", 
                    totalCalls, successCalls, failedCalls, activeConnections.get(), totalMessages);
            
        } catch (Exception e) {
            log.error("计算SDK性能指标失败", e);
        }
    }

    /**
     * 健康检查实现
     */
    @Override
    public Health health() {
        long currentTime = System.currentTimeMillis();
        
        // 使用缓存避免频繁检查
        if (currentTime - lastHealthCheck < HEALTH_CACHE_DURATION && cachedHealth.get() != null) {
            return cachedHealth.get();
        }

        Health health = performHealthCheck();
        cachedHealth.set(health);
        lastHealthCheck = currentTime;
        return health;
    }

    /**
     * 执行实际的健康检查
     */
    private Health performHealthCheck() {
        try {
            long totalCalls = totalApiCalls.get();
            long successCalls = successfulApiCalls.get();
            long failedCalls = failedApiCalls.get();
            
            double successRate = totalCalls > 0 ? (double) successCalls / totalCalls * 100 : 100;
            double failureRate = totalCalls > 0 ? (double) failedCalls / totalCalls * 100 : 0;
            
            Health.Builder builder = Health.up()
                    .withDetail("service", "sdk-monitor")
                    .withDetail("totalApiCalls", totalCalls)
                    .withDetail("successfulCalls", successCalls)
                    .withDetail("failedCalls", failedCalls)
                    .withDetail("successRate", String.format("%.2f%%", successRate))
                    .withDetail("failureRate", String.format("%.2f%%", failureRate))
                    .withDetail("activeConnections", activeConnections.get())
                    .withDetail("totalConnections", totalConnections.get())
                    .withDetail("messagesReceived", messagesReceived.get())
                    .withDetail("messagesSent", messagesSent.get())
                    .withDetail("apiCallRate", apiCallRate.get())
                    .withDetail("messageRate", messageRate.get());

            // 根据失败率判断健康状态
            if (failureRate > 20) {
                return builder.down()
                        .withDetail("reason", "API失败率过高: " + String.format("%.2f%%", failureRate))
                        .build();
            } else if (failureRate > 10) {
                return builder.up()
                        .withDetail("warning", "API失败率较高: " + String.format("%.2f%%", failureRate))
                        .build();
            } else {
                return builder.up()
                        .withDetail("status", "运行正常")
                        .build();
            }
            
        } catch (Exception e) {
            log.error("SDK健康检查失败", e);
            return Health.down()
                    .withDetail("service", "sdk-monitor")
                    .withDetail("error", e.getMessage())
                    .withException(e)
                    .build();
        }
    }

    /**
     * 获取监控统计信息
     */
    public SdkMonitoringStats getStats() {
        return new SdkMonitoringStats(
                totalApiCalls.get(),
                successfulApiCalls.get(),
                failedApiCalls.get(),
                activeConnections.get(),
                totalConnections.get(),
                messagesReceived.get(),
                messagesSent.get(),
                apiCallRate.get(),
                messageRate.get()
        );
    }

    /**
     * SDK监控统计信息类
     */
    public static class SdkMonitoringStats {
        private final long totalApiCalls;
        private final long successfulApiCalls;
        private final long failedApiCalls;
        private final long activeConnections;
        private final long totalConnections;
        private final long messagesReceived;
        private final long messagesSent;
        private final double apiCallRate;
        private final double messageRate;

        public SdkMonitoringStats(long totalApiCalls, long successfulApiCalls, long failedApiCalls,
                                 long activeConnections, long totalConnections, long messagesReceived,
                                 long messagesSent, double apiCallRate, double messageRate) {
            this.totalApiCalls = totalApiCalls;
            this.successfulApiCalls = successfulApiCalls;
            this.failedApiCalls = failedApiCalls;
            this.activeConnections = activeConnections;
            this.totalConnections = totalConnections;
            this.messagesReceived = messagesReceived;
            this.messagesSent = messagesSent;
            this.apiCallRate = apiCallRate;
            this.messageRate = messageRate;
        }

        // Getters
        public long getTotalApiCalls() { return totalApiCalls; }
        public long getSuccessfulApiCalls() { return successfulApiCalls; }
        public long getFailedApiCalls() { return failedApiCalls; }
        public long getActiveConnections() { return activeConnections; }
        public long getTotalConnections() { return totalConnections; }
        public long getMessagesReceived() { return messagesReceived; }
        public long getMessagesSent() { return messagesSent; }
        public double getApiCallRate() { return apiCallRate; }
        public double getMessageRate() { return messageRate; }
        
        public double getSuccessRate() { 
            return totalApiCalls > 0 ? (double) successfulApiCalls / totalApiCalls * 100 : 100; 
        }
        
        public double getFailureRate() { 
            return totalApiCalls > 0 ? (double) failedApiCalls / totalApiCalls * 100 : 0; 
        }

        @Override
        public String toString() {
            return String.format("SdkStats{apiCalls=%d, success=%d, failed=%d, activeConn=%d, " +
                    "totalConn=%d, msgReceived=%d, msgSent=%d, callRate=%.2f/min, msgRate=%.2f/min, " +
                    "successRate=%.2f%%, failureRate=%.2f%%}",
                    totalApiCalls, successfulApiCalls, failedApiCalls, activeConnections,
                    totalConnections, messagesReceived, messagesSent, apiCallRate, messageRate,
                    getSuccessRate(), getFailureRate());
        }
    }
}
