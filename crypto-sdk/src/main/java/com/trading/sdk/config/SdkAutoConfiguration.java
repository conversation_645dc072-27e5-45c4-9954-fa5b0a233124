package com.trading.sdk.config;

import com.trading.sdk.config.SdkConfiguration;
import com.trading.sdk.api.MarketDataApi;
import com.trading.sdk.api.impl.BinanceMarketDataApi;
import com.trading.sdk.api.impl.BinanceRestApiClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import com.trading.common.ratelimit.RateLimiter;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.ComponentScan;
import com.trading.sdk.client.BinanceClientPool;
import com.trading.common.ratelimit.RateLimiter;
import com.trading.common.retry.UnifiedRetryService;
import com.trading.common.metrics.ApiMetrics;
import com.trading.common.circuit.CircuitBreakerManager;
import com.trading.sdk.websocket.callback.MessageHandler;
import com.trading.common.metrics.WebSocketMetrics;
import com.trading.sdk.websocket.WebSocketManager;
import org.springframework.retry.annotation.EnableRetry;

/**
 * SDK自动配置类
 * 自动配置SDK相关的Bean和组件
 */
@Configuration
@EnableRetry
public class SdkAutoConfiguration {

    @Bean
    @ConditionalOnMissingBean
    @ConfigurationProperties(prefix = "trading.sdk")
    public SdkConfiguration sdkConfiguration() {
        return new SdkConfiguration();
    }


    /**
     * API Client Bean
     */
    @Bean
    @ConditionalOnMissingBean
    public BinanceRestApiClient restApiClient(BinanceClientPool clientPool, 
                                             SdkConfiguration sdkConfiguration,
                                             RateLimiter rateLimiter,
                                             UnifiedRetryService retryService,
                                             ApiMetrics apiMetrics,
                                             CircuitBreakerManager circuitBreakerManager) {
        return new BinanceRestApiClient(clientPool, sdkConfiguration, 
                                      rateLimiter, retryService, apiMetrics, circuitBreakerManager);
    }

    /**
     * MarketDataApi Bean
     * 使用完整的BinanceMarketDataApi实现
     */
    @Bean
    @ConditionalOnMissingBean
    public MarketDataApi marketDataApi(BinanceRestApiClient restApiClient) {
        return new BinanceMarketDataApi(restApiClient);
    }

    /**
     * WebSocketManager Bean
     * 负责管理WebSocket连接
     */
    @Bean(initMethod = "initialize", destroyMethod = "shutdown")
    @ConditionalOnMissingBean
    public WebSocketManager webSocketManager(SdkConfiguration sdkConfiguration,
                                           MessageHandler messageHandler) {
        return new WebSocketManager(sdkConfiguration, messageHandler);
    }
}
