package com.trading.sdk.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.time.Duration;

/**
 * 错误恢复配置类
 * 管理错误恢复机制的配置参数
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Component
@Validated
@ConfigurationProperties(prefix = "trading.error-recovery")
public class ErrorRecoveryConfig {

    /**
     * 是否启用错误恢复
     */
    private boolean enabled = true;

    /**
     * 自动恢复配置
     */
    @NotNull(message = "Auto recovery config cannot be null")
    private AutoRecoveryConfig autoRecovery = new AutoRecoveryConfig();

    /**
     * 健康检查配置
     */
    @NotNull(message = "Health check config cannot be null")
    private HealthCheckConfig healthCheck = new HealthCheckConfig();

    /**
     * 故障转移配置
     */
    @NotNull(message = "Failover config cannot be null")
    private FailoverConfig failover = new FailoverConfig();

    /**
     * 自动恢复配置
     */
    @Data
    public static class AutoRecoveryConfig {
        
        /**
         * 是否启用自动恢复
         */
        private boolean enabled = true;
        
        /**
         * 最大恢复尝试次数
         */
        @Min(value = 1, message = "Max recovery attempts must be at least 1")
        @Max(value = 10, message = "Max recovery attempts must not exceed 10")
        private int maxAttempts = 3;
        
        /**
         * 恢复间隔时间（毫秒）
         */
        @Min(value = 1000, message = "Recovery interval must be at least 1000ms")
        @Max(value = 300000, message = "Recovery interval must not exceed 300000ms")
        private long recoveryInterval = 30000;
        
        /**
         * 恢复超时时间（毫秒）
         */
        @Min(value = 5000, message = "Recovery timeout must be at least 5000ms")
        @Max(value = 600000, message = "Recovery timeout must not exceed 600000ms")
        private long recoveryTimeout = 60000;
        
        /**
         * 指数退避倍数
         */
        @Min(value = 1, message = "Backoff multiplier must be at least 1.0")
        @Max(value = 5, message = "Backoff multiplier must not exceed 5.0")
        private double backoffMultiplier = 2.0;
        
        /**
         * 最大退避时间（毫秒）
         */
        @Min(value = 10000, message = "Max backoff time must be at least 10000ms")
        @Max(value = 600000, message = "Max backoff time must not exceed 600000ms")
        private long maxBackoffTime = 300000;
    }

    /**
     * 健康检查配置
     */
    @Data
    public static class HealthCheckConfig {
        
        /**
         * 是否启用健康检查
         */
        private boolean enabled = true;
        
        /**
         * 健康检查间隔（毫秒）
         */
        @Min(value = 5000, message = "Health check interval must be at least 5000ms")
        @Max(value = 300000, message = "Health check interval must not exceed 300000ms")
        private long interval = 30000;
        
        /**
         * 健康检查超时时间（毫秒）
         */
        @Min(value = 1000, message = "Health check timeout must be at least 1000ms")
        @Max(value = 60000, message = "Health check timeout must not exceed 60000ms")
        private long timeout = 10000;
        
        /**
         * 连续失败阈值
         */
        @Min(value = 1, message = "Failure threshold must be at least 1")
        @Max(value = 10, message = "Failure threshold must not exceed 10")
        private int failureThreshold = 3;
        
        /**
         * 连续成功阈值
         */
        @Min(value = 1, message = "Success threshold must be at least 1")
        @Max(value = 10, message = "Success threshold must not exceed 10")
        private int successThreshold = 2;
        
        /**
         * 健康检查端点
         */
        private String endpoint = "/fapi/v1/ping";
        
        /**
         * 是否启用深度健康检查
         */
        private boolean deepCheck = false;
        
        /**
         * 深度检查端点列表
         */
        private String[] deepCheckEndpoints = {
            "/fapi/v1/time",
            "/fapi/v1/exchangeInfo"
        };
    }

    /**
     * 故障转移配置
     */
    @Data
    public static class FailoverConfig {
        
        /**
         * 是否启用故障转移
         */
        private boolean enabled = true;
        
        /**
         * 故障转移策略
         * ROUND_ROBIN: 轮询
         * PRIORITY: 优先级
         * RANDOM: 随机
         */
        private FailoverStrategy strategy = FailoverStrategy.PRIORITY;
        
        /**
         * 故障转移超时时间（毫秒）
         */
        @Min(value = 1000, message = "Failover timeout must be at least 1000ms")
        @Max(value = 60000, message = "Failover timeout must not exceed 60000ms")
        private long failoverTimeout = 10000;
        
        /**
         * 最大故障转移次数
         */
        @Min(value = 1, message = "Max failover attempts must be at least 1")
        @Max(value = 5, message = "Max failover attempts must not exceed 5")
        private int maxFailoverAttempts = 2;
        
        /**
         * 故障转移冷却时间（毫秒）
         */
        @Min(value = 10000, message = "Failover cooldown must be at least 10000ms")
        @Max(value = 600000, message = "Failover cooldown must not exceed 600000ms")
        private long failoverCooldown = 60000;
        
        /**
         * 备用端点列表
         */
        private String[] backupEndpoints = {
            "https://fapi.binance.com",
            "https://fapi1.binance.com",
            "https://fapi2.binance.com"
        };
    }

    /**
     * 故障转移策略枚举
     */
    public enum FailoverStrategy {
        /**
         * 轮询策略
         */
        ROUND_ROBIN,
        
        /**
         * 优先级策略
         */
        PRIORITY,
        
        /**
         * 随机策略
         */
        RANDOM
    }

    /**
     * 获取自动恢复配置
     * 
     * @return 自动恢复配置
     */
    public AutoRecoveryConfig getAutoRecovery() {
        return autoRecovery;
    }

    /**
     * 获取健康检查配置
     * 
     * @return 健康检查配置
     */
    public HealthCheckConfig getHealthCheck() {
        return healthCheck;
    }

    /**
     * 获取故障转移配置
     * 
     * @return 故障转移配置
     */
    public FailoverConfig getFailover() {
        return failover;
    }

    /**
     * 验证配置的有效性
     * 
     * @return 如果配置有效返回true，否则返回false
     */
    public boolean isValid() {
        return autoRecovery != null && healthCheck != null && failover != null;
    }

    /**
     * 获取配置摘要
     * 
     * @return 配置摘要字符串
     */
    public String getConfigSummary() {
        return String.format("ErrorRecoveryConfig{enabled=%s, autoRecovery.enabled=%s, " +
                           "healthCheck.enabled=%s, failover.enabled=%s, failover.strategy=%s}",
                           enabled, autoRecovery.enabled, healthCheck.enabled, 
                           failover.enabled, failover.strategy);
    }
}