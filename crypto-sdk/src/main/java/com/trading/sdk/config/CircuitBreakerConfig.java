package com.trading.sdk.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.time.Duration;

/**
 * 熔断器配置类
 * 管理熔断器相关的配置参数
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Component
@Validated
@ConfigurationProperties(prefix = "trading.circuit-breaker")
public class CircuitBreakerConfig {

    /**
     * API熔断器配置
     */
    @NotNull(message = "API circuit breaker config cannot be null")
    private ApiCircuitBreakerConfig api = new ApiCircuitBreakerConfig();

    /**
     * WebSocket熔断器配置
     */
    @NotNull(message = "WebSocket circuit breaker config cannot be null")
    private WebSocketCircuitBreakerConfig websocket = new WebSocketCircuitBreakerConfig();

    /**
     * 客户端熔断器配置
     */
    @NotNull(message = "Client circuit breaker config cannot be null")
    private ClientCircuitBreakerConfig client = new ClientCircuitBreakerConfig();

    /**
     * API熔断器配置
     */
    @Data
    public static class ApiCircuitBreakerConfig {
        
        /**
         * 是否启用熔断器
         */
        private boolean enabled = true;
        
        /**
         * 失败阈值（连续失败次数）
         */
        @Min(value = 1, message = "Failure threshold must be at least 1")
        @Max(value = 100, message = "Failure threshold must not exceed 100")
        private int failureThreshold = 10;
        
        /**
         * 成功阈值（半开状态下连续成功次数）
         */
        @Min(value = 1, message = "Success threshold must be at least 1")
        @Max(value = 20, message = "Success threshold must not exceed 20")
        private int successThreshold = 3;
        
        /**
         * 超时阈值（毫秒）
         */
        @Min(value = 100, message = "Timeout threshold must be at least 100ms")
        @Max(value = 60000, message = "Timeout threshold must not exceed 60000ms")
        private long timeoutThreshold = 5000;
        
        /**
         * 恢复超时时间（毫秒）
         */
        @Min(value = 1000, message = "Recovery timeout must be at least 1000ms")
        @Max(value = 300000, message = "Recovery timeout must not exceed 300000ms")
        private long recoveryTimeout = 60000;
        
        /**
         * 慢调用阈值（毫秒）
         */
        @Min(value = 100, message = "Slow call threshold must be at least 100ms")
        @Max(value = 30000, message = "Slow call threshold must not exceed 30000ms")
        private long slowCallThreshold = 3000;
        
        /**
         * 慢调用比例阈值（0.0-1.0）
         */
        @Min(value = 0, message = "Slow call rate threshold must be at least 0.0")
        @Max(value = 1, message = "Slow call rate threshold must not exceed 1.0")
        private double slowCallRateThreshold = 0.5;
        
        /**
         * 最小请求数量（用于计算失败率）
         */
        @Min(value = 1, message = "Minimum request count must be at least 1")
        @Max(value = 1000, message = "Minimum request count must not exceed 1000")
        private int minimumRequestCount = 20;
        
        /**
         * 滑动窗口大小（秒）
         */
        @Min(value = 10, message = "Sliding window size must be at least 10 seconds")
        @Max(value = 300, message = "Sliding window size must not exceed 300 seconds")
        private int slidingWindowSize = 60;
    }

    /**
     * WebSocket熔断器配置
     */
    @Data
    public static class WebSocketCircuitBreakerConfig {
        
        /**
         * 是否启用熔断器
         */
        private boolean enabled = true;
        
        /**
         * 失败阈值
         */
        @Min(value = 1, message = "Failure threshold must be at least 1")
        @Max(value = 50, message = "Failure threshold must not exceed 50")
        private int failureThreshold = 5;
        
        /**
         * 成功阈值
         */
        @Min(value = 1, message = "Success threshold must be at least 1")
        @Max(value = 10, message = "Success threshold must not exceed 10")
        private int successThreshold = 2;
        
        /**
         * 恢复超时时间（毫秒）
         */
        @Min(value = 5000, message = "Recovery timeout must be at least 5000ms")
        @Max(value = 600000, message = "Recovery timeout must not exceed 600000ms")
        private long recoveryTimeout = 30000;
        
        /**
         * 连接超时阈值（毫秒）
         */
        @Min(value = 1000, message = "Connection timeout must be at least 1000ms")
        @Max(value = 60000, message = "Connection timeout must not exceed 60000ms")
        private long connectionTimeout = 10000;
    }

    /**
     * 客户端熔断器配置
     */
    @Data
    public static class ClientCircuitBreakerConfig {
        
        /**
         * 是否启用熔断器
         */
        private boolean enabled = true;
        
        /**
         * 失败阈值
         */
        @Min(value = 1, message = "Failure threshold must be at least 1")
        @Max(value = 20, message = "Failure threshold must not exceed 20")
        private int failureThreshold = 5;
        
        /**
         * 成功阈值
         */
        @Min(value = 1, message = "Success threshold must be at least 1")
        @Max(value = 10, message = "Success threshold must not exceed 10")
        private int successThreshold = 2;
        
        /**
         * 恢复超时时间（毫秒）
         */
        @Min(value = 10000, message = "Recovery timeout must be at least 10000ms")
        @Max(value = 300000, message = "Recovery timeout must not exceed 300000ms")
        private long recoveryTimeout = 60000;
        
        /**
         * 健康检查间隔（毫秒）
         */
        @Min(value = 5000, message = "Health check interval must be at least 5000ms")
        @Max(value = 120000, message = "Health check interval must not exceed 120000ms")
        private long healthCheckInterval = 30000;
    }

    /**
     * 获取API熔断器配置
     * 
     * @return API熔断器配置
     */
    public ApiCircuitBreakerConfig getApi() {
        return api;
    }

    /**
     * 获取WebSocket熔断器配置
     * 
     * @return WebSocket熔断器配置
     */
    public WebSocketCircuitBreakerConfig getWebsocket() {
        return websocket;
    }

    /**
     * 获取客户端熔断器配置
     * 
     * @return 客户端熔断器配置
     */
    public ClientCircuitBreakerConfig getClient() {
        return client;
    }

    /**
     * 验证配置的有效性
     * 
     * @return 如果配置有效返回true，否则返回false
     */
    public boolean isValid() {
        return api != null && websocket != null && client != null;
    }

    /**
     * 获取配置摘要
     * 
     * @return 配置摘要字符串
     */
    public String getConfigSummary() {
        return String.format("CircuitBreakerConfig{api.enabled=%s, api.failureThreshold=%d, " +
                           "websocket.enabled=%s, websocket.failureThreshold=%d, " +
                           "client.enabled=%s, client.failureThreshold=%d}",
                           api.enabled, api.failureThreshold,
                           websocket.enabled, websocket.failureThreshold,
                           client.enabled, client.failureThreshold);
    }
}