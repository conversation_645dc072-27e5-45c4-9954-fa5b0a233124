package com.trading.sdk.config;

import com.trading.common.config.SdkConfigurationProvider;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;

/**
 * SDK配置类
 * 管理币安API的连接配置和参数
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Validated
@ConfigurationProperties(prefix = "trading.sdk")
public class SdkConfiguration implements SdkConfigurationProvider {

    /**
     * 币安API配置
     */
    @NotNull(message = "Binance config cannot be null")
    private BinanceConfig binance = new BinanceConfig();

    /**
     * 连接池配置
     */
    @NotNull(message = "Pool config cannot be null")
    private PoolConfig pool = new PoolConfig();

    /**
     * 重试配置
     */
    @NotNull(message = "Retry config cannot be null")
    private RetryConfig retry = new RetryConfig();

    /**
     * WebSocket配置
     */
    @NotNull(message = "WebSocket config cannot be null")
    private WebSocketConfig websocket = new WebSocketConfig();

    /**
     * 熔断器配置
     */
    @NotNull(message = "Circuit breaker config cannot be null")
    private CircuitBreakerConfig circuitBreaker = new CircuitBreakerConfig();

    /**
     * 错误恢复配置
     */
    @NotNull(message = "Error recovery config cannot be null")
    private ErrorRecoveryConfig errorRecovery = new ErrorRecoveryConfig();

    /**
     * 币安API配置
     */
    @Data
    public static class BinanceConfig {
        private String apiKey;
        private String secretKey;
        private boolean testnet = false;

        @Min(value = 1000, message = "Timeout must be at least 1000ms")
        @Max(value = 60000, message = "Timeout cannot exceed 60000ms")
        private int timeout = 10000; // 10秒

        @Min(value = 1, message = "Max retries must be at least 1")
        @Max(value = 10, message = "Max retries cannot exceed 10")
        private int maxRetries = 3;

        public String getBaseUrl() {
            return testnet ? "https://testnet.binancefuture.com" : "https://fapi.binance.com";
        }

        public String getUmFuturesWebSocketBaseUrl() {
            return testnet ? "wss://stream.binancefuture.com" : "wss://fstream.binance.com";
        }

        public String getCmFuturesWebSocketBaseUrl() {
            return testnet ? "wss://dstream.binancefuture.com" : "wss://dstream.binance.com";
        }
    }

    /**
     * 连接池配置
     */
    @Data
    public static class PoolConfig {
        @Min(value = 1, message = "Max total must be at least 1")
        @Max(value = 100, message = "Max total cannot exceed 100")
        private int maxTotal = 20;
        
        @Min(value = 1, message = "Max idle must be at least 1")
        private int maxIdle = 10;
        
        @Min(value = 0, message = "Min idle cannot be negative")
        private int minIdle = 2;
        
        @Min(value = 1000, message = "Max wait millis must be at least 1000ms")
        private long maxWaitMillis = 5000;
        
        private boolean testOnBorrow = true;
        private boolean testOnReturn = false;
        private boolean testWhileIdle = true;
    }

    /**
     * 重试配置
     */
    @Data
    public static class RetryConfig {
        @Min(value = 1, message = "Max attempts must be at least 1")
        @Max(value = 10, message = "Max attempts cannot exceed 10")
        private int maxAttempts = 3;
        
        @Min(value = 100, message = "Backoff delay must be at least 100ms")
        private long backoffDelay = 1000; // 1秒
        
        @Min(value = 1, message = "Backoff multiplier must be at least 1")
        private double backoffMultiplier = 2.0;
        
        @Min(value = 1000, message = "Max delay must be at least 1000ms")
        private long maxDelay = 30000; // 30秒
    }

    /**
     * WebSocket配置
     */
    @Data
    public static class WebSocketConfig {
        @Min(value = 1000, message = "Connection timeout must be at least 1000ms")
        private int connectionTimeout = 10000; // 10秒
        
        @Min(value = 5000, message = "Read timeout must be at least 5000ms")
        private int readTimeout = 60000; // 60秒
        
        @Min(value = 10000, message = "Ping interval must be at least 10000ms")
        private int pingInterval = 30000; // 30秒
        
        private boolean autoReconnect = true;
        
        @Min(value = 1, message = "Max reconnect attempts must be at least 1")
        @Max(value = 50, message = "Max reconnect attempts cannot exceed 50")
        private int maxReconnectAttempts = 5; // 减少重连次数，降低CPU消耗

        @Min(value = 1000, message = "Reconnect delay must be at least 1000ms")
        private long reconnectDelay = 15000; // 增加到15秒，减少重连频率
    }
}