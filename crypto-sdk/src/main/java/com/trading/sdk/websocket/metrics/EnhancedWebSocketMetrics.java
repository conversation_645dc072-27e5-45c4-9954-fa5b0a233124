package com.trading.sdk.websocket.metrics;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.LongAdder;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.List;
import java.util.ArrayList;
import java.util.Collections;

/**
 * 增强版WebSocket指标收集器
 * 提供详细的WebSocket连接和消息处理性能监控
 * 
 * 功能特性：
 * - 连接状态监控
 * - 消息处理性能统计
 * - 延迟分布分析
 * - 错误率统计
 * - 实时性能报告
 * - 历史数据趋势分析
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class EnhancedWebSocketMetrics {
    
    private static final Logger log = LoggerFactory.getLogger(EnhancedWebSocketMetrics.class);
    
    // 指标收集配置
    private static final int LATENCY_BUCKETS = 10;
    private static final long[] LATENCY_BOUNDARIES = {1, 5, 10, 25, 50, 100, 250, 500, 1000, 2500}; // 毫秒
    private static final int HISTORY_SIZE = 100;
    
    // 连接指标
    private final LongAdder totalConnections = new LongAdder();
    private final LongAdder activeConnections = new LongAdder();
    private final LongAdder connectionFailures = new LongAdder();
    private final LongAdder reconnections = new LongAdder();
    private final AtomicLong totalConnectionTime = new AtomicLong(0);
    
    // 消息指标
    private final LongAdder totalMessagesReceived = new LongAdder();
    private final LongAdder totalMessagesSent = new LongAdder();
    private final LongAdder messageProcessingErrors = new LongAdder();
    private final AtomicLong totalMessageProcessingTime = new AtomicLong(0);
    
    // 延迟分布统计
    private final LongAdder[] latencyBuckets = new LongAdder[LATENCY_BUCKETS];
    private final AtomicLong minLatency = new AtomicLong(Long.MAX_VALUE);
    private final AtomicLong maxLatency = new AtomicLong(0);
    
    // 订阅指标
    private final ConcurrentHashMap<String, SubscriptionMetrics> subscriptionMetrics = new ConcurrentHashMap<>();
    
    // 历史数据
    private final List<MetricsSnapshot> metricsHistory = Collections.synchronizedList(new ArrayList<>());
    
    // 定时任务
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    
    // 启动时间
    private final LocalDateTime startTime = LocalDateTime.now();
    
    public EnhancedWebSocketMetrics() {
        // 初始化延迟桶
        for (int i = 0; i < LATENCY_BUCKETS; i++) {
            latencyBuckets[i] = new LongAdder();
        }
        
        // 启动定时快照任务
        startPeriodicSnapshot();
        
        log.info("增强版WebSocket指标收集器初始化完成");
    }
    
    /**
     * 启动定时快照任务
     */
    private void startPeriodicSnapshot() {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                takeSnapshot();
            } catch (Exception e) {
                log.error("创建指标快照失败", e);
            }
        }, 60, 60, TimeUnit.SECONDS); // 每分钟创建一次快照
    }
    
    /**
     * 记录连接建立
     */
    public void recordConnectionEstablished(String subscriptionKey) {
        totalConnections.increment();
        activeConnections.increment();
        
        SubscriptionMetrics metrics = getOrCreateSubscriptionMetrics(subscriptionKey);
        metrics.connectionEstablished();
        
        log.debug("记录连接建立: subscriptionKey={}", subscriptionKey);
    }
    
    /**
     * 记录连接关闭
     */
    public void recordConnectionClosed(String subscriptionKey, long connectionDurationMs) {
        activeConnections.decrement();
        totalConnectionTime.addAndGet(connectionDurationMs);
        
        SubscriptionMetrics metrics = subscriptionMetrics.get(subscriptionKey);
        if (metrics != null) {
            metrics.connectionClosed(connectionDurationMs);
        }
        
        log.debug("记录连接关闭: subscriptionKey={}, duration={}ms", subscriptionKey, connectionDurationMs);
    }
    
    /**
     * 记录连接失败
     */
    public void recordConnectionFailure(String subscriptionKey, String reason) {
        connectionFailures.increment();
        
        SubscriptionMetrics metrics = getOrCreateSubscriptionMetrics(subscriptionKey);
        metrics.connectionFailed(reason);
        
        log.debug("记录连接失败: subscriptionKey={}, reason={}", subscriptionKey, reason);
    }
    
    /**
     * 记录重连
     */
    public void recordReconnection(String subscriptionKey) {
        reconnections.increment();
        
        SubscriptionMetrics metrics = getOrCreateSubscriptionMetrics(subscriptionKey);
        metrics.reconnected();
        
        log.debug("记录重连: subscriptionKey={}", subscriptionKey);
    }
    
    /**
     * 记录消息接收
     */
    public void recordMessageReceived(String subscriptionKey, long latencyMs) {
        totalMessagesReceived.increment();
        recordLatency(latencyMs);
        
        SubscriptionMetrics metrics = getOrCreateSubscriptionMetrics(subscriptionKey);
        metrics.messageReceived(latencyMs);
        
        if (log.isTraceEnabled()) {
            log.trace("记录消息接收: subscriptionKey={}, latency={}ms", subscriptionKey, latencyMs);
        }
    }
    
    /**
     * 记录消息发送
     */
    public void recordMessageSent(String subscriptionKey) {
        totalMessagesSent.increment();
        
        SubscriptionMetrics metrics = getOrCreateSubscriptionMetrics(subscriptionKey);
        metrics.messageSent();
        
        if (log.isTraceEnabled()) {
            log.trace("记录消息发送: subscriptionKey={}", subscriptionKey);
        }
    }
    
    /**
     * 记录消息处理
     */
    public void recordMessageProcessed(String subscriptionKey, long processingTimeNs) {
        totalMessageProcessingTime.addAndGet(processingTimeNs);
        
        SubscriptionMetrics metrics = getOrCreateSubscriptionMetrics(subscriptionKey);
        metrics.messageProcessed(processingTimeNs);
        
        if (log.isTraceEnabled()) {
            log.trace("记录消息处理: subscriptionKey={}, time={}μs", subscriptionKey, processingTimeNs / 1000);
        }
    }
    
    /**
     * 记录消息处理错误
     */
    public void recordMessageProcessingError(String subscriptionKey, String errorType) {
        messageProcessingErrors.increment();
        
        SubscriptionMetrics metrics = getOrCreateSubscriptionMetrics(subscriptionKey);
        metrics.processingError(errorType);
        
        log.debug("记录消息处理错误: subscriptionKey={}, errorType={}", subscriptionKey, errorType);
    }
    
    /**
     * 记录延迟
     */
    private void recordLatency(long latencyMs) {
        // 更新最小/最大延迟
        minLatency.updateAndGet(current -> Math.min(current, latencyMs));
        maxLatency.updateAndGet(current -> Math.max(current, latencyMs));
        
        // 分桶统计
        for (int i = 0; i < LATENCY_BOUNDARIES.length; i++) {
            if (latencyMs <= LATENCY_BOUNDARIES[i]) {
                latencyBuckets[i].increment();
                break;
            }
        }
        
        // 如果超过最大边界，记录到最后一个桶
        if (latencyMs > LATENCY_BOUNDARIES[LATENCY_BOUNDARIES.length - 1]) {
            latencyBuckets[LATENCY_BUCKETS - 1].increment();
        }
    }
    
    /**
     * 获取或创建订阅指标
     */
    private SubscriptionMetrics getOrCreateSubscriptionMetrics(String subscriptionKey) {
        return subscriptionMetrics.computeIfAbsent(subscriptionKey, k -> new SubscriptionMetrics(k));
    }
    
    /**
     * 创建指标快照
     */
    private void takeSnapshot() {
        MetricsSnapshot snapshot = new MetricsSnapshot(
            LocalDateTime.now(),
            totalConnections.sum(),
            activeConnections.sum(),
            connectionFailures.sum(),
            reconnections.sum(),
            totalMessagesReceived.sum(),
            totalMessagesSent.sum(),
            messageProcessingErrors.sum(),
            calculateAverageLatency(),
            calculateErrorRate()
        );
        
        // 保持历史记录大小
        if (metricsHistory.size() >= HISTORY_SIZE) {
            metricsHistory.remove(0);
        }
        metricsHistory.add(snapshot);
        
        if (log.isDebugEnabled()) {
            log.debug("创建指标快照: {}", snapshot);
        }
    }
    
    /**
     * 计算平均延迟
     */
    private double calculateAverageLatency() {
        long totalMessages = totalMessagesReceived.sum();
        if (totalMessages == 0) {
            return 0.0;
        }
        
        long weightedSum = 0;
        for (int i = 0; i < LATENCY_BOUNDARIES.length; i++) {
            long count = latencyBuckets[i].sum();
            long bucketMidpoint = i == 0 ? LATENCY_BOUNDARIES[i] / 2 : 
                                 (LATENCY_BOUNDARIES[i] + LATENCY_BOUNDARIES[i - 1]) / 2;
            weightedSum += count * bucketMidpoint;
        }
        
        return (double) weightedSum / totalMessages;
    }
    
    /**
     * 计算错误率
     */
    private double calculateErrorRate() {
        long totalMessages = totalMessagesReceived.sum();
        if (totalMessages == 0) {
            return 0.0;
        }
        
        return (double) messageProcessingErrors.sum() / totalMessages * 100.0;
    }
    
    /**
     * 获取当前指标摘要
     */
    public MetricsSummary getCurrentMetrics() {
        return new MetricsSummary(
            Duration.between(startTime, LocalDateTime.now()),
            totalConnections.sum(),
            activeConnections.sum(),
            connectionFailures.sum(),
            reconnections.sum(),
            totalMessagesReceived.sum(),
            totalMessagesSent.sum(),
            messageProcessingErrors.sum(),
            calculateAverageLatency(),
            minLatency.get() == Long.MAX_VALUE ? 0 : minLatency.get(),
            maxLatency.get(),
            calculateErrorRate(),
            subscriptionMetrics.size(),
            getLatencyDistribution()
        );
    }

    /**
     * 获取延迟分布
     */
    private Map<String, Long> getLatencyDistribution() {
        ConcurrentHashMap<String, Long> distribution = new ConcurrentHashMap<>();

        for (int i = 0; i < LATENCY_BOUNDARIES.length; i++) {
            String bucket = i == 0 ? "≤" + LATENCY_BOUNDARIES[i] + "ms" :
                           "≤" + LATENCY_BOUNDARIES[i] + "ms";
            distribution.put(bucket, latencyBuckets[i].sum());
        }

        return distribution;
    }

    /**
     * 获取订阅指标
     */
    public Map<String, SubscriptionMetrics> getSubscriptionMetrics() {
        return new ConcurrentHashMap<>(subscriptionMetrics);
    }

    /**
     * 获取历史快照
     */
    public List<MetricsSnapshot> getMetricsHistory() {
        return new ArrayList<>(metricsHistory);
    }

    /**
     * 重置所有指标
     */
    public void resetMetrics() {
        totalConnections.reset();
        connectionFailures.reset();
        reconnections.reset();
        totalMessagesReceived.reset();
        totalMessagesSent.reset();
        messageProcessingErrors.reset();
        totalConnectionTime.set(0);
        totalMessageProcessingTime.set(0);

        for (LongAdder bucket : latencyBuckets) {
            bucket.reset();
        }

        minLatency.set(Long.MAX_VALUE);
        maxLatency.set(0);

        subscriptionMetrics.clear();
        metricsHistory.clear();

        log.info("WebSocket指标已重置");
    }

    /**
     * 关闭指标收集器
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        log.info("WebSocket指标收集器已关闭");
    }

    /**
     * 订阅指标
     */
    public static class SubscriptionMetrics {
        private final String subscriptionKey;
        private final LocalDateTime createdTime;
        private final LongAdder connectionsEstablished = new LongAdder();
        private final LongAdder connectionsClosed = new LongAdder();
        private final LongAdder connectionsFailed = new LongAdder();
        private final LongAdder reconnections = new LongAdder();
        private final LongAdder messagesReceived = new LongAdder();
        private final LongAdder messagesSent = new LongAdder();
        private final LongAdder processingErrors = new LongAdder();
        private final AtomicLong totalConnectionTime = new AtomicLong(0);
        private final AtomicLong totalProcessingTime = new AtomicLong(0);
        private final AtomicLong totalLatency = new AtomicLong(0);
        private volatile LocalDateTime lastMessageTime;

        public SubscriptionMetrics(String subscriptionKey) {
            this.subscriptionKey = subscriptionKey;
            this.createdTime = LocalDateTime.now();
        }

        public void connectionEstablished() {
            connectionsEstablished.increment();
        }

        public void connectionClosed(long durationMs) {
            connectionsClosed.increment();
            totalConnectionTime.addAndGet(durationMs);
        }

        public void connectionFailed(String reason) {
            connectionsFailed.increment();
        }

        public void reconnected() {
            reconnections.increment();
        }

        public void messageReceived(long latencyMs) {
            messagesReceived.increment();
            totalLatency.addAndGet(latencyMs);
            lastMessageTime = LocalDateTime.now();
        }

        public void messageSent() {
            messagesSent.increment();
        }

        public void messageProcessed(long processingTimeNs) {
            totalProcessingTime.addAndGet(processingTimeNs);
        }

        public void processingError(String errorType) {
            processingErrors.increment();
        }

        // Getters
        public String getSubscriptionKey() { return subscriptionKey; }
        public LocalDateTime getCreatedTime() { return createdTime; }
        public long getConnectionsEstablished() { return connectionsEstablished.sum(); }
        public long getConnectionsClosed() { return connectionsClosed.sum(); }
        public long getConnectionsFailed() { return connectionsFailed.sum(); }
        public long getReconnections() { return reconnections.sum(); }
        public long getMessagesReceived() { return messagesReceived.sum(); }
        public long getMessagesSent() { return messagesSent.sum(); }
        public long getProcessingErrors() { return processingErrors.sum(); }
        public LocalDateTime getLastMessageTime() { return lastMessageTime; }

        public double getAverageLatency() {
            long messages = messagesReceived.sum();
            return messages > 0 ? (double) totalLatency.get() / messages : 0.0;
        }

        public double getAverageProcessingTime() {
            long messages = messagesReceived.sum();
            return messages > 0 ? (double) totalProcessingTime.get() / messages / 1000.0 : 0.0; // μs
        }

        public double getErrorRate() {
            long messages = messagesReceived.sum();
            return messages > 0 ? (double) processingErrors.sum() / messages * 100.0 : 0.0;
        }

        @Override
        public String toString() {
            return String.format("SubscriptionMetrics{key=%s, messages=%d, avgLatency=%.2fms, " +
                               "avgProcessing=%.2fμs, errors=%d(%.2f%%)}",
                               subscriptionKey, getMessagesReceived(), getAverageLatency(),
                               getAverageProcessingTime(), getProcessingErrors(), getErrorRate());
        }
    }

    /**
     * 指标摘要
     */
    public static class MetricsSummary {
        private final Duration uptime;
        private final long totalConnections;
        private final long activeConnections;
        private final long connectionFailures;
        private final long reconnections;
        private final long totalMessagesReceived;
        private final long totalMessagesSent;
        private final long messageProcessingErrors;
        private final double averageLatency;
        private final long minLatency;
        private final long maxLatency;
        private final double errorRate;
        private final int activeSubscriptions;
        private final Map<String, Long> latencyDistribution;

        public MetricsSummary(Duration uptime, long totalConnections, long activeConnections,
                            long connectionFailures, long reconnections, long totalMessagesReceived,
                            long totalMessagesSent, long messageProcessingErrors, double averageLatency,
                            long minLatency, long maxLatency, double errorRate, int activeSubscriptions,
                            Map<String, Long> latencyDistribution) {
            this.uptime = uptime;
            this.totalConnections = totalConnections;
            this.activeConnections = activeConnections;
            this.connectionFailures = connectionFailures;
            this.reconnections = reconnections;
            this.totalMessagesReceived = totalMessagesReceived;
            this.totalMessagesSent = totalMessagesSent;
            this.messageProcessingErrors = messageProcessingErrors;
            this.averageLatency = averageLatency;
            this.minLatency = minLatency;
            this.maxLatency = maxLatency;
            this.errorRate = errorRate;
            this.activeSubscriptions = activeSubscriptions;
            this.latencyDistribution = latencyDistribution;
        }

        // Getters
        public Duration getUptime() { return uptime; }
        public long getTotalConnections() { return totalConnections; }
        public long getActiveConnections() { return activeConnections; }
        public long getConnectionFailures() { return connectionFailures; }
        public long getReconnections() { return reconnections; }
        public long getTotalMessagesReceived() { return totalMessagesReceived; }
        public long getTotalMessagesSent() { return totalMessagesSent; }
        public long getMessageProcessingErrors() { return messageProcessingErrors; }
        public double getAverageLatency() { return averageLatency; }
        public long getMinLatency() { return minLatency; }
        public long getMaxLatency() { return maxLatency; }
        public double getErrorRate() { return errorRate; }
        public int getActiveSubscriptions() { return activeSubscriptions; }
        public Map<String, Long> getLatencyDistribution() { return latencyDistribution; }

        public double getConnectionSuccessRate() {
            return totalConnections > 0 ?
                (double) (totalConnections - connectionFailures) / totalConnections * 100.0 : 0.0;
        }

        @Override
        public String toString() {
            return String.format("MetricsSummary{uptime=%s, connections=%d(active=%d), " +
                               "messages=%d, avgLatency=%.2fms, errorRate=%.2f%%, subscriptions=%d}",
                               uptime, totalConnections, activeConnections, totalMessagesReceived,
                               averageLatency, errorRate, activeSubscriptions);
        }
    }

    /**
     * 指标快照
     */
    public static class MetricsSnapshot {
        private final LocalDateTime timestamp;
        private final long totalConnections;
        private final long activeConnections;
        private final long connectionFailures;
        private final long reconnections;
        private final long totalMessagesReceived;
        private final long totalMessagesSent;
        private final long messageProcessingErrors;
        private final double averageLatency;
        private final double errorRate;

        public MetricsSnapshot(LocalDateTime timestamp, long totalConnections, long activeConnections,
                             long connectionFailures, long reconnections, long totalMessagesReceived,
                             long totalMessagesSent, long messageProcessingErrors, double averageLatency,
                             double errorRate) {
            this.timestamp = timestamp;
            this.totalConnections = totalConnections;
            this.activeConnections = activeConnections;
            this.connectionFailures = connectionFailures;
            this.reconnections = reconnections;
            this.totalMessagesReceived = totalMessagesReceived;
            this.totalMessagesSent = totalMessagesSent;
            this.messageProcessingErrors = messageProcessingErrors;
            this.averageLatency = averageLatency;
            this.errorRate = errorRate;
        }

        // Getters
        public LocalDateTime getTimestamp() { return timestamp; }
        public long getTotalConnections() { return totalConnections; }
        public long getActiveConnections() { return activeConnections; }
        public long getConnectionFailures() { return connectionFailures; }
        public long getReconnections() { return reconnections; }
        public long getTotalMessagesReceived() { return totalMessagesReceived; }
        public long getTotalMessagesSent() { return totalMessagesSent; }
        public long getMessageProcessingErrors() { return messageProcessingErrors; }
        public double getAverageLatency() { return averageLatency; }
        public double getErrorRate() { return errorRate; }

        @Override
        public String toString() {
            return String.format("MetricsSnapshot{time=%s, connections=%d(active=%d), " +
                               "messages=%d, avgLatency=%.2fms, errorRate=%.2f%%}",
                               timestamp, totalConnections, activeConnections, totalMessagesReceived,
                               averageLatency, errorRate);
        }
    }
}
