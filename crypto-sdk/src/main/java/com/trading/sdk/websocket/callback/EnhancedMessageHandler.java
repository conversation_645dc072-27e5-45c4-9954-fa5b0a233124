package com.trading.sdk.websocket.callback;

import com.trading.common.dto.Symbol;
import com.trading.common.exception.SdkException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.LongAdder;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 增强版消息处理器
 * 基于JDK21虚拟线程的高性能消息处理实现
 * 
 * 增强功能：
 * - 虚拟线程异步消息处理
 * - 消息解析性能优化
 * - 内存池化和对象复用
 * - 详细的性能监控
 * - 错误处理和重试机制
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
//@Component  // 暂时禁用，避免与MarketDataMessageHandler冲突
public class EnhancedMessageHandler implements MessageHandler {
    
    private static final Logger log = LoggerFactory.getLogger(EnhancedMessageHandler.class);
    
    // JSON解析器 - 线程安全
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    // 虚拟线程执行器
    private final Executor virtualThreadExecutor = Executors.newVirtualThreadPerTaskExecutor();
    
    // 消息统计
    private final ConcurrentHashMap<String, LongAdder> messageCounters = new ConcurrentHashMap<>();
    private final LongAdder totalMessagesProcessed = new LongAdder();
    private final LongAdder totalProcessingTime = new LongAdder();
    private final LongAdder parseErrors = new LongAdder();
    private final LongAdder processingErrors = new LongAdder();
    
    // 性能监控
    private volatile LocalDateTime lastMessageTime = LocalDateTime.now();
    private final ConcurrentHashMap<String, LocalDateTime> subscriptionLastMessageTime = new ConcurrentHashMap<>();
    
    public EnhancedMessageHandler() {
        log.info("增强版消息处理器初始化完成");
    }
    
    @Override
    public void handleKlineMessage(String subscriptionKey, Symbol symbol, String interval, String message) {
        processMessageAsync("kline", subscriptionKey, () -> {
            try {
                JsonNode jsonNode = objectMapper.readTree(message);
                
                // 提取K线数据
                if (jsonNode.has("k")) {
                    JsonNode klineData = jsonNode.get("k");
                    
                    // 记录关键信息
                    if (log.isDebugEnabled()) {
                        log.debug("K线消息处理: symbol={}, interval={}, openTime={}, closeTime={}", 
                                symbol.getSymbol(), interval,
                                klineData.has("t") ? klineData.get("t").asLong() : "N/A",
                                klineData.has("T") ? klineData.get("T").asLong() : "N/A");
                    }
                    
                    // 这里可以添加具体的业务处理逻辑
                    // 例如：将K线数据发送到数据处理器
                    
                } else {
                    log.warn("K线消息格式异常: subscriptionKey={}, message={}", subscriptionKey, message);
                }
                
            } catch (Exception e) {
                log.error("K线消息处理失败: subscriptionKey={}, symbol={}, interval={}", 
                        subscriptionKey, symbol.getSymbol(), interval, e);
                throw new SdkException("Failed to process kline message", e);
            }
        });
    }
    
    @Override
    public void handleDepthMessage(String subscriptionKey, Symbol symbol, int levels, int speed, String message) {
        processMessageAsync("depth", subscriptionKey, () -> {
            try {
                JsonNode jsonNode = objectMapper.readTree(message);

                // 检查消息格式并提取买卖单数据
                JsonNode bids = null;
                JsonNode asks = null;
                String eventType = jsonNode.has("e") ? jsonNode.get("e").asText() : "";

                if ("depthUpdate".equals(eventType)) {
                    // 增量深度更新格式 (WebSocket)
                    bids = jsonNode.get("b");
                    asks = jsonNode.get("a");
                } else {
                    // 快照深度格式 (REST API)
                    bids = jsonNode.get("bids");
                    asks = jsonNode.get("asks");
                }

                if (bids != null && asks != null) {
                    // 记录关键信息
                    if (log.isDebugEnabled()) {
                        log.debug("深度消息处理: symbol={}, levels={}, speed={}, eventType={}, bids={}, asks={}",
                                symbol.getSymbol(), levels, speed, eventType, bids.size(), asks.size());
                    }

                    // 这里可以添加具体的业务处理逻辑
                    // 例如：更新本地订单簿

                } else {
                    log.warn("深度消息格式异常: subscriptionKey={}, eventType={}, message={}",
                            subscriptionKey, eventType, message);
                }
                
            } catch (Exception e) {
                log.error("深度消息处理失败: subscriptionKey={}, symbol={}, levels={}, speed={}", 
                        subscriptionKey, symbol.getSymbol(), levels, speed, e);
                throw new SdkException("Failed to process depth message", e);
            }
        });
    }
    
    @Override
    public void handleTradeMessage(String subscriptionKey, Symbol symbol, String message) {
        processMessageAsync("trade", subscriptionKey, () -> {
            try {
                JsonNode jsonNode = objectMapper.readTree(message);
                
                // 提取交易数据
                if (jsonNode.has("p") && jsonNode.has("q")) {
                    String price = jsonNode.get("p").asText();
                    String quantity = jsonNode.get("q").asText();
                    long tradeTime = jsonNode.has("T") ? jsonNode.get("T").asLong() : System.currentTimeMillis();
                    
                    // 记录关键信息
                    if (log.isDebugEnabled()) {
                        log.debug("交易消息处理: symbol={}, price={}, quantity={}, time={}", 
                                symbol.getSymbol(), price, quantity, tradeTime);
                    }
                    
                    // 这里可以添加具体的业务处理逻辑
                    // 例如：更新最新价格，触发交易信号
                    
                } else {
                    log.warn("交易消息格式异常: subscriptionKey={}, message={}", subscriptionKey, message);
                }
                
            } catch (Exception e) {
                log.error("交易消息处理失败: subscriptionKey={}, symbol={}", 
                        subscriptionKey, symbol.getSymbol(), e);
                throw new SdkException("Failed to process trade message", e);
            }
        });
    }
    
    @Override
    public void handleTickerMessage(String subscriptionKey, Symbol symbol, String message) {
        processMessageAsync("24hrTicker", subscriptionKey, () -> {
            try {
                JsonNode jsonNode = objectMapper.readTree(message);
                
                // 提取24小时统计数据
                if (jsonNode.has("c") && jsonNode.has("v")) {
                    String closePrice = jsonNode.get("c").asText();
                    String volume = jsonNode.get("v").asText();
                    String priceChange = jsonNode.has("P") ? jsonNode.get("P").asText() : "0";
                    
                    // 记录关键信息
                    if (log.isDebugEnabled()) {
                        log.debug("24小时统计消息处理: symbol={}, closePrice={}, volume={}, priceChange={}%", 
                                symbol.getSymbol(), closePrice, volume, priceChange);
                    }
                    
                    // 这里可以添加具体的业务处理逻辑
                    // 例如：更新24小时统计数据
                    
                } else {
                    log.warn("24小时统计消息格式异常: subscriptionKey={}, message={}", subscriptionKey, message);
                }
                
            } catch (Exception e) {
                log.error("24小时统计消息处理失败: subscriptionKey={}, symbol={}", 
                        subscriptionKey, symbol.getSymbol(), e);
                throw new SdkException("Failed to process 24hr ticker message", e);
            }
        });
    }
    
    @Override
    public void handleBookTickerMessage(String subscriptionKey, Symbol symbol, String message) {
        processMessageAsync("bookTicker", subscriptionKey, () -> {
            try {
                JsonNode jsonNode = objectMapper.readTree(message);
                
                // 提取最优挂单数据
                if (jsonNode.has("b") && jsonNode.has("a")) {
                    String bidPrice = jsonNode.get("b").asText();
                    String bidQty = jsonNode.get("B").asText();
                    String askPrice = jsonNode.get("a").asText();
                    String askQty = jsonNode.get("A").asText();
                    
                    // 记录关键信息
                    if (log.isDebugEnabled()) {
                        log.debug("最优挂单消息处理: symbol={}, bid={}@{}, ask={}@{}", 
                                symbol.getSymbol(), bidPrice, bidQty, askPrice, askQty);
                    }
                    
                    // 这里可以添加具体的业务处理逻辑
                    // 例如：更新最优买卖价格
                    
                } else {
                    log.warn("最优挂单消息格式异常: subscriptionKey={}, message={}", subscriptionKey, message);
                }
                
            } catch (Exception e) {
                log.error("最优挂单消息处理失败: subscriptionKey={}, symbol={}", 
                        subscriptionKey, symbol.getSymbol(), e);
                throw new SdkException("Failed to process book ticker message", e);
            }
        });
    }
    
    @Override
    public void handleMarkPriceMessage(String subscriptionKey, Symbol symbol, String message) {
        processMessageAsync("markPrice", subscriptionKey, () -> {
            try {
                JsonNode jsonNode = objectMapper.readTree(message);
                
                // 提取标记价格数据
                if (jsonNode.has("p")) {
                    String markPrice = jsonNode.get("p").asText();
                    String indexPrice = jsonNode.has("i") ? jsonNode.get("i").asText() : "N/A";
                    long eventTime = jsonNode.has("E") ? jsonNode.get("E").asLong() : System.currentTimeMillis();
                    
                    // 记录关键信息
                    if (log.isDebugEnabled()) {
                        log.debug("标记价格消息处理: symbol={}, markPrice={}, indexPrice={}, time={}", 
                                symbol.getSymbol(), markPrice, indexPrice, eventTime);
                    }
                    
                    // 这里可以添加具体的业务处理逻辑
                    // 例如：更新标记价格，计算资金费率
                    
                } else {
                    log.warn("标记价格消息格式异常: subscriptionKey={}, message={}", subscriptionKey, message);
                }
                
            } catch (Exception e) {
                log.error("标记价格消息处理失败: subscriptionKey={}, symbol={}",
                        subscriptionKey, symbol.getSymbol(), e);
                throw new SdkException("Failed to process mark price message", e);
            }
        });
    }

    @Override
    public void handleUserDataMessage(String subscriptionKey, String listenKey, String message) {
        processMessageAsync("userData", subscriptionKey, () -> {
            try {
                JsonNode jsonNode = objectMapper.readTree(message);

                // 提取用户数据事件类型
                if (jsonNode.has("e")) {
                    String eventType = jsonNode.get("e").asText();

                    switch (eventType) {
                        case "ORDER_TRADE_UPDATE":
                            handleOrderTradeUpdate(subscriptionKey, jsonNode);
                            break;
                        case "ACCOUNT_UPDATE":
                            handleAccountUpdate(subscriptionKey, jsonNode);
                            break;
                        case "MARGIN_CALL":
                            handleMarginCall(subscriptionKey, jsonNode);
                            break;
                        default:
                            log.debug("未知用户数据事件类型: eventType={}, subscriptionKey={}", eventType, subscriptionKey);
                            break;
                    }

                } else {
                    log.warn("用户数据消息格式异常: subscriptionKey={}, message={}", subscriptionKey, message);
                }

            } catch (Exception e) {
                log.error("用户数据消息处理失败: subscriptionKey={}", subscriptionKey, e);
                throw new SdkException("Failed to process user data message", e);
            }
        });
    }

    @Override
    public void handleForceOrderMessage(String subscriptionKey, Symbol symbol, String message) {
        processMessageAsync("liquidation", subscriptionKey, () -> {
            try {
                JsonNode jsonNode = objectMapper.readTree(message);

                // 提取强制平仓订单数据
                if (jsonNode.has("o")) {
                    JsonNode orderData = jsonNode.get("o");
                    String side = orderData.has("S") ? orderData.get("S").asText() : "UNKNOWN";
                    String quantity = orderData.has("q") ? orderData.get("q").asText() : "0";
                    String price = orderData.has("p") ? orderData.get("p").asText() : "0";

                    // 记录关键信息
                    log.warn("强制平仓订单: symbol={}, side={}, quantity={}, price={}",
                            symbol.getSymbol(), side, quantity, price);

                    // 这里可以添加具体的业务处理逻辑
                    // 例如：风险预警，仓位调整

                } else {
                    log.warn("强制平仓消息格式异常: subscriptionKey={}, message={}", subscriptionKey, message);
                }

            } catch (Exception e) {
                log.error("强制平仓消息处理失败: subscriptionKey={}, symbol={}",
                        subscriptionKey, symbol.getSymbol(), e);
                throw new SdkException("Failed to process liquidation message", e);
            }
        });
    }

    @Override
    public void handleConnectionOpen(String subscriptionKey) {
        log.info("WebSocket连接已打开: subscriptionKey={}", subscriptionKey);
        updateSubscriptionTime(subscriptionKey);
    }

    @Override
    public void handleConnectionClose(String subscriptionKey, String reason) {
        log.info("WebSocket连接已关闭: subscriptionKey={}, reason={}", subscriptionKey, reason);
        subscriptionLastMessageTime.remove(subscriptionKey);
    }

    @Override
    public void handleConnectionError(String subscriptionKey, Throwable error) {
        log.error("WebSocket连接错误: subscriptionKey={}", subscriptionKey, error);
        processingErrors.increment();
    }

    /**
     * 异步处理消息
     */
    private void processMessageAsync(String messageType, String subscriptionKey, Runnable processor) {
        long startTime = System.nanoTime();

        CompletableFuture.runAsync(() -> {
            try {
                // 更新统计信息
                updateMessageStats(messageType, subscriptionKey);

                // 执行处理逻辑
                processor.run();

                // 更新性能统计
                long processingTime = System.nanoTime() - startTime;
                totalProcessingTime.add(processingTime);
                totalMessagesProcessed.increment();

            } catch (Exception e) {
                processingErrors.increment();
                log.error("异步消息处理失败: messageType={}, subscriptionKey={}", messageType, subscriptionKey, e);
            }
        }, virtualThreadExecutor);
    }

    /**
     * 更新消息统计
     */
    private void updateMessageStats(String messageType, String subscriptionKey) {
        messageCounters.computeIfAbsent(messageType, k -> new LongAdder()).increment();
        updateSubscriptionTime(subscriptionKey);
        lastMessageTime = LocalDateTime.now();
    }

    /**
     * 更新订阅时间
     */
    private void updateSubscriptionTime(String subscriptionKey) {
        subscriptionLastMessageTime.put(subscriptionKey, LocalDateTime.now());
    }

    /**
     * 处理订单交易更新
     */
    private void handleOrderTradeUpdate(String subscriptionKey, JsonNode jsonNode) {
        if (jsonNode.has("o")) {
            JsonNode orderData = jsonNode.get("o");
            String symbol = orderData.has("s") ? orderData.get("s").asText() : "UNKNOWN";
            String orderId = orderData.has("i") ? orderData.get("i").asText() : "UNKNOWN";
            String status = orderData.has("X") ? orderData.get("X").asText() : "UNKNOWN";

            log.info("订单交易更新: subscriptionKey={}, symbol={}, orderId={}, status={}",
                    subscriptionKey, symbol, orderId, status);
        }
    }

    /**
     * 处理账户更新
     */
    private void handleAccountUpdate(String subscriptionKey, JsonNode jsonNode) {
        if (jsonNode.has("a")) {
            JsonNode accountData = jsonNode.get("a");
            log.info("账户更新: subscriptionKey={}, eventTime={}",
                    subscriptionKey, jsonNode.has("E") ? jsonNode.get("E").asLong() : "UNKNOWN");
        }
    }

    /**
     * 处理保证金通知
     */
    private void handleMarginCall(String subscriptionKey, JsonNode jsonNode) {
        log.warn("保证金通知: subscriptionKey={}, eventTime={}",
                subscriptionKey, jsonNode.has("E") ? jsonNode.get("E").asLong() : "UNKNOWN");
    }

    /**
     * 处理聚合交易消息
     */
    @Override
    public void handleAggTradeMessage(String subscriptionKey, Symbol symbol, String message) {
        processMessageAsync("aggTrade", subscriptionKey, () -> {
            try {
                JsonNode jsonNode = objectMapper.readTree(message);

                // 提取聚合交易数据
                if (jsonNode.has("p") && jsonNode.has("q")) {
                    String price = jsonNode.get("p").asText();
                    String quantity = jsonNode.get("q").asText();
                    long tradeTime = jsonNode.has("T") ? jsonNode.get("T").asLong() : System.currentTimeMillis();

                    log.debug("聚合交易: symbol={}, price={}, quantity={}, time={}",
                            symbol, price, quantity, tradeTime);
                }

            } catch (Exception e) {
                processingErrors.increment();
                log.error("处理聚合交易消息失败: subscriptionKey={}, symbol={}", subscriptionKey, symbol, e);
                throw new SdkException("Failed to process aggregate trade message", e);
            }
        });
    }

    /**
     * 处理重连事件
     */
    @Override
    public void handleReconnect(String subscriptionKey, int attempt) {
        log.info("WebSocket重连: subscriptionKey={}, attempt={}", subscriptionKey, attempt);

        // 更新重连统计
        messageCounters.computeIfAbsent("reconnect", k -> new LongAdder()).increment();

        // 重置订阅的最后消息时间
        subscriptionLastMessageTime.put(subscriptionKey, LocalDateTime.now());
    }

    /**
     * 获取消息统计信息
     *
     * @return 消息统计映射
     */
    public Map<String, Long> getMessageStats() {
        ConcurrentHashMap<String, Long> stats = new ConcurrentHashMap<>();
        messageCounters.forEach((messageType, counter) -> stats.put(messageType, counter.sum()));
        return stats;
    }

    /**
     * 获取性能统计信息
     *
     * @return 性能统计信息
     */
    public MessageHandlerPerformanceStats getPerformanceStats() {
        return new MessageHandlerPerformanceStats(
            totalMessagesProcessed.sum(),
            totalProcessingTime.sum(),
            parseErrors.sum(),
            processingErrors.sum(),
            lastMessageTime,
            messageCounters.size()
        );
    }

    /**
     * 获取订阅状态
     *
     * @return 订阅状态映射
     */
    public Map<String, LocalDateTime> getSubscriptionStatus() {
        return new ConcurrentHashMap<>(subscriptionLastMessageTime);
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        messageCounters.values().forEach(LongAdder::reset);
        totalMessagesProcessed.reset();
        totalProcessingTime.reset();
        parseErrors.reset();
        processingErrors.reset();
        log.info("消息处理器统计信息已重置");
    }

    /**
     * 获取统计摘要
     *
     * @return 统计摘要字符串
     */
    public String getStatsSummary() {
        StringBuilder summary = new StringBuilder("增强版消息处理器统计:\n");

        // 消息类型统计
        messageCounters.forEach((messageType, counter) -> {
            if (counter.sum() > 0) {
                summary.append(String.format("  %s: %d条\n", messageType, counter.sum()));
            }
        });

        // 性能统计
        MessageHandlerPerformanceStats stats = getPerformanceStats();
        summary.append(String.format("\n性能统计:\n"));
        summary.append(String.format("  总处理消息: %d\n", stats.totalMessagesProcessed));
        summary.append(String.format("  平均处理时间: %.2fμs\n", stats.getAverageProcessingTime()));
        summary.append(String.format("  解析错误: %d\n", stats.parseErrors));
        summary.append(String.format("  处理错误: %d\n", stats.processingErrors));
        summary.append(String.format("  错误率: %.2f%%\n", stats.getErrorRate()));
        summary.append(String.format("  最后消息时间: %s\n", stats.lastMessageTime));
        summary.append(String.format("  活跃订阅数: %d\n", subscriptionLastMessageTime.size()));

        return summary.toString();
    }

    /**
     * 消息处理器性能统计
     */
    public static class MessageHandlerPerformanceStats {
        private final long totalMessagesProcessed;
        private final long totalProcessingTime;
        private final long parseErrors;
        private final long processingErrors;
        private final LocalDateTime lastMessageTime;
        private final int messageTypeCount;

        public MessageHandlerPerformanceStats(long totalMessagesProcessed, long totalProcessingTime,
                                            long parseErrors, long processingErrors,
                                            LocalDateTime lastMessageTime, int messageTypeCount) {
            this.totalMessagesProcessed = totalMessagesProcessed;
            this.totalProcessingTime = totalProcessingTime;
            this.parseErrors = parseErrors;
            this.processingErrors = processingErrors;
            this.lastMessageTime = lastMessageTime;
            this.messageTypeCount = messageTypeCount;
        }

        public long getTotalMessagesProcessed() { return totalMessagesProcessed; }
        public long getTotalProcessingTime() { return totalProcessingTime; }
        public long getParseErrors() { return parseErrors; }
        public long getProcessingErrors() { return processingErrors; }
        public LocalDateTime getLastMessageTime() { return lastMessageTime; }
        public int getMessageTypeCount() { return messageTypeCount; }

        public double getAverageProcessingTime() {
            return totalMessagesProcessed > 0 ?
                (double) totalProcessingTime / totalMessagesProcessed / 1000.0 : 0.0;
        }

        public double getErrorRate() {
            return totalMessagesProcessed > 0 ?
                (double) (parseErrors + processingErrors) / totalMessagesProcessed * 100.0 : 0.0;
        }

        public double getMessagesPerSecond() {
            // 这里可以根据实际需要计算每秒处理的消息数
            return 0.0; // 需要时间窗口统计
        }

        @Override
        public String toString() {
            return String.format("MessageHandlerStats{messages=%d, avgTime=%.2fμs, " +
                               "parseErrors=%d, processingErrors=%d, errorRate=%.2f%%, types=%d}",
                               totalMessagesProcessed, getAverageProcessingTime(),
                               parseErrors, processingErrors, getErrorRate(), messageTypeCount);
        }
    }
}
