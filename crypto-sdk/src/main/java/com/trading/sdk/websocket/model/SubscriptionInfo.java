package com.trading.sdk.websocket.model;

import com.trading.common.enums.StreamType;
import com.trading.common.dto.Symbol;
import lombok.Builder;
import lombok.Data;

/**
 * WebSocket订阅信息
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Data
@Builder
public class SubscriptionInfo {
    
    /**
     * 订阅键（唯一标识）
     */
    private String subscriptionKey;
    
    /**
     * 连接ID
     */
    private int connectionId;
    
    /**
     * 交易对
     */
    private Symbol symbol;
    
    /**
     * 时间间隔或其他参数
     */
    private String interval;
    
    /**
     * 流类型（UM/CM期货）
     */
    private StreamType streamType;
    
    /**
     * 数据类型（K线、深度等）
     */
    private StreamType dataType;
    
    /**
     * 是否活跃
     */
    private boolean active;
    
    /**
     * 创建时间
     */
    private long createdAt;
    
    /**
     * 最后活跃时间
     */
    private long lastActiveAt;
    
    /**
     * 重连次数
     */
    private int reconnectCount;
    
    /**
     * 接收消息数量
     */
    private long messageCount;
    
    /**
     * 错误次数
     */
    private int errorCount;
    
    /**
     * 更新活跃状态
     */
    public void setActive(boolean active) {
        this.active = active;
        if (active) {
            this.lastActiveAt = System.currentTimeMillis();
        }
    }
    
    /**
     * 增加消息计数
     */
    public void incrementMessageCount() {
        this.messageCount++;
        this.lastActiveAt = System.currentTimeMillis();
    }
    
    /**
     * 增加错误计数
     */
    public void incrementErrorCount() {
        this.errorCount++;
    }
    
    /**
     * 增加重连计数
     */
    public void incrementReconnectCount() {
        this.reconnectCount++;
    }
    
    /**
     * 获取订阅持续时间（毫秒）
     */
    public long getDuration() {
        return System.currentTimeMillis() - createdAt;
    }
    
    /**
     * 获取空闲时间（毫秒）
     */
    public long getIdleTime() {
        return lastActiveAt > 0 ? System.currentTimeMillis() - lastActiveAt : getDuration();
    }
    
    /**
     * 检查是否超时
     */
    public boolean isTimeout(long timeoutMs) {
        return getIdleTime() > timeoutMs;
    }
    
    /**
     * 获取订阅统计信息
     */
    public String getStats() {
        return String.format("SubscriptionInfo{key='%s', connectionId=%d, active=%s, " +
                "duration=%dms, messages=%d, errors=%d, reconnects=%d}", 
                subscriptionKey, connectionId, active, getDuration(), 
                messageCount, errorCount, reconnectCount);
    }
}
