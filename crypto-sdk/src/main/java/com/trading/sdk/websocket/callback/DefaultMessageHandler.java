package com.trading.sdk.websocket.callback;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.trading.common.dto.Symbol;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * 默认WebSocket消息处理器实现
 * 提供基础的消息处理和日志记录功能
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component  // 暂时禁用，避免与MarketDataMessageHandler冲突
public class DefaultMessageHandler implements MessageHandler {
    
    private static final Logger log = LoggerFactory.getLogger(DefaultMessageHandler.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public void handleKlineMessage(String subscriptionKey, Symbol symbol, String interval, String message) {
        try {
            log.debug("收到K线消息: key={}, symbol={}, interval={}", subscriptionKey, symbol.getSymbol(), interval);
            
            JsonNode jsonNode = objectMapper.readTree(message);
            if (jsonNode.has("k")) {
                JsonNode klineData = jsonNode.get("k");
                String symbolName = klineData.get("s").asText();
                String openTime = klineData.get("t").asText();
                String closeTime = klineData.get("T").asText();
                String open = klineData.get("o").asText();
                String high = klineData.get("h").asText();
                String low = klineData.get("l").asText();
                String close = klineData.get("c").asText();
                String volume = klineData.get("v").asText();
                boolean isClosed = klineData.get("x").asBoolean();
                
                log.debug("K线数据: symbol={}, interval={}, open={}, high={}, low={}, close={}, volume={}, closed={}", 
                        symbolName, interval, open, high, low, close, volume, isClosed);
                
                // 这里可以添加具体的业务处理逻辑
                // 例如：发送到消息队列、存储到数据库、触发策略等
                
            } else {
                log.warn("K线消息格式异常: {}", message);
            }
            
        } catch (Exception e) {
            log.error("处理K线消息失败: key={}, message={}", subscriptionKey, message, e);
        }
    }
    
    @Override
    public void handleDepthMessage(String subscriptionKey, Symbol symbol, int levels, int speed, String message) {
        try {
            log.debug("收到深度消息: key={}, symbol={}, levels={}, speed={}ms",
                    subscriptionKey, symbol.getSymbol(), levels, speed);

            JsonNode jsonNode = objectMapper.readTree(message);

            // 检查消息格式并提取买卖单数据
            JsonNode bids = null;
            JsonNode asks = null;
            String eventType = jsonNode.has("e") ? jsonNode.get("e").asText() : "";
            Long lastUpdateId = null;

            if ("depthUpdate".equals(eventType)) {
                // 增量深度更新格式 (WebSocket)
                bids = jsonNode.get("b");
                asks = jsonNode.get("a");
                lastUpdateId = jsonNode.has("u") ? jsonNode.get("u").asLong() : null;
            } else {
                // 快照深度格式 (REST API)
                bids = jsonNode.get("bids");
                asks = jsonNode.get("asks");
                lastUpdateId = jsonNode.has("lastUpdateId") ? jsonNode.get("lastUpdateId").asLong() : null;
            }

            if (bids != null && asks != null) {
                log.debug("深度数据: symbol={}, eventType={}, lastUpdateId={}, bids={}, asks={}",
                        symbol.getSymbol(), eventType, lastUpdateId, bids.size(), asks.size());

                // 这里可以添加具体的业务处理逻辑
                // 例如：更新本地订单簿、计算价差、触发套利策略等

            } else {
                log.warn("深度消息格式异常: eventType={}, message={}", eventType, message);
            }
            
        } catch (Exception e) {
            log.error("处理深度消息失败: key={}, message={}", subscriptionKey, message, e);
        }
    }
    
    @Override
    public void handleTradeMessage(String subscriptionKey, Symbol symbol, String message) {
        try {
            log.debug("收到交易消息: key={}, symbol={}", subscriptionKey, symbol.getSymbol());
            
            JsonNode jsonNode = objectMapper.readTree(message);
            if (jsonNode.has("p") && jsonNode.has("q")) {
                String price = jsonNode.get("p").asText();
                String quantity = jsonNode.get("q").asText();
                long tradeTime = jsonNode.get("T").asLong();
                boolean isBuyerMaker = jsonNode.get("m").asBoolean();
                
                log.debug("交易数据: symbol={}, price={}, quantity={}, time={}, buyerMaker={}", 
                        symbol.getSymbol(), price, quantity, tradeTime, isBuyerMaker);
                
                // 这里可以添加具体的业务处理逻辑
                
            } else {
                log.warn("交易消息格式异常: {}", message);
            }
            
        } catch (Exception e) {
            log.error("处理交易消息失败: key={}, message={}", subscriptionKey, message, e);
        }
    }

    @Override
    public void handleTickerMessage(String subscriptionKey, Symbol symbol, String message) {
        try {
            log.debug("收到24小时统计消息: key={}, symbol={}", subscriptionKey, symbol.getSymbol());

            JsonNode jsonNode = objectMapper.readTree(message);
            if (jsonNode.has("c") && jsonNode.has("o")) {
                String closePrice = jsonNode.get("c").asText();
                String openPrice = jsonNode.get("o").asText();
                String highPrice = jsonNode.get("h").asText();
                String lowPrice = jsonNode.get("l").asText();
                String volume = jsonNode.get("v").asText();
                String priceChange = jsonNode.get("P").asText();

                log.debug("24小时统计数据: symbol={}, close={}, open={}, high={}, low={}, volume={}, change={}%",
                        symbol.getSymbol(), closePrice, openPrice, highPrice, lowPrice, volume, priceChange);

                // 这里可以添加具体的业务处理逻辑

            } else {
                log.warn("24小时统计消息格式异常: {}", message);
            }

        } catch (Exception e) {
            log.error("处理24小时统计消息失败: key={}, message={}", subscriptionKey, message, e);
        }
    }

    @Override
    public void handleBookTickerMessage(String subscriptionKey, Symbol symbol, String message) {
        try {
            log.debug("收到最优挂单消息: key={}, symbol={}", subscriptionKey, symbol.getSymbol());

            JsonNode jsonNode = objectMapper.readTree(message);
            if (jsonNode.has("b") && jsonNode.has("a")) {
                String bidPrice = jsonNode.get("b").asText();
                String bidQty = jsonNode.get("B").asText();
                String askPrice = jsonNode.get("a").asText();
                String askQty = jsonNode.get("A").asText();

                log.debug("最优挂单数据: symbol={}, bid={}@{}, ask={}@{}",
                        symbol.getSymbol(), bidPrice, bidQty, askPrice, askQty);

                // 这里可以添加具体的业务处理逻辑

            } else {
                log.warn("最优挂单消息格式异常: {}", message);
            }

        } catch (Exception e) {
            log.error("处理最优挂单消息失败: key={}, message={}", subscriptionKey, message, e);
        }
    }

    @Override
    public void handleMarkPriceMessage(String subscriptionKey, Symbol symbol, String message) {
        try {
            log.debug("收到标记价格消息: key={}, symbol={}", subscriptionKey, symbol.getSymbol());

            JsonNode jsonNode = objectMapper.readTree(message);
            if (jsonNode.has("p")) {
                String markPrice = jsonNode.get("p").asText();
                String indexPrice = jsonNode.get("i").asText();
                String fundingRate = jsonNode.get("r").asText();
                long nextFundingTime = jsonNode.get("T").asLong();

                log.debug("标记价格数据: symbol={}, markPrice={}, indexPrice={}, fundingRate={}, nextFundingTime={}",
                        symbol.getSymbol(), markPrice, indexPrice, fundingRate, nextFundingTime);

                // 这里可以添加具体的业务处理逻辑

            } else {
                log.warn("标记价格消息格式异常: {}", message);
            }

        } catch (Exception e) {
            log.error("处理标记价格消息失败: key={}, message={}", subscriptionKey, message, e);
        }
    }
    
    @Override
    public void handleAggTradeMessage(String subscriptionKey, Symbol symbol, String message) {
        try {
            log.debug("收到聚合交易消息: key={}, symbol={}", subscriptionKey, symbol.getSymbol());
            
            JsonNode jsonNode = objectMapper.readTree(message);
            if (jsonNode.has("p") && jsonNode.has("q")) {
                String price = jsonNode.get("p").asText();
                String quantity = jsonNode.get("q").asText();
                long tradeTime = jsonNode.get("T").asLong();
                boolean isBuyerMaker = jsonNode.get("m").asBoolean();
                
                log.debug("聚合交易数据: symbol={}, price={}, quantity={}, time={}, buyerMaker={}", 
                        symbol.getSymbol(), price, quantity, tradeTime, isBuyerMaker);
                
                // 这里可以添加具体的业务处理逻辑
                
            } else {
                log.warn("聚合交易消息格式异常: {}", message);
            }
            
        } catch (Exception e) {
            log.error("处理聚合交易消息失败: key={}, message={}", subscriptionKey, message, e);
        }
    }
    
    @Override
    public void handleUserDataMessage(String subscriptionKey, String listenKey, String message) {
        try {
            log.debug("收到用户数据流消息: key={}, listenKey={}", subscriptionKey, listenKey);
            
            JsonNode jsonNode = objectMapper.readTree(message);
            if (jsonNode.has("e")) {
                String eventType = jsonNode.get("e").asText();
                
                switch (eventType) {
                    case "ACCOUNT_UPDATE":
                        handleAccountUpdate(jsonNode);
                        break;
                    case "ORDER_TRADE_UPDATE":
                        handleOrderUpdate(jsonNode);
                        break;
                    case "ACCOUNT_CONFIG_UPDATE":
                        handleAccountConfigUpdate(jsonNode);
                        break;
                    default:
                        log.debug("未知用户数据事件类型: {}", eventType);
                }
                
            } else {
                log.warn("用户数据消息格式异常: {}", message);
            }
            
        } catch (Exception e) {
            log.error("处理用户数据消息失败: key={}, message={}", subscriptionKey, message, e);
        }
    }
    
    @Override
    public void handleForceOrderMessage(String subscriptionKey, Symbol symbol, String message) {
        try {
            log.debug("收到强制平仓订单消息: key={}, symbol={}", subscriptionKey, symbol.getSymbol());
            
            JsonNode jsonNode = objectMapper.readTree(message);
            if (jsonNode.has("o")) {
                JsonNode orderData = jsonNode.get("o");
                String orderSymbol = orderData.get("s").asText();
                String side = orderData.get("S").asText();
                String orderType = orderData.get("o").asText();
                String quantity = orderData.get("q").asText();
                String price = orderData.get("p").asText();
                
                log.warn("强制平仓订单: symbol={}, side={}, type={}, quantity={}, price={}", 
                        orderSymbol, side, orderType, quantity, price);
                
                // 这里可以添加具体的业务处理逻辑
                
            } else {
                log.warn("强制平仓消息格式异常: {}", message);
            }
            
        } catch (Exception e) {
            log.error("处理强制平仓消息失败: key={}, message={}", subscriptionKey, message, e);
        }
    }
    
    @Override
    public void handleConnectionOpen(String subscriptionKey) {
        log.info("WebSocket连接已打开: {}", subscriptionKey);
    }
    
    @Override
    public void handleConnectionClose(String subscriptionKey, String reason) {
        log.warn("WebSocket连接已关闭: key={}, reason={}", subscriptionKey, reason);
    }
    
    @Override
    public void handleConnectionError(String subscriptionKey, Throwable error) {
        log.error("WebSocket连接错误: key={}", subscriptionKey, error);
    }
    
    @Override
    public void handleReconnect(String subscriptionKey, int attempt) {
        log.info("WebSocket重连: key={}, attempt={}", subscriptionKey, attempt);
    }
    
    // ==================== 私有方法 ====================
    
    private void handleAccountUpdate(JsonNode jsonNode) {
        log.debug("处理账户更新事件");
        // 处理账户余额、持仓等更新
    }
    
    private void handleOrderUpdate(JsonNode jsonNode) {
        log.debug("处理订单更新事件");
        // 处理订单状态变化
    }
    
    private void handleAccountConfigUpdate(JsonNode jsonNode) {
        log.debug("处理账户配置更新事件");
        // 处理杠杆、多空模式等配置变化
    }
}
