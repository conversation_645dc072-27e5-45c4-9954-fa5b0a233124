package com.trading.sdk.websocket;

import com.trading.common.enums.WebSocketEventType;
import com.trading.common.enums.ErrorCode;
import com.trading.common.exception.SdkException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * WebSocket事件处理器
 * 基于WebSocketEventType枚举处理各种WebSocket事件
 * 支持事件监听、统计和回调机制
 * 
 * <AUTHOR> System
 * @since 1.0.0
 */
@Component
public class WebSocketEventHandler {

    private static final Logger log = LoggerFactory.getLogger(WebSocketEventHandler.class);
    
    // 事件监听器映射
    private final ConcurrentHashMap<WebSocketEventType, Consumer<WebSocketEvent>> eventListeners = new ConcurrentHashMap<>();
    
    // 事件统计计数器
    private final ConcurrentHashMap<WebSocketEventType, AtomicLong> eventCounters = new ConcurrentHashMap<>();
    
    // 连接状态跟踪
    private volatile boolean isConnected = false;
    private volatile boolean isAuthenticated = false;
    private volatile LocalDateTime lastEventTime = LocalDateTime.now();
    
    public WebSocketEventHandler() {
        // 初始化事件计数器
        for (WebSocketEventType eventType : WebSocketEventType.values()) {
            eventCounters.put(eventType, new AtomicLong(0));
        }
        log.info("WebSocket事件处理器初始化完成，支持{}种事件类型", WebSocketEventType.values().length);
    }
    
    /**
     * 注册事件监听器
     * 
     * @param eventType 事件类型
     * @param listener 事件监听器
     */
    public void addEventListener(WebSocketEventType eventType, Consumer<WebSocketEvent> listener) {
        eventListeners.put(eventType, listener);
        log.debug("注册事件监听器: {}", eventType.getDescription());
    }
    
    /**
     * 移除事件监听器
     * 
     * @param eventType 事件类型
     */
    public void removeEventListener(WebSocketEventType eventType) {
        eventListeners.remove(eventType);
        log.debug("移除事件监听器: {}", eventType.getDescription());
    }
    
    /**
     * 处理WebSocket事件
     * 
     * @param eventType 事件类型
     * @param data 事件数据
     */
    public void handleEvent(WebSocketEventType eventType, Object data) {
        try {
            // 更新事件时间和计数
            lastEventTime = LocalDateTime.now();
            eventCounters.get(eventType).incrementAndGet();
            
            // 更新连接状态
            updateConnectionStatus(eventType);
            
            // 创建事件对象
            WebSocketEvent event = new WebSocketEvent(eventType, data, lastEventTime);
            
            // 执行事件监听器
            Consumer<WebSocketEvent> listener = eventListeners.get(eventType);
            if (listener != null) {
                listener.accept(event);
            }
            
            // 记录关键事件
            logKeyEvents(eventType, event);
            
        } catch (Exception e) {
            log.error("处理WebSocket事件失败: eventType={}, error={}", eventType, e.getMessage(), e);
            throw new SdkException("Failed to handle WebSocket event: " + eventType, e);
        }
    }
    
    /**
     * 更新连接状态
     * 
     * @param eventType 事件类型
     */
    private void updateConnectionStatus(WebSocketEventType eventType) {
        switch (eventType) {
            case CONNECTION_OPENED:
                isConnected = true;
                log.info("WebSocket连接已建立");
                break;
            case CONNECTION_CLOSED:
                isConnected = false;
                isAuthenticated = false;
                log.info("WebSocket连接已关闭");
                break;
            case CONNECTION_FAILED:
                isConnected = false;
                isAuthenticated = false;
                log.warn("WebSocket连接失败");
                break;
            case AUTH_SUCCESS:
                isAuthenticated = true;
                log.info("WebSocket认证成功");
                break;
            case AUTH_FAILED:
                isAuthenticated = false;
                log.warn("WebSocket认证失败");
                break;
            default:
                // 其他事件不影响连接状态
                break;
        }
    }
    
    /**
     * 记录关键事件
     * 
     * @param eventType 事件类型
     * @param event 事件对象
     */
    private void logKeyEvents(WebSocketEventType eventType, WebSocketEvent event) {
        switch (eventType) {
            case CONNECTION_OPENED:
            case CONNECTION_CLOSED:
            case CONNECTION_FAILED:
            case AUTH_SUCCESS:
            case AUTH_FAILED:
            case ERROR_OCCURRED:
                log.info("WebSocket关键事件: {} - {}", eventType.getDescription(), event.getData());
                break;
            case MESSAGE_RECEIVED:
                log.debug("WebSocket消息接收: 数据长度={}", 
                    event.getData() != null ? event.getData().toString().length() : 0);
                break;
            case HEARTBEAT_SENT:
            case HEARTBEAT_RECEIVED:
                log.debug("WebSocket心跳事件: {}", eventType.getDescription());
                break;
            default:
                log.debug("WebSocket事件: {} - {}", eventType.getDescription(), event.getData());
                break;
        }
    }
    
    /**
     * 获取连接状态
     * 
     * @return 是否已连接
     */
    public boolean isConnected() {
        return isConnected;
    }
    
    /**
     * 获取认证状态
     * 
     * @return 是否已认证
     */
    public boolean isAuthenticated() {
        return isAuthenticated;
    }
    
    /**
     * 获取最后事件时间
     * 
     * @return 最后事件时间
     */
    public LocalDateTime getLastEventTime() {
        return lastEventTime;
    }
    
    /**
     * 获取事件统计信息
     * 
     * @return 事件统计映射
     */
    public ConcurrentHashMap<WebSocketEventType, Long> getEventStats() {
        ConcurrentHashMap<WebSocketEventType, Long> stats = new ConcurrentHashMap<>();
        eventCounters.forEach((eventType, counter) -> stats.put(eventType, counter.get()));
        return stats;
    }
    
    /**
     * 重置事件统计
     */
    public void resetEventStats() {
        eventCounters.values().forEach(counter -> counter.set(0));
        log.info("WebSocket事件统计已重置");
    }
    
    /**
     * 获取事件统计摘要
     * 
     * @return 统计摘要字符串
     */
    public String getEventStatsSummary() {
        StringBuilder summary = new StringBuilder("WebSocket事件统计:\n");
        eventCounters.forEach((eventType, counter) -> {
            if (counter.get() > 0) {
                summary.append(String.format("  %s: %d次\n", eventType.getDescription(), counter.get()));
            }
        });
        summary.append(String.format("连接状态: %s, 认证状态: %s, 最后事件时间: %s", 
            isConnected ? "已连接" : "未连接",
            isAuthenticated ? "已认证" : "未认证",
            lastEventTime));
        return summary.toString();
    }
    
    /**
     * WebSocket事件对象
     */
    public static class WebSocketEvent {
        private final WebSocketEventType eventType;
        private final Object data;
        private final LocalDateTime timestamp;
        
        public WebSocketEvent(WebSocketEventType eventType, Object data, LocalDateTime timestamp) {
            this.eventType = eventType;
            this.data = data;
            this.timestamp = timestamp;
        }
        
        public WebSocketEventType getEventType() {
            return eventType;
        }
        
        public Object getData() {
            return data;
        }
        
        public LocalDateTime getTimestamp() {
            return timestamp;
        }
        
        @Override
        public String toString() {
            return String.format("WebSocketEvent{eventType=%s, timestamp=%s, data=%s}", 
                eventType, timestamp, data);
        }
    }
}
