package com.trading.sdk.websocket;

import com.trading.common.enums.WebSocketEventType;
import com.trading.common.enums.ErrorCode;
import com.trading.common.exception.SdkException;
import com.trading.common.utils.AsyncDelayUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.LongAdder;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.List;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 增强版WebSocket事件处理器
 * 基于JDK21虚拟线程的高性能事件处理系统
 * 
 * 增强功能：
 * - 虚拟线程异步事件处理
 * - 事件批处理和队列管理
 * - 性能监控和统计
 * - 内存优化和对象池化
 * - 背压控制和流量管理
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
@Component
public class EnhancedWebSocketEventHandler {
    
    private static final Logger log = LoggerFactory.getLogger(EnhancedWebSocketEventHandler.class);
    
    // 事件处理配置 - 优化性能参数
    private static final int EVENT_QUEUE_CAPACITY = 100000; // 进一步增加队列容量，支持更高吞吐量
    private static final int BATCH_SIZE = 200; // 增加批处理大小，提高处理效率
    private static final long BATCH_TIMEOUT_MS = 5; // 减少超时时间，提高响应速度
    private static final int MAX_CONCURRENT_BATCHES = 8; // 增加并发批处理数量，充分利用虚拟线程
    
    // 事件监听器
    private final ConcurrentHashMap<WebSocketEventType, Consumer<WebSocketEvent>> eventListeners = new ConcurrentHashMap<>();
    
    // 事件计数器 - 使用LongAdder提升并发性能
    private final ConcurrentHashMap<WebSocketEventType, LongAdder> eventCounters = new ConcurrentHashMap<>();
    
    // 事件队列 - 支持批处理
    private final BlockingQueue<WebSocketEvent> eventQueue = new ArrayBlockingQueue<>(EVENT_QUEUE_CAPACITY);
    
    // 性能统计
    private final LongAdder totalEventsProcessed = new LongAdder();
    private final LongAdder totalProcessingTime = new LongAdder();
    private final LongAdder eventQueueOverflows = new LongAdder();
    private final LongAdder batchesProcessed = new LongAdder();
    private final LongAdder processingErrors = new LongAdder();
    
    // 连接状态
    private volatile boolean isConnected = false;
    private volatile boolean isAuthenticated = false;
    private volatile LocalDateTime lastEventTime = LocalDateTime.now();
    private volatile LocalDateTime connectionTime;
    
    // 处理器状态
    private final AtomicBoolean processingStarted = new AtomicBoolean(false);
    private final AtomicBoolean shutdownRequested = new AtomicBoolean(false);
    
    // 并发控制 - 使用CompletableFuture优化
    private final AtomicInteger activeBatchProcessors = new AtomicInteger(0);
    private final List<CompletableFuture<Void>> batchProcessorFutures = new ArrayList<>();
    private final CountDownLatch shutdownLatch = new CountDownLatch(MAX_CONCURRENT_BATCHES);
    
    public EnhancedWebSocketEventHandler() {
        // 初始化事件计数器
        for (WebSocketEventType eventType : WebSocketEventType.values()) {
            eventCounters.put(eventType, new LongAdder());
        }
        
        log.info("增强版WebSocket事件处理器初始化完成，支持{}种事件类型", WebSocketEventType.values().length);
        
        // 启动事件处理虚拟线程
        startEventProcessing();
    }
    
    /**
     * 启动事件处理虚拟线程
     */
    private void startEventProcessing() {
        if (!processingStarted.compareAndSet(false, true)) {
            return;
        }
        
        log.info("启动{}个事件处理虚拟线程", MAX_CONCURRENT_BATCHES);
        
        // 启动多个虚拟线程进行并发批处理
        for (int i = 0; i < MAX_CONCURRENT_BATCHES; i++) {
            final int processorId = i;
            Thread.startVirtualThread(() -> {
                log.debug("事件处理虚拟线程-{} 已启动", processorId);
                
                List<WebSocketEvent> batch = new ArrayList<>(BATCH_SIZE);
                
                while (!shutdownRequested.get()) {
                    try {
                        activeBatchProcessors.incrementAndGet();
                        
                        // 收集批处理事件
                        collectBatchEvents(batch);
                        
                        if (!batch.isEmpty()) {
                            // 批量处理事件
                            processBatchEvents(batch, processorId);
                            batch.clear();
                        }
                        
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        log.info("事件处理线程-{} 被中断", processorId);
                        break;
                    } catch (Exception e) {
                        log.error("批量处理事件失败 - 处理器{}", processorId, e);
                        processingErrors.increment();
                        // 继续处理，不中断线程
                    } finally {
                        activeBatchProcessors.decrementAndGet();
                    }
                }
                
                log.debug("事件处理虚拟线程-{} 已停止", processorId);
            });
        }
    }
    
    /**
     * 收集批处理事件
     */
    private void collectBatchEvents(List<WebSocketEvent> batch) throws InterruptedException {
        long startTime = System.currentTimeMillis();
        
        // 等待第一个事件
        WebSocketEvent firstEvent = eventQueue.take();
        batch.add(firstEvent);
        
        // 收集更多事件直到批次满或超时
        while (batch.size() < BATCH_SIZE) {
            long elapsed = System.currentTimeMillis() - startTime;
            if (elapsed >= BATCH_TIMEOUT_MS) {
                break;
            }
            
            WebSocketEvent event = eventQueue.poll();
            if (event == null) {
                break;
            }
            batch.add(event);
        }
    }
    
    /**
     * 批量处理事件
     */
    private void processBatchEvents(List<WebSocketEvent> batch, int processorId) {
        long startTime = System.nanoTime();
        
        try {
            // 并行处理批次中的事件
            batch.parallelStream().forEach(this::processEvent);
            
            // 更新统计信息
            totalEventsProcessed.add(batch.size());
            batchesProcessed.increment();
            long processingTime = System.nanoTime() - startTime;
            totalProcessingTime.add(processingTime);
            
            if (log.isDebugEnabled()) {
                log.debug("批量处理事件完成 - 处理器{}: count={}, time={}μs", 
                        processorId, batch.size(), processingTime / 1000);
            }
            
        } catch (Exception e) {
            log.error("批量处理事件失败 - 处理器{}: batchSize={}", processorId, batch.size(), e);
            processingErrors.increment();
        }
    }

    /**
     * 异步批量处理事件
     * 使用CompletableFuture优化性能
     */
    private CompletableFuture<Void> processBatchEventsAsync(List<WebSocketEvent> batch, int processorId) {
        long startTime = System.nanoTime();

        // 将批次分割为更小的子批次进行并行处理
        int subBatchSize = Math.max(1, batch.size() / 4); // 分成4个子批次
        List<CompletableFuture<Void>> subBatchFutures = new ArrayList<>();

        for (int i = 0; i < batch.size(); i += subBatchSize) {
            int endIndex = Math.min(i + subBatchSize, batch.size());
            List<WebSocketEvent> subBatch = batch.subList(i, endIndex);

            CompletableFuture<Void> subBatchFuture = CompletableFuture.runAsync(() -> {
                subBatch.forEach(this::processEvent);
            }, Executors.newVirtualThreadPerTaskExecutor());

            subBatchFutures.add(subBatchFuture);
        }

        // 等待所有子批次完成
        return CompletableFuture.allOf(subBatchFutures.toArray(new CompletableFuture[0]))
                .whenComplete((result, throwable) -> {
                    // 更新统计
                    batchesProcessed.increment();
                    totalEventsProcessed.add(batch.size());

                    long processingTime = System.nanoTime() - startTime;
                    totalProcessingTime.add(processingTime);

                    if (throwable != null) {
                        log.error("异步批处理失败 - 处理器{}", processorId, throwable);
                        processingErrors.increment();
                    } else if (log.isDebugEnabled()) {
                        log.debug("处理器-{} 完成异步批次处理: 事件数={}, 耗时={}μs",
                                processorId, batch.size(), processingTime / 1000);
                    }
                });
    }
    
    /**
     * 处理单个事件
     */
    private void processEvent(WebSocketEvent event) {
        try {
            // 执行事件监听器
            Consumer<WebSocketEvent> listener = eventListeners.get(event.getEventType());
            if (listener != null) {
                listener.accept(event);
            }
            
            // 记录关键事件
            logKeyEvents(event.getEventType(), event);
            
        } catch (Exception e) {
            log.error("处理单个事件失败: eventType={}", event.getEventType(), e);
            processingErrors.increment();
        }
    }
    
    /**
     * 注册事件监听器
     * 
     * @param eventType 事件类型
     * @param listener 事件监听器
     */
    public void addEventListener(WebSocketEventType eventType, Consumer<WebSocketEvent> listener) {
        eventListeners.put(eventType, listener);
        log.debug("注册事件监听器: {}", eventType.getDescription());
    }
    
    /**
     * 移除事件监听器
     * 
     * @param eventType 事件类型
     */
    public void removeEventListener(WebSocketEventType eventType) {
        eventListeners.remove(eventType);
        log.debug("移除事件监听器: {}", eventType.getDescription());
    }
    
    /**
     * 处理WebSocket事件 - 异步版本
     * 
     * @param eventType 事件类型
     * @param data 事件数据
     */
    public void handleEvent(WebSocketEventType eventType, Object data) {
        try {
            // 更新事件时间和计数
            lastEventTime = LocalDateTime.now();
            eventCounters.get(eventType).increment();
            
            // 更新连接状态
            updateConnectionStatus(eventType);
            
            // 创建事件对象
            WebSocketEvent event = new WebSocketEvent(eventType, data, lastEventTime);
            
            // 异步提交到事件队列
            if (!eventQueue.offer(event)) {
                // 队列满时记录溢出
                eventQueueOverflows.increment();
                log.warn("事件队列已满，丢弃事件: eventType={}", eventType);
                
                // 对于关键事件，尝试同步处理
                if (isCriticalEvent(eventType)) {
                    log.warn("关键事件同步处理: eventType={}", eventType);
                    processEvent(event);
                }
            }
            
        } catch (Exception e) {
            log.error("处理WebSocket事件失败: eventType={}, error={}", eventType, e.getMessage(), e);
            processingErrors.increment();
            throw new SdkException("Failed to handle WebSocket event: " + eventType, e);
        }
    }
    
    /**
     * 同步处理WebSocket事件 - 用于关键事件
     * 
     * @param eventType 事件类型
     * @param data 事件数据
     */
    public void handleEventSync(WebSocketEventType eventType, Object data) {
        try {
            // 更新事件时间和计数
            lastEventTime = LocalDateTime.now();
            eventCounters.get(eventType).increment();
            
            // 更新连接状态
            updateConnectionStatus(eventType);
            
            // 创建事件对象
            WebSocketEvent event = new WebSocketEvent(eventType, data, lastEventTime);
            
            // 同步处理事件
            processEvent(event);
            
        } catch (Exception e) {
            log.error("同步处理WebSocket事件失败: eventType={}, error={}", eventType, e.getMessage(), e);
            processingErrors.increment();
            throw new SdkException("Failed to handle WebSocket event synchronously: " + eventType, e);
        }
    }
    
    /**
     * 判断是否为关键事件
     */
    private boolean isCriticalEvent(WebSocketEventType eventType) {
        return eventType == WebSocketEventType.CONNECTION_OPENED ||
               eventType == WebSocketEventType.CONNECTION_CLOSED ||
               eventType == WebSocketEventType.CONNECTION_FAILED ||
               eventType == WebSocketEventType.AUTH_SUCCESS ||
               eventType == WebSocketEventType.AUTH_FAILED ||
               eventType == WebSocketEventType.ERROR_OCCURRED;
    }

    /**
     * 更新连接状态
     *
     * @param eventType 事件类型
     */
    private void updateConnectionStatus(WebSocketEventType eventType) {
        switch (eventType) {
            case CONNECTION_OPENED:
                isConnected = true;
                connectionTime = LocalDateTime.now();
                log.info("WebSocket连接已建立");
                break;
            case CONNECTION_CLOSED:
                isConnected = false;
                isAuthenticated = false;
                log.info("WebSocket连接已关闭");
                break;
            case CONNECTION_FAILED:
                isConnected = false;
                isAuthenticated = false;
                log.warn("WebSocket连接失败");
                break;
            case AUTH_SUCCESS:
                isAuthenticated = true;
                log.info("WebSocket认证成功");
                break;
            case AUTH_FAILED:
                isAuthenticated = false;
                log.warn("WebSocket认证失败");
                break;
            default:
                // 其他事件不影响连接状态
                break;
        }
    }

    /**
     * 记录关键事件
     *
     * @param eventType 事件类型
     * @param event 事件对象
     */
    private void logKeyEvents(WebSocketEventType eventType, WebSocketEvent event) {
        switch (eventType) {
            case CONNECTION_OPENED:
            case CONNECTION_CLOSED:
            case CONNECTION_FAILED:
            case AUTH_SUCCESS:
            case AUTH_FAILED:
            case ERROR_OCCURRED:
                log.info("WebSocket关键事件: {} - {}", eventType.getDescription(), event.getData());
                break;
            case MESSAGE_RECEIVED:
                if (log.isDebugEnabled()) {
                    log.debug("WebSocket消息接收: 数据长度={}",
                        event.getData() != null ? event.getData().toString().length() : 0);
                }
                break;
            case HEARTBEAT_SENT:
            case HEARTBEAT_RECEIVED:
                if (log.isDebugEnabled()) {
                    log.debug("WebSocket心跳事件: {}", eventType.getDescription());
                }
                break;
            default:
                if (log.isTraceEnabled()) {
                    log.trace("WebSocket事件: {} - {}", eventType.getDescription(), event.getData());
                }
                break;
        }
    }

    /**
     * 获取连接状态
     *
     * @return 是否已连接
     */
    public boolean isConnected() {
        return isConnected;
    }

    /**
     * 获取认证状态
     *
     * @return 是否已认证
     */
    public boolean isAuthenticated() {
        return isAuthenticated;
    }

    /**
     * 获取最后事件时间
     *
     * @return 最后事件时间
     */
    public LocalDateTime getLastEventTime() {
        return lastEventTime;
    }

    /**
     * 获取连接时间
     *
     * @return 连接时间
     */
    public LocalDateTime getConnectionTime() {
        return connectionTime;
    }

    /**
     * 获取事件统计信息
     *
     * @return 事件统计映射
     */
    public ConcurrentHashMap<WebSocketEventType, Long> getEventStats() {
        ConcurrentHashMap<WebSocketEventType, Long> stats = new ConcurrentHashMap<>();
        eventCounters.forEach((eventType, counter) -> stats.put(eventType, counter.sum()));
        return stats;
    }

    /**
     * 获取性能统计信息
     *
     * @return 性能统计信息
     */
    public PerformanceStats getPerformanceStats() {
        return new PerformanceStats(
            totalEventsProcessed.sum(),
            batchesProcessed.sum(),
            totalProcessingTime.sum(),
            eventQueueOverflows.sum(),
            processingErrors.sum(),
            eventQueue.size(),
            activeBatchProcessors.get()
        );
    }

    /**
     * 重置事件统计
     */
    public void resetEventStats() {
        eventCounters.values().forEach(LongAdder::reset);
        totalEventsProcessed.reset();
        batchesProcessed.reset();
        totalProcessingTime.reset();
        eventQueueOverflows.reset();
        processingErrors.reset();
        log.info("WebSocket事件统计已重置");
    }

    /**
     * 获取事件统计摘要
     *
     * @return 统计摘要字符串
     */
    public String getEventStatsSummary() {
        StringBuilder summary = new StringBuilder("增强版WebSocket事件统计:\n");

        // 事件类型统计
        eventCounters.forEach((eventType, counter) -> {
            if (counter.sum() > 0) {
                summary.append(String.format("  %s: %d次\n", eventType.getDescription(), counter.sum()));
            }
        });

        // 性能统计
        PerformanceStats stats = getPerformanceStats();
        summary.append(String.format("\n性能统计:\n"));
        summary.append(String.format("  总处理事件: %d\n", stats.totalEventsProcessed));
        summary.append(String.format("  批次处理: %d\n", stats.batchesProcessed));
        summary.append(String.format("  平均处理时间: %.2fμs\n", stats.getAverageProcessingTime()));
        summary.append(String.format("  队列溢出: %d\n", stats.queueOverflows));
        summary.append(String.format("  处理错误: %d\n", stats.processingErrors));
        summary.append(String.format("  当前队列大小: %d\n", stats.currentQueueSize));
        summary.append(String.format("  活跃处理器: %d\n", stats.activeBatchProcessors));

        // 连接状态
        summary.append(String.format("\n连接状态:\n"));
        summary.append(String.format("  连接状态: %s\n", isConnected ? "已连接" : "未连接"));
        summary.append(String.format("  认证状态: %s\n", isAuthenticated ? "已认证" : "未认证"));
        summary.append(String.format("  最后事件时间: %s\n", lastEventTime));
        if (connectionTime != null) {
            summary.append(String.format("  连接时间: %s\n", connectionTime));
        }

        return summary.toString();
    }

    /**
     * 关闭事件处理器
     */
    public void shutdown() {
        log.info("正在关闭增强版WebSocket事件处理器...");
        shutdownRequested.set(true);

        // 使用CompletableFuture等待所有处理器完成
        try {
            // 等待所有批处理器完成，最多等待5秒
            CompletableFuture<Void> allProcessors = CompletableFuture.allOf(
                    batchProcessorFutures.toArray(new CompletableFuture[0]));

            allProcessors.get(5, java.util.concurrent.TimeUnit.SECONDS);
            log.info("所有批处理器已正常关闭");

        } catch (java.util.concurrent.TimeoutException e) {
            log.warn("等待批处理器关闭超时，强制关闭");
            // 取消所有未完成的处理器
            batchProcessorFutures.forEach(future -> future.cancel(true));
        } catch (Exception e) {
            log.error("等待批处理器关闭时发生异常", e);
        }

        log.info("增强版WebSocket事件处理器已关闭，剩余队列大小: {}", eventQueue.size());
    }

    /**
     * 检查事件处理器是否已关闭
     *
     * @return 是否已关闭
     */
    public boolean isShutdown() {
        return shutdownRequested.get();
    }

    /**
     * 性能统计信息
     */
    public static class PerformanceStats {
        private final long totalEventsProcessed;
        private final long batchesProcessed;
        private final long totalProcessingTime;
        private final long queueOverflows;
        private final long processingErrors;
        private final int currentQueueSize;
        private final int activeBatchProcessors;

        public PerformanceStats(long totalEventsProcessed, long batchesProcessed,
                               long totalProcessingTime, long queueOverflows,
                               long processingErrors, int currentQueueSize,
                               int activeBatchProcessors) {
            this.totalEventsProcessed = totalEventsProcessed;
            this.batchesProcessed = batchesProcessed;
            this.totalProcessingTime = totalProcessingTime;
            this.queueOverflows = queueOverflows;
            this.processingErrors = processingErrors;
            this.currentQueueSize = currentQueueSize;
            this.activeBatchProcessors = activeBatchProcessors;
        }

        public long getTotalEventsProcessed() { return totalEventsProcessed; }
        public long getBatchesProcessed() { return batchesProcessed; }
        public long getTotalProcessingTime() { return totalProcessingTime; }
        public long getQueueOverflows() { return queueOverflows; }
        public long getProcessingErrors() { return processingErrors; }
        public int getCurrentQueueSize() { return currentQueueSize; }
        public int getActiveBatchProcessors() { return activeBatchProcessors; }

        public double getAverageProcessingTime() {
            return totalEventsProcessed > 0 ?
                (double) totalProcessingTime / totalEventsProcessed / 1000.0 : 0.0;
        }

        public double getAverageBatchSize() {
            return batchesProcessed > 0 ?
                (double) totalEventsProcessed / batchesProcessed : 0.0;
        }

        public double getErrorRate() {
            return totalEventsProcessed > 0 ?
                (double) processingErrors / totalEventsProcessed * 100.0 : 0.0;
        }

        @Override
        public String toString() {
            return String.format("PerformanceStats{events=%d, batches=%d, avgTime=%.2fμs, " +
                               "errors=%d(%.2f%%), queueSize=%d, processors=%d}",
                               totalEventsProcessed, batchesProcessed, getAverageProcessingTime(),
                               processingErrors, getErrorRate(), currentQueueSize, activeBatchProcessors);
        }
    }

    /**
     * WebSocket事件对象
     */
    public static class WebSocketEvent {
        private final WebSocketEventType eventType;
        private final Object data;
        private final LocalDateTime timestamp;

        public WebSocketEvent(WebSocketEventType eventType, Object data, LocalDateTime timestamp) {
            this.eventType = eventType;
            this.data = data;
            this.timestamp = timestamp;
        }

        public WebSocketEventType getEventType() {
            return eventType;
        }

        public Object getData() {
            return data;
        }

        public LocalDateTime getTimestamp() {
            return timestamp;
        }

        @Override
        public String toString() {
            return String.format("WebSocketEvent{eventType=%s, timestamp=%s, data=%s}",
                eventType, timestamp, data);
        }
    }
}
