package com.trading.sdk.websocket.callback;

import com.trading.common.dto.Symbol;

/**
 * WebSocket消息处理器接口
 * 定义各种类型消息的处理方法
 * 
 * <AUTHOR> Trading System
 * @since 1.0.0
 */
public interface MessageHandler {
    
    /**
     * 处理K线消息
     * 
     * @param subscriptionKey 订阅键
     * @param symbol 交易对
     * @param interval 时间间隔
     * @param message 消息内容
     */
    void handleKlineMessage(String subscriptionKey, Symbol symbol, String interval, String message);
    
    /**
     * 处理深度消息
     * 
     * @param subscriptionKey 订阅键
     * @param symbol 交易对
     * @param levels 深度级别
     * @param speed 更新速度
     * @param message 消息内容
     */
    void handleDepthMessage(String subscriptionKey, Symbol symbol, int levels, int speed, String message);
    
    /**
     * 处理交易消息
     *
     * @param subscriptionKey 订阅键
     * @param symbol 交易对
     * @param message 消息内容
     */
    void handleTradeMessage(String subscriptionKey, Symbol symbol, String message);

    /**
     * 处理24小时统计消息
     *
     * @param subscriptionKey 订阅键
     * @param symbol 交易对
     * @param message 消息内容
     */
    void handleTickerMessage(String subscriptionKey, Symbol symbol, String message);

    /**
     * 处理最优挂单消息
     *
     * @param subscriptionKey 订阅键
     * @param symbol 交易对
     * @param message 消息内容
     */
    void handleBookTickerMessage(String subscriptionKey, Symbol symbol, String message);

    /**
     * 处理标记价格消息
     *
     * @param subscriptionKey 订阅键
     * @param symbol 交易对
     * @param message 消息内容
     */
    void handleMarkPriceMessage(String subscriptionKey, Symbol symbol, String message);
    
    /**
     * 处理聚合交易消息
     * 
     * @param subscriptionKey 订阅键
     * @param symbol 交易对
     * @param message 消息内容
     */
    void handleAggTradeMessage(String subscriptionKey, Symbol symbol, String message);
    

    
    /**
     * 处理用户数据流消息
     * 
     * @param subscriptionKey 订阅键
     * @param listenKey 监听键
     * @param message 消息内容
     */
    void handleUserDataMessage(String subscriptionKey, String listenKey, String message);
    
    /**
     * 处理强制平仓订单消息
     * 
     * @param subscriptionKey 订阅键
     * @param symbol 交易对
     * @param message 消息内容
     */
    void handleForceOrderMessage(String subscriptionKey, Symbol symbol, String message);
    
    /**
     * 处理连接打开事件
     * 
     * @param subscriptionKey 订阅键
     */
    void handleConnectionOpen(String subscriptionKey);
    
    /**
     * 处理连接关闭事件
     * 
     * @param subscriptionKey 订阅键
     * @param reason 关闭原因
     */
    void handleConnectionClose(String subscriptionKey, String reason);
    
    /**
     * 处理连接错误事件
     * 
     * @param subscriptionKey 订阅键
     * @param error 错误信息
     */
    void handleConnectionError(String subscriptionKey, Throwable error);
    
    /**
     * 处理重连事件
     * 
     * @param subscriptionKey 订阅键
     * @param attempt 重连次数
     */
    void handleReconnect(String subscriptionKey, int attempt);
}
