package com.trading.sdk.websocket;

import com.binance.connector.futures.client.impl.UMWebsocketClientImpl;
import com.binance.connector.futures.client.impl.CMWebsocketClientImpl;
import com.binance.connector.futures.client.utils.WebSocketCallback;
import com.trading.sdk.config.SdkConfiguration;
import com.trading.common.exception.SdkException;
import com.trading.common.dto.Symbol;
import com.trading.sdk.websocket.callback.MessageHandler;
import com.trading.sdk.websocket.model.SubscriptionInfo;
import com.trading.common.enums.StreamType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;
import com.trading.common.enums.DataType;


@Component
public class WebSocketManager implements InitializingBean, DisposableBean {
    
    private static final Logger log = LoggerFactory.getLogger(WebSocketManager.class);
    
    private final SdkConfiguration sdkConfiguration;
    private final MessageHandler messageHandler;

    private UMWebsocketClientImpl umWebsocketClient;
    private CMWebsocketClientImpl cmWebsocketClient;
    
    private final ConcurrentHashMap<String, SubscriptionInfo> subscriptions = new ConcurrentHashMap<>();
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    
    private volatile boolean initialized = false;

    public WebSocketManager(SdkConfiguration sdkConfiguration, MessageHandler messageHandler) {
        this.sdkConfiguration = sdkConfiguration;
        this.messageHandler = messageHandler;
    }
    
    @Override
    public void afterPropertiesSet() throws Exception {
        initialize();
    }
    
    @Override
    public void destroy() throws Exception {
        shutdown();
    }
    
    public void initialize() {
        if (initialized) {
            return;
        }
        log.info("初始化WebSocket管理器...");
        
        String umStreamUrl = sdkConfiguration.getBinance().isTestnet() ? 
            "wss://stream.binancefuture.com" : "wss://fstream.binance.com";
        String cmStreamUrl = sdkConfiguration.getBinance().isTestnet() ? 
            "wss://dstream.binancefuture.com" : "wss://dstream.binance.com";
            
        this.umWebsocketClient = new UMWebsocketClientImpl(umStreamUrl);
        this.cmWebsocketClient = new CMWebsocketClientImpl(cmStreamUrl);
        
        initialized = true;
        log.info("WebSocket管理器初始化完成");
    }
    
    public void shutdown() {
        log.info("关闭WebSocket管理器...");
        if (umWebsocketClient != null) {
            umWebsocketClient.closeAllConnections();
        }
        if (cmWebsocketClient != null) {
            cmWebsocketClient.closeAllConnections();
        }
        log.info("WebSocket管理器已关闭");
    }
    
    public String subscribeKlineStream(Symbol symbol, String interval, StreamType streamType) {
        String subscriptionKey = String.format("kline_%s_%s_%s", symbol.getSymbol(), interval, streamType.name());
        
        lock.writeLock().lock();
        try {
            if (subscriptions.containsKey(subscriptionKey)) {
                log.warn("订阅已存在: {}", subscriptionKey);
                return subscriptionKey;
            }
            
            WebSocketCallback onMessageCallback = (event) -> {
                messageHandler.handleKlineMessage(subscriptionKey, symbol, interval, event);
            };

            int connectionId;
            if(streamType == StreamType.UM_FUTURES) {
                connectionId = umWebsocketClient.klineStream(symbol.getSymbol().toLowerCase(), interval, onMessageCallback);
            } else {
                connectionId = cmWebsocketClient.klineStream(symbol.getSymbol().toLowerCase(), interval, onMessageCallback);
            }

            SubscriptionInfo subInfo = SubscriptionInfo.builder()
                .subscriptionKey(subscriptionKey)
                .connectionId(connectionId)
                .symbol(symbol)
                .interval(interval)
                .streamType(streamType)
                .dataType(StreamType.UM_FUTURES)
                .active(true)
                .createdAt(System.currentTimeMillis())
                .lastActiveAt(System.currentTimeMillis())
                .build();
            subscriptions.put(subscriptionKey, subInfo);
            
            log.info("K线数据流订阅成功: {}", subscriptionKey);
            return subscriptionKey;
            
        } finally {
            lock.writeLock().unlock();
        }
    }

    public String subscribeDepthStream(Symbol symbol, Integer level, int speed, StreamType streamType) {
        String subscriptionKey = String.format("depth_%s_%d_%s", symbol.getSymbol(), level, streamType.name());
        lock.writeLock().lock();
        try {
            if (subscriptions.containsKey(subscriptionKey)) {
                log.warn("订阅已存在: {}", subscriptionKey);
                return subscriptionKey;
            }

            WebSocketCallback onMessageCallback = (event) -> {
                messageHandler.handleDepthMessage(subscriptionKey, symbol, level, speed, event);
            };

            int connectionId;
            if (streamType == StreamType.UM_FUTURES) {
                connectionId = umWebsocketClient.diffDepthStream(symbol.getSymbol().toLowerCase(), speed, onMessageCallback);
            } else {
                throw new SdkException("CM-Futures depth stream not implemented in this version.");
            }

            SubscriptionInfo subInfo = SubscriptionInfo.builder()
                .subscriptionKey(subscriptionKey)
                .connectionId(connectionId)
                .symbol(symbol)
                .streamType(streamType)
                .dataType(StreamType.UM_FUTURES)
                .active(true)
                .createdAt(System.currentTimeMillis())
                .lastActiveAt(System.currentTimeMillis())
                .build();
            subscriptions.put(subscriptionKey, subInfo);

            log.info("深度数据流订阅成功: {}", subscriptionKey);
            return subscriptionKey;
        } finally {
            lock.writeLock().unlock();
        }
    }

    public String subscribeTradeStream(Symbol symbol, StreamType streamType) {
        String subscriptionKey = String.format("trade_%s_%s", symbol.getSymbol(), streamType.name());
        lock.writeLock().lock();
        try {
            if (subscriptions.containsKey(subscriptionKey)) {
                log.warn("订阅已存在: {}", subscriptionKey);
                return subscriptionKey;
            }

            WebSocketCallback onMessageCallback = (event) -> {
                messageHandler.handleTradeMessage(subscriptionKey, symbol, event);
            };

            int connectionId;
            if (streamType == StreamType.UM_FUTURES) {
                connectionId = umWebsocketClient.aggTradeStream(symbol.getSymbol().toLowerCase(), onMessageCallback);
            } else {
                connectionId = cmWebsocketClient.aggTradeStream(symbol.getSymbol().toLowerCase(), onMessageCallback);
            }

            SubscriptionInfo subInfo = SubscriptionInfo.builder()
                .subscriptionKey(subscriptionKey)
                .connectionId(connectionId)
                .symbol(symbol)
                .streamType(streamType)
                .dataType(StreamType.UM_FUTURES)
                .active(true)
                .createdAt(System.currentTimeMillis())
                .lastActiveAt(System.currentTimeMillis())
                .build();
            subscriptions.put(subscriptionKey, subInfo);

            log.info("交易数据流订阅成功: {}", subscriptionKey);
            return subscriptionKey;
        } finally {
            lock.writeLock().unlock();
        }
    }

    public String subscribeTickerStream(Symbol symbol, StreamType streamType) {
        String subscriptionKey = String.format("ticker_%s_%s", symbol.getSymbol(), streamType.name());
        lock.writeLock().lock();
        try {
            if (subscriptions.containsKey(subscriptionKey)) {
                log.warn("订阅已存在: {}", subscriptionKey);
                return subscriptionKey;
            }

            WebSocketCallback onMessageCallback = (event) -> {
                messageHandler.handleTickerMessage(subscriptionKey, symbol, event);
            };

            int connectionId;
            if (streamType == StreamType.UM_FUTURES) {
                connectionId = umWebsocketClient.symbolTicker(symbol.getSymbol().toLowerCase(), onMessageCallback);
            } else {
                connectionId = cmWebsocketClient.symbolTicker(symbol.getSymbol().toLowerCase(), onMessageCallback);
            }

            SubscriptionInfo subInfo = SubscriptionInfo.builder()
                .subscriptionKey(subscriptionKey)
                .connectionId(connectionId)
                .symbol(symbol)
                .streamType(streamType)
                .dataType(StreamType.UM_FUTURES)
                .active(true)
                .createdAt(System.currentTimeMillis())
                .lastActiveAt(System.currentTimeMillis())
                .build();
            subscriptions.put(subscriptionKey, subInfo);

            log.info("Ticker数据流订阅成功: {}", subscriptionKey);
            return subscriptionKey;
        } finally {
            lock.writeLock().unlock();
        }
    }

    public boolean isSubscriptionActive(String subscriptionKey) {
        lock.readLock().lock();
        try {
            return subscriptions.containsKey(subscriptionKey) && subscriptions.get(subscriptionKey).isActive();
        } finally {
            lock.readLock().unlock();
        }
    }
    
    public boolean unsubscribe(String subscriptionKey) {
        lock.writeLock().lock();
        try {
            SubscriptionInfo subInfo = subscriptions.remove(subscriptionKey);
            if (subInfo != null) {
                if (subInfo.getStreamType() == StreamType.UM_FUTURES) {
                    umWebsocketClient.closeConnection(subInfo.getConnectionId());
                } else {
                    cmWebsocketClient.closeConnection(subInfo.getConnectionId());
                }
                log.info("订阅已取消: {}", subscriptionKey);
                return true;
            } else {
                log.warn("订阅不存在: {}", subscriptionKey);
                return false;
            }
        } finally {
            lock.writeLock().unlock();
        }
    }
}
