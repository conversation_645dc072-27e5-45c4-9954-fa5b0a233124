### General ###
# IDE folders
.idea/
.vscode/
.roo/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Log files and archives
logs/
*.log
*.log.*
*.gz
*.bak
*.zip

# Temp files
*.tmp
*.temp

### Java / Maven ###
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties

### Python ###
# Caches
__pycache__/
*.py[cod]
*$py.class
.numba_cache/
.pytest_cache/

# Virtual environments
.venv/
venv/
env/

# Test reports
test_results/
reports/

# Local config and dependencies
/crypto-ml-strategy/config/config.local.yaml
/crypto-ml-strategy/local_wheels/

# Data files from Python subdirectory - these should not be committed
/crypto-ml-strategy/data/raw/
/crypto-ml-strategy/data/processed/
/crypto-ml-strategy/data/cache/

# Model files
/crypto-ml-strategy/models/*.pth
/crypto-ml-strategy/models/*.pkl

# Numba cache
.numba_cache/

# IDE configs
.roo/

# Build artifacts
bin/
include/
mysql/
protoc-25.3-linux-x86_64.zip
readme.txt
