# 高性能加密货币量化交易系统

本项目是一个采用Java和Python混合架构的高性能加密货币量化交易系统。

## 技术栈

- **核心后端**: Java 21, Spring Boot 3, Maven, MyBatis
- **机器学习策略**: Python, PyTorch, GPU加速
- **监控**: Prometheus, Micrometer

## 模块架构

系统采用多模块设计，各模块职责分明：

- `crypto-common`: **核心基础模块**
  - 为所有Java模块提供共享的基础功能。
  - 包含了标准化的数据传输对象 (DTO)、异常处理、工具类、以及**服务治理框架 (基于 Resilience4j 的熔断器、限流器)、应用指标 (Micrometer) 和并发工具 (如对象池)**。
  - 是所有其他Java模块必须依赖的核心库。

- `crypto-sdk`: **外部API封装模块**
  - 负责与外部交易所（如Binance）的API进行交互，处理请求签名、连接管理和数据格式转换。
  - 依赖 `crypto-common` 来实现接口调用的弹性和监控，确保服务稳定性。

- `crypto-market-data`: **市场数据处理模块**
  - 负责从交易所收集、处理和存储实时及历史市场数据。
  - 是Python策略训练和回测的数据来源。
  - 依赖 `crypto-common` 来实现高可用的数据处理管道和性能监控。

- `crypto-ml-strategy`: **机器学习策略模块**
  - 一个独立的Python项目，用于开发、训练和执行基于`PyTorch`的交易策略。
  - 强制使用GPU进行加速，以处理大规模数据计算。

- `crypto-trading-engine`: **交易执行引擎**
  - 负责执行交易逻辑，管理订单和仓位。
  - 依赖 `crypto-sdk` 与交易所交互，依赖 `crypto-market-data` 获取实时行情。


**AGF (Acknowledge, Ground, Formulate) 就绪确认：**
1.  **Acknowledge (认知)：** 我已收到您提供的一系列高质量、高标准的操作原则。
2.  **Ground (基础)：** 这些原则的核心目标是建立一个严谨、高效、可靠的工程与问题解决流程，杜绝随意性、短期主义和技术负债。
3.  **Formulate (规划)：** 我的任务是对这些原则进行**优化和体系化**，将它们从一个规则列表，提炼成一个结构化、可执行的专业框架。我将为每个原则“调整参数”，即深入阐述其背后的**核心思想、执行要点（“参数”）和需要警惕的反模式**，使其更具指导性和实践价值。

---

**Ultrashink 模式已激活。** 我将对您提出的原则进行深度分析、归类和优化，构建一个全面的高质量工程框架。

### 优化后的高质量软件工程与问题解决框架

---

#### **第一阶段：理解与规划 (Understand & Plan)**
此阶段的目标是确保方向正确，避免在错误的道路上浪费精力。

**原则 1：任务确认与范围界定 (AGF 就绪)**
* **原始指令：** `必须确认理解任务内容后再运行`
* **核心思想：** 这是所有工作的起点。任何模糊不清的理解都会在后续环节被指数级放大，导致返工或彻底失败。
* **执行参数 (如何调整):**
    * **复述目标：** 用自己的话向需求方或上级复述你理解的任务目标和验收标准。
    * **明确边界：** 清晰界定“做什么”与“不做什么”（In-Scope vs. Out-of-Scope）。
    * **提问澄清：** 针对任何模糊点（例如，“提升性能”要提升多少？“优化界面”的具体指标是什么？）提出具体问题，直到不存在歧义。
* **反模式警惕：** 拿到一个模糊的需求就立刻开始写代码，边做边猜。

**原则 2：深度思考与循证推理 (启用 Ultrathink 模式)**
* **原始指令：** `ultrathink 模式默认启用`, `不得满目自信，必须推理 + 证据`
* **核心思想：** 拒绝表层修复和直觉驱动。所有决策和方案都必须建立在逻辑推理和客观证据之上，深入探究问题的根本原因 (Root Cause)。
* **执行参数 (如何调整):**
    * **根本原因分析 (RCA)：** 使用“5 Whys”等方法，连续追问“为什么”，直到找到问题的本源。
    * **假设与验证：** 提出一个或多个关于问题原因的假设，然后寻找证据（如日志、监控数据、用户反馈）来证实或证伪。
    * **方案权衡：** 对比多个潜在解决方案的优劣（如开发成本、长期维护性、性能影响），并说明选择当前方案的理由。
* **反模式警惕：** “我猜是这里的问题”、“以前都是这么解决的”，然后直接动手修改。

**原则 3：上下文全面分析**
* **原始指令：** `必须阅读所有上下文和关联文件，不得略过任何信息`
* **核心思想：** 代码和问题从不孤立存在。任何改动都可能对系统的其他部分产生涟漪效应。
* **执行参数 (如何调整):**
    * **代码考古：** 查看相关代码的提交历史 (`git blame`/`annotate`)，理解其演进过程和历史背景。
    * **依赖审查：** 分析当前模块的上下游依赖关系，评估改动可能产生的影响。
    * **文档与关联任务：** 阅读相关的技术文档、需求文档和历史工单 (Tickets)，获取完整的背景信息。
* **反模式警惕：** 只看问题本身所在的文件，不关心谁调用了它，它又调用了谁。

---

#### **第二阶段：执行与开发 (Execute & Develop)**
此阶段的目标是产出简洁、健壮、可维护的高质量代码。

**原则 4：代码简洁与 DRY 原则 (Don't Repeat Yourself)**
* **原始指令：** `必须移除冗余代码，禁止出现多个版本`
* **核心思想：** 系统中的每一份知识都必须有单一、明确、权威的表示。重复的代码意味着维护成本的成倍增加和潜在的不一致性。
* **执行参数 (如何调整):**
    * **抽象化：** 将重复的逻辑抽象成函数、类或模块。
    * **单一数据源：** 确保相同的数据或配置只存在于一个地方。
    * **定期重构：** 在开发过程中，持续对发现的冗余进行重构，而不是留到最后。
* **反模式警惕：** 复制粘贴代码，只为了快速实现功能。

**原则 5：配置外化，禁止硬编码**
* **原始指令：** `禁止硬编码`
* **核心思想：** 将易变或环境相关的配置（如数据库地址、API 密钥、功能开关、阈值）与代码逻辑分离。
* **执行参数 (如何调整):**
    * **使用环境变量：** 对于敏感信息（密钥）和环境特定配置（端口、地址）。
    * **使用配置文件：** 对于应用级别的、非敏感的配置（如 `.env`, `.yaml`, `.json`）。
    * **使用配置中心：** 对于需要动态更新和统一管理的分布式系统配置。
* **反模式警惕：** 在代码里直接出现 IP 地址、文件路径、密码、API Key 等字面量。

**原则 6：性能优化，算法先行**
* **原始指令：** `性能问题优先考虑算法优化，不得先做缓存等表层绕过`
* **核心思想：** 缓存、增加硬件等手段可以缓解性能问题，但只有算法和数据结构的优化才能从根本上解决问题。表层优化可能会掩盖深层设计缺陷。
* **执行参数 (如何调整):**
    * **性能剖析 (Profiling)：** 首先使用性能分析工具，精确定位性能瓶颈（热点代码）。
    * **复杂度分析：** 分析热点代码的时间和空间复杂度 (Big O)。
    * **数据结构与算法选型：** 思考是否有更高效的数据结构或算法可以替代现有实现。
* **反模式警惕：** “慢了？加个 Redis 缓存”，而不去分析慢查询的 SQL 或低效的循环。

---

#### **第三阶段：测试与验证 (Test & Validate)**
此阶段的目标是确保交付的产物符合预期，且质量可靠。

**原则 7：TFR 循环 (Test-Fix-Revalidate)**
* **原始指令：** `必须测试当前问题来源点 → 修复 → 再验证`
* **核心思想：** 这是一个确保修复有效的最小闭环。
* **执行参数 (如何调整):**
    * **编写复现测试：** 在修复前，编写一个能够稳定复现该问题的自动化测试用例，并确认它是失败的。
    * **实施修复：** 进行代码修复。
    * **运行测试验证：** 再次运行刚才失败的测试用例，确保它现在能够通过。同时，运行所有相关的测试，确保没有引入新的问题（回归测试）。
* **反模式警惕：** 本地手动测试一下，看起来没问题了，就直接提交代码。

**原则 8：严格、独立的单元/模块测试**
* **原始指令：** `禁止绕过测试`, `模块测试必须逐个推进`
* **核心思想：** 测试是保证质量的基石，不是可以跳过的流程。独立的模块测试可以快速定位问题，避免在集成时才发现问题，难以排查。
* **执行参数 (如何调整):**
    * **测试覆盖率：** 追求有意义的测试覆盖率（核心逻辑、边界条件、异常路径），而非盲目追求 100%。
    * **原子性：** 每个测试用例应该只关注一个功能点，且不依赖于其他测试用例的执行顺序。
    * **自动化：** 将测试集成到 CI/CD 流程中，确保每次提交都经过验证。
* **反模式警惕：** “我写的代码没问题，不用写测试”、“时间紧，先不上测试了”。

**原则 9：使用高保真数据进行验证**
* **原始指令：** `不得使用模拟数据，必须使用真实环境数据`
* **核心思想：** 模拟数据（Mock Data）无法覆盖真实世界数据的复杂性和边缘案例。
* **参数调整与澄清：**
    * **单元测试阶段：** 使用模拟数据是**合理且必要**的，目的是为了隔离被测单元，使其不依赖于外部系统（如数据库、API）。
    * **集成/验收测试阶段：** 在此阶段，**必须**使用真实或经过脱敏处理的高保真生产环境数据，在与生产环境一致的（Staging）环境中进行测试。这能有效发现由数据问题（脏数据、特殊字符、边界值）引发的 Bug。
* **反模式警惕：** 只用自己构造的“完美”数据进行测试，从未考虑过线上实际数据的样子。

---

#### **第四阶段：维护与审计 (Maintain & Audit)**
此阶段的目标是确保系统的长期健康和团队知识的沉淀。

**原则 10：根除问题，禁止降级或禁用**
* **原始指令：** `禁止禁用功能作为修复`
* **核心思想：** 禁用功能或版本回滚只是止血的临时应急手段，绝不是问题的最终解决方案。它会累积技术负债，并损害用户体验。
* **执行参数 (如何调整):**
    * **问题追踪：** 如果被迫采取临时措施，必须创建高优先级的技术债工单，明确后续的根治计划和负责人。
    * **功能开关 (Feature Flag)：** 合理使用功能开关。它可以作为发布风险控制的手段，但在修复问题时，不能以“永久关闭开关”作为最终方案。
* **反模式警惕：** “这个功能总出问题，先关掉吧”，然后这个问题就再也没人理了。

**原则 11：变更可追溯与代码审查 (Code Review)**
* **原始指令：** `改动历史必须审计，前一次变更必须审查是否安全`
* **核心思想：** 任何进入代码库的变更都应该是清晰、可理解且经过同行验证的。
* **执行参数 (如何调整):**
    * **有意义的提交信息：** 遵循规范（如 Conventional Commits），清晰说明每次提交的“做了什么”和“为什么这么做”。
    * **强制代码审查：** 通过 Pull Request / Merge Request 机制，确保核心代码至少有一位其他同事审查通过后才能合并。
    * **审查重点：** Code Review 不仅是找 Bug，更是关于代码可读性、设计合理性、测试完备性和知识传递。
* **反模式警惕：** 提交信息写成 “update” 或 “fix bug”，代码审查时只看一眼就点“Approve”。

**原则 12：遵循既定项目结构和规范**
* **原始指令：** `必须放置文件至正确路径，符合项目结构`
* **核心思想：** 一致性和可预测性是团队协作的润滑剂。
* **执行参数 (如何调整):**
    * **遵守规范：** 遵循团队或社区既有的文件结构、命名规范和编码风格指南。
    * **工具辅助：** 使用 Linter（如 ESLint）、Formatter（如 Prettier）等工具来自动化地强制执行规范。
* **反模式警惕：** 在项目中随心所欲地创建文件和目录，使用个人偏好的命名方式。
