<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.0</version>
        <relativePath/>
    </parent>

    <groupId>com.trading</groupId>
    <artifactId>crypto-trading-system</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>Crypto Trading System</name>
    <description>A high-performance cryptocurrency quantitative trading system</description>

    <properties>
        <maven.compiler.release>21</maven.compiler.release>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <java.version>21</java.version>

        <!-- Dependency Versions -->
        <spring-boot.version>3.2.0</spring-boot.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        <jackson.version>2.15.2</jackson.version>
        <okhttp.version>4.12.0</okhttp.version>
        <commons-codec.version>1.16.0</commons-codec.version>
        <hibernate-validator.version>8.0.1.Final</hibernate-validator.version>
        <mysql-connector.version>8.2.0</mysql-connector.version>
        <influxdb-client.version>7.1.0</influxdb-client.version>
        <kafka.version>3.6.0</kafka.version>
        <redis.version>4.4.6</redis.version>
        <lombok.version>1.18.30</lombok.version>
        <junit-jupiter.version>5.10.0</junit-jupiter.version>
        <mockito.version>5.5.0</mockito.version>
        <assertj.version>3.24.2</assertj.version>
        <mybatis-plus.version>3.5.12</mybatis-plus.version>
        <micrometer.version>1.12.0</micrometer.version>

        <!-- Corrected versions for managed dependencies -->
        <commons-lang3.version>3.13.0</commons-lang3.version>
        <commons-pool2.version>2.11.1</commons-pool2.version>
        <spring-retry.version>2.0.4</spring-retry.version>
        <resilience4j.version>2.1.0</resilience4j.version>
        <jsqlparser.version>4.6</jsqlparser.version>
        <testcontainers.version>1.19.0</testcontainers.version>
        <grpc.version>1.59.0</grpc.version>
        <protobuf.version>3.24.4</protobuf.version>
        <!-- Force stable JRE variant of Guava to avoid flaky android artifact from mirrors -->
        <guava.version>32.1.2-jre</guava.version>
    </properties>

    <modules>
        <module>crypto-common</module>
        <module>crypto-sdk</module>
        <module>crypto-market-data</module>
        <!-- 其他模块将在后续添加 -->
        <!-- <module>crypto-trade</module> -->
        <!-- <module>crypto-account</module> -->
        <!-- <module>crypto-risk</module> -->
        <!-- <module>crypto-monitor</module> -->
        <!-- <module>crypto-bootstrap</module> -->
    </modules>

    <dependencyManagement>
        <dependencies>
            <!-- Pin Guava version across all modules to JRE variant -->
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>
            <!-- Spring Cloud Dependencies -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
                <exclusions>
                    <!-- 全局排除重复的JSONObject依赖，避免类路径冲突 -->
                    <exclusion>
                        <groupId>com.vaadin.external.google</groupId>
                        <artifactId>android-json</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- Jackson Dependencies -->
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-core</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-annotations</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson.version}</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.module</groupId>
                <artifactId>jackson-module-afterburner</artifactId>
                <version>${jackson.version}</version>
            </dependency>

            <!-- Apache Commons Libraries -->
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-lang3</artifactId>
                <version>${commons-lang3.version}</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>${commons-pool2.version}</version>
            </dependency>
            
            <!-- WebSocket -->
            <dependency>
                <groupId>org.java-websocket</groupId>
                <artifactId>Java-WebSocket</artifactId>
                <version>1.5.4</version>
            </dependency>

            <!-- OkHttp -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>okhttp</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <!-- Apache Commons Codec -->
            <dependency>
                <groupId>commons-codec</groupId>
                <artifactId>commons-codec</artifactId>
                <version>${commons-codec.version}</version>
            </dependency>

            <!-- Hibernate Validator -->
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>${hibernate-validator.version}</version>
            </dependency>

            <!-- MySQL Connector -->
            <dependency>
                <groupId>com.mysql</groupId>
                <artifactId>mysql-connector-j</artifactId>
                <version>${mysql-connector.version}</version>
            </dependency>

            <!-- InfluxDB Client -->
            <dependency>
                <groupId>com.influxdb</groupId>
                <artifactId>influxdb-client-java</artifactId>
                <version>${influxdb-client.version}</version>
            </dependency>

            <!-- Kafka -->
            <dependency>
                <groupId>org.apache.kafka</groupId>
                <artifactId>kafka-clients</artifactId>
                <version>${kafka.version}</version>
            </dependency>

            <!-- Redis -->
            <dependency>
                <groupId>redis.clients</groupId>
                <artifactId>jedis</artifactId>
                <version>${redis.version}</version>
            </dependency>

            <!-- OkHttp Logging Interceptor -->
            <dependency>
                <groupId>com.squareup.okhttp3</groupId>
                <artifactId>logging-interceptor</artifactId>
                <version>${okhttp.version}</version>
            </dependency>

            <!-- Spring Retry -->
            <dependency>
                <groupId>org.springframework.retry</groupId>
                <artifactId>spring-retry</artifactId>
                <version>${spring-retry.version}</version>
            </dependency>

            <!-- Resilience4j -->
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-retry</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-circuitbreaker</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-ratelimiter</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>

            <!-- Micrometer Core -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-core</artifactId>
                <version>${micrometer.version}</version>
            </dependency>

            <!-- Micrometer Prometheus Registry -->
            <dependency>
                <groupId>io.micrometer</groupId>
                <artifactId>micrometer-registry-prometheus</artifactId>
                <version>${micrometer.version}</version>
            </dependency>

            <!-- MyBatis-Plus for Spring Boot 3.x -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis-plus.version}</version>
            </dependency>

            <!-- MyBatis-Plus JSqlParser -->
            <dependency>
                <groupId>com.github.jsqlparser</groupId>
                <artifactId>jsqlparser</artifactId>
                <version>${jsqlparser.version}</version>
            </dependency>

            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <!-- Test Dependencies -->
            <dependency>
                <groupId>org.junit</groupId>
                <artifactId>junit-bom</artifactId>
                <version>${junit-jupiter.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.assertj</groupId>
                <artifactId>assertj-core</artifactId>
                <version>${assertj.version}</version>
                <scope>test</scope>
            </dependency>
            
            <!-- Testcontainers BOM -->
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${testcontainers.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- gRPC & Protobuf -->
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java-util</artifactId>
                <version>${protobuf.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-netty-shaded</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-protobuf</artifactId>
                <version>${grpc.version}</version>
            </dependency>
            <dependency>
                <groupId>io.grpc</groupId>
                <artifactId>grpc-stub</artifactId>
                <version>${grpc.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- Maven Compiler Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>3.11.0</version>
                    <configuration>
                        <source>21</source>
                        <target>21</target>
                        <compilerArgs>
                        </compilerArgs>
                        <encoding>UTF-8</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                                <version>${lombok.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>

                <!-- Maven Surefire Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>3.1.2</version>
                    <configuration>
                        <!-- JVM参数配置 -->
                        <argLine>
                            -Xmx1g
                            -Xms512m
                            -XX:+UseG1GC
                            -XX:MaxGCPauseMillis=200
                            -XX:+EnableDynamicAgentLoading
                            -Djava.awt.headless=true
                            -Dfile.encoding=UTF-8
                        </argLine>
                        <!-- 测试执行配置 -->
                        <forkCount>1</forkCount>
                        <reuseForks>false</reuseForks>
                        <forkedProcessTimeoutInSeconds>3600</forkedProcessTimeoutInSeconds>
                        <forkedProcessExitTimeoutInSeconds>30</forkedProcessExitTimeoutInSeconds>
                        <!-- 测试报告配置 -->
                        <useSystemClassLoader>false</useSystemClassLoader>
                        <useManifestOnlyJar>false</useManifestOnlyJar>
                        <!-- 并行执行配置 -->
                        <parallel>none</parallel>
                        <!-- 测试失败处理 -->
                        <testFailureIgnore>false</testFailureIgnore>
                    </configuration>
                </plugin>

                <!-- Spring Boot Maven Plugin -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>

                <!-- Maven Source Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-source-plugin</artifactId>
                    <version>3.3.0</version>
                </plugin>

                <!-- Maven Javadoc Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-javadoc-plugin</artifactId>
                    <version>3.5.0</version>
                    <configuration>
                        <!-- 设置编码 -->
                        <encoding>UTF-8</encoding>
                        <docencoding>UTF-8</docencoding>
                        <charset>UTF-8</charset>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <repositories>
        <repository>
            <id>central</id>
            <name>Maven Central</name>
            <url>https://repo1.maven.org/maven2</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>central</id>
            <name>Maven Central</name>
            <url>https://repo1.maven.org/maven2</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>
</project>
